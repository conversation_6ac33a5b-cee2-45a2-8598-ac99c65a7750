# Proyecto para Generación de Contenidos

## Descripción General
Este proyecto es una Prueba de Concepto (POC) para la generación de contenidos. Sirve APIs y crea abstracciones basadas en métricas aún por determinar.

## Estructura del Proyecto
- **src/**: Contiene abstracciones bien estructuradas esenciales para el proyecto.
- **notebooks/**: Alberga experimentos siguiendo la convención de nomenclatura:
  - Formato: `{tema}/{concepto_explorado}_{número_secuencia}.ipynb`
  - Ejemplo: `agentes/multiagente_ReAct_01.ipynb`

## Patrones de Código
- [Link de la semilla](insertarLinkAquí) para ver el patrón de código de ejemplo utilizado.
- Andreu diseñó esta semilla de código. [Video explicativo](insertarLinkDelVideoAquí).

### Estructura Lógica
- **workflows**: Contiene la lógica principal de distintas categorías (content, courses, subjects, users).
- **api/common**: Alberga la lógica compartida (LLM generalizado, observability, herramientas de búsqueda).

## Instalación y Ejecución

### Ejecución vía Docker (recomendado)

#### Configuración de Variables de Entorno
1. Crear archivos de variables de entorno:
   - `.env.Local`: Para pruebas locales independientes.
   - `.env.Development`: Con variables del entorno de desarrollo desplegado.

2. Variables de entorno requeridas (definidas en `src/common/application_settings.py`):
   ```
   GLOBAL__ENVIRONMENT
   OPENAI_API_KEY
   CLAUDE_API_KEY
   PHOENIX_PROJECT_NAME (Contenidos IA)
   PHOENIX_COLLECTOR_ENDPOINT (Opcional)
   PHOENIX_PORT (Optional 6006)
   OPENAI_ORG_ID
   AZURE_OPENAI_ENDPOINT
   AZURE_OPENAI_API_KEY
   DB_PORT
   DB_USER
   DB_NAME
   DB_PASSWORD
   DB_HOST
   WEBSITES_PORT
   WORKERS
   ```

### Entornos de Docker

#### 1. Entorno de Desarrollo y Pruebas Local
- Requiere: archivo `.env.Local`
- Ejecución: 
  ```
  source .env.Local
  docker-compose -f "docker-compose-local.yml" up
  ```
- Características:
  - Recomendado para desarrollo local.
  - Permite actualización de código en tiempo real.
  - Replica la base de datos de Azure localmente.
  - Monta volúmenes de aplicación locales.

#### 2. Entorno para Replicar el Entorno Cloud y Desplegar
- Requiere: archivo `.env.Development`
- Ejecución:
  ```
  source .env.Development
  docker-compose up
  ```
- Características:
  - Usado para despliegues en Azure y producción.

### Pasos de Configuración
1. Instalar [Docker](https://docs.docker.com/engine/install/) y [Docker Compose](https://docs.docker.com/compose/install/).
2. En Windows, instalar y habilitar WSL.
3. Dar permisos de ejecución: `chmod +x entrypoint.sh`
4. Cargar variables de entorno: `source .env.Local` o `source .env.Development`
5. En Windows cambiar el end of line sequence de CRLF a LF del fichero `entrypoint.sh` (En VScode se puede hacer con Crtl+Mayus+p y buscar `Change End of Line Sequence`)
6. Construir y ejecutar:
   - Local: `docker-compose -f "docker-compose-local.yml" up --build`
   - Desarrollo: `docker-compose up --build`
   - Opción en segundo plano: agregar `-d` al final del comando
7. Para terminar: `docker-compose down`

### Acceso a Servicios
- API endpoints: `0.0.0.0:8000/docs` o `localhost:8000/docs`
- Playground: `0.0.0.0/7860` o `localhost:7860`
- Trazas LLM (Arize): `0.0.0.0:6006` o `localhost:6006`

### Configuración Inicial
Crear usuario mock:
- POST request a `localhost:8000/users/`
- Payload: `{"name": "Unir demo"}`


## Instrucciones de Ejecución sin Docker

Este documento proporciona instrucciones paso a paso para ejecutar el proyecto de generación de contenidos utilizando Python directamente, sin el uso de Docker.

### Requisitos Previos

- Python 3.11 instalado en el sistema
- pip (gestor de paquetes de Python)
- Acceso a una base de datos PostgreSQL (local o remota)

### Pasos de Configuración

1. **Clonar el Repositorio**

   Clone el repositorio del proyecto en su máquina local.

2. **Crear y Activar un Entorno Virtual**

   ```bash
   python3.11 -m venv venv

   # En Windows
   venv\Scripts\activate

   # En macOS/Linux
   source venv/bin/activate
   ```

3. **Instalar Dependencias**

   ```bash
   pip install -r requirements.txt
   ```

4. **Configurar Variables de Entorno**

   Cree un archivo `.env.Development` en la raíz del proyecto con el siguiente contenido:

   ```
   GLOBAL__ENVIRONMENT=Development
   OPENAI_API_KEY=tu_clave_api_openai
   CLAUDE_API_KEY=tu_clave_api_claude
   PHOENIX_PROJECT_NAME=Contenidos IA
   OPENAI_ORG_ID=tu_id_org_openai
   AZURE_OPENAI_ENDPOINT=tu_endpoint_azure_openai
   AZURE_OPENAI_API_KEY=tu_clave_api_azure_openai
   DB_PORT=tu_puerto_db
   DB_USER=tu_usuario_db
   DB_NAME=tu_nombre_db
   DB_PASSWORD=tu_contraseña_db
   DB_HOST=tu_host_db
   WEBSITES_PORT=7860
   WORKERS=1
   ```

   Asegúrese de reemplazar los valores con sus propias credenciales y configuraciones.

### Ejecución de la Aplicación

1. **Iniciar la API**

   En una terminal, ejecuta:

   ```bash
   uvicorn src.main:app --host 0.0.0.0 --port 8000 --reload --log-level info
   ```

2. **Iniciar el Playground de Gradio**

   En otra terminal, asegurate de que el entorno virtual esté activado y ejecuta:

   ```bash
   python gradio/app.py
   ```

## Acceso a la Aplicación

- **API Documentation**: Abra un navegador y vaya a `http://localhost:8000/docs`
- **Gradio Playground**: Abra un navegador y vaya a `http://localhost:7860`

## Configuración de Trazas LLM (Opcional)

Para ver las trazas de los modelos de lenguaje (LLM), necesitará configurar y ejecutar Arize Phoenix por separado. Consulte la [documentación de Arize](https://docs.arize.com/phoenix/tracing/llm-traces) para obtener instrucciones detalladas.

## Creación de Usuario de Prueba

Para crear un usuario de prueba, realiza una solicitud POST a `http://localhost:8000/users/` con el siguiente cuerpo JSON:

```json
{
  "name": "Unir demo"
}
```

Puede utilizar herramientas como Postman o curl para realizar esta solicitud.

### Notas Adicionales

- Asegúrese de tener instaladas las dependencias del sistema necesarias, como `build-essential` y `gcc`.
- Para entornos de producción, considere usar Gunicorn en lugar de Uvicorn para manejar múltiples workers:
  ```bash
  gunicorn src.main:app --bind 0.0.0.0:8000 --workers 4 --worker-class uvicorn.workers.UvicornWorker
  ```
- Ajusta el número de workers según las capacidades de tu sistema y los requisitos de rendimiento.

### Solución de Problemas

Si encuentras algún problema durante la configuración o ejecución:

1. Verifica que todas las variables de entorno estén correctamente configuradas.
2. Asegúrate de que la base de datos PostgreSQL esté accesible y las credenciales sean correctas.
3. Revisa los logs de la aplicación para obtener información sobre posibles errores.

Para obtener ayuda adicional, consulta la documentación del proyecto o contacta al equipo de desarrollo.

## Despliegue

Esta sección describe el proceso de despliegue de la aplicación en diferentes entornos de Azure.

### Requisitos previos

- Azure CLI instalado y configurado
- Docker instalado y configurado
- Acceso a Azure Container Registry (ACR)
- Permisos adecuados en los recursos de Azure

### Proceso de despliegue

1. **Preparación del entorno**

   Asegúrese de tener el archivo `deploy` en la raíz del proyecto con los permisos de ejecución adecuados:

   ```bash
   chmod +x deploy
   ```

2. **Iniciar sesión en Azure**

   Antes de ejecutar el script de despliegue, asegúrese de iniciar sesión en Azure:

   ```bash
   az login
   ```

3. **Ejecutar el script de despliegue**

   Primero, asegurarte de subir la version en el archivo src/version.py

   El script acepta un argumento para especificar el entorno de despliegue:

   ```bash
   ./deploy [dev|preprod|prod]
   ```

   Por ejemplo, para desplegar en el entorno de desarrollo:

   ```bash
   ./deploy dev
   ```

4. **Proceso de despliegue**

   El script realizará las siguientes acciones:
   - Construir la imagen Docker
   - Etiquetar la imagen
   - Iniciar sesión en Azure Container Registry
   - Subir la imagen a ACR
   - Actualizar la aplicación web de Azure con la nueva imagen

### Configuraciones específicas por entorno

El script utiliza diferentes configuraciones según el entorno seleccionado:

- **Desarrollo (dev)**:
  - ACR: acrwedevcontenidos001.azurecr.io
  - Grupo de recursos: rg-contenidos-dev-sc-001
  - Nombre de la aplicación web: webapp-contenidos-dev-sc-001

- **Pre-producción (preprod)**:
  - ACR: acrweprecontenidos001.azurecr.io
  - Grupo de recursos: rg-contenidos-pre-we-001
  - Nombre de la aplicación web: webapp-contenidos-dev-we-001

- **Producción (prod)**:
  - ACR: acrweprocontenidos001.azurecr.io
  - Grupo de recursos: rg-contenidos-pro-we-001
  - Nombre de la aplicación web: webapp-contenidos-pro-we-001

### Notas importantes

- Antes de desplegar una nueva imagen, subir el numero de version en el archivo src/version.py
- Para el entorno de desarrollo, el Dockerfile dentro de docker-compose debe cambiarse a Dockerfile.app debido a limitaciones de memoria y workers.
- Asegúrese de tener los permisos necesarios en Azure para realizar todas las operaciones de despliegue.

### Solución de problemas

Si encuentra problemas durante el despliegue:

1. Verifique que tiene los permisos adecuados en Azure.
2. Asegúrese de que los nombres de los recursos en Azure coincidan con los especificados en el script.
3. Revise los logs de Azure y Docker para obtener más información sobre posibles errores.

Para obtener ayuda adicional, contacte al equipo de IA o revise la documentación de Azure.

## Como añadir nuevas dependencias. 

* Asegurarse que es compatible con versiones entre 3.11-3.12
* Meter dependencia en el archivo requirements.txt en el formato que se especifican. 
* Ejecutar el contenedor de docker para comprobar que todo funciona correctamente con la nueva dependencia, si va bien con docker, se puede anadir. 