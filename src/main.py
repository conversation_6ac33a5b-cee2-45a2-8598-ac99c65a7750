from fastapi import Depends, FastAPI
from fastapi.middleware.cors import CORSMiddleware

from src.api.common.dependencies.security import verify_api_key
from src.api.common.dependency_container import DependencyContainer
from src.api.workflows.activities import activities_router
from src.api.workflows.ai_processes import ai_process_router
from src.api.workflows.competencies import competencies_router
from src.api.workflows.document_ingestion import document_router
from src.api.workflows.health_checks import health_check_router
from src.api.workflows.indexes import indexes_router
from src.api.workflows.rubrics import rubrics_router
from src.api.workflows.sections import sections_router
from src.api.workflows.subjects import subjects_router
from src.api.workflows.texts import texts_router
from src.api.workflows.titles import titles_router
from src.api.workflows.topics import topics_router
from src.api.workflows.tutoring_scripts import tutoring_scripts_router
from src.api.workflows.users import users_router
from src.api.workflows.validation_tests import validation_tests_router
from src.common.application_environment import ApplicationEnvironment

from .version import __version__

DependencyContainer.initialize(observability=True)

docs_url, redoc_url = (
    ("/docs", "/redoc")
    if ApplicationEnvironment.get_current() != ApplicationEnvironment.PRODUCTION
    else (None, None)
)

app = FastAPI(
    title="Creador de Contenidos app",
    version=__version__,
    docs_url=docs_url,
    redoc_url=redoc_url,
    dependencies=[Depends(verify_api_key)],
)


app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=False,
    allow_methods=["*"],
    allow_headers=["*"],
)
app.include_router(health_check_router.router)
app.include_router(users_router.router)
app.include_router(subjects_router.router)
app.include_router(titles_router.router)
app.include_router(competencies_router.router)
app.include_router(indexes_router.router)
app.include_router(topics_router.router)
app.include_router(ai_process_router.router)
app.include_router(sections_router.router)
app.include_router(document_router.router)
app.include_router(texts_router.router)
app.include_router(rubrics_router.router)
app.include_router(activities_router.router)
app.include_router(validation_tests_router.router)
app.include_router(tutoring_scripts_router.router)
