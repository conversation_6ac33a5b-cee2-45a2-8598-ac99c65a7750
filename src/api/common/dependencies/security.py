from typing import Optional

from fastapi import Header, HTTPException, status

from src.api.common.dependency_container import DependencyContainer


async def verify_api_key(x_api_key: Optional[str] = Header(None)):
    application_settings = DependencyContainer.get_application_settings()
    if x_api_key != application_settings.FASTAPI_API_KEY:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Not authenticated"
        )
