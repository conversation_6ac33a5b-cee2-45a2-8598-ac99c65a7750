from __future__ import annotations

import logging
import subprocess
from logging import Logger
from typing import TYPE_CHECKING, Any, Dict, Literal

from azure.servicebus.aio import ServiceBusClient
from ia_gen_core.prompts import PromptManager
from langchain.retrievers import ContextualCompressionRetriever
from langchain_core.documents import BaseDocumentCompressor
from langchain_core.retrievers import BaseRetriever
from openai import AsyncAzureOpenAI, AsyncOpenAI
from sqlalchemy import Engine, text
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncEngine, AsyncSession, create_async_engine
from sqlalchemy.orm import Session, sessionmaker
from sqlmodel import create_engine

from src.api.common.services import (
    LocalQueueInterface,
    LocalSqliteBroker,
    QueueInterface,
)
from src.api.common.services.llm import LLM
from src.api.common.services.message_broker.service_bus_queue_interface import (
    ServiceBusQueueInterface,
)
from src.background_tasks.base_service_bus_task import ServiceBusConfig
from src.common.application_environment import ApplicationEnvironment
from src.common.application_settings import ApplicationSettings
from src.domain.models import SQLModel, User

if TYPE_CHECKING:
    from src.api.common.services import (
        AITracer,
        BlobStorageInterface,
        ContentGenerator,
        ContentRegenerator,
        IndexRepository,
        MailSender,
        PhoenixObservability,
        SearchAgent,
        SearchEngine,
        UrlExtractor,
    )
    from src.api.workflows.activities.generate_activity.workflow import (
        GenerateActivitiesWorkflow,
    )
    from src.api.workflows.activities.generate_activity_unipro.workflow import (
        GenerateActivitiesUniproWorkflow,
    )
    from src.api.workflows.competencies import (
        GenerateCompetenciesWorkflow,
        RegenerateCompetenciesWorkflow,
    )
    from src.api.workflows.document_ingestion import (
        Reranker,
        SearchReferencesWorkflow,
    )
    from src.api.workflows.indexes import (
        GenerateContentWorkflow,
        GenerateFailedContentWorkflow,
        GenerateIndexWorkflow,
        GetAllIndexesWorkflow,
        GetIndexWorkflow,
        RegenerateIndexWorkflow,
    )
    from src.api.workflows.rubrics.extract_criteria.workflow import (
        ExtractCriteriaWorkflow,
    )
    from src.api.workflows.rubrics.generate_rubrics.workflow import (
        GenerateRubricsWorkflow,
    )
    from src.api.workflows.sections import (
        AddParagraphWorkflow,
        GetSectionsByTopicWorkflow,
        RegenerateParagraphWorkflow,
    )
    from src.api.workflows.titles import GenerateSubjectCompetenciesWorkflow
    from src.api.workflows.topics import (
        GenerateAllDidacticInstructionsWorkflow,
        GenerateInDepthWorkflow,
        GetContentMarkdownWorkflow,
        GetInDepthByIndexWorkflow,
        GetTopicsByIndexWorkflow,
        GetTopicStatusWorkflow,
        RegenerateDidacticInstructionsWorkflow,
    )
    from src.api.workflows.tutoring_scripts.generate_tutoring_scripts.workflow import (
        GenerateTutoringScriptWorkflow,
    )
    from src.api.workflows.validation_tests.generate.workflow import (
        GenerateValidationTestWorkflow,
    )
    from src.api.workflows.validation_tests.generate_plan.workflow import (
        GenerateValidationPlanWorkflow,
    )
    from src.common.application_settings import ApplicationSettings
GPT41 = "gpt-4.1"


class DependencyContainer:
    _LOGGER_NAME = "logger"
    _application_settings: ApplicationSettings
    _session_factory: sessionmaker
    _db_engine: Engine
    _async_db_engine: AsyncEngine
    _async_session_factory: sessionmaker
    _observability: PhoenixObservability
    _llm: LLM
    _async_queue_client: ServiceBusClient | None = None
    _retriever: BaseRetriever
    _reranker: BaseDocumentCompressor
    _search_engine: SearchEngine
    _content_generation_queue: QueueInterface | None = None
    _doc_processing_queue: QueueInterface | None = None
    _cooldown_storage: BlobStorageInterface
    _document_storage: BlobStorageInterface
    _local_queue_broker: LocalSqliteBroker | None = None

    # Create a method for initializing only api, and another for background needed things.

    @classmethod
    def initialize(
        cls, observability: bool = True, create_prompts: bool = True
    ) -> None:  # Add separation for background only deps
        cls._initialize_application_settings()
        cls._initialize_logger()
        cls._initialize_application_insights()
        cls._initialize_database_engines()
        if observability:
            cls._initialize_observability()
        cls._initialize_retriever()
        cls._initialize_llm_manager()
        cls._initialize_async_queue_client()
        cls._initialize_content_generation_queue()
        cls._initialize_doc_processing_queue()
        cls._initialize_search_engine(cls._application_settings.SEARCH_ENGINE)
        cls._initialize_url_extractor(cls._application_settings.URL_EXTRACTOR)
        cls._initialize_cooldown_storage()
        cls._initialize_document_storage()
        if create_prompts:
            cls._create_and_update_prompts()

    @classmethod
    def get_application_settings(cls) -> ApplicationSettings:
        return cls._application_settings

    @classmethod
    def get_retriever(cls) -> BaseRetriever | ContextualCompressionRetriever:
        return cls._retriever

    @classmethod
    def get_async_database_engine(cls) -> AsyncEngine:
        return cls._async_db_engine

    @classmethod
    def get_generate_index_workflow(cls) -> GenerateIndexWorkflow:
        from src.api.workflows.indexes import GenerateIndexWorkflow

        return GenerateIndexWorkflow(
            cls.get_async_database_engine(),
            cls.get_logger(),
            cls.get_ai_tracer(),
            cls._llm.get_llm(
                provider="model-router",
                model_name="gpt-5",
                max_tokens=40000,
                temperature=0,
                reasoning_effort="high",
            ),
            cls.get_prompt_manager(),
            cls.get_index_repository(),
            cls.get_mail_sender(),
        )

    @classmethod
    def get_generate_in_depth_workflow(cls) -> GenerateInDepthWorkflow:
        from src.api.workflows.topics import GenerateInDepthWorkflow

        return GenerateInDepthWorkflow(
            cls.get_database_engine(),
            cls.get_logger(),
            cls.get_async_openai_client(),
            cls.get_ai_tracer(),
            cls.get_search_agent(),
            cls.get_prompt_manager(),
            cls.get_index_repository(),
            cls.get_mail_sender(),
        )

    @classmethod
    def get_regenerate_index_workflow(cls) -> RegenerateIndexWorkflow:
        from src.api.workflows.indexes import RegenerateIndexWorkflow

        return RegenerateIndexWorkflow(
            db_engine=cls.get_database_engine(),
            logger=cls.get_logger(),
            content_regenerator=cls.get_content_regenerator(),
            index_repository=cls.get_index_repository(),
            ai_tracer=cls.get_ai_tracer(),
        )

    @classmethod
    def get_generate_subject_competencies_workflow(
        cls,
    ) -> GenerateSubjectCompetenciesWorkflow:
        from src.api.workflows.titles.generate.generate_subject_competencies_workflow import (
            GenerateSubjectCompetenciesWorkflow,
        )

        return GenerateSubjectCompetenciesWorkflow(
            cls.get_database_engine(),
            cls.get_logger(),
            cls.get_llm_manager().get_llm(
                provider="azure-openai",
                model_name="o3",
                max_tokens=20000,
                temperature=0,
                reasoning_effort="high",
            ),
            cls.get_prompt_manager(),
            cls.get_ai_tracer(),
            cls.get_index_repository(),
        )

    @classmethod
    def get_all_indexes_workflow(cls) -> GetAllIndexesWorkflow:
        from src.api.workflows.indexes import GetAllIndexesWorkflow

        return GetAllIndexesWorkflow(cls.get_database_engine(), cls.get_logger())

    @classmethod
    def get_generate_content_workflow(cls) -> GenerateContentWorkflow:
        from src.api.workflows.indexes import GenerateContentWorkflow

        return GenerateContentWorkflow(
            cls.get_database_engine(),
            cls.get_logger(),
            ai_tracer=cls.get_ai_tracer(),
            queue_interface=cls.get_content_generation_queue(),
            index_repository=cls.get_index_repository(),
        )

    @classmethod
    def get_generate_failed_content_workflow(cls) -> GenerateFailedContentWorkflow:
        from src.api.workflows.indexes import GenerateFailedContentWorkflow

        return GenerateFailedContentWorkflow(
            cls.get_database_engine(),
            cls.get_logger(),
            queue_interface=cls.get_content_generation_queue(),
        )

    @classmethod
    def get_topics_by_index_workflow(cls) -> GetTopicsByIndexWorkflow:
        from src.api.workflows.topics import GetTopicsByIndexWorkflow

        return GetTopicsByIndexWorkflow(cls.get_database_engine(), cls.get_logger())

    @classmethod
    def get_topic_status_workflow(cls) -> GetTopicStatusWorkflow:
        from src.api.workflows.topics import GetTopicStatusWorkflow

        return GetTopicStatusWorkflow(cls.get_database_engine(), cls.get_logger())

    @classmethod
    def get_markdown_service(cls):
        from src.api.common.services.markdown_service import MarkdownService

        return MarkdownService(cls.get_logger())

    @classmethod
    def get_content_markdown_workflow(cls) -> GetContentMarkdownWorkflow:
        from src.api.workflows.topics import GetContentMarkdownWorkflow

        return GetContentMarkdownWorkflow(
            cls.get_database_engine(), cls.get_logger(), cls.get_markdown_service()
        )

    @classmethod
    def get_generate_all_didactic_instructions_workflow(
        cls,
    ) -> GenerateAllDidacticInstructionsWorkflow:
        from src.api.workflows.topics import GenerateAllDidacticInstructionsWorkflow

        return GenerateAllDidacticInstructionsWorkflow(
            cls.get_async_database_engine(),
            cls.get_logger(),
            cls._llm.get_llm(
                provider="model-router",
                model_name="gpt-5",
                max_tokens=20000,
                reasoning_effort="high",
                temperature=0,
            ),
            cls.get_index_repository(),
            cls.get_prompt_manager(),
            cls.get_ai_tracer(),
            mail_sender=cls.get_mail_sender(),
        )

    @classmethod
    def get_regenerate_didactic_instructions_workflow(
        cls,
    ) -> RegenerateDidacticInstructionsWorkflow:
        from src.api.workflows.topics import RegenerateDidacticInstructionsWorkflow

        return RegenerateDidacticInstructionsWorkflow(
            cls.get_database_engine(),
            cls.get_logger(),
            cls.get_content_regenerator(),
            cls.get_index_repository(),
            cls.get_ai_tracer(),
        )

    @classmethod
    def get_generate_tests_workflow(cls):
        from src.api.workflows.topics import GenerateTestsWorkflow

        return GenerateTestsWorkflow(
            db_engine=cls.get_database_engine(),
            logger=cls.get_logger(),
            llm=cls.get_llm_manager().get_llm(
                provider="azure-openai",
                model_name=GPT41,
                max_tokens=12000,
                temperature=0,
            ),
            tracer=cls.get_ai_tracer(),
            content_markdown_workflow=cls.get_content_markdown_workflow(),
            index_repository=cls.get_index_repository(),
            prompt_manager=cls.get_prompt_manager(),
            mail_sender=cls.get_mail_sender(),
        )

    @classmethod
    def get_sections_by_topic_workflow(cls) -> GetSectionsByTopicWorkflow:
        from src.api.workflows.sections import GetSectionsByTopicWorkflow

        return GetSectionsByTopicWorkflow(cls.get_database_engine(), cls.get_logger())

    @classmethod
    def get_add_paragraph_workflow(cls) -> AddParagraphWorkflow:
        from src.api.workflows.sections import AddParagraphWorkflow

        return AddParagraphWorkflow(
            db_engine=cls.get_database_engine(),
            logger=cls.get_logger(),
            llm=cls.get_llm_manager().get_llm(
                provider="model-router",
                model_name="claude-sonnet-4-20250514",
                max_tokens=20000,
                temperature=0,
            ),
            ai_tracer=cls.get_ai_tracer(),
            content_generator=cls.get_content_generator(),
            prompt_manager=cls.get_prompt_manager(),
            index_repository=cls.get_index_repository(),
            markdown_service=cls.get_markdown_service(),
        )

    @classmethod
    def get_regenerate_paragraph_workflow(cls) -> RegenerateParagraphWorkflow:
        from src.api.workflows.sections.regenerate.regenerate_paragraph_workflow import (
            RegenerateParagraphWorkflow,
        )

        return RegenerateParagraphWorkflow(
            db_engine=cls.get_async_database_engine(),
            logger=cls.get_logger(),
            llm=cls.get_llm_manager().get_llm(
                provider="model-router",
                model_name="claude-sonnet-4-20250514",
                max_tokens=20000,
                temperature=0,
            ),
            ai_tracer=cls.get_ai_tracer(),
            prompt_manager=cls.get_prompt_manager(),
            content_generator=cls.get_content_generator(),
            index_repository=cls.get_index_repository(),
            markdown_service=cls.get_markdown_service(),
        )

    @classmethod
    def get_index_workflow(cls) -> GetIndexWorkflow:
        from src.api.workflows.indexes import GetIndexWorkflow

        return GetIndexWorkflow(
            cls.get_async_database_engine(),
            cls.get_logger(),
            cls.get_index_repository(),
        )

    @classmethod
    def get_in_depth_by_index_workflow(cls) -> GetInDepthByIndexWorkflow:
        from src.api.workflows.topics import GetInDepthByIndexWorkflow

        return GetInDepthByIndexWorkflow(cls.get_database_engine(), cls.get_logger())

    @classmethod
    def get_generate_competencies_workflow(cls) -> GenerateCompetenciesWorkflow:
        from src.api.workflows.competencies import GenerateCompetenciesWorkflow

        return GenerateCompetenciesWorkflow(
            cls.get_logger(),
            cls.get_index_repository(),
            cls.get_prompt_manager(),
            cls._llm.get_llm(
                provider="model-router",
                model_name="gpt-5",
                max_tokens=25000,
                temperature=0,
                reasoning_effort="minimal",
            ),
            cls.get_ai_tracer(),
        )

    @classmethod
    def get_regenerate_competencies_workflow(cls) -> RegenerateCompetenciesWorkflow:
        from src.api.workflows.competencies import RegenerateCompetenciesWorkflow

        return RegenerateCompetenciesWorkflow(
            cls.get_async_database_engine(),
            cls.get_logger(),
            cls.get_index_repository(),
            cls.get_content_regenerator(),
            cls.get_ai_tracer(),
        )

    @classmethod
    def get_search_references_workflow(cls) -> SearchReferencesWorkflow:
        from src.api.workflows.document_ingestion.search_references.search_references_workflow import (
            SearchReferencesWorkflow,
        )

        return SearchReferencesWorkflow(
            cls.get_async_database_engine(),
            cls.get_logger(),
            cls.get_search_engine(cls._application_settings.SEARCH_ENGINE),
            cls.get_search_agent(),
            cls._llm.get_llm(
                provider="azure-openai",
                model_name=GPT41,
                max_tokens=4096,
                temperature=0,
            ),
            cls._llm.get_llm(
                provider="azure-openai",
                model_name="o4-mini",
                max_tokens=4096,
                reasoning_effort="low",
            ),
            cls.get_prompt_manager(),
            cls.get_index_repository(),
            cls.get_process_document_queue(),
        )

    @classmethod
    def get_async_session_factory(cls) -> sessionmaker:
        return cls._async_session_factory

    @classmethod
    def get_content_generation_queue(cls) -> QueueInterface:
        return cls._content_generation_queue

    @property
    def retriever(self) -> BaseRetriever:
        from langchain.retrievers import ContextualCompressionRetriever

        if isinstance(self.get_retriever(), ContextualCompressionRetriever):
            return self.get_retriever().base_retriever
        return self.get_retriever()

    @classmethod
    def get_async_queue_client(cls):
        return cls._async_queue_client

    @classmethod
    def get_session_factory(cls) -> sessionmaker:
        return cls._session_factory

    @classmethod
    def get_logger(cls, module_name=None) -> Logger:
        logging.getLogger("azure.servicebus").setLevel(logging.WARNING)
        logging.getLogger("azure.core").setLevel(logging.WARNING)
        logging.getLogger("azure.monitor.opentelemetry").setLevel(logging.WARNING)
        if module_name is None:
            logger_name = cls._LOGGER_NAME
        else:
            logger_name = f"{cls._LOGGER_NAME}.{module_name}"
        return logging.getLogger(logger_name)

    @classmethod
    def get_llm_manager(cls) -> LLM:
        return cls._llm

    @classmethod
    def get_database_engine(cls) -> Engine:
        return cls._db_engine

    @classmethod
    def get_reranker(cls) -> Reranker:
        return cls._reranker

    @classmethod
    def get_search_agent(cls) -> SearchAgent:
        from src.api.common.services.search_agent import SearchAgent

        app_settings = cls.get_application_settings()
        return SearchAgent(
            cls.get_async_openai_client(),
            cls.get_search_engine(app_settings.ACADEMIC_SEARCH_ENGINE),
            cls.get_search_engine(app_settings.SEARCH_ENGINE),
            cls.get_url_extractor(app_settings.URL_EXTRACTOR),
            cls.get_prompt_manager(),
            logger=cls.get_logger(),
        )

    @classmethod
    def get_search_engine(cls, custom_search_engine: str | None = None) -> SearchEngine:
        logger = cls.get_logger()
        if custom_search_engine:
            try:
                from src.api.common.services import (
                    BraveSearchEngine,
                    DuckDuckGoSearchEngine,
                    JinaSearchEngine,
                    TavilySearchEngine,
                )

                application_settings = cls.get_application_settings()
                if custom_search_engine == "duckduckgo":
                    return DuckDuckGoSearchEngine(
                        logger=logger, reranker_model=cls.get_reranker()
                    )
                elif custom_search_engine == "tavily":
                    if application_settings.TAVILY_SEARCH_API_KEY is None:
                        raise ValueError(
                            "TAVILY_SEARCH_API_KEY is required for Tavily search engine."
                        )
                    return TavilySearchEngine(
                        logger=logger,
                        api_key=application_settings.TAVILY_SEARCH_API_KEY,
                        reranker_model=cls.get_reranker(),
                    )
                elif custom_search_engine == "brave":
                    if application_settings.BRAVE_SEARCH_API_KEY is None:
                        raise ValueError(
                            "BRAVE_SEARCH_API_KEY is required for Brave search engine."
                        )
                    return BraveSearchEngine(
                        logger=logger,
                        api_key=application_settings.BRAVE_SEARCH_API_KEY,
                        reranker_model=cls.get_reranker(),
                    )
                elif custom_search_engine == "jina":
                    if application_settings.JINA_API_KEY is None:
                        raise ValueError(
                            "JINA_API_KEY is required for Jina search engine."
                        )
                    cls._search_engine = JinaSearchEngine(
                        logger=logger,
                        api_key=application_settings.JINA_API_KEY,
                        reranker_model=cls.get_reranker(),
                    )
                else:
                    raise ValueError(
                        f"Unsupported SEARCH_ENGINE: {application_settings.SEARCH_ENGINE}"
                    )
            except Exception as e:
                logger.exception(f"Search engine not initialized: {e}")
        return cls._search_engine

    @classmethod
    def get_url_extractor(cls, custom_url_extractor: str | None = None) -> UrlExtractor:
        from src.api.common.services import (
            JinaExtractor,
            TavilyExtractor,
        )

        logger = cls.get_logger()
        if custom_url_extractor:
            try:
                if custom_url_extractor == "jina":
                    return JinaExtractor(
                        logger=logger, api_key=cls._application_settings.JINA_API_KEY
                    )
                elif custom_url_extractor == "tavily":
                    return TavilyExtractor(
                        logger=logger,
                        api_key=cls._application_settings.TAVILY_EXTRACTOR_API_KEY,
                    )
                else:
                    raise ValueError(
                        f"Unsupported URL extractor: {custom_url_extractor}"
                    )
            except Exception as e:
                logger.exception(f"URL extractor not initialized: {e}")
        return cls._url_extractor

    @classmethod
    def get_cooldown_storage(cls) -> BlobStorageInterface:
        return cls._cooldown_storage

    @classmethod
    def get_document_storage(cls) -> BlobStorageInterface:
        return cls._document_storage

    @classmethod
    def get_process_document_queue(cls) -> QueueInterface:
        return cls._doc_processing_queue

    @classmethod
    def get_ai_tracer(cls, metadata: Dict[str, Any] = {}) -> AITracer:
        from src.api.common.services.ai_tracer import AITracer

        return AITracer(cls._db_engine, metadata)

    @classmethod
    def get_async_openai_client(
        cls,
        base_provider: Literal[
            "openai", "azure-openai", "model-router"
        ] = "azure-openai",
    ) -> AsyncOpenAI | AsyncAzureOpenAI:
        from openai import AsyncAzureOpenAI, AsyncOpenAI

        settings = cls.get_application_settings()
        if base_provider == "openai":
            return AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
        elif base_provider == "azure-openai":
            return AsyncAzureOpenAI(
                api_key=settings.AZURE_OPENAI_API_KEY,
                api_version=settings.AZURE_API_VERSION,
                azure_endpoint=settings.AZURE_OPENAI_ENDPOINT,
            )
        elif base_provider == "model-router":
            return AsyncOpenAI(
                base_url=cls._application_settings.BASE_URL,
                default_headers=cls._application_settings.DEFAULT_HEADERS,
                api_key=cls._application_settings.ROUTER_API_KEY,
            )

    @classmethod
    def get_prompt_manager(cls) -> PromptManager:
        try:
            from ia_gen_core.prompts import PromptManager
        except ImportError:
            pass
        return PromptManager(db_engine=cls.get_database_engine())

    @classmethod
    def get_index_repository(cls) -> IndexRepository:
        from src.api.common.services import IndexRepository

        return IndexRepository(
            cls.get_async_database_engine(), cls.get_logger(), cls.get_ai_tracer()
        )

    @classmethod
    def get_mail_sender(cls) -> MailSender | None:
        from src.api.common.services import MailSender

        application_settings = cls.get_application_settings()
        if ApplicationEnvironment.get_current() != ApplicationEnvironment.LOCAL:
            return MailSender(
                mail_send_endpoint=application_settings.MAIL_SEND_ENDPOINT,
                mail_send_api_key=application_settings.SEND_EMAIL_API_KEY,
                logger=cls.get_logger(),
            )
        return None

    @classmethod
    def get_content_generator(cls, client_provider="azure-openai") -> ContentGenerator:
        from src.api.common.services import ContentGenerator

        return ContentGenerator(
            openai_client=cls.get_async_openai_client(client_provider),
            llm_manager=cls.get_llm_manager(),
            index_repository=cls.get_index_repository(),
            retriever=cls.get_retriever(),
            ai_tracer=cls.get_ai_tracer(),
            prompt_manager=cls.get_prompt_manager(),
            mermaid_base_url=cls.get_application_settings().MERMAID_BASE_URL,
            logger=cls.get_logger(),
        )

    @classmethod
    def get_content_regenerator(
        cls,
        model_provider: str = "azure-openai",
        model_name: str = GPT41,
        max_tokens: int = 4096,
        temperature: float = 0,
    ) -> ContentRegenerator:
        from src.api.common.services import ContentRegenerator

        llm_tracer = cls.get_ai_tracer(
            metadata={
                "class": "Content Generator",
                "model_info": {
                    "model_name": model_name,
                    "max_output_tokens": max_tokens,
                    "temperature": temperature,
                    "provider": model_provider,
                },
            }
        )
        return ContentRegenerator(
            llm=cls._llm.get_llm(
                provider=model_provider,
                model_name=model_name,
                max_tokens=max_tokens,
                temperature=temperature,
            ),
            llm_tracer=llm_tracer,
            db_engine=cls._db_engine,
            logger=cls.get_logger(),
            prompt_manager=cls.get_prompt_manager(),
        )

    @classmethod
    def _initialize_reranker(
        cls, local: bool = False, top_k: int = 5
    ):  # Top k could be env variable
        import cohere
        from langchain_cohere import CohereRerank

        from src.api.workflows.document_ingestion import Reranker

        application_settings = cls.get_application_settings()
        if local:
            cls._reranker = Reranker(top_k=top_k)
        else:
            co = cohere.ClientV2(
                base_url=application_settings.RERANK_AZURE_URL,
                api_key=application_settings.RERANK_AZURE_API_KEY,
            )
            cls._reranker = CohereRerank(
                client=co, model=application_settings.RERANK_AZURE_NAME, top_n=top_k
            )

    @classmethod
    def _initialize_retriever(cls, rerank: bool = True, type: str = "azure-openai"):
        from langchain.retrievers import ContextualCompressionRetriever

        from src.api.workflows.document_ingestion import PGVectorRetriever

        embedding_function = cls.get_embedding_function(type=type)
        if rerank:
            cls._initialize_reranker()
            base_retriever = PGVectorRetriever(
                db_engine=cls.get_database_engine(),
                async_db_engine=cls.get_async_database_engine(),
                top_k=40,
                embedding_function=embedding_function,
            )  # This could be env variables
            cls._retriever = ContextualCompressionRetriever(
                base_retriever=base_retriever, base_compressor=cls.get_reranker()
            )
        else:
            cls._retriever = PGVectorRetriever(
                db_engine=cls.get_database_engine(),
                async_db_engine=cls.get_async_database_engine(),
                embedding_function=embedding_function,
            )

    @classmethod
    def get_embedding_function(cls, type: str = "azure-openai") -> Any:
        from src.api.workflows.document_ingestion.embedding_functions import (
            OpenAIEmbeddingFunction,
        )

        application_settings = cls.get_application_settings()
        if type == "openai":
            embedding_function = OpenAIEmbeddingFunction(
                api_key=application_settings.OPENAI_API_KEY,
                model_name="text-embedding-3-large",
                dimensions=1536,
            )
        elif type == "azure-openai":
            embedding_function = OpenAIEmbeddingFunction(
                api_key=application_settings.AZURE_OPENAI_API_KEY,
                api_base=application_settings.AZURE_OPENAI_ENDPOINT,
                api_version=application_settings.AZURE_API_VERSION,
                model_name="text-embedding-3-large",
                dimensions=1536,
                api_type="azure",
            )
        elif type == "model-router":
            embedding_function = OpenAIEmbeddingFunction(
                api_base=application_settings.BASE_URL,
                default_headers={"use-case-id": "contenidos_embeddings"},
                api_key=application_settings.ROUTER_API_KEY,
                dimensions=1536,
                model_name="text-embedding-3-large",
            )
        return embedding_function

    @classmethod
    def get_generate_content_task(cls, n_concurrent: int = 1):
        from src.background_tasks.content_generation.content_generation_task import (
            ContentGenerationTask,
        )

        return ContentGenerationTask(
            cls.get_logger(),
            cls.get_content_generator(),
            cls.get_ai_tracer(),
            cls.get_content_generation_queue(),
            cls.get_database_engine(),
            ServiceBusConfig(n_concurrent=n_concurrent, batch_size=n_concurrent),
            cls.get_index_repository(),
            cls.get_mail_sender(),
        )

    @classmethod
    def get_process_document_task(cls, n_concurrent: int = 1):
        from src.background_tasks.process_document_task.process_document_task import (
            DocProcessingTask,
        )

        application_settings = cls.get_application_settings()
        config = ServiceBusConfig(n_concurrent=n_concurrent, batch_size=n_concurrent)
        return DocProcessingTask(
            cls.get_logger(),
            cls.get_process_document_queue(),
            cls.get_database_engine(),
            config,
            application_settings,
            cls.get_url_extractor(),
            cls.get_embedding_function(),
        )

    @classmethod
    def _initialize_logger(cls) -> None:
        logger = cls.get_logger()
        logger.setLevel(logging.INFO)

        if logger.hasHandlers():
            logger.handlers.clear()

        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )
        console_handler.setFormatter(console_formatter)
        logger.addHandler(console_handler)

        logger.propagate = False

    @classmethod
    def _initialize_llm_manager(cls):
        application_settings = cls.get_application_settings()
        cls._llm = LLM(
            openai_api_key=application_settings.OPENAI_API_KEY,
            anthropic_api_key=application_settings.ANTHROPIC_API_KEY,
            azure_api_key=application_settings.AZURE_OPENAI_API_KEY,
            azure_openai_endpoint=application_settings.AZURE_OPENAI_ENDPOINT,
            azure_api_version=application_settings.AZURE_API_VERSION,
            base_url=application_settings.BASE_URL,
            default_headers=application_settings.DEFAULT_HEADERS,
            router_api_key=application_settings.ROUTER_API_KEY,
        )

    @classmethod
    def _initialize_async_queue_client(cls):
        cls._async_queue_client = ServiceBusClient.from_connection_string(
            conn_str=cls._application_settings.SERVICE_BUS_CONNECTION_STRING
        )

    @classmethod
    def _initialize_application_settings(cls) -> None:
        cls._application_settings = ApplicationSettings()  # type: ignore

    @classmethod
    def _initialize_database_engines(cls) -> None:
        application_settings = cls.get_application_settings()
        logger = cls.get_logger()
        db_user = application_settings.DB_USER
        db_password = application_settings.DB_PASSWORD
        db_host = application_settings.DB_HOST
        db_port = application_settings.DB_PORT
        db_name = application_settings.DB_NAME
        sync_url = f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
        async_url = f"postgresql+asyncpg://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
        cls._db_engine = create_engine(
            url=sync_url,
            echo=False,
            pool_size=15,
            max_overflow=10,
            pool_timeout=30,
            pool_recycle=1800,
            connect_args={
                "connect_timeout": 30,
                "options": "-c statement_timeout=60000",
                "application_name": "ia_gestorcontenidosiagen_sync",
            },
        )
        cls._async_db_engine = create_async_engine(
            url=async_url,
            echo=False,
            pool_size=15,
            max_overflow=10,
            pool_timeout=30,
            pool_recycle=1800,
            connect_args={
                "timeout": 30,
                "command_timeout": 60,
                "server_settings": {
                    "application_name": "ia_gestorcontenidosiagen_async",
                },
            },
        )
        with Session(cls._db_engine) as session:
            try:
                session.execute(text("CREATE EXTENSION IF NOT EXISTS vector;"))
                session.execute(text('CREATE EXTENSION IF NOT EXISTS "uuid-ossp";'))
                session.commit()  # Commit the changes to the database
            except SQLAlchemyError as e:
                logger.error(f"Error creating extensions: {e}")
                session.rollback()
        SQLModel.metadata.create_all(bind=cls._db_engine)
        if ApplicationEnvironment.get_current() == ApplicationEnvironment.LOCAL:
            try:
                from src.domain import (
                    models_toolkit,  # importa metadata = MetaData(schema="toolkit")
                )

                with cls._db_engine.begin() as conn:
                    conn.execute(text('CREATE SCHEMA IF NOT EXISTS "toolkit";'))
                    conn.execute(text('CREATE SCHEMA IF NOT EXISTS "admin";'))
                # Crear tablas de ambos esquemas (admin y toolkit) usando metadata unificado
                models_toolkit.metadata.create_all(bind=cls._db_engine)
                logger.info("Esquemas y tablas 'toolkit' y 'admin' verificados/creados.")
            except Exception as e:
                logger.exception(f"No se pudieron crear las tablas del toolkit/admin: {e}")

        cls._session_factory = sessionmaker(
            bind=cls._db_engine, class_=Session, expire_on_commit=False
        )
        cls._async_session_factory = sessionmaker(
            bind=cls._async_db_engine, class_=AsyncSession, expire_on_commit=False
        )

        cls._create_initial_db_user()

    @classmethod
    def _create_initial_db_user(cls) -> None:
        logger = cls.get_logger()
        with cls._session_factory() as session:
            existing_user = session.query(User).filter(User.name == "Unir demo").first()
            if not existing_user:
                new_user = User(
                    name="Unir demo",
                    id="<EMAIL>",
                )
                session.add(new_user)
                session.commit()
                logger.info("Initial user 'Unir demo' created.")
            else:
                logger.info("Initial user 'Unir demo' already exists.")

    @classmethod
    def _create_and_update_prompts(cls) -> None:
        logger = cls.get_logger()
        try:
            from src.scripts.create_prompts import create_prompts

            create_prompts(cls.get_prompt_manager())
            logger.info("Created prompts successfully")
        except subprocess.CalledProcessError as e:
            logger.error(
                f"Error: Script execution failed with return code {e.returncode}"
            )
            logger.error(f"Output: {e.stdout}")
            logger.error(f"Error Message: {e.stderr}")
        except Exception as e:
            logger.error(f"Error creating prompts: {e}")

    @classmethod
    def _initialize_application_insights(cls) -> None:
        logger = DependencyContainer.get_logger()
        logger.setLevel(level=cls._application_settings.LOGGING_LEVEL)
        import os

        os.environ["OTEL_PYTHON_DISABLED_INSTRUMENTATIONS"] = (
            "sqlalchemy,django,flask,psycopg2,pymongo,redis"
        )

        try:
            from azure.monitor.opentelemetry import (
                configure_azure_monitor,  # type: ignore[reportUnknownVariableType]
            )

            if ApplicationEnvironment.get_current() != ApplicationEnvironment.LOCAL:
                configure_azure_monitor(
                    logger_name=cls._LOGGER_NAME,
                    connection_string=cls._application_settings.APPLICATIONINSIGHTS_CONNECTION_STRING,
                    enable_live_metrics=True,
                    instrumentation_options={
                        "sqlalchemy": {"enabled": False},
                        "django": {"enabled": False},
                        "flask": {"enabled": False},
                        "psycopg2": {"enabled": False},
                        "pymongo": {"enabled": False},
                        "redis": {"enabled": False},
                    },
                )
        except ImportError:
            logger.warning("Import error while initializing application insights")

    @classmethod
    def _initialize_observability(cls, provider="phoenix") -> None:
        from src.api.common.services.observability import PhoenixObservability

        settings = cls.get_application_settings()
        logger = cls.get_logger()
        if provider == "phoenix":
            cls._observability = PhoenixObservability(
                port=settings.PHOENIX_PORT,
                mode="server",
                logger=logger,
                collector_endpoint=settings.PHOENIX_COLLECTOR_ENDPOINT,
                oltp_headers=settings.OTEL_EXPORTER_OTLP_HEADERS,
                project_name=settings.PHOENIX_PROJECT_NAME,
            ).set_observability()

    @classmethod
    def _initialize_search_engine(cls, provider: str) -> None:
        logger = cls.get_logger()
        try:
            from src.api.common.services import (
                BraveSearchEngine,
                DuckDuckGoSearchEngine,
                JinaSearchEngine,
                TavilySearchEngine,
            )

            application_settings = cls.get_application_settings()
            if provider == "duckduckgo":
                cls._search_engine = DuckDuckGoSearchEngine(
                    logger=logger, reranker_model=cls.get_reranker()
                )
            elif provider == "tavily":
                if application_settings.TAVILY_SEARCH_API_KEY is None:
                    raise ValueError(
                        "TAVILY_SEARCH_API_KEY is required for Tavily search engine."
                    )
                cls._search_engine = TavilySearchEngine(
                    logger=logger,
                    api_key=application_settings.TAVILY_SEARCH_API_KEY,
                    reranker_model=cls.get_reranker(),
                )
            elif provider == "brave":
                if application_settings.BRAVE_SEARCH_API_KEY is None:
                    raise ValueError(
                        "BRAVE_SEARCH_API_KEY is required for Brave search engine."
                    )
                cls._search_engine = BraveSearchEngine(
                    logger=logger,
                    api_key=application_settings.BRAVE_SEARCH_API_KEY,
                    reranker_model=cls.get_reranker(),
                )
            elif provider == "jina":
                if application_settings.JINA_API_KEY is None:
                    raise ValueError("JINA_API_KEY is required for Jina search engine.")
                cls._search_engine = JinaSearchEngine(
                    logger=logger,
                    api_key=application_settings.JINA_API_KEY,
                    reranker_model=cls.get_reranker(),
                )
            else:
                raise ValueError(
                    f"Unsupported SEARCH_ENGINE: {application_settings.SEARCH_ENGINE}"
                )
        except Exception as e:
            logger.exception(f"Search engine not initialized: {e}")

    @classmethod
    def _initialize_url_extractor(cls, provider: str) -> None:
        logger = cls.get_logger()
        try:
            from src.api.common.services.url_extractor_engine import (
                JinaExtractor,
                TavilyExtractor,
            )

            application_settings = cls.get_application_settings()
            if provider == "jina":
                cls._url_extractor = JinaExtractor(
                    logger=logger, api_key=application_settings.JINA_API_KEY
                )
            elif provider == "tavily":
                if application_settings.TAVILY_SEARCH_API_KEY is None:
                    raise ValueError(
                        "TAVILY_EXTRACTOR_API_KEY is required for Tavily extractor."
                    )
                cls._url_extractor = TavilyExtractor(
                    logger=logger, api_key=application_settings.TAVILY_SEARCH_API_KEY
                )
            else:
                raise ValueError(f"Unsupported URL extractor: {provider}")
        except Exception as e:
            logger.exception(f"URL extractor not initialized: {e}")

    @classmethod
    def _initialize_local_queue_broker(cls) -> None:
        cls._local_queue_broker = LocalSqliteBroker(
            db_path="tmp/queue_broker.db", logger=cls.get_logger()
        )

    @classmethod
    def _get_local_queue_broker(cls) -> LocalSqliteBroker:
        if cls._local_queue_broker is None:
            cls._initialize_local_queue_broker()
        return cls._local_queue_broker

    @classmethod
    def _initialize_content_generation_queue(cls) -> None:
        application_settings = cls.get_application_settings()
        if application_settings.QUEUE_MODE == "local":
            cls._content_generation_queue = LocalQueueInterface(
                broker=cls._get_local_queue_broker(),
                queue_name=application_settings.CONTENT_GENERATION_QUEUE_NAME,
                logger=cls.get_logger(),
            )
        else:
            cls._content_generation_queue = ServiceBusQueueInterface(
                application_settings.SERVICE_BUS_CONNECTION_STRING,
                application_settings.CONTENT_GENERATION_QUEUE_NAME,
                cls.get_logger(),
            )

    @classmethod
    def _initialize_doc_processing_queue(cls) -> None:
        application_settings = cls.get_application_settings()
        if application_settings.QUEUE_MODE == "local":
            cls._doc_processing_queue = LocalQueueInterface(
                broker=cls._get_local_queue_broker(),
                queue_name=application_settings.PROCESS_DOCUMENT_QUEUE_NAME,
                logger=cls.get_logger(),
            )
        else:
            cls._doc_processing_queue = ServiceBusQueueInterface(
                application_settings.SERVICE_BUS_CONNECTION_STRING,
                application_settings.PROCESS_DOCUMENT_QUEUE_NAME,
                cls.get_logger(),
            )

    @classmethod
    def _initialize_cooldown_storage(cls) -> None:
        from src.api.workflows.document_ingestion.store import BlobStorageInterface

        application_settings = cls.get_application_settings()
        if application_settings.COOLDOWN_AZURE_STORAGE_CONNECTION_STRING is None:
            raise ValueError(
                "COOLDOWN_AZURE_STORAGE_CONNECTION_STRING is required for cooldown storage."
            )
        cls._cooldown_storage = BlobStorageInterface(
            application_settings.COOLDOWN_AZURE_STORAGE_CONNECTION_STRING,
            logger=cls.get_logger(),
        )

    @classmethod
    def _initialize_document_storage(cls) -> None:
        from src.api.workflows.document_ingestion.store import BlobStorageInterface

        application_settings = cls.get_application_settings()
        if application_settings.AZURE_STORAGE_CONNECTION_STRING is None:
            raise ValueError(
                "AZURE_STORAGE_CONNECTION_STRING is required for cooldown storage."
            )
        cls._document_storage = BlobStorageInterface(
            application_settings.AZURE_STORAGE_CONNECTION_STRING,
            logger=cls.get_logger(),
        )

    @classmethod
    def get_generate_reference_from_text_workflow(cls) -> GetSectionsByTopicWorkflow:
        from src.api.workflows.texts.generate_reference.generate_reference_workflow import (
            GenerateReferenceFromTextWorkflow,
        )

        return GenerateReferenceFromTextWorkflow(
            openai_client=cls.get_async_openai_client(),
            search_agent=cls.get_search_agent(),
            logger=cls.get_logger(),
        )

    #################################################################
    ########################   TOOLKIT ##############################
    #################################################################

    @classmethod
    def get_generate_rubrics_workflow(cls) -> GenerateRubricsWorkflow:
        from src.api.workflows.rubrics.generate_rubrics.workflow import (
            GenerateRubricsWorkflow,
        )

        return GenerateRubricsWorkflow(
            cls.get_async_openai_client(),
            cls.get_async_database_engine(),
            cls.get_logger(),
        )

    @classmethod
    def get_extract_criteria_workflow(cls) -> ExtractCriteriaWorkflow:
        from src.api.workflows.rubrics.extract_criteria.workflow import (
            ExtractCriteriaWorkflow,
        )

        return ExtractCriteriaWorkflow(cls.get_async_openai_client(), cls.get_logger())

    @classmethod
    def get_generate_activities_workflow(cls) -> GenerateActivitiesWorkflow:
        from src.api.workflows.activities.generate_activity.workflow import (
            GenerateActivitiesWorkflow,
        )

        return GenerateActivitiesWorkflow(
            cls.get_async_openai_client(), cls.get_logger()
        )

    @classmethod
    def get_generate_activities_unipro_workflow(
        cls,
    ) -> GenerateActivitiesUniproWorkflow:
        from src.api.workflows.activities.generate_activity_unipro.workflow import (
            GenerateActivitiesUniproWorkflow,
        )

        return GenerateActivitiesUniproWorkflow(
            cls.get_async_openai_client(),
            cls.get_logger(),
            db_engine=cls.get_async_database_engine(),
        )

    @classmethod
    def get_generate_tutoring_scripts_workflow(cls) -> GenerateTutoringScriptWorkflow:
        from src.api.workflows.tutoring_scripts.generate_tutoring_scripts.workflow import (
            GenerateTutoringScriptWorkflow,
        )

        return GenerateTutoringScriptWorkflow(
            cls.get_async_openai_client("model-router"),
            cls.get_logger(),
            cls.get_search_agent(),
            db_engine=cls.get_async_database_engine(),
        )

    @classmethod
    def get_generate_validation_plan_workflow(
        cls,
    ) -> GenerateValidationPlanWorkflow:
        from src.api.workflows.validation_tests.generate_plan.workflow import (
            GenerateValidationPlanWorkflow,
        )

        return GenerateValidationPlanWorkflow(
            openai_client=cls.get_async_openai_client("model-router"),
            logger=cls.get_logger(),
            db_engine=cls.get_async_database_engine(),
        )

    @classmethod
    def get_generate_validation_test_workflow(
        cls,
    ) -> GenerateValidationTestWorkflow:
        from src.api.workflows.validation_tests.generate.workflow import (
            GenerateValidationTestWorkflow,
        )

        return GenerateValidationTestWorkflow(
            openai_client=cls.get_async_openai_client("model-router"),
            logger=cls.get_logger(),
            db_engine=cls.get_async_database_engine(),
        )
