def check_for_custom_llm_lc(
    provider: str,
    model_name: str | None,
    max_tokens: int = 4096,
    temperature: int = 0,
    reasoning_effort: str | None = None,
):
    from src.api.common.dependency_container import DependencyContainer

    logger = DependencyContainer.get_logger()
    logger.debug("Checking for custom llm")
    llm_manager = DependencyContainer.get_llm_manager()
    available_models = llm_manager.get_available_models(provider)
    if model_name in available_models:
        llm = llm_manager.get_llm(
            provider=provider,
            model_name=model_name,
            max_tokens=max_tokens,
            temperature=temperature,
            reasoning_effort=reasoning_effort,
        )
        logger.info(
            f"Using custom model with provider: {provider} and model name: {model_name}"
        )
    else:
        logger.exception(
            f"Exception while checking for custom llm, combination of provider: {provider} and model_name: {model_name} not found, returning None"
        )
        return None
    return llm
