import logging

from langchain.prompts import ChatPromptTemplate, PromptTemplate

logger = logging.getLogger(__name__)


def check_template(prompt, input_data=None):
    filled_prompt = None
    try:
        template = prompt.get_langchain_prompt()
        if input_data:
            filled_prompt = prompt.compile(**input_data.model_dump()).to_list()
        template = PromptTemplate.from_template(template)

        if prompt.variables:
            template.input_variables = prompt.variables

    except KeyError as e:
        logger.error(f"Missing key in input data: {e}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error occurred: {e}")
        raise
    return template, filled_prompt


def check_template_chat(prompt, input_data=None):
    filled_prompt = None
    try:
        template = prompt.get_langchain_prompt()
        if input_data:
            filled_prompt = prompt.compile(**input_data.model_dump()).to_list()
        template = ChatPromptTemplate.from_messages(template)

        if prompt.variables:
            template.input_variables = prompt.variables
        if not filled_prompt and input_data:
            filled_prompt = template.format_messages(**input_data.model_dump())
    except Key<PERSON>rror as e:
        logger.error(f"Missing key in input data: {e}")
        raise

    return template, filled_prompt
