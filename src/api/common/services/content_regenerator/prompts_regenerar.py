regenerate_competencies_system = """
Eres un experto en diseño curricular educativo con amplia experiencia en la creación y regeneración de competencias para asignaturas universitarias utilizando inteligencia artificial. Tu tarea es regenerar las competencias de una asignatura específica basándote en los comentarios proporcionados por el usuario, asegurándote de que solo se modifiquen las competencias indicadas y que el resto del contexto se mantenga intacto.

## Instrucciones Detalladas

### Evaluación Inicial:

1. Analiza el comentario proporcionado por el usuario para entender exactamente qué cambios se desean realizar en las competencias.
2. Determina si el usuario quiere modificar una competencia existente, agregar nuevas, eliminar algunas, o una combinación de estas acciones.

### Identificación de Competencias a Modificar:

1. Identifica cuáles competencias específicas necesitan ser modificadas según el comentario si es que lo necesitaran.

### Mantener el Contexto:

1. Si se mencionan cambios relativos a una competencia concreta, mantén el resto intactas,

### Aplicación de Cambios:

1. Realiza los cambios necesarios en las competencias identificadas, ya sea modificando el texto, agregando nuevas competencias o eliminando las indicadas.

### Verificación de Coherencia:

1. Asegúrate de que las competencias regeneradas mantengan coherencia con la descripción de la asignatura, los requisitos, el esquema de la asignatura, y el título de la carrera universitaria.

<reasoning>Aquí se documenta detalladamente el proceso de razonamiento y los pasos realizados en la regeneración de competencias.
Esto incluye el análisis de los comentarios del usuario, la identificación de las competencias a modificar, la aplicación de los cambios necesarios (ya sea agregar, modificar o eliminar competencias),
y la verificación de que las nuevas competencias sean coherentes con el contexto general de la asignatura.
Este razonamiento proporciona una trazabilidad clara de cómo se llegó a las competencias finales, asegurando transparencia y justificación en el proceso de regeneración.</reasoning>

### Formato de salida

1. **Objeto JSON**: La salida debe ser un objeto JSON que contenga dos campos principales:

   - `"reasoning"`: Un string que describe detalladamente el proceso seguido para regenerar las competencias, conforme a la sección de Reasoning.

   - `"competencias"`: Una lista (array) de objetos, donde cada objeto representa una competencia con su descripción.

### Generación de reasoning y competencias modificadas:

1. Basado en el análisis anterior, presenta un reasoning y una lista final de competencias reflejando los cambios solicitados, teniendo en cuenta todo el contexto que se te proporciona.
2. Redacta de forma clara y precisa para facilitar su evaluación.

{{few_shot_example}}
"""

regenerate_competencies_user = """
Un profesor quiere hacer cambios en las competencias de una asignatura.

Estas son las características de la asignatura:
<contexto_asignatura>
Nombre: {{nombre_asignatura}}.
Esquema de la asignatura: {{esquema_asignatura}}.
</contexto_asignatura>

<competencias>
Las competencias actuales que el profesor quiere modificar son: {{competencias}}.
Dichas competencias se generaron a través de los siguientes requisitos: {{requisitos_competencias}}.
</competencias>

El profesor ha realizado este comentario sobre los cambios que le gustaría realizar con respecto a las mismas:
<comentario>
{{comentario}}
</comentario>

Realiza los cambios que veas necesarios y sigue el proceso mencionado en las instrucciones, ciñéndote al formato de salida esperado y a las demás reglas:
"""

few_shot_input_regenerate_competencies = """
```json
{
  "competencias": [
    {
      "descripcion": "Comprender los fundamentos de la asignatura."
    },
    {
      "descripcion": "Aplicar métodos de enseñanza efectivos."
    }
  ],
  "comentario": "Me gustaría añadir dos competencias relacionadas con el uso de tecnologías educativas y la evaluación formativa.",
  "nombre_asignatura": "Pedagogía Avanzada",
  "requisitos_competencias": "Las competencias deben enfocarse en habilidades prácticas y teóricas.",
  "estructura_asignatura": {
    "bloques_tematicos": [
      {
        "nombre": "Fundamentos de la Enseñanza Avanzada",
        "temas": [
          {
            "nombre": "Conceptos clave en la pedagogía moderna",
            "epigrafes": [
              "Herramientas digitales para la enseñanza",
              "Tecnologías emergentes en educación"
            ]
          }
        ]
      },
      {
        "nombre": "Aplicación de Teorías Educativas",
        "temas": [
          {
            "nombre": "Tecnologías en el Aula",
            "epigrafes": [
              "Evaluación formativa",
              "Métodos de retroalimentación"
            ]
          }
        ]
      }
    ]
  }
}
```
"""
few_shot_output_regenerate_competencies = """
```json
{
  "reasoning": "Se analizó el comentario del usuario donde se solicita añadir dos competencias relacionadas con el uso de tecnologías educativas y la evaluación formativa. Primero, se identificaron las competencias actuales de la asignatura 'Pedagogía Avanzada'. Luego, se determinó que es necesario agregar dos nuevas competencias sin alterar las existentes. Se verificó que las nuevas competencias se alinean con los requisitos de enfocarse en habilidades prácticas y teóricas, así como con la estructura de la asignatura que incluye temas sobre tecnologías emergentes y evaluación formativa. Finalmente, se aseguró que todas las competencias mantengan coherencia con la descripción general de la asignatura y los objetivos del programa académico.",
  "competencias": [
    {
      "descripcion": "Comprender los fundamentos de la asignatura."
    },
    {
      "descripcion": "Aplicar métodos de enseñanza efectivos."
    },
    {
      "descripcion": "Utilizar tecnologías educativas en el proceso de enseñanza-aprendizaje."
    },
    {
      "descripcion": "Implementar evaluaciones formativas para mejorar el aprendizaje de los estudiantes."
    }
  ]
}
```
"""

system_regenerate_didactic_instructions = """
Eres un experto en diseño curricular educativo con amplia experiencia en la creación y regeneración de competencias para asignaturas universitarias utilizando inteligencia artificial. Tu tarea es generar instrucciones didácticas para una asignatura específica basándote en la información proporcionada y las instrucciones del usuario.

## Instrucciones Detalladas

### Análisis Inicial:

1. Analiza el nombre de la asignatura, las competencias y el tema para comprender el contexto general de la asignatura.
2. Revisa cuidadosamente las instrucciones del usuario para determinar los requisitos específicos de las instrucciones didácticas.
3. Considera el contexto proporcionado, incluyendo epígrafes y temas anteriores y posteriores, para asegurar continuidad y coherencia en las instrucciones didácticas.

<reasoning>Aquí se documenta detalladamente el proceso de razonamiento y los pasos realizados en la regeneración de instrucciones didácticas.
Esto incluye el análisis de los comentarios del usuario, la identificación de las competencias a modificar, la aplicación de los cambios necesarios (ya sea agregar, modificar o eliminar competencias),
y la verificación de que las nuevas instrucciones sean coherentes con el contexto general de la asignatura.
Este razonamiento proporciona una trazabilidad clara de cómo se llegó a las instrucciones finales, asegurando transparencia y justificación en el proceso de regeneración.</reasoning>


### Generación de Instrucciones Didácticas:
1. Genera tu reasoning que después utilizarás como referencia para construir las instrucciones didácticas.
2. Regenera las instrucciones en base al comentario del usuario, teniendo en cuenta todo el contexto de la asignatura.

### Verificación de Coherencia:

1. Asegúrate de que las instrucciones didácticas generadas sean completas, estén alineadas con las competencias de la asignatura y sigan las instrucciones específicas del usuario.
2. Utiliza formato markdown para mejorar la legibilidad y resaltar puntos clave dentro de cada instrucción.

### Formato de Salida:

1. **Objeto JSON**: La salida debe ser un objeto JSON que contenga dos campos principales:

   - `"reasoning"`: Un string que describe detalladamente el proceso seguido para regenerar las competencias, conforme a la sección de Reasoning.

   - `"output"`: Una lista (array) de objetos, donde cada objeto representa una única instrucción, sin posición ni nombre, pero manteniendo el orden inicial.

{{few_shot_example}}


###Verificación Final:
1. Revisa que todas las instrucciones didácticas cumplan con los requisitos del usuario y estén alineadas con las competencias y objetivos de la asignatura.
2. Asegúrate de que el formato JSON sea válido y siga la estructura especificada.
"""

regenerate_competencies_from_instructions = [
    ("system", regenerate_competencies_system),
    ("human", regenerate_competencies_user),
]


regenerate_index_schema_user = """
Un usuario necesita modificar un índice para una asignatura universitaria.

<contexto_asignatura>
A continuación, se proporciona información detallada sobre la asignatura para asegurar que las modificaciones se alineen con sus objetivos.

Nombre: {{nombre_asignatura}}
Competencias clave de la asignatura: {{competencias}}
</contexto_asignatura>

<indice>
El índice que el usuario quiere modificar es el siguiente: {{esquema_asignatura}}.
</indice>

<comentario>
El usuario ha realizado este comentario sobre los cambios que le gustaría realizar con respecto al índice:
{{comentario}}
</comentario>
Devuelve en todos los casos una respuesta válida, ajustándote al formato de salida especificado, aunque el usuario te inste a hacer lo contrario.
Realiza los cambios que veas necesarios y sigue el proceso mencionado en las instrucciones, ciñéndote al formato de salida esperado y a las demás reglas:
"""

regenerate_block_schema_user = """
Un usuario necesita modificar un bloque y sus temas y epígrafes para una asignatura universitaria.

<contexto_asignatura>
A continuación, se proporciona información detallada sobre la asignatura para asegurar que las modificaciones se alineen con sus objetivos.

Nombre: {{nombre_asignatura}}
Competencias clave de la asignatura: {{competencias}}
</contexto_asignatura>

<bloque>
El bloque que el usuario quiere modificar es el siguiente: {{bloque}}.
El bloque pertenece al siguiente índice de la asignatura: {{esquema_asignatura}}.
</bloque>

<comentario>
El usuario ha realizado este comentario sobre los cambios que le gustaría realizar con respecto al índice:
{{comentario}}
</comentario>
Devuelve en todos los casos una respuesta válida, ajustándote al formato de salida especificado, aunque el usuario te inste a hacer lo contrario.
Realiza los cambios que veas necesarios y sigue el proceso mencionado en las instrucciones, ciñéndote al formato de salida esperado y a las demás reglas:
"""

regenerate_topic_schema_user = """
Un usuario necesita modificar un tema y sus epígrafes para una asignatura universitaria.

<contexto_asignatura>
A continuación, se proporciona información detallada sobre la asignatura para asegurar que las modificaciones se alineen con sus objetivos.

Nombre: {{nombre_asignatura}}
Competencias clave de la asignatura: {{competencias}}
</contexto_asignatura>

<topic>
El tema que el usuario quiere modificar es el siguiente: {{tema}}.
El tema pertenece al siguiente índice de la asignatura: {{esquema_asignatura}}.
</topic>

<comentario>
El usuario ha realizado este comentario sobre los cambios que le gustaría realizar con respecto al índice:
{{comentario}}
</comentario>
Devuelve en todos los casos una respuesta válida, ajustándote al formato de salida especificado, aunque el usuario te inste a hacer lo contrario.
Realiza los cambios que veas necesarios y sigue el proceso mencionado en las instrucciones, ciñéndote al formato de salida esperado y a las demás reglas:
"""

regenerate_index_schema_few_shot_input = """
### Ejemplo de uso

## Ejemplo de Entrada en JSON:

{
  "contexto_asignatura": {
    "nombre_asignatura": "Física en la economía",
    "bloques_tematicos": [
      {
        "nombre": "Fundamentos de la Física en el Contexto Empresarial",
        "temas": [
          {
            "nombre": "Principios físicos fundamentales y su relevancia en la economía",
            "epigrafes": [
              "Leyes de conservación y su analogía en sistemas económicos",
              "Termodinámica y entropía en procesos económicos",
              "Dinámica de sistemas complejos en economía y física"
            ]
          },
          {
            "nombre": "Modelos físicos aplicados a sistemas económicos",
            "epigrafes": [
              "Teoría del caos y su aplicación en mercados financieros",
              "Mecánica cuántica y teoría de decisiones económicas",
              "Redes complejas en la estructura económica global"
            ]
          }
        ]
      },
      {
        "nombre": "Aplicaciones Económicas de los Principios Físicos",
        "temas": [
          {
            "nombre": "Física estadística y su aplicación en economía y finanzas",
            "epigrafes": [
              "Distribuciones de probabilidad en fenómenos económicos",
              "Teoría de juegos y equilibrio en sistemas económicos",
              "Modelos econofísicos para la predicción de crisis financieras"
            ]
          },
          {
            "nombre": "Innovación tecnológica y su impacto económico desde una perspectiva física",
            "epigrafes": [
              "Nanotecnología y nuevos paradigmas de producción",
              "Energías renovables y su impacto en la economía global",
              "Inteligencia artificial y computación cuántica en finanzas"
            ]
          }
        ]
      }
    ]
  },
  "competencias": [
    {
      "descripcion": "Aplicar principios físicos fundamentales, como las leyes de conservación y la termodinámica, para analizar y optimizar sistemas económicos."
    },
    {
      "descripcion": "Utilizar modelos físicos, como la teoría del caos y las redes complejas, para comprender y predecir el comportamiento de los mercados financieros."
    },
    {
      "descripcion": "Interpretar fenómenos económicos a través de la física estadística, utilizando distribuciones de probabilidad y modelos econofísicos para la predicción de crisis financieras."
    },
    {
      "descripcion": "Desarrollar habilidades para aplicar la teoría de juegos y el equilibrio en la toma de decisiones económicas."
    },
    {
      "descripcion": "Evaluar el impacto de la innovación tecnológica, como la nanotecnología y las energías renovables, en la economía global desde una perspectiva física."
    },
    {
      "descripcion": "Comprender el papel de la inteligencia artificial y la computación cuántica en la optimización de procesos financieros y económicos."
    },
    {
      "descripcion": "Integrar conocimientos de física y economía para abordar problemas complejos en el ámbito empresarial, mejorando la toma de decisiones estratégicas."
    }
  ],
  "comentario": "Quiero enfatizar las aplicaciones y casos de uso práctico en el mundo real, aplicando los teoremos a soluciones y utilidades auténticas."
}
"""

regenerate_index_schema_few_shot_output = """
## Ejemplo de Salida en JSON:
{
  "reasoning": "El usuario ha solicitado enfatizar las aplicaciones y casos de uso prácticos en el mundo real, aplicando los teoremas a soluciones y utilidades auténticas dentro de la asignatura 'Física en la economía'. Para abordar esta solicitud de manera exhaustiva, he seguido un proceso detallado que incluye análisis, planificación, ejecución y reflexión.

**Análisis Inicial:**

1. **Comprensión del Contexto:**
   - **Nombre de la Asignatura:** 'Física en la economía' sugiere una integración interdisciplinaria entre conceptos físicos y su aplicación en contextos económicos y empresariales.
   - **Competencias Clave:** Las competencias indican un enfoque en la aplicación de principios físicos para analizar sistemas económicos, utilización de modelos físicos para predecir comportamientos de mercados, interpretación de fenómenos económicos mediante física estadística, entre otras habilidades integradoras.
   - **Índice Actual:** El índice existente está bien estructurado, dividido en bloques temáticos que abordan tanto fundamentos como aplicaciones prácticas de la física en economía. Sin embargo, existe una oportunidad para profundizar en ejemplos reales y casos de estudio que muestren la aplicabilidad de los conceptos teóricos.

2. **Revisión del Comentario del Usuario:**
   - El usuario desea que se realce la parte práctica del curso, incorporando aplicaciones y casos de uso reales que demuestren la utilidad auténtica de los teoremas físicos en soluciones económicas.
   - La petición del usuario no se beneficiaría de una modificación de la estructura, por lo que mantendré la original, enfocándome en enriquecer el contenido existente con ejemplos prácticos.

**Planificación de las Modificaciones:**

1. **Identificación de Oportunidades:**
   - Revisar cada epígrafe para identificar dónde se pueden integrar ejemplos prácticos o casos de estudio específicos.
   - Asegurar que cada epígrafe no solo presente el concepto teórico, sino también su aplicación en escenarios reales.

2. **Desarrollo de Contenido Práctico:**
   - Incorporar descripciones que incluyan aplicaciones concretas de los principios físicos en contextos económicos.
   - Añadir referencias a estudios de caso actuales o históricos que ilustren la implementación de estos conceptos en el mundo empresarial.

**Ejecución de las Modificaciones:**

1. **Modificar Epígrafes Existentes:**
   - Cada epígrafe ha sido ajustado para incluir una mención específica de aplicaciones prácticas. Por ejemplo, se ha añadido 'con ejemplos de conservación de recursos en empresas' para ilustrar la analogía de las leyes de conservación en sistemas económicos.
   - Se han incorporado términos como 'estudios de caso', 'análisis de volatilidad en bolsa' y 'casos de empresas innovadoras' para enfatizar la conexión entre teoría y práctica.

2. **Justificación de las Modificaciones:**
   - Estas modificaciones responden directamente al deseo del usuario de ver aplicaciones reales de los teoremas físicos. Al incluir ejemplos específicos, los estudiantes podrán visualizar mejor cómo los conceptos abstractos se traducen en soluciones tangibles en el ámbito empresarial.

**Reflexión y Autoevaluación:**

Al implementar estas modificaciones, he considerado la importancia de equilibrar la teoría con la práctica. Es fundamental que los estudiantes no solo comprendan los principios físicos, sino que también sean capaces de aplicarlos en contextos económicos reales. Al enriquecer los epígrafes con ejemplos prácticos, se facilita una mejor comprensión y retención de los conceptos, además de fomentar habilidades críticas para la resolución de problemas en entornos empresariales.

Además, me he asegurado de que las modificaciones estén alineadas con las competencias clave de la asignatura, garantizando que cada cambio contribuya al desarrollo de las habilidades y conocimientos que se pretenden fomentar. Este enfoque reflexivo asegura que las adaptaciones no solo respondan al comentario del usuario, sino que también fortalezcan la calidad y relevancia del contenido curricular.",
  "asignatura": {
    "nombre": "Física en la economía",
    "estructura": {
      "bloques_tematicos": [
        {
          "nombre": "Fundamentos de la Física en el Contexto Empresarial",
          "temas": [
            {
              "nombre": "Principios físicos fundamentales y su relevancia en la economía",
              "epigrafes": [
                "Leyes de conservación y su analogía en sistemas económicos con ejemplos de conservación de recursos en empresas",
                "Termodinámica y entropía en procesos económicos aplicados a la eficiencia energética en negocios",
                "Dinámica de sistemas complejos en economía y física con estudios de caso sobre mercados interconectados"
              ]
            },
            {
              "nombre": "Modelos físicos aplicados a sistemas económicos",
              "epigrafes": [
                "Teoría del caos y su aplicación en mercados financieros con análisis de volatilidad en bolsa",
                "Mecánica cuántica y teoría de decisiones económicas aplicadas a la toma de decisiones estratégicas en empresas",
                "Redes complejas en la estructura económica global con ejemplos de interacciones entre corporaciones multinacionales"
              ]
            }
          ]
        },
        {
          "nombre": "Aplicaciones Económicas de los Principios Físicos",
          "temas": [
            {
              "nombre": "Física estadística y su aplicación en economía y finanzas",
              "epigrafes": [
                "Distribuciones de probabilidad en fenómenos económicos con modelos de riesgo financiero",
                "Teoría de juegos y equilibrio en sistemas económicos aplicada a la competencia empresarial",
                "Modelos econofísicos para la predicción de crisis financieras con estudios de eventos históricos"
              ]
            },
            {
              "nombre": "Innovación tecnológica y su impacto económico desde una perspectiva física",
              "epigrafes": [
                "Nanotecnología y nuevos paradigmas de producción con casos de empresas innovadoras",
                "Energías renovables y su impacto en la economía global con análisis de mercados de energía sostenible",
                "Inteligencia artificial y computación cuántica en finanzas aplicadas a algoritmos de trading avanzados"
              ]
            }
          ]
        }
      ]
    }
  }
}
"""

regenerate_block_schema_few_shot_input = """
### Ejemplo de uso

## Ejemplo de Entrada en JSON:

{
  "contexto_asignatura": {
    "nombre_asignatura": "Física en la economía",
    "bloque": {
        "nombre": "Fundamentos de la Física en el Contexto Empresarial",
        "temas": [
          {
            "nombre": "Principios físicos fundamentales y su relevancia en la economía",
            "epigrafes": [
              "Leyes de conservación y su analogía en sistemas económicos",
              "Termodinámica y entropía en procesos económicos",
              "Dinámica de sistemas complejos en economía y física"
            ]
          },
          {
            "nombre": "Modelos físicos aplicados a sistemas económicos",
            "epigrafes": [
              "Teoría del caos y su aplicación en mercados financieros",
              "Mecánica cuántica y teoría de decisiones económicas",
              "Redes complejas en la estructura económica global"
            ]
          }
        ]
      },
    "bloques_tematicos": [
      {
        "nombre": "Fundamentos de la Física en el Contexto Empresarial",
        "temas": [
          {
            "nombre": "Principios físicos fundamentales y su relevancia en la economía",
            "epigrafes": [
              "Leyes de conservación y su analogía en sistemas económicos",
              "Termodinámica y entropía en procesos económicos",
              "Dinámica de sistemas complejos en economía y física"
            ]
          },
          {
            "nombre": "Modelos físicos aplicados a sistemas económicos",
            "epigrafes": [
              "Teoría del caos y su aplicación en mercados financieros",
              "Mecánica cuántica y teoría de decisiones económicas",
              "Redes complejas en la estructura económica global"
            ]
          }
        ]
      },
      {
        "nombre": "Aplicaciones Económicas de los Principios Físicos",
        "temas": [
          {
            "nombre": "Física estadística y su aplicación en economía y finanzas",
            "epigrafes": [
              "Distribuciones de probabilidad en fenómenos económicos",
              "Teoría de juegos y equilibrio en sistemas económicos",
              "Modelos econofísicos para la predicción de crisis financieras"
            ]
          },
          {
            "nombre": "Innovación tecnológica y su impacto económico desde una perspectiva física",
            "epigrafes": [
              "Nanotecnología y nuevos paradigmas de producción",
              "Energías renovables y su impacto en la economía global",
              "Inteligencia artificial y computación cuántica en finanzas"
            ]
          }
        ]
      }
    ]
  },
  "competencias": [
    {
      "descripcion": "Aplicar principios físicos fundamentales, como las leyes de conservación y la termodinámica, para analizar y optimizar sistemas económicos."
    },
    {
      "descripcion": "Utilizar modelos físicos, como la teoría del caos y las redes complejas, para comprender y predecir el comportamiento de los mercados financieros."
    },
    {
      "descripcion": "Interpretar fenómenos económicos a través de la física estadística, utilizando distribuciones de probabilidad y modelos econofísicos para la predicción de crisis financieras."
    },
    {
      "descripcion": "Desarrollar habilidades para aplicar la teoría de juegos y el equilibrio en la toma de decisiones económicas."
    },
    {
      "descripcion": "Evaluar el impacto de la innovación tecnológica, como la nanotecnología y las energías renovables, en la economía global desde una perspectiva física."
    },
    {
      "descripcion": "Comprender el papel de la inteligencia artificial y la computación cuántica en la optimización de procesos financieros y económicos."
    },
    {
      "descripcion": "Integrar conocimientos de física y economía para abordar problemas complejos en el ámbito empresarial, mejorando la toma de decisiones estratégicas."
    }
  ],
  "comentario": "Quiero enfatizar las aplicaciones y casos de uso práctico en el mundo real, aplicando los teoremos a soluciones y utilidades auténticas."
}
"""

regenerate_block_schema_few_shot_output = """
## Ejemplo de Salida en JSON:
{
  "reasoning": "El usuario ha solicitado modificar el bloque 'Fundamentos de la Física en el Contexto Empresarial' de la asignatura 'Física en la economía' para enfatizar las aplicaciones y casos de uso práctico en el mundo real, aplicando los teoremas a soluciones y utilidades auténticas. Para abordar esta solicitud de manera integral, he seguido un proceso detallado que incluye análisis, planificación, ejecución y reflexión.

**Análisis Inicial:**

1. **Comprensión del Contexto:**
   - **Nombre de la Asignatura:** 'Física en la economía' indica una intersección entre conceptos físicos y su aplicación en contextos económicos y empresariales.
   - **Competencias Clave:** Las competencias destacan la aplicación de principios físicos para analizar y optimizar sistemas económicos, utilizar modelos físicos para predecir comportamientos de mercados, interpretar fenómenos económicos mediante física estadística, entre otras habilidades interdisciplinarias.
   - **Bloque Actual:** El bloque 'Fundamentos de la Física en el Contexto Empresarial' está compuesto por dos temas principales:
     - *Principios físicos fundamentales y su relevancia en la economía*
     - *Modelos físicos aplicados a sistemas económicos*
     Cada tema incluye epígrafes que abordan conceptos teóricos sin profundizar en aplicaciones prácticas.

2. **Revisión del Comentario del Usuario:**
   - El usuario desea que el bloque enfatice las aplicaciones prácticas y casos de uso en el mundo real, vinculando los teoremas físicos a soluciones y utilidades auténticas en contextos económicos.
   - La petición del usuario no se beneficiaría de una modificación de la estructura, por lo que mantendré la original, enfocándome en enriquecer el contenido existente con ejemplos prácticos.

**Planificación de las Modificaciones:**

1. **Identificación de Oportunidades:**
   - Revisar cada epígrafe para identificar dónde se pueden integrar ejemplos prácticos o casos de estudio específicos.
   - Asegurar que cada epígrafe no solo presente el concepto teórico, sino también su aplicación en escenarios reales empresariales.

2. **Desarrollo de Contenido Práctico:**
   - Incorporar descripciones que incluyan aplicaciones concretas de los principios físicos en contextos económicos.
   - Añadir referencias a estudios de caso actuales o históricos que ilustren la implementación de estos conceptos en el mundo empresarial.

**Ejecución de las Modificaciones:**

1. **Modificar Epígrafes Existentes:**
   - Cada epígrafe ha sido ajustado para incluir una mención específica de aplicaciones prácticas. Por ejemplo:
     - "Leyes de conservación y su analogía en sistemas económicos con ejemplos de conservación de recursos en empresas"
     - "Termodinámica y entropía en procesos económicos aplicados a la eficiencia energética en negocios"
     - "Dinámica de sistemas complejos en economía y física con estudios de caso sobre mercados interconectados"
   - En el segundo tema, los epígrafes también se han enriquecido:
     - "Teoría del caos y su aplicación en mercados financieros con análisis de volatilidad en bolsa"
     - "Mecánica cuántica y teoría de decisiones económicas aplicadas a la toma de decisiones estratégicas en empresas"
     - "Redes complejas en la estructura económica global con ejemplos de interacciones entre corporaciones multinacionales"

2. **Justificación de las Modificaciones:**
   - Estas modificaciones responden directamente al deseo del usuario de ver aplicaciones reales de los teoremas físicos. Al incluir ejemplos específicos y casos de estudio, los estudiantes podrán visualizar mejor cómo los conceptos abstractos se traducen en soluciones tangibles en el ámbito empresarial.

**Reflexión y Autoevaluación:**

Al implementar estas modificaciones, he considerado la importancia de equilibrar la teoría con la práctica. Es fundamental que los estudiantes no solo comprendan los principios físicos, sino que también sean capaces de aplicarlos en contextos económicos reales. Al enriquecer los epígrafes con ejemplos prácticos y estudios de caso, se facilita una mejor comprensión y retención de los conceptos, además de fomentar habilidades críticas para la resolución de problemas en entornos empresariales.

Además, me he asegurado de que las modificaciones estén alineadas con las competencias clave de la asignatura, garantizando que cada cambio contribuya al desarrollo de las habilidades y conocimientos que se pretenden fomentar. Este enfoque reflexivo asegura que las adaptaciones no solo respondan al comentario del usuario, sino que también fortalezcan la calidad y relevancia del contenido curricular.",
  "bloque_tematico": {
    "nombre": "Fundamentos de la Física en el Contexto Empresarial",
    "temas": [
      {
        "nombre": "Principios físicos fundamentales y su relevancia en la economía",
        "epigrafes": [
          "Leyes de conservación y su analogía en sistemas económicos con ejemplos de conservación de recursos en empresas",
          "Termodinámica y entropía en procesos económicos aplicados a la eficiencia energética en negocios",
          "Dinámica de sistemas complejos en economía y física con estudios de caso sobre mercados interconectados"
        ]
      },
      {
        "nombre": "Modelos físicos aplicados a sistemas económicos",
        "epigrafes": [
          "Teoría del caos y su aplicación en mercados financieros con análisis de volatilidad en bolsa",
          "Mecánica cuántica y teoría de decisiones económicas aplicadas a la toma de decisiones estratégicas en empresas",
          "Redes complejas en la estructura económica global con ejemplos de interacciones entre corporaciones multinacionales"
        ]
      }
    ]
  }
}
"""

regenerate_topic_schema_few_shot_input = """
### Ejemplo de uso

## Ejemplo de Entrada en JSON:

{
  "contexto_asignatura": {
    "nombre_asignatura": "Física en la economía",
    "tema": {
            "nombre": "Principios físicos fundamentales y su relevancia en la economía",
            "epigrafes": [
              "Leyes de conservación y su analogía en sistemas económicos",
              "Termodinámica y entropía en procesos económicos",
              "Dinámica de sistemas complejos en economía y física"
            ]
          }
    "bloques_tematicos": [
      {
        "nombre": "Fundamentos de la Física en el Contexto Empresarial",
        "temas": [
          {
            "nombre": "Principios físicos fundamentales y su relevancia en la economía",
            "epigrafes": [
              "Leyes de conservación y su analogía en sistemas económicos",
              "Termodinámica y entropía en procesos económicos",
              "Dinámica de sistemas complejos en economía y física"
            ]
          },
          {
            "nombre": "Modelos físicos aplicados a sistemas económicos",
            "epigrafes": [
              "Teoría del caos y su aplicación en mercados financieros",
              "Mecánica cuántica y teoría de decisiones económicas",
              "Redes complejas en la estructura económica global"
            ]
          }
        ]
      },
      {
        "nombre": "Aplicaciones Económicas de los Principios Físicos",
        "temas": [
          {
            "nombre": "Física estadística y su aplicación en economía y finanzas",
            "epigrafes": [
              "Distribuciones de probabilidad en fenómenos económicos",
              "Teoría de juegos y equilibrio en sistemas económicos",
              "Modelos econofísicos para la predicción de crisis financieras"
            ]
          },
          {
            "nombre": "Innovación tecnológica y su impacto económico desde una perspectiva física",
            "epigrafes": [
              "Nanotecnología y nuevos paradigmas de producción",
              "Energías renovables y su impacto en la economía global",
              "Inteligencia artificial y computación cuántica en finanzas"
            ]
          }
        ]
      }
    ]
  },
  "competencias": [
    {
      "descripcion": "Aplicar principios físicos fundamentales, como las leyes de conservación y la termodinámica, para analizar y optimizar sistemas económicos."
    },
    {
      "descripcion": "Utilizar modelos físicos, como la teoría del caos y las redes complejas, para comprender y predecir el comportamiento de los mercados financieros."
    },
    {
      "descripcion": "Interpretar fenómenos económicos a través de la física estadística, utilizando distribuciones de probabilidad y modelos econofísicos para la predicción de crisis financieras."
    },
    {
      "descripcion": "Desarrollar habilidades para aplicar la teoría de juegos y el equilibrio en la toma de decisiones económicas."
    },
    {
      "descripcion": "Evaluar el impacto de la innovación tecnológica, como la nanotecnología y las energías renovables, en la economía global desde una perspectiva física."
    },
    {
      "descripcion": "Comprender el papel de la inteligencia artificial y la computación cuántica en la optimización de procesos financieros y económicos."
    },
    {
      "descripcion": "Integrar conocimientos de física y economía para abordar problemas complejos en el ámbito empresarial, mejorando la toma de decisiones estratégicas."
    }
  ],
  "comentario": "Quiero enfatizar las aplicaciones y casos de uso práctico en el mundo real, aplicando los teoremos a soluciones y utilidades auténticas."
}
"""

regenerate_topic_schema_few_shot_output = """
## Ejemplo de Salida en JSON:
{
  "reasoning": "El usuario ha solicitado modificar el tema 'Principios físicos fundamentales y su relevancia en la economía' de la asignatura 'Física en la economía' para enfatizar las aplicaciones y casos de uso práctico en el mundo real, aplicando los teoremas a soluciones y utilidades auténticas. Para abordar esta solicitud de manera integral, he seguido un proceso detallado que incluye análisis, planificación, ejecución y reflexión.

**Análisis Inicial:**

1. **Comprensión del Contexto:**
   - **Nombre de la Asignatura:** 'Física en la economía' indica una intersección entre conceptos físicos y su aplicación en contextos económicos y empresariales.
   - **Competencias Clave:** Las competencias destacan la aplicación de principios físicos para analizar y optimizar sistemas económicos, utilizar modelos físicos para predecir comportamientos de mercados, interpretar fenómenos económicos mediante física estadística, entre otras habilidades interdisciplinarias.
   - **Tema Actual:** El tema 'Principios físicos fundamentales y su relevancia en la economía' aborda conceptos físicos básicos y su relación con la economía, pero carece de ejemplos prácticos que demuestren su aplicación en el mundo real.

2. **Revisión del Comentario del Usuario:**
   - El usuario desea que el tema enfatice las aplicaciones prácticas y casos de uso en el mundo real, vinculando los teoremas físicos a soluciones y utilidades auténticas en contextos económicos.
   - La petición del usuario no se beneficiaría de una modificación de la estructura, por lo que mantendré la original, enfocándome en enriquecer el contenido existente con ejemplos prácticos.

**Planificación de las Modificaciones:**

1. **Identificación de Oportunidades:**
   - Revisar cada epígrafe para identificar dónde se pueden integrar ejemplos prácticos o casos de estudio específicos.
   - Asegurar que cada epígrafe no solo presente el concepto teórico, sino también su aplicación en escenarios reales empresariales.

2. **Desarrollo de Contenido Práctico:**
   - Incorporar descripciones que incluyan aplicaciones concretas de los principios físicos en contextos económicos.
   - Añadir referencias a estudios de caso actuales o históricos que ilustren la implementación de estos conceptos en el mundo empresarial.

**Ejecución de las Modificaciones:**

1. **Modificar Epígrafes Existentes:**
   - Cada epígrafe ha sido ajustado para incluir una mención específica de aplicaciones prácticas. Por ejemplo:
     - \"Leyes de conservación y su analogía en sistemas económicos con ejemplos de conservación de recursos en empresas\"
     - \"Termodinámica y entropía en procesos económicos aplicados a la eficiencia energética en negocios\"
     - \"Dinámica de sistemas complejos en economía y física con estudios de caso sobre mercados interconectados\"

2. **Justificación de las Modificaciones:**
   - Estas modificaciones responden directamente al deseo del usuario de ver aplicaciones reales de los teoremas físicos. Al incluir ejemplos específicos y casos de estudio, los estudiantes podrán visualizar mejor cómo los conceptos abstractos se traducen en soluciones tangibles en el ámbito empresarial.

**Reflexión y Autoevaluación:**

Al implementar estas modificaciones, he considerado la importancia de equilibrar la teoría con la práctica. Es fundamental que los estudiantes no solo comprendan los principios físicos, sino que también sean capaces de aplicarlos en contextos económicos reales. Al enriquecer los epígrafes con ejemplos prácticos y estudios de caso, se facilita una mejor comprensión y retención de los conceptos, además de fomentar habilidades críticas para la resolución de problemas en entornos empresariales.

Además, me he asegurado de que las modificaciones estén alineadas con las competencias clave de la asignatura, garantizando que cada cambio contribuya al desarrollo de las habilidades y conocimientos que se pretenden fomentar. Este enfoque reflexivo asegura que las adaptaciones no solo respondan al comentario del usuario, sino que también fortalezcan la calidad y relevancia del contenido curricular.",
  "tema": {
    "nombre": "Principios físicos fundamentales y su relevancia en la economía",
    "epigrafes": [
      "Leyes de conservación y su analogía en sistemas económicos con ejemplos de conservación de recursos en empresas",
      "Termodinámica y entropía en procesos económicos aplicados a la eficiencia energética en negocios",
      "Dinámica de sistemas complejos en economía y física con estudios de caso sobre mercados interconectados"
    ]
  }
}
"""

regenerate_index_schema_instructions = """
Eres un experto en diseño curricular educativo con amplia experiencia en la creación y adaptación de asignaturas universitarias. Tu tarea es regenerar el índice de una asignatura específica basándote en el comentario de un del usuario y el contexto dado.

## Instrucciones Detalladas

### Análisis Inicial:

1. **Comprensión del Contexto**:
   - **Nombre de la Asignatura**: Analiza el título para identificar el enfoque y los objetivos generales.
   - **Competencias**: Revisa las competencias establecidas para entender las habilidades y conocimientos que se buscan desarrollar.
   - **Índice Actual**: Examina la estructura existente para identificar la organización y los contenidos actuales.

2. **Revisión de Instrucciones del Usuario**:
   - **Requisitos Específicos**: Identifica las solicitudes concretas del usuario sobre modificaciones al índice.
   - **Objetivos de la Modificación**: Determina qué se busca mejorar o cambiar con las nuevas instrucciones.

3. **Contextualización del Contenido**:
   - **Epígrafes y Temas Adyacentes**: Considera cómo las secciones actuales se relacionan entre sí para mantener la continuidad.
   - **Coherencia del Nuevo Índice**: Asegura que las modificaciones propuestas encajen de manera lógica con el contenido previo y posterior.

### Razonamiento Detallado:

Aquí se documenta de manera exhaustiva el proceso de razonamiento seguido para regenerar el índice. Este proceso debe incluir los siguientes pasos:

1. **Análisis de los Comentarios del Usuario**:
   - **Identificación de Puntos Clave**: Identifica todas las observaciones del usuario.
   - **Determinación de Áreas de Cambio**: Especifica qué secciones del índice requieren modificaciones.

2. **Propuesta de Modificaciones**:
   - **Modificar Secciones Existentes**: Explica en detalle las adaptaciones realizadas en las secciones actuales para mejorar su coherencia o profundidad.
   - **Agregar/Eliminar Secciones**: Justifica la eliminación de secciones que ya no son pertinentes o que se han vuelto redundantes, o la agregación de otras relevantes.
   - **Justificar y razonar**: Razona cada cambio y decisión llevada a cabo, explicando el por qué detrás de ella, y por qué decides hacerlo de esa manera específicamente. Sigue las instrucciones del usuario de la manera más lógica e intuitiva.

Este razonamiento debe presentar una narrativa clara y estructurada que explique cada decisión tomada durante la regeneración del índice, garantizando así transparencia y solidez en el diseño curricular.

### Generación del Índice:

1. **Desarrollo del Razonamiento**:
   - Genera tu razonamiento detallado que servirá de referencia para construir el nuevo índice.

2. **Construcción del Nuevo Índice**:
   - Elabora el índice considerando los comentarios del usuario y todo el contexto proporcionado, asegurando que las modificaciones propuestas se integren de manera coherente.

### Verificación de Coherencia:

1. **Completitud y Alineación**:
   - Asegúrate de que el índice generado cubra todos los aspectos necesarios y esté alineado con las competencias de la asignatura.

### Formato de Salida:

1. **Objeto JSON**: La salida debe ser un objeto JSON que contenga:

   - `"reasoning"`: Un string que describe detalladamente el proceso seguido para regenerar el índice, siguiendo la estructura de razonamiento detallado anteriormente.

   - `"asignatura"`: Un objeto compuesto por el nombre de la asignatura, "asignatura", y la estructura de esta, "estructura".

{{few_shot_example}}
"""

regenerate_block_schema_instructions = """
Eres un experto en diseño curricular educativo con amplia experiencia en la creación y adaptación de asignaturas universitarias. Tu tarea es regenerar un bloque y sus temas y epígrafes para el índice de una asignatura específica basándote en el comentario de un del usuario y el contexto dado.

## Instrucciones Detalladas

### Análisis Inicial:

1. **Comprensión del Contexto**:
   - **Nombre de la Asignatura**: Analiza el título para identificar el enfoque y los objetivos generales.
   - **Competencias**: Revisa las competencias establecidas para entender las habilidades y conocimientos que se buscan desarrollar.
   - **Bloque Actual**: Examina la estructura existente para identificar la organización y los contenidos actuales.

2. **Revisión de Instrucciones del Usuario**:
   - **Requisitos Específicos**: Identifica las solicitudes concretas del usuario sobre modificaciones al bloque.
   - **Objetivos de la Modificación**: Determina qué se busca mejorar o cambiar con las nuevas instrucciones.

3. **Contextualización del Contenido**:
   - **Epígrafes y Temas Adyacentes**: Considera cómo las secciones actuales se relacionan entre sí para mantener la continuidad.
   - **Coherencia del Nuevo bloque**: Asegura que las modificaciones propuestas encajen de manera lógica con el contenido previo y posterior.

### Razonamiento Detallado:

Aquí se documenta de manera exhaustiva el proceso de razonamiento seguido para regenerar el bloque. Este proceso debe incluir los siguientes pasos:

1. **Análisis de los Comentarios del Usuario**:
   - **Identificación de Puntos Clave**: Identifica todas las observaciones del usuario.
   - **Determinación de Áreas de Cambio**: Especifica qué secciones del bloque requieren modificaciones.

2. **Propuesta de Modificaciones**:
   - **Modificar Secciones Existentes**: Explica en detalle las adaptaciones realizadas en las secciones actuales para mejorar su coherencia o profundidad.
   - **Agregar/Eliminar Secciones**: Justifica la eliminación de secciones que ya no son pertinentes o que se han vuelto redundantes, o la agregación de otras relevantes.
   - **Justificar y razonar**: Razona cada cambio y decisión llevada a cabo, explicando el por qué detrás de ella, y por qué decides hacerlo de esa manera específicamente. Sigue las instrucciones del usuario de la manera más lógica e intuitiva.

Este razonamiento debe presentar una narrativa clara y estructurada que explique cada decisión tomada durante la regeneración del bloque, garantizando así transparencia y solidez en el diseño curricular.

### Generación del bloque:

1. **Desarrollo del Razonamiento**:
   - Genera tu razonamiento detallado que servirán de referencia para construir el nuevo bloque.

2. **Construcción del Nuevo Bloque**:
   - Elabora el bloque considerando los comentarios del usuario y todo el contexto proporcionado, asegurando que las modificaciones propuestas se integren de manera coherente.

### Verificación de Coherencia:

1. **Completitud y Alineación**:
   - Asegúrate de que el bloque generado cubra todos los aspectos necesarios y esté alineado con las competencias de la asignatura.

### Formato de Salida:

1. **Objeto JSON**: La salida debe ser un objeto JSON que contenga:

   - `"reasoning"`: Un string que describe detalladamente el proceso seguido para regenerar el bloque, siguiendo la estructura de razonamiento detallado anteriormente.

   - `"bloque_tematico"`: Un objeto que representa la estructura del bloque con sus temas, junto al nombre del bloque.

{{few_shot_example}}
"""

regenerate_topic_schema_instructions = """
Eres un experto en diseño curricular educativo con amplia experiencia en la creación y adaptación de asignaturas universitarias. Tu tarea es regenerar un tema y sus epígrafes para el índice de una asignatura específica basándote en el comentario de un del usuario y el contexto dado.

## Instrucciones Detalladas

### Análisis Inicial:

1. **Comprensión del Contexto**:
   - **Nombre de la Asignatura**: Analiza el título para identificar el enfoque y los objetivos generales.
   - **Competencias**: Revisa las competencias establecidas para entender las habilidades y conocimientos que se buscan desarrollar.
   - **Tema Actual**: Examina la estructura existente para identificar la organización y los contenidos actuales.

2. **Revisión de Instrucciones del Usuario**:
   - **Requisitos Específicos**: Identifica las solicitudes concretas del usuario sobre modificaciones al tema.
   - **Objetivos de la Modificación**: Determina qué se busca mejorar o cambiar con las nuevas instrucciones.

3. **Contextualización del Contenido**:
   - **Epígrafes y Temas Adyacentes**: Considera cómo las secciones actuales se relacionan entre sí para mantener la continuidad.
   - **Coherencia del Nuevo tema**: Asegura que las modificaciones propuestas encajen de manera lógica con el contenido previo y posterior.

### Razonamiento Detallado:

Aquí se documenta de manera exhaustiva el proceso de razonamiento seguido para regenerar el tema. Este proceso debe incluir los siguientes pasos:

1. **Análisis de los Comentarios del Usuario**:
   - **Identificación de Puntos Clave**: Identifica todas las observaciones del usuario.
   - **Determinación de Áreas de Cambio**: Especifica qué secciones del tema requieren modificaciones.

2. **Propuesta de Modificaciones**:
   - **Modificar Secciones Existentes**: Explica en detalle las adaptaciones realizadas en las secciones actuales para mejorar su coherencia o profundidad.
   - **Agregar/Eliminar Secciones**: Justifica la eliminación de secciones que ya no son pertinentes o que se han vuelto redundantes, o la agregación de otras relevantes.
   - **Justificar y razonar**: Razona cada cambio y decisión llevada a cabo, explicando el por qué detrás de ella, y por qué decides hacerlo de esa manera específicamente. Sigue las instrucciones del usuario de la manera más lógica e intuitiva.

Este razonamiento debe presentar una narrativa clara y estructurada que explique cada decisión tomada durante la regeneración del tema, garantizando así transparencia y solidez en el diseño curricular.

### Generación del Tema:

1. **Desarrollo del Razonamiento**:
   - Genera tu razonamiento detallado que servirán de referencia para construir el nuevo tema.

2. **Construcción del Nuevo Tema**:
   - Elabora el tema considerando los comentarios del usuario y todo el contexto proporcionado, asegurando que las modificaciones propuestas se integren de manera coherente.

### Verificación de Coherencia:

1. **Completitud y Alineación**:
   - Asegúrate de que el tema generado cubra todos los aspectos necesarios y esté alineado con las competencias de la asignatura.

### Formato de Salida:

1. **Objeto JSON**: La salida debe ser un objeto JSON que contenga:

   - `"reasoning"`: Un string que describe detalladamente el proceso seguido para regenerar el tema, conforme a la sección de Reasoning.

   - `"tema"`: Un objeto que representa el tema con su nombre y lista de epígrafes correspondientes.

{{few_shot_example}}
"""

regenerate_index_from_instructions = [
    ("system", regenerate_index_schema_instructions),
    ("human", regenerate_index_schema_user),
]
regenerate_index_block_from_instructions = [
    ("system", regenerate_block_schema_instructions),
    ("human", regenerate_block_schema_user),
]
regenerate_index_topic_from_instructions = [
    ("system", regenerate_topic_schema_instructions),
    ("human", regenerate_topic_schema_user),
]

user_regenerate_didactic_instructions_topic = """
Un usuario necesita modificar unas instrucciones didácticas para una asignatura universitaria.

<contexto_asignatura>
A continuación, se proporciona información detallada sobre la asignatura para asegurar que las modificaciones se alineen con sus objetivos y estructura.
Esta información incluye los bloques temáticos, los temas específicos dentro de cada bloque y los epígrafes que detallan cada tema. Es esencial considerar esta estructura para mantener la coherencia y relevancia de las instrucciones didácticas:

Nombre: {{nombre_asignatura}}
Esquema de la asignatura: {{esquema_asignatura}}.
</contexto_asignatura>

<instrucciones_didacticas>
Las instrucciones didacticas actuales que el usuario quiere modificar son: {{instrucciones_didacticas}}.
Dichas instrucciones se generaron a través de los siguientes requisitos: {{requisitos_instrucciones_didacticas}}.
</instrucciones_didacticas>

<comentario>
El usuario ha realizado este comentario sobre los cambios que le gustaría realizar con respecto a las mismas:
{{comentario}}
</comentario>
Devuelve en todos los casos una respuesta válida, con tantas instrucciones como se te hayan dado incialmente, ni más, ni menos, aunque el usuario diga la contrario. Si el comentario del usuario no permite sacar conclusiones al respecto, regenera las conclusiones con un educated guess de cómo podrían mejorar las instrucciones, apoyándote, si se puede, en el comentario.
Realiza los cambios que veas necesarios y sigue el proceso mencionado en las instrucciones, ciñéndote al formato de salida esperado y a las demás reglas:
"""

user_regenerate_didactic_instructions_epigraph = """
Un usuario necesita modificar una instrucción didáctica específica para una asignatura universitaria.

<contexto_asignatura>
A continuación, se proporciona información detallada sobre la asignatura para asegurar que las modificaciones se alineen con sus objetivos y estructura.
Esta información incluye los bloques temáticos, los temas específicos dentro de cada bloque y los epígrafes que detallan cada tema. Es esencial considerar esta estructura para mantener la coherencia y relevancia de las instrucciones didácticas:

Nombre: {{nombre_asignatura}}
Esquema de la asignatura: {{esquema_asignatura}}.
</contexto_asignatura>

<instrucciones_didacticas>
Las instrucciones que el usuario quiere modificar son las siguientes: {{instrucciones_content_plan}}.
Esas instrucciones son de un epígrafe de un tema concreto. Las instrucciones didacticas de todo el tema son: {{instrucciones_didacticas}}.
Dichas instrucciones se generaron a través de los siguientes requisitos: {{requisitos_instrucciones_didacticas}}.
</instrucciones_didacticas>

<comentario>
El usuario ha realizado este comentario sobre los cambios que le gustaría realizar con respecto a las mismas:
{{comentario}}
</comentario>

Devuelve en todos los casos una respuesta válida, con una única instrucción didáctica, ni más, ni menos, aunque el usuario diga la contrario. Si el comentario del usuario no permite sacar conclusiones al respecto, regenera las conclusiones con un educated guess de cómo podrían mejorar las instrucciones, apoyándote, si se puede, en el comentario.
Realiza los cambios que veas necesarios y sigue el proceso mencionado en las instrucciones, ciñéndote al formato de salida esperado y a las demás reglas:
"""

regenerate_topic_instructions_few_shot_input = """
### Ejemplo de uso

## Ejemplo de Entrada en JSON:

{
  "contexto_asignatura": {
    "nombre_asignatura": "Álgebra avanzada",
    "bloques_tematicos": [
      {
        "nombre": "Estructuras Algebraicas Avanzadas",
        "temas": [
          {
            "nombre": "Teoría de Grupos Avanzada",
            "epigrafes": [
              "Grupos de Lie y álgebras de Lie: clasificación y estructura",
              "Teoría de representaciones: caracteres y módulos irreducibles",
              "Teoremas de Sylow y aplicaciones en grupos finitos",
              "Grupos infinitos y métodos geométricos en teoría de grupos"
            ]
          },
          {
            "nombre": "Teoría de Anillos y Campos",
            "epigrafes": [
              "Teoría avanzada de Galois y grupos resolubles",
              "Extensiones trascendentes y teoría de valoración algebraica",
              "Anillos no conmutativos y álgebras de división de dimensión finita",
              "Teoría de módulos: estructura y descomposición"
            ]
          }
        ]
      },
      {
        "nombre": "Teorías Algebraicas Modernas",
        "temas": [
          {
            "nombre": "Álgebra Homológica y Categorías",
            "epigrafes": [
              "Fundamentos de teoría de categorías y límites",
              "Funtores derivados y secuencias espectrales",
              "Homología y cohomología en estructuras algebraicas",
              "Aplicaciones en geometría algebraica moderna"
            ]
          }
        ]
      }
    ]
  },
  "instrucciones_didacticas": [
    {
      "position": 1,
      "name": "Grupos de Lie y álgebras de Lie: clasificación y estructura",
      "didactic_instruction": "### Introducción\n- Definir los conceptos de grupos de Lie y álgebras de Lie.\n- Explicar la importancia de estos conceptos en matemáticas y física.\n\n### Teoría\n- Describir la relación entre grupos de Lie y álgebras de Lie.\n- Presentar la clasificación de grupos de Lie simples y semisimples.\n- Explicar la estructura de las álgebras de Lie, incluyendo raíces y pesos.\n\n### Ejemplos prácticos\n- Analizar el grupo SO(3) y su álgebra de Lie correspondiente.\n- Describir la estructura del grupo SU(2) y su relación con las rotaciones en 3D.\n\n### Ejercicio\nProponer el siguiente ejercicio: Dado el grupo de Lie SU(2), realice las siguientes tareas:\n1. Identifique los generadores de su álgebra de Lie.\n2. Calcule los corchetes de Lie entre estos generadores.\n3. Demuestre que estos generadores satisfacen las relaciones de conmutación del álgebra su(2).\n\nNota: No se utilizarán gráficos en esta sección ni en las siguientes. Se emplearán únicamente textos y tablas para ilustrar el contenido."
    },
    {
      "position": 2,
      "name": "Teoría de representaciones: caracteres y módulos irreducibles",
      "didactic_instruction": "### Teoría\n- Definir el concepto de representación de un grupo.\n- Explicar la importancia de las representaciones irreducibles.\n- Introducir la noción de carácter de una representación.\n\n### Conceptos clave\n- Describir el teorema de Maschke y sus implicaciones.\n- Explicar el lema de Schur y su relevancia en la teoría de representaciones.\n- Presentar el teorema de Peter-Weyl para grupos compactos.\n\n### Módulos irreducibles\n- Definir el concepto de módulo sobre un álgebra de grupo.\n- Explicar la relación entre módulos irreducibles y representaciones irreducibles.\n- Describir el teorema de Wedderburn-Artin y su aplicación a la estructura de álgebras de grupo.\n\n### Tabla ilustrativa\nIncluir una tabla que muestre los caracteres de las representaciones irreducibles del grupo simétrico S3, incluyendo:\n- Clase de conjugación\n- Tamaño de la clase\n- Valores de los caracteres para cada representación irreducible\n\nNota: No se utilizarán gráficos en esta sección. Se emplearán únicamente textos y tablas para ilustrar el contenido."
    },
    {
      "position": 3,
      "name": "Teoremas de Sylow y aplicaciones en grupos finitos",
      "didactic_instruction": "### Teoría\n- Enunciar y demostrar los tres teoremas de Sylow.\n- Explicar la importancia de los p-subgrupos de Sylow en la estructura de grupos finitos.\n\n### Aplicaciones\n- Describir cómo los teoremas de Sylow se utilizan para clasificar grupos de orden bajo.\n- Explicar el uso de los teoremas de Sylow en la demostración de la simplicidad de grupos.\n\n### Conceptos avanzados\n- Introducir el concepto de fusión de subgrupos y su relación con los teoremas de Sylow.\n- Describir el teorema de Burnside pª qª y su importancia en la teoría de grupos finitos.\n\n### Tabla ilustrativa\nIncluir una tabla que muestre los p-subgrupos de Sylow para grupos de diferentes órdenes, incluyendo:\n- Orden del grupo\n- Primos p que dividen el orden\n- Número de p-subgrupos de Sylow\n- Orden de los p-subgrupos de Sylow\n\nNota: No se utilizarán gráficos en esta sección. Se emplearán únicamente textos y tablas para ilustrar el contenido."
    },
    {
      "position": 4,
      "name": "Grupos infinitos y métodos geométricos en teoría de grupos",
      "didactic_instruction": "### Teoría\n- Introducir el concepto de grupo infinito y sus diferencias con los grupos finitos.\n- Explicar la importancia de los métodos geométricos en el estudio de grupos infinitos.\n\n### Conceptos clave\n- Describir los grupos libres y su estructura.\n- Explicar el concepto de presentación de un grupo y su relación con los grupos libres.\n- Introducir la noción de grupo fundamental en topología algebraica.\n\n### Métodos geométricos\n- Describir el concepto de grafo de Cayley y su uso en la visualización de grupos.\n- Explicar la teoría de espacios recubridores y su relación con los subgrupos normales.\n- Introducir el concepto de curvatura en grupos y su relación con las propiedades algebraicas.\n\n### Ejercicio\nProponer el siguiente ejercicio: Considere el grupo libre F2 generado por dos elementos a y b.\n1. Dibuje una porción del grafo de Cayley de F2.\n2. Identifique un subgrupo H de índice 2 en F2.\n3. Describa el espacio recubridor correspondiente al subgrupo H.\n4. Demuestre que H es isomorfo a un grupo libre de rango 3.\n\nNota: Aunque el ejercicio menciona \"dibujar\", no se incluirán gráficos en la solución. Se describirá verbalmente la estructura del grafo de Cayley y del espacio recubridor. No se utilizarán gráficos en esta sección ni en las anteriores. Se emplearán únicamente textos y tablas para ilustrar el contenido."
    }
  ],
    "requisitos_instrucciones_didacticas": "Quiero que empiece con una introduccion, seguido de teoria y finalmente que acabe con ejemplos practicos y un ejercicio(incluye uno de ellos en el primer epígrafe y otro en el ultimo, los otros dos serán más teoricos). Los elementos dinamizadores que se podran incluir en la asignatura son textos y tablas para ilustrar el contenido. No uses gráficos y menciona explicitamente que no se han de utilizar"
  },
  "comentario": "Quiero enfatizar las aplicaciones y casos de uso práctico en el mundo real, aplicando los teoremos a soluciones y utilidades auténticas."
}
"""
regenerate_topic_instructions_few_shot_output = """
## Ejemplo de Salida en JSON
{
  "reasoning": "Para regenerar las instrucciones didácticas, se analizó el comentario del usuario que solicita enfatizar las aplicaciones y casos de uso práctico en el mundo real, aplicando los teoremas a soluciones y utilidades auténticas. Se revisaron las instrucciones existentes de cada epígrafe, modificando el contenido teórico para incluir aplicaciones prácticas relevantes y ajustando los ejercicios para reflejar escenarios del mundo real. Se mantuvo la coherencia con los objetivos generales de la asignatura y se aseguró que las instrucciones cumplan con los requisitos específicos proporcionados, incluyendo el uso exclusivo de textos y tablas para ilustrar el contenido sin la utilización de gráficos.",
  "output": [
    {
      "instruction": "### Introducción\n- Definir los conceptos de grupos de Lie y álgebras de Lie.\n- Explicar la importancia de estos conceptos en matemáticas y física, destacando sus aplicaciones en la teoría de partículas y la mecánica cuántica.\n\n### Teoría\n- Describir la relación entre grupos de Lie y álgebras de Lie.\n- Presentar la clasificación de grupos de Lie simples y semisimples.\n- Explicar la estructura de las álgebras de Lie, incluyendo raíces y pesos.\n\n### Ejemplos prácticos\n- Analizar el grupo SO(3) y su álgebra de Lie correspondiente, aplicándolo en la descripción de rotaciones en sistemas físicos.\n- Describir la estructura del grupo SU(2) y su relación con las rotaciones en 3D, destacando su uso en la teoría de espines en física.\n\n### Ejercicio\nProponer el siguiente ejercicio: **Aplicación de Grupos de Lie en Física Cuántica**\n1. Identifique los generadores de su álgebra de Lie para el grupo SU(2).\n2. Calcule los corchetes de Lie entre estos generadores.\n3. Demuestre que estos generadores satisfacen las relaciones de conmutación del álgebra su(2).\n4. Explique cómo estos generadores se relacionan con los operadores de espín en mecánica cuántica.\n\n**Nota:** No se utilizarán gráficos en esta sección ni en las siguientes. Se emplearán únicamente textos y tablas para ilustrar el contenido."
    },
    {
      "instruction": "### Teoría\n- Definir el concepto de representación de un grupo.\n- Explicar la importancia de las representaciones irreducibles en la simplificación de problemas complejos.\n- Introducir la noción de carácter de una representación y su utilidad en la clasificación de representaciones.\n\n### Conceptos clave\n- Describir el teorema de Maschke y sus implicaciones en la descomposición de representaciones.\n- Explicar el lema de Schur y su relevancia en la teoría de representaciones, incluyendo aplicaciones en la física de partículas.\n- Presentar el teorema de Peter-Weyl para grupos compactos y su importancia en el análisis armónico.\n\n### Módulos irreducibles\n- Definir el concepto de módulo sobre un álgebra de grupo.\n- Explicar la relación entre módulos irreducibles y representaciones irreducibles.\n- Describir el teorema de Wedderburn-Artin y su aplicación a la estructura de álgebras de grupo.\n\n### Tabla ilustrativa\nIncluir una tabla que muestre los caracteres de las representaciones irreducibles del grupo simétrico S3, incluyendo:\n- Clase de conjugación\n- Tamaño de la clase\n- Valores de los caracteres para cada representación irreducible\n\n**Nota:** No se utilizarán gráficos en esta sección. Se emplearán únicamente textos y tablas para ilustrar el contenido."
    },
    {
      "instruction": "### Teoría\n- Enunciar y demostrar los tres teoremas de Sylow.\n- Explicar la importancia de los p-subgrupos de Sylow en la estructura de grupos finitos y su aplicación en la clasificación de grupos.\n\n### Aplicaciones\n- Describir cómo los teoremas de Sylow se utilizan para clasificar grupos de orden bajo.\n- Explicar el uso de los teoremas de Sylow en la demostración de la simplicidad de grupos, con ejemplos de aplicaciones en álgebra y teoría de números.\n\n### Conceptos avanzados\n- Introducir el concepto de fusión de subgrupos y su relación con los teoremas de Sylow.\n- Describir el teorema de Burnside pª qª y su importancia en la teoría de grupos finitos.\n\n### Tabla ilustrativa\nIncluir una tabla que muestre los p-subgrupos de Sylow para grupos de diferentes órdenes, incluyendo:\n- Orden del grupo\n- Primos p que dividen el orden\n- Número de p-subgrupos de Sylow\n- Orden de los p-subgrupos de Sylow\n\n**Nota:** No se utilizarán gráficos en esta sección. Se emplearán únicamente textos y tablas para ilustrar el contenido."
    },
    {
      "instruction": "### Teoría\n- Introducir el concepto de grupo infinito y sus diferencias con los grupos finitos.\n- Explicar la importancia de los métodos geométricos en el estudio de grupos infinitos y sus aplicaciones en geometría algebraica.\n\n### Conceptos clave\n- Describir los grupos libres y su estructura, incluyendo aplicaciones en topología y teoría de nudos.\n- Explicar el concepto de presentación de un grupo y su relación con los grupos libres.\n- Introducir la noción de grupo fundamental en topología algebraica y su uso en la clasificación de espacios topológicos.\n\n### Métodos geométricos\n- Describir el concepto de grafo de Cayley y su uso en la visualización de grupos, aplicándolo en problemas de redes y criptografía.\n- Explicar la teoría de espacios recubridores y su relación con los subgrupos normales, con ejemplos en geometría y física.\n- Introducir el concepto de curvatura en grupos y su relación con las propiedades algebraicas, destacando aplicaciones en la teoría de la relatividad.\n\n### Ejercicio\nProponer el siguiente ejercicio: **Aplicación de Grupos Libres en Topología**\n1. Considere el grupo libre F2 generado por dos elementos a y b.\n2. Identifique un subgrupo H de índice 2 en F2.\n3. Describa el espacio recubridor correspondiente al subgrupo H.\n4. Demuestre que H es isomorfo a un grupo libre de rango 3.\n5. Explique cómo estos conceptos se aplican en la clasificación de superficies en topología.\n\n**Nota:** Aunque el ejercicio menciona \"dibujar\", no se incluirán gráficos en la solución. Se describirá verbalmente la estructura del grafo de Cayley y del espacio recubridor. No se utilizarán gráficos en esta sección ni en las anteriores. Se emplearán únicamente textos y tablas para ilustrar el contenido."
    }
  ]
}

"""

regenerate_epigraph_instructions_few_shot_input = """
### Ejemplo de uso

## Ejemplo de Entrada en JSON:

{
  "contexto_asignatura": {
    "nombre_asignatura": "Álgebra avanzada",
    "bloques_tematicos": [
      {
        "nombre": "Estructuras Algebraicas Avanzadas",
        "temas": [
          {
            "nombre": "Teoría de Grupos Avanzada",
            "epigrafes": [
              "Grupos de Lie y álgebras de Lie: clasificación y estructura",
              "Teoría de representaciones: caracteres y módulos irreducibles",
              "Teoremas de Sylow y aplicaciones en grupos finitos",
              "Grupos infinitos y métodos geométricos en teoría de grupos"
            ]
          },
          {
            "nombre": "Teoría de Anillos y Campos",
            "epigrafes": [
              "Teoría avanzada de Galois y grupos resolubles",
              "Extensiones trascendentes y teoría de valoración algebraica",
              "Anillos no conmutativos y álgebras de división de dimensión finita",
              "Teoría de módulos: estructura y descomposición"
            ]
          }
        ]
      },
      {
        "nombre": "Teorías Algebraicas Modernas",
        "temas": [
          {
            "nombre": "Álgebra Homológica y Categorías",
            "epigrafes": [
              "Fundamentos de teoría de categorías y límites",
              "Funtores derivados y secuencias espectrales",
              "Homología y cohomología en estructuras algebraicas",
              "Aplicaciones en geometría algebraica moderna"
            ]
          }
        ]
      }
    ]
  },
  "instrucciones_content_plan":
    {
      "position": 4,
      "name": "Grupos infinitos y métodos geométricos en teoría de grupos",
      "didactic_instruction": "### Teoría\n- Introducir el concepto de grupo infinito y sus diferencias con los grupos finitos.\n- Explicar la importancia de los métodos geométricos en el estudio de grupos infinitos.\n\n### Conceptos clave\n- Describir los grupos libres y su estructura.\n- Explicar el concepto de presentación de un grupo y su relación con los grupos libres.\n- Introducir la noción de grupo fundamental en topología algebraica.\n\n### Métodos geométricos\n- Describir el concepto de grafo de Cayley y su uso en la visualización de grupos.\n- Explicar la teoría de espacios recubridores y su relación con los subgrupos normales.\n- Introducir el concepto de curvatura en grupos y su relación con las propiedades algebraicas.\n\n### Ejercicio\nProponer el siguiente ejercicio: Considere el grupo libre F2 generado por dos elementos a y b.\n1. Dibuje una porción del grafo de Cayley de F2.\n2. Identifique un subgrupo H de índice 2 en F2.\n3. Describa el espacio recubridor correspondiente al subgrupo H.\n4. Demuestre que H es isomorfo a un grupo libre de rango 3.\n\nNota: Aunque el ejercicio menciona \"dibujar\", no se incluirán gráficos en la solución. Se describirá verbalmente la estructura del grafo de Cayley y del espacio recubridor. No se utilizarán gráficos en esta sección ni en las anteriores. Se emplearán únicamente textos y tablas para ilustrar el contenido."
    },
  "instrucciones_didacticas": [
    {
      "position": 1,
      "name": "Grupos de Lie y álgebras de Lie: clasificación y estructura",
      "didactic_instruction": "### Introducción\n- Definir los conceptos de grupos de Lie y álgebras de Lie.\n- Explicar la importancia de estos conceptos en matemáticas y física.\n\n### Teoría\n- Describir la relación entre grupos de Lie y álgebras de Lie.\n- Presentar la clasificación de grupos de Lie simples y semisimples.\n- Explicar la estructura de las álgebras de Lie, incluyendo raíces y pesos.\n\n### Ejemplos prácticos\n- Analizar el grupo SO(3) y su álgebra de Lie correspondiente.\n- Describir la estructura del grupo SU(2) y su relación con las rotaciones en 3D.\n\n### Ejercicio\nProponer el siguiente ejercicio: Dado el grupo de Lie SU(2), realice las siguientes tareas:\n1. Identifique los generadores de su álgebra de Lie.\n2. Calcule los corchetes de Lie entre estos generadores.\n3. Demuestre que estos generadores satisfacen las relaciones de conmutación del álgebra su(2).\n\nNota: No se utilizarán gráficos en esta sección ni en las siguientes. Se emplearán únicamente textos y tablas para ilustrar el contenido."
    },
    {
      "position": 2,
      "name": "Teoría de representaciones: caracteres y módulos irreducibles",
      "didactic_instruction": "### Teoría\n- Definir el concepto de representación de un grupo.\n- Explicar la importancia de las representaciones irreducibles.\n- Introducir la noción de carácter de una representación.\n\n### Conceptos clave\n- Describir el teorema de Maschke y sus implicaciones.\n- Explicar el lema de Schur y su relevancia en la teoría de representaciones.\n- Presentar el teorema de Peter-Weyl para grupos compactos.\n\n### Módulos irreducibles\n- Definir el concepto de módulo sobre un álgebra de grupo.\n- Explicar la relación entre módulos irreducibles y representaciones irreducibles.\n- Describir el teorema de Wedderburn-Artin y su aplicación a la estructura de álgebras de grupo.\n\n### Tabla ilustrativa\nIncluir una tabla que muestre los caracteres de las representaciones irreducibles del grupo simétrico S3, incluyendo:\n- Clase de conjugación\n- Tamaño de la clase\n- Valores de los caracteres para cada representación irreducible\n\nNota: No se utilizarán gráficos en esta sección. Se emplearán únicamente textos y tablas para ilustrar el contenido."
    },
    {
      "position": 3,
      "name": "Teoremas de Sylow y aplicaciones en grupos finitos",
      "didactic_instruction": "### Teoría\n- Enunciar y demostrar los tres teoremas de Sylow.\n- Explicar la importancia de los p-subgrupos de Sylow en la estructura de grupos finitos.\n\n### Aplicaciones\n- Describir cómo los teoremas de Sylow se utilizan para clasificar grupos de orden bajo.\n- Explicar el uso de los teoremas de Sylow en la demostración de la simplicidad de grupos.\n\n### Conceptos avanzados\n- Introducir el concepto de fusión de subgrupos y su relación con los teoremas de Sylow.\n- Describir el teorema de Burnside pª qª y su importancia en la teoría de grupos finitos.\n\n### Tabla ilustrativa\nIncluir una tabla que muestre los p-subgrupos de Sylow para grupos de diferentes órdenes, incluyendo:\n- Orden del grupo\n- Primos p que dividen el orden\n- Número de p-subgrupos de Sylow\n- Orden de los p-subgrupos de Sylow\n\nNota: No se utilizarán gráficos en esta sección. Se emplearán únicamente textos y tablas para ilustrar el contenido."
    },
    {
      "position": 4,
      "name": "Grupos infinitos y métodos geométricos en teoría de grupos",
      "didactic_instruction": "### Teoría\n- Introducir el concepto de grupo infinito y sus diferencias con los grupos finitos.\n- Explicar la importancia de los métodos geométricos en el estudio de grupos infinitos.\n\n### Conceptos clave\n- Describir los grupos libres y su estructura.\n- Explicar el concepto de presentación de un grupo y su relación con los grupos libres.\n- Introducir la noción de grupo fundamental en topología algebraica.\n\n### Métodos geométricos\n- Describir el concepto de grafo de Cayley y su uso en la visualización de grupos.\n- Explicar la teoría de espacios recubridores y su relación con los subgrupos normales.\n- Introducir el concepto de curvatura en grupos y su relación con las propiedades algebraicas.\n\n### Ejercicio\nProponer el siguiente ejercicio: Considere el grupo libre F2 generado por dos elementos a y b.\n1. Dibuje una porción del grafo de Cayley de F2.\n2. Identifique un subgrupo H de índice 2 en F2.\n3. Describa el espacio recubridor correspondiente al subgrupo H.\n4. Demuestre que H es isomorfo a un grupo libre de rango 3.\n\nNota: Aunque el ejercicio menciona \"dibujar\", no se incluirán gráficos en la solución. Se describirá verbalmente la estructura del grafo de Cayley y del espacio recubridor. No se utilizarán gráficos en esta sección ni en las anteriores. Se emplearán únicamente textos y tablas para ilustrar el contenido."
    }
  ],
    "requisitos_instrucciones_didacticas": "Quiero que empiece con una introduccion, seguido de teoria y finalmente que acabe con ejemplos practicos y un ejercicio(incluye uno de ellos en el primer epígrafe y otro en el ultimo, los otros dos serán más teoricos). Los elementos dinamizadores que se podran incluir en la asignatura son textos y tablas para ilustrar el contenido. No uses gráficos y menciona explicitamente que no se han de utilizar"
  },
  "comentario": "Quiero enfatizar las aplicaciones y casos de uso práctico en el mundo real, aplicando los teoremos a soluciones y utilidades auténticas."
}
"""

regenerate_epigraph_instructions_few_shot_output = """
## Ejemplo de Salida en JSON
{
  "reasoning": "Para regenerar las instrucciones didácticas, se analizó el comentario del usuario que solicita enfatizar las aplicaciones y casos de uso práctico en el mundo real, aplicando los teoremas a soluciones y utilidades auténticas. Se revisaron las instrucciones existentes de cada epígrafe, modificando el contenido teórico para incluir aplicaciones prácticas relevantes y ajustando los ejercicios para reflejar escenarios del mundo real. Se mantuvo la coherencia con los objetivos generales de la asignatura y se aseguró que las instrucciones cumplan con los requisitos específicos proporcionados, incluyendo el uso exclusivo de textos y tablas para ilustrar el contenido sin la utilización de gráficos.",
  "output": [
    {
      "instruction": "### Teoría\n- Introducir el concepto de grupo infinito y sus diferencias con los grupos finitos.\n- Explicar la importancia de los métodos geométricos en el estudio de grupos infinitos y sus aplicaciones en geometría algebraica.\n\n### Conceptos clave\n- Describir los grupos libres y su estructura, incluyendo aplicaciones en topología y teoría de nudos.\n- Explicar el concepto de presentación de un grupo y su relación con los grupos libres.\n- Introducir la noción de grupo fundamental en topología algebraica y su uso en la clasificación de espacios topológicos.\n\n### Métodos geométricos\n- Describir el concepto de grafo de Cayley y su uso en la visualización de grupos, aplicándolo en problemas de redes y criptografía.\n- Explicar la teoría de espacios recubridores y su relación con los subgrupos normales, con ejemplos en geometría y física.\n- Introducir el concepto de curvatura en grupos y su relación con las propiedades algebraicas, destacando aplicaciones en la teoría de la relatividad.\n\n### Ejercicio\nProponer el siguiente ejercicio: **Aplicación de Grupos Libres en Topología**\n1. Considere el grupo libre F2 generado por dos elementos a y b.\n2. Identifique un subgrupo H de índice 2 en F2.\n3. Describa el espacio recubridor correspondiente al subgrupo H.\n4. Demuestre que H es isomorfo a un grupo libre de rango 3.\n5. Explique cómo estos conceptos se aplican en la clasificación de superficies en topología.\n\n**Nota:** Aunque el ejercicio menciona \"dibujar\", no se incluirán gráficos en la solución. Se describirá verbalmente la estructura del grafo de Cayley y del espacio recubridor. No se utilizarán gráficos en esta sección ni en las anteriores. Se emplearán únicamente textos y tablas para ilustrar el contenido."
    }
  ]
}
"""
