# content_regenerator.py
from logging import Logger
from typing import Any, Callable, Optional, Type, TypeVar

from ia_gen_core.prompts import PromptManager
from sqlalchemy import Engine

from src.api.common.services.ai_tracer import AITracer
from src.api.common.services.structs import (
    ProcessMetadata,
)
from src.api.common.utils.check_prompt import (
    check_template_chat,
)
from src.domain.models import (
    AIProcess,
    AIProcessStatus,
    AIProcessType,
)

LLMOutputType = TypeVar("LLMOutputType")
ProcessedOutputType = TypeVar("ProcessedOutputType")


class ContentRegenerator:
    def __init__(
        self,
        llm: Any,
        llm_tracer: AITracer,
        db_engine: Engine,
        logger: Logger,
        prompt_manager: PromptManager,
    ):
        self._llm = llm
        self._llm_tracer = llm_tracer
        self._db_engine = db_engine
        self._logger = logger
        self._prompt_manager = prompt_manager

    async def _preparate_chain(self, prompt_name: str, input_data: Any):
        prompt = self._prompt_manager.get_prompt(
            name=prompt_name,
            prompt_type="chat",
        )

        template, _ = check_template_chat(prompt, input_data)

        if prompt.few_shot_examples:
            few_shot = (
                "Input:\n\n"
                + prompt.few_shot_examples[0].input
                + "\n\nOutput:"
                + prompt.few_shot_examples[0].output
            )
            input_data.few_shot_example = few_shot

        chain = template | self._llm

        # Dynamically derive flow and chain from the prompt_name
        flow_chain_base = " ".join(
            word.capitalize()
            for word in prompt_name.replace("regenerate-", "")
            .replace("-instructions", "")
            .split("-")
        )
        return prompt, chain, flow_chain_base

    async def _regenerate(
        self,
        input_data: Any,
        prompt_name: str,
        expected_llm_output: Type[LLMOutputType],
        process_type: AIProcessType,
        convert_result: Optional[Callable[[LLMOutputType], ProcessedOutputType]] = None,
        process_metadata: Optional[ProcessMetadata] = None,
        indice_id: int | None = None,
        tema_id: int | None = None,
        epigrafe_id: int | None = None,
    ) -> tuple[Optional[ProcessedOutputType], AIProcess]:
        self._llm = self._llm.with_structured_output(expected_llm_output)
        try:
            prompt, chain, flow_chain_base = await self._preparate_chain(
                prompt_name, input_data
            )

            metadata = {
                "flow": f"Regenerate {flow_chain_base}",
                "chain": f"Regenerate {flow_chain_base} Chain",
                "prompt_context": input_data.prompt_context.model_dump()
                if hasattr(input_data, "prompt_context") and input_data.prompt_context
                else None,
                "prompt_id": prompt.id,
                "prompt_name": prompt.name,
                "prompt_version": prompt.version,
            }

            invoke_input = {**input_data.model_dump()}

            result = await chain.with_config(metadata=metadata).ainvoke(invoke_input)
            self._logger.info(
                f"{prompt_name.replace('regenerate-', '').replace('-', ' ').title()} result: {result}"
            )

            final_result = convert_result(result) if convert_result else result

            process = self._llm_tracer.trace_process(
                process_type=process_type,
                input_data=input_data.model_dump(),
                output_data=result.model_dump(),
                execution_metadata=metadata,
                process_metadata=process_metadata,
                prompt_id=prompt.id,
                indice_id=indice_id,
                tema_id=tema_id,
                epigrafe_id=epigrafe_id,
            )

            return final_result, process

        except Exception as e:
            self._logger.error(f"Error in {prompt_name}: {e}", exc_info=True)
            process = self._llm_tracer.trace_process(
                process_type=process_type,
                execution_status=AIProcessStatus.FAILED,
                process_status=AIProcessStatus.FAILED,
                input_data=input_data.model_dump(exclude={"prompt_context"})
                if hasattr(input_data, "prompt_context") and input_data.prompt_context
                else input_data.model_dump(),
                output_data=result.model_dump() if "result" in locals() else {},
                execution_metadata=metadata if "metadata" in locals() else {},
                process_metadata=process_metadata
                if "process_metadata" in locals()
                else ProcessMetadata(),
                prompt_id=prompt.id if "prompt" in locals() else None,
                indice_id=indice_id,
                tema_id=tema_id,
                epigrafe_id=epigrafe_id,
                error_message=str(e),
            )
            return None, process
