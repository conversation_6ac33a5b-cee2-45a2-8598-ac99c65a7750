from pydantic import BaseModel

from src.api.common.services.structs import (
    BloqueTematico,
    Competencias,
    Estructura,
    PromptContext,
    Tema,
)


class RegenerateCompetenciesInput(BaseModel):
    nombre_asignatura: str
    esquema_asignatura: Estructura
    competencias: Competencias
    requisitos_competencias: str
    comentario: str
    few_shot_example: str | None = None
    prompt_context: PromptContext | None = None


class RegenerateIndexInput(BaseModel):
    nombre_asignatura: str
    bloque: BloqueTematico | None = None
    tema: Tema | None = None
    esquema_asignatura: Estructura
    competencias: Competencias
    comentario: str
    few_shot_example: str | None = None
    prompt_context: PromptContext | None = None


class ContentPlanInstruction(BaseModel):
    epigraph_position: int
    epigraph_name: str
    didactic_instruction: str


class RegenerateDidacticInstructionsInputTopic(BaseModel):
    nombre_asignatura: str
    esquema_asignatura: Estructura
    instrucciones_didacticas: list[ContentPlanInstruction]
    requisitos_instrucciones_didacticas: str
    comentario: str
    few_shot_example: str | None = None
    prompt_context: PromptContext | None = None


class RegenerateDidacticInstructionsInputEpigraph(BaseModel):
    nombre_asignatura: str
    esquema_asignatura: Estructura
    instrucciones_content_plan: ContentPlanInstruction
    instrucciones_didacticas: list[ContentPlanInstruction]
    requisitos_instrucciones_didacticas: str
    comentario: str
    few_shot_example: str | None = None
    prompt_context: PromptContext | None = None
