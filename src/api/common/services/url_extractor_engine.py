import asyncio
import ssl
from abc import ABC, abstractmethod
from logging import Logger
from typing import List, Literal, Optional

import certifi
import httpx
from pydantic import BaseModel, Field
from tavily import AsyncTavilyClient


class ExtractionSuccess(BaseModel):
    status: Literal["success"] = Field(default="success")
    url: str
    content: Optional[str] = None
    images: Optional[List[str]] = None


class ExtractionError(BaseModel):
    status: Literal["error"] = Field(default="error")
    url: str
    error: str


# Ahora definimos la unión de ambas respuestas
ExtractionResult = ExtractionSuccess | ExtractionError


class UrlExtractor(ABC):
    def __init__(
        self,
        logger: Logger,
    ):
        self._logger = logger

    @abstractmethod
    async def extract(self, urls: list[str]) -> list[ExtractionResult]:
        """
        Extracts content from the given URLs.

        Args:
            urls (list[str]): list of URLs to extract content from.

        Returns:
            list[ExtractionResult]: list of extraction results for each URL.
        """
        pass


class TavilyExtractor(UrlExtractor):
    def __init__(self, logger: Logger, api_key: str, timeout: int = 120) -> None: # a timeout of 120 is set so that during agent execution, it cannot spend too much time on a single source
        super().__init__(logger)
        self.client = AsyncTavilyClient(api_key=api_key)
        self.ssl_context = ssl.create_default_context(cafile=certifi.where())
        self.client._client_creator = lambda: httpx.AsyncClient(
            headers={
                "Content-Type": "application/json",
                "Authorization": f"Bearer {api_key}",
            },
            base_url="https://api.tavily.com",
            timeout=timeout,
            verify=self.ssl_context,
        )

    async def extract(
        self,
        urls: list[str],
    ) -> list[ExtractionResult]:
        output = []
        response = await self.client.extract(
            urls=urls, include_images=True, extract_depth="advanced"
        )
        for result in response["results"]:
            output.append(
                ExtractionSuccess(
                    url=result["url"],
                    content=result["raw_content"],
                    images=result["images"],
                )
            )
        for i in response["failed_results"]:
            output.append(
                ExtractionError(
                    url=i["url"],
                    error=i["error"],
                )
            )
        return output


class JinaExtractor(UrlExtractor):
    """URL content extractor that uses Jina API directly with httpx for extracting website content."""

    def __init__(self, logger: Logger, api_key: str, timeout: int = 120) -> None: # a timeout of 120 is set so that during agent execution, it cannot spend too much time on a single source
        """
        Initialize the JinaExtractor with httpx client.

        Args:
            logger: Logger instance
            api_key: Jina API key
        """
        super().__init__(logger)
        self.url = "https://r.jina.ai/"
        self.timeout = httpx.Timeout(timeout, connect=10.0)
        self.ssl_context = ssl.create_default_context(cafile=certifi.where())

        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "X-Engine": "browser",
            "X-With-Generated-Alt": "true",
            "X-With-Images-Summary": "true",
            "Accept": "application/json",
        }

    async def extract(self, urls: list[str]) -> list[ExtractionResult]:
        """
        Extract content from the given URLs using Jina API directly.

        Args:
            urls: list of URLs to extract content from

        Returns:
            list of ExtractionResult objects containing the extracted content
        """
        self._logger.info(
            f"Extracting content from {len(urls)} URLs using JinaExtractor with httpx"
        )
        extraction_tasks = [self._extract_single_url(url) for url in urls]
        return await asyncio.gather(*extraction_tasks)

    async def _extract_single_url(self, url: str) -> ExtractionResult:
        """Extract content from a single URL using httpx to call Jina API directly."""
        response = None
        try:
            response = await self._call_jina_api(url)
            if response.status_code == 200:
                data = response.json().get("data", {})
                return ExtractionSuccess(
                    url=url,
                    content=data.get("content", ""),
                    images=data.get("images", []).values(),
                )
            else:
                self._logger.error(
                    f"Failed to extract content from {url}. Status code: {response.status_code}"
                )
                self._logger.error(f"Response: {response.text}")
                return ExtractionError(
                    url=url,
                    error=f"Failed to extract content. Status code: {response.status_code}",
                )
        except Exception as e:
            self._logger.error(f"Error extracting content from {url}: {str(e)}")
            if response is not None:
                self._logger.error(f"Response: {response.text}")
            return ExtractionError(
                url=url,
                error=str(e),
            )

    async def _call_jina_api(self, url: str) -> httpx.Response:
        """Call Jina API directly using httpx."""
        async with httpx.AsyncClient(
            headers=self.headers, timeout=self.timeout, verify=self.ssl_context
        ) as client:
            response = await client.post(
                self.url,
                json={"url": url},
                headers=self.headers,
            )
        return response
