from __future__ import annotations

import ast
import asyncio
import json
import re
from copy import deepcopy
from datetime import datetime
from logging import Logger
from typing import TYPE_CHECKING, Iterable

from langchain_core.messages import HumanMessage
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import RunnableConfig, RunnableLambda
from markdown import Markdown
from markdown.preprocessors import Preprocessor
from pydantic import BaseModel

from src.api.common.services.content_generator.utils.mermaid_context import (
    mermaid_context,
)
from src.api.common.services.structs import (
    Asignatura,
    ContentMetadata,
    GeneratedContent,
)
from src.api.common.tools.utils import (
    extract_content,
    format_url_markdown,
    validate_and_retry_mermaid,
)
from src.domain.models import AIProcess, AIProcessStatus, AIProcessType

from .utils.formatting import format_docs

if TYPE_CHECKING:
    from ia_gen_core.prompts import PromptManager
    from langchain.retrievers import ContextualCompressionRetriever
    from langchain_core.documents import Document
    from openai import AsyncOpenAI

    from src.api.common.services import LLM, IndexRepository
    from src.api.common.services.ai_tracer import AITracer


class Query(BaseModel):
    query: str


class SearchQueries(BaseModel):
    queries: list[Query]


class ContentGenerator:
    def __init__(
        self,
        openai_client: AsyncOpenAI,
        llm_manager: LLM,
        index_repository: IndexRepository,
        retriever: ContextualCompressionRetriever,
        ai_tracer: AITracer,
        prompt_manager: PromptManager,
        mermaid_base_url: str,
        logger: Logger,
    ):
        self._openai_client = openai_client
        self._llm_manager = llm_manager
        self._index_repository = index_repository
        self._ai_tracer = ai_tracer
        self._retriever = retriever
        self._prompt_manager = prompt_manager
        self._mermaid_base_url = mermaid_base_url
        self._logger = logger

    async def generate_topic_queries(
        self,
        topic_info: dict,
        model: str = "o4-mini",
        reasoning_effort: str | None = None,
        parent_process_id: int | None = None,
    ) -> SearchQueries:
        system_prompt = """
        Tu tarea es generar queries de búsqueda generales para generar un tema sobre un contexto que se te proporcione.
        Se te proporcionará un plan de lo que se va a escribir en el tema, y has de hacer queries que permitan cubrir todas las areas generales necesarias que puedan ser complementadas con fuentes.
        Ya se ha encontrado información reelevante para esta información.
        Lo que debes hacer es generar queries para encontrar pasajes sobre las fuentes que permitan asistir en la generación de contenido.
        Apuntarás a hacer entre 10 - 20 queries totales que permitan cubrir las partes más importantes para las cuales sería reelevante la inclusión de fuentes.
        """
        self._logger.info(
            f"Searching with model: {model} and reasoning_effort: {reasoning_effort}"
        )
        corutines = []
        for epigraph in topic_info.epigraphs:
            corutines.append(
                self._openai_client.responses.parse(
                    instructions=system_prompt,
                    text_format=SearchQueries,
                    model=model,
                    input=f"Topic name: {topic_info.topic_name}\n\nEpigraph info and instructions: {epigraph}",
                    reasoning={
                        "effort": reasoning_effort if reasoning_effort else "low"
                    }
                    if any(sub in model for sub in ["o4", "o3"])
                    else None,
                )
            )
        results = await asyncio.gather(*corutines)
        all_queries = [query for sq in results for query in sq.output_parsed.queries]

        if parent_process_id:
            metadata = {
                "flow": "Topic Query Generation",
                "chain": "Generate Search Queries",
                "model": model,
                "reasoning_effort": reasoning_effort,
                "topic_name": topic_info.topic_name,
            }
            self._ai_tracer.trace_process(
                AIProcessType.GENERATE_TOPIC_QUERIES,
                {"topic_info": str(topic_info)},
                {"queries": [q.query for q in all_queries]},
                metadata,
                tema_id=topic_info.topic_id,
                process_status=AIProcessStatus.COMPLETED,
                parent_id=parent_process_id,
            )

        return SearchQueries(queries=all_queries)

    async def search_documents_multi_query(
        self,
        queries: SearchQueries,
        epigrafe_ids: list[int],
        top_k: int = 5,
        min_score: float = 0.6,
        ai_process: AIProcess | None = None,
    ) -> tuple[str, dict[int, str], dict[str, list[Document]]]:
        """
        Search documents using multiple queries with sequential retrieval.

        Args:
            queries: SearchQueries object with list of queries
            epigrafe_ids: List of epigraph IDs to search within
            top_k: Number of documents to retrieve from vector search per query
            min_score: min reranker score

        Returns:
            Dictionary with aggregated results and metadata
        """
        all_chunks, query_results, seen_chunk_ids = [], {}, set()

        for query_obj in queries.queries:
            query = query_obj.query
            try:
                retriever = self._retriever
                retriever.base_retriever.epigrafe_ids = epigrafe_ids
                metadata = {
                    "chain": "Document Retrieval",
                    "flow": "Content Generation",
                    "query": query,
                }
                chunks = await retriever.with_config(metadata).ainvoke(query)
                valid_chunks = [
                    chunk
                    for chunk in chunks
                    if chunk.metadata.get("relevance_score", 0) > min_score
                ]
                if valid_chunks:
                    query_results[query] = {"chunks": valid_chunks}
                    for chunk in valid_chunks:
                        chunk_id = f"{chunk.metadata.get('doc_id')}_{chunk.metadata.get('chunk_id', '')}"
                        if chunk_id not in seen_chunk_ids:
                            seen_chunk_ids.add(chunk_id)
                            all_chunks.append(chunk)
            except Exception as e:
                self._logger.info(f"Error searching for query '{query}': {e}")
                continue

        formatted_docs, doc_id_to_name, chunks_by_doc = format_docs(all_chunks)

        return formatted_docs, doc_id_to_name, chunks_by_doc

    async def process_mermaid_diagrams(
        self, content: str, view_mode: bool = False
    ) -> tuple[str, list[str], list[str]]:
        """
        Process Mermaid diagrams in content.
        Args:
            content: Content with <mermaid> tags
            view_mode: If True, convert to ```mermaid format. If False, convert to image URL

        Returns:
            Tuple of (processed_content, original_diagrams, diagram_urls)
        """
        mermaid_pattern = r"<mermaid>(.*?)</mermaid>"
        matches = list(re.finditer(mermaid_pattern, content, re.DOTALL))

        if not matches:
            return content, [], []

        original_diagrams = []
        diagram_urls = []

        for match in reversed(matches):
            mermaid_code = match.group(1).strip()

            original_diagrams.insert(0, mermaid_code)

            try:
                validated_code, _ = await validate_and_retry_mermaid(
                    client=self._openai_client,
                    mermaid=mermaid_code,
                    mermaid_base_url=self._mermaid_base_url,
                    model="o4-mini",
                    documentation=mermaid_context,
                )

                if view_mode:
                    replacement = f"```mermaid\n{validated_code}\n```"
                    diagram_urls.insert(0, replacement)
                else:
                    replacement = format_url_markdown(
                        validated_code, self._mermaid_base_url
                    )
                    diagram_urls.insert(0, replacement)

                content = (
                    content[: match.start()] + replacement + content[match.end() :]
                )

            except Exception as e:
                self._logger.info(f"Error processing Mermaid diagram: {e}")
                diagram_urls.insert(0, f"<mermaid>{mermaid_code}</mermaid>")

        return content, original_diagrams, diagram_urls

    async def post_process_content(
        self,
        raw_content: dict[str, str],
        doc_id_to_name: dict,
        chunks_by_doc: dict,
        view_mode: bool = False,
    ) -> list[GeneratedContent]:
        """
        Post-process raw content by adding diagram processing and source extraction.

        Args:
            raw_content: Dictionary of epigraph names to raw content
            doc_id_to_name: Mapping of document IDs to names
            chunks_by_doc: Chunks grouped by document
            view_mode: If True, convert diagrams to ```mermaid format

        Returns:
            List of GeneratedContent objects with full metadata
        """
        generated_contents = []

        for epigraph_name, content in raw_content.items():
            (
                processed_content,
                original_diagrams,
                diagram_urls,
            ) = await self.process_mermaid_diagrams(content, view_mode)
            generated_content = self._extract_sources(
                processed_content, doc_id_to_name, chunks_by_doc
            )

            cleaned_content = re.sub(
                r"<fuentes>.*?</fuentes>", "", processed_content, flags=re.DOTALL
            ).strip()
            generated_content.content = cleaned_content
            metadata = ContentMetadata(
                diagrams=original_diagrams,
                diagrams_urls=diagram_urls,
                original_ai_content=content,
            )
            generated_content.metadata = metadata
            generated_contents.append(generated_content)

        return generated_contents

    async def generate_topic_pipeline(
        self,
        index_id: int,
        topic_id: int,
        provider: str = "model-router",
        model: str = "claude-sonnet-4-20250514",
        view_mode: bool = False,
        search_params: dict = {"model": "gpt-4.1-mini"},
        parent_process_id: int | None = None,
    ) -> tuple[list[GeneratedContent], list[AIProcess | None]]:
        t_info, t_instr = await self._index_repository.get_instructions_per_topic(
            index_id, topic_id=topic_id
        )
        t_info = deepcopy(t_info)
        epigraphs = [e["epigraph_name"] for e in t_info.epigraphs]

        pipeline_config = RunnableConfig(
            run_name=f"Topic Pipeline: {t_info.topic_name}",
            metadata={
                "flow": "Topic Generation Pipeline",
                "topic_name": t_info.topic_name,
                "subject_name": t_info.subject_name,
                "index_id": index_id,
                "topic_id": topic_id,
                "openinference.span.kind": "CHAIN",
                "llm.model_name": model,
                "llm.provider": provider,
                "session.id": f"topic-{topic_id}-{index_id}",
            },
        )

        async def execute_pipeline(_):
            index = await self._index_repository.get_subject_index(index_id)
            memories = await self.get_memories_for_topic(index_id, topic_id)
            search_queries = await self.generate_topic_queries(
                t_info, **search_params, parent_process_id=parent_process_id
            )
            epigrafe_ids = [e.get("epigraph_id") for e in t_info.epigraphs]
            (
                docs,
                doc_id_to_name,
                chunks_by_doc,
            ) = await self.search_documents_multi_query(search_queries, epigrafe_ids)
            raw_content, ai_processes = await self.generate_content(
                t_info.subject_name,
                index,
                t_instr,
                epigraphs,
                epigrafe_ids,
                t_info.topic_name,
                memories,
                docs,
                provider,
                model,
                parent_process_id,
            )
            cleaned_content = self._clean_content(raw_content)

            generated_content = await self.post_process_content(
                cleaned_content, doc_id_to_name, chunks_by_doc, view_mode
            )
            return generated_content, ai_processes

        pipeline = RunnableLambda(execute_pipeline).with_retry(stop_after_attempt=3)
        content, ai_processes = await pipeline.ainvoke({}, config=pipeline_config)
        return content, ai_processes

    def _clean_content(self, content: dict[str, str]) -> dict[str, str]:
        class StripHeaders(Preprocessor):
            def __init__(self, md):
                super().__init__(md)
                self.h3s = []

            def run(self, lines):
                out = []
                for ln in lines:
                    s = ln.lstrip()
                    if s.startswith("# ") and not s.startswith("#### "):
                        continue
                    if s.startswith("## ") and not s.startswith("### "):
                        continue
                    if s.startswith("### ") and not s.startswith("#### "):
                        self.h3s.append(s[4:].strip())
                        continue
                    out.append(ln)
                return out

        for key, value in content.items():
            md = Markdown()
            pre = StripHeaders(md)
            cleaned = "\n".join(pre.run(value.splitlines())).strip()
            content[key] = cleaned
        return content

    async def generate_content(
        self,
        subject_name: str,
        index: Asignatura,
        plan: str,
        epigrafes: list[str],
        epigrafe_ids: list[int],
        t_name: str,
        memories: dict,
        docs: str,
        provider: str,
        model: str,
        parent_process_id: int | None = None,
    ) -> tuple[dict[str, str], list | None]:
        llm = self._llm_manager.get_llm(
            provider=provider,
            model_name=model,
            max_tokens=60000,
            temperature=0.3,
            reasoning_effort="medium",
        )
        out, ai_processes = {}, []

        first_epigraph = epigrafes[0]
        prompt_contenido_tema = self._prompt_manager.get_prompt(
            "prompt-contenido-tema", "chat"
        )
        messages = prompt_contenido_tema.compile().to_langchain()
        template = ChatPromptTemplate.from_messages(messages)
        chain = template | llm

        model_input = {
            "asignatura": subject_name,
            "indice_asignatura": index.model_dump(),
            "plan": plan,
            "nombre_tema": t_name,
            "memorias": str(memories),
            "fuentes_relacionadas": docs,
            "epigrafe": first_epigraph,
        }

        llm_config = {
            "run_name": f"Generate Epigraph: {first_epigraph}",
            "metadata": {
                "chain": "LLM Generation",
                "flow": "Content Generation",
                "epigraph_name": first_epigraph,
            },
        }
        res = await chain.ainvoke(model_input, config=llm_config)
        content = self._extract_content(res)
        out[first_epigraph] = content
        if parent_process_id:
            ap = self._ai_tracer.trace_process(
                AIProcessType.CONTENT_GENERATION_WITH_SOURCES,
                model_input,
                {"content": content},
                llm_config["metadata"],
                epigrafe_id=epigrafe_ids[0],
                parent_id=parent_process_id,
            )
            ai_processes.append(ap)

        messages.append(res)

        for i, epigraph in enumerate(epigrafes[1:], start=1):
            messages.append(HumanMessage(content=f"### {epigraph}"))

            llm_config = {
                "run_name": f"Generate Epigraph: {epigraph}",
                "metadata": {
                    "chain": "LLM Generation",
                    "flow": "Content Generation",
                    "epigraph_name": epigraph,
                },
            }
            chain = ChatPromptTemplate.from_messages(messages) | llm

            res = await chain.ainvoke(model_input, config=llm_config)
            content = self._extract_content(res)
            if parent_process_id:
                ap = self._ai_tracer.trace_process(
                    AIProcessType.CONTENT_GENERATION_WITH_SOURCES,
                    model_input,
                    {"content": content},
                    llm_config["metadata"],
                    epigrafe_id=epigrafe_ids[i],
                    parent_id=parent_process_id,
                )
                ai_processes.append(ap)
            out[epigraph] = content
            messages.append(res)

        return out, ai_processes if parent_process_id else None

    def _extract_content(self, res) -> str:
        """Extract text content from LLM response"""
        c = getattr(res, "content", "")
        if isinstance(c, str):
            return c
        elif isinstance(c, list):
            return "".join(
                i.get("text", "")
                for i in c
                if isinstance(i, dict) and i.get("type") in {"text", "output_text"}
            )
        return ""

    def _extract_sources(
        self, content: str, doc_id_to_name: dict, chunks_by_doc: dict
    ) -> GeneratedContent:
        """Extract source information from generated content."""
        try:
            related_docs, related_chunks = set(), set()
            fuentes_usadas = "".join(extract_content(content, tag_name="fuentes"))
            if not fuentes_usadas.strip():
                return GeneratedContent()

            fuentes_usadas = ast.literal_eval(fuentes_usadas)
            if not isinstance(fuentes_usadas, list):
                return GeneratedContent()

            self._logger.info(f"Fuentes usadas are: {str(fuentes_usadas)}")

            used_chunks = self._get_used_chunks(
                fuentes_usadas, doc_id_to_name, chunks_by_doc
            )
            if used_chunks:
                for chunk in used_chunks:
                    related_docs.add(chunk.metadata["doc_id"])
                    related_chunks.add(chunk.metadata["chunk_id"])
                return GeneratedContent(
                    related_chunks=list(related_chunks),
                    related_docs=list(related_docs),
                )
            return GeneratedContent()
        except Exception as e:
            self._logger.info(f"Error extracting sources: {e}, returning empty list")
            return GeneratedContent()

    def _get_used_chunks(self, fuentes_usadas, doc_id_to_name, chunks_by_doc):
        """Get chunks used based on source indices."""
        used_chunks = []
        for fuente in fuentes_usadas:
            doc_name = doc_id_to_name.get(fuente)
            if doc_name:
                chunks = chunks_by_doc.get(doc_name)
                if chunks:
                    used_chunks.extend(chunks)
        return used_chunks

    async def create_memories_for_topics(self, topics_info: list) -> dict[int, list]:
        """
        Devuelve un dict donde la clave es el índice del tema (posicion - 1) y el valor
        son las memorias de TODOS los temas previos a ese índice.
        """
        accumulated: dict[int, list] = {}
        previous: set = set()
        tools = [
            {
                "type": "function",
                "name": "memory",
                "description": "Accepts a list of strings with memories, and topic id to which it corresponds.",
                "strict": True,
                "parameters": {
                    "type": "object",
                    "required": ["memories", "topic_id"],
                    "properties": {
                        "memories": {
                            "type": "array",
                            "description": "List of memory strings",
                            "items": {
                                "type": "string",
                                "description": "A memory string",
                            },
                        },
                        "topic_id": {
                            "type": "string",
                            "description": "Identifier for the topic associated with the memories",
                        },
                    },
                    "additionalProperties": False,
                },
            }
        ]
        prompt_memorias = self._prompt_manager.get_prompt("prompt-memorias", "chat")
        for i, tema in enumerate(topics_info):
            accumulated[i] = list(previous)
            messages = prompt_memorias.compile(
                tema=tema, tema_id=i, memorias=previous
            ).to_list()

            response = await self._openai_client.responses.create(
                model="o4-mini",
                tools=tools,
                input=messages,
            )

            new_memories: Iterable = []
            for r in response.output:
                if hasattr(r, "arguments"):
                    new_memories = json.loads(r.arguments).get("memories", [])
                    break

            previous.update(new_memories)

        return accumulated

    async def store_memories(
        self, index_id: int, memories_dict: dict[int, list[str]], version: int = 1
    ) -> None:
        """
        Store memories for all topics in an index, overriding existing memories.
        Args:
            index_id: ID of the index
            memories_dict: Dictionary from create_memories_for_topics where keys are
                          topic indices (0-based) and values are lists of memory strings
            version: Version of the memories (default: 1)
        """

        topics = await self._index_repository.get_index_elements(
            index_id, structure_type="tema"
        )
        for topic, (_, memories) in zip(topics, memories_dict.items()):
            memory_data = [
                {
                    "version": version,
                    "memories": memories,
                    "created_at": datetime.now().isoformat(),
                }
            ]
            await self._index_repository.update_topic_memories(topic.id, memory_data)

    async def get_memories_for_topic(
        self, index_id: int, topic_id: int, version: int = 1
    ) -> list[str]:
        """
        Retrieve memories for a specific topic from the database.

        Args:
            index_id: ID of the index
            topic_id: Topic id.
            version: Version of the memories to retrieve (default: 1)

        Returns:
            List of memory strings for the topic
        """
        topics = await self._index_repository.get_index_elements(
            index_id, structure_type="tema"
        )
        topic = next(iter([t for t in topics if t.id == topic_id]), None)

        if not topic or not topic.memories:
            return []

        for memory_entry in topic.memories:
            if memory_entry.get("version") == version:
                return memory_entry.get("memories", [])

        return []

    async def create_and_store_memories_for_index(
        self, index_id: int, version: int = 1
    ) -> dict[int, list[str]]:
        """
        Create and store memories for all topics in an index.

        Args:
            index_id: ID of the index
            version: Version of the memories (default: 1)

        Returns:
            Dictionary of memories created and stored
        """
        _, topics_info = await self._index_repository.get_instructions_per_topic(
            index_id, content_plan_version=version
        )
        memories_dict = await self.create_memories_for_topics(topics_info)
        await self.store_memories(index_id, memories_dict, version)

        return memories_dict
