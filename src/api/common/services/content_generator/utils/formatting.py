from collections import defaultdict

from langchain_core.documents import Document


def format_docs(
    docs: list[Document],
) -> tuple[str, dict[int, str], dict[str, list[Document]]]:
    chunks_by_doc = defaultdict(list)
    doc_context = "\n"
    for doc in docs:
        chunks_by_doc[doc.metadata.get("name")].append(doc)
    doc_id_to_name = {i: k for i, k in enumerate(chunks_by_doc)}
    for i, (source_doc_name, chunks) in enumerate(chunks_by_doc.items()):
        doc_context += f"""[[Document {i}]] \n\nName:{source_doc_name}\n\n
                       Inline citation: {chunks[0].metadata["inline_citation"]}\n\n
                       Document Summary: {chunks[0].metadata["summary"]}\n
                       Potentially reelevant excerpts:\n\n"""
        for i, chunk in enumerate(chunks):
            doc_context += f"""Page: {chunk.metadata["page"]}\n\n
                               Content: {chunk.page_content}\n\n
                               Score: **{chunk.metadata["relevance_score"]}**\n\n"""
        # Sorted by page.
    return doc_context, doc_id_to_name, chunks_by_doc
