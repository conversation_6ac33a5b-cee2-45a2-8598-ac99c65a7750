mermaid_context = r"""Title: Flowcharts Syntax | Mermaid

URL Source: https://mermaid.js.org/syntax/flowchart.html

Markdown Content:
Flowcharts are composed of **nodes** (geometric shapes) and **edges** (arrows or lines). The Mermaid code defines how nodes and edges are made and accommodates different arrow types, multi-directional arrows, and any linking to and from subgraphs.

WARNING

If you are using the word "end" in a Flowchart node, capitalize the entire word or any of the letters (e.g., "End" or "END"), or apply this [workaround](https://github.com/mermaid-js/mermaid/issues/1444#issuecomment-*********). Typing "end" in all lowercase letters will break the Flowchart.

WARNING

If you are using the letter "o" or "x" as the first letter in a connecting Flowchart node, add a space before the letter or capitalize the letter (e.g., "dev--- ops", "dev---Ops").

Typing "A---oB" will create a [circle edge](https://mermaid.js.org/syntax/flowchart.html#circle-edge-example).

Typing "A---xB" will create a [cross edge](https://mermaid.js.org/syntax/flowchart.html#cross-edge-example).

### A node (default) [​](https://mermaid.js.org/syntax/flowchart.html#a-node-default)

INFO

The id is what is displayed in the box.

TIP

Instead of `flowchart` one can also use `graph`.

### A node with text [​](https://mermaid.js.org/syntax/flowchart.html#a-node-with-text)

It is also possible to set text in the box that differs from the id. If this is done several times, it is the last text found for the node that will be used. Also if you define edges for the node later on, you can omit text definitions. The one previously defined will be used when rendering the box.

#### Unicode text [​](https://mermaid.js.org/syntax/flowchart.html#unicode-text)

Use `"` to enclose the unicode text.

#### Markdown formatting [​](https://mermaid.js.org/syntax/flowchart.html#markdown-formatting)

Use double quotes and backticks "\` text \`" to enclose the markdown text.

### Direction [​](https://mermaid.js.org/syntax/flowchart.html#direction)

This statement declares the direction of the Flowchart.

This declares the flowchart is oriented from top to bottom (`TD` or `TB`).

This declares the flowchart is oriented from left to right (`LR`).

Possible FlowChart orientations are:

*   TB - Top to bottom
*   TD - Top-down/ same as top to bottom
*   BT - Bottom to top
*   RL - Right to left
*   LR - Left to right

Node shapes [​](https://mermaid.js.org/syntax/flowchart.html#node-shapes)
-------------------------------------------------------------------------

### A node with round edges [​](https://mermaid.js.org/syntax/flowchart.html#a-node-with-round-edges)

### A stadium-shaped node [​](https://mermaid.js.org/syntax/flowchart.html#a-stadium-shaped-node)

### A node in a subroutine shape [​](https://mermaid.js.org/syntax/flowchart.html#a-node-in-a-subroutine-shape)

### A node in a cylindrical shape [​](https://mermaid.js.org/syntax/flowchart.html#a-node-in-a-cylindrical-shape)

### A node in the form of a circle [​](https://mermaid.js.org/syntax/flowchart.html#a-node-in-the-form-of-a-circle)

### A node in an asymmetric shape [​](https://mermaid.js.org/syntax/flowchart.html#a-node-in-an-asymmetric-shape)

Currently only the shape above is possible and not its mirror. _This might change with future releases._

### A node (rhombus) [​](https://mermaid.js.org/syntax/flowchart.html#a-node-rhombus)

### A hexagon node [​](https://mermaid.js.org/syntax/flowchart.html#a-hexagon-node)

### Parallelogram [​](https://mermaid.js.org/syntax/flowchart.html#parallelogram)

### Parallelogram alt [​](https://mermaid.js.org/syntax/flowchart.html#parallelogram-alt)

### Trapezoid [​](https://mermaid.js.org/syntax/flowchart.html#trapezoid)

### Trapezoid alt [​](https://mermaid.js.org/syntax/flowchart.html#trapezoid-alt)

### Double circle [​](https://mermaid.js.org/syntax/flowchart.html#double-circle)

Expanded Node Shapes in Mermaid Flowcharts (v11.3.0+) [​](https://mermaid.js.org/syntax/flowchart.html#expanded-node-shapes-in-mermaid-flowcharts-v11-3-0)
----------------------------------------------------------------------------------------------------------------------------------------------------------

Mermaid introduces 30 new shapes to enhance the flexibility and precision of flowchart creation. These new shapes provide more options to represent processes, decisions, events, data storage visually, and other elements within your flowcharts, improving clarity and semantic meaning.

New Syntax for Shape Definition

Mermaid now supports a general syntax for defining shape types to accommodate the growing number of shapes. This syntax allows you to assign specific shapes to nodes using a clear and flexible format:

This syntax creates a node A as a rectangle. It renders in the same way as `A["A"]`, or `A`.

### Complete List of New Shapes [​](https://mermaid.js.org/syntax/flowchart.html#complete-list-of-new-shapes)

Below is a comprehensive list of the newly introduced shapes and their corresponding semantic meanings, short names, and aliases:

| **Semantic Name** | **Shape Name** | **Short Name** | **Description** | **Alias Supported** |
| --- | --- | --- | --- | --- |
| Card | Notched Rectangle | `notch-rect` | Represents a card | `card`, `notched-rectangle` |
| Collate | Hourglass | `hourglass` | Represents a collate operation | `collate`, `hourglass` |
| Com Link | Lightning Bolt | `bolt` | Communication link | `com-link`, `lightning-bolt` |
| Comment | Curly Brace | `brace` | Adds a comment | `brace-l`, `comment` |
| Comment Right | Curly Brace | `brace-r` | Adds a comment |  |
| Comment with braces on both sides | Curly Braces | `braces` | Adds a comment |  |
| Data Input/Output | Lean Right | `lean-r` | Represents input or output | `in-out`, `lean-right` |
| Data Input/Output | Lean Left | `lean-l` | Represents output or input | `lean-left`, `out-in` |
| Database | Cylinder | `cyl` | Database storage | `cylinder`, `database`, `db` |
| Decision | Diamond | `diam` | Decision-making step | `decision`, `diamond`, `question` |
| Delay | Half-Rounded Rectangle | `delay` | Represents a delay | `half-rounded-rectangle` |
| Direct Access Storage | Horizontal Cylinder | `h-cyl` | Direct access storage | `das`, `horizontal-cylinder` |
| Disk Storage | Lined Cylinder | `lin-cyl` | Disk storage | `disk`, `lined-cylinder` |
| Display | Curved Trapezoid | `curv-trap` | Represents a display | `curved-trapezoid`, `display` |
| Divided Process | Divided Rectangle | `div-rect` | Divided process shape | `div-proc`, `divided-process`, `divided-rectangle` |
| Document | Document | `doc` | Represents a document | `doc`, `document` |
| Event | Rounded Rectangle | `rounded` | Represents an event | `event` |
| Extract | Triangle | `tri` | Extraction process | `extract`, `triangle` |
| Fork/Join | Filled Rectangle | `fork` | Fork or join in process flow | `join` |
| Internal Storage | Window Pane | `win-pane` | Internal storage | `internal-storage`, `window-pane` |
| Junction | Filled Circle | `f-circ` | Junction point | `filled-circle`, `junction` |
| Lined Document | Lined Document | `lin-doc` | Lined document | `lined-document` |
| Lined/Shaded Process | Lined Rectangle | `lin-rect` | Lined process shape | `lin-proc`, `lined-process`, `lined-rectangle`, `shaded-process` |
| Loop Limit | Trapezoidal Pentagon | `notch-pent` | Loop limit step | `loop-limit`, `notched-pentagon` |
| Manual File | Flipped Triangle | `flip-tri` | Manual file operation | `flipped-triangle`, `manual-file` |
| Manual Input | Sloped Rectangle | `sl-rect` | Manual input step | `manual-input`, `sloped-rectangle` |
| Manual Operation | Trapezoid Base Top | `trap-t` | Represents a manual task | `inv-trapezoid`, `manual`, `trapezoid-top` |
| Multi-Document | Stacked Document | `docs` | Multiple documents | `documents`, `st-doc`, `stacked-document` |
| Multi-Process | Stacked Rectangle | `st-rect` | Multiple processes | `processes`, `procs`, `stacked-rectangle` |
| Odd | Odd | `odd` | Odd shape |  |
| Paper Tape | Flag | `flag` | Paper tape | `paper-tape` |
| Prepare Conditional | Hexagon | `hex` | Preparation or condition step | `hexagon`, `prepare` |
| Priority Action | Trapezoid Base Bottom | `trap-b` | Priority action | `priority`, `trapezoid`, `trapezoid-bottom` |
| Process | Rectangle | `rect` | Standard process shape | `proc`, `process`, `rectangle` |
| Start | Circle | `circle` | Starting point | `circ` |
| Start | Small Circle | `sm-circ` | Small starting point | `small-circle`, `start` |
| Stop | Double Circle | `dbl-circ` | Represents a stop point | `double-circle` |
| Stop | Framed Circle | `fr-circ` | Stop point | `framed-circle`, `stop` |
| Stored Data | Bow Tie Rectangle | `bow-rect` | Stored data | `bow-tie-rectangle`, `stored-data` |
| Subprocess | Framed Rectangle | `fr-rect` | Subprocess | `framed-rectangle`, `subproc`, `subprocess`, `subroutine` |
| Summary | Crossed Circle | `cross-circ` | Summary | `crossed-circle`, `summary` |
| Tagged Document | Tagged Document | `tag-doc` | Tagged document | `tag-doc`, `tagged-document` |
| Tagged Process | Tagged Rectangle | `tag-rect` | Tagged process | `tag-proc`, `tagged-process`, `tagged-rectangle` |
| Terminal Point | Stadium | `stadium` | Terminal point | `pill`, `terminal` |
| Text Block | Text Block | `text` | Text block |  |

### Example Flowchart with New Shapes [​](https://mermaid.js.org/syntax/flowchart.html#example-flowchart-with-new-shapes)

Here’s an example flowchart that utilizes some of the newly introduced shapes:

### Process [​](https://mermaid.js.org/syntax/flowchart.html#process)

### Event [​](https://mermaid.js.org/syntax/flowchart.html#event)

### Terminal Point (Stadium) [​](https://mermaid.js.org/syntax/flowchart.html#terminal-point-stadium)

### Subprocess [​](https://mermaid.js.org/syntax/flowchart.html#subprocess)

### Database (Cylinder) [​](https://mermaid.js.org/syntax/flowchart.html#database-cylinder)

### Start (Circle) [​](https://mermaid.js.org/syntax/flowchart.html#start-circle)

### Odd [​](https://mermaid.js.org/syntax/flowchart.html#odd)

### Decision (Diamond) [​](https://mermaid.js.org/syntax/flowchart.html#decision-diamond)

### Prepare Conditional (Hexagon) [​](https://mermaid.js.org/syntax/flowchart.html#prepare-conditional-hexagon)

### Data Input/Output (Lean Right) [​](https://mermaid.js.org/syntax/flowchart.html#data-input-output-lean-right)

### Data Input/Output (Lean Left) [​](https://mermaid.js.org/syntax/flowchart.html#data-input-output-lean-left)

### Priority Action (Trapezoid Base Bottom) [​](https://mermaid.js.org/syntax/flowchart.html#priority-action-trapezoid-base-bottom)

### Manual Operation (Trapezoid Base Top) [​](https://mermaid.js.org/syntax/flowchart.html#manual-operation-trapezoid-base-top)

### Stop (Double Circle) [​](https://mermaid.js.org/syntax/flowchart.html#stop-double-circle)

### Text Block [​](https://mermaid.js.org/syntax/flowchart.html#text-block)

### Card (Notched Rectangle) [​](https://mermaid.js.org/syntax/flowchart.html#card-notched-rectangle)

### Lined/Shaded Process [​](https://mermaid.js.org/syntax/flowchart.html#lined-shaded-process)

### Start (Small Circle) [​](https://mermaid.js.org/syntax/flowchart.html#start-small-circle)

### Stop (Framed Circle) [​](https://mermaid.js.org/syntax/flowchart.html#stop-framed-circle)

### Fork/Join (Long Rectangle) [​](https://mermaid.js.org/syntax/flowchart.html#fork-join-long-rectangle)

### Collate (Hourglass) [​](https://mermaid.js.org/syntax/flowchart.html#collate-hourglass)

### Comment (Curly Brace) [​](https://mermaid.js.org/syntax/flowchart.html#comment-curly-brace)

### Comment Right (Curly Brace Right) [​](https://mermaid.js.org/syntax/flowchart.html#comment-right-curly-brace-right)

### Comment with braces on both sides [​](https://mermaid.js.org/syntax/flowchart.html#comment-with-braces-on-both-sides)

### Com Link (Lightning Bolt) [​](https://mermaid.js.org/syntax/flowchart.html#com-link-lightning-bolt)

### Document [​](https://mermaid.js.org/syntax/flowchart.html#document)

### Delay (Half-Rounded Rectangle) [​](https://mermaid.js.org/syntax/flowchart.html#delay-half-rounded-rectangle)

### Direct Access Storage (Horizontal Cylinder) [​](https://mermaid.js.org/syntax/flowchart.html#direct-access-storage-horizontal-cylinder)

### Disk Storage (Lined Cylinder) [​](https://mermaid.js.org/syntax/flowchart.html#disk-storage-lined-cylinder)

### Display (Curved Trapezoid) [​](https://mermaid.js.org/syntax/flowchart.html#display-curved-trapezoid)

### Divided Process (Divided Rectangle) [​](https://mermaid.js.org/syntax/flowchart.html#divided-process-divided-rectangle)

### Extract (Small Triangle) [​](https://mermaid.js.org/syntax/flowchart.html#extract-small-triangle)

### Internal Storage (Window Pane) [​](https://mermaid.js.org/syntax/flowchart.html#internal-storage-window-pane)

### Junction (Filled Circle) [​](https://mermaid.js.org/syntax/flowchart.html#junction-filled-circle)

### Lined Document [​](https://mermaid.js.org/syntax/flowchart.html#lined-document)

### Loop Limit (Notched Pentagon) [​](https://mermaid.js.org/syntax/flowchart.html#loop-limit-notched-pentagon)

### Manual File (Flipped Triangle) [​](https://mermaid.js.org/syntax/flowchart.html#manual-file-flipped-triangle)

### Manual Input (Sloped Rectangle) [​](https://mermaid.js.org/syntax/flowchart.html#manual-input-sloped-rectangle)

### Multi-Document (Stacked Document) [​](https://mermaid.js.org/syntax/flowchart.html#multi-document-stacked-document)

### Multi-Process (Stacked Rectangle) [​](https://mermaid.js.org/syntax/flowchart.html#multi-process-stacked-rectangle)

### Paper Tape (Flag) [​](https://mermaid.js.org/syntax/flowchart.html#paper-tape-flag)

### Stored Data (Bow Tie Rectangle) [​](https://mermaid.js.org/syntax/flowchart.html#stored-data-bow-tie-rectangle)

### Summary (Crossed Circle) [​](https://mermaid.js.org/syntax/flowchart.html#summary-crossed-circle)

### Tagged Document [​](https://mermaid.js.org/syntax/flowchart.html#tagged-document)

### Tagged Process (Tagged Rectangle) [​](https://mermaid.js.org/syntax/flowchart.html#tagged-process-tagged-rectangle)

Special shapes in Mermaid Flowcharts (v11.3.0+) [​](https://mermaid.js.org/syntax/flowchart.html#special-shapes-in-mermaid-flowcharts-v11-3-0)
----------------------------------------------------------------------------------------------------------------------------------------------

Mermaid also introduces 2 special shapes to enhance your flowcharts: **icon** and **image**. These shapes allow you to include icons and images directly within your flowcharts, providing more visual context and clarity.

### Icon Shape [​](https://mermaid.js.org/syntax/flowchart.html#icon-shape)

You can use the `icon` shape to include an icon in your flowchart. To use icons, you need to register the icon pack first. Follow the instructions provided [here](https://mermaid.js.org/config/icons.html). The syntax for defining an icon shape is as follows:

### Parameters [​](https://mermaid.js.org/syntax/flowchart.html#parameters)

*   **icon**: The name of the icon from the registered icon pack.
*   **form**: Specifies the background shape of the icon. If not defined there will be no background to icon. Options include:
    *   `square`
    *   `circle`
    *   `rounded`
*   **label**: The text label associated with the icon. This can be any string. If not defined, no label will be displayed.
*   **pos**: The position of the label. If not defined label will default to bottom of icon. Possible values are:
    *   `t`
    *   `b`
*   **h**: The height of the icon. If not defined this will default to 48 which is minimum.

### Image Shape [​](https://mermaid.js.org/syntax/flowchart.html#image-shape)

You can use the `image` shape to include an image in your flowchart. The syntax for defining an image shape is as follows:

### Parameters [​](https://mermaid.js.org/syntax/flowchart.html#parameters-1)

*   **img**: The URL of the image to be displayed.
*   **label**: The text label associated with the image. This can be any string. If not defined, no label will be displayed.
*   **pos**: The position of the label. If not defined, the label will default to the bottom of the image. Possible values are:
    *   `t`
    *   `b`
*   **w**: The width of the image. If not defined, this will default to the natural width of the image.
*   **h**: The height of the image. If not defined, this will default to the natural height of the image.
*   **constraint**: Determines if the image should constrain the node size. This setting also ensures the image maintains its original aspect ratio, adjusting the height (`h`) accordingly to the width (`w`). If not defined, this will default to `off` Possible values are:
    *   `on`
    *   `off`

These new shapes provide additional flexibility and visual appeal to your flowcharts, making them more informative and engaging.

Links between nodes [​](https://mermaid.js.org/syntax/flowchart.html#links-between-nodes)
-----------------------------------------------------------------------------------------

Nodes can be connected with links/edges. It is possible to have different types of links or attach a text string to a link.

### A link with arrow head [​](https://mermaid.js.org/syntax/flowchart.html#a-link-with-arrow-head)

### An open link [​](https://mermaid.js.org/syntax/flowchart.html#an-open-link)

### Text on links [​](https://mermaid.js.org/syntax/flowchart.html#text-on-links)

or

### A link with arrow head and text [​](https://mermaid.js.org/syntax/flowchart.html#a-link-with-arrow-head-and-text)

or

### Dotted link [​](https://mermaid.js.org/syntax/flowchart.html#dotted-link)

### Dotted link with text [​](https://mermaid.js.org/syntax/flowchart.html#dotted-link-with-text)

### Thick link [​](https://mermaid.js.org/syntax/flowchart.html#thick-link)

### Thick link with text [​](https://mermaid.js.org/syntax/flowchart.html#thick-link-with-text)

### An invisible link [​](https://mermaid.js.org/syntax/flowchart.html#an-invisible-link)

This can be a useful tool in some instances where you want to alter the default positioning of a node.

### Chaining of links [​](https://mermaid.js.org/syntax/flowchart.html#chaining-of-links)

It is possible declare many links in the same line as per below:

It is also possible to declare multiple nodes links in the same line as per below:

You can then describe dependencies in a very expressive way. Like the one-liner below:

If you describe the same diagram using the basic syntax, it will take four lines. A word of warning, one could go overboard with this making the flowchart harder to read in markdown form. The Swedish word `lagom` comes to mind. It means, not too much and not too little. This goes for expressive syntaxes as well.

New arrow types [​](https://mermaid.js.org/syntax/flowchart.html#new-arrow-types)
---------------------------------------------------------------------------------

There are new types of arrows supported:

*   circle edge
*   cross edge

### Circle edge example [​](https://mermaid.js.org/syntax/flowchart.html#circle-edge-example)

### Cross edge example [​](https://mermaid.js.org/syntax/flowchart.html#cross-edge-example)

Multi directional arrows [​](https://mermaid.js.org/syntax/flowchart.html#multi-directional-arrows)
---------------------------------------------------------------------------------------------------

There is the possibility to use multidirectional arrows.

### Minimum length of a link [​](https://mermaid.js.org/syntax/flowchart.html#minimum-length-of-a-link)

Each node in the flowchart is ultimately assigned to a rank in the rendered graph, i.e. to a vertical or horizontal level (depending on the flowchart orientation), based on the nodes to which it is linked. By default, links can span any number of ranks, but you can ask for any link to be longer than the others by adding extra dashes in the link definition.

In the following example, two extra dashes are added in the link from node _B_ to node _E_, so that it spans two more ranks than regular links:

> **Note** Links may still be made longer than the requested number of ranks by the rendering engine to accommodate other requests.

When the link label is written in the middle of the link, the extra dashes must be added on the right side of the link. The following example is equivalent to the previous one:

For dotted or thick links, the characters to add are equals signs or dots, as summed up in the following table:

| Length | 1 | 2 | 3 |
| --- | --- | --- | --- |
| Normal | `---` | `----` | `-----` |
| Normal with arrow | `-->` | `--->` | `---->` |
| Thick | `===` | `====` | `=====` |
| Thick with arrow | `==>` | `===>` | `====>` |
| Dotted | `-.-` | `-..-` | `-...-` |
| Dotted with arrow | `-.->` | `-..->` | `-...->` |

Special characters that break syntax [​](https://mermaid.js.org/syntax/flowchart.html#special-characters-that-break-syntax)
---------------------------------------------------------------------------------------------------------------------------

It is possible to put text within quotes in order to render more troublesome characters. As in the example below:

### Entity codes to escape characters [​](https://mermaid.js.org/syntax/flowchart.html#entity-codes-to-escape-characters)

It is possible to escape characters using the syntax exemplified here.

Numbers given are base 10, so `#` can be encoded as `#35;`. It is also supported to use HTML character names.

Subgraphs [​](https://mermaid.js.org/syntax/flowchart.html#subgraphs)
---------------------------------------------------------------------

```
subgraph title
    graph definition
end
```

An example below:

You can also set an explicit id for the subgraph.

### flowcharts [​](https://mermaid.js.org/syntax/flowchart.html#flowcharts)

With the graphtype flowchart it is also possible to set edges to and from subgraphs as in the flowchart below.

### Direction in subgraphs [​](https://mermaid.js.org/syntax/flowchart.html#direction-in-subgraphs)

With the graphtype flowcharts you can use the direction statement to set the direction which the subgraph will render like in this example.

#### Limitation [​](https://mermaid.js.org/syntax/flowchart.html#limitation)

If any of a subgraph's nodes are linked to the outside, subgraph direction will be ignored. Instead the subgraph will inherit the direction of the parent graph:

Markdown Strings [​](https://mermaid.js.org/syntax/flowchart.html#markdown-strings)
-----------------------------------------------------------------------------------

The "Markdown Strings" feature enhances flowcharts and mind maps by offering a more versatile string type, which supports text formatting options such as bold and italics, and automatically wraps text within labels.

Formatting:

*   For bold text, use double asterisks (`**`) before and after the text.
*   For italics, use single asterisks (`*`) before and after the text.
*   With traditional strings, you needed to add `<br>` tags for text to wrap in nodes. However, markdown strings automatically wrap text when it becomes too long and allows you to start a new line by simply using a newline character instead of a `<br>` tag.

This feature is applicable to node labels, edge labels, and subgraph labels.

The auto wrapping can be disabled by using

```
---
config:
  markdownAutoWrap: false
---
graph LR
```

Interaction [​](https://mermaid.js.org/syntax/flowchart.html#interaction)
-------------------------------------------------------------------------

It is possible to bind a click event to a node, the click can lead to either a javascript callback or to a link which will be opened in a new browser tab.

INFO

This functionality is disabled when using `securityLevel='strict'` and enabled when using `securityLevel='loose'`.

```
click nodeId callback
click nodeId call callback()
```

*   nodeId is the id of the node
*   callback is the name of a javascript function defined on the page displaying the graph, the function will be called with the nodeId as parameter.

Examples of tooltip usage below:

html

```
<script>
  window.callback = function () {
    alert('A callback was triggered');
  };
</script>
```

The tooltip text is surrounded in double quotes. The styles of the tooltip are set by the class `.mermaidTooltip`.

> **Success** The tooltip functionality and the ability to link to urls are available from version 0.5.2.

?\> Due to limitations with how Docsify handles JavaScript callback functions, an alternate working demo for the above code can be viewed at [this jsfiddle](https://jsfiddle.net/yk4h7qou/2/).

Links are opened in the same browser tab/window by default. It is possible to change this by adding a link target to the click definition (`_self`, `_blank`, `_parent` and `_top` are supported):

Beginner's tip—a full example using interactive links in a html context:

html

```
<body>
  <pre class="mermaid">
    flowchart LR
        A-->B
        B-->C
        C-->D
        click A callback "Tooltip"
        click B "https://www.github.com" "This is a link"
        click C call callback() "Tooltip"
        click D href "https://www.github.com" "This is a link"
  </pre>

  <script>
    window.callback = function () {
      alert('A callback was triggered');
    };
    const config = {
      startOnLoad: true,
      flowchart: { useMaxWidth: true, htmlLabels: true, curve: 'cardinal' },
      securityLevel: 'loose',
    };
    mermaid.initialize(config);
  </script>
</body>
```

### Comments [​](https://mermaid.js.org/syntax/flowchart.html#comments)

Comments can be entered within a flow diagram, which will be ignored by the parser. Comments need to be on their own line, and must be prefaced with `%%` (double percent signs). Any text after the start of the comment to the next newline will be treated as a comment, including any flow syntax

Styling and classes [​](https://mermaid.js.org/syntax/flowchart.html#styling-and-classes)
-----------------------------------------------------------------------------------------

### Styling links [​](https://mermaid.js.org/syntax/flowchart.html#styling-links)

It is possible to style links. For instance, you might want to style a link that is going backwards in the flow. As links have no ids in the same way as nodes, some other way of deciding what style the links should be attached to is required. Instead of ids, the order number of when the link was defined in the graph is used, or use default to apply to all links. In the example below the style defined in the linkStyle statement will belong to the fourth link in the graph:

```
linkStyle 3 stroke:#ff3,stroke-width:4px,color:red;
```

It is also possible to add style to multiple links in a single statement, by separating link numbers with commas:

```
linkStyle 1,2,7 color:blue;
```

### Styling line curves [​](https://mermaid.js.org/syntax/flowchart.html#styling-line-curves)

It is possible to style the type of curve used for lines between items, if the default method does not meet your needs. Available curve styles include `basis`, `bumpX`, `bumpY`, `cardinal`, `catmullRom`, `linear`, `monotoneX`, `monotoneY`, `natural`, `step`, `stepAfter`, and `stepBefore`.

In this example, a left-to-right graph uses the `stepBefore` curve style:

```
%%{ init: { 'flowchart': { 'curve': 'stepBefore' } } }%%
graph LR
```

For a full list of available curves, including an explanation of custom curves, refer to the [Shapes](https://github.com/d3/d3-shape/blob/main/README.md#curves) documentation in the [d3-shape](https://github.com/d3/d3-shape/) project.

### Styling a node [​](https://mermaid.js.org/syntax/flowchart.html#styling-a-node)

It is possible to apply specific styles such as a thicker border or a different background color to a node.

#### Classes [​](https://mermaid.js.org/syntax/flowchart.html#classes)

More convenient than defining the style every time is to define a class of styles and attach this class to the nodes that should have a different look.

A class definition looks like the example below:

```
    classDef className fill:#f9f,stroke:#333,stroke-width:4px;
```

Also, it is possible to define style to multiple classes in one statement:

```
    classDef firstClassName,secondClassName font-size:12pt;
```

Attachment of a class to a node is done as per below:

It is also possible to attach a class to a list of nodes in one statement:

```
    class nodeId1,nodeId2 className;
```

A shorter form of adding a class is to attach the classname to the node using the `:::`operator as per below:

This form can be used when declaring multiple links between nodes:

### CSS classes [​](https://mermaid.js.org/syntax/flowchart.html#css-classes)

It is also possible to predefine classes in CSS styles that can be applied from the graph definition as in the example below:

**Example style**

html

```
<style>
  .cssClass > rect {
    fill: #ff0000;
    stroke: #ffff00;
    stroke-width: 4px;
  }
</style>
```

**Example definition**

### Default class [​](https://mermaid.js.org/syntax/flowchart.html#default-class)

If a class is named default it will be assigned to all classes without specific class definitions.

```
    classDef default fill:#f9f,stroke:#333,stroke-width:4px;
```

Basic support for fontawesome [​](https://mermaid.js.org/syntax/flowchart.html#basic-support-for-fontawesome)
-------------------------------------------------------------------------------------------------------------

It is possible to add icons from fontawesome.

The icons are accessed via the syntax fa:#icon class name#.

Mermaid supports Font Awesome if the CSS is included on the website. Mermaid does not have any restriction on the version of Font Awesome that can be used.

Please refer the [Official Font Awesome Documentation](https://fontawesome.com/start) on how to include it in your website.

Adding this snippet in the `<head>` would add support for Font Awesome v6.5.1

html

```
<link
  href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"
  rel="stylesheet"
/>
```

### Custom icons [​](https://mermaid.js.org/syntax/flowchart.html#custom-icons)

It is possible to use custom icons served from Font Awesome as long as the website imports the corresponding kit.

Note that this is currently a paid feature from Font Awesome.

For custom icons, you need to use the `fak` prefix.

**Example**

```
flowchart TD
    B[fa:fa-twitter] %% standard icon
    B-->E(fak:fa-custom-icon-name) %% custom icon
```

And trying to render it

Graph declarations with spaces between vertices and link and without semicolon [​](https://mermaid.js.org/syntax/flowchart.html#graph-declarations-with-spaces-between-vertices-and-link-and-without-semicolon)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

*   In graph declarations, the statements also can now end without a semicolon. After release 0.2.16, ending a graph statement with semicolon is just optional. So the below graph declaration is also valid along with the old declarations of the graph.

*   A single space is allowed between vertices and the link. However there should not be any space between a vertex and its text and a link and its text. The old syntax of graph declaration will also work and hence this new feature is optional and is introduced to improve readability.


Below is the new declaration of the graph edges which is also valid along with the old declaration of the graph edges.

Configuration [​](https://mermaid.js.org/syntax/flowchart.html#configuration)
-----------------------------------------------------------------------------

### Renderer [​](https://mermaid.js.org/syntax/flowchart.html#renderer)

The layout of the diagram is done with the renderer. The default renderer is dagre.

Starting with Mermaid version 9.4, you can use an alternate renderer named elk. The elk renderer is better for larger and/or more complex diagrams.

The _elk_ renderer is an experimental feature. You can change the renderer to elk by adding this directive:

```
%%{init: {"flowchart": {"defaultRenderer": "elk"}} }%%
```

INFO

Note that the site needs to use mermaid version 9.4+ for this to work and have this featured enabled in the lazy-loading configuration.

### Width [​](https://mermaid.js.org/syntax/flowchart.html#width)

It is possible to adjust the width of the rendered flowchart.

This is done by defining **mermaid.flowchartConfig** or by the CLI to use a JSON file with the configuration. How to use the CLI is described in the mermaidCLI page. mermaid.flowchartConfig can be set to a JSON string with config parameters or the corresponding object.

javascript

```
mermaid.flowchartConfig = {
    width: 100%
}
```

Title: Sequence diagrams | Mermaid

URL Source: https://mermaid.js.org/syntax/sequenceDiagram.html

Markdown Content:
> A Sequence diagram is an interaction diagram that shows how processes operate with one another and in what order.

Mermaid can render sequence diagrams.

INFO

A note on nodes, the word "end" could potentially break the diagram, due to the way that the mermaid language is scripted.

If unavoidable, one must use parentheses(), quotation marks "", or brackets {},\[\], to enclose the word "end". i.e : (end), \[end\], {end}.

Syntax [​](https://mermaid.js.org/syntax/sequenceDiagram.html#syntax)
---------------------------------------------------------------------

### Participants [​](https://mermaid.js.org/syntax/sequenceDiagram.html#participants)

The participants can be defined implicitly as in the first example on this page. The participants or actors are rendered in order of appearance in the diagram source text. Sometimes you might want to show the participants in a different order than how they appear in the first message. It is possible to specify the actor's order of appearance by doing the following:

### Actors [​](https://mermaid.js.org/syntax/sequenceDiagram.html#actors)

If you specifically want to use the actor symbol instead of a rectangle with text you can do so by using actor statements as per below.

### Aliases [​](https://mermaid.js.org/syntax/sequenceDiagram.html#aliases)

The actor can have a convenient identifier and a descriptive label.

### Actor Creation and Destruction (v10.3.0+) [​](https://mermaid.js.org/syntax/sequenceDiagram.html#actor-creation-and-destruction-v10-3-0)

It is possible to create and destroy actors by messages. To do so, add a create or destroy directive before the message.

```
create participant B
A --> B: Hello
```

Create directives support actor/participant distinction and aliases. The sender or the recipient of a message can be destroyed but only the recipient can be created.

#### Unfixable actor/participant creation/deletion error [​](https://mermaid.js.org/syntax/sequenceDiagram.html#unfixable-actor-participant-creation-deletion-error)

If an error of the following type occurs when creating or deleting an actor/participant:

> The destroyed participant **participant-name** does not have an associated destroying message after its declaration. Please check the sequence diagram.

And fixing diagram code does not get rid of this error and rendering of all other diagrams results in the same error, then you need to update the mermaid version to (v10.7.0+).

### Grouping / Box [​](https://mermaid.js.org/syntax/sequenceDiagram.html#grouping-box)

The actor(s) can be grouped in vertical boxes. You can define a color (if not, it will be transparent) and/or a descriptive label using the following notation:

```
box Aqua Group Description
... actors ...
end
box Group without description
... actors ...
end
box rgb(33,66,99)
... actors ...
end
box rgba(33,66,99,0.5)
... actors ...
end
```

INFO

If your group name is a color you can force the color to be transparent:

```
box transparent Aqua
... actors ...
end
```

Messages [​](https://mermaid.js.org/syntax/sequenceDiagram.html#messages)
-------------------------------------------------------------------------

Messages can be of two displayed either solid or with a dotted line.

```
[Actor][Arrow][Actor]:Message text
```

There are ten types of arrows currently supported:

| Type | Description |
| --- | --- |
| `->` | Solid line without arrow |
| `-->` | Dotted line without arrow |
| `->>` | Solid line with arrowhead |
| `-->>` | Dotted line with arrowhead |
| `<<->>` | Solid line with bidirectional arrowheads (v11.0.0+) |
| `<<-->>` | Dotted line with bidirectional arrowheads (v11.0.0+) |
| `-x` | Solid line with a cross at the end |
| `--x` | Dotted line with a cross at the end |
| `-)` | Solid line with an open arrow at the end (async) |
| `--)` | Dotted line with a open arrow at the end (async) |

Activations [​](https://mermaid.js.org/syntax/sequenceDiagram.html#activations)
-------------------------------------------------------------------------------

It is possible to activate and deactivate an actor. (de)activation can be dedicated declarations:

There is also a shortcut notation by appending `+`/`-` suffix to the message arrow:

Activations can be stacked for same actor:

Notes [​](https://mermaid.js.org/syntax/sequenceDiagram.html#notes)
-------------------------------------------------------------------

It is possible to add notes to a sequence diagram. This is done by the notation Note \[ right of | left of | over \] \[Actor\]: Text in note content

See the example below:

It is also possible to create notes spanning two participants:

Line breaks [​](https://mermaid.js.org/syntax/sequenceDiagram.html#line-breaks)
-------------------------------------------------------------------------------

Line break can be added to Note and Message:

Line breaks in Actor names requires aliases:

Loops [​](https://mermaid.js.org/syntax/sequenceDiagram.html#loops)
-------------------------------------------------------------------

It is possible to express loops in a sequence diagram. This is done by the notation

```
loop Loop text
... statements ...
end
```

See the example below:

Alt [​](https://mermaid.js.org/syntax/sequenceDiagram.html#alt)
---------------------------------------------------------------

It is possible to express alternative paths in a sequence diagram. This is done by the notation

```
alt Describing text
... statements ...
else
... statements ...
end
```

or if there is sequence that is optional (if without else).

```
opt Describing text
... statements ...
end
```

See the example below:

Parallel [​](https://mermaid.js.org/syntax/sequenceDiagram.html#parallel)
-------------------------------------------------------------------------

It is possible to show actions that are happening in parallel.

This is done by the notation

```
par [Action 1]
... statements ...
and [Action 2]
... statements ...
and [Action N]
... statements ...
end
```

See the example below:

It is also possible to nest parallel blocks.

Critical Region [​](https://mermaid.js.org/syntax/sequenceDiagram.html#critical-region)
---------------------------------------------------------------------------------------

It is possible to show actions that must happen automatically with conditional handling of circumstances.

This is done by the notation

```
critical [Action that must be performed]
... statements ...
option [Circumstance A]
... statements ...
option [Circumstance B]
... statements ...
end
```

See the example below:

It is also possible to have no options at all

This critical block can also be nested, equivalently to the `par` statement as seen above.

Break [​](https://mermaid.js.org/syntax/sequenceDiagram.html#break)
-------------------------------------------------------------------

It is possible to indicate a stop of the sequence within the flow (usually used to model exceptions).

This is done by the notation

```
break [something happened]
... statements ...
end
```

See the example below:

Background Highlighting [​](https://mermaid.js.org/syntax/sequenceDiagram.html#background-highlighting)
-------------------------------------------------------------------------------------------------------

It is possible to highlight flows by providing colored background rects. This is done by the notation

```
rect COLOR
... content ...
end
```

The colors are defined using rgb and rgba syntax.

```
rect rgb(0, 255, 0)
... content ...
end
```

```
rect rgba(0, 0, 255, .1)
... content ...
end
```

See the examples below:

Comments can be entered within a sequence diagram, which will be ignored by the parser. Comments need to be on their own line, and must be prefaced with `%%` (double percent signs). Any text after the start of the comment to the next newline will be treated as a comment, including any diagram syntax

Entity codes to escape characters [​](https://mermaid.js.org/syntax/sequenceDiagram.html#entity-codes-to-escape-characters)
---------------------------------------------------------------------------------------------------------------------------

It is possible to escape characters using the syntax exemplified here.

Numbers given are base 10, so `#` can be encoded as `#35;`. It is also supported to use HTML character names.

Because semicolons can be used instead of line breaks to define the markup, you need to use `#59;` to include a semicolon in message text.

sequenceNumbers [​](https://mermaid.js.org/syntax/sequenceDiagram.html#sequencenumbers)
---------------------------------------------------------------------------------------

It is possible to get a sequence number attached to each arrow in a sequence diagram. This can be configured when adding mermaid to the website as shown below:

html

```
<script>
  mermaid.initialize({ sequence: { showSequenceNumbers: true } });
</script>
```

It can also be turned on via the diagram code as in the diagram:

Actor Menus [​](https://mermaid.js.org/syntax/sequenceDiagram.html#actor-menus)
-------------------------------------------------------------------------------

Actors can have popup-menus containing individualized links to external pages. For example, if an actor represented a web service, useful links might include a link to the service health dashboard, repo containing the code for the service, or a wiki page describing the service.

This can be configured by adding one or more link lines with the format:

```
link <actor>: <link-label> @ <link-url>
```

#### Advanced Menu Syntax [​](https://mermaid.js.org/syntax/sequenceDiagram.html#advanced-menu-syntax)

There is an advanced syntax that relies on JSON formatting. If you are comfortable with JSON format, then this exists as well.

This can be configured by adding the links lines with the format:

```
links <actor>: <json-formatted link-name link-url pairs>
```

An example is below:

Styling [​](https://mermaid.js.org/syntax/sequenceDiagram.html#styling)
-----------------------------------------------------------------------

Styling of a sequence diagram is done by defining a number of css classes. During rendering these classes are extracted from the file located at src/themes/sequence.scss

### Classes used [​](https://mermaid.js.org/syntax/sequenceDiagram.html#classes-used)

| Class | Description |
| --- | --- |
| actor | Styles for the actor box. |
| actor-top | Styles for the actor figure/ box at the top of the diagram. |
| actor-bottom | Styles for the actor figure/ box at the bottom of the diagram. |
| text.actor | Styles for text of all of the actors. |
| text.actor-box | Styles for text of the actor box. |
| text.actor-man | Styles for text of the actor figure. |
| actor-line | The vertical line for an actor. |
| messageLine0 | Styles for the solid message line. |
| messageLine1 | Styles for the dotted message line. |
| messageText | Defines styles for the text on the message arrows. |
| labelBox | Defines styles label to left in a loop. |
| labelText | Styles for the text in label for loops. |
| loopText | Styles for the text in the loop box. |
| loopLine | Defines styles for the lines in the loop box. |
| note | Styles for the note box. |
| noteText | Styles for the text on in the note boxes. |

### Sample stylesheet [​](https://mermaid.js.org/syntax/sequenceDiagram.html#sample-stylesheet)

css

```
body {
  background: white;
}

.actor {
  stroke: #ccccff;
  fill: #ececff;
}
text.actor {
  fill: black;
  stroke: none;
  font-family: Helvetica;
}

.actor-line {
  stroke: grey;
}

.messageLine0 {
  stroke-width: 1.5;
  stroke-dasharray: '2 2';
  marker-end: 'url(#arrowhead)';
  stroke: black;
}

.messageLine1 {
  stroke-width: 1.5;
  stroke-dasharray: '2 2';
  stroke: black;
}

#arrowhead {
  fill: black;
}

.messageText {
  fill: black;
  stroke: none;
  font-family: 'trebuchet ms', verdana, arial;
  font-size: 14px;
}

.labelBox {
  stroke: #ccccff;
  fill: #ececff;
}

.labelText {
  fill: black;
  stroke: none;
  font-family: 'trebuchet ms', verdana, arial;
}

.loopText {
  fill: black;
  stroke: none;
  font-family: 'trebuchet ms', verdana, arial;
}

.loopLine {
  stroke-width: 2;
  stroke-dasharray: '2 2';
  marker-end: 'url(#arrowhead)';
  stroke: #ccccff;
}

.note {
  stroke: #decc93;
  fill: #fff5ad;
}

.noteText {
  fill: black;
  stroke: none;
  font-family: 'trebuchet ms', verdana, arial;
  font-size: 14px;
}
```

Configuration [​](https://mermaid.js.org/syntax/sequenceDiagram.html#configuration)
-----------------------------------------------------------------------------------

It is possible to adjust the margins for rendering the sequence diagram.

This is done by defining `mermaid.sequenceConfig` or by the CLI to use a json file with the configuration. How to use the CLI is described in the [mermaidCLI](https://mermaid.js.org/config/mermaidCLI.html) page. `mermaid.sequenceConfig` can be set to a JSON string with config parameters or the corresponding object.

javascript

```
mermaid.sequenceConfig = {
  diagramMarginX: 50,
  diagramMarginY: 10,
  boxTextMargin: 5,
  noteMargin: 10,
  messageMargin: 35,
  mirrorActors: true,
};
```

### Possible configuration parameters: [​](https://mermaid.js.org/syntax/sequenceDiagram.html#possible-configuration-parameters)

| Parameter | Description | Default value |
| --- | --- | --- |
| mirrorActors | Turns on/off the rendering of actors below the diagram as well as above it | false |
| bottomMarginAdj | Adjusts how far down the graph ended. Wide borders styles with css could generate unwanted clipping which is why this config param exists. | 1 |
| actorFontSize | Sets the font size for the actor's description | 14 |
| actorFontFamily | Sets the font family for the actor's description | "Open Sans", sans-serif |
| actorFontWeight | Sets the font weight for the actor's description | "Open Sans", sans-serif |
| noteFontSize | Sets the font size for actor-attached notes | 14 |
| noteFontFamily | Sets the font family for actor-attached notes | "trebuchet ms", verdana, arial |
| noteFontWeight | Sets the font weight for actor-attached notes | "trebuchet ms", verdana, arial |
| noteAlign | Sets the text alignment for text in actor-attached notes | center |
| messageFontSize | Sets the font size for actor<\-\>actor messages | 16 |
| messageFontFamily | Sets the font family for actor<\-\>actor messages | "trebuchet ms", verdana, arial |
| messageFontWeight | Sets the font weight for actor<\-\>actor messages | "trebuchet ms", verdana, arial |


Title: User Journey Diagram | Mermaid

URL Source: https://mermaid.js.org/syntax/userJourney.html

Markdown Content:
User Journey Diagram | Mermaid
===============

[Skip to content](https://mermaid.js.org/syntax/userJourney.html#VPContent)

[Mermaid](https://mermaid.js.org/)

SearchMetaK

Main Navigation[Docs](https://mermaid.js.org/intro/)[Tutorials](https://mermaid.js.org/ecosystem/tutorials.html)[Integrations](https://mermaid.js.org/ecosystem/integrations-community.html)[Contributing](https://mermaid.js.org/community/intro.html)[Latest News](https://mermaid.js.org/news/announcements.html)

11.4.1

[Changelog](https://github.com/mermaid-js/mermaid/releases)

[💻 Live Editor](https://mermaid.live/)

[](https://github.com/mermaid-js/mermaid)[](https://discord.gg/sKeNQX4Wtj)[](https://www.mermaidchart.com/)

Appearance

[](https://github.com/mermaid-js/mermaid)[](https://discord.gg/sKeNQX4Wtj)[](https://www.mermaidchart.com/)

Menu

Return to top

Sidebar Navigation

📔 Introduction
---------------

[About Mermaid](https://mermaid.js.org/intro/)

[Getting Started](https://mermaid.js.org/intro/getting-started.html)

[Syntax and Configuration](https://mermaid.js.org/intro/syntax-reference.html)

📊 Diagram Syntax
-----------------

[Flowchart](https://mermaid.js.org/syntax/flowchart.html)

[Sequence Diagram](https://mermaid.js.org/syntax/sequenceDiagram.html)

[Class Diagram](https://mermaid.js.org/syntax/classDiagram.html)

[State Diagram](https://mermaid.js.org/syntax/stateDiagram.html)

[Entity Relationship Diagram](https://mermaid.js.org/syntax/entityRelationshipDiagram.html)

[User Journey](https://mermaid.js.org/syntax/userJourney.html)

[Gantt](https://mermaid.js.org/syntax/gantt.html)

[Pie Chart](https://mermaid.js.org/syntax/pie.html)

[Quadrant Chart](https://mermaid.js.org/syntax/quadrantChart.html)

[Requirement Diagram](https://mermaid.js.org/syntax/requirementDiagram.html)

[Gitgraph (Git) Diagram](https://mermaid.js.org/syntax/gitgraph.html)

[C4 Diagram 🦺⚠️](https://mermaid.js.org/syntax/c4.html)

[Mindmaps](https://mermaid.js.org/syntax/mindmap.html)

[Timeline](https://mermaid.js.org/syntax/timeline.html)

[ZenUML](https://mermaid.js.org/syntax/zenuml.html)

[Sankey 🔥](https://mermaid.js.org/syntax/sankey.html)

[XY Chart 🔥](https://mermaid.js.org/syntax/xyChart.html)

[Block Diagram 🔥](https://mermaid.js.org/syntax/block.html)

[Packet 🔥](https://mermaid.js.org/syntax/packet.html)

[Kanban 🔥](https://mermaid.js.org/syntax/kanban.html)

[Architecture 🔥](https://mermaid.js.org/syntax/architecture.html)

[Other Examples](https://mermaid.js.org/syntax/examples.html)

📚 Ecosystem
------------

[Mermaid Chart](https://mermaid.js.org/ecosystem/mermaid-chart.html)

[Tutorials](https://mermaid.js.org/ecosystem/tutorials.html)

[Integrations - Community](https://mermaid.js.org/ecosystem/integrations-community.html)

[Integrations - Create](https://mermaid.js.org/ecosystem/integrations-create.html)

⚙️ Deployment and Configuration
-------------------------------

[Configuration](https://mermaid.js.org/config/configuration.html)

[API-Usage](https://mermaid.js.org/config/usage.html)

[Mermaid API Configuration](https://mermaid.js.org/config/setup/README.html)

[Mermaid Configuration Options](https://mermaid.js.org/config/schema-docs/config.html)

[Registering icons](https://mermaid.js.org/config/icons.html)

[Directives](https://mermaid.js.org/config/directives.html)

[Theming](https://mermaid.js.org/config/theming.html)

[Math](https://mermaid.js.org/config/math.html)

[Accessibility](https://mermaid.js.org/config/accessibility.html)

[Mermaid CLI](https://mermaid.js.org/config/mermaidCLI.html)

[FAQ](https://mermaid.js.org/config/faq.html)

🙌 Contributing
---------------

[Getting Started](https://mermaid.js.org/community/intro.html)

[Contributing to Mermaid](https://mermaid.js.org/community/contributing.html)

[Adding Diagrams](https://mermaid.js.org/community/new-diagram.html)

[Questions and Suggestions](https://mermaid.js.org/community/questions-and-suggestions.html)

[Security](https://mermaid.js.org/community/security.html)

📰 Latest News
--------------

[Announcements](https://mermaid.js.org/news/announcements.html)

[Blog](https://mermaid.js.org/news/blog.html)

On this page

[Replace ChatGPT Pro, Mermaid.live, and Lucid Chart with Mermaid Chart Try now](https://www.mermaidchart.com/play?utm_source=mermaid_js&utm_medium=banner_ad&utm_campaign=AIbundle_B)

User Journey Diagram [​](https://mermaid.js.org/syntax/userJourney.html#user-journey-diagram)
=============================================================================================

> User journeys describe at a high level of detail exactly what steps different users take to complete a specific task within a system, application or website. This technique shows the current (as-is) user workflow, and reveals areas of improvement for the to-be workflow. (Wikipedia)

Mermaid can render user journey diagrams:

Each user journey is split into sections, these describe the part of the task the user is trying to complete.

Tasks syntax is `Task name: <score>: <comma separated list of actors>`

[Edit this page on GitHub](https://github.com/mermaid-js/mermaid/edit/develop/packages/mermaid/src/docs/syntax/userJourney.md)

Pager

[Previous pageEntity Relationship Diagram](https://mermaid.js.org/syntax/entityRelationshipDiagram.html)

[Next pageGantt](https://mermaid.js.org/syntax/gantt.html)
Title: Quadrant Chart | Mermaid

URL Source: https://mermaid.js.org/syntax/quadrantChart.html

Markdown Content:
> A quadrant chart is a visual representation of data that is divided into four quadrants. It is used to plot data points on a two-dimensional grid, with one variable represented on the x-axis and another variable represented on the y-axis. The quadrants are determined by dividing the chart into four equal parts based on a set of criteria that is specific to the data being analyzed. Quadrant charts are often used to identify patterns and trends in data, and to prioritize actions based on the position of data points within the chart. They are commonly used in business, marketing, and risk management, among other fields.

Example [​](https://mermaid.js.org/syntax/quadrantChart.html#example)
---------------------------------------------------------------------

Syntax [​](https://mermaid.js.org/syntax/quadrantChart.html#syntax)
-------------------------------------------------------------------

INFO

If there are no points available in the chart both **axis** text and **quadrant** will be rendered in the center of the respective quadrant. If there are points **x-axis** labels will rendered from the left of the respective quadrant also they will be displayed at the bottom of the chart, and **y-axis** labels will be rendered at the bottom of the respective quadrant, the quadrant text will render at the top of the respective quadrant.

INFO

For points x and y value min value is 0 and max value is 1.

### Title [​](https://mermaid.js.org/syntax/quadrantChart.html#title)

The title is a short description of the chart and it will always render on top of the chart.

#### Example [​](https://mermaid.js.org/syntax/quadrantChart.html#example-1)

```
quadrantChart
    title This is a sample example
```

### x-axis [​](https://mermaid.js.org/syntax/quadrantChart.html#x-axis)

The x-axis determines what text would be displayed in the x-axis. In x-axis there is two part **left** and **right** you can pass **both** or you can pass only **left**. The statement should start with `x-axis` then the `left axis text` followed by the delimiter `-->` then `right axis text`.

#### Example [​](https://mermaid.js.org/syntax/quadrantChart.html#example-2)

1.  `x-axis <text> --> <text>` both the left and right axis text will be rendered.
2.  `x-axis <text>` only the left axis text will be rendered.

### y-axis [​](https://mermaid.js.org/syntax/quadrantChart.html#y-axis)

The y-axis determines what text would be displayed in the y-axis. In y-axis there is two part **top** and **bottom** you can pass **both** or you can pass only **bottom**. The statement should start with `y-axis` then the `bottom axis text` followed by the delimiter `-->` then `top axis text`.

#### Example [​](https://mermaid.js.org/syntax/quadrantChart.html#example-3)

1.  `y-axis <text> --> <text>` both the bottom and top axis text will be rendered.
2.  `y-axis <text>` only the bottom axis text will be rendered.

### Quadrants text [​](https://mermaid.js.org/syntax/quadrantChart.html#quadrants-text)

The `quadrant-[1,2,3,4]` determine what text would be displayed inside the quadrants.

#### Example [​](https://mermaid.js.org/syntax/quadrantChart.html#example-4)

1.  `quadrant-1 <text>` determine what text will be rendered inside the top right quadrant.
2.  `quadrant-2 <text>` determine what text will be rendered inside the top left quadrant.
3.  `quadrant-3 <text>` determine what text will be rendered inside the bottom left quadrant.
4.  `quadrant-4 <text>` determine what text will be rendered inside the bottom right quadrant.

### Points [​](https://mermaid.js.org/syntax/quadrantChart.html#points)

Points are used to plot a circle inside the quadrantChart. The syntax is `<text>: [x, y]` here x and y value is in the range 0 - 1.

#### Example [​](https://mermaid.js.org/syntax/quadrantChart.html#example-5)

1.  `Point 1: [0.75, 0.80]` here the Point 1 will be drawn in the top right quadrant.
2.  `Point 2: [0.35, 0.24]` here the Point 2 will be drawn in the bottom left quadrant.

Chart Configurations [​](https://mermaid.js.org/syntax/quadrantChart.html#chart-configurations)
-----------------------------------------------------------------------------------------------

| Parameter | Description | Default value |
| --- | --- | --- |
| chartWidth | Width of the chart | 500 |
| chartHeight | Height of the chart | 500 |
| titlePadding | Top and Bottom padding of the title | 10 |
| titleFontSize | Title font size | 20 |
| quadrantPadding | Padding outside all the quadrants | 5 |
| quadrantTextTopPadding | Quadrant text top padding when text is drawn on top ( not data points are there) | 5 |
| quadrantLabelFontSize | Quadrant text font size | 16 |
| quadrantInternalBorderStrokeWidth | Border stroke width inside the quadrants | 1 |
| quadrantExternalBorderStrokeWidth | Quadrant external border stroke width | 2 |
| xAxisLabelPadding | Top and bottom padding of x-axis text | 5 |
| xAxisLabelFontSize | X-axis texts font size | 16 |
| xAxisPosition | Position of x-axis (top , bottom) if there are points the x-axis will always be rendered in bottom | 'top' |
| yAxisLabelPadding | Left and Right padding of y-axis text | 5 |
| yAxisLabelFontSize | Y-axis texts font size | 16 |
| yAxisPosition | Position of y-axis (left , right) | 'left' |
| pointTextPadding | Padding between point and the below text | 5 |
| pointLabelFontSize | Point text font size | 12 |
| pointRadius | Radius of the point to be drawn | 5 |

Chart Theme Variables [​](https://mermaid.js.org/syntax/quadrantChart.html#chart-theme-variables)
-------------------------------------------------------------------------------------------------

| Parameter | Description |
| --- | --- |
| quadrant1Fill | Fill color of the top right quadrant |
| quadrant2Fill | Fill color of the top left quadrant |
| quadrant3Fill | Fill color of the bottom left quadrant |
| quadrant4Fill | Fill color of the bottom right quadrant |
| quadrant1TextFill | Text color of the top right quadrant |
| quadrant2TextFill | Text color of the top left quadrant |
| quadrant3TextFill | Text color of the bottom left quadrant |
| quadrant4TextFill | Text color of the bottom right quadrant |
| quadrantPointFill | Points fill color |
| quadrantPointTextFill | Points text color |
| quadrantXAxisTextFill | X-axis text color |
| quadrantYAxisTextFill | Y-axis text color |
| quadrantInternalBorderStrokeFill | Quadrants inner border color |
| quadrantExternalBorderStrokeFill | Quadrants outer border color |
| quadrantTitleFill | Title color |

Example on config and theme [​](https://mermaid.js.org/syntax/quadrantChart.html#example-on-config-and-theme)
-------------------------------------------------------------------------------------------------------------

### Point styling [​](https://mermaid.js.org/syntax/quadrantChart.html#point-styling)

Points can either be styled directly or with defined shared classes

1.  Direct styling

md

```
Point A: [0.9, 0.0] radius: 12
Point B: [0.8, 0.1] color: #ff3300, radius: 10
Point C: [0.7, 0.2] radius: 25, color: #00ff33, stroke-color: #10f0f0
Point D: [0.6, 0.3] radius: 15, stroke-color: #00ff0f, stroke-width: 5px ,color: #ff33f0
```

2.  Classes styling

md

```
Point A:::class1: [0.9, 0.0]
Point B:::class2: [0.8, 0.1]
Point C:::class3: [0.7, 0.2]
Point D:::class3: [0.7, 0.2]
classDef class1 color: #109060
classDef class2 color: #908342, radius : 10, stroke-color: #310085, stroke-width: 10px
classDef class3 color: #f00fff, radius : 10
```

#### Available styles: [​](https://mermaid.js.org/syntax/quadrantChart.html#available-styles)

| Parameter | Description |
| --- | --- |
| color | Fill color of the point |
| radius | Radius of the point |
| stroke-width | Border width of the point |
| stroke-color | Border color of the point (useless when stroke-width is not specified) |

INFO

Order of preference:

1.  Direct styles
2.  Class styles
3.  Theme styles

Example on styling [​](https://mermaid.js.org/syntax/quadrantChart.html#example-on-styling)
-------------------------------------------------------------------------------------------

Title: Mindmap | Mermaid

URL Source: https://mermaid.js.org/syntax/mindmap.html

Markdown Content:
> Mindmap: This is an experimental diagram for now. The syntax and properties can change in future releases. The syntax is stable except for the icon integration which is the experimental part.

"A mind map is a diagram used to visually organize information into a hierarchy, showing relationships among pieces of the whole. It is often created around a single concept, drawn as an image in the center of a blank page, to which associated representations of ideas such as images, words and parts of words are added. Major ideas are connected directly to the central concept, and other ideas branch out from those major ideas." Wikipedia

### An example of a mindmap. [​](https://mermaid.js.org/syntax/mindmap.html#an-example-of-a-mindmap)

Syntax [​](https://mermaid.js.org/syntax/mindmap.html#syntax)
-------------------------------------------------------------

The syntax for creating Mindmaps is simple and relies on indentation for setting the levels in the hierarchy.

In the following example you can see how there are 3 different levels. One with starting at the left of the text and another level with two rows starting at the same column, defining the node A. At the end there is one more level where the text is indented further than the previous lines defining the nodes B and C.

In summary is a simple text outline where there is one node at the root level called `Root` which has one child `A`. `A` in turn has two children `B`and `C`. In the diagram below we can see this rendered as a mindmap.

In this way we can use a text outline to generate a hierarchical mindmap.

Different shapes [​](https://mermaid.js.org/syntax/mindmap.html#different-shapes)
---------------------------------------------------------------------------------

Mermaid mindmaps can show nodes using different shapes. When specifying a shape for a node the syntax is similar to flowchart nodes, with an id followed by the shape definition and with the text within the shape delimiters. Where possible we try/will try to keep the same shapes as for flowcharts, even though they are not all supported from the start.

Mindmap can show the following shapes:

### Square [​](https://mermaid.js.org/syntax/mindmap.html#square)

### Rounded square [​](https://mermaid.js.org/syntax/mindmap.html#rounded-square)

### Circle [​](https://mermaid.js.org/syntax/mindmap.html#circle)

### Bang [​](https://mermaid.js.org/syntax/mindmap.html#bang)

### Cloud [​](https://mermaid.js.org/syntax/mindmap.html#cloud)

### Hexagon [​](https://mermaid.js.org/syntax/mindmap.html#hexagon)

### Default [​](https://mermaid.js.org/syntax/mindmap.html#default)

More shapes will be added, beginning with the shapes available in flowcharts.

Icons and classes [​](https://mermaid.js.org/syntax/mindmap.html#icons-and-classes)
-----------------------------------------------------------------------------------

Icons [​](https://mermaid.js.org/syntax/mindmap.html#icons)
-----------------------------------------------------------

As with flowcharts you can add icons to your nodes but with an updated syntax. The styling for the font based icons are added during the integration so that they are available for the web page. _This is not something a diagram author can do but has to be done with the site administrator or the integrator_. Once the icon fonts are in place you add them to the mind map nodes using the `::icon()` syntax. You place the classes for the icon within the parenthesis like in the following example where icons for material design and [Font Awesome 5](https://fontawesome.com/v5/search?o=r&m=free) are displayed. The intention is that this approach should be used for all diagrams supporting icons. **Experimental feature:** This wider scope is also the reason Mindmaps are experimental as this syntax and approach could change.

Classes [​](https://mermaid.js.org/syntax/mindmap.html#classes)
---------------------------------------------------------------

Again the syntax for adding classes is similar to flowcharts. You can add classes using a triple colon following a number of css classes separated by space. In the following example one of the nodes has two custom classes attached urgent turning the background red and the text white and large increasing the font size:

_These classes need to be supplied by the site administrator._

Unclear indentation [​](https://mermaid.js.org/syntax/mindmap.html#unclear-indentation)
---------------------------------------------------------------------------------------

The actual indentation does not really matter only compared with the previous rows. If we take the previous example and disrupt it a little we can see how the calculations are performed. Let us start with placing C with a smaller indentation than `B` but larger then `A`.

This outline is unclear as `B` clearly is a child of `A` but when we move on to `C` the clarity is lost. `C` is not a child of `B` with a higher indentation nor does it have the same indentation as `B`. The only thing that is clear is that the first node with smaller indentation, indicating a parent, is A. Then Mermaid relies on this known truth and compensates for the unclear indentation and selects `A` as a parent of `C` leading till the same diagram with `B` and `C` as siblings.

Markdown Strings [​](https://mermaid.js.org/syntax/mindmap.html#markdown-strings)
---------------------------------------------------------------------------------

The "Markdown Strings" feature enhances mind maps by offering a more versatile string type, which supports text formatting options such as bold and italics, and automatically wraps text within labels.

Formatting:

*   For bold text, use double asterisks \*\* before and after the text.
*   For italics, use single asterisks \* before and after the text.
*   With traditional strings, you needed to add
    tags for text to wrap in nodes. However, markdown strings automatically wrap text when it becomes too long and allows you to start a new line by simply using a newline character instead of a
    tag.

Integrating with your library/website. [​](https://mermaid.js.org/syntax/mindmap.html#integrating-with-your-library-website)
----------------------------------------------------------------------------------------------------------------------------

Mindmap uses the experimental lazy loading & async rendering features which could change in the future. From version 9.4.0 this diagram is included in mermaid but use lazy loading in order to keep the size of mermaid down. This is important in order to be able to add additional diagrams going forward.

You can still use the pre 9.4.0 method to add mermaid with mindmaps to a web page:

html

```
<script type="module">
  import mermaid from 'https://cdn.jsdelivr.net/npm/mermaid@9.3.0/dist/mermaid.esm.min.mjs';
  import mindmap from 'https://cdn.jsdelivr.net/npm/@mermaid-js/mermaid-mindmap@9.3.0/dist/mermaid-mindmap.esm.min.mjs';
  await mermaid.registerExternalDiagrams([mindmap]);
</script>
```

From version 9.4.0 you can simplify this code to:

html

```
<script type="module">
  import mermaid from 'https://cdn.jsdelivr.net/npm/mermaid@11/dist/mermaid.esm.min.mjs';
</script>
```

You can also refer the implementation in the live editor [here](https://github.com/mermaid-js/mermaid-live-editor/blob/develop/src/lib/util/mermaid.ts) to see how the async loading is done.

Title: Timeline Diagram | Mermaid

URL Source: https://mermaid.js.org/syntax/timeline.html

Markdown Content:
> Timeline: This is an experimental diagram for now. The syntax and properties can change in future releases. The syntax is stable except for the icon integration which is the experimental part.

"A timeline is a type of diagram used to illustrate a chronology of events, dates, or periods of time. It is usually presented graphically to indicate the passing of time, and it is usually organized chronologically. A basic timeline presents a list of events in chronological order, usually using dates as markers. A timeline can also be used to show the relationship between events, such as the relationship between the events of a person's life" [(Wikipedia)](https://en.wikipedia.org/wiki/Timeline).

### An example of a timeline [​](https://mermaid.js.org/syntax/timeline.html#an-example-of-a-timeline)

Syntax [​](https://mermaid.js.org/syntax/timeline.html#syntax)
--------------------------------------------------------------

The syntax for creating Timeline diagram is simple. You always start with the `timeline` keyword to let mermaid know that you want to create a timeline diagram.

After that there is a possibility to add a title to the timeline. This is done by adding a line with the keyword `title` followed by the title text.

Then you add the timeline data, where you always start with a time period, followed by a colon and then the text for the event. Optionally you can add a second colon and then the text for the event. So, you can have one or more events per time period.

json

```
{time period} : {event}
```

or

json

```
{time period} : {event} : {event}
```

or

json

```
{time period} : {event}
              : {event}
              : {event}
```

**NOTE**: Both time period and event are simple text, and not limited to numbers.

Let us look at the syntax for the example above.

In this way we can use a text outline to generate a timeline diagram. The sequence of time period and events is important, as it will be used to draw the timeline. The first time period will be placed at the left side of the timeline, and the last time period will be placed at the right side of the timeline.

Similarly, the first event will be placed at the top for that specific time period, and the last event will be placed at the bottom.

Grouping of time periods in sections/ages [​](https://mermaid.js.org/syntax/timeline.html#grouping-of-time-periods-in-sections-ages)
------------------------------------------------------------------------------------------------------------------------------------

You can group time periods in sections/ages. This is done by adding a line with the keyword `section` followed by the section name.

All subsequent time periods will be placed in this section until a new section is defined.

If no section is defined, all time periods will be placed in the default section.

Let us look at an example, where we have grouped the time periods in sections.

As you can see, the time periods are placed in the sections, and the sections are placed in the order they are defined.

All time periods and events under a given section follow a similar color scheme. This is done to make it easier to see the relationship between time periods and events.

Wrapping of text for long time-periods or events [​](https://mermaid.js.org/syntax/timeline.html#wrapping-of-text-for-long-time-periods-or-events)
--------------------------------------------------------------------------------------------------------------------------------------------------

By default, the text for time-periods and events will be wrapped if it is too long. This is done to avoid that the text is drawn outside the diagram.

You can also use `<br>` to force a line break.

Let us look at another example, where we have a long time period, and a long event.

Styling of time periods and events [​](https://mermaid.js.org/syntax/timeline.html#styling-of-time-periods-and-events)
----------------------------------------------------------------------------------------------------------------------

As explained earlier, each section has a color scheme, and each time period and event under a section follow the similar color scheme.

However, if there is no section defined, then we have two possibilities:

1.  Style time periods individually, i.e. each time period(and its corresponding events) will have its own color scheme. This is the DEFAULT behavior.

**NOTE**: that there are no sections defined, and each time period and its corresponding events will have its own color scheme.

2.  Disable the multiColor option using the `disableMultiColor` option. This will make all time periods and events follow the same color scheme.

You will need to add this option either via mermaid.initialize function or directives.

javascript

```
mermaid.initialize({
        theme: 'base',
        startOnLoad: true,
        logLevel: 0,
        timeline: {
          disableMulticolor: false,
        },
        ...
        ...
```

let us look at same example, where we have disabled the multiColor option.

### Customizing Color scheme [​](https://mermaid.js.org/syntax/timeline.html#customizing-color-scheme)

You can customize the color scheme using the `cScale0` to `cScale11` theme variables, which will change the background colors. Mermaid allows you to set unique colors for up-to 12 sections, where `cScale0` variable will drive the value of the first section or time-period, `cScale1` will drive the value of the second section and so on. In case you have more than 12 sections, the color scheme will start to repeat.

If you also want to change the foreground color of a section, you can do so use theme variables corresponding `cScaleLabel0` to `cScaleLabel11` variables.

**NOTE**: Default values for these theme variables are picked from the selected theme. If you want to override the default values, you can use the `initialize` call to add your custom theme variable values.

Example:

Now let's override the default values for the `cScale0` to `cScale2` variables:

See how the colors are changed to the values specified in the theme variables.

Themes [​](https://mermaid.js.org/syntax/timeline.html#themes)
--------------------------------------------------------------

Mermaid supports a bunch of pre-defined themes which you can use to find the right one for you. PS: you can actually override an existing theme's variable to get your own custom theme going. Learn more about theming your diagram [here](https://mermaid.js.org/config/theming.html).

The following are the different pre-defined theme options:

*   `base`
*   `forest`
*   `dark`
*   `default`
*   `neutral`

**NOTE**: To change theme you can either use the `initialize` call or _directives_. Learn more about [directives](https://mermaid.js.org/config/directives.html) Let's put them to use, and see how our sample diagram looks in different themes:

### Base Theme [​](https://mermaid.js.org/syntax/timeline.html#base-theme)

### Forest Theme [​](https://mermaid.js.org/syntax/timeline.html#forest-theme)

### Dark Theme [​](https://mermaid.js.org/syntax/timeline.html#dark-theme)

### Default Theme [​](https://mermaid.js.org/syntax/timeline.html#default-theme)

### Neutral Theme [​](https://mermaid.js.org/syntax/timeline.html#neutral-theme)

Integrating with your library/website [​](https://mermaid.js.org/syntax/timeline.html#integrating-with-your-library-website)
----------------------------------------------------------------------------------------------------------------------------

Timeline uses experimental lazy loading & async rendering features which could change in the future.The lazy loading is important in order to be able to add additional diagrams going forward.

You can use this method to add mermaid including the timeline diagram to a web page:

html

```
<script type="module">
  import mermaid from 'https://cdn.jsdelivr.net/npm/mermaid@11/dist/mermaid.esm.min.mjs';
</script>
```

You can also refer the implementation in the live editor [here](https://github.com/mermaid-js/mermaid-live-editor/blob/develop/src/lib/util/mermaid.ts) to see how the async loading is done.
Title: Sankey diagram (v10.3.0+) | Mermaid

URL Source: https://mermaid.js.org/syntax/sankey.html

Markdown Content:
> A sankey diagram is a visualization used to depict a flow from one set of values to another.

WARNING

This is an experimental diagram. Its syntax are very close to plain CSV, but it is to be extended in the nearest future.

The things being connected are called nodes and the connections are called links.

Example [​](https://mermaid.js.org/syntax/sankey.html#example)
--------------------------------------------------------------

This example taken from [observable](https://observablehq.com/@d3/sankey/2?collection=@d3/d3-sankey). It may be rendered a little bit differently, though, in terms of size and colors.

Syntax [​](https://mermaid.js.org/syntax/sankey.html#syntax)
------------------------------------------------------------

The idea behind syntax is that a user types `sankey-beta` keyword first, then pastes raw CSV below and get the result.

It implements CSV standard as [described here](https://www.ietf.org/rfc/rfc4180.txt) with subtle **differences**:

*   CSV must contain **3 columns only**
*   It is **allowed** to have **empty lines** without comma separators for visual purposes

### Basic [​](https://mermaid.js.org/syntax/sankey.html#basic)

It is implied that 3 columns inside CSV should represent `source`, `target` and `value` accordingly:

### Empty Lines [​](https://mermaid.js.org/syntax/sankey.html#empty-lines)

CSV does not support empty lines without comma delimiters by default. But you can add them if needed:

### Commas [​](https://mermaid.js.org/syntax/sankey.html#commas)

If you need to have a comma, wrap it in double quotes:

### Double Quotes [​](https://mermaid.js.org/syntax/sankey.html#double-quotes)

If you need to have double quote, put a pair of them inside quoted string:

Configuration [​](https://mermaid.js.org/syntax/sankey.html#configuration)
--------------------------------------------------------------------------

You can customize link colors, node alignments and diagram dimensions.

html

```
<script>
  const config = {
    startOnLoad: true,
    securityLevel: 'loose',
    sankey: {
      width: 800,
      height: 400,
      linkColor: 'source',
      nodeAlignment: 'left',
    },
  };
  mermaid.initialize(config);
</script>
```

### Links Coloring [​](https://mermaid.js.org/syntax/sankey.html#links-coloring)

You can adjust links' color by setting `linkColor` to one of those:

*   `source` - link will be of a source node color
*   `target` - link will be of a target node color
*   `gradient` - link color will be smoothly transient between source and target node colors
*   hex code of color, like `#a1a1a1`

### Node Alignment [​](https://mermaid.js.org/syntax/sankey.html#node-alignment)

Graph layout can be changed by setting `nodeAlignment` to:

*   `justify`
*   `center`
*   `left`
*   `right`
Title: XY Chart | Mermaid

URL Source: https://mermaid.js.org/syntax/xyChart.html

Markdown Content:
> In the context of mermaid-js, the XY chart is a comprehensive charting module that encompasses various types of charts that utilize both x-axis and y-axis for data representation. Presently, it includes two fundamental chart types: the bar chart and the line chart. These charts are designed to visually display and analyze data that involve two numerical variables.

> It's important to note that while the current implementation of mermaid-js includes these two chart types, the framework is designed to be dynamic and adaptable. Therefore, it has the capacity for expansion and the inclusion of additional chart types in the future. This means that users can expect an evolving suite of charting options within the XY chart module, catering to various data visualization needs as new chart types are introduced over time.

Example [​](https://mermaid.js.org/syntax/xyChart.html#example)
---------------------------------------------------------------

Syntax [​](https://mermaid.js.org/syntax/xyChart.html#syntax)
-------------------------------------------------------------

INFO

All text values that contain only one word can be written without `"`. If a text value has many words in it, specifically if it contains spaces, enclose the value in `"`

### Orientations [​](https://mermaid.js.org/syntax/xyChart.html#orientations)

The chart can be drawn horizontal or vertical, default value is vertical.

```
xychart-beta horizontal
...
```

### Title [​](https://mermaid.js.org/syntax/xyChart.html#title)

The title is a short description of the chart and it will always render on top of the chart.

#### Example [​](https://mermaid.js.org/syntax/xyChart.html#example-1)

```
xychart-beta
    title "This is a simple example"
    ...
```

INFO

If the title is a single word one no need to use `"`, but if it has space `"` is needed

### x-axis [​](https://mermaid.js.org/syntax/xyChart.html#x-axis)

The x-axis primarily serves as a categorical value, although it can also function as a numeric range value when needed.

#### Example [​](https://mermaid.js.org/syntax/xyChart.html#example-2)

1.  `x-axis title min --> max` x-axis will function as numeric with the given range
2.  `x-axis "title with space" [cat1, "cat2 with space", cat3]` x-axis if categorical, categories are text type

### y-axis [​](https://mermaid.js.org/syntax/xyChart.html#y-axis)

The y-axis is employed to represent numerical range values, it cannot have categorical values.

#### Example [​](https://mermaid.js.org/syntax/xyChart.html#example-3)

1.  `y-axis title min --> max`
2.  `y-axis title` it will only add the title, the range will be auto generated from data.

INFO

Both x and y axis are optional if not provided we will try to create the range

### Line chart [​](https://mermaid.js.org/syntax/xyChart.html#line-chart)

A line chart offers the capability to graphically depict lines.

#### Example [​](https://mermaid.js.org/syntax/xyChart.html#example-4)

1.  `line [2.3, 45, .98, -3.4]` it can have all valid numeric values.

### Bar chart [​](https://mermaid.js.org/syntax/xyChart.html#bar-chart)

A bar chart offers the capability to graphically depict bars.

#### Example [​](https://mermaid.js.org/syntax/xyChart.html#example-5)

1.  `bar [2.3, 45, .98, -3.4]` it can have all valid numeric values.

#### Simplest example [​](https://mermaid.js.org/syntax/xyChart.html#simplest-example)

The only two things required are the chart name (`xychart-beta`) and one data set. So you will be able to draw a chart with a simple config like

```
xychart-beta
    line [+1.3, .6, 2.4, -.34]
```

Chart Configurations [​](https://mermaid.js.org/syntax/xyChart.html#chart-configurations)
-----------------------------------------------------------------------------------------

| Parameter | Description | Default value |
| --- | --- | --- |
| width | Width of the chart | 700 |
| height | Height of the chart | 500 |
| titlePadding | Top and Bottom padding of the title | 10 |
| titleFontSize | Title font size | 20 |
| showTitle | Title to be shown or not | true |
| xAxis | xAxis configuration | AxisConfig |
| yAxis | yAxis configuration | AxisConfig |
| chartOrientation | 'vertical' or 'horizontal' | 'vertical' |
| plotReservedSpacePercent | Minimum space plots will take inside the chart | 50 |

### AxisConfig [​](https://mermaid.js.org/syntax/xyChart.html#axisconfig)

| Parameter | Description | Default value |
| --- | --- | --- |
| showLabel | Show axis labels or tick values | true |
| labelFontSize | Font size of the label to be drawn | 14 |
| labelPadding | Top and Bottom padding of the label | 5 |
| showTitle | Axis title to be shown or not | true |
| titleFontSize | Axis title font size | 16 |
| titlePadding | Top and Bottom padding of Axis title | 5 |
| showTick | Tick to be shown or not | true |
| tickLength | How long the tick will be | 5 |
| tickWidth | How width the tick will be | 2 |
| showAxisLine | Axis line to be shown or not | true |
| axisLineWidth | Thickness of the axis line | 2 |

Chart Theme Variables [​](https://mermaid.js.org/syntax/xyChart.html#chart-theme-variables)
-------------------------------------------------------------------------------------------

INFO

Themes for xychart resides inside xychart attribute so to set the variables use this syntax %%{init: { "themeVariables": {"xyChart": {"titleColor": "#ff0000"} } }}%%

| Parameter | Description |
| --- | --- |
| backgroundColor | Background color of the whole chart |
| titleColor | Color of the Title text |
| xAxisLabelColor | Color of the x-axis labels |
| xAxisTitleColor | Color of the x-axis title |
| xAxisTickColor | Color of the x-axis tick |
| xAxisLineColor | Color of the x-axis line |
| yAxisLabelColor | Color of the y-axis labels |
| yAxisTitleColor | Color of the y-axis title |
| yAxisTickColor | Color of the y-axis tick |
| yAxisLineColor | Color of the y-axis line |
| plotColorPalette | String of colors separated by comma e.g. "#f3456, #43445" |

Example on config and theme [​](https://mermaid.js.org/syntax/xyChart.html#example-on-config-and-theme)
-------------------------------------------------------------------------------------------------------

Title: Mermaid Kanban Diagram Documentation | Mermaid

URL Source: https://mermaid.js.org/syntax/kanban.html

Markdown Content:
Mermaid’s Kanban diagram allows you to create visual representations of tasks moving through different stages of a workflow. This guide explains how to use the Kanban diagram syntax, based on the provided example.

Overview [​](https://mermaid.js.org/syntax/kanban.html#overview)
----------------------------------------------------------------

A Kanban diagram in Mermaid starts with the kanban keyword, followed by the definition of columns (stages) and tasks within those columns.

Defining Columns [​](https://mermaid.js.org/syntax/kanban.html#defining-columns)
--------------------------------------------------------------------------------

Columns represent the different stages in your workflow, such as “Todo,” “In Progress,” “Done,” etc. Each column is defined using a unique identifier and a title enclosed in square brackets.

**Syntax:**

*   columnId: A unique identifier for the column.
*   \[Column Title\]: The title displayed on the column header.

Like this `id1[Todo]`

Adding Tasks to Columns [​](https://mermaid.js.org/syntax/kanban.html#adding-tasks-to-columns)
----------------------------------------------------------------------------------------------

Tasks are listed under their respective columns with an indentation. Each task also has a unique identifier and a description enclosed in square brackets.

**Syntax:**

```
•	taskId: A unique identifier for the task.
•	[Task Description]: The description of the task.
```

**Example:**

```
docs[Create Documentation]
```

You can include additional metadata for each task using the @{ ... } syntax. Metadata can contain key-value pairs like assigned, ticket, priority, etc. This will be rendered added to the rendering of the node.

```
•	assigned: Specifies who is responsible for the task.
•	ticket: Links the task to a ticket or issue number.
•	priority: Indicates the urgency of the task. Allowed values: 'Very High', 'High', 'Low' and 'Very Low'
```

Configuration Options [​](https://mermaid.js.org/syntax/kanban.html#configuration-options)
------------------------------------------------------------------------------------------

You can customize the Kanban diagram using a configuration block at the beginning of your markdown file. This is useful for setting global settings like a base URL for tickets. Currently there is one configuration option for kanban diagrams tacketBaseUrl. This can be set as in the the following example:

yaml

```
---
config:
  kanban:
    ticketBaseUrl: 'https://yourproject.atlassian.net/browse/#TICKET#'
---
```

When the kanban item has an assigned ticket number the ticket number in the diagram will have a link to an external system where the ticket is defined. The `ticketBaseUrl` sets the base URL to the external system and #TICKET# is replaced with the ticket value from task metadata to create a valid link.

Full Example [​](https://mermaid.js.org/syntax/kanban.html#full-example)
------------------------------------------------------------------------

Below is the full Kanban diagram based on the provided example:

In conclusion, creating a Kanban diagram in Mermaid is a straightforward process that effectively visualizes your workflow. Start by using the kanban keyword to initiate the diagram. Define your columns with unique identifiers and titles to represent different stages of your project. Under each column, list your tasks—also with unique identifiers—and provide detailed descriptions as needed. Remember that proper indentation is crucial; tasks must be indented under their parent columns to maintain the correct structure.

You can enhance your diagram by adding optional metadata to tasks using the @{ ... } syntax, which allows you to include additional context such as assignee, ticket numbers, and priority levels. For further customization, utilize the configuration block at the top of your file to set global options like ticketBaseUrl for linking tickets directly from your diagram.

By adhering to these guidelines—ensuring unique identifiers, proper indentation, and utilizing metadata and configuration options—you can create a comprehensive and customized Kanban board that effectively maps out your project’s workflow using Mermaid.


Title: Architecture Diagrams Documentation (v11.1.0+) | Mermaid

URL Source: https://mermaid.js.org/syntax/architecture.html

Markdown Content:
> In the context of mermaid-js, the architecture diagram is used to show the relationship between services and resources commonly found within the Cloud or CI/CD deployments. In an architecture diagram, services (nodes) are connected by edges. Related services can be placed within groups to better illustrate how they are organized.

Example [​](https://mermaid.js.org/syntax/architecture.html#example)
--------------------------------------------------------------------

Syntax [​](https://mermaid.js.org/syntax/architecture.html#syntax)
------------------------------------------------------------------

The building blocks of an architecture are `groups`, `services`, `edges`, and `junctions`.

For supporting components, icons are declared by surrounding the icon name with `()`, while labels are declared by surrounding the text with `[]`.

To begin an architecture diagram, use the keyword `architecture-beta`, followed by your groups, services, edges, and junctions. While each of the 3 building blocks can be declared in any order, care must be taken to ensure the identifier was previously declared by another component.

### Groups [​](https://mermaid.js.org/syntax/architecture.html#groups)

The syntax for declaring a group is:

```
group {group id}({icon name})[{title}] (in {parent id})?
```

Put together:

```
group public_api(cloud)[Public API]
```

creates a group identified as `public_api`, uses the icon `cloud`, and has the label `Public API`.

Additionally, groups can be placed within a group using the optional `in` keyword

```
group private_api(cloud)[Private API] in public_api
```

### Services [​](https://mermaid.js.org/syntax/architecture.html#services)

The syntax for declaring a service is:

```
service {service id}({icon name})[{title}] (in {parent id})?
```

Put together:

```
service database1(database)[My Database]
```

creates the service identified as `database1`, using the icon `database`, with the label `My Database`.

If the service belongs to a group, it can be placed inside it through the optional `in` keyword

```
service database1(database)[My Database] in private_api
```

### Edges [​](https://mermaid.js.org/syntax/architecture.html#edges)

The syntax for declaring an edge is:

```
{serviceId}{{group}}?:{T|B|L|R} {<}?--{>}? {T|B|L|R}:{serviceId}{{group}}?
```

#### Edge Direction [​](https://mermaid.js.org/syntax/architecture.html#edge-direction)

The side of the service the edge comes out of is specified by adding a colon (`:`) to the side of the service connecting to the arrow and adding `L|R|T|B`

For example:

creates an edge between the services `db` and `server`, with the edge coming out of the right of `db` and the left of `server`.

creates a 90 degree edge between the services `db` and `server`, with the edge coming out of the top of `db` and the left of `server`.

#### Arrows [​](https://mermaid.js.org/syntax/architecture.html#arrows)

Arrows can be added to each side of an edge by adding `<` before the direction on the left, and/or `>` after the direction on the right.

For example:

creates an edge with the arrow going into the `gateway` service

#### Edges out of Groups [​](https://mermaid.js.org/syntax/architecture.html#edges-out-of-groups)

To have an edge go from a group to another group or service within another group, the `{group}` modifier can be added after the `serviceId`.

For example:

```
service server[Server] in groupOne
service subnet[Subnet] in groupTwo

server{group}:B --> T:subnet{group}
```

creates an edge going out of `groupOne`, adjacent to `server`, and into `groupTwo`, adjacent to `subnet`.

It's important to note that `groupId`s cannot be used for specifying edges and the `{group}` modifier can only be used for services within a group.

### Junctions [​](https://mermaid.js.org/syntax/architecture.html#junctions)

Junctions are a special type of node which acts as a potential 4-way split between edges.

The syntax for declaring a junction is:

```
junction {junction id} (in {parent id})?
```

Icons [​](https://mermaid.js.org/syntax/architecture.html#icons)
----------------------------------------------------------------

By default, architecture diagram supports the following icons: `cloud`, `database`, `disk`, `internet`, `server`. Users can use any of the 200,000+ icons available in iconify.design, or add their own custom icons, by following the steps [here](https://mermaid.js.org/config/icons.html).

After the icons are installed, they can be used in the architecture diagram by using the format "name:icon-name", where name is the value used when registering the icon pack."""
