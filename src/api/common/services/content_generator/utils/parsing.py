import copy
import re
from typing import Any, Dict

import yaml


def extract_content(text: str, tag_name: str):
    pattern = rf"<{tag_name}>(.*?)</{tag_name}>"
    matches = re.findall(pattern, text, re.DOTALL)
    return matches


def extract_and_remove_content(result: Any, tag_name: str):
    result = copy.deepcopy(result)
    content = result.content
    tag_contents = extract_content(content, tag_name=tag_name)
    if tag_contents:
        pattern = rf"<{tag_name}>.*?</{tag_name}>\s*"
        content = re.sub(pattern, "", content, flags=re.DOTALL)
        result.content = content
    return tag_contents, result


def parse_yaml(yaml_str: str) -> Dict[Any, Any]:
    return yaml.safe_load(yaml_str)
