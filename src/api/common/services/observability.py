from __future__ import annotations

import socket
import time
from logging import Logger

import requests

try:
    from openinference.instrumentation.langchain import LangChainInstrumentor
    from openinference.instrumentation.openai_agents import OpenAIAgentsInstrumentor
    from openinference.semconv.resource import ResourceAttributes
    from opentelemetry import trace as trace_api
    from opentelemetry.exporter.otlp.proto.http.trace_exporter import OTLPSpanExporter
    from opentelemetry.sdk import trace as trace_sdk
    from opentelemetry.sdk.resources import Resource
    from opentelemetry.sdk.trace.export import SimpleSpanProcessor
    from opentelemetry.trace import Span

    from src.common.application_environment import ApplicationEnvironment

    class FilteringSpanProcessor(SimpleSpanProcessor):
        def on_end(self, span: Span):
            span_name = span.name.lower() if span.name else ""
            if (
                span.attributes.get("messaging.system") == "servicebus"
                or span.attributes.get("az.namespace") == "Microsoft.ServiceBus"
                or span.attributes.get("db", None) is not None
                or span.attributes.get("db.system", None) is not None
                or span.attributes.get("db.statement", None) is not None
                or span.attributes.get("db.operation", None) is not None
                or "sqlalchemy" in span_name
                or "postgresql" in span_name
            ):
                return
            super().on_end(span)

except Exception as e:
    Logger(__name__).exception(
        f"Import error occurred while importing OTel modules: {e}"
    )


class PhoenixObservability:
    def __init__(
        self,
        mode: str,
        logger: Logger,
        collector_endpoint: str | None = None,
        oltp_headers: str | None = None,
        port: int = 6006,
        project_name: str | None = None,
    ):
        self._port = port
        self._mode = mode
        self._collector_endpoint = collector_endpoint
        self._oltp_headers = oltp_headers
        self._project_name = project_name or "Contenidos IA"
        self._logger = logger

    def __check_port_occupied(self):
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            return s.connect_ex(("localhost", self._port)) == 0

    def __is_endpoint_reachable(self, url, max_retries=5, delay=5):
        for attempt in range(max_retries):
            try:
                response = requests.head(url, timeout=5)
                if response.status_code == 200:
                    self._logger.info(
                        f"Phoenix endpoint reachable after {attempt + 1} attempts."
                    )
                    return True
            except requests.exceptions.RequestException as e:
                self._logger.info(
                    f"Attempt {attempt + 1}: Phoenix endpoint not reachable. Error: {e}"
                )
            time.sleep(delay)
        return False

    def instrument(self):
        """
        Instead of creating a NEW TracerProvider, we'll reuse the global one
        that Azure Monitor sets up. Then we add our Phoenix exporter as an
        additional span processor.
        """
        try:
            if ApplicationEnvironment.get_current() != ApplicationEnvironment.LOCAL:
                self._logger.info("Entered in non local path")
                global_tracer_provider = trace_api.get_tracer_provider()
                global_tracer_provider._resource = (
                    global_tracer_provider.resource.merge(
                        Resource(
                            attributes={
                                ResourceAttributes.PROJECT_NAME: self._project_name
                            }
                        )
                    )
                )
            else:
                self._logger.info("Entered in local path")
                resource = Resource(
                    attributes={ResourceAttributes.PROJECT_NAME: self._project_name}
                )
                global_tracer_provider = trace_sdk.TracerProvider(resource=resource)
                trace_api.set_tracer_provider(global_tracer_provider)

            if not self._collector_endpoint:
                raise ValueError(
                    "PHOENIX_COLLECTOR_ENDPOINT environment variable is not set."
                )
            if not self._oltp_headers:
                raise ValueError("OTLP headers not set.")

            self._logger.info(
                f"Global tracer provider resource is: {global_tracer_provider.resource.attributes}"
            )

            headers = dict(item.split("=") for item in self._oltp_headers.split(","))

            span_exporter = OTLPSpanExporter(
                endpoint=self._collector_endpoint, headers=headers
            )
            try:
                filtering_span_processor = FilteringSpanProcessor(
                    span_exporter=span_exporter
                )
                global_tracer_provider.add_span_processor(filtering_span_processor)
            except Exception:
                self._logger.info("Exception adding Filtering Span Processor")

            LangChainInstrumentor().instrument(skip_dep_check=True)
            OpenAIAgentsInstrumentor().instrument()
            self._logger.info("PhoenixObservability initialized.")
        except Exception as e:
            self._logger.info(
                f"An error has occurred while instrumenting PhoenixObservability: {e}"
            )

    def set_observability_local(self):
        if self.__check_port_occupied():
            self._logger.info(
                f"Port {self._port} is already in use. Server is already started."
            )
            self.instrument()
        else:
            try:
                self.instrument()
            except Exception as e:
                self._logger.info(f"An exception starting server has occurred: {e}")

    def set_observability_endpoint(self):
        if self._collector_endpoint:
            if self.__is_endpoint_reachable(self._collector_endpoint):
                self.instrument()
            else:
                self._logger.info(
                    "PHOENIX_COLLECTOR_ENDPOINT is not reachable after multiple attempts. Skipping instrumentation."
                )
        else:
            self._logger.info(
                "PHOENIX_COLLECTOR_ENDPOINT environment variable is not set. Skipping instrumentation."
            )

    def set_observability(self):
        # Based on your 'mode', choose local or endpoint approach
        if self._mode == "server":
            self.set_observability_endpoint()
        else:
            self.set_observability_local()
