from enum import Enum
from typing import Any

from pydantic import BaseModel, ConfigDict, Field, field_serializer

from src.domain.models import AIProcess


class PromptContext(BaseModel):
    id_asignatura: int | None = None
    id_tema: int | None = None
    id_indice: int | None = None
    id_titulo: int | None = None
    id_epigrafe: int | None = None
    parent_process_id: int | None = None
    ai_process: AIProcess | None = None
    plan_item_id: int | None = None
    plan_id: int | None = None
    tipo_herramienta: Any | None = None


class ContentTypes(Enum):
    TEXTO = "TEXTO"
    GRAFICA = "GRAFICA"
    TABLE = "TABLA"


class Competencia(BaseModel):
    """Concepto academico que proporciona utilidad en el ambito laboral"""

    descripcion: str = Field(
        description="Habilidad adquirida en el contexto académico de una asignatura, que es aplicable y útil en el ámbito laboral."
    )


class Competencias(BaseModel):
    competencias: list[Competencia] = Field(
        description="Lista de competencias que pueden ser adquiridas al cursar una asignatura"
    )


class CompetenciasReasoning(BaseModel):
    reasoning: str = Field(
        description="Espacio donde se documenta detalladamente el proceso de razonamiento y los pasos realizados en la regeneración de competencias."
    )
    competencias: list[Competencia] = Field(
        description="Lista de competencias que pueden ser adquiridas al cursar una asignatura"
    )


class FormatTypes(Enum):
    MARKDOWN = "MARKDOWN"


class Epigrafe(BaseModel):
    nombre: str
    "Nombre del epigrafe"


class Tema(BaseModel):
    nombre: str = Field(description="Nombre del tema")
    epigrafes: list[str] = Field(
        description="Lista de epigrafes que forman parte del tema, suponen la unidad minima en la que se estructura el contenido dentro de el esquema de una asignatura."
    )


class TemaReasoning(BaseModel):
    reasoning: str = Field(
        description="Espacio donde se documenta detalladamente el proceso de razonamiento y los pasos realizados en la regeneración del tema."
    )
    tema: Tema = Field(description="Detalles del tema regenerado.")


class BloqueTematico(BaseModel):
    nombre: str = Field(description="Nombre del bloque tematico")
    temas: list[Tema] = Field(description="Temas que forman parte del bloque tematico")


class BloqueTematicoReasoning(BaseModel):
    reasoning: str = Field(
        description="Espacio donde se documenta detalladamente el proceso de razonamiento y los pasos realizados en la regeneración del bloque."
    )
    bloque_tematico: BloqueTematico = Field(
        description="Detalles del bloque temático regenerado."
    )


class Estructura(BaseModel):
    bloques_tematicos: list[BloqueTematico] = Field(
        description="Lista de pilares o pilar principal que compone una asignatura. La unidad jerárquica principal, la cual contiene temas."
    )


class Asignatura(BaseModel):
    nombre: str = Field(
        description="Nombre de la asignatura, esa esignatura pertenece a un master o curso universitario"
    )
    estructura: Estructura = Field(
        description="Contiene el indice o estructura principal de la asignatura definida en base a los requisitos de un docente."
    )


class AsignaturaReasoning(BaseModel):
    reasoning: str = Field(
        description="Espacio donde se documenta detalladamente el proceso de razonamiento y los pasos realizados en la regeneración de la asignatura."
    )
    asignatura: Asignatura = Field(description="Detalles de la asignatura regenerada.")


class EpigrafeContext(BaseModel):
    epigrafes_anteriores: list[str] = Field(default_factory=list)
    epigrafes_siguientes: list[str] = Field(default_factory=list)
    temas_anteriores: list[str] = Field(default_factory=list)
    temas_siguientes: list[str] = Field(default_factory=list)


class EpigrafeWithContext(Epigrafe):
    id: int
    nombre_tema: str
    nombre_bloque: str
    nombre_asignatura: str
    position: int
    id_tema: int
    id_bloque: int
    index: int
    didactic_instructions: str | None = None
    context: EpigrafeContext | None = None


class DidacticInstruction(BaseModel):
    id: int = Field(description="Id of the epigrafe that is being referenced")
    instruction: str = Field(
        description="Didactic instruction for the epigrafe. The information inside the instruction will be redacted in markdown"
    )


class DidacticInstructions(BaseModel):
    output: list[DidacticInstruction] = Field(
        description="List of didactic instructions ordered by the epigrafe id."
    )


class DidacticInstructionRegenerate(BaseModel):
    instruction: str = Field(description="Didactic instruction for the epigrafe.")


class DidacticInstructionsReasoning(BaseModel):
    reasoning: str = Field(
        description="Detailed reasoning for regenerating didactic instructions"
    )
    output: list[DidacticInstructionRegenerate] = Field(
        description="List of didactic instructions."
    )


class Plan(BaseModel):
    id: int | None = None
    plan: str = Field(
        description="Nombre conciso del plan, titulo del parrafo a generar"
    )
    descripcion: str = Field(
        description="Descripcion de como llevar a cabo ese plan, detalles adicionales sobre estilo de escritura o feedback de como se quiere que se genere."
    )
    herramienta: ContentTypes
    version: int | None = None

    model_config = ConfigDict(extra="allow")

    @field_serializer("herramienta")
    def serialize_herramienta(self, herramienta: ContentTypes, _info):
        return herramienta.value


class ContentPlan(BaseModel):
    output: list[Plan]


class ContentPlanLinked(ContentPlan):
    id: int | None = None
    epigrafe_id: int
    context: EpigrafeWithContext
    version: int | None = None


class AIProcessInfo(BaseModel):
    ai_process_type: str
    ai_process_id: int
    filled_prompt: Any | None = None


class ModelInfo(BaseModel):
    name: str
    provider: str
    reasoning_effort: str | None = None
    max_tokens: int = 8000
    temperature: float = 0


class ProcessMetadata(BaseModel):
    plan_version: int | None = None
    failed_generation_count: int | None = None
    model_info: ModelInfo | None = None


class SubjectDescription(BaseModel):
    subject_name: str
    description: str


class ContentMetadata(BaseModel):
    tables: list[str] | None = []
    diagrams: list[str] | None = []
    reasonings: list[str] | None = []
    diagrams_urls: list[str] | None = []
    original_ai_content: str | None = None


class GeneratedContent(BaseModel):
    plan_item_id: int | None = None
    content: str | None = None
    related_chunks: list[str] | None = None
    related_docs: list[str] | None = None
    metadata: ContentMetadata | None = None


class DetailedGeneratedContent(BaseModel):
    content: str
    related_docs: list[str]
    metadata: ContentMetadata | None = None
    epigrafe_id: int
    epigrafe_nombre: str
    epigrafe_position: int
    plan_item_position: int
    tema_id: int
    tema_position: int
    tema_nombre: str
    bloque_id: int
    bloque_nombre: str
    bloque_position: int
