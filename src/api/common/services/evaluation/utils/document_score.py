from typing import List

from src.api.common.services.evaluation.document_info_sources import DocumentInfo
from src.api.workflows.document_ingestion.embedding_functions import (
    OpenAIEmbeddingFunction,
)


# TODO: Implement a more sophisticated scoring algorithms
def quality_score(paper_info: DocumentInfo) -> int:
    if paper_info.citation_count is None:
        return 0
    else:
        return paper_info.citation_count + sum(
            author.h_index or 0 for author in paper_info.authors
        )


def freshness_score(paper_info: DocumentInfo) -> int:
    if paper_info.published_year is None:
        return 0
    else:
        return paper_info.published_year - 2000


def relevance_score(
    document_summary: str,
    query_list: List[str],
    embedding_model: OpenAIEmbeddingFunction,
) -> float:
    # Embeddings can be used to calculate the similarity between the paper's title and the queries

    embbedings = embedding_model([document_summary.replace("\n", " ")] + query_list)
    embedding_document = embbedings[0]
    embbeding_query_list = embbedings[1:]
    similarity_scores = [
        embedding_model.calculate_similarity(embedding_document, embbeding_query)
        for embbeding_query in embbeding_query_list
    ]
    return sum(similarity_scores) / len(similarity_scores)
