import asyncio
import logging
from typing import List

from src.api.common.services.evaluation.document_info_sources import (
    CrossrefClient,
    DocumentInfo,
    DocumentInfoSource,
    SemanticScholarClient,
)

logger = logging.getLogger(__name__)


class DocumentInfoService:
    def __init__(self, use_semantic_scholar=True, use_crossref=True):
        self.clients: List[DocumentInfoSource] = []
        if use_semantic_scholar:
            self.clients.append(SemanticScholarClient())
        if use_crossref:
            self.clients.append(CrossrefClient())
        if not self.clients:
            raise ValueError("At least one source must be enabled")

    async def get_paper_info(self, title: str) -> DocumentInfo:
        return await self.get_aggregated_paper_info(title)

    async def get_aggregated_paper_info(self, title: str) -> DocumentInfo:
        paper_infos = await asyncio.gather(
            *[client.get_paper_info(title) for client in self.clients]
        )

        # Combine the information, preferring non-None values
        combined_info = DocumentInfo()
        for info in paper_infos:
            combined_info.title = combined_info.title or info.title
            combined_info.citation_count = (
                combined_info.citation_count or info.citation_count
            )
            combined_info.published_year = (
                combined_info.published_year or info.published_year
            )
            combined_info.authors = combined_info.authors or info.authors
            combined_info.citations = combined_info.citations or info.citations

        return combined_info


async def main():
    paper_info_service = DocumentInfoService()
    try:
        paper_info = await paper_info_service.get_paper_info(
            "Electricity market model trends"
        )
        print(paper_info)
    except ValueError as e:
        logger.error(f"Invalid input: {e}")
    except Exception as e:
        logger.error(f"An unexpected error occurred: {e}")


if __name__ == "__main__":
    asyncio.run(main())
