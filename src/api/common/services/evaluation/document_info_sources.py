import logging
import urllib.parse
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import List, Optional

import aiohttp

logger = logging.getLogger(__name__)


@dataclass
class Author:
    name: Optional[str] = None
    h_index: Optional[int] = None


@dataclass
class Citation:
    year: Optional[int] = None
    count: Optional[int] = None


@dataclass
class DocumentInfo:
    title: Optional[str] = None
    citation_count: Optional[int] = None
    published_year: Optional[int] = None
    authors: List[Author] = field(default_factory=list)
    citations: List[Citation] = field(default_factory=list)


class DocumentInfoSource(ABC):
    @abstractmethod
    async def get_paper_info(self, title: str) -> DocumentInfo:
        pass


class SemanticScholarClient(DocumentInfoSource):
    BASE_URL = "https://api.semanticscholar.org/graph/v1"

    async def get_paper_info(self, title: str) -> DocumentInfo:
        if not title.strip():
            raise ValueError("Paper title cannot be empty")

        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.BASE_URL}/paper/search/match?query={title}"
                ) as response:
                    paper_reponse = await response.json()
                    paper_id = paper_reponse["data"][0]["paperId"]

                search_url = f"{self.BASE_URL}/paper/{paper_id}?fields=year,authors,authors.name,authors.hIndex,citationCount,citations.citationCount,citations.year"
                async with session.get(search_url) as response:
                    search_response = await response.json()
                # TODO: ONLY for testing, wait for an api key to be provided
                # while True:
                #     async with session.get(search_url) as response:
                #         if response.status == 429:
                #             continue
                #         search_response = await response.json()
                #         break

            if search_response.get("code") == "429" or search_response.get("error"):
                raise aiohttp.ClientResponseError(
                    status=429,
                    request_info=None,
                    history=None,
                    message=search_response.get("message")
                    or search_response.get("error"),
                    headers=None,
                )

            paper_data = search_response

            authors = [
                Author(name=author.get("name"), h_index=author.get("hIndex"))
                for author in paper_data.get("authors", [])
            ]

            citations = [
                Citation(year=citation.get("year"), count=citation.get("citationCount"))
                for citation in paper_data.get("citations", [])
            ]

            return DocumentInfo(
                title=paper_data.get("title"),
                published_year=paper_data.get("year"),
                authors=authors,
                citations=citations,
                citation_count=paper_data.get("citationCount"),
            )

        except Exception as e:
            logger.error(f"Error fetching data from Semantic Scholar: {e}")
            return DocumentInfo()


class CrossrefClient(DocumentInfoSource):
    BASE_URL = "https://api.crossref.org/works"

    async def get_paper_info(self, title: str) -> DocumentInfo:
        if not title.strip():
            raise ValueError("Paper title cannot be empty")

        try:
            encoded_title = urllib.parse.quote_plus(title)
            url = f"{self.BASE_URL}?query.title={encoded_title}&rows=1"
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    response_data = await response.json()

            if not response_data.get("message", {}).get("items"):
                return DocumentInfo()

            best_match = response_data["message"]["items"][0]
            authors = [
                Author(name=author.get("given", "") + " " + author.get("family", ""))
                for author in best_match.get("author", [])
            ]

            return DocumentInfo(
                title=best_match.get("title", [None])[0],
                authors=authors,
                citation_count=best_match.get("is-referenced-by-count"),
                published_year=best_match.get("published", {}).get(
                    "date-parts", [[None]]
                )[0][0],
            )

        except Exception as e:
            logger.error(f"Error fetching data from Crossref: {e}")
            return DocumentInfo()
