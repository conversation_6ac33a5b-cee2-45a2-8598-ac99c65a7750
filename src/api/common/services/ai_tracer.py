from datetime import datetime
from typing import Any, List, Optional

from sqlalchemy import Engine, func
from sqlmodel import Session, select

from src.api.common.services.structs import ProcessMetadata
from src.domain.models import (
    AIExecution,
    AIProcess,
    AIProcessRating,
    AIProcessStatus,
    AIProcessType,
)


class AITracerError(Exception):
    """
    Custom exception for AITracer errors.
    """

    pass


class AITracer:
    def __init__(self, db_engine: Engine, metadata: dict[str, Any] | None = None):
        self.metadata = metadata if metadata else {}
        self._db_engine = db_engine

    def start_process(
        self,
        process_type: AIProcessType,
        indice_id: Optional[int] = None,
        tema_id: Optional[int] = None,
        epigrafe_id: Optional[int] = None,
        parent_id: Optional[int] = None,
        process_metadata: Optional[ProcessMetadata] = None,
        status: AIProcessStatus | None = AIProcessStatus.IN_PROGRESS,
    ) -> AIProcess:
        """
        Start AI process and create an AIProcess instance.
        """
        with Session(self._db_engine) as session:
            process = AIProcess(
                indice_id=indice_id,
                tema_id=tema_id,
                epigrafe_id=epigrafe_id,
                process_type=process_type,
                parent_id=parent_id,
                status=status,
                additional_metadata=process_metadata.model_dump(exclude_none=True)
                if process_metadata
                else {},
                started_at=datetime.now(),
            )
            session.add(process)
            session.commit()
            session.refresh(process)
            return process

    def log_execution(
        self,
        ai_process: AIProcess,
        input_data: dict[Any, Any],
        output_data: dict[str, Any],
        metadata: dict[str, Any],
        is_retry: bool = False,
        error_message: Optional[str] = None,
        prompt_id: Optional[int] = None,
        status: AIProcessStatus = AIProcessStatus.COMPLETED,
    ) -> AIExecution:
        """
        Log AI execution inside AI process.
        """
        with Session(self._db_engine) as session:
            metadata = {**self.metadata, **metadata}
            execution = AIExecution(
                ai_process_id=ai_process.id,
                input_data=input_data,
                output_data=output_data,
                execution_metadata=metadata,
                status=status,
                is_retry=is_retry,
                error_message=error_message,
                prompt_id=prompt_id,
            )
            session.add(execution)
            session.commit()
            session.refresh(execution)
            return execution

    def complete_process(
        self,
        ai_process: AIProcess,
        status: AIProcessStatus = AIProcessStatus.COMPLETED,
        indice_id: Optional[int] = None,
    ):
        """
        Mark AI process as completed
        """
        with Session(self._db_engine) as session:
            ai_process.status = status
            ai_process.completed_at = datetime.now()
            if indice_id:
                ai_process.indice_id = indice_id
            if session.object_session(ai_process) is not session:
                ai_process = session.merge(ai_process)
            session.commit()
            session.refresh(ai_process)
            return ai_process

    # Method to check the overall status of the process seing the status of the executions.

    def trace_process(
        self,
        process_type: AIProcessType,
        input_data: dict[str, Any],
        output_data: dict[str, Any],
        execution_metadata: dict[str, Any],
        execution_status: AIProcessStatus = AIProcessStatus.COMPLETED,
        process_status: AIProcessStatus = AIProcessStatus.COMPLETED,
        indice_id: Optional[int] = None,
        tema_id: Optional[int] = None,
        epigrafe_id: Optional[int] = None,
        parent_id: Optional[int] = None,
        prompt_id: Optional[int] = None,
        error_message: Optional[str] = None,
        process_metadata: Optional[ProcessMetadata] = None,
    ) -> AIProcess:
        """
        Trace AI process from start to finish.
        """
        metadata = {**self.metadata, **execution_metadata}
        process = self.start_process(
            process_type, indice_id, tema_id, epigrafe_id, parent_id, process_metadata
        )
        self.log_execution(
            process,
            input_data,
            output_data,
            metadata,
            prompt_id=prompt_id,
            status=execution_status,
            error_message=error_message,
        )
        process = self.complete_process(process, process_status, indice_id)
        return process

    def get_process_history(
        self,
        process_type: Optional[AIProcessType] = None,
        indice_id: Optional[int] = None,
        tema_id: Optional[int] = None,
        parent_id: Optional[int] = None,
    ) -> List[AIProcess]:
        """
        Retrieve the history of AI processes, optionally filtered by user input, process type, subject structure, or parent process.
        """
        with Session(self._db_engine) as session:
            statement = select(AIProcess)
            if process_type:
                statement = statement.where(AIProcess.process_type == process_type)
            if indice_id:
                statement = statement.where(AIProcess.indice_id == indice_id)
            if tema_id:
                statement = statement.where(AIProcess.tema_id == tema_id)
            if parent_id:
                statement = statement.where(AIProcess.parent_id == parent_id)
            return session.exec(statement).all()

    def get_execution_details(
        self, ai_process_id: int, include_retries: bool = True
    ) -> list[AIExecution]:
        """
        Retrieve all execution details for a specific AI process.
        """
        # Use expire_on_commit=False to preserve object state after session closes
        with Session(self._db_engine) as session:
            statement = select(AIExecution).where(
                AIExecution.ai_process_id == ai_process_id
            )
            if not include_retries:
                statement = statement.where(AIExecution.is_retry.is_(False))
            results = session.exec(statement).all()
        return results

    def get_retry_count(self, ai_process_id: int) -> int:
        with Session(self._db_engine) as session:
            return session.exec(
                select(func.count()).where(
                    AIExecution.ai_process_id == ai_process_id,
                    AIExecution.is_retry.is_(True),
                )
            ).one()

    def get_retries_in_sequence(self, ai_process_id: int) -> List[AIExecution]:
        with Session(self._db_engine) as session:
            return session.exec(
                select(AIExecution)
                .where(
                    AIExecution.ai_process_id == ai_process_id,
                    AIExecution.is_retry.is_(True),
                )
                .order_by(AIExecution.created_at)
            ).all()  # type: ignore

    def get_execution(
        self, ai_process_id: int, process_type: str, chain_name: Optional[str] = None
    ) -> Any | None:
        from sqlalchemy import func, inspect

        with Session(self._db_engine) as session:
            statement = (
                select(AIExecution)
                .where(
                    AIExecution.ai_process_id == ai_process_id,
                    AIProcess.process_type == process_type,
                )
                .join(AIProcess, AIExecution.ai_process_id == AIProcess.id)
                .order_by(AIExecution.created_at.asc())
            )  # type: ignore
            if chain_name:
                # Detectar el dialecto de la base de datos
                dialect_name = inspect(session.get_bind()).dialect.name
                if dialect_name == "sqlite":
                    statement = statement.where(
                        func.json_extract(AIExecution.execution_metadata, "$.chain")
                        == chain_name
                    )
                else:
                    statement = statement.where(
                        AIExecution.execution_metadata["chain"].astext == chain_name
                    )

            result = session.exec(statement).first()
        return result if result else None

    def rate_process(
        self,
        process_id: int,
        rating: int,
        evaluator_name: str,
        comment: Optional[str] = None,
    ) -> AIProcessRating:
        """
        Rate an AI process on a scale of 1 to 5.
        """
        with Session(self._db_engine) as session:
            process = session.get(AIProcess, process_id)
            if not process:
                raise AITracerError(f"AIProcess with id {process_id} not found")

            new_rating = AIProcessRating(
                ai_process_id=process_id,
                rating=rating,
                comment=comment,
                evaluator_name=evaluator_name,
            )
            session.add(new_rating)
            session.commit()
            session.refresh(new_rating)
            return new_rating

    def get_process_ratings(
        self, process_id: int, ai_process_type: AIProcessType = None
    ) -> List[AIProcessRating]:
        """
        Get all ratings for a specific AI process.
        """
        with Session(self._db_engine) as session:
            statement = select(AIProcessRating).where(
                AIProcessRating.ai_process_id == process_id
            )
            if ai_process_type:
                statement = statement.join(
                    AIProcess, AIProcessRating.ai_process_id == AIProcess.id
                ).where(AIProcess.process_type == AIProcessType(ai_process_type))
            return session.exec(statement).all()
