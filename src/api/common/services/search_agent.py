import asyncio
import datetime
import json
import logging
from enum import Enum
from typing import Any

import openai
import tiktoken
from agents import (
    Agent,
    ModelSettings,
    Runner,
    function_tool,
    set_default_openai_client,
)
from ia_gen_core.prompts import PromptManager
from pydantic import BaseModel, Field

from src.api.common.services.search_engine import (
    SearchEngine,
    SearchResult,
)
from src.api.common.services.url_extractor_engine import UrlExtractor


# Pydantic models
class Recurso(BaseModel):
    id: int
    title: str
    snippet: str


class EvaluationInput(BaseModel):
    concepto: str
    additional_info: str
    recursos: list[Recurso]


class EvaluationDetail(BaseModel):
    reasoning: str = Field(..., description="Razon de la evaluacion.")
    valoracion: int = Field(..., description="Puntuacion asignada.")


class Evaluaciones(BaseModel):
    pertinencia_titulo: EvaluationDetail = Field(
        ..., description="Evaluacion de la pertinencia del titulo."
    )
    coherencia_descripcion: EvaluationDetail = Field(
        ..., description="Evaluacion de la coherencia de la descripcion."
    )
    profundidad_informacion: EvaluationDetail = Field(
        ..., description="Evaluacion de la profundidad de la informacion."
    )
    claridad_precision: EvaluationDetail = Field(
        ..., description="Evaluacion de la claridad y precision."
    )


class ValoracionItem(BaseModel):
    id: int = Field(..., description="ID del documento evaluado.")
    evaluaciones: Evaluaciones = Field(..., description="Evaluaciones del documento.")


class ValoracionesOutputModel(BaseModel):
    valoraciones: list[ValoracionItem] = Field(
        ..., description="Lista con todos los recursos evaluados."
    )


class SearchQueryOutput(BaseModel):
    title: str
    url: str
    snippet: str


class AFondo(BaseModel):
    titulo: str
    cita: str
    url: str
    justificacion: str


class TopicAFondo(BaseModel):
    referencias: list[AFondo]


class ExtractionError(BaseModel):
    url: str
    error: str


class ExtractContentOutputModel(BaseModel):
    title: str = Field(..., description="title of the document")
    url: str = Field(..., description="url of the document")
    authors: list[str] = Field(..., description="authors of the document")
    date: int | None = Field(
        None,
        description="year of the document in format YYYY, if not available, return null",
    )
    summary: list[str] = Field(
        ..., description="summary of the document in bullet points"
    )
    lenguaje: str = Field(
        ...,
        description="language of the document in ISO 639-1 code (e.g., 'ES' for Spanish, 'EN' for English)",
    )
    is_pay_walled: bool = Field(
        default=False,
        description="boolean indicating if the document is behind a paywall",
    )
    is_info_accesbile: bool = Field(
        default=True,
        description="boolean indicating if the information is accessible directly from the document without any restrictions or extra steps",
    )


class QueryList(BaseModel):
    plan: str
    queries: list[str]


class SearchType(Enum):
    academic = "academic"
    non_academic = "non_academic"
    both = "both"


class AgentResult(BaseModel):
    documents: list[ExtractContentOutputModel]


class SearchAgent(Agent):
    def __init__(
        self,
        openai_async_client: openai.AsyncClient,
        academic_search_engine: SearchEngine,
        non_academic_search_engine: SearchEngine,
        url_extractor: UrlExtractor,
        prompt_manager: PromptManager,
        logger: logging.Logger = None,
        model: str = "o4-mini",
        name: str = "search_agent",
        model_settings: ModelSettings | None = None,
        top_extracts: int = 20,
    ):
        """
        Initialize the SearchAgent with required dependencies.

        Args:
            openai_async_client: OpenAI async client for API calls.
            academic_search_engine: Search engine for academic searches.
            non_academic_search_engine: Search engine for non-academic searches.
            url_extractor: URL extractor service to extract content from URLs.
            prompt_manager: Prompt manager to handle prompts.
            logger: Optional logger instance for logging.
            model: Model to use for the agent (default: "o4-mini").
            name: Name of the agent (default: "search_agent").
            model_settings: Model settings for the agent (default: None).
            top_extracts: Number of top extractions to return (default: 20).
        """
        # Set up logging
        self._logger = logger or logging.getLogger(__name__)
        self._academic_search_engine = academic_search_engine
        self._non_academic_search_engine = non_academic_search_engine
        self._openai_client = openai_async_client
        self._url_extractor = url_extractor
        self._prompt_manager = prompt_manager
        self.url_content_cache = {}
        self.top_extractions = top_extracts
        set_default_openai_client(openai_async_client)

        # Modificar instrucciones para que el primer paso sea SIEMPRE llamar a plan_search_strategy
        instructions = self._prompt_manager.get_prompt(name="agent-prompt")
        instructions = instructions.compile(
            fecha_actual=datetime.datetime.now().strftime("%d/%m/%Y")
        ).to_list()[0].content
        self._logger.info(f"Instructions are: {instructions}")
        model_settings = model_settings or ModelSettings(
            parallel_tool_calls=True, tool_choice="auto"
        )
        # Inicializar el agente con la nueva herramienta primero
        super().__init__(
            name=name,
            instructions=instructions,
            tools=[],
            model=model,
            output_type=AgentResult,
            model_settings=model_settings,
        )

        # Define tool functions as nested functions to capture self
        @function_tool
        async def search(
            reasoning: str,
            original_concept: str,
            search_additional_info: str,
            queries: list[str],
            only_pdfs: bool,
            search_type: SearchType = SearchType.non_academic,
        ) -> list[ExtractContentOutputModel | ExtractionError]:
            """Una funcion que realiza busquedas segun el tipo especificado (academico, no academico o ambos) en un motor de busqueda, la salida es una lista de resultados sobre la que se ha extraido resumenes y metadatos.
            PARAMETROS:
                original_concept : str : concepto original de la consulta
                search_additional_info : str : informacion adicional sobre la consulta que sea relevante de cara a la evaluacion de los resultados
                queries : list[str] : lista de consultas a realizar
                search_type : type : tipo de busqueda a realizar
                only_pdfs : bool : si se deben buscar solo pdfs
            SALIDA:
                list[ExtractContentOutputModel | ExtractionError] : lista de fuentes sobre las queries generadas
            """
            return await self._search(
                original_concept,
                search_additional_info,
                queries,
                only_pdfs,
                search_type,
            )

        # Add tools to the agent
        self.tools.extend([search])

    async def run(
        self, query: str, search_context: str, max_results: int = 1
    ) -> AgentResult:
        """
        Execute the search agent with the given query and additional information.

        Args:
            query: The search query
            additional_info: Additional context for the search
            max_results: Maximum number of results to return (default: 1)

        Returns:
            The search results
        """
        user_input = f"# MAIN SEARCH OBJETIVE\n {query}\n\n# SEARCH CONTEXT\n{search_context}\n\n# NUMBER OF RESULTS\n {max_results}"
        result = await Runner.run(self, user_input, max_turns=25)
        return result.final_output

    async def _call_api_structured(
        self,
        dev_prompt: str,
        model: str,
        user_msg: str | None,
        output_class: type[BaseModel],
        temperature: float = 0.5,
        max_response_length: int = 100,
        top_probabilities: float = 0.9,
        frequency_penalty: float = 0.0,
        presence_penalty: float = 0.0,
        reasoning_effort: str | None = None,
    ) -> Any:
        """Call the OpenAI API with structured output."""
        args = {
            "messages": [
                {"role": "system", "content": [{"type": "text", "text": dev_prompt}]},
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": user_msg if user_msg is not None else "",
                        }
                    ],
                },
            ],
            "model": model,
            "response_format": output_class,
        }

        if reasoning_effort is not None:
            args["reasoning_effort"] = reasoning_effort
        else:
            args["temperature"] = temperature
            args["max_tokens"] = max_response_length
            args["top_p"] = top_probabilities
            args["frequency_penalty"] = frequency_penalty
            args["presence_penalty"] = presence_penalty

        response = await self._openai_client.chat.completions.parse(**args)
        return response.choices[0].message.parsed

    def _break_text_into_chunks(self, text: str, max_tokens: int) -> list[str]:
        """Breaks a text into chunks of a maximum number of tokens."""
        encoding = tiktoken.get_encoding("o200k_base")
        tokens = encoding.encode(text, disallowed_special=())
        chunks = [
            encoding.decode(tokens[i : i + max_tokens])
            for i in range(0, len(tokens), max_tokens)
        ]
        return chunks

    async def _search_non_academic(
        self, query: str, only_pdfs: bool
    ) -> list[SearchResult]:
        return await self._non_academic_search_engine.search(
            query=query,
            max_fetch_results=10,
            only_pdfs=only_pdfs,
            domains=[],
        )

    async def _search_academic(self, query: str) -> list[SearchResult]:
        """Search using Tavily Search Engine."""

        return await self._academic_search_engine.search(
            query=query, max_fetch_results=10, only_pdfs=True
        )

    async def _search(
        self,
        original_concept: str,
        search_additional_info: str,
        queries: list[str],
        only_pdfs: bool,
        search_type: SearchType = SearchType.non_academic,
    ) -> list[tuple[str, str, float]]:
        """Search for information using multiple search engines."""
        non_academic_results = []
        tavily_results = []

        if search_type == SearchType.non_academic or search_type == SearchType.both:
            non_academic_results = await asyncio.gather(
                *[self._search_non_academic(query, only_pdfs) for query in queries]
            )

        if search_type == SearchType.academic or search_type == SearchType.both:
            tavily_results = await asyncio.gather(
                *[self._search_academic(query) for query in queries]
            )

        results = [result for result in non_academic_results for result in result] + [
            result for result in tavily_results for result in result
        ]
        search_queries = [
            SearchQueryOutput(title=result.title, url=result.href, snippet=result.body)
            for result in results
        ]

        # Agrupa y elimina duplicados, pero ignora los grupos: solo deduplicar
        seen_urls = set()
        deduped_search_queries = []
        for i in search_queries:
            if i.url in seen_urls:
                continue
            seen_urls.add(i.url)
            deduped_search_queries.append(i)

        result = await self._evaluate_results(
            original_concept, deduped_search_queries, search_additional_info
        )

        result = [i for i in result if i[2] > 7]
        result = result[: min(self.top_extractions, len(result))]
        urls = [i[1] for i in result]

        return await self._extract_content(urls) if urls != [] else []

    async def _evaluate_results(
        self, query: str, documents: list[SearchQueryOutput], additional_info: str
    ) -> list[tuple[str, str, float]]:
        """Evaluate search results based on relevance to the query using parallel evaluations."""
        prompt_valoraciones = self._prompt_manager.get_prompt(
            name="source-evaluation-prompt"
        ).prompt

        # Prepare evaluation inputs for each document
        eval_inputs = [
            EvaluationInput(
                concepto=query,
                additional_info=additional_info,
                recursos=[
                    Recurso(
                        id=0,
                        title=doc.title,
                        snippet=doc.snippet,
                    )
                ],
            )
            for doc in documents
        ]

        # Run evaluations in parallel using asyncio.gather
        async def eval_single(eval_input):
            rubrica: ValoracionesOutputModel = await self._call_api_structured(
                prompt_valoraciones,
                "gpt-4.1-nano",
                json.dumps(eval_input.model_dump()),
                ValoracionesOutputModel,
                temperature=0,
                max_response_length=2000,
            )
            return rubrica

        eval_tasks = [eval_single(eval_input) for eval_input in eval_inputs]
        eval_results = await asyncio.gather(*eval_tasks)

        # Calculate scores
        resource_punctuation = []
        for rubricas, document in zip(eval_results, documents):
            if not rubricas.valoraciones:
                continue
            resource = rubricas.valoraciones[0]
            score = (
                resource.evaluaciones.pertinencia_titulo.valoracion
                * resource.evaluaciones.coherencia_descripcion.valoracion
                * resource.evaluaciones.profundidad_informacion.valoracion
                * resource.evaluaciones.claridad_precision.valoracion
            ) ** (1 / 4)
            resource_punctuation.append((document.title, document.url, score))

        resource_punctuation.sort(key=lambda x: x[2], reverse=True)
        return resource_punctuation

    async def process_url(
        self, url: str, prompt: str
    ) -> ExtractionError | ExtractContentOutputModel:
        response = await self._url_extractor.extract([url])
        response = response[0] if isinstance(response, list) else response
        if response.status == "error":
            return ExtractionError(url=response.url, error=response.error)
        self.url_content_cache[url] = response.content

        content_chunks = self._break_text_into_chunks(response.content, 1000000)

        output_model = ExtractContentOutputModel(
            authors=[],
            url=response.url,
            date=None,
            title="",
            summary=[],
            lenguaje="",
        )
        for index, chunk in enumerate(content_chunks):
            result: ExtractContentOutputModel = await self._call_api_structured(
                dev_prompt=prompt,
                model="gpt-4.1-mini",
                user_msg=chunk,
                temperature=0,
                output_class=ExtractContentOutputModel,
                max_response_length=32000,
            )
            output_model.summary.extend(result.summary)
            if index == 0:
                output_model.title = result.title
                output_model.authors = result.authors
                output_model.date = result.date
                output_model.lenguaje = result.lenguaje
        output_model.lenguaje = output_model.lenguaje.upper()
        return output_model

    async def _extract_content(
        self, urls: list[str]
    ) -> list[ExtractContentOutputModel | ExtractionError]:
        """Extract content from a URL in parallel."""

        prompt = self._prompt_manager.get_prompt(name="extract-content-prompt").prompt

        tasks = [self.process_url(url, prompt) for url in urls]
        output = await asyncio.gather(*tasks)
        return output
