from logging import Logger
from typing import List

from sqlmodel import Session, not_, select

from src.api.common.services.structs import ContentMetadata, DetailedGeneratedContent
from src.domain.models import (
    Bloque as BloqueModel,
)
from src.domain.models import (
    ContenidoGenerado,
    ContentPlanItem,
)
from src.domain.models import (
    ContentPlan as ContentPlanModel,
)
from src.domain.models import (
    Doc as DocumentoModel,
)
from src.domain.models import (
    Epigrafe as EpigrafeModel,
)
from src.domain.models import (
    Tema as TemaModel,
)


class MarkdownService:
    """
    Service that provides core markdown content generation functionality.
    Can be used by multiple workflows without tight coupling to specific schemas.
    """

    def __init__(self, logger: Logger):
        self._logger = logger

    def get_content_markdown(
        self,
        session: Session,
        tema_id: int,
        content_plan_version: int,
        citations_at_end: bool = True,
        epigrafe_id: int | None = None,
    ) -> tuple[str, List[DetailedGeneratedContent]] | tuple[None, None]:
        """
        Get content formatted as markdown for a given topic/epigraph.

        Args:
            session: Database session
            tema_id: Topic ID
            content_plan_version: Version of content plan to use
            citations_at_end: Whether to include citations at the end
            epigrafe_id: Optional epigraph ID to filter content

        Returns:
            Tuple of (markdown_content, content_list) or (None, None) if no content found
        """
        content_list = []

        query = self._get_content_query(tema_id, content_plan_version, epigrafe_id)
        results = session.exec(query).all()
        if len(results) > 0:
            for result in results:
                content = DetailedGeneratedContent(
                    content=result[0],
                    related_docs=result[1] if result[1] else [],
                    metadata=ContentMetadata.model_validate(result[2])
                    if result[2]
                    else None,
                    epigrafe_id=result[3],
                    epigrafe_nombre=result[4],
                    epigrafe_position=result[5],
                    plan_item_position=result[6],
                    tema_id=result[7],
                    tema_position=result[8],
                    tema_nombre=result[9],
                    bloque_id=result[10],
                    bloque_nombre=result[11],
                    bloque_position=result[12],
                )
                content_list.append(content)
        else:
            self._logger.exception("No content was found for this content plans")
            return None, None

        final_markdown = self._format_content_as_markdown(content_list)
        if citations_at_end:
            final_markdown = self._add_markdown_citations(
                session, content_list, final_markdown
            )
        return final_markdown, content_list

    def get_content_query(
        self, tema_id: int, content_plan_version: int, epigrafe_id: int | None = None
    ):
        """
        Build a query to get content for markdown generation.
        Public method for workflows that need the query directly.

        Args:
            tema_id: Topic ID
            content_plan_version: Version of content plan to use
            epigrafe_id: Optional epigraph ID to filter content

        Returns:
            SQLModel query object
        """
        return self._get_content_query(tema_id, content_plan_version, epigrafe_id)

    def _get_content_query(
        self, tema_id: int, content_plan_version: int, epigrafe_id: int | None = None
    ):
        """
        Internal method to build a query to get content for markdown generation.
        """
        query = (
            select(
                ContenidoGenerado.contenido,
                ContenidoGenerado.documentos_relacionados,
                ContenidoGenerado.metadatos,
                EpigrafeModel.id.label("epigrafe_id"),
                EpigrafeModel.name.label("epigrafe_nombre"),
                EpigrafeModel.position.label("epigrafe_position"),
                ContentPlanItem.position.label("plan_item_position"),
                TemaModel.id.label("tema_id"),
                TemaModel.position.label("tema_position"),
                TemaModel.name.label("tema_nombre"),
                BloqueModel.id.label("bloque_id"),
                BloqueModel.name.label("bloque_nombre"),
                BloqueModel.position.label("bloque_position"),
            )
            .join(
                ContentPlanItem,
                ContenidoGenerado.id_item_plan_contenido == ContentPlanItem.id,
            )
            .join(
                ContentPlanModel, ContentPlanItem.content_plan_id == ContentPlanModel.id
            )
            .join(EpigrafeModel, ContentPlanModel.epigrafe_id == EpigrafeModel.id)
            .join(TemaModel, EpigrafeModel.id_tema == TemaModel.id)
            .join(BloqueModel, TemaModel.id_bloque == BloqueModel.id)
            .where(TemaModel.id == tema_id)
            .where(ContentPlanModel.version == content_plan_version)
            .where(not_(ContentPlanItem.is_deleted))
        )
        if epigrafe_id:
            query = query.where(EpigrafeModel.id == epigrafe_id)
        query = query.order_by(
            BloqueModel.position,
            TemaModel.position,
            EpigrafeModel.position,
            ContentPlanItem.position,
        )
        return query

    def _format_content_as_markdown(
        self, content_list: List[DetailedGeneratedContent]
    ) -> str:
        """
        Internal method to format a list of content items as markdown with proper headings.
        """
        final_content = ""
        current_bloque, current_tema, current_epigrafe = None, None, None

        for content in content_list:
            current_content = content.content

            if current_bloque != content.bloque_id:
                final_content += f"# {content.bloque_nombre}\n"

            if current_tema != content.tema_id:
                final_content += f"\n## {content.tema_nombre}\n"

            if current_epigrafe != content.epigrafe_id:
                final_content += f"\n### {content.epigrafe_nombre}\n"

            current_bloque, current_tema, current_epigrafe = (
                content.bloque_id,
                content.tema_id,
                content.epigrafe_id,
            )
            final_content += current_content
        return final_content

    def _add_markdown_citations(
        self,
        session: Session,
        content_list: List[DetailedGeneratedContent],
        markdown_str: str,
    ) -> str:
        """
        Internal method to add bibliographic citations to markdown content.
        """
        citations_str = ""
        doc_ids = [
            doc_id
            for content in content_list
            for doc_id in (content.related_docs if content.related_docs else [])
        ]

        if not doc_ids:
            return markdown_str

        statement = (
            select(DocumentoModel.final_citation)
            .where(DocumentoModel.id.in_(doc_ids))
            .order_by(DocumentoModel.id)
        )
        doc_citations = session.exec(statement).all()
        for citation in doc_citations:
            citations_str += f"- {citation}\n"
        return markdown_str + f"\n\n### Referencias Bibliográficas \n\n{citations_str}"
