from logging import Logger

import httpx
import requests

from src.domain.models import EmailTriggerStatus


class MailSender:
    mail_endpoint: str
    mail_api_key: str
    logger: <PERSON>gger

    def __init__(self, mail_send_endpoint: str, mail_send_api_key: str, logger: Logger):
        self.mail_endpoint = mail_send_endpoint
        self.mail_api_key = mail_send_api_key
        self.logger = logger

    async def async_send_email(
        self, order_id: int, status: EmailTriggerStatus, topic_id: int | None = None
    ) -> None:
        try:
            async with httpx.AsyncClient() as client:
                payload = {
                    "orderId": order_id,
                    "triggerStatus": status.value,
                }
                if topic_id is not None:
                    payload["topicId"] = topic_id
                response = await client.post(
                    self.mail_endpoint,
                    headers={
                        "x-api-key": f"{self.mail_api_key}",
                        "Content-Type": "application/json",
                    },
                    json=payload,
                )
                response.raise_for_status()
                self.logger.info(
                    f"Email for order {order_id} {'and topic' + str(topic_id) if topic_id is not None else ''} with body {payload} sent successfully twith status {status.value}"
                )
        except httpx.HTTPStatusError as e:
            self.logger.error(
                f"Failed to send email for order {order_id} {'and topic' + str(topic_id) if topic_id is not None else ''} with body {payload}: {e.response.status_code} - {e.response.text}"
            )

    def send_email(
        self, order_id: int, status: EmailTriggerStatus, topic_id: int | None = None
    ) -> None:
        try:
            payload = {
                "orderId": order_id,
                "triggerStatus": status.value,
            }
            if topic_id is not None:
                payload["topicId"] = topic_id
            response = requests.post(
                self.mail_endpoint,
                headers={
                    "x-api-key": f"{self.mail_api_key}",
                    "Content-Type": "application/json",
                },
                json=payload,
            )
            response.raise_for_status()
            self.logger.info(
                f"Email for order {order_id} {'and topic' + str(topic_id) if topic_id is not None else ''} sent successfully with status {status.value}"
            )
        except requests.HTTPError as e:
            self.logger.error(
                f"Failed to send email for order {order_id} {'and topic' + str(topic_id) if topic_id is not None else ''}: {e.response.status_code} - {e.response.text}"
            )
