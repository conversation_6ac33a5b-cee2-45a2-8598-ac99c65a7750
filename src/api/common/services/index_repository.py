from copy import deepcopy
from logging import Logger
from typing import Any, List, cast

from sqlalchemy import and_, desc, func, tuple_
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncEngine
from sqlalchemy.orm import aliased, joinedload, selectinload
from sqlmodel import delete, select
from sqlmodel.ext.asyncio.session import AsyncSession

from src.api.common.services.ai_tracer import AITracer
from src.api.common.services.structs import (
    Asignatura,
    BloqueTematico,
    Competencia,
    Competencias,
    ContentPlanLinked,
    EpigrafeContext,
    EpigrafeWithContext,
    Estructura,
    Plan,
    Tema,
)
from src.domain.models import (
    AIProcess,
    Bloque,
    Competencies,
    CompetenciesIndice,
    ContentPlanItem,
    ContentPlanType,
    Indice,
    IndiceStatus,
    Order,
    Subject,
    TitleSubject,
    TopicStatus,
)
from src.domain.models import B<PERSON>que as BloqueModel
from src.domain.models import (
    ContentPlan as ContentPlanModel,
)
from src.domain.models import Epigrafe as EpigrafeModel
from src.domain.models import Tema as TemaModel

# Meter tambien de sacar topics, epigrafes, etc? Documentarla guay.


class IndexNotFoundError(Exception):
    pass


class TopicNotFoundError(Exception):
    pass


class CompetenciesNotFoundError(Exception):
    pass


class BlocksNotFoundError(Exception):
    pass


class OrderNotFoundError(Exception):
    pass


class TitleSubjectNotFoundError(Exception):
    pass


class IndexRepository:
    def __init__(self, db_engine: AsyncEngine, logger: Logger, llm_tracer: AITracer):
        self._db_engine = db_engine
        self._logger = logger
        self._llm_tracer = llm_tracer

    async def store_competencies(
        self,
        indice_id: int,
        competencies: Competencias,
        session: AsyncSession | None = None,
    ) -> list[int]:  # TODO: Cambiar de donde se use
        provided_session = session is not None
        if not provided_session:
            session = AsyncSession(self._db_engine)
        try:
            delete_stmt = delete(CompetenciesIndice).where(
                CompetenciesIndice.indice_id == indice_id
            )
            await session.execute(delete_stmt)
            competencies_list = competencies.model_dump()["competencias"]
            competencie_instances_ids = []

            for competencie in competencies_list:
                competencie_description = competencie["descripcion"]
                statement = select(Competencies).where(
                    Competencies.description == competencie_description
                )
                result = await session.exec(statement)
                competencie_instance = result.first()

                if not competencie_instance:
                    competencie_instance = Competencies(
                        description=competencie_description
                    )
                    session.add(competencie_instance)
                    await session.flush()

                competencie_instances_ids.append(competencie_instance.id)

                competencie_indice = CompetenciesIndice(
                    indice_id=indice_id, competencie_id=competencie_instance.id
                )
                session.add(competencie_indice)
            await session.commit()
        finally:
            if not provided_session:
                await session.close()
        return competencie_instances_ids

    async def get_competencies(
        self, indice_id: int, session: AsyncSession | None = None
    ) -> Competencias:
        session_provided = session is not None
        if not session_provided:
            session = AsyncSession(self._db_engine)
        try:
            result = await session.exec(
                select(Competencies)
                .join(
                    CompetenciesIndice,
                    Competencies.id == CompetenciesIndice.competencie_id,
                )
                .join(Indice, Indice.id == CompetenciesIndice.indice_id)
                .where(Indice.id == indice_id)
            )
            competencies = result.all()
            if not competencies:
                self._logger.exception(
                    f"Competencies for indice with id: {indice_id} werent found, either competencies not exist or index with that id not exists"
                )
                raise CompetenciesNotFoundError(
                    f"Competencies for indice with id: {indice_id} werent found, either competencies not exist or index with that id not exists"
                )
            return Competencias(
                competencias=[
                    Competencia(descripcion=c.description) for c in competencies
                ]
            )
        finally:
            if not session_provided:
                await session.close()

    async def get_latest_index(
        self, order_id: int, session: AsyncSession | None = None
    ) -> Indice:
        provided_session = session is not None
        if not provided_session:
            session = AsyncSession(self._db_engine)
        try:
            result = await session.exec(
                select(Indice)
                .where(Indice.order_id == order_id)
                .order_by(Indice.version.desc())
                .limit(1)
            )
            index = result.first()
            if not index:
                raise IndexNotFoundError()
            if not provided_session:
                await session.refresh(index)
        finally:
            if not provided_session:
                await session.close()
        return index

    async def get_subject_index(
        self, indice_id: int, session: AsyncSession | None = None
    ) -> Asignatura:
        session_provided = session is not None
        if not session_provided:
            session = AsyncSession(self._db_engine)
        try:
            self._logger.info("Entered in async session")
            stmt = (
                select(Indice)
                .where(Indice.id == indice_id)
                .options(selectinload(Indice.order).selectinload(Order.title_subject))
            )
            query_result = await session.exec(stmt)
            indice = query_result.first()
            self._logger.info(f"Indice inside get subject index is, {indice}")
            if not indice:
                self._logger.exception(
                    f"While loading asignatura indice with id: {indice_id} wasn't found."
                )
                raise IndexNotFoundError("Index not found")
            bloques_result = await session.exec(
                (
                    select(BloqueModel)
                    .where(BloqueModel.indice_id == indice_id)
                    .options(
                        joinedload(BloqueModel.temas).joinedload(TemaModel.epigrafes)
                    )
                    .order_by(BloqueModel.position)
                )
            )
            bloques = bloques_result.unique().all()
            self._logger.info(f"Bloques inside get subject index is, {bloques}")
            if not bloques:
                self._logger.warning(f"No bloques found for indice_id: {indice_id}")
            bloques_tematicos = [
                BloqueTematico(
                    nombre=bloque.name,
                    temas=[
                        Tema(
                            nombre=tema.name,
                            epigrafes=[
                                epigrafe.name
                                for epigrafe in sorted(
                                    tema.epigrafes, key=lambda e: e.position
                                )
                            ],
                        )
                        for tema in sorted(bloque.temas, key=lambda t: t.position)
                    ],
                )
                for bloque in bloques
            ]
            estructura = Estructura(bloques_tematicos=bloques_tematicos)
            asignatura = Asignatura(
                nombre=indice.order.title_subject.name, estructura=estructura
            )
            self._logger.info(f"Asignatura is, {asignatura}")
            return asignatura
        finally:
            if not session_provided:
                await session.close()

    async def store_subject_index(
        self,
        asignatura: Asignatura,
        order_id: int,
        indice_id: int | None = None,
        competencias: Competencias | None = None,
    ) -> int | None:
        async with AsyncSession(self._db_engine) as session:
            if indice_id:
                indice = await self.get_index(indice_id, session)
                current_indice_id = indice_id
            else:
                indice = await self.create_index(order_id, session)
                current_indice_id = indice.id
            if competencias:
                await self.store_competencies(current_indice_id, competencias, session)

            if indice_id:
                await session.execute(
                    delete(BloqueModel).where(BloqueModel.indice_id == indice_id)
                )

            last_bloque = None
            last_db_bloque = None

            for i, bloque in enumerate(asignatura.estructura.bloques_tematicos):
                db_bloque = BloqueModel(
                    name=bloque.nombre, position=i + 1, indice_id=current_indice_id
                )
                session.add(db_bloque)
                await session.flush()
                last_bloque = bloque
                last_db_bloque = db_bloque

                for j, tema in enumerate(last_bloque.temas):
                    db_tema = TemaModel(
                        name=tema.nombre, position=j + 1, id_bloque=last_db_bloque.id
                    )
                    session.add(db_tema)
                    await session.flush()

                    for z, epigrafe in enumerate(tema.epigrafes):
                        db_epigrafe = EpigrafeModel(
                            name=epigrafe, position=z + 1, id_tema=db_tema.id
                        )
                        session.add(db_epigrafe)
                        await session.flush()
            try:
                await session.commit()
                await session.refresh(indice)
                indice_id = indice.id
            except SQLAlchemyError as e:
                self._logger.error(f"Failed to store subject index: {str(e)}")
                await session.rollback()
                return None
            return indice_id

    async def create_index(
        self, order_id: int, session: AsyncSession | None = None
    ) -> Indice:
        session_provided = session is not None
        if not session_provided:
            session = AsyncSession(self._db_engine)
        try:
            order_result = await session.exec(select(Order).where(Order.id == order_id))
            order = order_result.first()
            if not order:
                self._logger.error(f"El pedido con ID {order_id} no existe")
                raise ValueError(f"No se encontró el pedido con ID {order_id}")

            existing_index_result = await session.exec(
                select(Indice)
                .where(Indice.order_id == order_id)
                .order_by(Indice.version.desc())
                .limit(1)
            )
            existing_index = existing_index_result.first()
            self._logger.info(f"Existing index is: {existing_index}")
            if existing_index:
                indice = Indice(version=existing_index.version + 1, order_id=order_id)
            else:
                indice = Indice(order_id=order_id, version=1)
            session.add(indice)
            if not session_provided:
                await session.commit()
                await session.refresh(indice)
            else:
                await session.flush()
                await session.refresh(indice)
            return indice
        finally:
            if not session_provided:
                await session.close()

    async def get_index(
        self, indice_id: int, session: AsyncSession | None = None
    ) -> Indice:
        """Get index by ID, handling session management properly."""
        if session:
            return await self._get_index(indice_id, session)
        else:
            async with AsyncSession(self._db_engine) as new_session:
                return await self._get_index(indice_id, new_session)

    async def _get_index(self, indice_id: int, session: AsyncSession) -> Indice:
        """Private method that requires a session to avoid lazy loading issues."""
        indice = await session.get(Indice, indice_id)
        if not indice:
            raise IndexNotFoundError("Index Provided not found")
        return indice

    async def get_index_elements(
        self,
        indice_id: int,
        structure_type: str,
        tema_id: int | None = None,
        bloque_id: int | None = None,
        epigrafe_id: int | None = None,
        session: AsyncSession | None = None,
    ) -> list[Any]:
        session_provided = session is not None
        if not session_provided:
            session = AsyncSession(self._db_engine)
        try:
            statement = self._build_index_elements_statement(
                structure_type, indice_id, bloque_id, tema_id, epigrafe_id
            )
            if statement is None:
                raise ValueError(f"Invalid structure_type: {structure_type}")
            result = await session.exec(statement)
            indices = result.all()
        finally:
            if not session_provided:
                await session.close()
        return indices

    def _build_bloque_statement(self, indice_id: int, bloque_id: int | None):
        statement = select(BloqueModel).where(BloqueModel.indice_id == indice_id)
        if bloque_id:
            statement = statement.where(BloqueModel.id == bloque_id)
        return statement.order_by(BloqueModel.position)

    def _build_tema_statement(
        self, indice_id: int, bloque_id: int | None, tema_id: int | None
    ):
        statement = (
            select(TemaModel)
            .join(BloqueModel, TemaModel.id_bloque == BloqueModel.id)
            .where(BloqueModel.indice_id == indice_id)
        )
        if bloque_id:
            statement = statement.where(BloqueModel.id == bloque_id)
        if tema_id:
            statement = statement.where(TemaModel.id == tema_id)
        return statement.order_by(BloqueModel.position, TemaModel.position)

    def _build_epigrafe_statement(
        self,
        indice_id: int,
        bloque_id: int | None,
        tema_id: int | None,
        epigrafe_id: int | None,
    ):
        statement = (
            select(EpigrafeModel)
            .join(TemaModel, EpigrafeModel.id_tema == TemaModel.id)
            .join(BloqueModel, TemaModel.id_bloque == BloqueModel.id)
            .where(BloqueModel.indice_id == indice_id)
        )
        if bloque_id:
            statement = statement.where(BloqueModel.id == bloque_id)
        if tema_id:
            statement = statement.where(TemaModel.id == tema_id)
        if epigrafe_id:
            statement = statement.where(EpigrafeModel.id == epigrafe_id)
        return statement.order_by(
            BloqueModel.position, TemaModel.position, EpigrafeModel.position
        )

    def _build_index_elements_statement(
        self,
        structure_type: str,
        indice_id: int,
        bloque_id: int | None,
        tema_id: int | None,
        epigrafe_id: int | None,
    ):
        if structure_type == "bloque":
            return self._build_bloque_statement(indice_id, bloque_id)
        elif structure_type == "tema":
            return self._build_tema_statement(indice_id, bloque_id, tema_id)
        elif structure_type == "epigrafe":
            return self._build_epigrafe_statement(
                indice_id, bloque_id, tema_id, epigrafe_id
            )
        else:
            return None

    async def change_index_status(
        self, indice_id: int, status: IndiceStatus, session: AsyncSession | None = None
    ) -> Indice:
        session_provided = session is not None
        if not session_provided:
            session = AsyncSession(self._db_engine)
        try:
            indice = await session.get(Indice, indice_id)
            if not indice:
                raise IndexNotFoundError(f"Index with id: {indice_id} not found")
            indice.status = status
            await session.commit()
            self._logger.info(f"Index number: {indice_id} status changed to: {status}")
        finally:
            if not session_provided:
                await session.close()
        return indice

    def complete_ai_processes(self, ai_processes: list[AIProcess], indice_id: int):
        ai_processes = [
            self._llm_tracer.complete_process(ai_process, indice_id=indice_id)
            for ai_process in ai_processes
        ]
        return ai_processes

    async def change_topic_status(
        self, topic_id: int, status: TopicStatus, session: AsyncSession | None = None
    ) -> TemaModel:
        session_provided = session is not None
        if not session_provided:
            session = AsyncSession(self._db_engine)
        try:
            topic = await session.get(TemaModel, topic_id)
            if not topic:
                raise TopicNotFoundError(f"Topic with id: {topic_id} not found")
            topic.status = status
            await session.commit()
            self._logger.info(f"Topic number: {topic_id} status changed to {status}")
        finally:
            if not session_provided:
                await session.close()
        return topic

    async def update_topic_memories(
        self,
        topic_id: int,
        memories: list[dict[str, Any]],
        session: AsyncSession | None = None,
    ) -> TemaModel:
        session_provided = session is not None
        if not session_provided:
            session = AsyncSession(self._db_engine)
        try:
            topic = await session.get(TemaModel, topic_id)
            if not topic:
                raise TopicNotFoundError(f"Topic with id: {topic_id} not found")
            topic.memories = memories
            await session.commit()
            self._logger.info(f"Topic {topic_id} memories updated")
        finally:
            if not session_provided:
                await session.close()
        return topic

    async def store_didactic_instructions(
        self,
        didactic_instructions: List[str],
        epigrafes: List[EpigrafeModel],
        plan_version: int | None = None,
        allow_increase_version: bool | None = None,
        session: AsyncSession | None = None,
    ) -> int:
        """
        Store didactic instructions for a list of epigrafes.

        Args:
            didactic_instructions: List of didactic instructions text
            epigrafes: List of EpigrafeModel objects
            plan_version: Optional version number for the content plan
            allow_increase_version: Whether to allow incrementing the version
            session: Optional AsyncSession to use

        Returns:
            The version number of the content plan
        """
        session_provided = session is not None
        if not session_provided:
            session = AsyncSession(self._db_engine)
        try:
            for epigrafe, inst in zip(epigrafes, didactic_instructions):
                db_epigrafe = await session.get(EpigrafeModel, epigrafe.id)
                if not db_epigrafe:
                    self._logger.exception(
                        f"Epigrafe {epigrafe.id} not found when storing didactic instructions, skipping"
                    )
                    raise IndexNotFoundError("Epigrafe not found")

                plan = await self._get_or_create_content_plan(
                    session, epigrafe.id, plan_version, allow_increase_version
                )
                plan.didactic_instructions = inst
                session.add(plan)

            await session.commit()
            await session.refresh(plan)
            return plan.version
        finally:
            if not session_provided:
                await session.close()

    async def _get_or_create_content_plan(
        self,
        session: AsyncSession,
        epigrafe_id: int,
        plan_version: int | None,
        allow_increase_version: bool | None,
    ) -> ContentPlanModel:
        statement = select(ContentPlanModel).where(
            ContentPlanModel.epigrafe_id == epigrafe_id
        )
        if plan_version and not allow_increase_version:
            statement = statement.where(ContentPlanModel.version == plan_version)
            result = await session.exec(
                statement.order_by(desc(ContentPlanModel.version))
            )
            plan = result.first()
            if not plan:
                if plan_version > 1:
                    previous_plan_result = await session.exec(
                        select(ContentPlanModel).where(
                            ContentPlanModel.version == plan_version - 1
                        )
                    )
                    previous_plan = previous_plan_result.first()
                    if not previous_plan:
                        self._logger.warning(
                            f"Tried to store a later version without having store previous on epigrafe: {epigrafe_id}"
                        )
                        raise IndexNotFoundError(
                            "Trying to store a later version without having stored prev."
                        )
                plan = ContentPlanModel(epigrafe_id=epigrafe_id, version=plan_version)
        else:
            result = await session.exec(
                statement.order_by(desc(ContentPlanModel.version))
            )
            plan = result.first()

        if not plan:
            plan = ContentPlanModel(epigrafe_id=epigrafe_id, version=1)
        elif allow_increase_version:
            plan = ContentPlanModel(**plan.model_dump(exclude={"id"}))
            plan.version += 1
        return plan

    async def get_epigrafes_with_context(
        self,
        indice_id: int,
        tema_id: int | None = None,
        epigrafe_id: int | None = None,
        plan_version: int | None = None,
        session: AsyncSession | None = None,
    ) -> List[EpigrafeWithContext]:
        """
        Get epigrafes with additional context information.

        Args:
            indice_id: Index ID
            tema_id: Optional Topic ID to filter by
            epigrafe_id: Optional Epigraph ID to filter by
            plan_version: Optional content plan version
            session: Optional AsyncSession to use

        Returns:
            List of EpigrafeWithContext objects
        """

        epigrafes_with_context: List[EpigrafeWithContext] = []
        session_provided = session is not None
        if not session_provided:
            session = AsyncSession(self._db_engine)
        try:
            stmt = (
                select(Indice)
                .options(selectinload(Indice.order).selectinload(Order.title_subject))
                .where(Indice.id == indice_id)
            )
            result = await session.exec(stmt)
            indice = result.first()

            if indice:
                nombre_asignatura = indice.order.title_subject.name
            else:
                return epigrafes_with_context

            epigrafes = await self.get_index_elements(
                indice_id=indice_id,
                structure_type="epigrafe",
                tema_id=tema_id,
                epigrafe_id=epigrafe_id,
                session=session,
            )

            for i, epigrafe in enumerate(epigrafes):
                parent_info = await self._get_parent_info(epigrafe, session)

                statement = select(ContentPlanModel).where(
                    ContentPlanModel.epigrafe_id == epigrafe.id
                )
                if plan_version:
                    statement = statement.where(
                        ContentPlanModel.version == plan_version
                    )
                else:
                    max_version_statement: Any = select(
                        func.max(ContentPlanModel.version)
                    ).where(ContentPlanModel.epigrafe_id == epigrafe.id)
                    max_version_result = await session.exec(max_version_statement)
                    max_version = max_version_result.first()
                    statement = statement.where(ContentPlanModel.version == max_version)

                result = await session.exec(statement)
                content_plan = result.first()
                if not content_plan:
                    self._logger.warning(
                        f"No content plan was found for indice_id: {indice_id}, tema_id: {tema_id}, plan_version: {plan_version}"
                    )

                current_epigrafe = EpigrafeWithContext(
                    nombre=epigrafe.name,
                    id=epigrafe.id,
                    position=epigrafe.position,
                    nombre_asignatura=nombre_asignatura,
                    index=i,
                    didactic_instructions=content_plan.didactic_instructions
                    if content_plan
                    else None,
                    **parent_info,
                )
                epigrafes_with_context.append(current_epigrafe)

            for epigrafe in epigrafes_with_context:
                context = await self.get_epigrafe_context(epigrafe.id, session)
                epigrafe.context = context

        finally:
            if not session_provided:
                await session.close()

        return epigrafes_with_context

    async def _get_parent_info(self, epigrafe: EpigrafeModel, session: AsyncSession):
        """Get parent information (tema, bloque) for an epigrafe"""
        parent_info = {}

        tema = await session.get(TemaModel, epigrafe.id_tema)
        if tema:
            parent_info["nombre_tema"] = tema.name
            parent_info["id_tema"] = str(tema.id)

            bloque = await session.get(BloqueModel, tema.id_bloque)
            if bloque:
                parent_info["nombre_bloque"] = bloque.name
                parent_info["id_bloque"] = str(bloque.id)

        return parent_info

    async def get_epigrafe_context(
        self, current_epigrafe_id: int, session: AsyncSession | None = None
    ) -> EpigrafeContext:
        """
        Get context information for an epigrafe including previous/next epigrafes and temas.

        Args:
            current_epigrafe_id: The ID of the current epigrafe
            session: Optional AsyncSession to use
        Returns:
            EpigrafeContext with previous/next epigrafes and temas
        """
        session_provided = session is not None
        if not session_provided:
            session = AsyncSession(self._db_engine)

        try:
            current_epigrafe = await session.get(EpigrafeModel, current_epigrafe_id)
            if not current_epigrafe:
                return EpigrafeContext(
                    epigrafes_anteriores=[],
                    epigrafes_siguientes=[],
                    temas_anteriores=[],
                    temas_siguientes=[],
                )

            result = await session.exec(
                select(EpigrafeModel.name, EpigrafeModel.position)
                .where(EpigrafeModel.id_tema == current_epigrafe.id_tema)
                .order_by(EpigrafeModel.position)
            )
            all_epigraphes = result.all()

            prev_epigrafes = [
                str(ep.name)
                for ep in all_epigraphes
                if ep.position < current_epigrafe.position
            ]
            next_epigrafes = [
                str(ep.name)
                for ep in all_epigraphes
                if ep.position > current_epigrafe.position
            ]

            tema = await session.get(TemaModel, current_epigrafe.id_tema)
            if tema:
                bloque = await session.get(BloqueModel, tema.id_bloque)
                if bloque:
                    result = await session.exec(
                        select(TemaModel.name, TemaModel.id)
                        .join(BloqueModel, TemaModel.id_bloque == BloqueModel.id)
                        .where(BloqueModel.indice_id == bloque.indice_id)
                        .order_by(BloqueModel.position, TemaModel.position)
                    )
                    all_temas = result.all()

                    prev_temas = [
                        str(t.name)
                        for t in all_temas
                        if t.id < current_epigrafe.id_tema
                    ]
                    next_temas = [
                        str(t.name)
                        for t in all_temas
                        if t.id > current_epigrafe.id_tema
                    ]

                    return EpigrafeContext(
                        epigrafes_anteriores=prev_epigrafes,
                        epigrafes_siguientes=next_epigrafes,
                        temas_anteriores=prev_temas,
                        temas_siguientes=next_temas,
                    )

            return EpigrafeContext(
                epigrafes_anteriores=prev_epigrafes,
                epigrafes_siguientes=next_epigrafes,
                temas_anteriores=[],
                temas_siguientes=[],
            )
        finally:
            if not session_provided:
                await session.close()

    def _group_and_convert_plans(self, results):
        from collections import defaultdict

        grouped_plans = defaultdict(list)
        for plan, epigrafe, version in results:
            if not plan.plan:
                plan.plan = ""
            if not plan.descripcion:
                plan.descripcion = ""
            if (
                plan.herramienta == ContentPlanType.HUMANO
                or plan.herramienta == ContentPlanType.IMAGEN
            ):
                converted_plan = Plan(
                    **plan.model_dump(exclude="herramienta"),
                    herramienta="TEXTO",
                    version=version,
                )
                object.__setattr__(converted_plan, "herramienta", plan.herramienta)
            else:
                converted_plan = Plan(**plan.model_dump(), version=version)
            grouped_plans[epigrafe.id].append(converted_plan)
        return grouped_plans

    async def _assign_plan_ids(self, session, linked_plans):
        epigrafe_versions = [
            (lp.epigrafe_id, lp.version)
            for lp in linked_plans
            if lp.version is not None
        ]
        if epigrafe_versions:
            plan_ids_query = select(
                ContentPlanModel.epigrafe_id,
                ContentPlanModel.version,
                ContentPlanModel.id,
            ).where(
                tuple_(ContentPlanModel.epigrafe_id, ContentPlanModel.version).in_(
                    epigrafe_versions
                )
            )
            result = await session.exec(plan_ids_query)
            plan_id_results = result.all()
            plan_id_dict = {
                (epigrafe_id, version): plan_id
                for epigrafe_id, version, plan_id in plan_id_results
            }
            for lp in linked_plans:
                lp.id = plan_id_dict.get((lp.epigrafe_id, lp.version))

    async def get_content_plan_linked(
        self,
        indice_id: int,
        id_tema: int,
        epigrafe_id: int | None = None,
        version: int | None = None,
        session: AsyncSession | None = None,
    ) -> List[ContentPlanLinked]:
        """
        Get linked content plans for a tema/epigrafe.

        Args:
            indice_id: Index ID
            id_tema: Topic ID
            epigrafe_id: Optional Epigraph ID to filter by
            version: Optional content plan version
            session: Optional AsyncSession to use

        Returns:
            List of ContentPlanLinked objects
        """
        session_provided = session is not None
        if not session_provided:
            session = AsyncSession(self._db_engine)
        try:
            max_version = (
                select(
                    ContentPlanModel.epigrafe_id,
                    func.max(ContentPlanModel.version).label("max_version"),
                )
                .group_by(ContentPlanModel.epigrafe_id)
                .subquery()
            )
            cpm = aliased(ContentPlanModel)
            base_query = (
                select(ContentPlanItem, EpigrafeModel, cpm.version)
                .join(cpm, ContentPlanItem.content_plan_id == cpm.id)
                .join(EpigrafeModel, cpm.epigrafe_id == EpigrafeModel.id)
                .join(TemaModel, EpigrafeModel.id_tema == TemaModel.id)
                .where(TemaModel.id == id_tema)
                .order_by(ContentPlanItem.position)
            )
            if epigrafe_id:
                base_query = base_query.where(EpigrafeModel.id == epigrafe_id)
            if version:
                final_query = base_query.where(cpm.version == version)
            else:
                final_query = base_query.join(
                    max_version,
                    and_(
                        EpigrafeModel.id == max_version.c.epigrafe_id,
                        cpm.version == max_version.c.max_version,
                    ),
                )
            result = await session.exec(final_query.order_by(ContentPlanItem.position))
            results = result.all()
            grouped_plans = self._group_and_convert_plans(results)
            epigrafes_with_context = await self.get_epigrafes_with_context(
                indice_id, id_tema, epigrafe_id, session=session
            )
            epigrafes_dict = {
                epigrafe.id: epigrafe for epigrafe in epigrafes_with_context
            }
            linked_plans = [
                ContentPlanLinked(
                    epigrafe_id=epigrafe_id,
                    context=epigrafes_dict.get(epigrafe_id),
                    output=plans,
                    version=max(cast(int, plan.version) for plan in plans)
                    if plans
                    else None,
                )
                for epigrafe_id, plans in grouped_plans.items()
            ]
            await self._assign_plan_ids(session, linked_plans)
            return sorted(linked_plans, key=lambda x: x.epigrafe_id)
        except Exception as e:
            self._logger.exception(f"An exception occurred: {e}")
            raise
        finally:
            if not session_provided:
                await session.close()

    async def get_order_and_subject_details(
        self, order_id: int
    ) -> tuple[Order, TitleSubject, Subject]:
        async with AsyncSession(self._db_engine) as session:
            statement = (
                select(Order)
                .where(Order.id == order_id)
                .options(
                    joinedload(Order.title_subject).joinedload(TitleSubject.subject)
                )
            )
            result = await session.exec(statement)
            order = result.first()
            if not order:
                self._logger.exception("Order not found")
                raise OrderNotFoundError("Order not found")
            title_subject = order.title_subject
            if not title_subject:
                self._logger.exception("Title subject not found")
                raise TitleSubjectNotFoundError("Title subject not found")
            subject = title_subject.subject
            self._logger.info(
                f"Order and details are: order: {order.id}, title_subject: {title_subject.id}, subject: {subject.id}"
            )
            return order, title_subject, subject

    async def update_index_based_on_topics(
        self,
        order_id: int,
        taget_status: IndiceStatus,
        allowed_status: list[TopicStatus],
    ) -> bool:
        async with AsyncSession(self._db_engine) as session:
            index = await self.get_latest_index(order_id, session)
            bloques_stmt = await session.exec(
                select(Bloque).where(Bloque.indice_id == index.id)
            )
            bloques = bloques_stmt.unique().all()
            topics = []
            for bloque in bloques:
                temas_stmt = await session.exec(
                    select(TemaModel).where(TemaModel.id_bloque == bloque.id)
                )
                temas = temas_stmt.unique().all()
                topics.extend(temas)
        has_ended = all(topic.status in allowed_status for topic in topics)
        if has_ended:
            await self.change_index_status(index.id, taget_status)
            self._logger.info(
                f"Index with id: {index.id} status changed to {taget_status}"
            )
        else:
            self._logger.info(
                f"Index with id: {index.id} not updated, topics not in allowed status"
            )
        return has_ended

    async def get_instructions_per_topic(
        self,
        index_id: int,
        topic_position: int | None = None,
        topic_id: int | None = None,
        content_plan_version: int = 1,
        session: AsyncSession | None = None,
    ) -> tuple[list[Any] | Any, list[str] | str]:
        if topic_id and topic_position:
            raise ValueError("You can only specify one of (topic_position, topic_id)")

        session_provided = session is not None
        if not session_provided:
            session = AsyncSession(self._db_engine)

        try:
            stmt = (
                select(
                    TitleSubject.name.label("subject_name"),
                    TemaModel.name.label("topic_name"),
                    TemaModel.id.label("topic_id"),
                    TemaModel.position.label("topic_position"),
                    func.json_agg(
                        func.json_build_object(
                            "epigraph_name",
                            EpigrafeModel.name,
                            "epigraph_id",
                            EpigrafeModel.id,
                            "epigraph_position",
                            EpigrafeModel.position,
                            "didactic_instructions",
                            ContentPlanModel.didactic_instructions,
                        )
                    ).label("epigraphs"),
                )
                .select_from(ContentPlanModel)
                .join(EpigrafeModel, ContentPlanModel.epigrafe_id == EpigrafeModel.id)
                .join(TemaModel, EpigrafeModel.id_tema == TemaModel.id)
                .join(BloqueModel, TemaModel.id_bloque == BloqueModel.id)
                .join(Indice, BloqueModel.indice_id == Indice.id)
                .join(Order, Indice.order_id == Order.id)
                .join(TitleSubject, Order.title_subject_id == TitleSubject.id)
                .where(Indice.id == index_id)
                .where(ContentPlanModel.version == content_plan_version)
                .group_by(
                    TitleSubject.id, TemaModel.id, TemaModel.name, TemaModel.position
                )
                .order_by(TemaModel.position)
            )

            if topic_id:
                stmt = stmt.where(TemaModel.id == topic_id)
            if topic_position:
                stmt = stmt.where(TemaModel.position == topic_position)
            result = await session.exec(stmt)
            topics_result = deepcopy(result.all())

            ti_list = []
            for t in topics_result:
                t_text = f"#Topic plan\n\n## {t.topic_name}\n\n"
                for e in t.epigraphs:
                    t_text += (
                        f"### {e['epigraph_name']}\n\n{e['didactic_instructions']}\n\n"
                    )
                ti_list.append(t_text)

            if (topic_id is not None or topic_position is not None) and len(
                topics_result
            ) == 1:
                return topics_result[0], ti_list[0] if len(ti_list) == 1 else ti_list
            else:
                return topics_result, ti_list
        finally:
            if not session_provided:
                await session.close()
