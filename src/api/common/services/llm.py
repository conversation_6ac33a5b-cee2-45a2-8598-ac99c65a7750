import copy
import logging
from typing import Any

from langchain_anthropic import ChatAnthropic
from langchain_openai import AzureChatOpenAI, ChatOpenAI

logger = logging.getLogger(__name__)

OPENAI_REASONING_MODELS = [
    "o1",
    "o3-mini",
    "o1-mini",
    "o1-preview",
    "o4-mini",
    "o3",
    "gpt-5",
    "gpt-5-mini",
    "gpt-5-nano",
]

REASONING_MODELS = [
    "o1",
    "o3-mini",
    "o1-mini",
    "o1-preview",
    "o4-mini",
    "o3",
    "gpt-5",
    "gpt-5-mini",
    "gpt-5-nano",
    "claude-3-7-sonnet-20250219",
    "claude-sonnet-4-20250514",
    "claude-opus-4-20250514",
    "claude-opus-4-1-20250805",
    "gemini-2.5-flash",
    "gemini-2.5-flash-preview-04-17",
    "gemini-2.5-flash-preview-05-20",
    "gemini-2.5-pro",
    "gemini-2.5-pro-preview-03-25",
    "gemini-2.5-pro-preview-05-06",
    "gemini-2.5-pro-preview-06-05",
]


class LLM:
    _model_dict = {
        "openai": {
            "gpt-4-turbo": "gpt-4-turbo",
            "gpt-3.5-turbo": "gpt-3.5-turbo",
            "gpt-4-turbo-preview": "gpt-4-turbo-preview",
            "gpt-3.5-turbo-0125": "gpt-3.5-turbo-0125",
            "gpt-3.5-turbo-1106": "gpt-3.5-turbo-1106",
            "gpt-4-turbo-2024-04-09": "gpt-4-turbo-2024-04-09",
            "gpt-4-0125-preview": "gpt-4-0125-preview",
            "gpt-4o": "gpt-4o",
            "gpt-4o-2024-08-06": "gpt-4o-2024-08-06",
            "o1": "o1",
            "o1-mini": "o1-mini",
            "o1-preview": "o1-preview",
            "o3-mini": "o3-mini",
            "o4-mini": "o4-mini",
            "o3": "o3",
            "gpt-4o-mini": "gpt-4o-mini",
            "gpt-4.1": "gpt-4.1",
            "gpt-4.1-mini": "gpt-4.1-mini",
            "gpt-4.1-nano": "gpt-4.1-nano",
            "gpt-5": "gpt-5",
            "gpt-5-mini": "gpt-5-mini",
            "gpt-5-nano": "gpt-5-nano",
        },
        "anthropic": {
            "claude-3-opus-20240229": "claude-3-opus-20240229",
            "claude-3-sonnet-20240229": "claude-3-sonnet-20240229",
            "claude-3-haiku-20240307": "claude-3-haiku-20240307",
            "claude-3-5-sonnet-20240620": "claude-3-5-sonnet-20240620",
            "claude-3-5-sonnet-20241022": "claude-3-5-sonnet-20241022",
            "claude-3-7-sonnet-20250219": "claude-3-7-sonnet-20250219",
            "claude-opus-4-20250514": "claude-opus-4-20250514",
            "claude-sonnet-4-20250514": "claude-sonnet-4-20250514",
            "claude-opus-4-1-20250805": "claude-opus-4-1-20250805",
        },
        "azure-openai": {
            "gpt-4o": "gpt-4o",
            "gpt-4.1": "gpt-4.1",
            "gpt-4.1-mini": "gpt-4.1-mini",
            "gpt-4.1-nano": "gpt-4.1-nano",
            "o4-mini": "o4-mini",
            "o3": "o3",
        },
        "model-router": {
            "gpt-4o": "gpt-4o",
            "gpt-4.1": "gpt-4.1",
            "gpt-4.1-mini": "gpt-4.1-mini",
            "gpt-4.1-nano": "gpt-4.1-nano",
            "o4-mini": "o4-mini",
            "o3": "o3",
            "gemini-2.5-flash-preview-05-20": "gemini-2.5-flash-preview-05-20",
            "gemini-2.5-flash-preview-04-17": "gemini-2.5-flash-preview-04-17",
            "gemini-2.5-pro-preview-06-05": "gemini-2.5-pro-preview-06-05",
            "gemini-2.5-pro": "gemini-2.5-pro",
            "gemini-2.5-flash": "gemini-2.5-flash",
            "claude-opus-4-20250514": "claude-opus-4-20250514",
            "claude-sonnet-4-20250514": "claude-sonnet-4-20250514",
            "claude-opus-4-1-20250805": "claude-opus-4-1-20250805",
            "gpt-5": "gpt-5",
            "gpt-5-mini": "gpt-5-mini",
            "gpt-5-nano": "gpt-5-nano",
        },
    }

    def __init__(
        self,
        openai_api_key: str,
        anthropic_api_key: str,
        azure_api_key: str | None = None,
        azure_openai_endpoint: str | None = None,
        azure_api_version: str | None = None,
        default_headers: dict | None = None,
        base_url: str | None = None,
        router_api_key: str | None = None,
    ):
        self._openai_api_key = openai_api_key
        self._anthropic_api_key = anthropic_api_key
        self._azure_api_key = azure_api_key
        self._azure_openai_endpoint = azure_openai_endpoint
        self._azure_api_version = azure_api_version
        self._default_headers = default_headers
        self._base_url = base_url
        self._router_api_key = router_api_key

    def chat_openai(
        self,
        model_name: str = "gpt-4.1",
        max_tokens: int = 1024,
        temperature: float = 0.7,
        frequency_penalty: float | None = None,
        reasoning_effort: str | None = "low",
        base_url: str | None = None,
        default_headers: dict | None = None,
        api_key: str | None = None,
    ):
        args = {
            "api_key": api_key,
            "model": model_name,
            "max_completion_tokens": max_tokens,
            "frequency_penalty": frequency_penalty,
            "base_url": base_url,
            "default_headers": default_headers,
        }

        if model_name in REASONING_MODELS:
            args["reasoning_effort"] = reasoning_effort
        if model_name not in OPENAI_REASONING_MODELS:
            args["temperature"] = temperature
        llm = ChatOpenAI(**args)
        return llm

    def chat_anthropic(
        self,
        model_name: str = "claude-sonnet-4-20250514",
        max_tokens: int = 1024,
        temperature: float = 0.7,
        reasoning_effort: str = "low",
    ):
        default_thinking = {"type": "disabled"}
        match reasoning_effort:
            case "low":
                thinking = {"type": "enabled", "budget_tokens": 2000}
            case "medium":
                thinking = {"type": "enabled", "budget_tokens": 8000}
            case "high":
                thinking = {"type": "enabled", "budget_tokens": 20000}
            case _:
                thinking = default_thinking

        if model_name in [
            "claude-3-7-sonnet-20250219",
            "claude-sonnet-4-20250514",
            "claude-opus-4-20250514",
            "claude-opus-4-1-20250805",
        ]:
            llm = ChatAnthropic(
                api_key=self._anthropic_api_key,
                model_name=model_name,
                max_tokens_to_sample=max_tokens,
                temperature=1 if reasoning_effort else temperature,
                thinking=thinking,
            )
        else:
            llm = ChatAnthropic(
                api_key=self._anthropic_api_key,
                model_name=model_name,
                max_tokens_to_sample=max_tokens,
                temperature=temperature,
            )
        return llm

    def chat_azure(
        self,
        model_name: str = "gpt-4.1",
        max_tokens: int = 1024,
        temperature: float = 0.7,
        frequency_penalty: float | None = None,
        reasoning_effort: str | None = None,
    ):
        if model_name in OPENAI_REASONING_MODELS:
            llm = AzureChatOpenAI(
                azure_endpoint=self._azure_openai_endpoint,
                api_key=self._azure_api_key,
                model=model_name,
                max_completion_tokens=max_tokens,
                frequency_penalty=frequency_penalty,
                api_version=self._azure_api_version,
                reasoning_effort=reasoning_effort,
            )
        else:
            llm = AzureChatOpenAI(
                azure_endpoint=self._azure_openai_endpoint,
                api_key=self._azure_api_key,
                model=model_name,
                max_tokens=max_tokens,
                temperature=temperature,
                frequency_penalty=frequency_penalty,
                api_version=self._azure_api_version,
            )
        return llm

    def get_llm(
        self,
        provider: str,
        model_name: str | None = None,
        max_tokens: int = 1024,
        temperature: float = 0.7,
        frequency_penalty: float | None = None,
        reasoning_effort: str | None = None,
    ) -> Any:
        if provider == "openai":
            if model_name is not None:
                return self.chat_openai(
                    api_key=self._openai_api_key,
                    model_name=model_name,
                    max_tokens=max_tokens,
                    temperature=temperature,
                    frequency_penalty=frequency_penalty,
                    reasoning_effort=reasoning_effort,
                )
            else:
                return self.chat_openai(
                    api_key=self._openai_api_key,
                    max_tokens=max_tokens,
                    temperature=temperature,
                    frequency_penalty=frequency_penalty,
                )
        elif provider == "anthropic":
            if model_name is not None:
                return self.chat_anthropic(
                    model_name=model_name,
                    max_tokens=max_tokens,
                    temperature=temperature,
                    reasoning_effort=reasoning_effort,
                )
            else:
                return self.chat_anthropic(
                    max_tokens=max_tokens,
                    temperature=temperature,
                    reasoning_effort=reasoning_effort,
                )
        elif provider == "azure-openai":
            if model_name is not None:
                return self.chat_azure(
                    model_name=model_name,
                    max_tokens=max_tokens,
                    temperature=temperature,
                    frequency_penalty=frequency_penalty,
                    reasoning_effort=reasoning_effort,
                )
            else:
                return self.chat_azure(
                    max_tokens=max_tokens,
                    temperature=temperature,
                    frequency_penalty=frequency_penalty,
                    reasoning_effort=reasoning_effort,
                )

        elif provider == "model-router":
            if model_name is not None:
                return self.chat_openai(
                    api_key=self._router_api_key,
                    model_name=model_name,
                    max_tokens=max_tokens,
                    temperature=temperature,
                    frequency_penalty=frequency_penalty,
                    reasoning_effort=reasoning_effort,
                    default_headers=self._default_headers,
                    base_url=self._base_url,
                )
            else:
                return self.chat_openai(
                    api_key=self._router_api_key,
                    max_tokens=max_tokens,
                    temperature=temperature,
                    frequency_penalty=frequency_penalty,
                    reasoning_effort=reasoning_effort,
                    default_headers=self._default_headers,
                    base_url=self._base_url,
                )

        else:
            raise ValueError(
                f"Unsupported provider: {provider}. Supported providers are: openai, anthropic, azure-openai"
            )

    @property
    def models(self):
        models = {}
        index = 1
        for provider, provider_models in self._model_dict.items():
            for model_name, model_value in provider_models.items():
                models[index] = {"model": model_value, "provider": provider}
                index += 1
        return models

    @classmethod
    def get_available_models(cls, provider: str | None = None):
        if provider:
            try:
                return list(cls._model_dict[provider].keys())
            except Exception:
                print(
                    f"Provider not found, available providers are:{cls._model_dict.keys()}"
                )
        models = {k: list(v.keys()) for k, v in cls._model_dict.items()}
        return models

    def copy(self):
        return copy.deepcopy(self)
