import asyncio
import multiprocessing
import platform
import ssl
from abc import ABC, abstractmethod
from dataclasses import dataclass
from logging import Logger
from typing import Sequence

import aiohttp
import certifi
import httpx
import tenacity
from langchain_core.documents import BaseDocumentCompressor
from tavily import AsyncTavilyClient
from tavily.errors import UsageLimitExceededError

try:
    from duckduckgo_search import AsyncDDGS
except ImportError:
    pass  # Skip duckduckgo dep if not found

SEMANTIC_SCHOLAR_DOMAIN = "semanticscholar.org"


@dataclass
class SearchResult:
    href: str
    title: str
    body: str
    source: str = "SEARCH_ENGINE"


class SearchEngine(ABC):
    def __init__(
        self,
        logger: Logger,
        reranker_model: BaseDocumentCompressor | None = None,
        default_domains: list[str] | None = None,
    ) -> None:
        if default_domains is None:
            default_domains = [SEMANTIC_SCHOLAR_DOMAIN]
        self.logger = logger
        self.default_domains = default_domains
        self.reranker = reranker_model
        self.ssl_context = ssl.create_default_context(cafile=certifi.where())

    def _generate_query(
        self, query: str, domains: Sequence[str], only_pdfs: bool
    ) -> str:
        """Generate search queries with domain filters."""
        search_query = f" {query} {'filetype:pdf' if only_pdfs else ''} {'AND' if only_pdfs else ''} {' OR '.join([f'site:{site}' for site in domains])}"
        return search_query

    def _rerank_results(
        self, query: str, documents: Sequence[SearchResult], top_n: int
    ) -> list[SearchResult]:
        """Rerank search results."""
        try:
            if not self.reranker:
                self.logger.warning("Reranker model not provided. Skipping reranking.")
                return list(documents[:top_n])
            # TODO: Make local reranker have a rerank method (now this only works with Cohere Reranker)
            relevant_docs = self.reranker.rerank(
                documents=[f"{doc.title}: {doc.body}" for doc in documents],
                query=query,
                top_n=top_n,
            )
            return [documents[doc["index"]] for doc in relevant_docs]
        except Exception as e:
            self.logger.error(f"Error reranking results: {str(e)}")
            return list(documents[:top_n])

    @abstractmethod
    async def _fetch_results_async(
        self, query: str, max_fetch_results: int
    ) -> list[SearchResult]:
        """Abstract method to fetch results from specific search engine."""
        pass

    async def search(
        self,
        query: str,
        max_fetch_results: int = 20,
        max_results: int = 10,
        use_reranker: bool = False,
        only_pdfs: bool = True,
        domains: list[str] | None = None,
        exclude_domains: list[str] | None = None,
    ) -> list[SearchResult]:
        """
        Perform a search with the given query.

        Args:
            query: The search query
            max_results_per_domain: Maximum results to fetch per domain
            max_results: Maximum total results to return
            use_reranker: Whether to use reranking
            only_pdfs: Whether to search for PDFs only
            domains: list of domains to search (falls back to default_domains if None)

        Returns:
            list of SearchResult objects

        Raises:
            Exception: If search operation fails
        """
        try:
            search_domains = domains if domains is not None else self.default_domains
            search_engine_query = self._generate_query(query, search_domains, only_pdfs)
            if exclude_domains:
                search_engine_query += " " + " ".join(
                    [f"-site:{domain}" for domain in exclude_domains]
                )
            search_results = await self._fetch_results_async(
                search_engine_query, max_fetch_results
            )

            if use_reranker:
                search_results = self._rerank_results(
                    query, search_results, max_results
                )

            return search_results[:max_results]
        except Exception as e:
            self.logger.error(f"Error performing search: {str(e)}")
            raise


class DuckDuckGoSearchEngine(SearchEngine):
    _SEMAPHORE = asyncio.Semaphore(multiprocessing.cpu_count() - 1)

    @staticmethod
    def _get_os_specific_headers() -> dict[str, str]:
        system = platform.system().lower()
        if system in ["linux", "darwin"]:  # macOS or Linux
            return {
                "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                "accept-language": "es-ES,es;q=0.9",
                "cache-control": "max-age=0",
                "priority": "u=0, i",
                "referer": "https://duckduckgo.com/",
                "sec-ch-ua": '"Chromium";v="125", "Not.A/Brand";v="24"',
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": '"macOS"',
                "sec-fetch-dest": "document",
                "sec-fetch-mode": "navigate",
                "sec-fetch-site": "same-origin",
                "sec-fetch-user": "?1",
                "upgrade-insecure-requests": "1",
                "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36",
            }
        else:  # Windows or other
            return {
                "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                "accept-language": "es-ES,es;q=0.9",
                "cache-control": "max-age=0",
                "priority": "u=0, i",
                "referer": "https://duckduckgo.com/",
                "sec-ch-ua": '"Not)A;Brand";v="99", "Google Chrome";v="127", "Chromium";v="127"',
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": '"Windows"',
                "sec-fetch-dest": "document",
                "sec-fetch-mode": "navigate",
                "sec-fetch-site": "same-origin",
                "sec-fetch-user": "?1",
                "upgrade-insecure-requests": "1",
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Safari/537.36",
            }

    def __init__(
        self,
        logger: Logger,
        reranker_model: BaseDocumentCompressor | None = None,
        default_domains: list[str] = [SEMANTIC_SCHOLAR_DOMAIN],
        headers: dict[str, str] | None = None,
    ) -> None:
        super().__init__(
            logger=logger,
            reranker_model=reranker_model,
            default_domains=default_domains,
        )
        self.headers = headers or self._get_os_specific_headers()

    @tenacity.retry(
        wait=tenacity.wait_exponential(multiplier=2, min=10),
        stop=tenacity.stop_after_attempt(3),
    )
    async def _fetch_results_async(
        self, query: str, max_fetch_results: int
    ) -> list[SearchResult]:
        try:
            async with self._SEMAPHORE:
                async with AsyncDDGS(headers=self.headers) as ddgs:
                    results = await ddgs.atext(query, max_results=max_fetch_results)
                    return [
                        SearchResult(**result, source="DUCKDUCKGO_SEARCH")
                        for result in results
                    ]
        except Exception as e:
            self.logger.error(f"Error in DuckDuckGo search: {str(e)}")
            raise


class BraveSearchEngine(SearchEngine):
    def __init__(
        self,
        logger: Logger,
        api_key: str,
        reranker_model: BaseDocumentCompressor | None = None,
        default_domains: list[str] = [SEMANTIC_SCHOLAR_DOMAIN],
    ) -> None:
        super().__init__(
            logger=logger,
            reranker_model=reranker_model,
            default_domains=default_domains,
        )
        self.api_key = api_key
        self.base_url = "https://api.search.brave.com/res/v1/web/search"
        self.headers = {"Accept": "application/json", "X-Subscription-Token": api_key}

    async def _fetch_results_async(
        self, query: str, max_fetch_results: int
    ) -> list[SearchResult]:
        for i in range(5):
            try:
                async with aiohttp.ClientSession() as session:
                    params = {"q": query, "count": max_fetch_results}
                    async with session.get(
                        self.base_url,
                        headers=self.headers,
                        params=params,
                        ssl=self.ssl_context,
                    ) as response:
                        if response.status == 200:
                            data = await response.json()
                            return [
                                SearchResult(
                                    href=result["url"],
                                    title=result["title"],
                                    body=result["description"],
                                    source="BRAVE_SEARCH",
                                )
                                for result in data["web"]["results"]
                            ]
                        raise aiohttp.ClientResponseError(
                            request_info=response.request_info,
                            history=response.history,
                            status=response.status,
                            message=f"Brave search failed with status {response.status}",
                            headers=response.headers,
                        )
            except Exception as e:
                if response.status == 429:
                    if i == 4:
                        self.logger.error(
                            "Brave API usage limit exceeded after multiple retries."
                        )
                        raise
                    self.logger.error("Brave API usage limit exceeded, retrying...")
                    await asyncio.sleep(2**i)
                else:
                    self.logger.error(f"Error in Brave search: {str(e)}")
                    raise


class TavilySearchEngine(SearchEngine):
    def __init__(
        self,
        logger: Logger,
        api_key: str,
        reranker_model: BaseDocumentCompressor | None = None,
        default_domains: list[str] = [SEMANTIC_SCHOLAR_DOMAIN],
    ) -> None:
        super().__init__(
            logger=logger,
            reranker_model=reranker_model,
            default_domains=default_domains,
        )
        self.client = AsyncTavilyClient(api_key=api_key)
        self.client._client_creator = lambda: httpx.AsyncClient(
            headers={
                "Content-Type": "application/json",
                "Authorization": f"Bearer {api_key}",
            },
            base_url="https://api.tavily.com",
            timeout=120, # a timeout of 120 is set so that during agent execution, it cannot spend too much time on a single search
            verify=self.ssl_context,
        )

    async def _fetch_results_async(
        self, query: str, max_fetch_results: int
    ) -> list[SearchResult]:
        for i in range(5):
            try:
                response = await self.client.search(
                    query=query,
                    max_results=max_fetch_results,
                    include_domains=self.default_domains,
                    search_depth="advanced",
                )

                return [
                    SearchResult(
                        href=result["url"],
                        title=result["title"],
                        body=result["content"],
                        source="TAVILY_SEARCH",
                    )
                    for result in response["results"]
                ]
            except Exception as e:
                if isinstance(e, UsageLimitExceededError):
                    if i == 4:
                        self.logger.error(
                            "Tavily API usage limit exceeded after multiple retries."
                        )
                        raise
                    self.logger.error("Tavily API usage limit exceeded, retrying...")
                    await asyncio.sleep(2**i)
                else:
                    self.logger.error(f"Error in Tavily search: {str(e)}")
                    raise


class JinaSearchEngine(SearchEngine):
    def __init__(
        self,
        logger: Logger,
        api_key: str,
        reranker_model: BaseDocumentCompressor | None = None,
        default_domains: list[str] = [SEMANTIC_SCHOLAR_DOMAIN],
    ) -> None:
        super().__init__(
            logger=logger,
            reranker_model=reranker_model,
            default_domains=default_domains,
        )

        self.base_url = "https://s.jina.ai/"
        self.headers = {
            "Accept": "application/json",
            "Authorization": f"Bearer {api_key}",
            "X-Respond-With": "no-content",
        }

    async def _fetch_results_async(
        self, query: str, max_fetch_results: int
    ) -> list[SearchResult]:
        for i in range(5):
            try:
                async with aiohttp.ClientSession() as session:
                    params = {"q": query, "count": max_fetch_results}
                    response = await session.get(
                        self.base_url,
                        headers=self.headers,
                        params=params,
                        ssl=self.ssl_context,
                    )
                    if response.status == 200:
                        data = await response.json()
                        return [
                            SearchResult(
                                href=result["url"],
                                title=result["title"],
                                body=result["description"],
                                source="JINA_SEARCH",
                            )
                            for result in data["data"]
                        ]
                    # Jina returns a 422 status, expected behaviour to rise an exception so the agents knows what urls fails
                    if response.status == 422:
                        data = await response.json()
                        if "No search results available for query" in data.get("message", ""):
                            self.logger.warning(f"Jina search returned no results for query: {query}")
                    raise aiohttp.ClientResponseError(
                        request_info=response.request_info,
                        history=response.history,
                        status=response.status,
                        message=f"Jina search failed with status {response.status}",
                        headers=response.headers,
                    )
            except Exception as e:
                if isinstance(e, aiohttp.ClientResponseError) and e.status == 429:
                    if i == 4:
                        self.logger.error(
                            "Jina API usage limit exceeded after multiple retries."
                        )
                        raise
                    self.logger.error("Jina API usage limit exceeded, retrying...")
                    await asyncio.sleep(2**i)
                else:
                    self.logger.error(f"Error in Jina search: {str(e)}")
                    raise
