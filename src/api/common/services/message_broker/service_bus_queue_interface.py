import json
from datetime import datetime, timedelta, timezone
from logging import Logger
from typing import Any, Dict, List, Optional, Tuple, Union

from azure.servicebus import (
    ServiceBusMessage,
    ServiceBusMessageBatch,
    ServiceBusReceivedMessage,
)
from azure.servicebus.aio import ServiceBusClient, ServiceBusReceiver
from azure.servicebus.amqp import AmqpMessageBodyType
from src.api.common.services.message_broker.queue_interface import QueueInterface

NOT_iNITIALIZED_ERROR = "ServiceBusReceiver is not initialized."


class ServiceBusQueueInterface(QueueInterface):
    RETRY_COUNT_NAME = "retry_count"

    def __init__(self, connection_string: str, queue_name: str, logger: Logger):
        logger = logger.getChild(self.__class__.__name__)
        logger = logger.getChild(queue_name)
        super().__init__(queue_name=queue_name, logger=logger)
        self._connection_string = connection_string
        self.client: Optional[ServiceBusClient] = None
        self.receiver: Optional[ServiceBusReceiver] = None

    async def __aenter__(self):
        self.client = ServiceBusClient.from_connection_string(self._connection_string)
        await self.client.__aenter__()
        self.receiver = self.client.get_queue_receiver(queue_name=self.queue_name)
        await self.receiver.__aenter__()
        self.sender = self.client.get_queue_sender(queue_name=self.queue_name)
        await self.sender.__aenter__()
        return self

    async def __aexit__(self, exc_type, exc_value, traceback):
        if self.receiver:
            await self.receiver.__aexit__(exc_type, exc_value, traceback)
            self.receiver = None
        if self.sender:
            await self.sender.__aexit__(exc_type, exc_value, traceback)
            self.sender = None
        if self.client:
            await self.client.__aexit__(exc_type, exc_value, traceback)
            self.client = None

    async def send_messages(
        self, payloads: Union[dict, List[dict]], delay_minutes: float = 0.1
    ) -> List[str]:
        """
        Send messages to the Service Bus queue.

        :param payloads: A single dictionary or a list of dictionaries to be sent as messages.
        :param delay_minutes: Delay in minutes before the message is enqueued.
        :return: A list of message IDs for the sent messages.
        """
        if not self.sender:
            raise RuntimeError("ServiceBusSender is not initialized.")

        if isinstance(payloads, dict):
            payloads = [payloads]

        message_ids = []
        try:
            batch_message: ServiceBusMessageBatch = (
                await self.sender.create_message_batch()
            )
            for payload in payloads:
                message = ServiceBusMessage(json.dumps(payload))
                message.scheduled_enqueue_time_utc = datetime.now(
                    tz=timezone.utc
                ) + timedelta(minutes=delay_minutes)
                batch_message.add_message(message)
                message_ids.append(message.message_id)  # Collect message IDs

            if len(batch_message) > 0:
                await self.sender.send_messages(batch_message)
                self.logger.info(
                    f"Sent {len(batch_message)} messages to the queue '{self.queue_name}'."
                )
        except Exception as e:
            self.logger.error(f"Error sending messages: {str(e)}")
            raise

        return message_ids

    async def renew_message_lock(self, message: ServiceBusReceivedMessage):
        """Renew the lock on a message."""
        if not self.receiver:
            raise RuntimeError(NOT_iNITIALIZED_ERROR)
        try:
            await self.receiver.renew_message_lock(message)
            self.logger.info(
                f"Message lock renewed for message {message.message_id} in queue {self.queue_name}."
            )
        except Exception as e:
            self.logger.error(
                f"Error renewing message lock on message {message.message_id} from queue {self.queue_name}: {str(e)}"
            )

    async def receive_messages(
        self, max_message_count: int = 1, max_wait_time: Optional[int] = None
    ) -> Tuple[bool, List[ServiceBusReceivedMessage]]:
        """
        Receive messages from the Service Bus queue.

        :param max_message_count: Maximum number of messages to receive.
        :param max_wait_time: Maximum time to wait for messages.
        :return: A list of received messages.
        """
        if not self.receiver:
            raise RuntimeError("ServiceBusClient is not initialized")
        try:
            received_messages = await self.receiver.receive_messages(
                max_message_count=max_message_count, max_wait_time=max_wait_time
            )
            return (bool(received_messages), received_messages)
        except Exception as e:
            self.logger.error(f"Error receiving messages: {str(e)}")
            raise

    async def complete_message(self, message: ServiceBusReceivedMessage) -> None:
        """
        Complete a message, removing it from the queue.

        :param message: The message to complete.
        """
        if not self.receiver:
            raise RuntimeError("ServiceBusReceiver is not initialized")
        try:
            await self.receiver.complete_message(message)
            self.logger.info(
                f"Message {message.message_id} completed in queue {self.queue_name}."
            )
        except Exception as e:
            self.logger.error(f"Error completing message: {str(e)}")
            raise

    async def abandon_message(
        self, message: ServiceBusReceivedMessage, timestamp: Optional[datetime] = None
    ) -> None:
        """
        Abandon a message that could not be processed

        :param message: The message to complete
        """
        if not self.receiver:
            raise RuntimeError(NOT_iNITIALIZED_ERROR)
        await self.receiver.abandon_message(message)
        self.logger.info(
            f"Message {message.message_id} with delivery count {message.delivery_count} abandoned in queue {self.queue_name}."
        )

    async def retry_message_with_delay(
        self, message: ServiceBusReceivedMessage, delay: timedelta
    ) -> str:
        if not self.sender:
            raise RuntimeError("ServiceBusSender is not initialized.")
        if not self.receiver:
            raise RuntimeError(NOT_iNITIALIZED_ERROR)
        try:
            msg_dict = self.msg_to_dict(message)
            self.increment_message_retry_count(msg_dict)

            new_message_id = (
                await self.send_messages(
                    msg_dict, delay_minutes=delay.total_seconds() / 60
                )
            )[0]
            await self.receiver.complete_message(message)
            return new_message_id
        except Exception as e:
            self.logger.error(
                f"Error adding message {message.message_id} from queue {self.queue_name} to retry: {str(e)}"
            )
            raise

    async def peek_messages(
        self, max_message_count: int = 1
    ) -> List[ServiceBusReceivedMessage]:
        if not self.receiver:
            raise RuntimeError("ServiceBusReceiver is not initialized")
        try:
            messages = await self.receiver.peek_messages(
                max_message_count=max_message_count
            )
            return messages
        except Exception as e:
            self.logger.error(f"Error peeking messages: {str(e)}")
            raise

    async def dead_letter_message(
        self,
        message: ServiceBusReceivedMessage,
        reason: str = "Processing Error",
        error_description: str = "Exceeded maximum retry attempts.",
    ) -> None:
        """
        Dead-letter a message that cannot be processed.

        :param message: The message to dead-letter.
        :param reason: The reason for dead-lettering the message.
        :param error_description: A detailed error description.
        """
        if not self.receiver:
            raise RuntimeError(NOT_iNITIALIZED_ERROR)
        try:
            await self.receiver.dead_letter_message(
                message, reason=reason, error_description=error_description
            )
            self.logger.info(
                f"Message {message.message_id} moved to dead-letter queue in queue {self.queue_name}. Reason: {reason}"
            )
        except Exception as e:
            self.logger.error(f"Error dead-lettering message: {str(e)}")
            raise e

    @staticmethod
    def msg_to_dict(message: ServiceBusReceivedMessage) -> Dict[Any, Any]:
        """
        Convert a ServiceBusReceivedMessage to a dictionary.

        :param msg: The ServiceBusReceivedMessage object to convert.
        :return: A dictionary representation of the message body.
        """
        if message.body_type == AmqpMessageBodyType.DATA:
            body = b"".join(message.body)
            msg_str = body.decode("utf-8")
            msg_dict = json.loads(msg_str)
        elif message.body_type == AmqpMessageBodyType.SEQUENCE:
            body = message.body
            msg_dict = json.loads(body)
        elif message.body_type == AmqpMessageBodyType.VALUE:
            body = message.body
            if isinstance(body, (str, bytes)):
                msg_str = body.decode("utf-8") if isinstance(body, bytes) else body
                msg_dict = json.loads(msg_str)
            else:
                msg_dict = body
        else:
            msg_str = str(message.body)
            msg_dict = json.loads(msg_str)
        return msg_dict

    # Methods for handling message retries in custom retry logic

    def get_message_retry_count(self, message: ServiceBusReceivedMessage) -> int:
        """
        Get the number of times a message has been retried.

        :param message: The message to check.
        :return: The number of times the message has been retried.
        """
        return self.msg_to_dict(message).get(self.RETRY_COUNT_NAME, 0)

    def increment_message_retry_count(self, msg_dict: Dict[Any, Any]) -> None:
        """
        Increment the retry count for a message_dict

        :param message: The message to increment the retry count for.
        """
        msg_dict[self.RETRY_COUNT_NAME] = msg_dict.get(self.RETRY_COUNT_NAME, 0) + 1
        return None
