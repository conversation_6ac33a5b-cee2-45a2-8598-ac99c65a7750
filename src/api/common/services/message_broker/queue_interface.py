from abc import ABC, abstractmethod
from logging import Lo<PERSON>
from typing import Any, Dict, List, Optional, Tuple, Union


class QueueInterface(ABC):
    """Abstract base class defining the interface for queue operations"""

    def __init__(self, queue_name: str, logger: Logger) -> None:
        self.queue_name = queue_name
        self.logger = logger

    @abstractmethod
    async def send_messages(
        self,
        payloads: Union[Dict[str, Any], List[Dict[str, Any]]],
        delay_minutes: float = 0,
    ) -> List[str]:
        """Send one or more messages to the queue"""
        pass

    @abstractmethod
    async def receive_messages(
        self, max_message_count: int = 1, max_wait_time: Optional[int] = None
    ) -> Tuple[bool, Any]:
        """Receive messages from the queue"""
        pass

    @abstractmethod
    async def complete_message(self, message: Any) -> None:
        """Mark a message as completed"""
        pass

    @abstractmethod
    async def abandon_message(self, message: Any) -> None:
        """Abandon a message back to the queue"""
        pass

    @abstractmethod
    async def peek_messages(self, max_message_count: int = 10) -> List[Any]:
        """Peek at messages in the queue"""
        pass

    @staticmethod
    @abstractmethod
    def msg_to_dict(message: Any) -> Dict[str, Any]:
        """Convert a message object to a dictionary"""
        pass

    @abstractmethod
    async def dead_letter_message(
        self,
        message: Any,
        reason: str = "Processing Error",
        error_description: str = "Exceeded maximum retry attempts",
    ) -> None:
        """Move a message to the dead letter queue"""
        pass

    @abstractmethod
    async def renew_message_lock(self, message: Any) -> bool:
        """Renew the lock on a message"""
        pass

    @abstractmethod
    async def __aenter__(self):
        """Async context manager entry"""
        pass

    @abstractmethod
    async def __aexit__(self, exc_type, exc_value, traceback):
        """Async context manager exit"""
        pass
