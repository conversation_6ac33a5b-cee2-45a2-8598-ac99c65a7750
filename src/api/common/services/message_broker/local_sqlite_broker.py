import json
import uuid
from logging import Logger
from typing import Any, Dict, List, Union

try:
    import aiosqlite
except ImportError:
    pass


class LocalSqliteMessage:
    message_id: str
    body: Dict[str, Any]
    delivery_count: int = 0

    def __init__(self, message_id: str, body: Dict[str, Any], delivery_count: int):
        self.message_id = message_id
        self.body = body
        self.delivery_count = delivery_count


class LocalSqliteBroker:
    """
    Basic emulator of a 'broker' using SQLite asynchronously with aiosqlite.
    Each 'queue' is a different table within the same database.
    """

    def __init__(self, db_path: str, logger: Logger):
        """
        :param db_path: SQLite database path.
        :param queue_name: name of the queue (table).
        """
        self.db_path = db_path
        self.logger = logger.getChild(self.__class__.__name__)

    async def _init_queue(self, queue_name: str):
        """
        Creates the table if it does not exist. locked_until simulates the temporary lock of a message.
        """
        create_table_sql = f"""
        CREATE TABLE IF NOT EXISTS {queue_name} (
            id TEXT PRIMARY KEY,
            body TEXT NOT NULL,
            locked_until TIMESTAMP,
            delivery_count INTEGER NOT NULL DEFAULT 0
        )
        """
        create_dead_letter_sql = f"""
        CREATE TABLE IF NOT EXISTS {queue_name}_dead_letter (
            id TEXT PRIMARY KEY,
            body TEXT NOT NULL,
            reason TEXT NOT NULL,
            error_description TEXT NOT NULL
        )
        """
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute(create_table_sql)
            await db.execute(create_dead_letter_sql)
            await db.commit()

    async def send_message(
        self, queue_name: str, body: Dict[str, Any], delay_minutes: float = 0
    ) -> str:
        """
        Inserts a message (row) into the corresponding table.
        Returns the generated message ID.
        """
        msg_id = str(uuid.uuid4())
        locked_until = (
            f"datetime('now', '+{delay_minutes} minutes')"
            if delay_minutes > 0
            else "NULL"
        )
        insert_sql = f"""
        INSERT INTO {queue_name} (id, body, locked_until, delivery_count)
        VALUES (?, ?, {locked_until}, 0)
        """
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute(insert_sql, (msg_id, json.dumps(body)))
            await db.commit()
        return msg_id

    async def send_messages(
        self,
        queue_name: str,
        payloads: Union[Dict[str, Any], List[Dict[str, Any]]],
        delay_minutes: float = 0,
    ) -> List[str]:
        """
        Inserts multiple messages into the queue with an optional delay between each insertion.
        Returns a list of generated message IDs.
        """
        if isinstance(payloads, dict):
            payloads = [payloads]

        msg_ids = []
        for payload in payloads:
            msg_id = await self.send_message(
                queue_name=queue_name, body=payload, delay_minutes=delay_minutes
            )
            msg_ids.append(msg_id)

        return msg_ids

    async def dead_letter_message(
        self, queue_name: str, msg_id: str, reason: str, error_description: str
    ) -> None:
        """
        Moves the message to a 'dead letter' table.
        """
        async with aiosqlite.connect(self.db_path) as db:
            select_sql = f"SELECT body FROM {queue_name} WHERE id = ?"
            cursor = await db.execute(select_sql, (msg_id,))
            row = await cursor.fetchone()
            if row:
                body = json.loads(row[0])
                dead_letter_table = f"{queue_name}_dead_letter"
                insert_sql = f"""
                INSERT INTO {dead_letter_table} (id, body, reason, error_description)
                VALUES (?, ?, ?, ?)
                """
                await db.execute(
                    insert_sql, (msg_id, json.dumps(body), reason, error_description)
                )
                await db.commit()
                # Delete the message from the original table
                delete_sql = f"DELETE FROM {queue_name} WHERE id = ?"
                await db.execute(delete_sql, (msg_id,))
                await db.commit()

    async def receive_messages(
        self, queue_name: str, lock_seconds: int = 90, max_message_count: int = 1
    ) -> List[LocalSqliteMessage]:
        """
        Retrieves up to max_message_count unlocked messages and marks them as locked
        (locked_until = now + lock_seconds).

        To prevent two consumers from taking the same message,
        BEGIN IMMEDIATE TRANSACTION is used, which forces a write-lock on the database in SQLite.

        Returns a list of LocalSqliteMessage.
        """
        select_sql = f"""
        SELECT id, body, delivery_count
        FROM {queue_name}
        WHERE locked_until IS NULL
           OR locked_until < datetime('now')
        LIMIT {max_message_count}
        """
        async with aiosqlite.connect(self.db_path) as db:
            # We force an immediate transaction to prevent another consumer
            # from reading the same message before the update
            await db.execute("BEGIN IMMEDIATE TRANSACTION;")

            cursor = await db.execute(select_sql)
            rows = await cursor.fetchall()
            if not rows:
                await db.commit()
                return []

            msg_ids = [row[0] for row in rows]
            update_sql = f"""
            UPDATE {queue_name}
            SET locked_until = datetime('now', '+{lock_seconds} seconds')
            WHERE id IN ({",".join(["?"] * len(msg_ids))})
            """
            await db.execute(update_sql, msg_ids)

            messages = [
                LocalSqliteMessage(
                    message_id=row[0], body=row[1], delivery_count=row[2]
                )
                for row in rows
            ]

            await db.commit()

        return messages

    async def complete_message(self, queue_name: str, msg_id: str) -> None:
        """
        Deletes the message from the table, simulating 'complete' in Service Bus.
        """
        delete_sql = f"DELETE FROM {queue_name} WHERE id = ?"
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute(delete_sql, (msg_id,))
            await db.commit()

    async def abandon_message(self, queue_name: str, msg_id: str) -> None:
        """
        Sets locked_until = NULL so that the message becomes available again and delivery_count is incremented.
        """
        abandon_sql = f"UPDATE {queue_name} SET locked_until = NULL, delivery_count = delivery_count + 1 WHERE id = ?"
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute(abandon_sql, (msg_id,))
            await db.commit()

    async def initialize_queue(self, queue_name: str):
        """
        Method to initialize the table if it does not exist.
        It is recommended to call this when starting the application.
        """
        await self._init_queue(queue_name=queue_name)

    async def renew_lock(
        self, queue_name: str, msg_id: str, lock_seconds: int = 90
    ) -> bool:
        """
        Renews the lock on a specific message for another lock_seconds.
        Returns True if the message existed and was renewed, False otherwise.
        """
        update_sql = f"""
        UPDATE {queue_name}
        SET locked_until = datetime('now', '+{lock_seconds} seconds')
        WHERE id = ? AND locked_until > datetime('now')
        """
        async with aiosqlite.connect(self.db_path) as db:
            cursor = await db.execute(update_sql, (msg_id,))
            await db.commit()
            return cursor.rowcount > 0

    async def peek_messages(
        self, queue_name: str, max_message_count: int = 10
    ) -> List[LocalSqliteMessage]:
        """
        Retrieves up to max_message_count messages without locking them.
        """
        select_sql = f"""
        SELECT id, body, delivery_count
        FROM {queue_name}
        WHERE locked_until IS NULL
        LIMIT {max_message_count}
        """
        async with aiosqlite.connect(self.db_path) as db:
            cursor = await db.execute(select_sql)
            rows = await cursor.fetchall()
        return [
            LocalSqliteMessage(
                message_id=row[0], body=json.loads(row[1]), delivery_count=row[2]
            )
            for row in rows
        ]
