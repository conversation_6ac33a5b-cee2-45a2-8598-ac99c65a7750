import asyncio
import json
import logging
from typing import Any, Dict, List, Optional, Tuple, Union

from src.api.common.services.message_broker.local_sqlite_broker import (
    LocalSqliteBroker,
    LocalSqliteMessage,
)

from .queue_interface import QueueInterface


class LocalQueueInterface(QueueInterface):
    """
    This class simulates the ServiceBusQueueInterface, but uses
    LocalSqliteBrokerAsync internally. It is asynchronous and exposes similar methods
    (send_messages, receive_messages, complete_message, abandon_message, etc.).
    """

    RETRY_COUNT_NAME = "retry_count"

    def __init__(
        self, broker: LocalSqliteBroker, queue_name: str, logger: logging.Logger
    ):
        logger = logger.getChild(self.__class__.__name__)
        logger = logger.getChild(queue_name)
        super().__init__(queue_name=queue_name, logger=logger)
        self.broker = broker
        # Real table names cannot have dashes, so we replace them with underscores
        # Further sanitization might be needed
        self.sanitized_queue_name = queue_name.replace("-", "_")

    async def __aenter__(self):
        # Initialize the database (create the table if it doesn't exist)
        await self.broker.initialize_queue(self.sanitized_queue_name)
        return self

    async def __aexit__(self, exc_type, exc_value, traceback):
        # Nothing specific to close in aiosqlite
        pass

    async def send_messages(
        self,
        payloads: Union[Dict[str, Any], List[Dict[str, Any]]],
        delay_minutes: float = 0.1,
    ) -> List[str]:
        """
        Sends one or more dictionaries as 'messages' to the local database.
        Returns the list of IDs of the messages that were sent.
        """
        if isinstance(payloads, dict):
            payloads = [payloads]

        message_ids = []
        for payload in payloads:
            msg_id = await self.broker.send_message(
                queue_name=self.sanitized_queue_name,
                body=payload,
                delay_minutes=delay_minutes,
            )
            message_ids.append(msg_id)

        self.logger.info(
            f"[Local] {len(message_ids)} message(s) sent to '{self.queue_name}'"
        )
        return message_ids

    async def receive_messages(
        self, max_message_count: int = 1, max_wait_time: Optional[int] = None
    ) -> Tuple[bool, List[LocalSqliteMessage]]:
        """
        Receives up to max_message_count 'messages'.
        max_wait_time is ignored in this local implementation.

        Returns (bool, [messages]) with bool indicating if there were messages.
        Each 'message' is a dict with 'id' and 'body'.
        """
        messages = await self.broker.receive_messages(
            queue_name=self.sanitized_queue_name, max_message_count=max_message_count
        )

        return (len(messages) > 0, messages)

    async def complete_message(self, message: LocalSqliteMessage) -> None:
        """
        Marks the message as completed (deletes it from the table).
        """
        msg_id = message.message_id
        await self.broker.complete_message(
            queue_name=self.sanitized_queue_name, msg_id=msg_id
        )
        self.logger.info(f"[Local] Message {msg_id} completed in {self.queue_name}.")

    async def abandon_message(self, message: LocalSqliteMessage) -> None:
        """
        Abandon the message (locked_until = NULL) so it becomes available again.
        """
        msg_id = message.message_id
        await self.broker.abandon_message(
            queue_name=self.sanitized_queue_name, msg_id=msg_id
        )
        self.logger.info(f"[Local] Message {msg_id} abandoned in {self.queue_name}.")

    def get_message_retry_count(self, message: LocalSqliteMessage) -> int:
        """
        Reads the retry_count if it's in the body, otherwise returns 0.
        """
        return message.body.get(self.RETRY_COUNT_NAME, 0)

    def increment_message_retry_count(self, message: LocalSqliteMessage) -> None:
        """
        Increments the retry_count in the body.
        Note: this does not automatically persist in the database.
        Depending on your logic, you might want to perform an update if you want
        the database to reflect the change in the body.
        """
        body = message.body
        body[self.RETRY_COUNT_NAME] = body.get(self.RETRY_COUNT_NAME, 0) + 1

    async def renew_message_lock(self, message: LocalSqliteMessage) -> bool:
        """
        Renews the lock on a message for the specified time.
        Returns True if the lock was successfully renewed, False if it could not be (e.g., message already unlocked).
        """
        msg_id = message.message_id
        try:
            success = await self.broker.renew_lock(
                queue_name=self.sanitized_queue_name, msg_id=msg_id
            )
            if success:
                self.logger.info(
                    f"[Local] Message lock renewed for message {msg_id} in queue {self.queue_name}."
                )
            else:
                self.logger.warning(
                    f"[Local] Could not renew lock for message {msg_id} in {self.queue_name}"
                )
            return success
        except Exception as e:
            self.logger.error(
                f"[Local] Error renewing message lock on message {msg_id} from queue {self.queue_name}: {str(e)}"
            )
            return False

    async def dead_letter_message(
        self,
        message: LocalSqliteMessage,
        reason: str = "Processing Error",
        error_description: str = "Exceeded maximum retry attempts.",
    ) -> None:
        """
        Moves a message to the dead letter queue.
        """
        msg_id = message.message_id
        await self.broker.dead_letter_message(
            queue_name=self.sanitized_queue_name,
            msg_id=msg_id,
            reason=reason,
            error_description=error_description,
        )
        self.logger.info(
            f"[Local] Message {msg_id} moved to dead letter queue in {self.queue_name}."
        )

    async def peek_messages(
        self, max_message_count: int = 10
    ) -> List[LocalSqliteMessage]:
        return await self.broker.peek_messages(
            queue_name=self.sanitized_queue_name, max_message_count=max_message_count
        )

    @staticmethod
    def msg_to_dict(message: LocalSqliteMessage) -> Dict[str, Any]:
        """
        Converts a message object to a dictionary.
        """
        return json.loads(message.body)


# ----------------------------------------------------------------
# Ejemplo
# ----------------------------------------------------------------


async def main_example():
    logger = logging.getLogger("LocalServiceBusTest")
    logging.basicConfig(level=logging.INFO)

    broker = LocalSqliteBroker(db_path="tmp/db", logger=logger)

    async with LocalQueueInterface(broker, "test-name", logger) as queue:
        # Enviar mensajes
        await queue.send_messages({"type": "demo", "content": "hello world"})

        # Recibir mensajes
        has_msgs, msgs = await queue.receive_messages(max_message_count=2)
        if has_msgs:
            for msg in msgs:
                print("[RECIBIDO]", msg)
                # Completar el primero
                await queue.complete_message(msg)


if __name__ == "__main__":
    asyncio.run(main_example())
