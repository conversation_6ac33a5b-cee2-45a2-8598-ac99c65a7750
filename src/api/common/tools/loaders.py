try:
    import asyncio
    import os
    import tempfile

    import markdown
    import pymupdf
    import requests
except ImportError as _:
    pass
from abc import ABC, abstractmethod
from typing import Any
from urllib.parse import urlparse

import tiktoken
from pydantic import BaseModel


class Toc(BaseModel):
    level: int
    name: str
    page: int


class TextSegment(BaseModel):
    text: str
    page_number: int
    metadata: dict[str, Any]
    file_name: str | None
    start_char: int
    end_char: int
    tokens: int


TEXT_ERROR = "Text is not extracted yet."


class DocumentLoader(ABC):
    @abstractmethod
    async def extract_text(self):
        """
        Extract all the text inside a certain document.
        Saves it inside self.text
        """
        pass

    @abstractmethod
    async def extract_metadata(self):
        """
        Extract all the metadata related to a certain document.
        Saves it inside self.metadata
        """
        pass

    @abstractmethod
    async def extract_toc(self):
        """
        Extract the table of contents/structure of a certain document.
        Saves it inside self.toc
        """
        pass

    @abstractmethod
    async def get_results(self) -> dict[str, Any]:
        """
        Get the results of the extraction
        """
        pass

    @abstractmethod
    async def extract_text_segments(self) -> dict[str, Any]:
        """
        Extract text segments, initially defined (by pages) or other segmentation attributes.
        """
        pass

    @abstractmethod
    async def extract_all(self):
        """
        Perform all the extractions available
        """
        pass

    @abstractmethod
    async def count_tokens(self):
        """
        Count the total tokens of the full document.
        """
        pass


class PdfLoader(DocumentLoader):
    _temp_dir = os.path.join(os.getcwd(), "tmp")
    _temp_files = set()
    os.makedirs(_temp_dir, exist_ok=True)

    def __init__(self, doc_path, blob_store=None, db_session=None):
        self.doc_path = doc_path
        self.doc = None
        self.blob_store = blob_store
        self.db_session = db_session
        self.text = None
        self.toc = []
        self._is_temp = doc_path in self._temp_files
        self.metadata = {}
        self.text_segments = []
        self.total_tokens = -1
        self.token_counter = tiktoken.encoding_for_model("gpt-4o")

    @staticmethod
    def remove_null_characters(s):
        return s.replace("\x00", "") if isinstance(s, str) else s

    @classmethod
    def from_url(cls, url, filetype="pdf", additional_metadata: dict[str, str] = None):
        """Attempts to download a pdf from a url online"""
        response = requests.get(url)
        if response.status_code != 200:
            print(f"Status code not 200. Received: {response.status_code}")
            raise ValueError(f"Failed to download document from {url}")

        parsed_url = urlparse(url)
        original_filename = os.path.basename(parsed_url.path)
        _, ext = os.path.splitext(original_filename)

        if ext.lower() != f".{filetype.lower()}":
            original_filename = f"{original_filename}.{filetype}"

        temp_file = tempfile.NamedTemporaryFile(
            delete=False, suffix=f".{filetype}", dir=cls._temp_dir
        )
        temp_path = temp_file.name
        with temp_file as file:
            file.write(response.content)

        cls._temp_files.add(temp_path)

        instance = cls(temp_path)
        try:
            instance.doc = pymupdf.open(temp_path)
            instance.metadata = {
                "origin_url": url,
                "filename": cls.remove_null_characters(original_filename),
            }
            if additional_metadata:
                instance.metadata.update(
                    {
                        k: cls.remove_null_characters(v)
                        for k, v in additional_metadata.items()
                    }
                )
        except Exception as e:
            print(f"An exception has occurred while loading pdf: {e}")
            raise
        return instance

    async def get_doc(self):
        if self.doc is None:
            try:
                self.doc = pymupdf.open(self.doc_path)
                self.metadata["filename"] = self.remove_null_characters(self.doc.name)
            except Exception as e:
                print(f"Exception occurred loading pdf: {e}")
                raise
        return self

    async def extract_text(self):
        if not self.doc:
            await self.get_doc()
        self.text = self.remove_null_characters(
            "".join([page.get_text().strip() for page in self.doc])
        )
        return self

    async def extract_toc(self):
        if not self.doc:
            await self.get_doc()
        try:
            toc_data = []
            for toc_item in self.doc.get_toc():
                if len(toc_item) >= 3:  # Ensure we have at least level, title, and page
                    level = int(toc_item[0]) if toc_item[0] is not None else 0
                    name = (
                        self.remove_null_characters(str(toc_item[1]))
                        if toc_item[1] is not None
                        else ""
                    )
                    page = int(toc_item[2]) if toc_item[2] is not None else 0
                    toc_data.append(Toc(level=level, name=name, page=page).model_dump())
            self.toc = toc_data
        except Exception as e:
            print(f"Error extracting TOC: {e}")
            self.toc = []
        return self

    async def extract_metadata(self):
        if not self.doc:
            await self.get_doc()
        metadata = self.doc.metadata | self.metadata
        self.metadata = {k: self.remove_null_characters(v) for k, v in metadata.items()}
        return self

    async def extract_all(self):
        await self.extract_metadata()
        if self.doc:
            await self.extract_text()
            await self.extract_toc()
            await self.extract_text_segments()
            await self.count_tokens()
        else:
            print(
                f"Cannot extract from document: {self.metadata.get('error', 'Unknown error')}"
            )
        return self

    async def extract_text_segments(self):
        text_segments = []
        if not self.metadata:
            await self.extract_metadata()
        if not self.doc:
            await self.get_doc()
        file_name = self.metadata.get("filename")
        char_count = 0
        for num, page in enumerate(self.doc, start=1):
            text = self.remove_null_characters(page.get_text("text").strip())
            if not text:
                print(f"Page {num} in document appears to be empty")
            segment_metadata = self.metadata.copy()
            segment_metadata.update({"page": str(num)})
            segment = TextSegment(
                text=text,
                page_number=num,
                metadata=segment_metadata,
                file_name=file_name,
                start_char=char_count,
                end_char=char_count + len(text),
                tokens=0,
            )
            text_segments.append(segment)
            char_count += len(text)
        if not text_segments:
            print(f"No text content found in document {file_name}")
        self.text_segments = text_segments
        return self

    def cleanup(self):
        if self.doc:
            self.doc.close()
            self.doc = None
        if self._is_temp and self.doc_path:
            try:
                os.remove(self.doc_path)
                self._temp_files.remove(self.doc_path)
                self.doc_path = None
            except OSError as e:
                print(f"Error deleting temporary file {self.doc_path}: {e}")

    def __del__(self):
        self.cleanup()

    @classmethod
    def cleanup_all(cls):
        for temp_file in list(cls._temp_files):
            try:
                os.remove(temp_file)
                cls._temp_files.remove(temp_file)
            except OSError as e:
                print(f"Error deleting temporary file {temp_file}: {e}")

    async def get_results(self):
        return {
            "text": self.text,
            "metadata": self.metadata,
            "toc": self.toc,
            "text_segments": self.text_segments,
            "total_tokens": self.total_tokens,
        }

    async def count_tokens(self):
        if not self.text:
            await self.extract_text()
        self.total_tokens = len(
            self.token_counter.encode(self.text, disallowed_special=())
        )
        return self


class WebsiteLoader(DocumentLoader):
    def __init__(
        self,
        url: str,
        text: str = "",
        toc: list[Toc] | None = None,
        metadata: dict[str, str | None] = None,
        text_segments: list[TextSegment] | None = None,
        total_tokens: int = -1,
        url_extractor: Any | None = None,
    ):
        from logging import Logger

        logger = Logger(__name__)
        self._doc_url = url
        self._text = text
        self._toc = toc if toc is not None else []
        self._metadata = metadata if metadata is not None else {}
        self._text_segments = text_segments if text_segments is not None else []
        self._total_tokens = total_tokens
        self._token_counter = tiktoken.encoding_for_model("gpt-4o")
        from src.api.common.services.url_extractor_engine import JinaExtractor

        self._url_extractor = (
            url_extractor
            if url_extractor
            else JinaExtractor(logger=logger, api_key=os.getenv("JinaApiKey"))
        )

    async def extract_text(self) -> "WebsiteLoader":
        if self._url_extractor is None:
            raise ValueError("URL extractor is not provided.")
        content = (await self._url_extractor.extract([self._doc_url]))[0]
        if content.status == "error":
            raise ValueError(f"Error extracting content from URL: {content.error}")
        self._text = content.content
        return self

    async def extract_metadata(self) -> "WebsiteLoader":
        if not self._text:
            raise ValueError(TEXT_ERROR)
        # Configuramos Markdown con las extensiones necesarias.
        md = markdown.Markdown(extensions=["meta"])

        try:
            _ = md.convert(self._text)
        except Exception as _:
            return self
        metadata_raw = md.Meta if hasattr(md, "Meta") else {}
        if metadata_raw == {}:
            return self
        self._metadata = {key: " ".join(value) for key, value in metadata_raw.items()}
        return self

    @classmethod
    def _flatten_toc_tokens(cls, toc_tokens) -> list[Toc]:
        toc_list = []
        for token in toc_tokens:
            toc_item = Toc(
                level=token.get("level", 1), name=token.get("name", ""), page=-1
            )
            toc_list.append(toc_item)
            if "children" in token:
                toc_list.extend(cls._flatten_toc_tokens(token["children"]))
        return toc_list

    async def extract_toc(self) -> "WebsiteLoader":
        if not self._text:
            raise ValueError(TEXT_ERROR)

        md = markdown.Markdown(extensions=["meta"])

        _ = md.convert(self._text)

        toc_tokens = md.toc_tokens if hasattr(md, "toc_tokens") else []
        if toc_tokens == []:
            return self
        self._toc = self._flatten_toc_tokens(toc_tokens)
        return self

    async def get_results(self) -> dict[str, Any]:
        return {
            "text": self._text,
            "metadata": self._metadata,
            "toc": self._toc,
            "text_segments": self._text_segments,
            "total_tokens": self._total_tokens,
        }

    async def extract_text_segments(self) -> "WebsiteLoader":
        if not self._text:
            raise ValueError(TEXT_ERROR)
        acum_text = ""
        lines = self._text.splitlines()
        page_counter = 1
        curr_char = 0
        for line in lines:
            if line.strip() != "":
                acum_text += line.strip() + "\n"
                curr_char += len(line.strip()) + 1

            current_tokens = len(
                self._token_counter.encode(acum_text, disallowed_special=())
            )
            if current_tokens > 1000:
                self._text_segments.append(
                    TextSegment(
                        text=acum_text,
                        page_number=page_counter,
                        metadata={},
                        file_name=self._doc_url,
                        start_char=curr_char - len(acum_text),
                        end_char=curr_char,
                        tokens=current_tokens,
                    )
                )
                acum_text = ""
                page_counter += 1
        if acum_text != "":
            self._text_segments.append(
                TextSegment(
                    text=acum_text,
                    page_number=page_counter,
                    metadata={},
                    file_name=self._doc_url,
                    start_char=curr_char - len(acum_text),
                    end_char=curr_char,
                    tokens=current_tokens,
                )
            )
        return self

    async def extract_all(self, extract_text=False) -> "WebsiteLoader":
        if extract_text:
            await self.extract_text()

        await asyncio.gather(
            self.extract_metadata(),
            self.extract_toc(),
            self.extract_text_segments(),
            self.count_tokens(),
        )

        return self

    async def count_tokens(self) -> "WebsiteLoader":
        if not self._text:
            raise ValueError(TEXT_ERROR)
        self._total_tokens = len(
            self._token_counter.encode(self._text, disallowed_special=())
        )
        return self
