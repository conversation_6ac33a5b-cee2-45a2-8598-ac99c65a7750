from __future__ import annotations

import base64
import json
import re
import unicodedata
from typing import TYPE_CHECKING, Any, Optional

import httpx
from langchain.chat_models.base import BaseChatModel
from langchain.prompts import PromptTemplate
from openai import AsyncOpenAI
from pydantic import BaseModel, Field

from src.api.common.services.structs import DetailedGeneratedContent

if TYPE_CHECKING:
    from ia_gen_core.prompts import PromptManager

    from src.domain.models import Epigrafe


class InvalidMermaidError(Exception):
    def __init__(self, message):
        super().__init__(message)


class AugmentedSearch(BaseModel):
    reasoning: str
    "Reasoning about how to improve the original query to a one more reelevant for a search engine on our vector store"
    queries: list[str]
    "Reframed queries results."


def extract_content(text: str, tag_name: str):
    pattern = rf"<{tag_name}>(.*?)</{tag_name}>"
    matches = re.findall(pattern, text, re.DOTALL)
    return matches


def encode_mermaid(mermaid_code: str):
    """
    Encode Mermaid code for use in URL.
    """
    json_obj = {"code": mermaid_code, "mermaid": {"theme": "default"}}
    json_str = json.dumps(json_obj)
    return (
        base64.urlsafe_b64encode(json_str.encode("utf-8")).decode("utf-8").rstrip("=")
    )


def get_mermaid_png_url(
    mermaid_code: str,
    base_url: str,
    width: Optional[int] = None,
    height: Optional[int] = None,
):
    encoded_code = encode_mermaid(mermaid_code)
    png_url = f"{base_url}{encoded_code}"
    if width and height:
        png_url += f"?width={width}&height={height}"
    return png_url


def format_url_markdown(content: str, base_url: str, alt_text: str = "Figure"):
    url = get_mermaid_png_url(content, base_url)
    markdown = f"![{alt_text}]({url})\n"
    return markdown


async def validate_mermaid(mermaid: str, base_url: str):
    encoded_mermaid = encode_mermaid(mermaid)
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{base_url}{encoded_mermaid}")
    except Exception:
        raise
    if response.status_code != 200:
        response_content = response.content
        status_code = response.status_code
        raise InvalidMermaidError(
            f"Diagram validation failed, status code: {status_code}, "
            f"the detail of message is: {response_content}. "
            f"The original mermaid provided was: {mermaid}"
        )
    return mermaid


class ValidDiagram(BaseModel):
    diagram: str = Field(description="Diagram following the mermaid specification")


async def correct_diagram(
    client: AsyncOpenAI,
    error_message: str,
    model: str = "o4-mini",
    documentation: str | None = None,
) -> ValidDiagram:
    system_prompt_correct_diagrams = """
    You correct and return valid mermaid diagrams.
    If overly complex, try to simplify it while retaining meaning.
    Refer to the documentation for valid examples, it will be provided to you below if the diagram matches any of the ones provided in the documentation, if not provided refer to your knowledge.

    Things to look for:

    Carefully think about the mermaid standard and specification and indentify incorrectly formated parts, normally the error message includes a hint about where is the problem.
    Be careful about special characters like quotes or double quotes and make sure there are not more than needed.
    Be careful of excessive amounts of text in diagrams that could look bad if filled with text.

    Final result:
    The final output should be a correct and valid diagram, following the mermaid spec.
    """

    try:
        response = await client.beta.chat.completions.parse(
            model=model,
            messages=[
                {
                    "role": "system",
                    "content": system_prompt_correct_diagrams
                    + (
                        f"Mermaid documentation: \n{documentation}"
                        if documentation
                        else ""
                    ),
                },
                {
                    "role": "user",
                    "content": f"There has been an error validating a mermaid diagram, please correct the diagram: {error_message}",
                },
            ],
            reasoning_effort="high",
            response_format=ValidDiagram,
        )
    except Exception:
        raise
    return response.choices[0].message.parsed


async def validate_and_retry_mermaid(
    client: "AsyncOpenAI",
    mermaid: str,
    mermaid_base_url: str,
    total_retries: int = 4,
    model: str = "o4-mini",
    documentation: str | None = None,
) -> tuple[str, bool]:
    final_mermaid = None
    mermaid_to_validate = mermaid
    tries = 0

    while tries <= total_retries:
        try:
            tries += 1
            final_mermaid = await validate_mermaid(
                mermaid_to_validate, mermaid_base_url
            )
            break
        except httpx.ReadTimeout:
            return mermaid, False
        except Exception as e:
            try:
                if tries > 2:
                    valid_diagram = await correct_diagram(
                        client, str(e), model, documentation=documentation
                    )
                else:
                    valid_diagram = await correct_diagram(client, str(e), model)
                mermaid_to_validate = valid_diagram.diagram
                continue
            except Exception as e:
                return mermaid, False

    if final_mermaid is not None:
        return final_mermaid, True
    else:
        return mermaid, False


def _replace_diagrams(contenido_actualizado, metadata):
    if metadata and metadata.diagrams and len(metadata.diagrams) > 0:
        for j, diagram in enumerate(metadata.diagrams):
            if metadata.diagrams_urls and j < len(metadata.diagrams_urls):
                contenido_actualizado = contenido_actualizado.replace(
                    metadata.diagrams_urls[j], f"<mermaid>{diagram}</mermaid>"
                )
    return contenido_actualizado


def convert_content_for_llm(
    content_list: list[DetailedGeneratedContent],
) -> list[DetailedGeneratedContent]:
    for i, cont in enumerate(content_list):
        contenido_actualizado = cont.content
        contenido_actualizado = _replace_diagrams(contenido_actualizado, cont.metadata)
        content_list[i].content = contenido_actualizado
    return content_list


async def augment_user_query(
    original_query: str,
    epigraph: Epigrafe,
    prompt_manager: PromptManager,
    llm: BaseChatModel,
) -> list[str]:
    prompt = prompt_manager.get_prompt(name="augment-paragraph-user-query")
    aggregated_context = f"""
        This is the context of the subject in order, from the top of the hieararchy of the subject structure:

        Subject name -> {epigraph.tema.bloque.indice.order.title_subject.name}
        # Block name -> {epigraph.tema.bloque.name}
        ## Topic name -> {epigraph.tema.name}
        ### Epigraph name -> {epigraph.name}

        We want to write about a paragraph that is part of : {epigraph.name}
        """

    lc_template = prompt.compile().to_langchain()
    template = PromptTemplate.from_template(lc_template)
    llm = llm.with_structured_output(AugmentedSearch)
    chain = template | llm
    result = await chain.ainvoke(
        {"query": original_query, "context": aggregated_context}
    )
    return result.queries


def sanitize_string(s: Any) -> Any:
    """
    Comprehensive string sanitization for document processing.

    Handles:
    - NULL bytes removal (prevents database errors)
    - Control characters removal
    - Unicode normalization
    - Excessive whitespace cleanup
    - Invalid encoding handling
    - Length limits for database fields
    """
    if s is None:
        return None
    if not isinstance(s, str):
        return s
    try:
        sanitized = s.replace("\x00", "")
        sanitized = re.sub(r"[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]", "", sanitized)
        sanitized = unicodedata.normalize("NFC", sanitized)
        sanitized = "".join(
            char
            for char in sanitized
            if unicodedata.category(char)[0] != "C" or char in "\n\r\t"
        )
        sanitized = re.sub(r" {2,}", " ", sanitized)
        sanitized = re.sub(r"\n{3,}", "\n\n", sanitized)
        sanitized = re.sub(r"\t{2,}", "\t", sanitized)
        sanitized = sanitized.strip()
        if not sanitized:
            return None
        sanitized = sanitized.encode("utf-8", errors="ignore").decode("utf-8")
        return sanitized
    except Exception:
        if isinstance(s, str):
            fallback = s.replace("\x00", "").replace("\x01", "").replace("\x02", "")
            return fallback
