from functools import wraps
from typing import Any, Callable, Dict, TypeVar

from pydantic import BaseModel

T = TypeVar("T", bound=BaseModel)


def tool(schema: BaseModel, name: str, description: str):
    def decorator(func):
        assert issubclass(schema, BaseModel), (
            "The schema must be an instance of Pydantic BaseModel"
        )

        @wraps(func)
        def wrapper(*args, **kwargs):
            return func(*args, **kwargs)

        wrapper._is_tool = True
        wrapper._schema = schema
        wrapper._tool_name = name
        wrapper._tool_description = description
        return wrapper

    return decorator


def get_tools():
    tools = []
    for name, obj in globals().items():
        if callable(obj) and hasattr(obj, "_is_tool"):
            tools.append(obj)
    return tools


def format_function(
    function: Callable[..., T], provider: str = "openai"
) -> Dict[str, Any]:
    if provider not in ["anthropic", "openai"]:
        raise ValueError("Provider not accepted")
    if provider == "openai":
        schema = function._schema.model_json_schema()
        return {
            "type": "function",
            "function": {
                "name": function.__name__,
                "description": function._tool_description,
                **schema,
            },
        }
    elif provider == "anthropic":
        schema = function._schema.model_json_schema()
        del schema["title"]
        for p in schema["properties"]:
            del schema["properties"][p]["title"]
        return {
            "name": function.__name__,
            "description": function._tool_description,
            "input_schema": schema,
        }
