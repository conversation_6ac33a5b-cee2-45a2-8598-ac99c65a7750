import asyncio
import inspect
import ssl
from typing import Any, Dict, List, Optional, Tuple

import certifi
import httpx
import tiktoken
from anthropic import AsyncAnthropic
from pydantic import AnyUrl, BaseModel, Field, ValidationError

from src.api.common.dependency_container import DependencyContainer

try:
    import instructor
except ImportError:
    pass


class ValidUrl(BaseModel):
    url: AnyUrl


class UserQuery(BaseModel):
    query: str = Field(
        min_length=3, max_length=200
    )  # Only queries up to 200 characters allowed.


class RetrievedDoc(BaseModel):
    id: int = Field(ge=0, le=4)
    title: str


class RetrievedDocs(BaseModel):
    docs: List[RetrievedDoc]


class ReaderResponse(BaseModel):
    query: str | None = Field(default=None)
    title: Optional[str] = Field(default=None)
    content: Optional[str] = Field(default=None)
    published_time: Optional[str] = Field(alias="PublishedTime", default=None)
    description: Optional[str] = None
    url: Optional[AnyUrl] = None
    links: str | Dict[str, str] = ""
    images: str | Dict[str, str] = ""
    tokens: int = Field(default=0)

    def __init__(self, **data):
        super().__init__(**data)
        self._calculate_tokens()

    def _calculate_tokens(self) -> None:
        title = self.title or ""
        content = self.content or ""
        if title or content:
            enc = tiktoken.get_encoding("o200k_base")
            encoded_tokens = enc.encode(title + " " + content)
            object.__setattr__(self, "tokens", len(encoded_tokens))

    def __setattr__(self, name, value):
        super().__setattr__(name, value)
        if name in ("title", "content"):
            self._calculate_tokens()


class SearchResults(BaseModel):
    results: List[ReaderResponse] | None = None
    number_results: int | None = None


class JinaWebTools:
    """Tool that uses jina ai to read and search in the web"""

    # Add embedded urls, add embedded images
    # TODO: Include a rate limit counter, or include retries for searches.
    # TODO: Utilities for the response such as getting all urls from the content or other markdown attributes we can find useful

    def __init__(
        self,
        api_key: str | None = None,
        include_image_caption=True,
        include_links=True,
        include_images=True,
    ):
        """Initialize the JinaWebTools with an optional API key."""
        self._jina_api_key: str | None = (
            api_key or DependencyContainer._application_settings.JINA_API_KEY
        )
        self.reader_base_url: str = "https://r.jina.ai/"
        self.search_base_url: str = "https://s.jina.ai/"
        self.headers: Dict[str, str] = {"Accept": "application/json", "X-Timeout": "10"}
        self.ssl_context = ssl.create_default_context(cafile=certifi.where())
        if self._jina_api_key is not None:
            self.headers["Authorization"] = f"Bearer {self._jina_api_key}"
        else:
            print(
                "Running search without API key, note that rate limit can be limited, get your api key at https://jina.ai/reader/ or make sure your env variable is provided"
            )
        if include_image_caption:
            self.headers["X-With-Generated-Alt"] = "true"
        if include_links:
            self.headers["X-With-Links-Summary"] = "true"
        if include_images:
            self.headers["X-With-Images-Summary"] = "true"
        self._timeout = httpx.Timeout(100.0, connect=60.0)

    # TODO: Make the async method aread_url
    def read_url(self, url: AnyUrl) -> Tuple[ReaderResponse, Any]:
        """Read the content of a URL using the Jina AI reader."""
        try:
            requested_url = str(ValidUrl(url=url).url)
        except ValidationError:
            raise ValueError("URL is not valid")

        with httpx.Client(timeout=self._timeout) as client:
            response = client.get(
                url=self.reader_base_url + requested_url,
                headers=self.headers,
                ssl=self.ssl_context,
            )
        try:
            if response.status_code == 200:
                reader_response = response.json()
                formatted_response = self._format_response(reader_response)
                return formatted_response, reader_response
            else:
                raise httpx.HTTPStatusError(
                    f"Invalid response: {response.status_code}",
                    request=response.request,
                    response=response,
                )
        except Exception as e:
            raise httpx.RequestError(f"Exception occurred while reading url: {e}")
        # TODO: Do timeout for request, if not quick request again.

    def search_web(
        self,
        query: str,
        url: AnyUrl = None,
        exclude_url: AnyUrl = None,
        filetype: str = None,
        exact_match: str = None,
        title_matches: str = None,
        match_similar: str = None,
        boost_term: str = None,
        url_includes: str = None,
        decrease_term: str = None,
    ) -> SearchResults:
        """Search the web using the Jina AI search engine."""
        try:
            query = UserQuery(query=query).query
        except ValidationError:
            raise ValueError(
                "Length of query must be minimum 3 characters and a maximum of 200 characters."
            )

        query = self._construct_query(
            query,
            url,
            exclude_url,
            filetype,
            exact_match,
            title_matches,
            match_similar,
            boost_term,
            url_includes,
            decrease_term,
        )

        with httpx.Client(timeout=self._timeout) as client:
            response = client.get(
                url=self.search_base_url + query,
                headers=self.headers,
                ssl=self.ssl_context,
            )

        if response.status_code == 200:
            writer_response = response.json()
            return self._format_response(writer_response, query)
        else:
            print(f"Invalid response: {response.status_code}")
            return SearchResults([], 0)

    async def asearch_web(
        self,
        query: str,
        url: AnyUrl = None,
        exclude_url: AnyUrl = None,
        filetype: str = None,
        exact_match: str = None,
        title_matches: str = None,
        match_similar: str = None,
        boost_term: str = None,
        url_includes: str = None,
        decrease_term: str = None,
    ) -> SearchResults:
        """Asynchronously search the web using the Jina AI search engine."""
        try:
            query = UserQuery(query=query).query
        except ValidationError:
            raise ValueError(
                "Length of query must be minimum 3 characters and a maximum of 200 characters."
            )

        # Construct the query string (same as in the synchronous method)
        query = self._construct_query(
            query,
            url,
            exclude_url,
            filetype,
            exact_match,
            title_matches,
            match_similar,
            boost_term,
            url_includes,
            decrease_term,
        )

        async with httpx.AsyncClient(
            timeout=self._timeout, verify=self.ssl_context
        ) as client:
            response = await client.get(
                url=self.search_base_url + query, headers=self.headers
            )

        if response.status_code == 200:
            writer_response = response.json()
            return self._format_response(writer_response, query)
        else:
            print(f"Invalid response: {response.status_code}")
            return SearchResults([], 0)

    async def aread_url(self, url: AnyUrl) -> Tuple[ReaderResponse, Any]:
        """Asynchronously read the content of a URL using the Jina AI reader."""
        try:
            requested_url = str(ValidUrl(url=url).url)
        except ValidationError:
            raise ValueError("URL is not valid")

        async with httpx.AsyncClient(
            timeout=self._timeout, verify=self.ssl_context
        ) as client:
            response = await client.get(
                url=self.reader_base_url + requested_url, headers=self.headers
            )

        if response.status_code == 200:
            reader_response = response.json()
            formatted_response = self._format_response(reader_response)
            return formatted_response, reader_response
        else:
            raise httpx.HTTPStatusError(
                f"Invalid response: {response.status_code}",
                request=response.request,
                response=response,
            )

    def _format_response(self, response, query=None) -> Any:
        calling_method = inspect.stack()[1].function
        if calling_method in ["read_url", "aread_url"]:
            return ReaderResponse(**response["data"])
        if calling_method in ["search_web", "asearch_web"]:
            for r in response["data"]:
                r["query"] = query
            search_results = SearchResults(
                results=response["data"], number_results=len(response["data"])
            )
            return search_results

    def _construct_query(
        self,
        query: str,
        url: AnyUrl = None,
        exclude_url: AnyUrl = None,
        filetype: str = None,
        exact_match: str = None,
        title_matches: str = None,
        match_similar: str = None,
        boost_term: str = None,
        url_includes: str = None,
        decrease_term: str = None,
    ) -> str:
        """Construct the query string with all provided parameters."""
        if url:
            query += f" site:{url}"
        if exclude_url:
            query += f" -site:{exclude_url}"
        if filetype:
            query += f" filetype:{filetype}"
        if exact_match:
            query += f' "{exact_match}"'
        if title_matches:
            query += f" intitle:{title_matches}"
        if match_similar:
            query += f' ~"{match_similar}"'
        if boost_term:
            query += f" +{boost_term}"
        if url_includes:
            query += f" inurl:{url_includes}"
        if decrease_term:
            query += f" -{decrease_term}"
        return query


class WebSearchPostprocessor:
    def __init__(self):
        self.client = instructor.from_anthropic(
            AsyncAnthropic(), mode=instructor.Mode.ANTHROPIC_JSON
        )
        self.search_client = JinaWebTools()

    async def filter_results(self, results: SearchResults):
        retrieved_docs = RetrievedDocs(
            docs=[
                RetrievedDoc(id=i, title=result.title)
                for i, result in enumerate(results.results)
            ]
        )
        user_query = results.results[
            0
        ].query  # Assuming the set of docs are from the same query
        filtered_docs = await self.client.chat.completions.create(
            model="claude-3-haiku-20240307",
            max_tokens=1000,
            response_model=RetrievedDocs,
            messages=[
                {
                    "role": "system",
                    "content": "A user has used a search tool that provides some up to date information to answer a question. Your task is to find which of the search results are actually relevant given the user query. You will be given a list of results with id and title and you fill filter and return the most reelevant ones for the user question",
                },
                {
                    "role": "user",
                    "content": f"This was the user query: <query>{user_query}</query> and this were the docs that were retrieved from the user question <retrieved docs>{retrieved_docs.json()}</retrieved docs>. Return the docs that are really reelevant in json: ",
                },
            ],
        )
        filtered_docs_ids = [f.id for f in filtered_docs.docs]
        results = [
            result for i, result in enumerate(results.results) if i in filtered_docs_ids
        ]
        updated_results = await asyncio.gather(
            *[self._get_content(result) for result in results]
        )
        content_results = [
            result for result in updated_results if result.content is not None
        ]
        return content_results

    async def _get_content(self, result):
        if result.content is None and hasattr(result, "url"):
            try:
                response, _ = await self.search_client.aread_url(result.url)
                result.content = response.content
            except Exception as e:
                print(f"An exception occurred while reading URL {result.url}: {e}")
        return result

    def format_verified_context(
        self, results: List[ReaderResponse], context_limit: int = 100000
    ) -> List[str]:
        verified_results = []
        current_context = []
        current_tokens = 0

        for i, result in enumerate(results):
            result_context = [
                f"-------------------------------------<<Verified source {i}>>-------------------------------------",
                f"<<Source id>>: {i}",
                f"<<Title>>: {result.title}",
                f"<<Content>>: {result.content}",
            ]
            result_tokens = result.tokens

            if current_tokens + result_tokens > context_limit and current_context:
                verified_results.append("\n".join(current_context))
                current_context = []
                current_tokens = 0

            current_context.extend(result_context)
            current_tokens += result_tokens

        if current_context:
            verified_results.append("\n".join(current_context))

        # TODO: If len > 100k, synthesize into one as context.
        return verified_results
