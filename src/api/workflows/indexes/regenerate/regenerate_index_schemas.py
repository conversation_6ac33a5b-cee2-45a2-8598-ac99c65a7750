from typing import Optional

from pydantic import BaseModel
from src.api.common.services.structs import (
    AIProcessInfo,
    Asignatura,
    BloqueTematico,
    ModelInfo,
    Tema,
)


class RegenerateIndexSchemaRequest(BaseModel):
    order_id: int | None = None
    comentario: str
    model_info: Optional[ModelInfo] = None


class RegenerateBlockSchemaRequest(BaseModel):
    order_id: int | None = None
    bloque_id: int
    comentario: str
    model_info: Optional[ModelInfo] = None


class RegenerateTopicSchemaRequest(BaseModel):
    order_id: int | None = None
    tema_id: int
    comentario: str
    model_info: Optional[ModelInfo] = None


class RegenerateIndexSchemaResponse(BaseModel):
    index: Asignatura
    processes_info: list[AIProcessInfo]


class RegenerateBlockSchemaResponse(BaseModel):
    bloque: BloqueTematico
    processes_info: list[AIProcessInfo]


class RegenerateTopicSchemaResponse(BaseModel):
    tema: Tema
    processes_info: list[AIProcessInfo]
