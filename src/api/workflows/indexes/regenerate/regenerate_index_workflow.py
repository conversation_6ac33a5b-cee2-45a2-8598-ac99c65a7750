from logging import Logger
from typing import Optional, Union

from fastapi import HTTPException, status
from sqlalchemy import Engine
from sqlmodel import Session, select
from src.api.common.services import AI<PERSON>racer, ContentRegenerator
from src.api.common.services.content_regenerator.prompt_input_schemas import (
    RegenerateIndexInput,
)
from src.api.common.services.index_repository import IndexRepository
from src.api.common.services.structs import (
    AIProcessInfo,
    Asignatura,
    AsignaturaReasoning,
    BloqueTematico,
    BloqueTematicoReasoning,
    Competencias,
    PromptContext,
    TemaReasoning,
)
from src.api.common.services.structs import (
    Tema as TemaStruct,
)
from src.domain.models import (
    AIProcess,
    AIProcessType,
    Bloque,
    Indice,
    Order,
    Tema,
    TitleSubject,
)

from .regenerate_index_schemas import (
    RegenerateBlockSchemaRequest,
    RegenerateBlockSchemaResponse,
    RegenerateIndexSchemaRequest,
    RegenerateIndexSchemaResponse,
    RegenerateTopicSchemaRequest,
    RegenerateTopicSchemaResponse,
)


class RegenerateIndexWorkflow:
    def __init__(
        self,
        db_engine: Engine,
        logger: Logger,
        content_regenerator: ContentRegenerator,
        index_repository: IndexRepository,
        ai_tracer: AITracer,
    ) -> None:
        self._db_engine = db_engine
        self._logger = logger
        self._content_regenerator = content_regenerator
        self._index_repository = index_repository
        self._ai_tracer = ai_tracer

    async def execute(
        self,
        request: Union[
            RegenerateIndexSchemaRequest,
            RegenerateBlockSchemaRequest,
            RegenerateTopicSchemaRequest,
        ],
    ) -> Union[
        RegenerateIndexSchemaResponse,
        RegenerateBlockSchemaResponse,
        RegenerateTopicSchemaResponse,
    ]:
        self._logger.info(f"Request regenerate_index_workflow: {request}")
        tema_id, bloque_id = None, None
        if isinstance(request, RegenerateBlockSchemaRequest):
            bloque_id = request.bloque_id
        elif isinstance(request, RegenerateTopicSchemaRequest):
            tema_id = request.tema_id

        indice = await self._index_repository.get_latest_index(request.order_id)
        asignatura = await self._index_repository.get_subject_index(indice.id)
        title_subject, bloque, tema = self._get_subject_details(
            indice.id, bloque_id, tema_id
        )

        competencies = await self._index_repository.get_competencies(indice.id)

        input_data = self.prepare_input_data(
            request, asignatura, competencies, title_subject, bloque, tema, indice.id
        )

        try:
            result, process = await self.regenerate_index(input_data)
        except Exception as e:
            self._logger.exception(f"Failed to regenerate index: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to regenerate index: {e}",
            )
        return self.prepare_response(result, tema, bloque, process)

    async def regenerate_index(
        self, input_data: RegenerateIndexInput
    ) -> tuple[Optional[Union[Asignatura, BloqueTematico, Tema]], AIProcess]:
        if input_data.tema:
            prompt_name = "regenerate-topic-schema-instructions"
            expected_llm_output = TemaReasoning
            process_type = AIProcessType.SCHEMA_TOPIC_REGENERATION

            def convert_result(r):
                return TemaStruct(nombre=r.tema.nombre, epigrafes=r.tema.epigrafes)
        elif input_data.bloque:
            prompt_name = "regenerate-block-schema-instructions"
            expected_llm_output = BloqueTematicoReasoning
            process_type = AIProcessType.SCHEMA_BLOCK_REGENERATION

            def convert_result(r):
                return BloqueTematico(
                    nombre=r.bloque_tematico.nombre, temas=r.bloque_tematico.temas
                )
        else:
            prompt_name = "regenerate-index-schema-instructions"
            expected_llm_output = AsignaturaReasoning
            process_type = AIProcessType.SCHEMA_REGENERATION

            def convert_result(r):
                return Asignatura(
                    nombre=r.asignatura.nombre, estructura=r.asignatura.estructura
                )

        final_result, process = await self._content_regenerator._regenerate(
            input_data=input_data,
            prompt_name=prompt_name,
            expected_llm_output=expected_llm_output,
            process_type=process_type,
            convert_result=convert_result,
            indice_id=getattr(input_data.prompt_context, "id_indice", None),
            tema_id=getattr(input_data.prompt_context, "id_tema", None),
        )

        return final_result, process

    def prepare_response(
        self,
        result: Union[Asignatura, BloqueTematico, TemaStruct],
        tema: Optional[TemaStruct],
        bloque: Optional[BloqueTematico],
        process: AIProcessInfo,
    ) -> Union[
        RegenerateIndexSchemaResponse,
        RegenerateBlockSchemaResponse,
        RegenerateTopicSchemaResponse,
    ]:
        if tema:
            response_class = RegenerateTopicSchemaResponse
            response_data = {"tema": result}
        elif bloque:
            response_class = RegenerateBlockSchemaResponse
            response_data = {"bloque": result}
        else:
            response_class = RegenerateIndexSchemaResponse
            response_data = {"index": result}

        return response_class(
            **response_data,
            processes_info=[
                AIProcessInfo(
                    ai_process_id=process.id, ai_process_type=process.process_type
                )
            ],
        )

    def prepare_input_data(
        self,
        request: Union[
            RegenerateIndexSchemaRequest,
            RegenerateBlockSchemaRequest,
            RegenerateTopicSchemaRequest,
        ],
        asignatura: Asignatura,
        competencies: Competencias,
        title_subject: TitleSubject,
        bloque: Optional[BloqueTematico],
        tema: Optional[TemaStruct],
        indice_id: int,
    ) -> RegenerateIndexInput:
        input_data = RegenerateIndexInput(
            nombre_asignatura=asignatura.nombre,
            esquema_asignatura=asignatura.estructura,
            competencias=competencies,
            comentario=request.comentario,
            bloque=bloque,
            tema=tema,
            prompt_context=PromptContext(
                id_asignatura=title_subject.subject_id,
                id_tema=getattr(request, "tema_id", None),
                id_indice=indice_id,
            ),
        )

        return input_data

    def _get_subject_details(
        self, id_indice: int, id_bloque: Optional[int], id_tema: Optional[int]
    ) -> tuple[TitleSubject, Optional[BloqueTematico], Optional[TemaStruct]]:
        with Session(self._db_engine) as session:
            title_subject = session.exec(
                select(TitleSubject)
                .join(Order, Order.title_subject_id == TitleSubject.id)
                .join(Indice, Indice.order_id == Order.id)
                .where(Indice.id == id_indice)
            ).first()

            if not title_subject:
                self._logger.error(f"No TitleSubject found for Indice id {id_indice}.")
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="No title subject found for the given indice.",
                )

            bloque, tema = None, None

            if id_bloque:
                bloque_db = session.get(Bloque, id_bloque)
                if not bloque_db:
                    self._logger.error(f"No Bloque found for id {id_bloque}.")
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail=f"No bloque found with id {id_bloque}.",
                    )
                bloque = BloqueTematico(
                    nombre=bloque_db.name,
                    temas=[
                        TemaStruct(
                            nombre=tema.name,
                            epigrafes=[epigrafe.name for epigrafe in tema.epigrafes],
                        )
                        for tema in bloque_db.temas
                    ],
                )

            if id_tema:
                tema_db = session.get(Tema, id_tema)
                if not tema_db:
                    self._logger.error(f"No Tema found for id {id_tema}.")
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail=f"No tema found with id {id_tema}.",
                    )
                tema = TemaStruct(
                    nombre=tema_db.name,
                    epigrafes=[epigrafe.name for epigrafe in tema_db.epigrafes],
                )
            return title_subject, bloque, tema
