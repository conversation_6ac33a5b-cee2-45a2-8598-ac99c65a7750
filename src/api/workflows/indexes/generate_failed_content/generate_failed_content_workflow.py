from collections import defaultdict
from logging import Logger
from typing import Optional

from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, status
from sqlalchemy import Engine, Integer, desc, func
from sqlmodel import Session, delete, select, update
from src.api.common.services.message_broker.queue_interface import QueueInterface
from src.api.common.services.structs import ModelInfo, ProcessMetadata
from src.domain.models import (
    AIProcess,
    AIProcessStatus,
    AIProcessType,
    ContentPlan,
    ContentPlanItem,
    Epigrafe,
    Indice,
)

from .generate_failed_content_schemas import (
    GenerateFailedContentRequest,
    GenerateFailedContentResponse,
    TemaAIProcess,
)


class GenerateFailedContentWorkflow:
    def __init__(
        self, db_engine: Engine, logger: Logger, queue_interface: QueueInterface
    ):
        self._db_engine = db_engine
        self._logger = logger
        self._queue_interface = queue_interface

    async def execute(
        self, request: GenerateFailedContentRequest
    ) -> GenerateFailedContentResponse:
        """
        - If tema_id is provided, regenerate ONLY if that topic has a FAILED process.
        - Otherwise, regenerate for all FAILED topics in the given indice_id.
        - If plan_version is provided, link the regeneration to that specific content plan version.
        """
        self._logger.info("Entered GenerateFailedContentWorkflow.execute")

        with Session(self._db_engine, expire_on_commit=False) as session:
            self._check_index_exists(session, request.indice_id)
            failed_processes, plan_version = self._get_failed_ai_processes(
                session=session,
                indice_id=request.indice_id,
                tema_id=request.tema_id,
                plan_version=request.plan_version,
            )
            self._update_processes_to_pending(session, failed_processes)
            session.commit()

        await self._send_to_queue(failed_processes, plan_version, request.model_info)

        response = GenerateFailedContentResponse(
            ai_processes=[
                TemaAIProcess(
                    ai_process_type=process.process_type,
                    ai_process_id=process.id,
                    tema_id=process.tema_id,
                    plan_version=plan_version if plan_version else None,
                )
                for process in failed_processes
            ]
        )
        self._logger.info(f"GenerateFailedContentWorkflow response: {response}")
        return response

    def _check_index_exists(self, session: Session, indice_id: int) -> None:
        """Ensure the given indice exists or raise a 404."""
        if not session.get(Indice, indice_id):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Indice not found."
            )

    def _get_failed_ai_processes(
        self,
        session: Session,
        indice_id: int,
        tema_id: Optional[int] = None,
        plan_version: Optional[int] = None,
    ) -> tuple[list[AIProcess], int]:
        """
        Find all FAILED AIProcess entries for the specified criteria, grouped by tema_id,
        and prepare them for regeneration:
          - If more than one AIProcess failed for the same tema, keep exactly one
            and delete the others. The 'failed_generation_count' in the metadata of the kept
            process is incremented by the total number of failed processes in that group.
          - If a plan_version is given, only processes/items for that plan_version will be handled;
            if none is given, we use the latest plan_version among the failed processes.
        """

        # 1. Retrieve ALL FAILED processes for the indice (and optionally tema)
        failed_processes = self._retrieve_failed_processes(
            session, indice_id, tema_id, plan_version
        )
        if not failed_processes:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No FAILED AIProcess entries found for regeneration with the specified criteria.",
            )

        # 2. Decide which plan_version to use.
        if plan_version is None:
            plan_version = self._retrieve_or_infer_plan_version(
                failed_processes, plan_version
            )
            # 3. If no plan_version was provided, filter the retrieved processes to only match the chosen version.
            matching_processes = [
                p
                for p in failed_processes
                if self._extract_plan_version_from_metadata(p) == plan_version
            ]
            if not matching_processes:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=(
                        f"Failed AIProcess entries exist, but none match plan_version={plan_version}. "
                        "Nothing to regenerate."
                    ),
                )
        else:
            matching_processes = failed_processes

        # 4. Group final processes (deleting duplicates) and return them.
        final_failed_processes = self._group_and_finalize_failed_processes(
            session, matching_processes
        )

        # 5. Remove outdated ContentPlanItems for each TEMA in final_failed_processes.
        self._delete_content_plan_items(session, final_failed_processes, plan_version)

        self._logger.info(
            f"Final AIProcess (kept) for regeneration: {final_failed_processes}"
        )
        return final_failed_processes, plan_version

    def _retrieve_failed_processes(
        self,
        session: Session,
        indice_id: int,
        tema_id: Optional[int] = None,
        plan_version: Optional[int] = None,
    ) -> list[AIProcess]:
        """Retrieve all FAILED processes for the given indice (and tema, if provided)."""
        stmt = (
            select(AIProcess)
            .where(AIProcess.indice_id == indice_id)
            .where(AIProcess.process_type == AIProcessType.GENERATE_CONTENT_FOR_TOPIC)
            .where(AIProcess.status == AIProcessStatus.FAILED)
            .order_by(desc(AIProcess.created_at))
        )
        if tema_id is not None:
            stmt = stmt.where(AIProcess.tema_id == tema_id)

        if plan_version is not None:
            stmt = stmt.where(
                AIProcess.additional_metadata["plan_version"].astext.cast(Integer)
                == plan_version
            )

        return session.execute(stmt).scalars().all()

    def _retrieve_or_infer_plan_version(
        self,
        failed_processes: list[AIProcess],
        requested_plan_version: Optional[int] = None,
    ) -> int:
        """
        Determine which plan_version to use:
          - If 'requested_plan_version' is given, just use that.
          - Otherwise, pick the latest (max) version among the failed_processes.
        """
        if requested_plan_version is not None:
            return requested_plan_version

        # Plan version not explicitly provided; gather from metadata.
        versions_found = []
        for p in failed_processes:
            ver = self._extract_plan_version_from_metadata(p)
            if ver is not None:
                versions_found.append(ver)

        if not versions_found:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No plan_version found in the FAILED AIProcess entries for regeneration.",
            )

        inferred_version = max(versions_found)  # use highest numeric plan_version
        self._logger.info(
            f"No plan_version provided; using latest found: {inferred_version}"
        )
        return inferred_version

    def _extract_plan_version_from_metadata(self, process: AIProcess) -> Optional[int]:
        """Safely extract the numeric plan_version from the AIProcess's metadata, if present."""
        if not process.additional_metadata:
            return None
        try:
            metadata = ProcessMetadata(**process.additional_metadata)
            return metadata.plan_version
        except Exception as e:
            self._logger.error(
                f"Error parsing process metadata for AIProcess {process.id}: {e}"
            )
            return None

    def _group_and_finalize_failed_processes(
        self, session: Session, processes: list[AIProcess]
    ) -> list[AIProcess]:
        """
        For each tema_id, keep the newest FAILED AIProcess and delete older duplicates.
        Also increment the 'failed_generation_count' in the process's metadata by
        the total number of processes in that group.
        """
        grouped_by_tema = defaultdict(list)
        for p in processes:
            grouped_by_tema[p.tema_id].append(p)

        final_processes = []
        for tema_id, tema_processes in grouped_by_tema.items():
            # Because we ordered by created_at desc, the first is the newest.
            keep = tema_processes[0]
            discard = tema_processes[1:]

            # Update metadata on the kept process.
            existing_metadata = ProcessMetadata(**(keep.additional_metadata or {}))

            existing_failed_count = existing_metadata.failed_generation_count or 0
            updated_failed_count = existing_failed_count + len(tema_processes)
            updated_metadata = existing_metadata.copy(
                update={"failed_generation_count": updated_failed_count}
            )
            keep.additional_metadata = updated_metadata.dict(exclude_none=True)

            # Add & delete.
            session.add(keep)
            for old_process in discard:
                # 1) Delete any child processes that have this old_process as parent_id.
                session.exec(
                    delete(AIProcess).where(AIProcess.parent_id == old_process.id)
                )
                # 2) Delete the old_process itself.
                session.delete(old_process)
            # Delete any prior children of the remaining process.
            session.exec(delete(AIProcess).where(AIProcess.parent_id == keep.id))
            final_processes.append(keep)

        return final_processes

    def _delete_content_plan_items(
        self, session: Session, processes: list[AIProcess], plan_version: int
    ) -> None:
        """
        For each TEMA in 'processes', delete the ContentPlanItems (under that plan_version)
        so they can be regenerated.
        """
        unique_tema_ids = {p.tema_id for p in processes if p.tema_id is not None}
        self._logger.debug(
            f"TEMA_IDS for content plan item deletion: {unique_tema_ids}"
        )

        for t_id in unique_tema_ids:
            stmt_cpi = (
                select(ContentPlanItem)
                .join(ContentPlan, ContentPlanItem.content_plan_id == ContentPlan.id)
                .join(Epigrafe, ContentPlan.epigrafe_id == Epigrafe.id)
                .where(Epigrafe.id_tema == t_id)
                .where(ContentPlan.version == plan_version)
            )
            content_plan_items = session.execute(stmt_cpi).scalars().all()
            if content_plan_items:
                for cpi in content_plan_items:
                    session.delete(cpi)
                self._logger.warning(
                    f"Deleted {len(content_plan_items)} ContentPlanItem entries "
                    f"for tema_id={t_id}, plan_version={plan_version}."
                )

    def _update_processes_to_pending(
        self, session: Session, processes: list[AIProcess]
    ) -> None:
        """
        Move each AIProcess in 'processes' from FAILED -> PENDING.
        """
        for process in processes:
            self._logger.info(
                f"Updating AIProcess ID {process.id} from FAILED to PENDING"
            )
            session.exec(
                update(AIProcess)
                .where(AIProcess.id == process.id)
                .values(status=AIProcessStatus.PENDING, updated_at=func.now())
            )
        self._logger.info("All FAILED AIProcess entries have been updated to PENDING.")

    async def _send_to_queue(
        self,
        processes: list[AIProcess],
        plan_version: Optional[int],
        model_info: ModelInfo | None = None,
    ) -> None:
        """
        Send regeneration messages for each AIProcess to the queue.
        """
        try:
            messages = []
            for process in processes:
                msg = {
                    "ai_process_id": process.id,
                    "indice_id": process.indice_id,
                    "tema_id": process.tema_id,
                    "ai_process_type": process.process_type.value,
                }
                if plan_version is not None:
                    msg["plan_version"] = plan_version
                if model_info:
                    msg["model_info"] = model_info.model_dump()
                messages.append(msg)

            async with self._queue_interface:
                message_ids = await self._queue_interface.send_messages(messages)

            self._logger.info(
                f"Successfully sent {len(message_ids)} FAILED-content regeneration messages "
                f"with IDs: {message_ids}"
            )
        except Exception as e:
            self._logger.exception(
                f"An error occurred while sending messages to the queue: {e}."
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to send messages to the queue: {e}.",
            )
