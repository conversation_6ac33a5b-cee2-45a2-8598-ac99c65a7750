from pydantic import BaseModel
from src.api.common.services.structs import AIProcessInfo, ModelInfo


class GenerateFailedContentRequest(BaseModel):
    indice_id: int
    tema_id: int | None = None
    plan_version: int | None = None
    model_info: ModelInfo | None = None


class TemaAIProcess(AIProcessInfo):
    tema_id: int | None = None
    plan_version: int | None = None


class GenerateFailedContentResponse(BaseModel):
    ai_processes: list[TemaAIProcess]
