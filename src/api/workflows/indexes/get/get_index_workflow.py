from logging import Logger

from fastapi import HTT<PERSON>Exception, status
from sqlalchemy.ext.asyncio import AsyncEngine
from sqlmodel.ext.asyncio.session import AsyncSession

from src.api.common.services.index_repository import IndexRepository
from src.domain.models import Indice

from .get_index_schemas import GetIndexRequest, GetIndexResponse


class GetIndexWorkflow:
    def __init__(
        self, db_engine: AsyncEngine, logger: Logger, index_repository: IndexRepository
    ):
        self._db_engine = db_engine
        self._logger = logger
        self._index_repository = index_repository

    async def execute(self, request: GetIndexRequest) -> GetIndexResponse:
        async with AsyncSession(self._db_engine) as session:
            index = await session.get(Indice, request.id)
            self._logger.info(f"Index is {index}")
            if not index:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
            asignatura = await self._index_repository.get_subject_index(
                request.id, session=session
            )
            self._logger.info(f"Asignatura is: {asignatura}")
            competencies = await self._index_repository.get_competencies(
                request.id, session=session
            )
            self._logger.info(f"Competencies are: {competencies}")
            if not (asignatura and competencies):
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
            return GetIndexResponse(
                index=asignatura,
                competencies=competencies.competencias,
                status=index.status,
                order_id=index.order_id,
            )
