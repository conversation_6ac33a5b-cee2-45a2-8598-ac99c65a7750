from logging import Logger
from typing import List

from azure.servicebus.exceptions import ServiceBusError
from fastapi import HTTPException, status
from sqlalchemy import Engine
from sqlalchemy.orm import joinedload
from sqlmodel import Session, func, select
from src.api.common.services.ai_tracer import AITracer
from src.api.common.services.index_repository import IndexRepository
from src.api.common.services.message_broker.queue_interface import QueueInterface
from src.api.common.services.structs import ModelInfo, ProcessMetadata
from src.domain.models import (
    AIProcess,
    AIProcessStatus,
    AIProcessType,
    Bloque,
    ContentPlan,
    Epigrafe,
    Indice,
    IndiceStatus,
    Tema,
)

from .generate_content_schemas import (
    GenerateContentRequest,
    GenerateContentResponse,
    TemaAIProcess,
)


class GenerateContentWorkflow:
    def __init__(
        self,
        db_engine: Engine,
        logger: Logger,
        ai_tracer: AITracer,
        queue_interface: QueueInterface,
        index_repository: IndexRepository,
    ):
        self._db_engine = db_engine
        self._logger = logger
        self._ai_tracer = ai_tracer
        self._queue_interface = queue_interface
        self._user_id = 1
        self._index_repository = index_repository

    async def execute(self, request: GenerateContentRequest) -> GenerateContentResponse:
        self._logger.info("Entered in execute")
        self._check_index_exist(request)
        temas = self._get_temas(request.indice_id, request.tema_id)
        self._logger.info(f"Temas is: {[(tema.id, tema.name) for tema in temas]}")
        plan_version = self._verify_instructions(request, temas)
        temas = self._filter_running(request.indice_id, temas, plan_version)
        processes = [
            self._ai_tracer.start_process(
                AIProcessType.GENERATE_CONTENT_FOR_TOPIC,
                request.indice_id,
                tema.id,
                status=AIProcessStatus.PENDING,
                process_metadata=ProcessMetadata(
                    model_info=request.model_info, plan_version=plan_version
                ),
            )
            for tema in temas
        ]
        self._logger.info(f"Processes are: {[process.id for process in processes]}")
        await self._send_to_queue(processes, request.model_info, request.plan_version)
        await self._index_repository.change_index_status(
            request.indice_id, IndiceStatus.CONTENT_GENERATION
        )
        response = GenerateContentResponse(
            ai_processes=[
                TemaAIProcess(
                    ai_process_type=p.process_type,
                    ai_process_id=p.id,
                    tema_id=p.tema_id,
                )
                for p in processes
            ]
        )
        self._logger.info(f"Response is: {response}")
        return response

    def _verify_instructions(
        self, request: GenerateContentRequest, topics: list[Tema]
    ) -> int:
        with Session(self._db_engine) as session:
            statement = (
                select(ContentPlan)
                .join(Epigrafe, ContentPlan.epigrafe_id == Epigrafe.id)
                .join(Tema, Epigrafe.id_tema == Tema.id)
                .join(Bloque, Tema.id_bloque == Bloque.id)
                .join(Indice, Bloque.indice_id == Indice.id)
                .where(Indice.id == request.indice_id)
            )
            if request.plan_version:
                self._verify_plan_version_for_all_epigrafes(session, request, topics)
                statement = statement.where(ContentPlan.version == request.plan_version)
                version_to_use = request.plan_version
            else:
                version_to_use = self._get_and_verify_max_version_for_topics(
                    session, topics
                )
                statement = statement.where(ContentPlan.version == version_to_use)

            content_plans = session.exec(statement).all()
            self._verify_didactic_instructions(content_plans)
            return version_to_use

    def _verify_plan_version_for_all_epigrafes(self, session, request, topics):
        all_epigrafe_ids = {e.id for topic in topics for e in topic.epigrafes}
        self._logger.info(f"All epigrafe ids are: {all_epigrafe_ids}")
        distinct_count_stmt = select(
            func.count(ContentPlan.epigrafe_id.distinct())
        ).where(
            ContentPlan.version == request.plan_version,
            ContentPlan.epigrafe_id.in_(all_epigrafe_ids),
        )
        found_count = session.exec(distinct_count_stmt).one()
        if found_count != len(all_epigrafe_ids):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=(
                    f"Not all topics have plan_version={request.plan_version}. "
                    f"Only {found_count} out of {len(all_epigrafe_ids)} epigrafes "
                    "match this version."
                ),
            )

    def _get_and_verify_max_version_for_topics(self, session, topics):
        max_versions = []
        for topic in topics:
            max_version_stmt = select(func.max(ContentPlan.version)).where(
                ContentPlan.epigrafe_id.in_([e.id for e in topic.epigrafes])
            )
            max_version = session.exec(max_version_stmt).first()
            if max_version is None:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"No content plan found for topic {topic.id}",
                )
            max_versions.append(max_version)
            if len(set(max_versions)) > 1:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail=(
                        f"Not all topics share the same plan version."
                        f"Found versions: {set(max_versions)}"
                    ),
                )
        if len(max_versions) < 1:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Not found"
            )
        return max_versions[0]

    def _verify_didactic_instructions(self, content_plans):
        for plan in content_plans:
            self._logger.debug(f"Plan is: {plan}")
            if not plan.didactic_instructions:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Didactic instructions not present for some of the plan",
                )

    def _filter_running(
        self, indice_id: int, temas: list[Tema], plan_version: int
    ) -> list[Tema]:
        with Session(self._db_engine) as session:
            existing_processes = session.exec(
                select(AIProcess).where(
                    AIProcess.process_type == AIProcessType.GENERATE_CONTENT_FOR_TOPIC,
                    AIProcess.indice_id == indice_id,
                )
            ).all()

        processed_tema_ids = set()
        for proc in existing_processes:
            metadata_dict = proc.additional_metadata or {}
            process_metadata = ProcessMetadata(**metadata_dict)
            existing_version = process_metadata.plan_version

            # If the stored plan_version matches the one we're using, consider it processed
            if existing_version == plan_version:
                processed_tema_ids.add(proc.tema_id)

        # Now filter out any temas whose IDs appear in processed_tema_ids
        not_processed_temas = [t for t in temas if t.id not in processed_tema_ids]

        # Check that we are processing exactly the same number of topics as exist in the index.
        if len(not_processed_temas) != len(temas):
            self._logger.error(
                f"Mismatch in topic count: expected {len(temas)} unprocessed topics, "
                f"but found {len(not_processed_temas)} unprocessed topics for plan version {plan_version}."
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=(
                    f"All topics in the index must be unprocessed. "
                    f"Found {len(not_processed_temas)} unprocessed topics out of {len(temas)}."
                ),
            )

        self._logger.info(
            f"Not processed temas for generate_content_workflow: {not_processed_temas}"
        )
        return not_processed_temas

    def _get_temas(self, indice_id: int, tema_id: int | None = None) -> List[Tema]:
        with Session(self._db_engine) as session:
            indice = (
                session.exec(
                    select(Indice)
                    .where(Indice.id == indice_id)
                    .options(
                        joinedload(Indice.bloques)
                        .joinedload(Bloque.temas)
                        .joinedload(Tema.epigrafes)
                    )
                )
                .unique()
                .first()
            )
            if not indice:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
            temas = [tema for bloque in indice.bloques for tema in bloque.temas]
            if tema_id:
                temas = [t for t in temas if t.id == tema_id]
                if not temas:
                    raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
            self._logger.info(f"Temas for indice {indice_id}: {len(temas)}")
            return temas

    async def _send_to_queue(
        self,
        processes: List[AIProcess],
        model_info: ModelInfo | None = None,
        plan_version: int | None = None,
    ):
        try:
            messages = []
            for process in processes:
                message_body = {
                    "ai_process_id": process.id,
                    "indice_id": process.indice_id,
                    "tema_id": process.tema_id,
                    "ai_process_type": process.process_type.value,
                }
                if model_info:
                    message_body["model_info"] = model_info.model_dump()
                if plan_version:
                    message_body["plan_version"] = plan_version
                messages.append(message_body)
            async with self._queue_interface:
                await self._queue_interface.send_messages(messages)
            self._logger.info("Successfully sent all messages to the queue.")

        except ServiceBusError as e:
            self._logger.error(f"Failed to send messages: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to send messages to the queue",
            )
        except Exception:
            self._logger.exception(
                "Generic Exception ocurred while sending message to queue"
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to send messages to the queue",
            )

    def _check_index_exist(self, request: GenerateContentRequest):
        with Session(self._db_engine) as session:
            index = session.get(Indice, request.indice_id)
        if not index:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Not found"
            )
