from typing import List

from pydantic import BaseModel
from src.api.common.services.structs import AIProcessInfo, ModelInfo


class GenerateContentRequest(BaseModel):
    indice_id: int
    tema_id: int | None = None
    plan_version: int | None = 1
    model_info: ModelInfo | None = None


class TemaAIProcess(AIProcessInfo):
    tema_id: int | None = None


class GenerateContentResponse(BaseModel):
    ai_processes: List[TemaAIProcess]
