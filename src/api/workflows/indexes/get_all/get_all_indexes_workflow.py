from logging import Logger

from fastapi import Depends, HTTPException, status
from sqlalchemy import Engine
from sqlmodel import Session, desc, select
from src.domain.models import (
    Indice,
    Order,
    TitleSubject,
)

from .get_all_indexes_schemas import GetAllIndexesQueryParams


class GetAllIndexesWorkflow:
    def __init__(self, db_engine: Engine, logger: Logger) -> None:
        self._db_engine = db_engine
        self._logger = logger

    def execute(self, query: GetAllIndexesQueryParams = Depends()):
        with Session(self._db_engine) as session:
            db_query = self._format_query(query)
            indexes = session.exec(db_query).all()
            if not indexes:
                self._logger.exception(
                    f"Indexes not found for subject with subject id: {query.title_subject_id}"
                )
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
            return indexes

    def _format_query(self, query: GetAllIndexesQueryParams):
        statement = (
            select(
                Indice.id,
                Order.id.label("order_id"),  # type: ignore
                Indice.version,
                Indice.updated_at,
                Indice.is_displayed,
                Indice.status,
                TitleSubject.name,
            )
            .join(Order, Indice.order_id == Order.id)
            .join(TitleSubject, Order.title_subject_id == TitleSubject.id)
            .group_by(
                Indice.id,
                Order.id,
                TitleSubject.name,
                Indice.version,
                Indice.updated_at,
                Indice.is_displayed,
                Indice.status,
            )
        )
        # Additional filters
        if query.title_subject_id:
            statement = statement.where(TitleSubject.id == query.title_subject_id)
        if query.is_displayed is not None:
            statement = statement.where(Indice.is_displayed == query.is_displayed)
        if query.version:
            statement = statement.where(Indice.version == query.version)
        if query.status:
            statement = statement.where(Indice.status == query.status)
        if query.desc:
            statement = statement.order_by(desc(Indice.id))
        else:
            statement = statement.order_by(Indice.id)
        if query.limit:
            statement = statement.limit(query.limit)

        return statement
