from typing import Any

from pydantic import BaseModel
from src.domain.models import IndiceStatus


class GetAllIndexesQueryParams(BaseModel):
    title_subject_id: int | None = None
    is_displayed: bool | None = None
    limit: int | None = None
    desc: bool | None = None
    version: int | None = None
    status: IndiceStatus | None = None


class IndexResponse(BaseModel):
    id: int
    order_id: int
    name: str
    version: int
    updated_at: Any
    is_displayed: bool
    status: IndiceStatus
