from fastapi import APIRouter, Depends

from src.api.common.dependency_container import DependencyContainer
from src.api.workflows.indexes import (
    GenerateContentRequest,
    GenerateContentResponse,
    GenerateFailedContentRequest,
    GenerateFailedContentResponse,
    GenerateIndexRequest,
    GenerateIndexResponse,
    GetAllIndexesQueryParams,
    GetIndexRequest,
    GetIndexResponse,
    IndexResponse,
    RegenerateBlockSchemaRequest,
    RegenerateBlockSchemaResponse,
    RegenerateIndexSchemaRequest,
    RegenerateIndexSchemaResponse,
    RegenerateTopicSchemaRequest,
    RegenerateTopicSchemaResponse,
)

router = APIRouter(prefix="/api/v1/indexes", tags=["indexes"])


@router.post("/generate", response_model=GenerateIndexResponse)
async def generate_index(request: GenerateIndexRequest):
    return await DependencyContainer.get_generate_index_workflow().execute(request)


@router.post("/{order_id}/regenerate", response_model=RegenerateIndexSchemaResponse)
async def regenerate_index(order_id: int, request: RegenerateIndexSchemaRequest):
    request.order_id = order_id
    return await DependencyContainer().get_regenerate_index_workflow().execute(request)


@router.post(
    "/{order_id}/regenerate/block", response_model=RegenerateBlockSchemaResponse
)
async def regenerate_block_index(order_id: int, request: RegenerateBlockSchemaRequest):
    request.order_id = order_id
    return await DependencyContainer().get_regenerate_index_workflow().execute(request)


@router.post(
    "/{order_id}/regenerate/topic", response_model=RegenerateTopicSchemaResponse
)
async def regenerate_topic_index(order_id: int, request: RegenerateTopicSchemaRequest):
    request.order_id = order_id
    return await DependencyContainer().get_regenerate_index_workflow().execute(request)


@router.get("", response_model=list[IndexResponse])
def get_all_indexes(params: GetAllIndexesQueryParams = Depends()):
    return DependencyContainer().get_all_indexes_workflow().execute(params)


@router.get("/{index_id}", response_model=GetIndexResponse)
async def get_index(index_id: int):
    request = GetIndexRequest(id=index_id)
    return await DependencyContainer().get_index_workflow().execute(request)


@router.post("/generate_content", response_model=GenerateContentResponse)
async def generate_content(request: GenerateContentRequest):
    return await DependencyContainer().get_generate_content_workflow().execute(request)


@router.post("/generate_failed_content", response_model=GenerateFailedContentResponse)
async def generate_failed_content(request: GenerateFailedContentRequest):
    return (
        await DependencyContainer()
        .get_generate_failed_content_workflow()
        .execute(request)
    )
