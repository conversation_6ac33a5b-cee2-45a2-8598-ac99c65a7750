from pydantic import BaseModel

from src.api.common.services.index_repository import (
    Asignatura,
    Competencia,
    Competencias,
)
from src.api.common.services.structs import (
    AIProcessInfo,
    ModelInfo,
)


class GenerateIndexRequest(BaseModel):
    order_id: int
    descripcion: str | None = None
    numero_bloques: int | None = 3  # <PERSON><PERSON>, por defecto a 2 o 3
    numero_temas: int | None = 10
    numero_epigrafes: int | None = 4
    model_info: ModelInfo | None = None
    store: bool = True


class GenerateIndexResponse(BaseModel):
    index: Asignatura
    competencies: list[Competencia]
    indice_id: int | None = None
    processes_info: list[AIProcessInfo] | None = None


class SchemaInput(BaseModel):
    asignatura: str
    descripcion: str
    numero_bloques: int
    numero_temas: int
    numero_epigrafes: int
    definicion_competencia: str | None = None
    competencias: Competencias | None = None
