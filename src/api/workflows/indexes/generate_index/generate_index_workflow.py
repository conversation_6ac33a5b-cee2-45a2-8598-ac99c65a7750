from logging import Logger

from fastapi import HTT<PERSON><PERSON>x<PERSON>, status
from ia_gen_core.prompts import PromptManager
from langchain.chat_models.base import BaseChatModel
from sqlalchemy.ext.asyncio import AsyncEngine
from sqlalchemy.orm import joinedload
from sqlmodel import desc, or_, select
from sqlmodel.ext.asyncio.session import AsyncSession

from src.api.common.services import MailSender
from src.api.common.services.ai_tracer import AIProcess, AITracer
from src.api.common.services.index_repository import (
    Asignatura,
    IndexRepository,
)
from src.api.common.services.structs import (
    AIProcessInfo,
)
from src.api.common.utils.check_prompt import check_template
from src.api.common.utils.custom_llm import check_for_custom_llm_lc
from src.domain.models import (
    AIProcessStatus,
    AIProcessType,
    EmailTriggerStatus,
    Indice,
    IndiceStatus,
    Order,
)

from .generate_index_schemas import (
    GenerateIndexRequest,
    GenerateIndexResponse,
    SchemaInput,
)


class GenerateIndexWorkflow:
    def __init__(
        self,
        db_engine: AsyncEngine,
        logger: Logger,
        ai_tracer: AITracer,
        llm: BaseChatModel,
        prompt_manager: PromptManager,
        index_repository: IndexRepository,
        mail_sender: MailSender | None,
    ) -> None:
        self._db_engine = db_engine
        self._logger = logger
        self._ai_tracer = ai_tracer
        self._llm = llm
        self._prompt_manager = prompt_manager
        self._index_repository = index_repository
        self._mail_sender = mail_sender

    async def execute(self, request: GenerateIndexRequest) -> GenerateIndexResponse:
        if request.model_info:
            custom_llm = check_for_custom_llm_lc(
                request.model_info.provider,
                request.model_info.name,
                request.model_info.max_tokens,
            )
            self._llm = custom_llm if custom_llm else self._llm
        try:
            (
                order,
                title_subject,
                subject,
            ) = await self._index_repository.get_order_and_subject_details(
                request.order_id
            )
        except Exception:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
        if not request.descripcion:
            request.descripcion = subject.description if subject.description else ""
        input_data = SchemaInput(**request.model_dump(), asignatura=title_subject.name)
        index = await self._check_if_index_with_competencies(order)
        if not index.id:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
        try:
            competencies = await self._index_repository.get_competencies(index.id)
            input_data.competencias = competencies
            self._logger.info(f"Competences are {competencies}")
        except Exception:
            raise HTTPException(
                status_code=404, detail="Competences not found for this index"
            )
        asignatura, process, filled_prompt = await self.generate_schema(input_data)
        self._logger.info("Schema has been generated")
        ai_processes = [process]
        if request.store:
            if index.status not in (
                IndiceStatus.NOT_STARTED,
                IndiceStatus.NON_CONFIRMED_COMPETENCIES,
                IndiceStatus.CONFIRMED_COMPETENCIES,
            ):
                stored_index_id = await self._index_repository.store_subject_index(
                    asignatura, order.id, competencias=competencies
                )
            else:
                stored_index_id = await self._index_repository.store_subject_index(
                    asignatura, order.id, indice_id=index.id
                )
        else:
            stored_index_id = None
        if (stored_index_id is not None) and request.store:
            await self._index_repository.change_index_status(
                stored_index_id, IndiceStatus.INDICE_GENERATION
            )
            if self._mail_sender is not None:
                await self._mail_sender.async_send_email(
                    order_id=order.id,
                    status=EmailTriggerStatus.INDEX,
                )

            self._index_repository.complete_ai_processes(
                ai_processes, indice_id=stored_index_id
            )
            return GenerateIndexResponse(
                index=asignatura,
                competencies=competencies.competencias,
                indice_id=stored_index_id,
                processes_info=[
                    AIProcessInfo(
                        ai_process_id=process.id, ai_process_type=process.process_type
                    )
                    for process in ai_processes
                ],
            )
        elif request.store is False:
            return GenerateIndexResponse(
                index=asignatura,
                competencies=competencies.competencias,
                processes_info=[
                    AIProcessInfo(
                        ai_process_id=process.id,
                        ai_process_type=process.process_type,
                        filled_prompt=filled_prompt,
                    )
                    for process in ai_processes
                ],
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to store subject index",
            )

    async def _check_if_index_with_competencies(self, order: Order) -> Indice:
        async with AsyncSession(self._db_engine) as session:
            statement = (
                select(Indice)
                .where(Indice.order_id == order.id)
                .options(joinedload(Indice.competencies))
                .order_by(desc(Indice.version))
                .limit(1)
            )
            result = await session.exec(statement)
            latest_index = result.first()
            self._logger.info(
                f"Latest index at check if with competencies is: {latest_index}"
            )
            if latest_index:
                self._logger.info(
                    f"Latest index competencies are: {latest_index.competencies}"
                )
                if latest_index.competencies and latest_index.id:
                    return latest_index
                else:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Competencies must be present before creating and index. ",
                    )
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Competencies must be present before creating and index. ",
                )

    async def _check_if_generated(self, indice_id: int):
        async with AsyncSession(self._db_engine) as session:
            statement = (
                select(AIProcess)
                .where(AIProcess.indice_id == indice_id)
                .where(
                    or_(
                        AIProcess.process_type == AIProcessType.SCHEMA_GENERATION,
                        AIProcess.process_type
                        == AIProcessType.SCHEMA_GENERATION_FROM_COMPETENCIES,
                    )
                )
            )
            result = await session.exec(statement)
            ai_processes = result.all()
            if ai_processes:
                self._logger.exception(
                    "Index contents have already been generated for this index"
                )
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="There is already a schema generation process for this subject structure",
                )

    async def generate_schema(
        self, input_data: SchemaInput
    ) -> tuple[Asignatura, AIProcess, str]:
        process_type = AIProcessType.SCHEMA_GENERATION_FROM_COMPETENCIES
        llm = self._llm.with_structured_output(Asignatura)
        metadata, result = (
            {"flow": "Content Schema", "chain": "Generate Index from competencies"},
            None,
        )
        try:
            prompt = self._prompt_manager.get_prompt(
                "generate-schema-samples-from-competencies"
            )
            template, filled_prompt = check_template(prompt, input_data)
            chain = template | llm
            chain = chain.with_retry(stop_after_attempt=3)
            result = await chain.with_config(metadata=metadata).ainvoke(
                {**input_data.model_dump()}
            )
            process = self._ai_tracer.trace_process(
                process_type,
                input_data.model_dump(),
                result.model_dump(),
                metadata,
                prompt_id=prompt.id,
            )
        except Exception as e:
            process = self._ai_tracer.trace_process(
                process_type,
                input_data.model_dump(),
                result.model_dump() if result else {},
                metadata,
                prompt_id=prompt.id,
                process_status=AIProcessStatus.FAILED,
                execution_status=AIProcessStatus.FAILED,
                error_message=str(e),
            )
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
        return result, process, filled_prompt
