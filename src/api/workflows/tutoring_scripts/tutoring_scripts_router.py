import io

from fastapi import APIRouter, File, Form, Query, UploadFile
from src.api.common.dependency_container import DependencyContainer
from src.api.workflows.rubrics.services.file_service import FileService
from src.api.workflows.tutoring_scripts.generate_tutoring_scripts.request import (
    GenerateTutoringScriptRequest,
)
from src.api.workflows.tutoring_scripts.generate_tutoring_scripts.response import (
    GenerateTutoringScriptResponse,
)

router = APIRouter(prefix="/api/v1/tutoring-scripts", tags=["Tutoring scripts"])

file_service = FileService()


@router.post(
    "/generate",
    response_model=GenerateTutoringScriptResponse,
    description="""

""",
)
async def generate_tutoring_scripts(
    include_web_search: bool = Query(
        True,
        description="Incluir búsqueda web para información adicional, por defecto si",
    ),
    asignatura: str = Form(..., description="Nombre de la asignatura"),
    temas_a_tratar: str = Form(..., description="Temas a tratar en la tutoría"),
    faculty: str = Form(..., description="Facultad"),
    numero_tutoria: int = Form(..., description="Número de la tutoría"),
    subject_corpus: UploadFile = File(
        ..., description="Temario completo o guía docente de la asignatura"
    ),
    actividades: UploadFile | None = File(
        None, description="Actividades a resolver (opcional)"
    ),
) -> GenerateTutoringScriptResponse:
    file = io.BytesIO(await subject_corpus.read())
    file.name = subject_corpus.filename
    temario_content = await file_service.a_read_document(
        file=file, convert_docx_to_pdf=True
    )
    actividades_content = None
    if actividades:
        file = io.BytesIO(await actividades.read())
        file.name = actividades.filename
        actividades_content = await file_service.a_read_document(
            file=file, convert_docx_to_pdf=True
        )
    data = GenerateTutoringScriptRequest(
        include_web_search=include_web_search,
        asignatura=asignatura,
        temas_a_tratar=temas_a_tratar,
        faculty=faculty,
        numero_tutoria=numero_tutoria,
        temario_content=temario_content,
        actividades_content=actividades_content,
    )
    return await DependencyContainer.get_generate_tutoring_scripts_workflow().execute(
        data
    )
