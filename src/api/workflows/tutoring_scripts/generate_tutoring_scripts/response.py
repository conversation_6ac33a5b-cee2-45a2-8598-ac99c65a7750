from pydantic import BaseModel, Field

TITULO_FASE_DESC = "Título de la fase."


class EsquemaFase(BaseModel):
    """Describe una de las tres fases de la tutoría en el esquema visual."""

    fase: str = Field(
        description="Nombre de la fase (ej. 'FASE 1: Bienvenida y Conexión')."
    )
    descripcion: str = Field(
        description="Breve descripción de los objetivos y acciones en esta fase."
    )


class PresentacionCasoPractico(BaseModel):
    """Contiene la información de presentación del caso práctico que se usará en la tutoría."""

    nombre_caso: str = Field(
        description="Nombre del caso práctico, empresa o situación a analizar."
    )
    descripcion: str = Field(description="Breve descripción del caso práctico.")
    justificacion_eleccion: str = Field(
        description="Explicación de por qué este caso es adecuado y relevante para la tutoría."
    )
    vinculacion_temario: str = Field(
        description="Cómo el caso práctico se conecta con los temas de la tutoría."
    )


class Fase1Desarrollo(BaseModel):
    titulo: str = Field(
        default="FASE 1: Bienvenida, Conexión y Motivación",
        description=TITULO_FASE_DESC,
    )

    guion_dialogo: str = Field(
        description="Guion detallado para el docente, incluyendo saludos, preguntas de sondeo y palabras de motivación."
    )


class Fase2Desarrollo(BaseModel):
    titulo: str = Field(
        default="FASE 2: Contenido Teórico-Práctico y Actividades",
        description=TITULO_FASE_DESC,
    )

    explicacion_teorica: str = Field(
        description="Explicación teórica breve y clara de los conceptos del temario a tratar."
    )
    aplicacion_caso_real: str = Field(
        description="Aplicación de la teoría al caso práctico central, con ejemplos concretos."
    )
    explicacion_actividad: str | None = Field(
        None,
        description="Explicación de la actividad a realizar por el alumnado (sin resolverla), recordando los prerrequisitos.",
    )


class Fase3Desarrollo(BaseModel):
    titulo: str = Field(
        default="FASE 3: Repaso, Preguntas y Cierre", description=TITULO_FASE_DESC
    )

    preguntas_repaso: list[str] = Field(
        description="Lista de preguntas que el docente puede lanzar para consolidar el conocimiento."
    )
    que_hemos_aprendido: str = Field(
        description="Resumen de los 3-5 puntos clave y aprendizajes principales de la tutoría, en formato de lista o viñetas."
    )
    despedida_motivacional: str = Field(
        description="Guion para la despedida, emplazando a la siguiente tutoría o deseando suerte para las pruebas finales (según corresponda)."
    )


class GenerateTutoringScriptResponse(BaseModel):
    """
    Modelo de respuesta para el guion completo y detallado de una tutoría online,
    diseñado según la nueva estructura de 3 fases.
    """

    esquema_visual: list[EsquemaFase] = Field(
        description="Tabla que muestra las TRES FASES de la tutoría y lo que se hará en cada una."
    )

    presentacion_caso: PresentacionCasoPractico = Field(
        description="Presentación del caso práctico o ejemplo real que servirá de hilo conductor."
    )

    fase1: Fase1Desarrollo = Field(description="Desarrollo completo de la Fase 1.")
    fase2: Fase2Desarrollo = Field(description="Desarrollo completo de la Fase 2.")
    fase3: Fase3Desarrollo = Field(description="Desarrollo completo de la Fase 3.")

    asignatura: str = Field(description="Nombre de la asignatura.")
    numero_tutoria: int = Field(description="Número de la tutoría (1-5).")
