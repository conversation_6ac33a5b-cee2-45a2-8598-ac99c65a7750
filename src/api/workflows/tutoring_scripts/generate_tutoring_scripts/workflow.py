from logging import Logger

import tik<PERSON>en
from agents import <PERSON>
from openai import <PERSON>ync<PERSON><PERSON>A<PERSON>
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncEngine
from sqlmodel import select
from sqlmodel.ext.asyncio.session import AsyncSession
from src.api.common.services import SearchAgent
from src.api.common.services.search_agent import Agent<PERSON><PERSON>ult
from src.api.workflows.tutoring_scripts.generate_tutoring_scripts.prompts import (
    system_prompt_tutoria_unipro,
    user_prompt_tutoria_unipro,
)
from src.api.workflows.tutoring_scripts.generate_tutoring_scripts.request import (
    GenerateTutoringScriptRequest,
)
from src.api.workflows.tutoring_scripts.generate_tutoring_scripts.response import (
    GenerateTutoringScriptResponse,
)
from src.domain.models_toolkit import Application, KnowledgeAreaInfo, Activities


class VerifiedSource(BaseModel):
    title: str
    url: str
    body: str
    tokens: int


class GenerateTutoringScriptWorkflow:
    """
    Workflow for extracting criteria from a document.
    """

    def __init__(
        self,
        openai_client: As<PERSON><PERSON><PERSON><PERSON><PERSON>,
        logger: Logger,
        search_agent: SearchAgent,
        db_engine: AsyncEngine,
        model: str = "gemini-2.5-pro",
    ) -> None:
        self.openai_client = openai_client
        self.logger = logger
        self.model = model
        self.search_agent = search_agent
        self.db_engine = db_engine

    async def execute(
        self, request: GenerateTutoringScriptRequest
    ) -> GenerateTutoringScriptResponse:
        try:
            self.logger.info("Starting full tutorial generation process")

            all_news_content = ""
            if request.include_web_search:
                self.logger.info("Web search is enabled. Performing search with agent.")
                all_news_content = await self.search_sources(
                    temas_a_tratar=request.temas_a_tratar,
                    asignatura=request.asignatura,
                    temario_content=request.temario_content,
                    faculty=request.faculty,
                )
            else:
                self.logger.info("Web search is disabled.")
            self.logger.info("Generating tutoring script with OpenAI model")

            async with AsyncSession(self.db_engine) as session:
                statement = (
                    select(KnowledgeAreaInfo)
                    .join(Application, KnowledgeAreaInfo.application_id == Application.id_application)
                    .where(KnowledgeAreaInfo.name == request.faculty)
                    .where(Application.name_application == Activities.GENERADOR_GUIONES_TUTORIAS_UNIPRO)
                )
                result = await session.exec(statement)
                area_info = result.first()

            if area_info is None:
                raise ValueError("Información del estudio no válida o no encontrada")

            profesion = area_info.variables.get("profesion", None)
            titulo = area_info.variables.get("titulo", None)

            if titulo is None or profesion is None:
                raise ValueError("Información del estudio no válida o no encontrada")

            system_prompt = system_prompt_tutoria_unipro.format(
                profesion=profesion,
                titulo=titulo,
            )

            user_prompt = user_prompt_tutoria_unipro.format(
                asignatura=request.asignatura,
                numero_tutoria=request.numero_tutoria,
                bachelor_title=request.faculty,
                temas_a_tratar=request.temas_a_tratar,
                temario_content=request.temario_content,
                programacion_content=request.programacion_content
                if hasattr(request, "programacion_content")
                else "",
                actividades_content=request.actividades_content,
                relevant_news=all_news_content,
            )

            response = await self.openai_client.beta.chat.completions.parse(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt},
                ],
                max_tokens=30000,
                temperature=0.5,
                response_format=GenerateTutoringScriptResponse,
            )

            self.logger.info("Full tutorial generation completed successfully")
            return response.choices[0].message.parsed
        except Exception as e:
            self.logger.error(f"Error in generate_tutoria_script: {str(e)}")
            raise ValueError(f"Error al procesar la solicitud: {str(e)}")

    async def search_sources(
        self, asignatura: str, faculty: str, temas_a_tratar: str, temario_content: str
    ):
        self.logger.info("Starting web search with the intelligent search agent.")

        search_query = f"""
Necesito encontrar fuentes académicas y profesionales de alta calidad para preparar un guion de tutoría.

**Asignatura:** {asignatura}
**Bachelor/Facultad:** {faculty}
**Temas clave a tratar en la tutoría:**
{temas_a_tratar}

**Contexto del temario de la asignatura:**
---
{temario_content[:3000]}...
---

**Objetivo:**
Busca fuentes que me ayuden a crear un caso práctico o a encontrar ejemplos y datos actuales relevantes para los temas mencionados. El objetivo es enriquecer la tutoría con información práctica y bien fundamentada, al estilo de un caso de estudio de Harvard. Sigue tu estrategia de búsqueda por rondas para asegurar una cobertura completa.
"""
        try:
            runner_result = await Runner.run(
                self.search_agent, input=search_query, max_turns=8
            )
            search_results = runner_result.final_output

            self.logger.info(
                f"Search agent finished. Found {len(search_results.documents)} potential sources."
            )

            verified_sources, formatted_context = await self.postprocess_search_results(
                search_results, max_tokens=80000, max_tokens_per_doc=20000
            )

            self.logger.info(f"Processed {len(verified_sources)} verified sources.")

            return formatted_context

        except Exception as e:
            self.logger.error(f"An error occurred during the agent-based search: {e}")

            raise

    async def postprocess_search_results(
        self,
        search_results: AgentResult,
        max_tokens: int = 100000,
        max_tokens_per_doc: int = 30000,
    ) -> tuple[list[VerifiedSource], str]:
        """
        Process search results using cached content and creating a formatted context string.

        Returns:
            tuple: (verified_sources, formatted_context) where formatted_context is ready for model input
        """
        verified_sources = []
        enc = tiktoken.get_encoding("o200k_base")

        for result in search_results.documents:
            if result.url in self.search_agent.url_content_cache:
                full_content = self.search_agent.url_content_cache[result.url]
                token_count = len(enc.encode(full_content))

                if token_count < 200:
                    continue

                verified_sources.append(
                    VerifiedSource(
                        title=result.title,
                        body="\n".join(result.summary),
                        url=result.url,
                        tokens=token_count,
                    )
                )
                self.logger.debug(
                    f"✓ Using cached content for: {result.url[:60]}... ({token_count} tokens)"
                )
            else:
                self.logger.debug(f"✗ No cached content for: {result.url[:60]}...")

        formatted_context = "## Verified Sources\n\n"
        current_tokens = len(enc.encode(formatted_context))
        included_count = 0

        for i, source in enumerate(verified_sources, 1):
            full_content = self.search_agent.url_content_cache.get(source.url, "")

            content_tokens = len(enc.encode(full_content))
            if content_tokens > max_tokens_per_doc:
                tokens = enc.encode(full_content)
                cropped_tokens = tokens[:max_tokens_per_doc]
                cropped_content = enc.decode(cropped_tokens)
                cropped_content += "\n\n[Content truncated - showing some of the first pages as the document was really lengthy]"
                content_to_use = cropped_content
            else:
                content_to_use = full_content

            source_text = f"**Source {i}:**\n"
            source_text += f"- **Title:** {source.title}\n"
            source_text += f"- **URL:** {source.url}\n"
            source_text += f"- **Summary:** {source.body}\n"
            source_text += f"- **Content:** {content_to_use}\n\n"

            source_tokens = len(enc.encode(source_text))

            if current_tokens + source_tokens > max_tokens:
                break

            formatted_context += source_text
            current_tokens += source_tokens
            included_count += 1

        total_sources = len(verified_sources)
        if included_count < total_sources:
            footer = f"*Showing top {included_count} of {total_sources} sources (limited by {max_tokens} token context)*\n"
        else:
            footer = f"*Total: {included_count} verified sources*\n"

        formatted_context += footer

        return verified_sources, formatted_context
