from enum import Enum

from pydantic import BaseModel
from src.api.workflows.rubrics.extract_criteria.response import (
    ExtractCriteriaResponse,
)


class Language(str, Enum):
    EN = "en"
    ES = "es"


class GenerateRubricsRequest(BaseModel):
    subject_type: str
    language: Language
    document: bytes
    document_filename: str
    syllabus_corpus: bytes | None = None
    syllabus_corpus_filename: str | None = None
    criteria: ExtractCriteriaResponse
