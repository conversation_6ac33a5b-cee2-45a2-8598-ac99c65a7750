from pydantic import BaseModel


class RubricLevel(BaseModel):
    excellent_description: str
    good_description: str
    average_description: str
    needs_to_improve_description: str


class RubricCriterionDetail(BaseModel):
    order: int
    name: str
    description: str
    performance_levels: RubricLevel
    educational_guideline: list[str]


class GenerateRubricsResponse(BaseModel):
    criteria: list[RubricCriterionDetail]
