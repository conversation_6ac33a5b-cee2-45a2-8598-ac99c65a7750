import io
import json
from logging import Logger

from fastapi import HTTPException
from openai import AsyncOpenAI
from sqlalchemy.ext.asyncio import AsyncEngine
from sqlmodel import select
from sqlmodel.ext.asyncio.session import AsyncSession
from src.api.workflows.rubrics.extract_criteria.response import (
    ExtractCriteriaResponse,
)
from src.api.workflows.rubrics.generate_rubrics.request import (
    GenerateRubricsRequest,
    Language,
)
from src.api.workflows.rubrics.generate_rubrics.response import GenerateRubricsResponse
from src.api.workflows.rubrics.services.file_service import FileService
from src.domain.models_toolkit import Activities, Application, KnowledgeAreaInfo


class GenerateRubricsWorkflow:
    """
    Workflow for extracting criteria from a document.
    """

    def __init__(
        self, openai_client: AsyncOpenAI, db_engine: AsyncEngine, logger: Logger
    ):
        self.openai_client = openai_client
        self.db_engine = db_engine
        self.file_service = FileService()
        self.logger = logger

    async def execute(self, data: GenerateRubricsRequest) -> GenerateRubricsResponse:
        """Generate final rubric with all details"""
        self.logger.info("Generating final rubric")
        try:
            file = io.BytesIO(data.document)
            file.name = data.document_filename
            content = await self.file_service.a_read_document(
                file, convert_docx_to_pdf=True
            )
        except Exception as e:
            self.logger.error(f"Error reading document: {str(e)}")
            raise HTTPException(
                status_code=400,
                detail={
                    "msg": "Error reading document",
                    "errors": str(e),
                },
            )
            # Handle empty syllabus_files list
        syllabus_content = ""
        if data.syllabus_corpus is not None:
            try:
                file = io.BytesIO(data.syllabus_corpus)
                file.name = data.syllabus_corpus_filename
                syllabus_content = "\n\n".join(
                    await self.file_service.a_read_document(
                        file, convert_docx_to_pdf=True
                    )
                )
            except Exception as e:
                self.logger.error(f"Error reading syllabus document: {str(e)}")
                raise HTTPException(
                    status_code=400,
                    detail={
                        "msg": "Error reading syllabus document",
                        "errors": str(e),
                    },
                )

        rubric_result = await self.a_generate_rubric_details(
            data.language, content, syllabus_content, data.criteria, data.subject_type
        )

        try:
            if len(rubric_result.criteria) == len(data.criteria.elements):
                for i, element in enumerate(data.criteria.elements):
                    rubric_result.criteria[i].description = element.description
        except Exception as e:
            self.logger.error(f"Error updating rubric criteria descriptions: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail={
                    "msg": "Error updating rubric criteria descriptions",
                    "errors": str(e),
                },
            )
        return rubric_result

    async def a_generate_rubric_details(
        self,
        language: Language,
        content: str,
        syllabus_content: str,
        criterios: ExtractCriteriaResponse,
        prompt_key: str,
    ) -> GenerateRubricsResponse:
        """Generates detailed rubric based on approved criteria"""

        self.logger.debug("Generating rubric details")

        system_prompt, user_prompt = await self._get_prompt_from_db(prompt_key)
        try:
            system_prompt = system_prompt.replace("{{", "{").replace("}}", "}")
            user_prompt = user_prompt.replace("{{", "{").replace("}}", "}")

            user_prompt = user_prompt.format(
                content=content,
                syllabus_content=syllabus_content,
                criterios=json.dumps(
                    criterios.model_dump(), ensure_ascii=False, indent=2
                ),
            )
        except Exception as e:
            self.logger.error(f"Error formatting prompts: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail={
                    "msg": "Error formatting prompts",
                    "errors": str(e),
                },
            )
        if language == Language.EN:
            user_prompt += """
TIENES QUE RESPONDER TODO OBLIGATORIAMENTE EN INGLÉS.
"""
        else:
            user_prompt += """
TIENES QUE RESPONDER TODO OBLIGATORIAMENTE EN ESPAÑOL.
"""

        try:
            response = await self.openai_client.responses.parse(
                instructions=system_prompt,
                input=user_prompt,
                model="o4-mini",
                text_format=GenerateRubricsResponse,
            )
            return response.output_parsed
        except Exception as e:
            self.logger.error(f"Error generating rubric details: {str(e)}")
            raise

    async def _get_prompt_from_db(self, prompt_key: str) -> tuple[str, str]:
        """Fetches prompt from the database"""
        try:
            async with AsyncSession(self.db_engine) as session:
                statement = (
                    select(KnowledgeAreaInfo)
                    .join(Application, KnowledgeAreaInfo.application_id == Application.id_application)
                    .where(KnowledgeAreaInfo.name == prompt_key)
                    .where(Application.name_application == Activities.RUBRICAS)
                )
                result = await session.exec(statement)
                prompt = result.first()
                if not prompt:
                    raise ValueError(f"Prompt with key '{prompt_key}' not found")
                return prompt.system_prompt, prompt.user_prompt
        except ValueError as e:
            self.logger.error(f"Error fetching prompt from DB: {str(e)}")
            raise HTTPException(
                status_code=404,
                detail={
                    "msg": "Subject type not found",
                    "errors": str(e),
                },
            )
        except Exception as e:
            self.logger.error(f"Unexpected error fetching prompt: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail={
                    "msg": "Unexpected error fetching prompt",
                    "errors": str(e),
                },
            )
