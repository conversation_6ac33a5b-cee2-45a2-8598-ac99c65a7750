import base64
from typing import Binary<PERSON>

from src.api.workflows.rubrics.services.utils.utils import a_read_file_content


class FileService:
    """Service for handling file operations"""

    async def a_read_document(
        self,
        file: BinaryIO,
        is_scanned: bool = False,
        page_range: dict | None = None,
        convert_docx_to_pdf: bool = False,
    ) -> str | tuple[str, str]:
        """Async version of read_document"""
        if is_scanned:
            file.seek(0)
            pdf_data = base64.b64encode(file.read()).decode("utf-8")
            content = ""
            return content, pdf_data

        content, _ = await a_read_file_content(file, page_range, convert_docx_to_pdf)
        return content
