import asyncio
import io
import os
import re
import tempfile
import unicodedata
import zipfile
from typing import BinaryIO, List, Optional

import aiofiles
import openpyxl
import pymupdf
import xlrd
from docx import Document
from docx.oxml.ns import qn
from ftfy import fix_text
from pptx import Presentation

_CTRL_CHARS_RE = re.compile(r"[\x00-\x08\x0b-\x0c\x0e-\x1f\x7f]")


def _clean_text(s: str) -> str:
    """
    • Arregla codificación con ftfy (si está instalada)
    • Elimina caracteres de control invisibles
    • Normaliza a Unicode NFC
    """
    s = fix_text(s)
    s = _CTRL_CHARS_RE.sub("", s)
    s = unicodedata.normalize("NFC", s)
    return s


def _fmt_table(rows: list[list[str]]) -> str:
    clean = [["" if c is None else str(c).strip() for c in r] for r in rows]
    return "\n".join("\t".join(r) for r in clean)


def read_pdf_file(file: BinaryIO, page_range: Optional[dict] = None) -> str:
    """
    Lee un PDF y devuelve el texto etiquetado (con tablas, TOC, etc.)
    page_range (opcional) ➜ dict con:
        mode: 'first_n' | 'last_n' | 'custom_n' | 'full'  (por defecto 'full')
        count:   nº páginas para first_n / last_n
        start_page, end_page: rangos 1-based para custom_n
    """
    file.seek(0)

    file_content = file.read()

    doc = pymupdf.open(stream=file_content, filetype="pdf")

    try:
        total_pages = len(doc)
        pages_to_process = range(total_pages)

        if page_range and page_range.get("mode", "full") != "full":
            mode = page_range.get("mode")
            if mode == "first_n":
                n = min(page_range.get("count", total_pages), total_pages)
                pages_to_process = range(n)
            elif mode == "last_n":
                n = min(page_range.get("count", total_pages), total_pages)
                pages_to_process = range(total_pages - n, total_pages)
            elif mode == "custom_n":
                start = max(1, min(page_range.get("start_page", 1), total_pages)) - 1
                end = max(
                    start + 1, min(page_range.get("end_page", total_pages), total_pages)
                )
                pages_to_process = range(start, end)

        parts: list[str] = []

        toc = doc.get_toc(simple=True)

        title = getattr(file, "name", None) or "untitled.pdf"
        parts.append(f"## Doc name: {title}\n")

        if toc:
            parts.append("### TOC\n")
            for lvl, text, page_num in toc:
                indent = "  " * (lvl - 1)
                parts.append(f"{indent}- {_clean_text(text)} ……… (page {page_num})")
            parts.append("\n")

        for idx in pages_to_process:
            page = doc.load_page(idx)
            page_num = idx + 1

            parts.append(f"[Page {page_num}]\n")

            try:
                text_content = page.get_text("text", sort=True)
                parts.append(_clean_text(text_content))
            except Exception as e:
                print(f"Warning: Could not extract text from page {page_num}: {str(e)}")

            try:
                table_finder = page.find_tables()

                for t_idx, tbl in enumerate(table_finder.tables, start=1):
                    try:
                        rows = tbl.extract()
                        label = f"[TABLE {page_num}.{t_idx}]"
                        parts.append(f"\n{label}\n{_fmt_table(rows)}\n")
                    except Exception as e:
                        print(
                            f"Warning: Could not extract table {t_idx} on page {page_num}: {str(e)}"
                        )

            except (ValueError, ReferenceError) as e:
                if "not a textpage" in str(e) or "weakly-referenced" in str(e):
                    print(
                        f"Info: Table extraction skipped for page {page_num} due to PyMuPDF limitations"
                    )
                else:
                    print(
                        f"Warning: Table extraction failed for page {page_num}: {str(e)}"
                    )
            except Exception as e:
                print(
                    f"Warning: Unexpected error extracting tables from page {page_num}: {str(e)}"
                )

        parts.append(
            "\n-----------------------------------DOCUMENT END-----------------------------------\n"
        )

    finally:
        doc.close()

    return "".join(parts)


def read_excel_file(file: BinaryIO) -> str:
    """Reads content from Excel files (XLSX or XLS)"""
    try:
        file_bytes = io.BytesIO(file.read())
        file.seek(0)

        try:
            workbook = openpyxl.load_workbook(file_bytes, data_only=True)
            sheet = workbook.active
            rows = []
            for row in sheet.iter_rows(values_only=True):
                rows.append(
                    "\t".join(str(cell) if cell is not None else "" for cell in row)
                )
            return "\n".join(rows)
        except Exception as xlsx_error:
            try:
                file_bytes.seek(0)
                workbook = xlrd.open_workbook(file_contents=file_bytes.read())
                sheet = workbook.sheet_by_index(0)
                rows = []
                for row_idx in range(sheet.nrows):
                    row = [
                        str(cell.value) if cell.value else ""
                        for cell in sheet.row(row_idx)
                    ]
                    rows.append("\t".join(row))
                return "\n".join(rows)
            except Exception as xls_error:
                raise ValueError(
                    f"Could not process Excel file. XLSX error: {xlsx_error}. XLS error: {xls_error}"
                )
    except Exception as e:
        raise IOError(f"Error reading Excel file: {str(e)}")


def read_pptx_file(file: BinaryIO) -> str:
    """Reads content from a PowerPoint PPTX file"""
    try:
        prs = Presentation(file)
        text = []

        for slide in prs.slides:
            slide_text = []
            for shape in slide.shapes:
                if hasattr(shape, "text") and shape.text.strip():
                    slide_text.append(shape.text.strip())
            if slide_text:
                text.append("\n".join(slide_text))

        return "\n\n".join(text)
    except Exception as e:
        raise ValueError(f"Error reading PPTX file: {str(e)}")


def _has_page_break(para) -> bool:
    """
    Devuelve True si el párrafo contiene un salto de página duro
    (<w:br w:type="page"/>).  Funciona con cualquier versión de python-docx.
    """
    br_tag = qn("w:br")  # '{http://…/wordprocessingml/2006/main}br'
    type_att = qn("w:type")  # '{http://…/wordprocessingml/2006/main}type'

    for elem in para._element.iter(br_tag):
        if elem.get(type_att) == "page":
            return True
    return False


def is_processable_file(filename: str, supported_extensions: list[str]) -> bool:
    """Check if a file should be processed - resistant to any OS metadata"""
    basename = os.path.basename(filename)

    return (
        not filename.endswith("/")
        and os.path.splitext(filename.lower())[1] in supported_extensions
        and not basename.startswith(".")
        and not any(part.startswith(".") for part in filename.split("/"))
        and ".." not in filename
        and not filename.startswith("/")
        and basename.lower() not in ["thumbs.db", "desktop.ini"]
        and not any(
            meta in filename.lower()
            for meta in [
                "__macosx",
                "__pycache__",
                ".git",
                ".svn",
                "__metadata__",
                "system volume information",
            ]
        )
        and len(basename) > len(os.path.splitext(basename)[1])
    )


async def a_read_file_content(
    file: BinaryIO, page_range: dict | None = None, convert_docx_to_pdf: bool = False
) -> tuple[str, Optional[Document]]:
    """
    Async version of read_file_content
    Reads content from various file types and returns the text content
    and optionally the document object for further processing.
    """
    from src.api.workflows.rubrics.services.file_service import FileService

    filename = file.name

    if filename.endswith(".zip"):
        files = await a_extract_zip(file)
        file_service = FileService()
        contents = await asyncio.gather(
            *[
                file_service.a_read_document(
                    f, page_range=page_range, convert_docx_to_pdf=convert_docx_to_pdf
                )
                for f in files
            ]
        )
        return "\n\n".join(contents), None

    elif filename.endswith(".docx"):
        return await a_read_docx_file(file, convert_docx_to_pdf)

    elif filename.endswith(".doc"):
        doc_content = await a_read_doc_file(file)
        return doc_content, None

    elif filename.endswith(".pdf"):
        pdf_text = await a_read_pdf_file(file, page_range)
        return pdf_text, None

    elif filename.endswith(".txt"):
        txt_content = await a_read_txt_file(file)
        return txt_content, None

    elif filename.endswith(".xlsx") or filename.endswith(".xls"):
        excel_content = await a_read_excel_file(file)
        return excel_content, None

    elif filename.endswith(".pptx"):
        pptx_content = await a_read_pptx_file(file)
        return pptx_content, None

    elif filename.endswith(".ppt"):
        ppt_content = await a_read_ppt_file(file)
        return ppt_content, None

    else:
        raise ValueError(f"Unsupported file format: {filename}")


async def a_convert_docx_to_pdf(file: BinaryIO) -> BinaryIO:
    file.seek(0)
    file_content = file.read()
    file.seek(0)

    base = os.path.splitext(os.path.basename(file.name or "document"))[0]

    with tempfile.TemporaryDirectory() as tmp:
        docx_path = os.path.join(tmp, f"{base}.docx")
        pdf_path = os.path.join(tmp, f"{base}.pdf")

        async with aiofiles.open(docx_path, "wb") as fp:
            await fp.write(file_content)

        proc = None
        try:
            proc = await asyncio.create_subprocess_exec(
                "pandoc",
                docx_path,
                "-o",
                pdf_path,
                "--pdf-engine=xelatex",
                "--pdf-engine-opt=-interaction=nonstopmode",
                "--quiet",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
            )

            _, stderr = await asyncio.wait_for(proc.communicate(), timeout=180)

            if proc.returncode != 0:
                raise RuntimeError(f"⚠️  Pandoc falló:\n{stderr.decode()}")

        except Exception:
            if proc and proc.returncode is None:
                proc.terminate()
                try:
                    await asyncio.wait_for(proc.wait(), timeout=5)
                except asyncio.TimeoutError:
                    proc.kill()
                    await proc.wait()
            raise

        if not os.path.exists(pdf_path):
            raise FileNotFoundError("⚠️  PDF file was not created by pandoc")

        try:
            async with aiofiles.open(pdf_path, "rb") as fp:
                pdf_data = await fp.read()

            pdf_io = io.BytesIO(pdf_data)
            pdf_io.name = base + ".pdf"
            return pdf_io

        except Exception as e:
            raise IOError(f"⚠️  Error reading converted PDF: {str(e)}")


async def a_read_docx_file(
    file: BinaryIO, convert_docx_to_pdf: bool = False
) -> tuple[str, Document]:
    """Async version of read_docx_file"""
    if convert_docx_to_pdf:
        try:
            pdf_bytes = await a_convert_docx_to_pdf(file)
            return await a_read_pdf_file(pdf_bytes), Document(file)
        except Exception as e:
            print(
                f"Exception converting pdf: {e}\n\nFalling back to native docx conversion"
            )

    try:
        doc = Document(file)
        title = file.name or ""
        parts = []
        page = 1
        parts.append(f"## Doc name: {title}\n[Page number: {page}]")

        for para in doc.paragraphs:
            if _has_page_break(para):
                page += 1
                parts.append(f"\n[Page number: {page}]")

            txt = para.text.strip()
            if txt:
                parts.append(txt)

        final_result = (
            "\n".join(parts)
            + "\n-----------------------------------DOCUMENT END-----------------------------------\n"
        )
        return final_result, doc
    except Exception as e:
        raise ValueError(f"Error reading DOCX file: {str(e)}")


async def a_read_doc_file(file: BinaryIO) -> str:
    """
    Async version of read_doc_file for legacy .doc files
    Uses LibreOffice to convert .doc to .docx then processes it
    """
    temp_doc_path = None
    temp_docx_path = None

    try:
        with tempfile.NamedTemporaryFile(delete=False, suffix=".doc") as temp_file:
            temp_doc_path = temp_file.name
            file.seek(0)
            temp_file.write(file.read())
            file.seek(0)

        temp_docx_path = temp_doc_path + "x"

        try:
            proc = await asyncio.create_subprocess_exec(
                "soffice",
                "--headless",
                "--convert-to",
                "docx",
                temp_doc_path,
                "--outdir",
                os.path.dirname(temp_doc_path),
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
            )

            _, stderr = await asyncio.wait_for(proc.communicate(), timeout=120)

            if proc.returncode != 0:
                raise RuntimeError(f"LibreOffice conversion failed: {stderr.decode()}")

        except FileNotFoundError:
            raise FileNotFoundError(
                "LibreOffice is not installed or not in PATH. Required for .doc file processing."
            )
        except asyncio.TimeoutError:
            raise TimeoutError("LibreOffice conversion timeout after 120 seconds")

        if not os.path.exists(temp_docx_path):
            raise FileNotFoundError("Conversion failed. DOCX file was not generated.")

        async with aiofiles.open(temp_docx_path, "rb") as docx_file:
            docx_content = await docx_file.read()
            docx_io = io.BytesIO(docx_content)
            docx_io.name = (file.name or "document").replace(".doc", ".docx")

            content, _ = await a_read_docx_file(docx_io, convert_docx_to_pdf=False)
            return content

    except Exception as e:
        raise IOError(f"Error processing .doc file: {str(e)}")

    finally:
        for temp_path in [temp_doc_path, temp_docx_path]:
            if temp_path and os.path.exists(temp_path):
                try:
                    os.unlink(temp_path)
                except Exception:
                    pass


async def a_read_pdf_file(file: BinaryIO, page_range: Optional[dict] = None) -> str:
    """Async version of read_pdf_file - PyMuPDF is not async so we keep it sync"""
    return read_pdf_file(file, page_range)


async def a_read_txt_file(file: BinaryIO) -> str:
    """Async version of read_txt_file"""
    try:
        content = file.read()
        if isinstance(content, bytes):
            return content.decode("utf-8")
        return content
    except Exception as e:
        raise UnicodeDecodeError(
            "utf-8", b"", 0, 0, f"Error reading TXT file: {str(e)}"
        )


async def a_read_excel_file(file: BinaryIO) -> str:
    """Async version of read_excel_file - Excel libraries are not async"""
    try:
        return read_excel_file(file)
    except Exception as e:
        raise IOError(f"Error reading Excel file asynchronously: {str(e)}")


async def a_read_pptx_file(file: BinaryIO) -> str:
    """Async version of read_pptx_file - python-pptx is not async"""
    try:
        return read_pptx_file(file)
    except Exception as e:
        raise ValueError(f"Error reading PPTX file asynchronously: {str(e)}")


async def a_read_ppt_file(file: BinaryIO) -> str:
    """Async version of read_ppt_file using async subprocess"""
    temp_path = None
    try:
        with tempfile.NamedTemporaryFile(delete=False, suffix=".ppt") as temp_file:
            temp_path = temp_file.name
            temp_file.write(file.read())
            file.seek(0)

        output_path = temp_path + "x"

        try:
            # Use async subprocess for LibreOffice conversion
            proc = await asyncio.create_subprocess_exec(
                "soffice",
                "--headless",
                "--convert-to",
                "pptx",
                temp_path,
                "--outdir",
                os.path.dirname(temp_path),
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
            )

            _, stderr = await asyncio.wait_for(proc.communicate(), timeout=60)

            if proc.returncode != 0:
                raise RuntimeError(f"Conversion failed: {stderr.decode()}")

        except (FileNotFoundError, asyncio.TimeoutError):
            raise FileNotFoundError(
                "No se pudo convertir el archivo PPT. Asegúrese de que LibreOffice esté instalado."
            )

        if not os.path.exists(output_path):
            raise FileNotFoundError(
                "La conversión falló. El archivo PPTX no se generó."
            )

        async with aiofiles.open(output_path, "rb") as pptx_file:
            content = await pptx_file.read()
            pptx_io = io.BytesIO(content)
            return read_pptx_file(pptx_io)

    except Exception as e:
        raise IOError(f"Error procesando archivo PPT: {str(e)}")

    finally:
        if temp_path:
            try:
                os.unlink(temp_path)
                if os.path.exists(output_path):
                    os.unlink(output_path)
            except Exception:
                pass


async def a_extract_zip(file: BinaryIO) -> List[BinaryIO]:
    """Async version of extract_zip"""
    extracted_files = []
    supported_extensions = [".docx", ".pdf", "doc"]
    max_size = 100 * 1024 * 1024  # 100 MB
    max_files = 20

    with tempfile.TemporaryDirectory() as temp_dir:
        with zipfile.ZipFile(file) as zip_ref:
            processable_files = [
                info
                for info in zip_ref.infolist()
                if is_processable_file(info.filename, supported_extensions)
            ]

            print(
                f"Encontrados {len(processable_files)} archivos procesables de {len(zip_ref.infolist())} entradas totales"
            )

            total_processable_size = sum(info.file_size for info in processable_files)
            if total_processable_size > max_size:
                raise ValueError(
                    f"Los archivos procesables son demasiado grandes: {total_processable_size / 1024 / 1024:.1f}MB (límite: 100MB)"
                )

            if len(processable_files) > max_files:
                raise ValueError(
                    f"Demasiados archivos procesables en el ZIP: {len(processable_files)} (límite: {max_files})"
                )

            for info in processable_files:
                try:
                    zip_ref.extract(info, temp_dir)
                    file_path = os.path.join(temp_dir, info.filename)

                    if info.file_size > max_size / 5:
                        print(
                            f"Saltando {info.filename}: demasiado grande ({info.file_size / 1024 / 1024:.1f}MB)"
                        )
                        continue

                    async with aiofiles.open(file_path, "rb") as f:
                        content = await f.read()

                    file_obj = io.BytesIO(content)
                    file_obj.name = os.path.basename(info.filename)

                    extracted_files.append(file_obj)
                    print(f"Extraído: {file_obj.name}")

                except Exception as e:
                    print(f"Error extrayendo {info.filename}: {str(e)}")
                    continue

    print(f"Extraídos exitosamente {len(extracted_files)} archivos")
    return extracted_files
