import io
import json
from logging import Logger

from openai import AsyncOpenAI
from src.api.workflows.rubrics.extract_criteria.prompts import (
    system_prompt_criteria,
    user_prompt_criteria,
)
from src.api.workflows.rubrics.extract_criteria.request import (
    ExtractCriteriaRequest,
    Language,
)
from src.api.workflows.rubrics.extract_criteria.response import ExtractCriteriaResponse
from src.api.workflows.rubrics.services.file_service import FileService


class ExtractCriteriaWorkflow:
    """
    Workflow for extracting criteria from a document.
    """

    def __init__(self, openai_client: AsyncOpenAI, logger: Logger) -> None:
        self.openai_client = openai_client
        self.file_service = FileService()
        self.logger = logger

    async def execute(self, data: ExtractCriteriaRequest) -> ExtractCriteriaResponse:
        """
        Run the workflow to extract criteria from the document.
        """
        self.logger.info("Starting criteria extraction")

        file = io.BytesIO(data.document)
        file.name = data.document_filename
        content = await self.file_service.a_read_document(
            file, convert_docx_to_pdf=True
        )

        # Handle empty syllabus_files list
        syllabus_content = ""
        if data.syllabus_corpus is not None:
            file = io.BytesIO(data.syllabus_corpus)
            file.name = data.syllabus_corpus_filename
            syllabus_content = "\n\n".join(
                await self.file_service.a_read_document(file, convert_docx_to_pdf=True)
            )

        enhanced_content = f"""
ACTIVIDAD COMPLETA:
{content}
TEMARIO DEL CURSO:
{syllabus_content}

        """
        if data.previous_criteria:
            enhanced_content += f"""
CRITERIOS ORIGINALES QUE EL USUARIO QUIERE REGENERAR:
{json.dumps(data.previous_criteria.model_dump(), ensure_ascii=False, indent=2)}

"""
        if data.user_feedback:
            enhanced_content += f"""
FEEDBACK DEL USUARIO:
{data.user_feedback}

ASEGÚRATE DE MODIFICAR EXCLUSIVAMENTE LO QUE TE DIGA EL USUARIO, NADA MÁS.
            """
        if data.language == Language.EN:
            enhanced_content += """
TIENES QUE RESPONDER TODO OBLIGATORIAMENTE EN INGLÉS.
"""
        else:
            enhanced_content += """
TIENES QUE RESPONDER TODO OBLIGATORIAMENTE EN ESPAÑOL.
"""
        return await self._a_extract_criteria(enhanced_content)

    async def _a_extract_criteria(self, context: str) -> ExtractCriteriaResponse:
        """Extracts evaluation criteria from the activity content and syllabus"""
        self.logger.debug("Extracting criteria")

        try:
            response = await self.openai_client.responses.parse(
                instructions=system_prompt_criteria,
                input=user_prompt_criteria.format(context=context),
                model="o4-mini",
                text_format=ExtractCriteriaResponse,
            )
            return response.output_parsed
        except Exception as e:
            self.logger.error(f"Error extracting criteria: {str(e)}")
            raise
