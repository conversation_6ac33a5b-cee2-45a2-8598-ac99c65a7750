import json

from fastapi import APIRouter, File, Form, HTTPException, Query, UploadFile
from pydantic import ValidationError
from src.api.common.dependency_container import DependencyContainer
from src.api.workflows.rubrics.extract_criteria.request import (
    ExtractCriteriaRequest,
    Language,
)
from src.api.workflows.rubrics.extract_criteria.response import ExtractCriteriaResponse
from src.api.workflows.rubrics.generate_rubrics.request import (
    GenerateRubricsRequest,
)
from src.api.workflows.rubrics.generate_rubrics.response import GenerateRubricsResponse

router = APIRouter(prefix="/api/v1/rubrics", tags=["Rubrics"])


@router.post(
    "/extract_criteria",
    response_model=ExtractCriteriaResponse,
    description="""
Extrae los **criterios** de evaluación de una asignatura a partir de un documento y un corpus de syllabus.

Campos user feedback y criterios previos son opcionales. Si se proporcionan, se utilizarán para repetir la extraccion de criterios mejorando la previa.
Formato esperado de user_feedback: String
Formato esperado de criterios previos: JSON con la estructura de RubricCriteria.
```json
[
    {
    "order": 0,
    "name": "string",
    "description": "string",
    "justification": "string"
    }
]
```
""",
)
async def extract_criteria(
    subject_type: str = Query(..., description="Tipo de la asignatura"),
    language: Language = Query(Language.ES, description="Idioma de la rúbrica"),
    document: UploadFile = File(..., description="Archivo principal"),
    syllabus_corpus: UploadFile | None = File(
        None, description="Archivo de corpus de syllabus"
    ),
    user_feedback: str | None = Form(
        None, description="Feedback del usuario (texto plano)"
    ),
    previous_criteria: str | None = Form(
        None, description="Criterios previos en formato JSON"
    ),
) -> ExtractCriteriaResponse:
    document_bytes = await document.read()
    syllabus_corpus_bytes = await syllabus_corpus.read() if syllabus_corpus else None

    criteria_obj = None
    if previous_criteria is not None:
        try:
            criteria_obj = json.loads(previous_criteria)
            criteria_obj = ExtractCriteriaResponse(**criteria_obj)
        except ValidationError as e:
            raise HTTPException(
                status_code=422,
                detail={
                    "msg": "Error de validación en los criterios previos.",
                    "errors": e.errors(),
                },
            )
        except json.JSONDecodeError as e:
            raise HTTPException(
                status_code=422,
                detail={
                    "msg": "Error de formato en los criterios. Asegúrate de que es un JSON válido.",
                    "errors": str(e),
                },
            )

    data = ExtractCriteriaRequest(
        subject_type=subject_type,
        language=language,
        document=document_bytes,
        document_filename=document.filename,
        syllabus_corpus=syllabus_corpus_bytes,
        syllabus_corpus_filename=syllabus_corpus.filename if syllabus_corpus else None,
        user_feedback=user_feedback,
        previous_criteria=criteria_obj,
    )
    return await DependencyContainer().get_extract_criteria_workflow().execute(data)


@router.post(
    "/generate",
    response_model=GenerateRubricsResponse,
    description="""
Genera rúbricas basadas en los **criterios** extraídos.

---
### Formato esperado de `criteria`

```json
{
  "analysis": "string",
  "elements": [
    {
      "order": 0,
      "name": "string",
      "description": "string",
      "justification": "string"
    }
  ]
}
```
""",
)
async def generate_rubrics(
    subject_type: str = Query(..., description="Tipo de la asignatura"),
    language: Language = Query(Language.ES, description="Idioma de la rúbrica"),
    document: UploadFile = File(..., description="Archivo principal"),
    syllabus_corpus: UploadFile | None = File(
        None, description="Archivo de corpus de syllabus"
    ),
    criteria: str = Form(..., description="Criterios del ejercicio en formato JSON"),
) -> GenerateRubricsResponse:
    document_bytes = await document.read()
    syllabus_corpus_bytes = (
        await syllabus_corpus.read() if syllabus_corpus is not None else None
    )
    try:
        criteria_obj = json.loads(criteria)
        criteria_obj = ExtractCriteriaResponse(**criteria_obj)
    except ValidationError as e:
        raise HTTPException(
            status_code=422,
            detail={
                "msg": "Error de validación en los criterios previos.",
                "errors": e.errors(),
            },
        )
    except json.JSONDecodeError as e:
        raise HTTPException(
            status_code=422,
            detail={
                "msg": "Error de formato en los criterios. Asegúrate de que es un JSON válido.",
                "errors": str(e),
            },
        )
    data = GenerateRubricsRequest(
        subject_type=subject_type,
        language=language,
        document=document_bytes,
        document_filename=document.filename,
        syllabus_corpus=syllabus_corpus_bytes,
        syllabus_corpus_filename=syllabus_corpus.filename
        if syllabus_corpus is not None
        else None,
        criteria=criteria_obj,
    )
    return await DependencyContainer().get_generate_rubrics_workflow().execute(data)
