import json
from logging import Logger

from fastapi import HTTPException
from openai import AsyncClient
from src.api.common.services import SearchAgent
from src.api.common.services.search_agent import ExtractContentOutputModel

from .generate_reference_clases import (
    GenerateReferenceRequest,
    GenerateReferenceResponse,
)


class GenerateReferenceFromTextWorkflow:
    def __init__(
        self, openai_client: AsyncClient, search_agent: SearchAgent, logger: Logger
    ) -> None:
        self._openai_client = openai_client
        self._search_agent = search_agent
        self._logger = logger

    async def execute(
        self, request: GenerateReferenceRequest
    ) -> GenerateReferenceResponse:
        try:
            self._logger.info("Executing reference generation workflow.")
            doc, reason = await self.extract_reference(request.text)
            inline_citation = ""
            final_citation = ""
            publishing_date = doc.date or "n.d."
            if len(doc.authors) == 0:
                final_citation = f"{doc.title} ({publishing_date}). {doc.url}."
                inline_citation = f"{doc.title} ({publishing_date})."
            else:
                if len(doc.authors) == 1:
                    author_str = doc.authors[0]
                elif len(doc.authors) == 2:
                    author_str = f"{doc.authors[0]} & {doc.authors[1]}"
                else:
                    author_str = f"{doc.authors[0]} et al."
                final_citation = (
                    f"{author_str} ({publishing_date}). {doc.title}. {doc.url}."
                )
                inline_citation = f"({author_str}, {publishing_date})"

            self._logger.info("Reference generated successfully.")
            return GenerateReferenceResponse(
                reason=reason,
                inline_reference=inline_citation,
                final_reference=final_citation,
            )
        except HTTPException as http_exc:
            self._logger.error(
                f"HTTP error in extract_reference: {http_exc.detail}", exc_info=True
            )
            raise
        except Exception as e:
            self._logger.error(f"Error in execute: {e}", exc_info=True)
            raise HTTPException(status_code=500, detail="Error generating reference.")

    async def extract_reference(
        self, text: str
    ) -> tuple[ExtractContentOutputModel, str]:
        try:
            self._logger.info("Extracting reference from text.")
            if text is None or text.strip() == "":
                self._logger.error("Empty text provided for reference extraction.")
                raise HTTPException(status_code=400, detail="Text cannot be empty.")
            response = await self._openai_client.responses.create(
                input=text,
                model="gpt-4.1-nano",
                temperature=0,
                max_output_tokens=100,
                instructions="extrae un enunciado que resuma la tematica principal del texto que se te ha pasado, y que sea lo más breve posible, sin perder el sentido del texto original",
            )
            self._logger.debug(f"OpenAI summary response: {response.output_text}")

            result = await self._search_agent.run(
                f"```text\n{response.output_text}\n```",
                "Tu tarea es encontrar fuentes que esten relacionadas con el texto para añadirlas a la bibliografia del texto.\n\nApunta a minimizar el numero de busquedas, la velocidad es un requisito importante. Nunca hagas mas de una una busqueda a no ser que no encunetres resultados, quedate con el primer resultado valido que encuentres y realiza busqueda con fuentes tanto academicas como no academicas",
                1,
            )
            self._logger.debug(f"SearchAgent result: {result}")
            if not result.documents:
                self._logger.error("No documents found in SearchAgent result.")
                raise HTTPException(
                    status_code=404, detail="No relevant documents found."
                )
            result = result.documents[0]
            response = await self._openai_client.responses.create(
                input=f"# TEXTO\n```text\n{text}\n```\n\n# FUENTE\n\n```json\n{json.dumps(result.model_dump())}\n```",
                model="gpt-4.1-mini",
                temperature=0,
                max_output_tokens=500,
                instructions="Genera una explicacion de porque la fuente puede justificar el texto de la entrada en no mas de dos oraciones, referenciando los temas en comun.",
            )
            self._logger.debug(f"OpenAI justification response: {response.output_text}")

            self._logger.info("Reference extraction completed.")
            return result, response.output_text
        except HTTPException as http_exc:
            self._logger.error(
                f"HTTP error in extract_reference: {http_exc.detail}", exc_info=True
            )
            raise
        except Exception as e:
            self._logger.error(f"Error in extract_reference: {e}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail="Error extracting reference during OpenAI processing.",
            )
