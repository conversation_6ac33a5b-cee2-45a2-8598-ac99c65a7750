from fastapi import APIRouter
from src.api.common.dependency_container import DependencyContainer
from src.api.workflows.texts.generate_reference.generate_reference_clases import (
    GenerateReferenceRequest,
    GenerateReferenceResponse,
)

router = APIRouter(prefix="/api/v1/texts", tags=["Texts"])


@router.post("/generate_reference", response_model=GenerateReferenceResponse)
async def create_user(request: GenerateReferenceRequest) -> GenerateReferenceResponse:
    return (
        await DependencyContainer.get_generate_reference_from_text_workflow().execute(
            request
        )
    )
