import json
from logging import Logger

from openai import AsyncOpenAI
from pydantic import BaseModel, Field
from sqlalchemy.ext.asyncio import AsyncEngine
from sqlmodel import select
from sqlmodel.ext.asyncio.session import AsyncSession
from src.api.workflows.validation_tests.generate_plan.prompts import (
    generate_verification_plan_system_prompt,
)
from src.api.workflows.validation_tests.generate_plan.request import (
    GenerateValidationPlanRequest,
)
from src.api.workflows.validation_tests.generate_plan.response import (
    GenerateValidationPlanResponse,
)
from src.domain.models_toolkit import KnowledgeAreaInfo, Application, Activities


class ValidationPlanResponse(BaseModel):
    estructura_general: str = Field(
        description="Descripción general del formato y organización de la validación"
    )
    distribucion_puntos: str = Field(
        description="Puntuación total y distribución por secciones"
    )
    secciones_detalladas: str = Field(
        description="Secciones de la validación en formato markdown bien estructurado con nombre, tipo, número de preguntas, temas específicos y puntuación"
    )
    asignacion_tiempo: str = Field(
        description="Tiempo recomendado por sección y actividades"
    )
    criterios_evaluacion: str = Field(description="Qué se evalúa y rúbrica básica")


class GenerateValidationPlanWorkflow:
    """
    Workflow for extracting criteria from a document.
    """

    def __init__(
        self,
        openai_client: AsyncOpenAI,
        logger: Logger,
        db_engine: AsyncEngine,
        model: str = "gemini-2.5-flash",
    ) -> None:
        self.openai_client = openai_client
        self.logger = logger
        self.model = model
        self.db_engine = db_engine

    async def execute(
        self, request: GenerateValidationPlanRequest
    ) -> GenerateValidationPlanResponse:
        try:
            self.logger.info("Generando plan de la validación")

            reference_mode = (
                request.reference_mode.value
                if request.reference_mode
                else "[VACIO] - No se especificó modo de referencia"
            )

            async with AsyncSession(self.db_engine) as session:
                statement = (
                    select(KnowledgeAreaInfo)
                    .join(Application, KnowledgeAreaInfo.application_id == Application.id_application)
                    .where(KnowledgeAreaInfo.name == request.study_info)
                    .where(Application.name_application == Activities.GENERADOR_TESTS_VALIDACION_UNIPRO)
                )
                result = await session.exec(statement)
                area_info = result.first()

            if area_info is None:
                raise ValueError("Información del estudio no válida o no encontrada")
            data = f"Estudio: {area_info.name}\nDescripción: {area_info.description}"

            template_values = {
                "subject_info": json.dumps(data, indent=2),
                "selected_topics": request.activities_content,
                "validation_type": request.validation_type.value,
                "difficulty": request.difficulty.value,
                "duration": str(request.duration),
                "custom_instructions": request.custom_instructions,
                "reference_mode": reference_mode,
                "reference_structure": request.reference_context,
            }

            plan_response = await self.openai_client.chat.completions.parse(
                messages=[
                    {
                        "role": "user",
                        "content": generate_verification_plan_system_prompt.format(
                            **template_values
                        ),
                    },
                ],
                model=self.model,
                response_format=ValidationPlanResponse,
                max_completion_tokens=30000,
                temperature=0,
            )

            self.logger.info("Plan de la validación generado exitosamente")
            return GenerateValidationPlanResponse(
                validation_plan=self.flatten_object(
                    plan_response.choices[0].message.parsed
                )
            )

        except Exception as e:
            self.logger.error(f"Error generando plan de la validación: {str(e)}")
            raise

    @classmethod
    def flatten_object(cls, obj: ValidationPlanResponse) -> str:
        return f"""# Plan de la Validación
## Estructura General
{obj.estructura_general}

## Distribución de Puntos
{obj.distribucion_puntos}

## Secciones de la Validación
{obj.secciones_detalladas}

## Asignación de Tiempo
{obj.asignacion_tiempo}

## Criterios de Evaluación
{obj.criterios_evaluacion}
"""
