from pydantic import BaseModel, Field
from src.api.workflows.validation_tests.aux_enums import (
    DifficultyLevel,
    ReferenceMode,
    ValidationType,
)


class GenerateValidationPlanRequest(BaseModel):
    validation_type: ValidationType = Field(description="Tipo de validación a generar")
    difficulty: DifficultyLevel = Field(
        description="Nivel de dificultad de la validación"
    )
    duration: int = Field(description="Duración estimada en minutos", ge=30, le=180)
    custom_instructions: str = Field(
        description="Instrucciones específicas del docente para la validación"
    )
    reference_mode: ReferenceMode | None = Field(
        default=None,
        description="Cómo usar las validaciones de referencia si están disponibles",
    )
    activities_content: str = Field(
        ..., description="Contenido de las actividades de la asignatura (obligatorio)"
    )
    reference_context: str = Field(
        default=...,
        description="Validaciones anteriores para tomar como referencia (opcional)",
    )
    study_info: str = Field(
        default="", description="Información de la titulacion de la asignatura"
    )
