import io

from fastapi import APIRouter, File, Form, Query, UploadFile
from src.api.common.dependency_container import DependencyContainer
from src.api.workflows.rubrics.services.file_service import FileService
from src.api.workflows.validation_tests.aux_enums import (
    DifficultyLevel,
    ReferenceMode,
    ValidationType,
)
from src.api.workflows.validation_tests.generate.request import (
    GenerateValidationTestRequest,
)
from src.api.workflows.validation_tests.generate.response import (
    GenerateValidationTestResponse,
)
from src.api.workflows.validation_tests.generate_plan.request import (
    GenerateValidationPlanRequest,
)
from src.api.workflows.validation_tests.generate_plan.response import (
    GenerateValidationPlanResponse,
)

router = APIRouter(prefix="/api/v1/validation-tests", tags=["Validation tests"])

file_service = FileService()


@router.post(
    "/plans/generate",
    response_model=GenerateValidationPlanResponse,
    description=""" """,
)
async def generate_validation_plan(
    validation_type: ValidationType = Query(
        ValidationType.TEST_ONLY,
        description="Tipo de validación a generar",
    ),
    dificulty: DifficultyLevel = Query(..., description="Nombre de la asignatura"),
    duration: int = Query(
        ...,
        description="Duración estimada en minutos entre 30 y 180 minutos",
        ge=30,
        le=180,
    ),
    custom_instructions: str = Form(
        """Generar 10 preguntas tipo test.
Se marcarán las opciones válidas en negrita.
Cada pregunta correcta contará 1 punto y las incorrectas no restarán.
En el caso de proporcionarse dos actividades, 5 preguntas estarán relacionadas con la actividad 1 y las otras 5 con la actividad 2.""",
        description="Instrucciones específicas del docente para la validación",
    ),
    reference_mode: ReferenceMode | None = Query(
        None,
        description="Cómo usar las validaciones de referencia si están disponibles",
    ),
    faculty: str = Query(..., description="Facultad"),
    activities_content: UploadFile = File(
        ..., description="Archivos de actividades de la asignatura (obligatorio)"
    ),
    reference_context: UploadFile | None = File(
        None, description="Validaciones anteriores de referencia (opcional)"
    ),
) -> GenerateValidationPlanResponse:
    file = io.BytesIO(await activities_content.read())
    file.name = activities_content.filename
    readed_activity_content = await file_service.a_read_document(
        file=file, convert_docx_to_pdf=True
    )
    references_content = None
    if reference_context:
        file = io.BytesIO(await reference_context.read())
        file.name = reference_context.filename
        references_content = await file_service.a_read_document(
            file=file, convert_docx_to_pdf=True
        )
    data = GenerateValidationPlanRequest(
        validation_type=validation_type,
        difficulty=dificulty,
        duration=duration,
        custom_instructions=custom_instructions,
        reference_mode=reference_mode,
        activities_content=readed_activity_content,
        reference_context=references_content or "[VACIO]",
        study_info=faculty,
    )
    return await DependencyContainer.get_generate_validation_plan_workflow().execute(
        data
    )


@router.post(
    "/generate",
    response_model=GenerateValidationTestResponse,
    description=""" """,
)
async def generate_validation_test(
    validation_plan: str = Form(
        ...,
        description="Plan de validación generado",
    ),
    faculty: str = Query(..., description="Facultad"),
    activities_content: UploadFile = File(
        ..., description="Archivos de actividades de la asignatura (obligatorio)"
    ),
    reference_context: UploadFile | None = File(
        None, description="Validaciones anteriores de referencia (opcional)"
    ),
) -> GenerateValidationTestResponse:
    file = io.BytesIO(await activities_content.read())
    file.name = activities_content.filename
    readed_activity_content = await file_service.a_read_document(
        file=file, convert_docx_to_pdf=True
    )
    references_content = None
    if reference_context:
        file = io.BytesIO(await reference_context.read())
        file.name = reference_context.filename
        references_content = await file_service.a_read_document(
            file=file, convert_docx_to_pdf=True
        )
    data = GenerateValidationTestRequest(
        validation_plan=validation_plan,
        activities_content=readed_activity_content,
        reference_context=references_content or "[VACIO]",
        study_info=faculty,
    )
    return await DependencyContainer.get_generate_validation_test_workflow().execute(
        data
    )
