import json
from logging import Logger

from openai import Async<PERSON>penAI
from sqlalchemy.ext.asyncio import AsyncEng<PERSON>
from sqlmodel import select
from sqlmodel.ext.asyncio.session import AsyncSession
from src.api.workflows.validation_tests.generate.prompts import (
    generate_verification_test_system_prompt,
)
from src.api.workflows.validation_tests.generate.request import (
    GenerateValidationTestRequest,
)
from src.api.workflows.validation_tests.generate.response import (
    GenerateValidationTestResponse,
)
from src.domain.models_toolkit import Activities, Application, KnowledgeAreaInfo


class GenerateValidationTestWorkflow:
    """
    Workflow for extracting criteria from a document.
    """

    def __init__(
        self,
        openai_client: AsyncOpenAI,
        logger: Logger,
        db_engine: AsyncEngine,
        model: str = "gemini-2.5-pro",
    ) -> None:
        self.openai_client = openai_client
        self.logger = logger
        self.model = model
        self.db_engine = db_engine

    async def execute(
        self, request: GenerateValidationTestRequest
    ) -> GenerateValidationTestResponse:
        """
        Genera la validación completa basada en el plan proporcionado.
        """
        try:
            self.logger.info("Generando validación desde plan")

            activities_with_references = request.activities_content
            if (
                request.reference_context != "[VACIO]"
                and not request.reference_context.startswith("[ERROR]")
            ):
                activities_with_references += f"\n\n## VALIDACIONES DE REFERENCIA PARA CONTEXTO:\n{request.reference_context}"

            async with AsyncSession(self.db_engine) as session:
                statement = (
                    select(KnowledgeAreaInfo)
                    .join(Application, KnowledgeAreaInfo.application_id == Application.id_application)
                    .where(KnowledgeAreaInfo.name == request.study_info)
                    .where(Application.name_application == Activities.GENERADOR_TESTS_VALIDACION_UNIPRO)
                )
                result = await session.exec(statement)
                area_info = result.first()

            if area_info is None:
                raise ValueError("Información del estudio no válida o no encontrada")
            data = f"Estudio: {area_info.name}\nDescripción: {area_info.description}"

            template_values = {
                "subject_info": json.dumps(data, indent=2),
                "validation_plan": request.validation_plan,
                "topics_content": activities_with_references,
            }

            validation_response = await self.openai_client.chat.completions.create(
                messages=[
                    {
                        "role": "user",
                        "content": generate_verification_test_system_prompt.format(
                            **template_values
                        ),
                    },
                ],
                model=self.model,
                max_completion_tokens=30000,
                temperature=0,
            )

            self.logger.info("Validación generada exitosamente")
            return GenerateValidationTestResponse(
                validation_test=validation_response.choices[0].message.content
            )

        except Exception as e:
            self.logger.error(f"Error generando validación: {str(e)}")
            raise
