from pydantic import BaseModel, Field


class GenerateValidationTestRequest(BaseModel):
    validation_plan: str = Field(description="Texto con el plan de validación generado")
    activities_content: str = Field(
        ..., description="Contenido de las actividades de la asignatura (obligatorio)"
    )
    reference_context: str = Field(
        default="[VACIO]",
        description="Validaciones anteriores para tomar como referencia (opcional)",
    )
    study_info: str = Field(
        default="", description="Información de la titulacion de la asignatura"
    )
