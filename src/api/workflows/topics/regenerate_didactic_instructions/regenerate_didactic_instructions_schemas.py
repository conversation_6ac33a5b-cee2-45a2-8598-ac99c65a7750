from pydantic import BaseModel
from src.api.common.services.structs import (
    AIProcessInfo,
    ModelInfo,
)


class RegenerateDidacticInstructionsRequest(BaseModel):
    topic_id: int | None = None
    epigraph_id: int | None = None
    comment: str
    model_info: ModelInfo | None = None
    plan_version: int | None = None


class DidacticInstructionRegenerateItem(BaseModel):
    id: int | None = None  # Epigraph id
    position: int
    name: str
    didactic_instructions: str
    plan_version: int | None = None


class RegenerateDidacticInstructionsResponse(BaseModel):
    didactic_instructions: list[DidacticInstructionRegenerateItem]
    processes_info: list[AIProcessInfo]
    topic_id: int
