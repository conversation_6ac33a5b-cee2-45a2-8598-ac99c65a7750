from logging import Logger
from typing import Op<PERSON>, Set, Union

from fastapi import HTTPException, status
from sqlalchemy import Engine, func
from sqlalchemy.orm import selectinload
from sqlmodel import Session, select
from src.api.common.services import AI<PERSON>racer, ContentRegenerator
from src.api.common.services.content_regenerator.prompt_input_schemas import (
    ContentPlanInstruction,
    RegenerateDidacticInstructionsInputEpigraph,
    RegenerateDidacticInstructionsInputTopic,
)
from src.api.common.services.index_repository import Asignatura, IndexRepository
from src.api.common.services.structs import (
    AIProcessInfo,
    DidacticInstructionsReasoning,
    PromptContext,
)
from src.domain.models import (
    AIExecution,
    AIProcess,
    AIProcessStatus,
    AIProcessType,
    ContentPlan,
    Epigrafe,
    Tema,
)

from .regenerate_didactic_instructions_schemas import (
    DidacticInstructionRegenerateItem,
    RegenerateDidacticInstructionsRequest,
    RegenerateDidacticInstructionsResponse,
)


class RegenerateDidacticInstructionsWorkflow:
    def __init__(
        self,
        db_engine: Engine,
        logger: Logger,
        content_regenerator: ContentRegenerator,
        index_repository: IndexRepository,
        ai_tracer: AITracer,
    ) -> None:
        self._db_engine = db_engine
        self._logger = logger
        self._content_regenerator = content_regenerator
        self.index_repository = index_repository
        self._ai_tracer = ai_tracer

    async def execute(
        self, request: RegenerateDidacticInstructionsRequest
    ) -> RegenerateDidacticInstructionsResponse:
        self._logger.info(
            f"Received request for regenerate_didactic_instructions_workflow: {request}"
        )

        (
            tema,
            epigraph,
            epigrafe_ids,
            plan_version,
            content_plans,
            single_content_plan,
        ) = self._fetch_database_data(request)

        instrucciones_didacticas = [
            ContentPlanInstruction(
                epigraph_position=cp.epigrafe.position,
                epigraph_name=cp.epigrafe.name,
                didactic_instruction=cp.didactic_instructions or "",
            )
            for cp in content_plans
        ]
        self._logger.debug(f"Didactic instructions: {instrucciones_didacticas}")

        asignatura = await self.index_repository.get_subject_index(
            tema.bloque.indice_id
        )

        input_data = self._prepare_input_data(
            request,
            asignatura,
            tema,
            epigraph,
            instrucciones_didacticas,
            single_content_plan,
        )

        try:
            result, process = await self.regenerate_didactic_instructions(input_data)
        except Exception as e:
            self._logger.exception(f"Error during regeneration: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to regenerate instructions. Error: {e}",
            )

        return self._prepare_response_instructions(
            result, content_plans, process, request, plan_version
        )

    def _fetch_content_plans(
        self,
        session: Session,
        request: RegenerateDidacticInstructionsRequest,
        epigrafe_ids: Set[int],
        plan_version: int,
    ) -> tuple[list[ContentPlan], Optional[ContentPlan]]:
        if request.epigraph_id:
            content_plans = session.exec(
                select(ContentPlan)
                .where(
                    ContentPlan.epigrafe_id == request.epigraph_id,
                    ContentPlan.version == plan_version,
                )
                .options(selectinload(ContentPlan.epigrafe))  # type: ignore
            ).all()
            if not content_plans:
                self._logger.error(
                    f"No ContentPlan found for Epigrafe ID {request.epigraph_id} with version {plan_version}."
                )
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="No content plan found for the epigraph with the specified version.",
                )
            single_content_plan = content_plans[0]
        else:
            content_plans = session.exec(
                select(ContentPlan)
                .where(
                    ContentPlan.epigrafe_id.in_(epigrafe_ids),
                    ContentPlan.version == plan_version,
                )
                .options(selectinload(ContentPlan.epigrafe))
                .order_by(ContentPlan.epigrafe_id)  # type: ignore
            ).all()
            if not content_plans:
                self._logger.error(
                    f"No ContentPlans found for the given epigrafe_ids with version {plan_version}."
                )
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="No content plans found for the topic with the specified version.",
                )
            single_content_plan = (
                None  # Not applicable when not filtering by epigraph_id
            )

        return content_plans, single_content_plan

    def _verify_instructions(
        self,
        session: Session,
        request: RegenerateDidacticInstructionsRequest,
        epigrafe_ids: Set[int],
    ) -> int:
        if request.plan_version:
            self._logger.info(
                f"Verifying provided plan_version: {request.plan_version}"
            )

            distinct_count_stmt = select(
                func.count(ContentPlan.epigrafe_id.distinct())
            ).where(
                ContentPlan.version == request.plan_version,
                ContentPlan.epigrafe_id.in_(epigrafe_ids),
            )
            found_count = session.exec(distinct_count_stmt).one()
            self._logger.info(
                f"Found count for plan_version {request.plan_version}: {found_count}"
            )

            if found_count != len(epigrafe_ids):
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=(
                        f"Not all topics have plan_version={request.plan_version}. "
                        f"Only {found_count} out of {len(epigrafe_ids)} epigrafes match this version."
                    ),
                )

            return request.plan_version
        else:
            # Fetch the maximum version per epigrafe and ensure they are the same
            max_versions = {}
            for epigrafe_id in epigrafe_ids:
                max_version_stmt = select(func.max(ContentPlan.version)).where(
                    ContentPlan.epigrafe_id == epigrafe_id
                )
                max_version = session.exec(max_version_stmt).one()
                if max_version is None:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail=f"No content plan found for epigrafe_id {epigrafe_id}",
                    )
                max_versions[epigrafe_id] = max_version

            unique_versions = set(max_versions.values())
            self._logger.info(f"Unique max versions found: {unique_versions}")

            if len(unique_versions) > 1:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail=(
                        f"Not all epigrafes share the same plan version."
                        f" Found versions: {unique_versions}"
                    ),
                )

            selected_version = unique_versions.pop()
            self._logger.info(f"Selected plan_version: {selected_version}")

            return selected_version

    def _prepare_response_instructions(
        self,
        instructions: DidacticInstructionsReasoning,
        content_plans: list[ContentPlan],
        process: AIProcess,
        request: RegenerateDidacticInstructionsRequest,
        plan_version: int,
    ) -> RegenerateDidacticInstructionsResponse:
        instructions = instructions.output
        didactic_instructions_items: list[DidacticInstructionRegenerateItem] = []

        if request.epigraph_id:
            if len(instructions) != 1:
                self._logger.error(
                    f"Expected 1 regenerated instruction for Epigrafe ID {request.epigraph_id}, but got {len(instructions)}."
                )
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Mismatch between regenerated instructions and epigraph.",
                )
            regenerated_instruction = DidacticInstructionRegenerateItem(
                id=request.epigraph_id,
                position=content_plans[0].epigrafe.position if content_plans else 0,
                name=content_plans[0].epigrafe.name if content_plans else "Unknown",
                didactic_instructions=instructions[0].instruction,
                plan_version=plan_version,
            )
            didactic_instructions_items.append(regenerated_instruction)
        else:
            if len(instructions) != len(content_plans):
                self._logger.error(
                    f"Number of regenerated instructions ({len(instructions)}) does not match number of content plans ({len(content_plans)})."
                )
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Mismatch between number of regenerated instructions and content plans.",
                )

            for instr, cp in zip(instructions, content_plans):
                item = DidacticInstructionRegenerateItem(
                    id=cp.epigrafe_id,
                    position=cp.epigrafe.position,
                    name=cp.epigrafe.name,
                    didactic_instructions=instr.instruction,
                    plan_version=plan_version,
                )
                didactic_instructions_items.append(item)
                self._logger.debug(
                    f"Regenerated instruction for Epigrafe ID {cp.epigrafe_id}: {instr.instruction}"
                )

        return RegenerateDidacticInstructionsResponse(
            didactic_instructions=didactic_instructions_items,
            processes_info=[
                AIProcessInfo(
                    ai_process_id=process.id, ai_process_type=process.process_type
                )
            ],
            topic_id=request.topic_id,
        )

    def _get_subject_details(
        self, session: Session, topic_id: Optional[int], epigraph_id: Optional[int]
    ) -> tuple[Tema, Optional[Epigrafe], Set[int]]:
        if not topic_id and not epigraph_id:
            self._logger.error("Either topic_id or epigraph_id must be provided.")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Either topic_id or epigraph_id must be provided.",
            )

        # Fetch the Tema (Topic) if topic_id is provided
        tema: Optional[Tema] = None
        if topic_id:
            tema = session.exec(
                select(Tema)
                .options(selectinload(Tema.bloque))
                .where(Tema.id == topic_id)
            ).first()
            if not tema:
                self._logger.error(f"Tema with id {topic_id} not found.")
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND, detail="Topic not found."
                )
        else:
            # If topic_id is not provided, fetch it via epigraph
            epigraph_temp = session.get(Epigrafe, epigraph_id)
            if not epigraph_temp:
                self._logger.error(f"Epigrafe with id {epigraph_id} not found.")
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND, detail="Epigraph not found."
                )
            tema = session.exec(
                select(Tema)
                .options(selectinload(Tema.bloque))
                .where(Tema.id == epigraph_temp.id_tema)
            ).first()
            if not tema:
                self._logger.error(f"Tema with id {epigraph_temp.id_tema} not found.")
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Topic associated with epigraph not found.",
                )

        # Fetch the Epigrafe if epigraph_id is provided
        epigraph: Optional[Epigrafe] = (
            session.get(Epigrafe, epigraph_id) if epigraph_id else None
        )
        if epigraph_id and not epigraph:
            self._logger.error(f"Epigrafe with id {epigraph_id} not found.")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Epigraph not found."
            )

        # Retrieve all Epigrafes associated with the Tema
        epigrafe_list = session.exec(
            select(Epigrafe)
            .where(Epigrafe.id_tema == tema.id)
            .order_by(Epigrafe.position)
        ).all()  # type: ignore
        if not epigrafe_list:
            self._logger.error(f"No Epigrafes found for Tema with id {tema.id}.")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No epigraphs found for the topic.",
            )

        epigrafe_ids = {ep.id for ep in epigrafe_list}

        self._logger.info(f"Epigrafe IDs: {epigrafe_ids}")

        return tema, epigraph, epigrafe_ids

    def _fetch_database_data(self, request: RegenerateDidacticInstructionsRequest):
        with Session(self._db_engine) as session:
            tema, epigraph, epigrafe_ids = self._get_subject_details(
                session, request.topic_id, request.epigraph_id
            )
            plan_version = self._verify_instructions(session, request, epigrafe_ids)
            content_plans, single_content_plan = self._fetch_content_plans(
                session, request, epigrafe_ids, plan_version
            )
            session.expunge_all()
            return (
                tema,
                epigraph,
                epigrafe_ids,
                plan_version,
                content_plans,
                single_content_plan,
            )

    def _prepare_input_data(
        self,
        request: RegenerateDidacticInstructionsRequest,
        asignatura: Asignatura,
        tema: Tema,
        epigraph: Optional[Epigrafe],
        instrucciones_didacticas: list[ContentPlanInstruction],
        single_content_plan: Optional[ContentPlan],
    ) -> Union[
        RegenerateDidacticInstructionsInputTopic,
        RegenerateDidacticInstructionsInputEpigraph,
    ]:
        if epigraph:
            if not single_content_plan:
                self._logger.error(
                    "Single content plan is required when epigraph is provided."
                )
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Content plan missing for the provided epigraph.",
                )

            return RegenerateDidacticInstructionsInputEpigraph(
                nombre_asignatura=asignatura.nombre,
                esquema_asignatura=asignatura.estructura,
                instrucciones_content_plan=ContentPlanInstruction(
                    epigraph_position=epigraph.position,
                    epigraph_name=epigraph.name,
                    didactic_instruction=single_content_plan.didactic_instructions
                    or "",
                ),
                instrucciones_didacticas=instrucciones_didacticas,
                requisitos_instrucciones_didacticas=self._get_definicion_instrucciones(
                    tema.id
                ),
                comentario=request.comment,
                prompt_context=PromptContext(
                    id_tema=tema.id,
                    id_epigrafe=request.epigraph_id,
                    id_indice=tema.bloque.indice_id,
                ),
            )
        else:
            return RegenerateDidacticInstructionsInputTopic(
                nombre_asignatura=asignatura.nombre,
                esquema_asignatura=asignatura.estructura,
                instrucciones_didacticas=instrucciones_didacticas,
                requisitos_instrucciones_didacticas=self._get_definicion_instrucciones(
                    tema.id
                ),
                comentario=request.comment,
                prompt_context=PromptContext(
                    id_tema=tema.id, id_indice=tema.bloque.indice_id
                ),
            )

    def _get_definicion_instrucciones(self, topic_id: int) -> Optional[str]:
        with Session(self._db_engine) as session:
            process = session.exec(
                select(AIProcess)
                .where(
                    AIProcess.process_type == AIProcessType.DIDACTIC_INSTRUCTIONS,
                    AIProcess.tema_id == topic_id,
                )
                .order_by(AIProcess.created_at.desc())
            ).first()
            if not process:
                return ""
            ai_execution = session.exec(
                select(AIExecution)
                .where(
                    AIExecution.ai_process_id == process.id,
                    AIExecution.status == AIProcessStatus.COMPLETED,
                )
                .order_by(AIExecution.created_at.asc())
            ).first()
            return (
                ai_execution.input_data.get("instrucciones_didacticas", "")
                if ai_execution
                else ""
            )

    async def regenerate_didactic_instructions(
        self,
        input_data: Union[
            RegenerateDidacticInstructionsInputTopic,
            RegenerateDidacticInstructionsInputEpigraph,
        ],
    ) -> tuple[Optional[DidacticInstructionsReasoning], AIProcess]:
        if isinstance(input_data, RegenerateDidacticInstructionsInputEpigraph):
            prompt_name = "regenerate-epigraph-didactic-instructions"
        else:
            prompt_name = "regenerate-topic-didactic-instructions"

        return await self._content_regenerator._regenerate(
            input_data=input_data,
            prompt_name=prompt_name,
            expected_llm_output=DidacticInstructionsReasoning,
            process_type=AIProcessType.DIDACTIC_INSTRUCTIONS_REGENERATION,
            tema_id=getattr(input_data.prompt_context, "id_tema", None),
            indice_id=getattr(input_data.prompt_context, "id_indice", None),
            epigrafe_id=getattr(input_data.prompt_context, "id_epigrafe", None),
        )
