from datetime import datetime
from typing import Any

from pydantic import BaseModel
from src.domain.models import AIProcessStatus, AIProcessType


class GetTopicQueryParams(BaseModel):
    status: AIProcessStatus | None = None
    process_type: AIProcessType | None = None
    id_epigrafe: int | None = None


class GetTopicStatusItem(BaseModel):
    process_type: AIProcessType
    status: AIProcessStatus
    completed_at: datetime | None = None
    additional_metadata: dict[Any, Any] = {}


class GetTopicStatusResponse(BaseModel):
    items: list[GetTopicStatusItem]
