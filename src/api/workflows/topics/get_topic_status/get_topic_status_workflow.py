from logging import Logger

from sqlalchemy import Engine, Integer, cast, desc
from sqlmodel import Session, select
from src.domain.models import AIProcess

from .get_topic_status_schemas import (
    GetTopicQueryParams,
    GetTopicStatusItem,
    GetTopicStatusResponse,
)


class GetTopicStatusWorkflow:
    def __init__(self, db_engine: Engine, logger: Logger):
        self._db_engine = db_engine
        self._logger = logger

    def execute(
        self, tema_id: int, query_params: GetTopicQueryParams
    ) -> GetTopicStatusResponse:
        with Session(self._db_engine) as session:
            statement = select(AIProcess).where(AIProcess.tema_id == tema_id)

            # Apply optional filters
            if query_params.status is not None:
                statement = statement.where(AIProcess.status == query_params.status)
            if query_params.process_type is not None:
                statement = statement.where(
                    AIProcess.process_type == query_params.process_type
                )
            if query_params.id_epigrafe is not None:
                statement = statement.where(
                    cast(AIProcess.additional_metadata["epigrafe_id"].astext, Integer)
                    == query_params.id_epigrafe
                )

            statement = statement.order_by(desc(AIProcess.completed_at))  # type:ignore

            results = session.exec(statement).all()

            if not results:
                return GetTopicStatusResponse(items=[])

            response_items = [
                GetTopicStatusItem(
                    process_type=record.process_type,
                    status=record.status,
                    completed_at=record.completed_at,
                    additional_metadata=record.additional_metadata or {},
                )
                for record in results
            ]

            return GetTopicStatusResponse(items=response_items)
