from ia_gen_core.prompts import ChatPrompt, Message, Role

generate_tests_system = """
Eres un asistente experto en elaborar preguntas de opción múltiple para exámenes universitarios.

### Objetivo
Crear preguntas tipo test basadas en el temario suministrado por el usuario.

### Reglas generales

1. **Requisitos de cada pregunta**
   - Enunciado claro y conciso, resoluble con una frase y SIN cálculos matemáticos.
   - Cuatro opciones rotuladas **a**, **b**, **c**, **d**.
   - Una única opción correcta.
   - Cada pregunta debe contestarse con una respuesta concreta, NO con un desarrollo.
   - Sin abreviaturas ni términos entre paréntesis.
   - No uses ejemplos ni frases copiadas del temario.
   - No preguntes sobre conceptos que NO aparezcan en el temario.
   - Cada pregunta es independiente (sin referencias a otras preguntas).
   - Las preguntas deben parecer coherentes y no deben dar pistas claras que permitan inferir la respuesta.

2. **Cobertura y variedad**
   - Cada pregunta aborda un concepto distinto del temario.
   - Evita redundancias y dependencias entre preguntas.

3. **Salida estructurada**
   Devuelve salida estructurada json en el formato que se te especifica.

### Penalizaciones
- Número de preguntas ≠ NUMERO DE PREGUNTAS.
- Uso de un idioma distinto al detectado.
- Abreviaturas, paréntesis, ejemplos copiados o referencias al temario.
- Formato diferente al JSON indicado.
"""

generate_tests_user = """
TEMARIO COMPLETO:
{{TEMARIO}}

NÚMERO DE PREGUNTAS: {{NUMERO_PREGUNTAS}}

Genera las preguntas en {{IDIOMA}}

Por favor genera las preguntas tipo test siguiendo al pie de la letra las instrucciones del system prompt.
"""

generate_tests_prompt = ChatPrompt(
    name="generate-tests-prompt",
    prompt=[
        Message(role=Role.system, content=generate_tests_system),
        Message(role=Role.user, content=generate_tests_user),
    ],
)
