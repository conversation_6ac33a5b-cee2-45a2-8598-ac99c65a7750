from logging import Logger
from typing import Any

from fastapi import HTTPException, status
from ia_gen_core.prompts import PromptManager
from langchain.chat_models.base import BaseChatModel
from langchain_core.prompts import ChatPromptTemplate
from pydantic import BaseModel
from sqlalchemy import Engine
from sqlmodel import Session, delete, select
from src.api.common.services import IndexRepository, MailSender
from src.api.common.services.ai_tracer import AITracer
from src.api.workflows.topics.get_content.get_content_markdown_schemas import (
    GetContentMarkdownQueryParams,
)
from src.api.workflows.topics.get_content.get_content_markdown_workflows import (
    GetContentMarkdownWorkflow,
)
from src.domain.models import (
    AIProcessStatus,
    AIProcessType,
    Bloque,
    EmailTriggerStatus,
    Indice,
    IndiceStatus,
    Question,
    Tema,
    TopicStatus,
)

from .generate_tests_request import GenerateTestsRequest, GenerateTestsResponse


class Pregunta(BaseModel):
    id: int
    enunciado: str
    """Enunciado de la pregunta"""
    a: str
    """Opcion a"""
    b: str
    """Opcion b"""
    c: str
    """Opcion c"""
    d: str
    """Opcion d"""
    respuesta_correcta: str
    """Opcion de respuesta correcta, provee sólo la letra a|b|c|d y nada más"""
    razonamiento: str
    """explicación breve de por qué la opción correcta es la adecuada"""
    epigrafes_fuentes: list[str]
    """Lista de epigrafes con los cuales el alumno podría responder a la pregunta. *Nota: Los epigrafes son los que tienen el nivel markdown ###, asegurate de sólo incluir los que tengan el nivel ### como máximo nivel de granularidad, y nada por debajo.*"""


class ResultadoPreguntas(BaseModel):
    razonamiento_preguntas: str
    """Razonamiento sobre cómo responder a la solicitud del usuario de forma existosa"""
    preguntas: list[Pregunta]
    """Lista de preguntas de test a generar. Genera tantas preguntas como NUMERO DE PREGUNTAS se te solicite."""


class GenerateTestsWorkflow:
    def __init__(
        self,
        db_engine: Engine,
        logger: Logger,
        llm: BaseChatModel,
        tracer: AITracer,
        content_markdown_workflow: GetContentMarkdownWorkflow,
        index_repository: IndexRepository,
        prompt_manager: PromptManager,
        mail_sender: MailSender | None,
    ):
        self._db_engine = db_engine
        self._logger = logger
        self._llm = llm
        self._tracer = tracer
        self._content_markdown_workflow = content_markdown_workflow
        self._content_plan_version = 1
        self._index_repository = index_repository
        self._prompt_manager = prompt_manager
        self._mail_sender = mail_sender

    async def execute(self, request: GenerateTestsRequest) -> GenerateTestsResponse:
        """
        Execute the test generation workflow:
        1. Get the topic content in markdown format
        2. Delete any existing questions for this topic
        3. Generate tests using LLM
        4. Validate test results
        5. Store validated questions in database
        6. Return question IDs
        """

        ai_process = self._tracer.start_process(
            process_type=AIProcessType.GENERATE_TESTS,
            tema_id=request.topic_id,
        )

        try:
            query_params = GetContentMarkdownQueryParams(
                tema_id=request.topic_id,
                plan_version=self._content_plan_version,
                citations_at_end=True,
                epigrafe_id=None,
            )

            response = self._content_markdown_workflow.execute(query_params)
            markdown = response.markdown_content

            if markdown is None:
                await self._index_repository.change_topic_status(
                    request.topic_id, TopicStatus.TEST_FAILED
                )
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Topic with ID {request.topic_id} not found or has no content",
                )

            await self._index_repository.change_topic_status(
                request.topic_id, TopicStatus.TEST_IN_PROGRESS
            )

            with Session(self._db_engine) as session:
                order_id = session.exec(
                    select(Indice.order_id)
                    .join(Bloque, Bloque.indice_id == Indice.id)
                    .join(Tema, Tema.id_bloque == Bloque.id)
                    .where(Tema.id == request.topic_id)
                ).first()
                if order_id is None:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail=f"Order not found for topic ID {request.topic_id}",
                    )

            self._delete_existing_questions(request.topic_id)

            test_results = await self._generate_tests(
                markdown_content=markdown,
                num_questions=request.num_questions,
                topic_id=request.topic_id,
                ai_process=ai_process,
            )

            validated_questions = self._validate_questions(test_results)

            question_ids = self._store_questions(validated_questions, request.topic_id)

            self._tracer.complete_process(ai_process, status=AIProcessStatus.COMPLETED)
            await self._index_repository.change_topic_status(
                request.topic_id, TopicStatus.TEST_GENERATION
            )
            if self._mail_sender is not None:
                await self._mail_sender.async_send_email(
                    order_id=order_id,
                    status=EmailTriggerStatus.TOPIC,
                    topic_id=request.topic_id,
                )
            if await self._index_repository.update_index_based_on_topics(
                order_id,
                IndiceStatus.TEST_GENERATION,
                [
                    TopicStatus.TEST_GENERATION,
                    TopicStatus.TEST_REVIEW_PENDING,
                    TopicStatus.COMPLETED,
                ],
            ):
                if self._mail_sender is not None:
                    self._logger.info(
                        f"Sending notification email for order_id={order_id}."
                    )
                    await self._mail_sender.async_send_email(
                        order_id=order_id,
                        status=EmailTriggerStatus.INDEX,
                    )

            return GenerateTestsResponse(question_ids=question_ids)

        except Exception as e:
            self._logger.exception(f"Error generating tests: {e}")
            self._tracer.complete_process(ai_process, status=AIProcessStatus.FAILED)
            await self._index_repository.change_topic_status(
                request.topic_id, TopicStatus.TEST_FAILED
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to generate tests: {str(e)}",
            )

    def _delete_existing_questions(self, topic_id: int) -> None:
        """Delete existing questions for this topic"""
        with Session(self._db_engine) as session:
            session.execute(delete(Question).where(Question.topic_id == topic_id))
            session.commit()

    async def _generate_tests(
        self, markdown_content: str, num_questions: int, topic_id: int, ai_process: Any
    ) -> dict[str, Any]:
        """Generate tests using the LLM"""
        input_variables = {
            "TEMARIO": markdown_content,
            "NUMERO_PREGUNTAS": str(num_questions),
            "IDIOMA": "Español",
        }
        llm = self._llm.with_structured_output(ResultadoPreguntas)
        prompt = self._prompt_manager.get_prompt(
            name="generate-tests-prompt", prompt_type="chat"
        )
        prompt_messages = prompt.compile().to_langchain()
        template = ChatPromptTemplate.from_messages(prompt_messages)
        chain = template | llm
        chain = chain.with_retry(stop_after_attempt=3)

        metadata = {
            "flow": "Generate Tests",
            "chain": "Create test questions",
            "prompt_id": prompt.id,
            "topic_id": topic_id,
            "num_questions": num_questions,
        }

        try:
            result = await chain.with_config(metadata=metadata).ainvoke(input_variables)
            self._tracer.log_execution(
                ai_process=ai_process,
                input_data=input_variables,
                output_data=result.model_dump(),
                metadata=metadata,
            )
            return result
        except Exception as e:
            self._logger.exception(f"Error during LLM invocation: {e}")
            raise

    def _validate_questions(self, test_results: ResultadoPreguntas) -> list[Pregunta]:
        """Validate the generated questions"""
        if not hasattr(test_results, "preguntas") or not test_results.preguntas:
            raise ValueError("No questions were generated")

        questions = test_results.preguntas
        if len(questions) == 0:
            raise ValueError("Empty question list returned")

        # Validate each question
        for q in questions:
            if q.respuesta_correcta not in ["a", "b", "c", "d"]:
                raise ValueError(f"Invalid correct answer: {q.respuesta_correcta}")

        return questions

    def _store_questions(self, questions: list[Pregunta], topic_id: int) -> list[int]:
        """Store the validated questions in the database"""
        stored_ids = []

        with Session(self._db_engine) as session:
            for q in questions:
                db_question = Question(
                    topic_id=topic_id,
                    wording=q.enunciado,
                    option_a=q.a,
                    option_b=q.b,
                    option_c=q.c,
                    option_d=q.d,
                    correct_option=q.respuesta_correcta,
                    reasoning=q.razonamiento,
                    is_exam_ready=True,
                )
                session.add(db_question)
                session.commit()
                session.refresh(db_question)
                stored_ids.append(db_question.question_id)

        return stored_ids
