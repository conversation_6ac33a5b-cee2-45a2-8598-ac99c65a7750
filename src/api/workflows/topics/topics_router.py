from typing import List

from fastapi import APIRouter, BackgroundTasks, Depends, Response

from src.api.common.dependency_container import DependencyContainer
from src.api.workflows.topics import (
    GenerateAllDidacticInstructionsRequest,
    GenerateAllDidacticInstructionsResponse,
    GenerateTestsRequest,
    GenerateTestsResponse,
    GetContentMarkdownQueryParams,
    GetContentMarkdownResponse,
    GetInDepthByIndexRequest,
    GetInDepthByIndexResponse,
    GetTopicQueryParams,
    GetTopicsByIndexRequest,
    GetTopicsByIndexResponse,
    GetTopicStatusResponse,
    RegenerateDidacticInstructionsRequest,
    RegenerateDidacticInstructionsResponse,
)
from src.api.workflows.topics.generate_in_depth.generate_in_depth_aux_classes import (
    GenerateInDepthRequest,
)

router = APIRouter(prefix="/api/v1/topics", tags=["topics"])


@router.get("/{index_id}", response_model=list[GetTopicsByIndexResponse])
def get_topics_by_index(index_id: int):
    request = GetTopicsByIndexRequest(id=index_id)
    return DependencyContainer().get_topics_by_index_workflow().execute(request)


@router.get("/{tema_id}/status", response_model=GetTopicStatusResponse)
def get_topic_status(
    tema_id: int, params: GetTopicQueryParams = Depends()
) -> GetTopicStatusResponse:  # type: ignore
    return (
        DependencyContainer()
        .get_topic_status_workflow()
        .execute(tema_id=tema_id, query_params=params)
    )


@router.get("/{tema_id}/markdown", response_model=GetContentMarkdownResponse)
def get_content_markdown(
    tema_id: int,
    params: GetContentMarkdownQueryParams = Depends(),  # type: ignore
) -> GetContentMarkdownResponse:
    params_copy = params.model_copy(deep=True)
    params_copy.tema_id = tema_id
    return (
        DependencyContainer()
        .get_content_markdown_workflow()
        .execute(query_params=params_copy)
    )


@router.post(
    "/didactic_instructions/{order_id}/generate_all",
    response_model=GenerateAllDidacticInstructionsResponse,
)
async def generate_all_didactic_instructions(
    order_id: int, request: GenerateAllDidacticInstructionsRequest
):
    request.order_id = order_id
    return (
        await DependencyContainer()
        .get_generate_all_didactic_instructions_workflow()
        .execute(request)
    )


@router.post(
    "/didactic_instructions/{tema_id}/regenerate",
    response_model=RegenerateDidacticInstructionsResponse,
)
async def regenerate_didactic_instructions(
    tema_id: int, request: RegenerateDidacticInstructionsRequest
):
    request.topic_id = tema_id
    return (
        await DependencyContainer()
        .get_regenerate_didactic_instructions_workflow()
        .execute(request)
    )


@router.post(
    "/didactic_instructions/{tema_id}/epigraph/{epigrafe_id}/regenerate",
    response_model=RegenerateDidacticInstructionsResponse,
)
async def regenerate_didactic_instructions_epigraph(
    tema_id: int, epigrafe_id: int, request: RegenerateDidacticInstructionsRequest
):
    request.topic_id = tema_id
    request.epigraph_id = epigrafe_id
    return (
        await DependencyContainer()
        .get_regenerate_didactic_instructions_workflow()
        .execute(request)
    )


@router.post("/in_depth/{tema_id}/generate", response_model=None)
async def generate_in_depth(
    tema_id: int, request: GenerateInDepthRequest, background_tasks: BackgroundTasks
) -> Response:
    return (
        await DependencyContainer()
        .get_generate_in_depth_workflow()
        .execute(tema_id, request.num_references, background_tasks)
    )


@router.get("/in_depth/{topic_id}", response_model=List[GetInDepthByIndexResponse])
def get_in_depth_by_index(topic_id: int):
    request = GetInDepthByIndexRequest(id=topic_id)
    return DependencyContainer().get_in_depth_by_index_workflow().execute(request)


@router.post("/tests/{topic_id}/generate", response_model=GenerateTestsResponse)
async def generate_tests(topic_id: int, request: GenerateTestsRequest):
    request.topic_id = topic_id
    return await DependencyContainer().get_generate_tests_workflow().execute(request)
