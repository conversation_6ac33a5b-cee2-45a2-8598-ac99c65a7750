from pydantic import BaseModel
from src.api.common.services.search_agent import AgentResult


class TopicCorpus(BaseModel):
    topic_content: str
    section_names: list[str]


class ConceptList(BaseModel):
    concepts: list[str]
    "An array of key concepts that are relevant to the subject matter."


class JustifyResourceInputModel(BaseModel):
    concept: str
    resource: AgentResult


class ResourceJustification(BaseModel):
    scratchpad: str
    "a blank space for you to use as you need."
    justification: str
    "A paragraph that justifies the choice of the paper as a deepening content for a university subject topic."


class InDepthReferenceItem(BaseModel):
    title: str
    cite: str
    url: str
    justification: str


class TopicInDepth(BaseModel):
    references: list[InDepthReferenceItem]


class GenerateInDepthRequest(BaseModel):
    num_references: int = 3
