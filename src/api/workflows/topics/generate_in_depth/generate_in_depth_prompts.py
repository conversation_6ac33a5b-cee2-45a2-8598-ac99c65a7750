prompt_extract_queries = """Extract the most relevant concepts from the content of a university subject topic and articulate them in an atomic manner so that they can be easily searched on Google for more information.

You will receive the content of a topic as well as the names of the sections of the rest of the topics for context.

# Steps

1. **Analyze the Topic Content:** Carefully read through the topic content to understand the key points and subject area.
2. **Identify Key Concepts:** Determine the most significant concepts or terms used within the topic. Aim to select those that are central to understanding the material.
3. **Consider Additional Context:** Use the names of the sections from the rest of the topics to ensure that the selected concepts are not only relevant but also comprehensive in terms of the entire course’s framework.
4. **Articulate Atomically:** Write each concept in a brief, standalone manner suitable for being used as a search term on Google, ensuring clarity and searchability.

# Output Format

- List the identified concepts with a maximum of five entries, each as a short and clear phrase or term.

# Examples

**Input:**
- Topic Content: "The principles of quantum mechanics, including wave-particle duality and quantum entanglement, are foundational to modern physics. The topic discusses <PERSON><PERSON><PERSON>'s Uncertainty Principle, <PERSON><PERSON><PERSON><PERSON><PERSON>'s Equation, and the concept of superposition."
- Section Names: "Classical Mechanics, Thermodynamics, Electromagnetism"

**Output:**
1. Wave-particle duality
2. Quantum entanglement
3. <PERSON><PERSON><PERSON>'s Uncertainty Principle
4. Schrödinger's Equation
5. Superposition

(The above example can be expanded depending on the length and complexity of the real topic content provided, with more targeted concepts if necessary.)

# Notes

- The aim is to select concepts that both encapsulate the essential learnings of the topic and offer entry points for further research.
- Avoid including broad or generic terms that are not specific to the topic content received.
- Ensure that the identified concepts are accurate and central to the provided material.
- AIM TO EXTRACT THE AMMOUNT OF CONCEPTS STATED IN THE USER INPUT.
"""

prompt_justify_resource = """Vas a recibir un concepto relevante del temario de una asignatura universitaria, un titulo y una descripcion de un recurso que profundiza en el concepto. Tu tarea es realizar una descripcion justificativa de la eleccion del recurso como contenido de profundizacion para un temario de la asignatura. Explicale directamente al lector como el recurso puede ampliar su conocimiento sobre el concepto proporcionado y por que es interesante acceder al material recomendado. Se neutro y objetivo en tu justificacion, evitando afirmaciones exageradas o poco fundamentadas.
# Steps

1. Lee el concepto proporcionado y asegurate de comprenderlo completamente.
1. Lee el titulo y la descripcion del recurso.
2. Analiza como el concepto se relaciona con el contenido del recurso.
3. Determina que aspectos, fragmentos o ideas del recurso son especialmente relevantes para la comprension del tema universitario.
4. Explica de manera clara y justificativa la eleccion de este recurso y como amplia el conocimiento sobre el concepto.

# Output Format

- Un parrafo que justifique la eleccion del articulo, destacando su relevancia al ampliar tu conocimiento sobre el concepto escrito de forma entusiata pero profesional, no incluyas palabras como recurso o concepto en la justificacion, si quieres referenciarlos, hazlo de forma organica y natural.

- Debe tener una extension de aproximadamente 4-6 oraciones.

# Notes

- Evita simplemente resumir el recurso; en su lugar, enfocate en destacar como el contenido del mismo enriquece y amplia el aprendizaje del concepto proporcionado.
- Recuerda mantener una explicacion clara y persuasiva que te motive a explorar el recurso sin afirmar que su lectura es un requisito obligatorio. Y siempre centrala sobre lo que el rescurso puede aportar al conocimiento del concepto.
- SIEMPRE RESPONDE EN ESPAÑOL
"""
