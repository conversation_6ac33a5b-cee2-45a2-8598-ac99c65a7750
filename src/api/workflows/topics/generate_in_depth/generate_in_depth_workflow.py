import asyncio
import json
import logging

from fastapi import BackgroundTasks, Response
from ia_gen_core.prompts import PromptManager
from openai import AsyncOpenAI
from pydantic import BaseModel
from sqlalchemy import Engine
from sqlmodel import Session, select
from src.api.common.services import <PERSON><PERSON><PERSON>
from src.api.common.services.ai_tracer import AITracer
from src.api.common.services.evaluation import DocumentInfo
from src.api.common.services.evaluation.document_info_service import DocumentInfoService
from src.api.common.services.evaluation.document_info_sources import Author
from src.api.common.services.index_repository import IndexRepository
from src.api.common.services.search_agent import SearchAgent
from src.domain.models import (
    AIProcess,
    AIProcessStatus,
    AIProcessType,
    Bloque,
    ContenidoGenerado,
    ContentPlan,
    ContentPlanItem,
    Doc,
    EmailTriggerStatus,
    Epigrafe,
    InDepthReference,
    IndiceStatus,
    Tema,
    TopicStatus,
)

from .generate_in_depth_aux_classes import (
    <PERSON><PERSON><PERSON><PERSON>,
    Concept<PERSON>ist,
    InDepthReferenceItem,
    JustifyResourceInputModel,
    ResourceJustification,
    TopicCorpus,
    TopicInDepth,
)


class GenerateInDepthWorkflow:
    def __init__(
        self,
        db_engine: Engine,
        logger: logging.Logger,
        openai_client: AsyncOpenAI,
        ai_tracer: AITracer,
        search_agent: SearchAgent,
        prompt_manager: PromptManager,
        index_repository: IndexRepository,
        mail_sender: MailSender | None,
    ):
        self._db_engine = db_engine
        self._logger = logger
        self._ai_tracer = ai_tracer
        self._prompt_manager = prompt_manager
        self._openai_client = openai_client
        self._search_agent = search_agent
        self._index_repository = index_repository
        self._mail_sender = mail_sender

    async def execute(
        self, topic_id: int, num_references: int, background_tasks: BackgroundTasks
    ) -> Response:
        self._logger.info(
            f"Starting in-depth generation workflow for topic_id={topic_id} with {num_references} references."
        )
        await self._index_repository.change_topic_status(
            topic_id, TopicStatus.IN_DEPTH_IN_PROGRESS
        )
        ai_process = self._ai_tracer.start_process(
            AIProcessType.GENERATE_IN_DEPTH, tema_id=topic_id
        )
        background_tasks.add_task(
            self.generate_in_depth_from_topic, topic_id, num_references, ai_process
        )
        self._logger.info(
            f"Background task for in-depth generation added for topic_id={topic_id}."
        )
        return Response(
            content='{"message": "Topic in depth successfully requested"}',
            status_code=202,
            media_type="application/json",
        )

    async def generate_in_depth_from_topic(
        self, topic_id: int, num_references: int, ai_process: AIProcess
    ) -> None:
        self._logger.info(f"Generating in-depth content for topic_id={topic_id}.")
        try:
            section_names, topic_content, order_id = self._get_content_context(topic_id)
        except Exception as e:
            self._logger.error(
                f"Error fetching content context for topic_id={topic_id}: {e}"
            )
            await self._index_repository.change_topic_status(
                topic_id, TopicStatus.IN_DEPTH_FAILED
            )
            raise
        entrada = TopicCorpus(topic_content=topic_content, section_names=section_names)
        salida_final = TopicInDepth(references=[])

        self._logger.debug(f"Calling _generate_queries for topic_id={topic_id}.")

        try:
            queries = await self._generate_queries(entrada, num_references, ai_process)
        except Exception as e:
            self._logger.error(f"Error generating queries for topic_id={topic_id}: {e}")
            await self._index_repository.change_topic_status(
                topic_id, TopicStatus.IN_DEPTH_FAILED
            )
            raise

        self._logger.debug(
            f"Launching in-depth generation for {len(queries.concepts)} concepts."
        )

        used_references = self._get_used_references(topic_id)

        try:
            in_depth_results = await asyncio.gather(
                *[
                    self._generate_in_depth_from_concept(
                        data, ai_process, topic_id, used_references
                    )
                    for data in queries.concepts
                ]
            )
        except Exception as e:
            self._logger.error(
                f"Error generating in dephts for topic_id={topic_id}: {e}"
            )
            await self._index_repository.change_topic_status(
                topic_id, TopicStatus.IN_DEPTH_FAILED
            )
            raise
        salida_final.references.extend([r for r in in_depth_results if r is not None])
        self._logger.info(
            f"Completed in-depth generation for topic_id={topic_id}. Storing results."
        )
        try:
            self._ai_tracer.complete_process(ai_process)
            self._store_in_depth(topic_id, salida_final)
        except Exception as e:
            self._logger.error(f"Error storing in dephts for topic_id={topic_id}: {e}")
            await self._index_repository.change_topic_status(
                topic_id, TopicStatus.IN_DEPTH_FAILED
            )
            raise
        self._logger.info(
            f"Changing topic status to IN_DEPTH_GENERATION for topic_id={topic_id}."
        )
        await self._index_repository.change_topic_status(
            topic_id, TopicStatus.IN_DEPTH_GENERATION
        )
        if self._mail_sender is not None:
            self._logger.info(
                f"Sending notification email for topic_id={topic_id}, order_id={order_id}."
            )
            await self._mail_sender.async_send_email(
                order_id=order_id,
                status=EmailTriggerStatus.TOPIC,
                topic_id=topic_id,
            )
        if await self._index_repository.update_index_based_on_topics(
            order_id,
            IndiceStatus.IN_DEPTH_GENERATION,
            [
                TopicStatus.IN_DEPTH_GENERATION,
                TopicStatus.IN_DEPTH_REVIEW_PENDING,
                TopicStatus.TEST_IN_PROGRESS,
                TopicStatus.TEST_GENERATION,
                TopicStatus.TEST_REVIEW_PENDING,
                TopicStatus.TEST_FAILED,
                TopicStatus.COMPLETED,
            ],
        ):
            if self._mail_sender is not None:
                self._logger.info(
                    f"Sending notification email for order_id={order_id}."
                )
                await self._mail_sender.async_send_email(
                    order_id=order_id,
                    status=EmailTriggerStatus.INDEX,
                )

    def _get_content_context(self, topic_id: int) -> tuple[str, str]:
        self._logger.debug(f"Fetching content context for topic_id={topic_id}.")
        with Session(self._db_engine) as session:
            tema = session.exec(select(Tema).where(Tema.id == topic_id)).first()
            indice_id = tema.bloque.indice_id
            order_id = tema.bloque.indice.order_id
            epigraph_names = session.exec(
                select(Epigrafe.name)
                .join(Tema, Epigrafe.id_tema == Tema.id)
                .join(Bloque, Tema.id_bloque == Bloque.id)
                .where(Bloque.indice_id == indice_id)
            ).all()
            relevant_epigraphs = session.exec(
                select(Epigrafe).where(Epigrafe.id_tema == topic_id)
            ).all()
            topic_content = ""
            for epigraph in relevant_epigraphs:
                contents = [
                    content_plant_item.contenido_generado.contenido
                    for content_plant_item in epigraph.content_plans[-1].items
                ]
                topic_content += f"{epigraph.name}\n{'\n\n'.join(contents)}\n"
            self._logger.debug(f"Content context fetched for topic_id={topic_id}.")
            return epigraph_names, topic_content, order_id

    def _get_used_references(self, topic_id: int) -> list[str]:
        self._logger.debug(f"Fetching used references for topic_id={topic_id}.")
        with Session(self._db_engine) as session:
            document_ids = []
            document_id_lists = session.exec(
                select(ContenidoGenerado.documentos_relacionados)
                .join(
                    ContentPlanItem,
                    ContenidoGenerado.id_item_plan_contenido == ContentPlanItem.id,
                )
                .join(ContentPlan, ContentPlanItem.content_plan_id == ContentPlan.id)
                .join(Epigrafe, ContentPlan.epigrafe_id == Epigrafe.id)
                .join(Tema, Epigrafe.id_tema == Tema.id)
                .where(Tema.id == topic_id)
            )
            for i in document_id_lists:
                if i is not None:
                    document_ids.extend(i)
            document_ids = list(set(document_ids))
            documents = session.exec(select(Doc).where(Doc.id.in_(document_ids)))
            refs = [doc.document_url for doc in documents]
            self._logger.debug(
                f"Found {len(refs)} used references for topic_id={topic_id}."
            )
            return refs

    def _store_in_depth(
        self,
        topic_id: int,
        items: TopicInDepth,
    ):
        self._logger.info(f"Storing in-depth references for topic_id={topic_id}.")
        with Session(self._db_engine) as session:
            for i in items.references:
                in_depth_reference = InDepthReference(
                    title=i.title,
                    url=i.url,
                    cite=i.cite,
                    justification=i.justification,
                    topic_id=topic_id,
                )
                session.add(in_depth_reference)
            session.commit()
        self._logger.info(f"In-depth references stored for topic_id={topic_id}.")

    async def _call_api_structured(
        self,
        ai_process: AIProcess,
        prompt_name: str,
        user_input: str,
        model: str,
        output_class: type[BaseModel],
        temperature: float = 0.5,
        max_response_length: int = 100,
        top_probabilities: float = 0.9,
        frequency_penalty: float = 0.0,
        presence_penalty: float = 0.0,
        reasoning_effort: str | None = None,
    ) -> type[BaseModel]:
        self._logger.debug(f"Calling OpenAI API with prompt '{prompt_name}'.")
        prompt = self._prompt_manager.get_prompt(prompt_name, prompt_type="chat")
        messages = prompt.compile(user_input=user_input).to_list()
        args: dict[str, list[dict[str, str]]] = {
            "messages": messages,
            "model": model,
            "response_format": output_class,
        }

        if reasoning_effort is not None:
            args["reasoning_effort"] = reasoning_effort
        else:
            args["temperature"] = temperature
            args["max_tokens"] = max_response_length
            args["top_p"] = top_probabilities
            args["frequency_penalty"] = frequency_penalty
            args["presence_penalty"] = presence_penalty
        try:
            response = await self._openai_client.beta.chat.completions.parse(**args)
        except Exception as e:
            self._logger.error(f"Error calling OpenAI API: {e}")
            self._ai_tracer.log_execution(
                ai_process,
                user_input,
                {},
                metadata={},
                prompt_id=prompt.id,
                error_message=str(e),
                status=AIProcessStatus.FAILED,
            )
            raise e
        parsed_response = response.choices[0].message.parsed

        self._ai_tracer.log_execution(
            ai_process,
            user_input,
            parsed_response.model_dump(),
            metadata={},
            prompt_id=prompt.id,
        )
        self._logger.debug(
            f"OpenAI API call for prompt '{prompt_name}' completed successfully."
        )
        return parsed_response

    async def _apa_citation(self, info: DocumentInfoService) -> str:
        self._logger.debug(f"Formatting APA citation for document '{info.title}'.")
        authors_formatted = [
            f"{' '.join(author.name.split()[1:])}, {author.name.split()[0][0]}."
            for author in info.authors
        ]
        if len(authors_formatted) > 1:
            authors_str = (
                ", ".join(authors_formatted[:-1]) + ", & " + authors_formatted[-1]
            )
        elif len(authors_formatted) == 1:
            authors_str = authors_formatted[0]
        else:
            authors_str = ""

        apa_citation_output = f"{authors_str} ({info.published_year}). {info.title}."
        self._logger.debug(f"APA citation formatted: {apa_citation_output}")
        return apa_citation_output

    async def _generate_queries(
        self, entrada: TopicCorpus, num_references: int, ai_process: AIProcess
    ) -> ConceptList:
        self._logger.info(
            f"Generating queries for in-depth search (num_references={num_references})."
        )
        concepts = await self._call_api_structured(
            ai_process,
            prompt_name="prompt_extract_queries",
            model="o4-mini",
            user_input=json.dumps(entrada.model_dump())
            + "\n\n"
            + f"El resultado de la búsqueda debe incluir {num_references} conceptos clave.",
            output_class=ConceptList,
            reasoning_effort="low",
            max_response_length=3000,
        )
        self._logger.info(
            f"Generated {len(concepts.concepts)} queries for in-depth search."
        )
        return concepts

    async def _generate_in_depth(
        self, best: AgentResult, concept: str, ai_process
    ) -> InDepthReferenceItem:
        self._logger.debug(f"Generating in-depth reference for concept '{concept}'.")
        justification_input = JustifyResourceInputModel(concept=concept, resource=best)
        best = best.documents[0]
        authors = [Author(name=author, h_index=0) for author in best.authors]
        best_info = DocumentInfo(
            title=best.title,
            authors=authors,
            published_year=best.date,
            citation_count=0,
        )
        citation = await self._apa_citation(best_info)
        justification = await self._call_api_structured(
            ai_process,
            prompt_name="prompt_justify_resource",
            model="gpt-4.1",
            user_input=json.dumps(justification_input.model_dump()),
            output_class=ResourceJustification,
            temperature=1,
            max_response_length=1000,
        )
        self._logger.debug(f"In-depth reference generated for concept '{concept}'.")
        return InDepthReferenceItem(
            title=best.title,
            cite=f"{citation} {best.url}",
            url=best.url,
            justification=justification.justification,
        )

    async def _generate_in_depth_from_concept(
        self, data: str, ai_process, topic_id: int, used_references: list[str]
    ) -> InDepthReferenceItem | None:
        self._logger.debug(f"Running search agent for concept '{data}'.")
        result = await self._search_agent.run(
            data,
            f"""Estas haciendo la busqueda para la seccion "A Fondo" de una asigantura universitaria. Esta seccion esta orientada para que un estudiante que YA TIENE CONOCIMIENTOS DEL TEMARIO amplíe estos conocimientos con fuenteso ejemplos que aporte informacion que profundice o ejemplifique mas alla del de la descripcion del concepto.
El resultado de la búsqueda puede incluir papers que amplien la temática, páginas web que traten el tema de manera divulgativa y complementaria, sitios que ejemplifiquen la materia, así como videos de YouTube fiables tanto que ilustren como que expliquen la temática.
Para escribir el temario se han usado las siguientes fuentes: {used_references}, deben evitarse en la busqueda ya que los estudiantes ya las conocen.""",
            1,
        )
        self._logger.info(f"\n\nResult: {result}\n\n")

        if len(result.documents) == 0 or result.documents[0].title.strip() == "":
            self._logger.info(f"No result found for concept '{data}'. Skipping.")
            return None

        self._logger.debug(
            f"Generating in-depth reference for found result on concept '{data}'."
        )
        return await self._generate_in_depth(result, data, ai_process)
