from typing import Any, Optional

from pydantic import BaseModel, Field
from src.api.common.services.structs import AIProcessInfo, ModelInfo, PromptContext

default_instructions = """
La estructura general de un epígrafe ha de ser:

Comenzará por una introducción breve, seguida de la elaboración de los conceptos clave sobre los cuales trata el epígrafe en una forma narrativa.
Tratando de introducirlos de manera progresiva y cohesiva, evitando una excesiva segmentación de conceptos y conectando el contexto de los epígrafes anteriores a lo largo de la narración.

En cuanto a el orden de estos dentro de un tema:

Considerarás los otros epígrafes del tema para lo que será tratado en cada uno individualmente, evitando solapamientos.

Introducirás ejemplos y ejercicios prácticos cuando sea necesario a nivel global. Por tema introducirás entre 1 y 3 en alguno de los epígrafes. Considerás cuando es adecuado para evitar una repetición excesiva y ser moderado.

Combinarás cuando sea necesario elementos adicionales como tablas o gráficas, pero considerarás no abusar de ellas, teniendo en cuenta también incluirlas cuando sea adecuado dentro del contexto de todos los temas.
"""

default_length = """La extensión de referencia para un epígrafe es de entre 1000 y 2000 palabras, lo tendrás en cuenta para ajustar el nivel de detalle ilustrado en los mismos. Pueden darse excepciones donde haya que escribir más o menos, tenlo en cuenta, pero trata de seguir esa referencia.
Es importante que incluyas la extensión esperada que ha de tener cada epígrafe en base a eso."""


class GenerateAllDidacticInstructionsRequest(BaseModel):
    order_id: int | None = None
    didactic_instructions: str | None = Field(default=default_instructions)
    plan_version: int | None = 1
    recommended_length: str = Field(default=default_length)
    allow_increase_version: bool | None = False
    model_info: Optional[ModelInfo] = None


class DidacticInstructionsAllItem(BaseModel):
    id: int
    name: str
    position: int
    didactic_instructions: str
    version: int


class GenerateAllDidacticInstructionsResponse(BaseModel):
    didactic_instructions: list[DidacticInstructionsAllItem]
    processes_info: list[AIProcessInfo]


class GenerateDidacticInstructionsInput(BaseModel):
    instrucciones_didacticas: str
    asignatura: str
    contexto: str
    tema: str
    epigrafes: str
    len_epigrafes: int
    indice_asignatura: Any
    expected_output: str | None = None
    example: str | None = None
    prompt_context: PromptContext | None = None
    extensión_recomendada: str
