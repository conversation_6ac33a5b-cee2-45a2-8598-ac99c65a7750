import asyncio
from collections import defaultdict
from logging import Logger
from typing import List, <PERSON><PERSON>, cast

from fastapi import HTTP<PERSON>x<PERSON>, status
from ia_gen_core.prompts import PromptManager
from langchain.chat_models.base import BaseChatModel
from sqlalchemy.ext.asyncio import AsyncEngine
from sqlalchemy.orm import joinedload
from sqlmodel import delete, select
from sqlmodel.ext.asyncio.session import AsyncSession
from src.api.common.services import AITracer, MailSender
from src.api.common.services.index_repository import IndexRepository
from src.api.common.services.structs import (
    AIProcessInfo,
    Asignatura,
    DidacticInstructions,
    PromptContext,
)
from src.api.common.utils.check_prompt import (
    check_template_chat,
)
from src.api.common.utils.custom_llm import check_for_custom_llm_lc
from src.domain.models import (
    AIProcess,
    AIProcessType,
    Bloque,
    ContentPlanItem,
    EmailTriggerStatus,
    Indice,
    IndiceStatus,
    Order,
    Tema,
)

from .generate_all_didactic_instructions_schemas import (
    DidacticInstructionsAllItem,
    GenerateAllDidacticInstructionsRequest,
    GenerateAllDidacticInstructionsResponse,
    GenerateDidacticInstructionsInput,
)


class GenerateAllDidacticInstructionsWorkflow:
    def __init__(
        self,
        db_engine: AsyncEngine,
        logger: Logger,
        llm: BaseChatModel,
        index_repository: IndexRepository,
        prompt_manager: PromptManager,
        llm_tracer: AITracer,
        mail_sender: MailSender | None,
    ):
        self._db_engine = db_engine
        self._logger = logger
        self._llm = llm
        self._index_repository = index_repository
        self._prompt_manager = prompt_manager
        self._llm_tracer = llm_tracer
        self._mail_sender = mail_sender

    async def execute(
        self, request: GenerateAllDidacticInstructionsRequest
    ) -> GenerateAllDidacticInstructionsResponse:
        self._logger.info(f"Received GenerateAllDidacticInstructionsRequest: {request}")
        if request.model_info:
            custom_llm = check_for_custom_llm_lc(
                request.model_info.provider,
                request.model_info.name,
                reasoning_effort=request.model_info.reasoning_effort,
                max_tokens=request.model_info.max_tokens,
            )
            self._llm = custom_llm if custom_llm else self._llm

        (
            index_id,
            topics,
            index_with_relationships,
            subject_index,
            order_id,
        ) = await self._fetch_initial_data(request)

        input_data_temas = await self._prepare_all_input_data(
            topics,
            request.didactic_instructions,
            request.recommended_length,
            index_with_relationships,
            subject_index,
        )

        instr_by_topic, processes_info = await self._process_ai_operations(
            input_data_temas
        )

        ordered_instructions, ordered_epigraphs = await self._prepare_final_data(
            index_id, topics, instr_by_topic
        )

        all_epigraphs_results = await self._store_and_fetch_results(
            ordered_instructions, ordered_epigraphs, index_id
        )

        return GenerateAllDidacticInstructionsResponse(
            didactic_instructions=all_epigraphs_results,
            processes_info=processes_info,
        )

    async def _fetch_initial_data(
        self, request: GenerateAllDidacticInstructionsRequest
    ) -> tuple:
        async with AsyncSession(self._db_engine) as session:
            index = await self._index_repository.get_latest_index(
                request.order_id, session
            )
            index_id = index.id
            topics = await self._get_topics_for_index(index_id, session)
            await self._check_and_delete_contents(
                session, index, topics, request.plan_version
            )
            await self._index_repository.change_index_status(
                index_id, IndiceStatus.INSTRUCTIONS_GENERATION, session=session
            )

            stmt = (
                select(Indice)
                .options(joinedload(Indice.order).joinedload(Order.title_subject))
                .where(Indice.id == index_id)
            )

            result = await session.exec(stmt)

            index_with_relationships = result.first()
            subject_index = await self._index_repository.get_subject_index(
                indice_id=index_id, session=session
            )

        if self._mail_sender is not None:
            await self._mail_sender.async_send_email(
                order_id=index.order_id,
                status=EmailTriggerStatus.INDEX,
            )

        return index_id, topics, index_with_relationships, subject_index, index.order_id

    async def _prepare_all_input_data(
        self,
        topics: list,
        didactic_instructions: str,
        recommended_length: str,
        index_with_relationships,
        subject_index,
    ) -> list:
        epigrafe_contexts = {}
        async with AsyncSession(self._db_engine) as session:
            for topic in topics:
                epigrafes = sorted(topic.epigrafes, key=lambda ep: ep.position)
                if epigrafes:
                    epigrafe_context = (
                        await self._index_repository.get_epigrafe_context(
                            epigrafes[0].id, session
                        )
                    )
                    epigrafe_contexts[topic.id] = epigrafe_context

        input_data_temas = []
        for topic in topics:
            input_data = self._prepare_input_data_without_session(
                topic,
                didactic_instructions,
                recommended_length,
                index_with_relationships,
                subject_index,
                epigrafe_contexts.get(topic.id),
            )
            input_data_temas.append(input_data)
        return input_data_temas

    async def _process_ai_operations(self, input_data_temas: list) -> tuple:
        tasks = [
            asyncio.create_task(self.generate_didactic_instructions(input_data))
            for input_data in input_data_temas
        ]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        instr_by_topic = defaultdict(list)
        processes_info: list[AIProcessInfo] = []

        for result in results:
            if isinstance(result, Exception):
                self._logger.exception(
                    f"Exception while generating instructions: {result}"
                )
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Internal server error while generating instructions",
                )
            else:
                result_tuple = cast(Tuple[List[str], AIProcess], result)
                instructions, process = result_tuple
                instr_by_topic[process.tema_id] = instructions
                processes_info.append(
                    AIProcessInfo(
                        ai_process_id=process.id,
                        ai_process_type=process.process_type,
                    )
                )

        return instr_by_topic, processes_info

    async def _prepare_final_data(
        self, index_id: int, topics: list, instr_by_topic: dict
    ) -> tuple:
        async with AsyncSession(self._db_engine) as session:
            epigrafes = await self._index_repository.get_index_elements(
                indice_id=index_id,
                structure_type="epigrafe",
                session=session,
            )

        epigraphs_by_topic = defaultdict(list)
        for epigraph in epigrafes:
            epigraphs_by_topic[epigraph.id_tema].append(epigraph)
        for t_id in epigraphs_by_topic:
            epigraphs_by_topic[t_id].sort(key=lambda e: e.position)

        ordered_instructions = []
        ordered_epigraphs = []

        for topic in topics:
            t_id = topic.id
            instructions = instr_by_topic[t_id]
            epigrafs = epigraphs_by_topic[t_id]

            if len(instructions) != len(epigrafs):
                msg = (
                    f"Mismatch in lengths for topic_id={t_id}: "
                    f"{len(instructions)} instructions vs. {len(epigrafs)} epigraphs."
                )
                self._logger.warning(msg)
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Lengths of epigraphs and instructions don´t match",
                )

            ordered_instructions.extend(instructions)
            ordered_epigraphs.extend(epigrafs)

        return ordered_instructions, ordered_epigraphs

    async def _store_and_fetch_results(
        self, ordered_instructions: list, ordered_epigraphs: list, index_id: int
    ) -> list:
        async with AsyncSession(self._db_engine) as session:
            plan_version = await self._index_repository.store_didactic_instructions(
                ordered_instructions, ordered_epigraphs, session=session
            )
            epigrafes_with_context = (
                await self._index_repository.get_epigrafes_with_context(
                    index_id, plan_version=plan_version, session=session
                )
            )

        all_epigraphs_results: list[DidacticInstructionsAllItem] = []
        for epigrafe in epigrafes_with_context:
            item = DidacticInstructionsAllItem(
                id=epigrafe.id,
                name=epigrafe.nombre,
                position=epigrafe.position,
                didactic_instructions=epigrafe.didactic_instructions,
                version=plan_version,
            )
            all_epigraphs_results.append(item)

        return all_epigraphs_results

    async def _get_topics_for_index(
        self, indice_id: int, session: AsyncSession
    ) -> list[Tema]:
        """Get topics for an index using a synchronous session"""
        statement = (
            select(Tema)
            .join(Bloque, Tema.id_bloque == Bloque.id)
            .options(joinedload(Tema.epigrafes))
            .where(Bloque.indice_id == indice_id)
            .order_by(Tema.position)
        )
        result = await session.exec(statement)
        topics = result.unique().all()

        if not topics:
            self._logger.error(f"No Temas found for Indice ID {indice_id}.")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No Temas found for Indice ID {indice_id}.",
            )

        self._logger.info(f"Retrieved {len(topics)} Temas for Indice ID {indice_id}.")
        return topics

    def _prepare_input_data_without_session(
        self,
        tema: Tema,
        didactic_instructions: str,
        recommended_length: str,
        indice: Indice,
        asignatura: Asignatura,
        epigrafe_context=None,
    ) -> GenerateDidacticInstructionsInput:
        epigrafes = sorted(tema.epigrafes, key=lambda ep: ep.position)
        formatted_epigrafes = "\n\n".join(
            f"{i + 1}. {ep.name}" for i, ep in enumerate(epigrafes)
        )

        context = ""
        if epigrafe_context:
            context_dict = epigrafe_context.model_dump()
            del (
                context_dict["epigrafes_siguientes"],
                context_dict["epigrafes_anteriores"],
            )
            context = str(context_dict)

        return GenerateDidacticInstructionsInput(
            instrucciones_didacticas=didactic_instructions,
            asignatura=indice.order.title_subject.name,
            contexto=context,
            tema=tema.name,
            epigrafes=formatted_epigrafes,
            prompt_context=PromptContext(
                id_tema=tema.id,
                id_asignatura=indice.order.title_subject.id,
                id_indice=indice.id,
            ),
            indice_asignatura=str(asignatura.estructura.model_dump()),
            len_epigrafes=len(epigrafes),
            extensión_recomendada=recommended_length,
        )

    async def _check_and_delete_contents(
        self,
        session: AsyncSession,
        index: Indice,
        topics: list[Tema],
        plan_version: int,
    ):
        """
        Check for and delete existing content plans for the specified version.
        This method uses a synchronous session for the actual delete operation
        but an async session to fetch the content plans.
        """

        content_plans = []
        for topic in topics:
            plans = await self._index_repository.get_content_plan_linked(
                index.id, topic.id, version=plan_version, session=session
            )
            content_plans.extend(plans)

        plan_ids = [plan.id for plan in content_plans if plan.id]
        plan_item_ids = []
        for plan in content_plans:
            plan_item_ids.extend([item.id for item in plan.output if item.id])

        if not plan_ids and not plan_item_ids:
            return
        try:
            if plan_item_ids:
                delete_stm = delete(ContentPlanItem).where(
                    ContentPlanItem.id.in_(plan_item_ids)
                )
                await session.execute(delete_stm)
                await session.commit()
        except Exception as e:
            await session.rollback()
            self._logger.exception(
                f"Error deleting content plan items from the database: {e}"
            )
            raise

    async def generate_didactic_instructions(
        self, input_data: GenerateDidacticInstructionsInput
    ) -> tuple[List[str], AIProcess]:
        llm = self._llm.with_structured_output(DidacticInstructions)
        prompt = self._prompt_manager.get_prompt(
            name="didactic-instructions", prompt_type="chat"
        )

        few_shot = self._prompt_manager.get_few_shot_examples(prompt)
        if few_shot:
            few_shot = (
                "Input:\n\n" + few_shot[0].input + "\n\nOutput:" + few_shot[0].output
            )
            input_data.example = few_shot

        template, _ = check_template_chat(prompt, None)
        chain = template | llm
        chain.with_retry(stop_after_attempt=3)

        metadata = {
            "flow": "Didactic instructions",
            "chain": "Create instructions",
            "prompt_context": input_data.prompt_context.model_dump(),
            "prompt_id": prompt.id,
            "prompt_version": prompt.version,
            "prompt_name": prompt.name,
        }

        results = await chain.with_config(metadata=metadata).ainvoke(
            input_data.model_dump()
        )
        assert len(results.output) == input_data.len_epigrafes, (
            "Result not equal to len epigrafes."
        )
        instructions_result = []
        for result in results.output:
            instructions_result.append(result.instruction)
        process = self._llm_tracer.trace_process(
            AIProcessType.DIDACTIC_INSTRUCTIONS,
            input_data.model_dump(exclude={"prompt_context"}),
            instructions_result,
            metadata,
            indice_id=input_data.prompt_context.id_indice,
            tema_id=input_data.prompt_context.id_tema,
            prompt_id=prompt.id,
        )
        return instructions_result, process
