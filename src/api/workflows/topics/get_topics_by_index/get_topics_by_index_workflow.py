from logging import Logger

from fastapi import HTTPException, status
from sqlalchemy import Engine
from sqlmodel import Session, select
from src.domain.models import <PERSON><PERSON><PERSON>, <PERSON>ma

from .get_topics_by_index_schemas import GetTopicsByIndexRequest


class GetTopicsByIndexWorkflow:
    def __init__(self, db_engine: Engine, logger: Logger):
        self._db_engine = db_engine
        self._logger = logger

    def execute(self, request: GetTopicsByIndexRequest):
        with Session(self._db_engine) as session:
            statement = (
                select(Tema)
                .join(Bloque, Tema.id_bloque == Bloque.id)
                .where(Bloque.indice_id == request.id)
                .order_by(Tema.id)
            )  # type: ignore
            topics = session.exec(statement).all()
        if not topics:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Not found"
            )
        return topics
