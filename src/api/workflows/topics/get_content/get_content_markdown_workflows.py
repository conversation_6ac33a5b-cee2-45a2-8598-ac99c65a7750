from logging import Logger

from fastapi import HTTPException, status
from sqlalchemy import Engine
from sqlmodel import Session
from src.api.common.services.markdown_service import MarkdownService

from .get_content_markdown_schemas import (
    GetContentMarkdownQueryParams,
    GetContentMarkdownResponse,
)


class GetContentMarkdownWorkflow:
    def __init__(
        self, db_engine: Engine, logger: Logger, markdown_service: MarkdownService
    ):
        self._db_engine = db_engine
        self._logger = logger
        self._markdown_service = markdown_service

    def execute(
        self, query_params: GetContentMarkdownQueryParams
    ) -> GetContentMarkdownResponse:
        tema_id = query_params.tema_id
        with Session(self._db_engine) as session:
            try:
                markdown, _ = self._markdown_service.get_content_markdown(
                    session=session,
                    tema_id=tema_id,
                    content_plan_version=query_params.plan_version,
                    citations_at_end=query_params.citations_at_end,
                    epigrafe_id=query_params.epigrafe_id,
                )
                if markdown is None:
                    self._logger.warning(
                        f"No markdown content found for tema_id: {tema_id} with parameters: {query_params}"
                    )
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="Content not found for the given parameters.",
                    )
                return GetContentMarkdownResponse(markdown_content=markdown)
            except Exception as e:
                self._logger.exception(f"Error generating markdown content: {e}.")
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Content not found for the given parameters.",
                )
