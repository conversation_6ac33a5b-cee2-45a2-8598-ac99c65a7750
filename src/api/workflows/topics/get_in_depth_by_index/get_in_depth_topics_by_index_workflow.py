from logging import Logger

from fastapi import HTTPEx<PERSON>, status
from sqlalchemy import Engine
from sqlmodel import Session, select
from src.domain.models import InDepthReference

from .get_in_depth_by_index_schemas import (
    GetInDepthByIndexRequest,
    GetInDepthByIndexResponse,
)


class GetInDepthByIndexWorkflow:
    def __init__(self, db_engine: Engine, logger: Logger) -> None:
        self._db_engine = db_engine
        self._logger = logger

    def execute(
        self, request: GetInDepthByIndexRequest
    ) -> list[GetInDepthByIndexResponse]:
        self._logger.info(f"Entered in execute get in depth with: {request}")
        with Session(self._db_engine) as session:
            statement = select(InDepthReference).where(
                InDepthReference.topic_id == request.id
            )  # type: ignore
            references = session.exec(statement).all()
        if not references:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Not found"
            )
        else:
            return [
                GetInDepthByIndexResponse(
                    id=reference.id,
                    title=reference.title,
                    cite=reference.cite,
                    url=reference.url,
                    justification=reference.justification,
                )
                for reference in references
            ]
