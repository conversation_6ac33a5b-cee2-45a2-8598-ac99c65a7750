from logging import Logger

from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, status
from ia_gen_core.prompts import PromptManager
from langchain.chat_models.base import BaseChatModel

from src.api.common.services.ai_tracer import AITracer
from src.api.common.services.index_repository import Competencias, IndexRepository
from src.api.common.services.structs import PromptContext
from src.api.common.utils.check_prompt import check_template
from src.api.common.utils.custom_llm import check_for_custom_llm_lc
from src.domain.models import (
    AIProcess,
    AIProcessType,
    IndiceStatus,
)

from .generate_competencies_schemas import (
    AIProcessInfo,
    Competencie,
    GenerateCompetenciesRequest,
    GenerateCompetenciesResponse,
)
from .schemas import GenerateCompetenciesInput


class GenerateCompetenciesWorkflow:
    def __init__(
        self,
        logger: Logger,
        index_repository: IndexRepository,
        prompt_manager: PromptManager,
        llm: BaseChatModel,
        llm_tracer: AITracer,
    ) -> None:
        self._logger = logger
        self._index_repository = index_repository
        self._prompt_manager = prompt_manager
        self._llm = llm
        self._llm_tracer = llm_tracer

    async def execute(
        self, request: GenerateCompetenciesRequest
    ) -> GenerateCompetenciesResponse:
        if request.model_info:
            custom_llm = check_for_custom_llm_lc(
                request.model_info.provider, request.model_info.name
            )
            self._llm = custom_llm if custom_llm else self._llm
        try:
            (
                order,
                title_subject,
                subject,
            ) = await self._index_repository.get_order_and_subject_details(
                request.order_id
            )
        except Exception as e:
            self._logger.exception(
                f"Exception ocurred while getting title and order: {e}"
            )
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)

        if not request.descripcion:
            request.descripcion = subject.description if subject.description else ""
        input_data = GenerateCompetenciesInput(
            **request.model_dump(), asignatura=title_subject.name
        )

        input_data.prompt_context = PromptContext(id_asignatura=title_subject.id)

        competencias, process = await self._generate_competencies(input_data)

        index = await self._index_repository.create_index(order.id)
        index_id = index.id

        competencies_ids = await self._index_repository.store_competencies(
            index_id, competencias
        )

        self._index_repository.complete_ai_processes([process], index_id)
        await self._index_repository.change_index_status(
            index_id, IndiceStatus.NON_CONFIRMED_COMPETENCIES
        )

        ai_process_info = AIProcessInfo(
            ai_process_id=process.id, ai_process_type=process.process_type
        )
        competencies = [
            Competencie(id=id, description=c["descripcion"])
            for c, id in zip(
                competencias.model_dump()["competencias"], competencies_ids
            )
        ]

        self._logger.info(f"Generated competencies are: {competencies}")
        return GenerateCompetenciesResponse(
            competencies=competencies, processes_info=[ai_process_info]
        )

    async def _generate_competencies(
        self, input_data: GenerateCompetenciesInput
    ) -> tuple[Competencias, AIProcess]:
        flow, chain = "Competencies", "Competencies Chain"
        prompt = self._prompt_manager.get_prompt(
            "generate-competencies", prompt_type="text"
        )
        template, _ = check_template(prompt, input_data)
        llm = self._llm.with_structured_output(Competencias)

        competencies_chain = template | llm
        metadata = {
            "flow": flow,
            "chain": chain,
            "prompt_context": input_data.prompt_context.model_dump(),
            "prompt_id": prompt.id,
            "prompt_version": prompt.version,
            "prompt_name": prompt.name,
        }
        competencies = await competencies_chain.with_config(metadata=metadata).ainvoke(
            {**input_data.model_dump(exclude={"prompt_context"})}
        )
        process = self._llm_tracer.trace_process(
            AIProcessType.COMPETENCIE_WITHOUT_SCHEMA,
            input_data.model_dump(),
            competencies.model_dump(),
            metadata,
            prompt_id=prompt.id,
        )
        return competencies, process
