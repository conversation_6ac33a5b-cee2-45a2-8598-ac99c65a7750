from typing import List

from pydantic import BaseModel, Field

from src.api.common.services.structs import (
    AIProcessInfo,
    ModelInfo,
)


class GenerateCompetenciesRequest(BaseModel):
    order_id: int
    definicion_competencia: str | None = Field(
        default="Habilidad adquirida en el contexto académico de una asignatura, que es aplicable y útil en el ámbito laboral."
    )
    descripcion: str | None = None
    model_info: ModelInfo | None = None


class Competencie(BaseModel):
    id: int | None = None
    description: str


class GenerateCompetenciesResponse(BaseModel):
    competencies: List[Competencie]
    processes_info: List[AIProcessInfo]
