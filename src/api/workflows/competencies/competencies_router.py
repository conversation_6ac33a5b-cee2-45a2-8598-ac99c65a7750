from fastapi import APIRouter

from src.api.common.dependency_container import DependencyContainer
from src.api.workflows.competencies import (
    GenerateCompetenciesRequest,
    GenerateCompetenciesResponse,
    RegenerateCompetenciesRequest,
    RegenerateCompetenciesResponse,
)

router = APIRouter(prefix="/api/v1/competencies", tags=["competencies"])


@router.post("/generate", response_model=GenerateCompetenciesResponse)
async def generate_competencies(request: GenerateCompetenciesRequest):
    return await DependencyContainer.get_generate_competencies_workflow().execute(
        request
    )


@router.post("/regenerate", response_model=RegenerateCompetenciesResponse)
async def regenerate_competencies(request: RegenerateCompetenciesRequest):
    return await DependencyContainer.get_regenerate_competencies_workflow().execute(
        request
    )
