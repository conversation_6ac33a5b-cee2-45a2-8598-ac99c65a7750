from pydantic import BaseModel
from src.api.common.services.structs import (
    AIProcessInfo,
    ModelInfo,
)
from src.api.workflows.competencies.competencies_schemas import Competencie


class RegenerateCompetenciesRequest(BaseModel):
    order_id: int
    comentario: str
    model_info: ModelInfo | None = None


class RegenerateCompetenciesResponse(BaseModel):
    competencies: list[Competencie]
    processes_info: list[AIProcessInfo]
