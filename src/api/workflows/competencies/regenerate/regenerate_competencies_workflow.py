from logging import Logger

from fastapi import HTT<PERSON><PERSON>x<PERSON>, status
from sqlalchemy.ext.asyncio import AsyncEngine
from sqlmodel import and_, or_, select
from sqlmodel.ext.asyncio.session import AsyncSession
from src.api.common.services import AITracer
from src.api.common.services.content_regenerator.content_regenerator import (
    ContentRegenerator,
)
from src.api.common.services.content_regenerator.prompt_input_schemas import (
    RegenerateCompetenciesInput,
)
from src.api.common.services.index_repository import (
    Asignatura,
    Competencias,
    IndexRepository,
)
from src.api.common.services.structs import (
    AIProcessInfo,
    CompetenciasReasoning,
    PromptContext,
)
from src.api.common.utils.custom_llm import check_for_custom_llm_lc
from src.api.workflows.competencies.competencies_schemas import Competencie
from src.domain.models import (
    AIExecution,
    AIProcess,
    AIProcessStatus,
    AIProcessType,
    Indice,
    IndiceStatus,
    Order,
    TitleSubject,
)

from .regenerate_competencies_schemas import (
    RegenerateCompetenciesRequest,
    RegenerateCompetenciesResponse,
)


class RegenerateCompetenciesWorkflow:
    def __init__(
        self,
        db_engine: AsyncEngine,
        logger: Logger,
        index_repository: IndexRepository,
        content_regenerator: ContentRegenerator,
        ai_tracer: AITracer,
    ) -> None:
        self._db_engine = db_engine
        self._logger = logger
        self._index_repository = index_repository
        self._content_regenerator = content_regenerator
        self._ai_tracer = ai_tracer

    async def execute(
        self, request: RegenerateCompetenciesRequest
    ) -> RegenerateCompetenciesResponse:
        if request.model_info:
            custom_llm = check_for_custom_llm_lc(
                request.model_info.provider, request.model_info.name
            )
            self._content_regenerator._llm = (
                custom_llm if custom_llm else self._content_regenerator._llm
            )
        index, title_subject = await self._fetch_index(request.order_id)
        competencias = await self._index_repository.get_competencies(indice_id=index.id)
        asignatura = await self._index_repository.get_subject_index(index.id)
        initial_ai_process = await self._fetch_initial_ai_process(title_subject.id)

        definicion_competencia = await self._extract_definicion_competencia(
            initial_ai_process
        )
        input_data = self._prepare_input_data(
            request, asignatura, competencias, definicion_competencia, index.id
        )
        try:
            competencies, process = await self.regenerate_competencies(input_data)
        except Exception as e:
            self._logger.exception(f"Failed to regenerate competencies: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to regenerate competencies.",
            )
        if index.id:
            await self._index_repository.change_index_status(
                index.id, IndiceStatus.NON_CONFIRMED_COMPETENCIES
            )
        return RegenerateCompetenciesResponse(
            competencies=competencies,
            processes_info=[
                AIProcessInfo(
                    ai_process_id=process.id, ai_process_type=process.process_type
                )
            ],
        )

    async def _fetch_index(self, order_id: int) -> tuple[Indice, TitleSubject]:
        async with AsyncSession(self._db_engine) as session:
            index = await self._index_repository.get_latest_index(order_id, session)
            result = await session.exec(
                (
                    select(TitleSubject)
                    .join(Order, TitleSubject.id == Order.title_subject_id)
                    .join(Indice, Order.id == Indice.order_id)
                    .where(Indice.id == index.id)
                )
            )
            title_subject = result.first()
            if not title_subject:
                self._logger.exception("TitleSubject not found for the given index")
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="TitleSubject not found",
                )
            return index, title_subject

    async def _fetch_initial_ai_process(self, title_subject_id: int) -> AIProcess:
        async with AsyncSession(self._db_engine) as session:
            result = await session.exec(
                select(AIProcess)
                .join(Indice, AIProcess.indice_id == Indice.id)
                .join(Order, Indice.order_id == Order.id)
                .where(
                    and_(
                        or_(
                            AIProcess.process_type
                            == AIProcessType.COMPETENCIE_WITHOUT_SCHEMA,
                            AIProcess.process_type
                            == AIProcessType.COMPETENCIE_FROM_SCHEMA,
                        ),
                        Order.title_subject_id == title_subject_id,
                    )
                )
                .order_by(AIProcess.created_at.desc())
            )
            ai_process = result.first()
            if not ai_process:
                self._logger.exception(
                    "Initial AI Process for Competencie Generation not found"
                )
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Initial Competencie Generation process not found",
                )
            return ai_process

    async def _extract_definicion_competencia(self, ai_process: AIProcess) -> str:
        async with AsyncSession(self._db_engine) as session:
            result = await session.exec(
                select(AIExecution)
                .where(
                    AIExecution.ai_process_id == ai_process.id,
                    AIExecution.status == AIProcessStatus.COMPLETED,
                )
                .order_by(AIExecution.created_at.asc())
            )
            ai_execution = result.first()
            definicion = (
                ai_execution.input_data.get("definicion_competencia")
                if ai_execution
                else None
            )
            if not definicion:
                self._logger.exception(
                    "definicion_competencia not found in AIExecution"
                )
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Definicion competencia not found in AIExecution",
                )
            return definicion

    def _prepare_input_data(
        self,
        request: RegenerateCompetenciesRequest,
        asignatura: Asignatura,
        competencias: Competencias,
        definicion_competencia: str,
        indice_id: int,
    ) -> RegenerateCompetenciesInput:
        return RegenerateCompetenciesInput(
            nombre_asignatura=asignatura.nombre,
            esquema_asignatura=asignatura.estructura,
            competencias=competencias,
            requisitos_competencias=definicion_competencia,
            comentario=request.comentario,
            prompt_context=PromptContext(id_indice=indice_id),
        )

    async def regenerate_competencies(
        self, input_data: RegenerateCompetenciesInput
    ) -> tuple[Competencie | None, AIProcess]:
        def convert_result(r):
            return [
                Competencie(description=comp.descripcion) for comp in r.competencias
            ]

        return await self._content_regenerator._regenerate(
            input_data=input_data,
            prompt_name="regenerate-competencies-instructions",
            expected_llm_output=CompetenciasReasoning,
            process_type=AIProcessType.COMPETENCIE_REGENERATION,
            convert_result=convert_result,
            indice_id=getattr(input_data.prompt_context, "id_indice", None),
        )
