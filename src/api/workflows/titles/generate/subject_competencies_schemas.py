from typing import Any, List, Union

from pydantic import BaseModel
from src.api.common.services.structs import (
    AIProcessInfo,
    ModelInfo,
    PromptContext,
)


class ExtractDescriptorsInput(BaseModel):
    tipo_titulo: str
    nombre_titulo: str
    documento: str
    numero_año: int
    ejemplo: str | None = None
    prompt_context: PromptContext | None = None


class TitleCompetenciesInput(BaseModel):
    descriptor: str
    prompt_context: PromptContext | None = None


class GenerateSubjectCompetenciesRequest(BaseModel):
    title_id: int
    year_number: int
    term_number: int | None = None
    documento: str
    model_info: ModelInfo | None = None


class CompetenciesResponse(BaseModel):
    id: int
    description: str


class TitleSubjectResponse(BaseModel):
    id: int
    name: str
    competencies: List[CompetenciesResponse]


class GenerateSubjectCompetenciesResponse(BaseModel):
    title_subjects: List[Union[TitleSubjectResponse, Any]] = []
    processes_info: List[AIProcessInfo] | None = None
