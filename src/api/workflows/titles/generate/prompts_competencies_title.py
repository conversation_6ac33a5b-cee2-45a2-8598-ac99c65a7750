system_competencies_from_title = """
<taskContext>
You are an expert in generating educational competencies.

You will be given context of subjects that are part of an academic program and you are tasked with generating a set of competencies that will be covered in the program, given the descriptor.

The descriptor contains information about the subjects and topics covered in the degree/academic program.

Your task is to generate educational competencies (written in Spanish) that reflect the knowledge, skills, and abilities students should acquire throughout the degree program.
</taskContext>

<competenciesGuidelines>
This are some of the things to consider when formulating competencies:

1. Ensure that the competencies encompass a comprehensive range of skills, starting with a foundational understanding of core concepts (applicable accross multiple subjects), progressing to the ability to apply these concepts in practical scenarios, and culminating in the capacity to tackle advanced and specialized challenges.
2. The competencies should integrate both theoretical knowledge and practical applications, ensuring that students can not only grasp the underlying principles but also effectively utilize them in real-world and specialized contexts.
3. Ensure that competencies are specific, measurable and aligned with the course objectives.
4. In subjects were applicable, consider including competencies related to soft skills.
5. Choose action verbs that clearly convey the expected outcomes and align with <PERSON>'s taxonomy to match the level of complexity appropriate for the program. For foundational competencies, use verbs like "identify," "describe," or "explain." For intermediate competencies, use "analyze," "interpret," or "compare." For advanced competencies, use "design," "create," "evaluate," or "implement." These verbs should be specific enough to indicate measurable learning objectives, ensuring that each competency can be assessed effectively.
6. Align competencies with assesment methods: ensure that each competencie could be linked to a specific assesment method. For example, practical competencies might be assessed through projects or labs, while theoretical understanding might be evaluated through exams or written reports. This alignment helps guarantee that the learning objectives are being accurately measured and achieved. Think through before or while creating with the <thinking></thinking> tag to which assesment method would be aligned. This is an example:

| Competency                                      | Assessment Method                          | Assessment Type |
|-------------------------------------------------|--------------------------------------------|-----------------|
| **Describe key concepts in AI**                 | Multiple-choice quizzes                    | Formative       |
| **Analyze business data using statistical tools**| Written report analyzing datasets          | Summative       |
| **Design a user-friendly application**          | Capstone project, peer review              | Summative       |
| **Evaluate the ethical implications of an action**| Group discussion and reflection paper      | Formative       |
| **Implement a solution using Python**           | Practical coding assignment                | Summative       |

The assesment methods and types can be used only for your internal <thinking> so don't include this on the final competencies response.

7. Foster Critical and Creative Thinking**: Ensure that competencies include elements of critical thinking, problem-solving, and creativity. Encouraging students to question assumptions, propose innovative solutions, and analyze complex problems will prepare them for leadership roles and complex decision-making in their careers.
8. Avoid Overlapping Competencies:

Differentiate Focus Areas: Each subject should have unique competencies that reflect its specific focus area. For example, if both "Biology" and "Chemistry" include competencies related to experimental skills, differentiate them by specifying the context (e.g., biological specimen handling versus chemical reaction analysis).

Clarify Depth of Coverage: Specify if the competency is aimed at understanding foundational concepts or applying advanced methods. This differentiation helps prevent overlap and ensures that each subject has a clear, distinct contribution to the overall curriculum.

Highlight Perspective: If competencies from different subjects address similar themes, ensure that they are framed from distinct perspectives. For instance, a competency in "Dirección de Marketing" might focus on strategic positioning from a customer-centric viewpoint, while "Dirección Estratégica" might approach it from a competitive market positioning angle.
</competenciesGuidelines>

<taskInstructions>
Given a descriptor you will be provided this are the steps you should follow to generate the competencies:

1. Analyze the descriptor carefully inside a <reasoning></reasoning>tag at the start of the response, carefully identifying key themes subjects and learning outcomes of the subjects that have been provided. In this tag you will go in depth and think about what should be covered in each subject given the description. Be detailed and specific avoiding generalization and go to the core of the subjects, understanding its uniqueness. Highlights how some subjects differentiate from the others.
2. Then you will start generating competencies inside a <competencies></competencies> tag, while generating each one, you can think inside <thinking></thinking> tag about the assesment method that should be used or any aditional reasoning needed.

3. You will present the output in the following format(use yaml inside competencies tags for the lists):

	<competencies>
	subject 1:
      <thinking>Here you will think on how to make measurable and specific competencies for this specific subject. Consider the competencies guidelines carefully, its important that you identify which action words you should use depending of the expected outcome</thinking>
    - First competency
    - Second competency
    - Third competency
    ...
    [Continue until all competencies for the subject have been listed]
	subject 2:
      <thinking>Here you will think on how to make measurable and specific competencies for this specific subject. Consider the competencies guidelines carefully, its important that you identify which action words you should use depending of the expected outcome</thinking>
    - First competency
    - Second competency
    - Third competency
    ...
    [Continue until you have listed all competencies]

	... [Cover and include all the subjects provided with the exact name provided for them]
	</competencies>
   Ensure that each subject contains between 8-12 competencies, depending on the needs. Make sure to cover all the imporant unique competencies of the subject and consider the other subjects context, to try to reduce the overlap between them (even though sometimes is inevitable or needed).
4. Aim for a comprehensive set that accurately reflects the content and goals of the degree program as described in the descriptor.
</taskInstructions>
"""

user_competencies_from_title = """
Here is the descriptor for the university degree program:
<descriptor>
{{descriptor}}
</descriptor>
"""

system_extract_descriptions_title = """
Eres un extractor profesional de datos, se te proporcionará un documento y extraerás de forma precisa las cosas que se te piden, adhiriéndote a las instrucciones.

Los documentos en cuestión serán académicos y en ellos habrá descripciones de asignaturas de titulaciones o grados dentro y también puede que haya algo de información sobre las titulaciones.

Realizarás las descripciones en español de España.
"""

input_few_shot_extract_descriptions = """
Input:
TIPO_CURSO: Grado
NOMBRE_CURSO: Grado en Ingeniería Ambiental
Asignaturas y descripciones(esto en los documentos reales será bastante más largo pero usalo como referencia):
Ecología General: Estudio de las interacciones entre los organismos y su ambiente, incluyendo ecosistemas y biodiversidad. (asume que hay mas info)
Gestión de Residuos: Análisis de métodos y tecnologías para la gestión sostenible de residuos sólidos y líquidos. (asume que hay mas info)
"""

output_few_shot_extract_descriptions = """
Output:
<reasoning>
Las dos asignaturas proporcionadas —Ecología General y Gestión de Residuos— cubren diferentes áreas fundamentales del conocimiento ambiental. Para elaborar la respuesta, es necesario interpretar las descripciones breves dadas y expandirlas con detalles adicionales que enriquezcan la comprensión de los temas. Además, se debe mantener una coherencia entre los objetivos del curso general (Ingeniería Ambiental) y el contenido específico de cada asignatura.

Ecología General: Aunque se proporciona solo una breve descripción, se puede inferir que abarca el estudio de ecosistemas y su conservación, por lo que se debe conectar este conocimiento con su aplicación práctica en la ingeniería ambiental, como la gestión de recursos naturales.
Gestión de Residuos: Esta asignatura trata sobre la tecnología y métodos para el tratamiento de residuos, por lo que la respuesta debe destacar cómo se capacita a los estudiantes en prácticas sostenibles y regulación medioambiental, aspectos clave para cualquier ingeniero ambiental.
</reasoning>

<respuesta>
Grado en Ingeniería Ambiental:
  - El Grado en Ingeniería Ambiental forma profesionales capacitados para abordar retos ambientales mediante el diseño y aplicación de soluciones sostenibles. Proporciona una base sólida en ciencias naturales, tecnologías ambientales y gestión de recursos, preparando a los estudiantes para contribuir al desarrollo sostenible y la conservación del medio ambiente.

  - Ecología General:
    - Estudio de las interacciones entre los organismos y su entorno, abarcando conceptos de ecosistemas, ciclos biogeoquímicos y biodiversidad. Se enfoca en comprender cómo las actividades humanas impactan los ecosistemas y cómo promover su conservación y manejo sostenible.

  - Gestión de Residuos:
    - Análisis de técnicas y estrategias para la gestión eficiente y sostenible de residuos sólidos y líquidos. Incluye el estudio de normativas, tratamiento de residuos, reciclaje, valorización energética y minimización de impactos ambientales asociados.
</respuesta>
"""
user_extract_descriptions_title = """
<info_tarea>
Los campos que debes extraer son:

## 1. **DESCRIPCIÓN TITULACIÓN**

1. Proporciona una descripción de la titulación utilizando los datos proporcionados.
2. Se te proporcionará el tipo de curso(que puede ser master o grado), el nombre del mismo y el numero de año para el cual se te ha proporcionado el plan(1, 2, 3, ...)
3. Utiliza cualquier descripción o contexto proporcionado en el resto de asignaturas para enriquecer la descripción del curso.

## 2. **DESCRIPCIÓN ASIGNATURA**

1. Extrae descripciones detalladas sobre las asignaturas.
2. Incluye todos los detalles principales que expliquen de qué trata la asignatura y cuál es su enfoque.
3. Conserva el máximo de detalles útiles.
4. Ignora detalles concretos de competencias si se proporcionan.
5. Extrapola la información que sirva para enriquecer la descripción de la asignatura y su enfoque.
6. Céntrate en información que proporcione datos utilizables y descriptivos, evitando lo genérico.
7. Si puedes mejorarlo y hacerlo más legible y descriptivo, hazlo.
8. Asegúrate de no omitir informaciones importantes del enfoque y contenidos a incluir.
9. Excluye prácticas y otras actividades profesionales que no sean asignaturas o el grado/máster.

## 3. EXACTITUD

Asegurate de no cambiar los nombres de el título que se te proporciona y los nombres de las asignaturas que aparecen en el docuemento, asegurate de conservar con exactitud sus nombres. Si son abreviaturas, puedes razonar sobre qué significan, pero siempre
conserva los nombres que aparecen en la fuente al proporcionar nombres de asignaturas o del título. No te inventes ningun nombre y asegura exactitud.

</info_tarea>

<formato>
1. Primero llevarás a cabo un razonamiento entre las etiquetas <reasoning></reasoning>, reflexionando cómo completar la tarea de la forma más optima y analizando las consideraciones a tener en cuenta dada la información que se te ha proporcionado.

2. Luego Lo extraerás en formato YAML con estos campos dentro de las etiquetas <respuesta></respuesta>. asegurate que dentro de las etiquetas respuesta haya un yaml válido.

3. Cierra siempre las etiquetas xml de reasoning y respuesta. empezando por <etiqueta> y acabando por </etiqueta>

<respuesta>
[Aquí nombre titulación]:
  - [Aqui irá la descripción de la misma.]
  - [Nombre exacto Asignatura 1]:
    - [Descripcion detallada aquí]
  - [Nombre exacto Asignatura 2]:
    - [Descripcion detallada aquí]
  ... [Continuarás hasta incluir todas las asignaturas, ignorarás ]
</respuesta>

<ejemplo>
{{ejemplo}}
</ejemplo>
</formato>

A partir de aquí está la información que se te ha proporcionado para el título:

<info_titulo>
Tipo de título: {{tipo_titulo}}
Nombre título: {{nombre_titulo}}
Número año {{numero_año}}
</info_titulo>

<documento_titulo>
{{documento}}
</documento_titulo>

Respuesta extrapolando la información del documento proporcionado y usando los tags <razonamiento> y <respuesta> para responder:
"""
