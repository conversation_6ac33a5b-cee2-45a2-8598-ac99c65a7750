from logging import Logger
from typing import Dict, List, Tuple

from fastapi import HTT<PERSON><PERSON>x<PERSON>, status
from ia_gen_core.prompts import PromptManager
from langchain.chat_models.base import BaseChatModel
from pydantic import ValidationError
from sqlalchemy import Engine
from sqlalchemy.orm import joinedload
from sqlmodel import Session, desc, select
from src.api.common.services import AITracer
from src.api.common.services.content_generator.utils.parsing import (
    extract_and_remove_content,
    extract_content,
    parse_yaml,
)
from src.api.common.services.index_repository import IndexRepository
from src.api.common.services.structs import (
    AIProcessInfo,
    Competencia,
    Competencias,
    SubjectDescription,
)
from src.api.common.utils.check_prompt import (
    check_template_chat,
)
from src.api.common.utils.custom_llm import check_for_custom_llm_lc
from src.domain.models import (
    AIExecution,
    AIProcess,
    AIProcessStatus,
    AIProcessType,
    Competencies,
    Indice,
    IndiceStatus,
    Order,
    Subject,
    Title,
    TitleSubject,
)
from tenacity import retry, stop_after_attempt

from .subject_competencies_schemas import (
    ExtractDescriptorsInput,
    GenerateSubjectCompetenciesRequest,
    GenerateSubjectCompetenciesResponse,
    PromptContext,
    TitleCompetenciesInput,
    TitleSubjectResponse,
)


class GenerateSubjectCompetenciesWorkflow:
    """
    This workflow takes a description document of a title with its subjects and converts it to
    subjects with its name and description and competencies associated to each one of them.
    The title needs to be created before executing this workflow.
    """

    def __init__(
        self,
        db_engine: Engine,
        logger: Logger,
        llm: BaseChatModel,
        prompt_manager: PromptManager,
        ai_tracer: AITracer,
        index_repository: IndexRepository,
    ) -> None:
        self._db_engine = db_engine
        self._logger = logger
        self._llm = llm
        self._prompt_manager = prompt_manager
        self._index_repository = index_repository
        self._ai_tracer = ai_tracer

    async def execute(
        self, request: GenerateSubjectCompetenciesRequest
    ) -> GenerateSubjectCompetenciesResponse:
        if request.model_info:
            custom_llm = check_for_custom_llm_lc(
                request.model_info.provider,
                request.model_info.name,
                reasoning_effort=request.model_info.reasoning_effort,
                max_tokens=request.model_info.max_tokens,
            )
            self._llm = custom_llm if custom_llm else self._llm

        title = self._get_title(request)
        input_data_desc = self._format_descriptors_prompt_input(
            request.documento, title, request.year_number
        )

        try:
            (
                subjects_descriptions,
                process_desc,
            ) = await self.generate_subject_descriptions(input_data_desc)
            title_subjects = self._store_and_get_title_subjects(
                subjects_descriptions, title, request.year_number, request.term_number
            )
            self._create_mock_orders(title_subjects)
            input_data_comp = self._format_competencies_prompt_input(
                title, title_subjects
            )

            (
                subject_competencies,
                process_comp,
            ) = await self._generate_competencies_with_retries(
                input_data_comp, title_subjects
            )
            stored_subject_competencies = await self._store_competencies_for_subjects(
                subject_competencies
            )
            response = self._get_and_format_response(
                stored_subject_competencies, [process_desc, process_comp]
            )
        except Exception as e:
            self._logger.exception(
                f"An exception ocurred during generating subject descriptions: {e}"
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Unexpected error generating subject descriptions, try again.",
            )
        return response

    def _get_and_format_response(
        self,
        subject_competencies: Dict[int, Tuple[str, List[int]]],
        processes: List[AIProcess],
    ) -> GenerateSubjectCompetenciesResponse:
        # Add ai processes info
        response = GenerateSubjectCompetenciesResponse(
            processes_info=[
                AIProcessInfo(
                    ai_process_id=process.id, ai_process_type=process.process_type
                )
                for process in processes
            ]
        )

        with Session(self._db_engine) as session:
            for title_subject_id, (
                title_subject_name,
                competencies_ids,
            ) in subject_competencies.items():
                competencies = session.exec(
                    select(Competencies).where(Competencies.id.in_(competencies_ids))
                ).all()
                title_subj_response = TitleSubjectResponse(
                    id=title_subject_id,
                    name=title_subject_name,
                    competencies=[c.model_dump() for c in competencies],
                )
                response.title_subjects.append(title_subj_response)
        return response

    async def _store_competencies_for_subjects(
        self, input_subject_competencies: Dict[int, Tuple[Competencias, int, str]]
    ) -> Dict[int, Tuple[str, List[int]]]:
        """
        input_subject_competencies format:
        {title_subject_id: (Competencias_obj, title_subject_id, title_subject_name)}

        Returns:
        {title_subject_id: (title_subject_name, [competencies_ids])}
        """
        subject_competencies = {}
        for title_subject_id, (
            competencies_obj,
            ts_id,
            ts_name,
        ) in input_subject_competencies.items():
            with Session(self._db_engine) as session:
                statement = (
                    select(Order)
                    .where(Order.title_subject_id == title_subject_id)
                    .order_by(desc(Order.order_date))
                )
                latest_order = session.exec(statement).first()
                if not latest_order:
                    raise ValueError("Order not found")

                index = await self._index_repository.create_index(latest_order.id)
                refreshed_index = session.get(Indice, index.id)
                if not refreshed_index:
                    raise RuntimeError("Index was not properly stored")
                refreshed_index.status = IndiceStatus.NON_CONFIRMED_COMPETENCIES
                session.commit()

                competencies_ids = await self._index_repository.store_competencies(
                    index.id, competencies_obj
                )
                subject_competencies[title_subject_id] = (ts_name, competencies_ids)
        self._logger.info("Competencies for subjects succesfully stored")
        return subject_competencies

    async def generate_subject_descriptions(
        self, input_data: ExtractDescriptorsInput
    ) -> Tuple[List[SubjectDescription], AIProcess]:
        prompt = self._prompt_manager.get_prompt(
            name="extract-descriptions-title", prompt_type="chat"
        )
        self._logger.info(f"Prompt few shot examples is: {prompt.few_shot_examples}")
        input_data.EJEMPLO = (
            f"Input: {prompt.few_shot_examples[0].input}\nOutput:{prompt.few_shot_examples[0].output}"
            if prompt.few_shot_examples
            else ""
        )
        template, _ = check_template_chat(prompt)
        chain = template | self._llm
        metadata = {
            "flow": "Competencies from Title",
            "chain": "Generate Subject Descriptions",
            "prompt_context": input_data.prompt_context.model_dump()
            if input_data.prompt_context
            else None,
            "prompt_id": prompt.id,
            "prompt_version": prompt.version,
            "prompt_name": prompt.name,
        }

        process = self._ai_tracer.start_process(
            AIProcessType.EXTRACT_SUBJECT_DESCRIPTIONS
        )

        result = await chain.with_config(metadata=metadata).ainvoke(
            input_data.model_dump(exclude={"prompt_context"})
        )

        with Session(self._db_engine) as session:
            execution = self._ai_tracer.log_execution(
                process,
                input_data.model_dump(exclude={"prompt_context"}),
                result.content,
                metadata,
                status=AIProcessStatus.PENDING,
                prompt_id=prompt.id,
            )
            try:
                _, result = extract_and_remove_content(result, "reasoning")
                self._logger.info(f"Result before parsing yaml is: {result.content}")
                respuesta = parse_yaml(extract_content(result.content, "respuesta")[0])
                execution.output_data = respuesta
                session.add(execution)
                session.commit()
                session.refresh(execution)
            except Exception as e:
                execution.error_message = str(e)
                execution.status = AIProcessStatus.FAILED
                session.add(execution)
                session.commit()
                self._logger.exception(f"Error parsing: {e}")
                process = self._ai_tracer.complete_process(
                    process, AIProcessStatus.FAILED
                )
                raise

        try:
            if respuesta:
                self._logger.info(
                    f"Generate subject description response before validating is: {respuesta}"
                )
                if input_data.NOMBRE_TITULO != list(respuesta.keys())[0]:
                    raise ValueError(
                        f"Unexpected course name: {list(respuesta.keys())[0]} is different from the expected: {input_data.NOMBRE_TITULO}"
                    )
                title_description, *subjects = respuesta[input_data.NOMBRE_TITULO]
                if not isinstance(title_description, str):
                    raise TypeError("Title description must be a string")
                if not subjects or not isinstance(subjects[0], dict):
                    raise TypeError("Subjects must be a dictionary")
        except (ValueError, TypeError, KeyError) as e:
            with Session(self._db_engine) as session:
                execution = session.get(AIExecution, execution.id)
                execution.error_message = str(e)
                execution.status = AIProcessStatus.FAILED
                session.add(execution)
                session.commit()
                self._logger.exception(f"Validation failed: {str(e)}")
                process = self._ai_tracer.complete_process(
                    process, AIProcessStatus.FAILED
                )
            raise

        try:
            subject_descriptions = [
                SubjectDescription(subject_name=n, description=d[0])
                for subject in subjects
                for n, d in subject.items()
            ]
        except ValidationError as exc:
            with Session(self._db_engine) as session:
                execution = session.get(AIExecution, execution.id)
                execution.error_message = str(exc)
                execution.status = AIProcessStatus.FAILED
                session.add(execution)
                session.commit()
                self._logger.exception(
                    f"Exception ocurred validating pydantic model: {exc}"
                )
                process = self._ai_tracer.complete_process(
                    process, AIProcessStatus.FAILED
                )
            raise

        self._ai_tracer.complete_process(process, AIProcessStatus.COMPLETED)
        return subject_descriptions, process

    async def generate_competencies_from_title(
        self, input_data: TitleCompetenciesInput
    ) -> Tuple[Dict[str, Competencias], AIProcess]:
        competencies_dict = {}
        prompt = self._prompt_manager.get_prompt(
            name="competencies-from-title", prompt_type="chat"
        )
        template, _ = check_template_chat(prompt)
        chain = template | self._llm
        metadata = {
            "flow": "Competencies from Title",
            "chain": "Generate competencies from Title",
            "prompt_context": input_data.prompt_context.model_dump()
            if input_data.prompt_context
            else None,
            "prompt_id": prompt.id,
            "prompt_version": prompt.version,
            "prompt_name": prompt.name,
        }
        result = await chain.with_config(metadata=metadata).ainvoke(
            input_data.model_dump(exclude={"prompt_context"})
        )
        content, result = extract_and_remove_content(result, "reasoning")
        content, result = extract_and_remove_content(result, "thinking")
        competencies = extract_content(result.content, "competencies")
        competencies_dict = parse_yaml(competencies[0])
        process = self._ai_tracer.trace_process(
            AIProcessType.COMPETENCIES_FROM_TITLE_DESCRIPTOR,
            input_data.model_dump(exclude={"prompt_context"}),
            competencies_dict,
            metadata,
            prompt_id=prompt.id,
        )
        for key, value in competencies_dict.items():
            competencies_dict[key] = Competencias(
                competencias=[Competencia(descripcion=d) for d in value]
            )
        return competencies_dict, process

    @retry(stop=stop_after_attempt(3))
    async def _generate_competencies_with_retries(
        self,
        input_data: TitleCompetenciesInput,
        title_subjects: List[Tuple[int, str, str, str]],
    ) -> Tuple[Dict[int, Tuple[Competencias, int, str]], AIProcess]:
        """
        title_subjects: List of tuples (title_subject_id, title_subject_name, subject_name, subject_description)
        We need a dict keyed by title_subject_name to match competencies_dict from LLM.
        """
        competencies_dict, process = await self.generate_competencies_from_title(
            input_data
        )

        subject_dict = {
            ts_name: (ts_id, ts_name)
            for (ts_id, ts_name, subj_name, subj_desc) in title_subjects
        }

        self._logger.info(
            f"Subject dict is: {subject_dict.keys()}\n\nCompetencies dict are: {competencies_dict.keys()}"
        )
        assert list(subject_dict.keys()) == list(competencies_dict.keys())

        subject_competencies_dict = {
            ts_id: (competencies_dict[name], ts_id, ts_name)
            for name, (ts_id, ts_name) in subject_dict.items()
        }

        return subject_competencies_dict, process

    def _get_title(self, request: GenerateSubjectCompetenciesRequest) -> Title:
        with Session(self._db_engine) as session:
            title = session.exec(
                select(Title).where(Title.id == request.title_id)
            ).first()
            if not title:
                self._logger.exception(f"Title with id {request.title_id} not found")
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
            return title

    def _create_mock_orders(
        self,
        title_subjects: List[Tuple[int, str, str, str]],
        author_id: str = "<EMAIL>",
        coordinator_id: str = "<EMAIL>",
    ):
        """
        title_subjects: List of (title_subject_id, title_subject_name, subject_name, subject_description)
        """
        with Session(self._db_engine) as session:
            for ts_id, ts_name, subj_name, subj_desc in title_subjects:
                order = Order(
                    author_id=author_id,
                    coordinator_id=coordinator_id,
                    title_subject_id=ts_id,
                )
                session.add(order)
            session.commit()

    def _store_and_get_title_subjects(
        self,
        subjects: List[SubjectDescription],
        title: Title,
        year_number: int,
        term_number: int | None = None,
    ) -> List[Tuple[int, str, str, str]]:
        """
        Returns a list of tuples:
        (title_subject_id, title_subject_name, subject_name, subject_description)
        """
        title_subjects_data = []
        with Session(self._db_engine) as session:
            for subject in subjects:
                # Check if subject exists
                db_subject = session.exec(
                    select(Subject).where(
                        Subject.name == subject.subject_name,
                        Subject.description == subject.description,
                    )
                ).first()

                if not db_subject:
                    db_subject = Subject(
                        name=subject.subject_name, description=subject.description
                    )
                    session.add(db_subject)
                    session.flush()

                statement = (
                    select(TitleSubject)
                    .options(joinedload(TitleSubject.subject))
                    .where(
                        TitleSubject.year_number == year_number,
                        TitleSubject.term_number == term_number,
                        TitleSubject.name == f"{db_subject.name} - {title.name}",
                    )
                )
                existing_title_subject = session.exec(statement).first()

                if existing_title_subject:
                    self._logger.exception("Title subject was already present")
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Title subject was already created",
                    )

                new_title_subject = TitleSubject(
                    name=f"{db_subject.name} - {title.name}",
                    year_number=year_number,
                    subject_id=db_subject.id,
                    term_number=term_number,
                    title_id=title.id,
                )

                session.add(new_title_subject)
                session.commit()
                session.refresh(new_title_subject)

                title_subjects_data.append(
                    (
                        new_title_subject.id,
                        new_title_subject.name,
                        db_subject.name,
                        db_subject.description
                        if db_subject.description
                        else "Description not Provided",
                    )
                )

        return title_subjects_data

    def _format_competencies_prompt_input(
        self, title: Title, title_subjects: List[Tuple[int, str, str, str]]
    ) -> TitleCompetenciesInput:
        """
        title_subjects: (title_subject_id, title_subject_name, subject_name, subject_description)
        We only need title_subject_name and subject_description to build prompt.
        """
        title_descriptor = f"## {title.name}\n{title.description}\n\n"
        for ts_id, ts_name, subj_name, subj_desc in title_subjects:
            title_descriptor += f"### **{ts_name}**\nDescription: {subj_desc}\n"

        input_data = TitleCompetenciesInput(
            descriptor=title_descriptor,
            prompt_context=PromptContext(id_titulo=title.id),
        )
        return input_data

    def _format_descriptors_prompt_input(
        self, documento: str, title: Title, year_number: int
    ) -> ExtractDescriptorsInput:
        input_data = ExtractDescriptorsInput(
            tipo_titulo=str(title.type.value),
            nombre_titulo=title.name,
            documento=documento,
            numero_año=year_number,
            prompt_context=PromptContext(id_titulo=title.id),
        )
        return input_data
