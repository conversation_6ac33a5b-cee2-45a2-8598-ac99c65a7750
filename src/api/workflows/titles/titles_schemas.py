from typing import Optional

from pydantic import BaseModel

from src.domain.models import TitleType


class CreateTitleRequest(BaseModel):
    name: str
    type: TitleType | None = None
    description: str | None = None


class CreateTitleResponse(CreateTitleRequest):
    id: int
    plan_id: int | None = None


class UpdateTitleRequest(BaseModel):
    name: Optional[str] = None
    type: Optional[str] = None
    description: Optional[str] = None
    owner: Optional[int] = None
