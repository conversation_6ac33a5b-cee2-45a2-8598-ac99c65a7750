from datetime import datetime

from fastapi import APIRouter, HTTPException, status
from sqlalchemy.exc import SQLAlchemyError
from sqlmodel import Session, select

from src.api.common.dependency_container import DependencyContainer
from src.api.workflows.titles import (
    CreateTitleRequest,
    CreateTitleResponse,
    GenerateSubjectCompetenciesRequest,
    GenerateSubjectCompetenciesResponse,
    UpdateTitleRequest,
)
from src.domain.models import Plan, Title

router = APIRouter(prefix="/api/v1/titles", tags=["Titles"])


@router.post("", response_model=CreateTitleResponse)
def create_title(request: CreateTitleRequest):
    db_engine = DependencyContainer.get_database_engine()
    logger = DependencyContainer.get_logger()
    try:
        with Session(db_engine) as session:
            statement = select(Title).where(
                Title.name == request.name,
                Title.description == request.description,
                Title.type == request.type,
            )
            title = session.exec(statement).first()
            logger.info(f"Title is: {title}")
            if title:
                raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST)

            plan = Plan(year=datetime.now().year)
            session.add(plan)
            session.flush()

            title = Title(
                type=request.type,
                name=request.name,
                description=request.description,
                plan_id=plan.id,
            )
            session.add(title)
            session.commit()
            session.refresh(title)

    except SQLAlchemyError:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An internal server error ocurred",
        )
    return title


@router.get("", response_model=list[CreateTitleResponse])
def get_titles():
    db_engine = DependencyContainer.get_database_engine()
    with Session(db_engine) as session:
        statement = select(Title)
        titles = session.exec(statement).all()
        if not titles:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
        return [
            CreateTitleResponse.model_validate(title.model_dump()) for title in titles
        ]


@router.get("/{id}", response_model=CreateTitleResponse)
def get_title(id: int):
    db_engine = DependencyContainer.get_database_engine()
    with Session(db_engine) as session:
        title = session.get(Title, id)
        if not title:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
        return title


@router.patch(
    "/{id}", response_model=CreateTitleResponse, status_code=status.HTTP_200_OK
)
def update_title(id: int, request: UpdateTitleRequest):
    db_engine = DependencyContainer.get_database_engine()
    try:
        with Session(db_engine) as session:
            db_title = session.get(Title, id)
            if not db_title:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND, detail="Title not found"
                )

            title_data = request.model_dump(exclude_unset=True)
            db_title.sqlmodel_update(title_data)

            session.add(db_title)
            session.commit()
            session.refresh(db_title)
    except SQLAlchemyError:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An internal server error ocurred",
        )
    return db_title


@router.delete("/{id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_title(id: int):
    db_engine = DependencyContainer.get_database_engine()
    with Session(db_engine) as session:
        title = session.get(Title, id)
        if not title:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Title not found"
            )
        session.delete(title)
        session.commit()
    return status.HTTP_204_NO_CONTENT


@router.post(
    "/generate_competencies",
    status_code=status.HTTP_200_OK,
    response_model=GenerateSubjectCompetenciesResponse,
)
async def generate_competencies_from_title(
    request: GenerateSubjectCompetenciesRequest,
) -> GenerateSubjectCompetenciesResponse:
    return (
        await DependencyContainer()
        .get_generate_subject_competencies_workflow()
        .execute(request)
    )
