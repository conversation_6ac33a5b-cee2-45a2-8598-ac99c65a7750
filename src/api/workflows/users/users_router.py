from fastapi import APIRouter, Depends, HTTPException, status
from sqlmodel import Session

from src.api.common.dependencies.get_session import get_session
from src.api.workflows.users import RequestUser, UserResponse
from src.domain.models import User

router = APIRouter(prefix="/api/v1/users", tags=["Users"])


@router.post("", response_model=UserResponse)
def create_user(request: RequestUser, session: Session = Depends(get_session)):
    user = User(name=request.name)
    session.add(user)
    session.commit()
    session.refresh(user)
    return user


@router.get("/{id}", response_model=UserResponse)
def get_user(id: int, session: Session = Depends(get_session)):
    user = session.get(User, id)
    if not user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
    return user


@router.delete("/{id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_user(id: int, session: Session = Depends(get_session)):
    user = session.get(User, id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
        )
    session.delete(user)
    session.commit()
    return None
