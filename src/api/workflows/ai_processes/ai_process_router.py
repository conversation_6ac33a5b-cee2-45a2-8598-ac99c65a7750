from fastapi import APIRouter, Depends, HTTPException, status
from sqlmodel import Session, select

from src.api.common.dependencies.get_session import get_session
from src.api.common.dependency_container import DependencyContainer
from src.domain.models import AIProcess, AIProcessRating

from .ai_process_schemas import (
    GetRatingResponse,
    RateProcessRequest,
    RateProcessResponse,
)

router = APIRouter(prefix="/api/v1/ai_processes", tags=["AI Proccesses"])


@router.post("/ratings/{ai_process_id}")
def rate_ai_process(
    ai_process_id: int,
    request: RateProcessRequest,
    session: Session = Depends(get_session),
):
    tracer = DependencyContainer().get_ai_tracer()
    try:
        rating = tracer.rate_process(
            ai_process_id,
            request.rating,
            request.evaluator_name,
            comment=request.comment,
        )
        return RateProcessResponse(process_id=ai_process_id, **rating.model_dump())
    except ValueError:
        raise HTTPException(status_code=400, detail="Bad request")
    except Exception:
        raise HTTPException(status_code=404, detail="Not allowed")


@router.get("/ratings/{ai_process_id}", response_model=GetRatingResponse)
def get_rating_by_id(ai_process_id: int, session: Session = Depends(get_session)):
    statament = (
        select(AIProcessRating)
        .join(AIProcess, AIProcessRating.ai_process_id == AIProcess.id)
        .where(AIProcess.id == ai_process_id)
    )  # type: ignore
    rating = session.exec(statament).first()
    if not rating:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
    return rating
