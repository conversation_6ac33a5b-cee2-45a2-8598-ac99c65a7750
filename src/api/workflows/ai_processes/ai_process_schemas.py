from typing import Optional

from pydantic import BaseModel, Field


class RateProcessRequest(BaseModel):
    rating: int = Field(..., ge=1, le=5, description="Rating for the process (1-5)")
    comment: Optional[str] = Field(None, description="Optional comment for the rating")
    evaluator_name: str = Field(None, description="Name of the evaluator")


class RateProcessResponse(BaseModel):
    process_id: int
    rating: int
    comment: Optional[str]
    evaluator_name: str


class GetRatingResponse(BaseModel):
    rating: int
    comment: Optional[str]
    evaluator_name: str
