import logging
from collections import named<PERSON>ple
from typing import Any, Dict

from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, status
from ia_gen_core.prompts import PromptManager
from langchain.chat_models.base import BaseChatModel
from langchain.prompts import ChatPromptTemplate
from sqlalchemy.ext.asyncio import AsyncEngine
from sqlalchemy.orm import selectinload
from sqlmodel import select
from sqlmodel.ext.asyncio.session import AsyncSession

from src.api.common.services.ai_tracer import AIProcessStatus, AIProcessType, AITracer
from src.api.common.services.content_generator import ContentGenerator
from src.api.common.services.content_generator.content_generator import (
    Query,
    SearchQueries,
)
from src.api.common.services.content_generator.utils.parsing import extract_content
from src.api.common.services.index_repository import IndexRepository
from src.api.common.services.markdown_service import MarkdownService
from src.api.common.services.structs import (
    ContentMetadata,
    DetailedGeneratedContent,
    GeneratedContent,
)
from src.api.common.tools.utils import augment_user_query, convert_content_for_llm
from src.domain.models import (
    AIProcess,
    Bloque,
    ContentPlanItem,
    Epigrafe,
    Indice,
    Order,
    Tema,
)
from src.domain.models import ContentPlan as ContentPlanModel

from .regenerate_paragraph_schemas import (
    RegenerateParagraphRequest,
    RegenerateParagraphResponse,
)

RegenerateParagraphData = namedtuple(
    "RegenerateParagraphData",
    [
        "consulta",
        "bloque",
        "tema",
        "epigrafe",
        "asignatura",
        "contexto",
        "contenido_anterior",
        "párrafo_actual",
        "contenido_posterior",
        "fuentes_relacionadas",
    ],
)


class RegenerateParagraphWorkflow:
    def __init__(
        self,
        db_engine: AsyncEngine,
        logger: logging.Logger,
        llm: BaseChatModel,
        ai_tracer: AITracer,
        prompt_manager: PromptManager,
        content_generator: ContentGenerator,
        index_repository: IndexRepository,
        markdown_service: MarkdownService,
    ):
        self._db_engine = db_engine
        self._logger = logger
        self._llm = llm
        self._ai_tracer = ai_tracer
        self._prompt_manager = prompt_manager
        self._content_generator = content_generator
        self._index_repository = index_repository
        self._markdown_service = markdown_service

    async def execute(
        self, request: RegenerateParagraphRequest
    ) -> RegenerateParagraphResponse:
        try:
            epigraph, _ = await self._perform_request_validations(request)
            ai_process = self._ai_tracer.start_process(
                process_type=AIProcessType.REGENERATE_PARAGRAPH,
                tema_id=epigraph.tema.id,
                indice_id=epigraph.tema.bloque.indice_id,
                epigrafe_id=epigraph.id,
            )
            augmented_query = await augment_user_query(
                request.user_comment, epigraph, self._prompt_manager, self._llm
            )
            search_queries = SearchQueries(
                queries=[Query(query=q) for q in augmented_query]
            )
            (
                formatted_sources,
                doc_id_to_name,
                chunks_by_doc,
            ) = await self._content_generator.search_documents_multi_query(
                queries=search_queries,
                epigrafe_ids=[epigraph.id],
                ai_process=ai_process,
            )
            docs_info = (doc_id_to_name, chunks_by_doc) if formatted_sources else None
            input_data = await self._format_prompt_input(
                request, epigraph, formatted_sources
            )
            paragraph, ai_process = await self._regenerate_paragraph(
                input_data, ai_process, docs_info
            )
            return RegenerateParagraphResponse(
                content=paragraph.content,
                metadata=paragraph.metadata.model_dump()
                if paragraph.metadata
                else None,
                related_chunks=paragraph.related_chunks
                if paragraph.related_chunks
                else [],
                related_docs=paragraph.related_docs if paragraph.related_docs else [],
            )
        except Exception as e:
            self._logger.exception(f"Error in add paragraph workflow: {str(e)}")
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)

    async def _format_prompt_input(
        self,
        request: RegenerateParagraphRequest,
        epigraph: Epigrafe,
        fuentes_relacionadas: str = "",
    ) -> Dict[str, Any]:
        content_list = await self._fetch_content_data(request, epigraph)

        prev_content = [cont for cont in content_list][
            max((request.position - 1) - request.window_size, 0) : request.position - 1
        ]
        next_content = [cont for cont in content_list][
            request.position : min(
                (request.position) + request.window_size, len(content_list) + 1
            )
        ]
        prev_content_text = "\n".join([p.content for p in prev_content])
        next_content_text = "\n".join([p.content for p in next_content])
        current_content = content_list[request.position - 1].content

        epigrafe_with_context_result = (
            await self._index_repository.get_epigrafes_with_context(
                tema_id=epigraph.tema.id,
                epigrafe_id=epigraph.id,
                indice_id=epigraph.tema.bloque.indice_id,
                plan_version=1,
            )
        )
        epigrafe_with_context = epigrafe_with_context_result[0]

        n_bloque, n_tema, n_epigrafe, n_asignatura, ctx = (
            epigrafe_with_context.nombre_bloque,
            epigrafe_with_context.nombre_tema,
            epigrafe_with_context.nombre,
            epigrafe_with_context.nombre_asignatura,
            epigrafe_with_context.context,
        )
        input_data = RegenerateParagraphData(
            request.user_comment,
            n_bloque,
            n_tema,
            n_epigrafe,
            n_asignatura,
            ctx,
            prev_content_text,
            current_content,
            next_content_text,
            fuentes_relacionadas,
        )

        return input_data._asdict()

    async def _fetch_content_data(
        self, request: RegenerateParagraphRequest, epigraph: Epigrafe
    ) -> list[DetailedGeneratedContent]:
        async with AsyncSession(self._db_engine) as session:
            query = self._markdown_service.get_content_query(
                epigraph.tema.id, 1, epigraph.id
            )
            results = await session.execute(query)

            raw_results = results.all()
            content_list = []

            for result in raw_results:
                content = DetailedGeneratedContent(
                    content=result[0],
                    related_docs=result[1] if result[1] else [],
                    metadata=ContentMetadata.model_validate(result[2])
                    if result[2]
                    else None,
                    epigrafe_id=result[3],
                    epigrafe_nombre=result[4],
                    epigrafe_position=result[5],
                    plan_item_position=result[6],
                    tema_id=result[7],
                    tema_position=result[8],
                    tema_nombre=result[9],
                    bloque_id=result[10],
                    bloque_nombre=result[11],
                    bloque_position=result[12],
                )
                content_list.append(content)

            if not content_list:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="No content was found for this content plans",
                )
            return convert_content_for_llm(content_list)

    async def _perform_request_validations(
        self, request: RegenerateParagraphRequest
    ) -> tuple[Epigrafe, int]:
        async with AsyncSession(self._db_engine) as session:
            stmt_1 = (
                select(Epigrafe)
                .options(
                    selectinload(Epigrafe.tema)
                    .selectinload(Tema.bloque)
                    .selectinload(Bloque.indice)
                    .selectinload(Indice.order)
                    .selectinload(Order.title_subject)
                )
                .where(Epigrafe.id == request.section_id)
            )
            result_1 = await session.exec(stmt_1)
            epigraph = result_1.first()
            if not epigraph:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
            if not epigraph.tema.bloque.indice:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
            if request.position < 1:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="First position starts at 1, provide a valid position",
                )
            stmt_2 = (
                select(ContentPlanItem)
                .join(
                    ContentPlanModel,
                    ContentPlanItem.content_plan_id == ContentPlanModel.id,
                )
                .where(ContentPlanModel.epigrafe_id == request.section_id)
                .where(ContentPlanItem.position == request.position)
            )
            result_2 = await session.exec(stmt_2)
            content_plan_item = result_2.first()

            if not content_plan_item:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND, detail="Paragraph not found"
                )
            return epigraph, content_plan_item.id

    async def _regenerate_paragraph(
        self,
        input_data: RegenerateParagraphData,
        ai_process: AIProcess,
        docs_info: tuple[Any, Any] | None = None,
    ) -> tuple[GeneratedContent, AIProcess]:
        generated_content = GeneratedContent()
        try:
            prompt = self._prompt_manager.get_prompt(
                name="regenerate-paragraph-prompt", prompt_type="chat"
            )
            prompt_messages = prompt.compile().to_langchain()
            template = ChatPromptTemplate.from_messages(prompt_messages)
            chain = template | self._llm
            metadata = {
                "flow": "Regenerate Paragraph",
                "chain": "Generate Paragraph",
                "prompt_id": prompt.id,
                "prompt_version": prompt.version,
            }
            result = await chain.with_config(metadata=metadata).ainvoke(input_data)
            if docs_info:
                doc_id_to_name, chunks_by_doc = docs_info
                content = extract_content(result.content, "content")[0]
                (
                    processed_content,
                    original_diagrams,
                    diagram_urls,
                ) = await self._content_generator.process_mermaid_diagrams(content)
                generated_content = self._content_generator._extract_sources(
                    content,
                    doc_id_to_name,
                    chunks_by_doc,
                )
                import re

                generated_content.content = re.sub(
                    r"<fuentes>.*?</fuentes>", "", processed_content, flags=re.DOTALL
                ).strip()
                generated_content.metadata = ContentMetadata(
                    diagrams=original_diagrams,
                    diagrams_urls=diagram_urls,
                    original_ai_content=content,
                )
            input_data.pop("contexto")

            self._ai_tracer.log_execution(
                ai_process,
                input_data,
                generated_content.content,
                metadata,
                prompt_id=prompt.id,
            )
            self._ai_tracer.complete_process(ai_process)

            return generated_content, ai_process
        except Exception as e:
            if "contexto" in input_data:
                input_data.pop("contexto")
            content_text = (
                generated_content.content if generated_content.content else ""
            )
            input_data.pop("contexto")
            self._ai_tracer.log_execution(
                ai_process,
                input_data,
                content_text,
                metadata,
                prompt_id=prompt.id,
                error_message=e,
                status=AIProcessStatus.FAILED,
            )
            self._ai_tracer.complete_process(ai_process, AIProcessStatus.FAILED)
            self._logger.exception(f"Error generating content: {str(e)}")
            raise
