from pydantic import BaseModel


class RegenerateParagraphRequest(BaseModel):
    section_id: int | None = None
    position: int
    "Position of the paragraph that user wants to modify"
    user_comment: str
    window_size: int = 3
    "How many previous and next items to take contigous to the current paragraph"


class RegenerateParagraphResponse(BaseModel):
    content: str
    metadata: dict | None = None
    related_chunks: list[str] = []
    related_docs: list[str] = []
