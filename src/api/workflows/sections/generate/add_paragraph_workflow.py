import logging
import re
from collections import named<PERSON>ple
from typing import Any, Dict

from fastapi import HTT<PERSON>Ex<PERSON>, status
from ia_gen_core.prompts import PromptManager
from langchain.prompts import ChatPromptTemplate
from sqlalchemy import Engine
from sqlalchemy.orm import selectinload
from sqlmodel import Session, desc, select
from src.api.common.services.ai_tracer import AITracer
from src.api.common.services.content_generator import ContentGenerator
from src.api.common.services.content_generator.content_generator import (
    Query,
    SearchQueries,
)
from src.api.common.services.content_generator.utils.parsing import extract_content
from src.api.common.services.index_repository import IndexRepository
from src.api.common.services.llm import LLM
from src.api.common.services.markdown_service import MarkdownService
from src.api.common.services.structs import (
    ContentMetadata,
    DetailedGeneratedContent,
    EpigrafeWithContext,
    GeneratedContent,
)
from src.api.common.tools.utils import augment_user_query, convert_content_for_llm
from src.domain.models import (
    AIProcess,
    AIProcessStatus,
    AIProcessType,
    Bloque,
    ContenidoGenerado,
    ContentPlanItem,
    ContentPlanType,
    Indice,
    Order,
    Tema,
)
from src.domain.models import (
    ContentPlan as ContentPlanModel,
)
from src.domain.models import (
    Epigrafe as EpigrafeModel,
)

from .add_paragraph_schemas import (
    AddParagraphRequest,
    AddParagraphResponse,
    EpigraphData,
)


class AddParagraphWorkflow:
    def __init__(
        self,
        db_engine: Engine,
        logger: logging.Logger,
        llm: LLM,
        ai_tracer: AITracer,
        prompt_manager: PromptManager,
        content_generator: ContentGenerator,
        index_repository: IndexRepository,
        markdown_service: MarkdownService,
    ):
        self._db_engine = db_engine
        self._logger = logger
        self._ai_tracer = ai_tracer
        self._prompt_manager = prompt_manager
        self._content_generator = content_generator
        self._llm = llm
        self._index_repository = index_repository
        self._markdown_service = markdown_service

    async def execute(self, request: AddParagraphRequest) -> AddParagraphResponse:
        """
        Main workflow execution for adding a paragraph
        1. Retrieve context (surrounding content) for the given position
        2. Search for relevant sources
        3. Generate new content using LLM with sources and tool type
        4. Validate diagrams if present
        5. Store the new content
        6. Return the generated content
        """
        epigraph_data, ai_process = self._fetch_epigraph_data(request)

        (
            prev_content,
            next_content,
            new_position,
            epigrafe_with_context,
        ) = await self._get_content_context(
            request.prev_position,
            epigraph_data.index_id,
            epigraph_data.tema_id,
            request.window_size,
            request.section_id,
        )

        try:
            paragraph = await self._process_paragraph_external(
                request,
                epigraph_data,
                prev_content,
                next_content,
                epigrafe_with_context,
                ai_process,
            )

            plan_item_id = (
                self._store_paragraph_result(
                    paragraph,
                    request.tool,
                    new_position,
                    request.section_id,
                    request.user_comment,
                    ai_process,
                )
                if request.store
                else None
            )

            return AddParagraphResponse(
                content=paragraph.content,
                plan_item_id=plan_item_id,
                metadata=paragraph.metadata.model_dump()
                if paragraph.metadata
                else None,
                related_chunks=paragraph.related_chunks
                if paragraph.related_chunks
                else [],
                related_docs=paragraph.related_docs if paragraph.related_docs else [],
            )

        except Exception as e:
            self._logger.exception(f"Error in add paragraph workflow: {str(e)}")
            raise e

    def _fetch_epigraph_data(
        self, request: AddParagraphRequest
    ) -> tuple[EpigraphData, AIProcess]:
        with Session(self._db_engine) as session:
            self._logger.info(f"Tool is : {request.tool}")
            stmt = (
                select(EpigrafeModel)
                .options(
                    selectinload(EpigrafeModel.tema)
                    .selectinload(Tema.bloque)
                    .selectinload(Bloque.indice)
                    .selectinload(Indice.order)
                    .selectinload(Order.title_subject)
                )
                .where(EpigrafeModel.id == request.section_id)
            )
            epigraph = session.exec(stmt).first()
            if not epigraph:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Epigraph with ID {request.section_id} not found",
                )

            (
                index_id,
                epigraph_id,
            ) = epigraph.tema.bloque.indice.id, epigraph.id
            tema_id, tema_name = epigraph.tema.id, epigraph.tema.name
            bloque_name, epigraph_name = epigraph.tema.bloque.name, epigraph.name
            subject_name = epigraph.tema.bloque.indice.order.title_subject.name

            if not index_id:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Index id of epigraph with ID: {epigraph_id} not found",
                )
            self._check_item_exists(session, request.section_id, request.prev_position)
            ai_process = self._ai_tracer.start_process(
                process_type=AIProcessType.ADD_PARAGRAPH,
                tema_id=tema_id,
                indice_id=index_id,
                epigrafe_id=epigraph_id,
            )
            epigraph_data = EpigraphData(
                id=epigraph_id,
                tema_id=tema_id,
                index_id=index_id,
                epigraph_obj=epigraph,
                epigraph_name=epigraph_name,
                tema_name=tema_name,
                bloque_name=bloque_name,
                subject_name=subject_name,
            )
            session.expunge(epigraph)

            return epigraph_data, ai_process

    async def _process_paragraph_external(
        self,
        request: AddParagraphRequest,
        epigraph_data: EpigraphData,
        prev_content: list,
        next_content: list,
        epigrafe_with_context: Any,
        ai_process: AIProcess,
    ) -> GeneratedContent:
        prev_content_text = "\n".join([p.content for p in prev_content])
        next_content_text = "\n".join([p.content for p in next_content])

        queries = await augment_user_query(
            request.user_comment,
            epigraph_data.epigraph_obj,
            self._prompt_manager,
            self._llm,
        )

        search_queries = SearchQueries(queries=[Query(query=q) for q in queries])
        (
            formatted_sources,
            *docs_info,
        ) = await self._content_generator.search_documents_multi_query(
            queries=search_queries,
            epigrafe_ids=[epigraph_data.id],
            ai_process=ai_process,
        )
        input_data = self._format_prompt_input(
            epigrafe_with_context,
            request.user_comment,
            prev_content_text,
            next_content_text,
            formatted_sources,
            request.tool,
        )
        paragraph, ai_process = await self._generate_paragraph(
            input_data, ai_process, docs_info
        )
        return paragraph

    def _store_paragraph_result(
        self,
        paragraph: GeneratedContent,
        tool_type: ContentPlanType,
        new_content_position: int,
        epigraph_id: int,
        user_comment: str,
        ai_process: AIProcess,
    ) -> int:
        with Session(self._db_engine) as session:
            return self._store_paragraph(
                session,
                paragraph,
                tool_type,
                new_content_position,
                epigraph_id,
                user_comment,
                ai_process,
            )

    def _check_item_exists(self, session: Session, epigraph_id: int, position: int):
        if position == 0:
            return
        content_plan_item = session.exec(
            select(ContentPlanItem)
            .join(
                ContentPlanModel, ContentPlanItem.content_plan_id == ContentPlanModel.id
            )
            .where(ContentPlanModel.epigrafe_id == epigraph_id)
            .where(ContentPlanItem.position == position)
        ).first()
        if not content_plan_item:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Paragraph specified does not exist.",
            )

    async def _get_content_context(
        self,
        prev_pos: int,
        index_id: int,
        topic_id: int,
        window_size: int,
        epigraph_id: int,
        content_plan_version: int = 1,
    ) -> tuple[
        list[DetailedGeneratedContent], list[DetailedGeneratedContent], int, Any
    ]:
        with Session(self._db_engine) as session:
            _, content_list = self._markdown_service.get_content_markdown(
                session=session,
                tema_id=topic_id,
                content_plan_version=content_plan_version,
                citations_at_end=False,
                epigrafe_id=epigraph_id,
            )
            if content_list:
                content_list = convert_content_for_llm(content_list)
                prev_content = [cont for cont in content_list][
                    max(prev_pos - window_size, 0) : prev_pos
                ]
                post_content = [cont for cont in content_list][
                    prev_pos : min(prev_pos + window_size, len(content_list) + 1)
                ]
            else:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
            if prev_content:
                new_paragraph_position = prev_content[-1].plan_item_position + 1
            else:
                new_paragraph_position = 1
            epigrafe_with_context_result = (
                await self._index_repository.get_epigrafes_with_context(
                    tema_id=topic_id,
                    epigrafe_id=epigraph_id,
                    indice_id=index_id,
                    plan_version=content_plan_version,
                )
            )
            epigrafe_with_context = epigrafe_with_context_result[0]
        return prev_content, post_content, new_paragraph_position, epigrafe_with_context

    def _format_prompt_input(
        self,
        epigrafe: EpigrafeWithContext,
        user_comment: str,
        prev_content: str,
        post_content: str,
        sources: str = "",
        tool_type: ContentPlanType = ContentPlanType.TEXTO,
    ) -> Dict[str, Any]:
        """
        Build the input data for the prompt with sources and tool type
        """
        AddParagraphInput = namedtuple(
            "AddParagraphInput",
            [
                "bloque",
                "consulta",
                "contenido_anterior",
                "contenido_posterior",
                "contexto",
                "epigrafe",
                "tema",
                "fuentes_relacionadas",
                "tipo_herramienta",
            ],
        )

        input_data = AddParagraphInput(
            bloque=epigrafe.nombre_bloque,
            consulta=user_comment,
            contenido_anterior=prev_content,
            contenido_posterior=post_content,
            contexto=epigrafe.context,
            epigrafe=epigrafe.nombre,
            tema=epigrafe.nombre_tema,
            fuentes_relacionadas=sources,
            tipo_herramienta=tool_type.value,
        )

        return input_data._asdict()

    async def _generate_paragraph(
        self,
        input_data: Dict[str, Any],
        ai_process: AIProcess,
        docs_info: tuple[Any, Any] | None = None,
    ) -> tuple[GeneratedContent, AIProcess]:
        """
        Generate a new paragraph using the content generator with sources and tool type
        """
        generated_content = GeneratedContent()
        metadata = {}
        prompt_id = None
        try:
            filters = []
            prompt = self._prompt_manager.get_prompt(
                "add-paragraph", prompt_type="chat"
            )
            prompt_id = prompt.id
            tool_type = input_data.get("tipo_herramienta", ContentPlanType.TEXTO.value)

            if tool_type == ContentPlanType.TABLA.value:
                filters.append("tablas")
            elif tool_type == ContentPlanType.GRAFICA.value:
                filters.append("diagramas")

            if filters:
                self._logger.info(f"Filters present: {filters}")
                lc_messages = (
                    prompt.compile()
                    .filter(herramienta=filters)
                    .add_examples(n_examples=10)
                    .to_langchain()
                )
            else:
                lc_messages = prompt.compile().to_langchain()
            template = ChatPromptTemplate.from_messages(lc_messages)

            chain = template | self._llm

            metadata = {
                "flow": "Add Paragraph",
                "chain": "Generate Paragraph",
                "prompt_version": prompt.version,
                "prompt_id": prompt.id,
                "tool_type": tool_type,
            }

            result = await chain.with_config(metadata=metadata).ainvoke(input_data)
            if docs_info:
                doc_id_to_name, chunks_by_doc = docs_info
                content = extract_content(result.content, "content")[0]
                (
                    processed_content,
                    original_diagrams,
                    diagram_urls,
                ) = await self._content_generator.process_mermaid_diagrams(content)
                generated_content = self._content_generator._extract_sources(
                    processed_content, doc_id_to_name, chunks_by_doc
                )

                generated_content.content = re.sub(
                    r"<fuentes>.*?</fuentes>", "", processed_content, flags=re.DOTALL
                ).strip()
                generated_content.metadata = ContentMetadata(
                    diagrams=original_diagrams,
                    diagrams_urls=diagram_urls,
                    original_ai_content=content,
                )

            input_data.pop("contexto")

            self._ai_tracer.log_execution(
                ai_process,
                input_data,
                generated_content.content,
                metadata,
                prompt_id=prompt_id,
            )
            self._ai_tracer.complete_process(ai_process)

            return generated_content, ai_process
        except Exception as e:
            if "contexto" in input_data:
                input_data.pop("contexto")
            content_text = (
                generated_content.content if generated_content.content else ""
            )
            self._ai_tracer.log_execution(
                ai_process,
                input_data,
                content_text,
                metadata,
                prompt_id=prompt_id,
                error_message=str(e),
                status=AIProcessStatus.FAILED,
            )
            self._ai_tracer.complete_process(ai_process, AIProcessStatus.FAILED)
            self._logger.exception(f"Error generating content: {str(e)}")
            raise

    def _store_paragraph(  # Make this also async.
        self,
        session: Session,
        paragraph: GeneratedContent,
        tool_type: ContentPlanType,
        new_content_position: int,
        epigraph_id: int,
        user_comment: str,
        ai_process: AIProcess,
    ):
        content_plan = session.exec(
            select(ContentPlanModel)
            .where(ContentPlanModel.epigrafe_id == epigraph_id)
            .order_by(desc(ContentPlanModel.version))
        ).first()

        items_to_update = session.exec(
            select(ContentPlanItem).where(
                ContentPlanItem.content_plan_id == content_plan.id,
                ContentPlanItem.position >= new_content_position,
                ContentPlanItem.is_deleted.is_(False),
            )
        ).all()

        for item in items_to_update:
            item.position += 1

        new_plan_item = ContentPlanItem(
            content_plan_id=content_plan.id,
            position=new_content_position,
            herramienta=tool_type,
            descripcion=f"""[User requested to add this paragraph with AI]\nAI reasoning:\n{paragraph.metadata.reasonings[0] if paragraph.metadata.reasonings else None}""",
            plan=user_comment,
            is_deleted=False,
        )
        session.add(new_plan_item)
        session.flush()

        new_content = ContenidoGenerado(
            id_item_plan_contenido=new_plan_item.id,
            contenido=paragraph.content,
            metadatos=paragraph.metadata.model_dump() if paragraph.metadata else {},
        )
        session.add(new_content)
        session.flush()

        ai_process.additional_metadata = {
            "plan_item_id": new_plan_item.id,
            "generated_content_id": new_content.id,
        }

        session.commit()
        return new_plan_item.id
