from dataclasses import dataclass
from typing import Any, Optional

from pydantic import BaseModel, Field
from src.domain.models import ContentPlanType


@dataclass
class EpigraphData:
    id: int
    tema_id: int
    index_id: int
    epigraph_obj: Any
    epigraph_name: str
    tema_name: str
    bloque_name: str
    subject_name: str


class AddParagraphRequest(BaseModel):
    section_id: int | None = None
    prev_position: int = Field(
        description="Position of the previous paragraph from 0 to n. 0 if there is not a previous paragraph."
    )
    tool: ContentPlanType = ContentPlanType.TEXTO
    user_comment: str
    window_size: int = Field(
        default=3,
        description="How many previous and next items to take contiguous to the current paragraph",
    )
    store: bool = True


class AddParagraphResponse(BaseModel):
    content: str
    plan_item_id: int | None = None
    metadata: Optional[dict] = None
    related_chunks: list[str] = []
    related_docs: list[str] = []
