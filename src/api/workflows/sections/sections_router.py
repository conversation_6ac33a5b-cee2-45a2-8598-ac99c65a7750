from typing import List

from fastapi import APIRouter

from src.api.common.dependency_container import DependencyContainer
from src.api.workflows.sections import (
    AddParagraphRequest,
    AddParagraphResponse,
    GetSectionsByTopicRequest,
    GetSectionsByTopicResponse,
    RegenerateParagraphRequest,
    RegenerateParagraphResponse,
)

router = APIRouter(prefix="/api/v1/sections", tags=["sections"])


@router.get("/{topic_id}", response_model=List[GetSectionsByTopicResponse])
def get_sections_by_topic(topic_id: int):
    request = GetSectionsByTopicRequest(id=topic_id)
    return DependencyContainer().get_sections_by_topic_workflow().execute(request)


@router.post("/{section_id}/paragraph", response_model=AddParagraphResponse)
async def add_paragraph(section_id: int, request: AddParagraphRequest):
    request.section_id = section_id
    return await DependencyContainer().get_add_paragraph_workflow().execute(request)


@router.post(
    "/{section_id}/regenerate-paragraph", response_model=RegenerateParagraphResponse
)
async def regenerate_paragraph(section_id: int, request: RegenerateParagraphRequest):
    request.section_id = section_id
    return (
        await DependencyContainer().get_regenerate_paragraph_workflow().execute(request)
    )
