from logging import Logger

from fastapi import HTTPException, status
from sqlalchemy import Engine
from sqlmodel import Session, select

from src.domain.models import <PERSON><PERSON><PERSON><PERSON>, Tema

from .get_sections_by_topic_schemas import GetSectionsByTopicRequest


class GetSectionsByTopicWorkflow:
    def __init__(self, db_engine: Engine, logger: Logger):
        self._db_engine = db_engine
        self._logger = logger

    def execute(self, request: GetSectionsByTopicRequest):
        with Session(self._db_engine) as session:
            statement = (
                select(Epigrafe)
                .join(Tema, Epigrafe.id_tema == Tema.id)
                .where(Tema.id == request.id)
            )
            epigraphs = session.exec(statement).all()
        if not epigraphs:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Not found"
            )
        return epigraphs
