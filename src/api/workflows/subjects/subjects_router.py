from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.exc import SQLAlchemyError
from sqlmodel import Session, desc, select

from src.api.common.dependencies.get_session import get_session
from src.api.common.dependency_container import DependencyContainer
from src.domain.models import Order, Subject, Title, TitleSubject

from .subjects_schemas import (
    RequestTitleSubject,
    TitleSubjectList,
    TitleSubjectResponse,
)

router = APIRouter(prefix="/api/v1/subjects", tags=["Subjects"])


@router.post("", response_model=TitleSubjectResponse)
def create_title_subject(request: RequestTitleSubject):
    try:
        db_engine, logger = (
            DependencyContainer.get_database_engine(),
            DependencyContainer.get_logger(),
        )
        with Session(db_engine) as session:
            subject = session.exec(
                select(Subject).where(
                    Subject.name == request.name,
                    Subject.description == request.description,
                )
            ).first()
            if not subject:
                subject = Subject(
                    name=request.name,
                    description=request.description,
                    credits=request.credits,
                )
                session.add(subject)
                session.commit()
                session.refresh(subject)

            title = session.get(Title, request.title_id)
            if not title:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND, detail="Title not found"
                )

            title_subject = session.exec(
                select(TitleSubject).where(
                    TitleSubject.title_id == title.id,
                    TitleSubject.subject_id == subject.id,
                    TitleSubject.year_number == request.year_number,
                    TitleSubject.term_number == request.term_number,
                )
            ).first()

            if not title_subject:
                title_subject = TitleSubject(
                    name=f"{subject.name} - {title.name}",
                    title_id=title.id,
                    subject_id=subject.id,
                    year_number=request.year_number,
                    term_number=request.term_number,
                )
                session.add(title_subject)
                session.commit()
                session.refresh(title_subject)

            order = Order(
                coordinator_id=request.coordinator_id,
                author_id=request.author_id,
                title_subject_id=title_subject.id,
            )
            session.add(order)
            session.commit()
            session.refresh(order)
            session.refresh(title_subject)
            response = TitleSubjectResponse(
                **title_subject.model_dump(),
                order_id=order.id,
                description=title_subject.subject.description,
            )
            return response
    except SQLAlchemyError as e:
        session.rollback()
        logger.exception(f"An SQLAlchemy exception ocurred: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/{title_subject_id}", response_model=TitleSubjectResponse)
def get_title_subject(
    title_subject_id: int, session: Session = Depends(get_session)
) -> TitleSubjectResponse:
    logger = DependencyContainer.get_logger()
    statement = (
        select(TitleSubject, Order, Subject.description)
        .join(Order, TitleSubject.id == Order.title_subject_id)
        .where(TitleSubject.id == title_subject_id)
        .order_by(desc(Order.order_date))
        .limit(1)
    )
    result = session.execute(statement).first()
    title_subject, order, description = result if result else (None, None, None)
    if not (title_subject and order and description):
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
    logger.info(f"Title subject orders are: {title_subject.orders}")
    return TitleSubjectResponse(
        **title_subject.model_dump(), order_id=order.id, description=description
    )


@router.delete("/{id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_title_subject(id: int, session: Session = Depends(get_session)) -> None:
    title_subject = session.get(TitleSubject, id)
    if not title_subject:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
    session.delete(title_subject)
    session.commit()


@router.get("", description="Get subjects", response_model=TitleSubjectList)
def get_title_subjects(
    session: Session = Depends(get_session),
    title_id: Optional[int] = Query(None, description="Filter by course ID"),
):
    statement = select(TitleSubject)
    if title_id:
        statement = statement.where(TitleSubject.title_id == title_id)
    title_subjects = session.execute(statement).scalars().all()
    if not title_subjects:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="No title subjects found"
        )

    response_subjects = []
    for subject in title_subjects:
        order_statement = (
            select(Order)
            .where(Order.title_subject_id == subject.id)
            .order_by(desc(Order.order_date))
            .limit(1)
        )
        latest_order = session.execute(order_statement).scalars().first()
        order_id = latest_order.id if latest_order else None

        response_subjects.append(
            TitleSubjectResponse(
                **subject.model_dump(),
                order_id=order_id,
                description=subject.subject.description,
            )
        )
    return {"title_subjects": response_subjects}
