from datetime import datetime
from typing import List

from pydantic import BaseModel


class RequestTitleSubject(BaseModel):
    name: str
    description: str
    author_id: str = "<EMAIL>"
    coordinator_id: str = "<EMAIL>"
    title_id: int
    year_number: int
    term_number: int
    credits: int = 60


class TitleSubjectResponse(BaseModel):
    id: int
    name: str
    description: str | None = None
    term_number: int
    year_number: int
    subject_id: int
    order_id: int
    title_id: int
    created_at: datetime
    updated_at: datetime | None = None


class TitleSubjectList(BaseModel):
    title_subjects: List[TitleSubjectResponse]
