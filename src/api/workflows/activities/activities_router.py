import io

from fastapi import APIRouter, File, Form, Query, UploadFile
from src.api.common.dependency_container import DependencyContainer
from src.api.workflows.activities.generate_activity.request import (
    ActivityDesignerRequest,
)
from src.api.workflows.activities.generate_activity.response import (
    ActivityDesignerResponse,
)
from src.api.workflows.activities.generate_activity_unipro.request import (
    ActivityDesignerRequestUnipro,
)
from src.api.workflows.activities.generate_activity_unipro.response import (
    ActivityDesignerResponseUnipro,
)
from src.api.workflows.rubrics.services.file_service import FileService

router = APIRouter(prefix="/api/v1/activities", tags=["Activities"])

file_service = FileService()


@router.post(
    "/generate",
    response_model=ActivityDesignerResponse,
)
async def generate_activities(
    individual_activities: int = Query(
        ..., description="Cantidad de actividades individuales"
    ),
    group_activities: int = Query(..., description="Cantidad de actividades grupales"),
    include_ai: bool = Query(
        ..., description="Incluir el uso de IA en las actividades"
    ),
    subject_corpus: UploadFile = File(
        ...,
        description="Temario completo o temas de la asignatura sobre los que se basarán las actividades (PDF/DOCX)",
    ),
    activity_briefing: UploadFile | None = File(
        None, description="Caso o briefing para las actividades"
    ),
    subject_competencies: str | None = Form(
        None, description="Competencias de la Asignatura (opcional)"
    ),
) -> ActivityDesignerResponse:
    file = io.BytesIO(await subject_corpus.read())
    file.name = subject_corpus.filename
    content_corpus = await file_service.a_read_document(
        file=file, convert_docx_to_pdf=True
    )
    content_briefing = None
    if activity_briefing:
        file = io.BytesIO(await activity_briefing.read())
        file.name = subject_corpus.filename
        content_briefing = await file_service.a_read_document(
            file=file, convert_docx_to_pdf=True
        )
    data = ActivityDesignerRequest(
        temas_asignatura=content_corpus,
        competencias_asignatura=subject_competencies,
        uso_ia=include_ai,
        caso=content_briefing,
        num_individual=individual_activities,
        num_group=group_activities,
    )
    return await DependencyContainer().get_generate_activities_workflow().execute(data)


@router.post(
    "/generate-unipro",
    response_model=ActivityDesignerResponse,
)
async def generate_activities_unipro(
    estudio: str | None = Form(
        None, description="Titulacion que se utilizará como base para las actividades"
    ),
    incluir_ppt: bool = Form(
        False, description="Incluir presentación PPT en la actividad"
    ),
    num_diapositivas: int = Form(
        0, description="Número de diapositivas para la presentación PPT"
    ),
    incluir_texto: bool = Form(
        False, description="Incluir documento de texto en la actividad"
    ),
    num_paginas: int = Form(
        0, description="Número de páginas para el documento de texto"
    ),
    incluir_bibliografia: bool = Form(
        False, description="Incluir bibliografía en la actividad"
    ),
    num_citas: int = Form(0, description="Número de citas bibliográficas requeridas"),
    subject_corpus: UploadFile = File(
        ...,
        description="Temario completo o temas de la asignatura sobre los que se basarán las actividades (PDF/DOCX)",
    ),
) -> ActivityDesignerResponseUnipro:
    file = io.BytesIO(await subject_corpus.read())
    file.name = subject_corpus.filename
    content_corpus = await file_service.a_read_document(
        file=file, convert_docx_to_pdf=True
    )
    data = ActivityDesignerRequestUnipro(
        temas_asignatura=content_corpus, estudio=estudio
    )
    if incluir_ppt:
        data.incluir_ppt = incluir_ppt
        data.num_diapositivas = num_diapositivas if num_diapositivas else 0
    if incluir_texto:
        data.incluir_texto = incluir_texto
        data.num_paginas = num_paginas if num_paginas else 0
    if incluir_bibliografia:
        data.incluir_bibliografia = incluir_bibliografia
        data.num_citas = num_citas if num_citas else 0

    return (
        await DependencyContainer()
        .get_generate_activities_unipro_workflow()
        .execute(data)
    )
