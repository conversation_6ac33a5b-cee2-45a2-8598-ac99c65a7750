import re
from logging import Logger

from openai import AsyncOpenAI
from src.api.workflows.activities.generate_activity.prompts import (
    system_prompt_activity_designer,
    user_prompt_activity_designer,
)
from src.api.workflows.activities.generate_activity.request import (
    ActivityDesignerRequest,
)
from src.api.workflows.activities.generate_activity.response import (
    ActivityDesignerResponse,
)


class GenerateActivitiesWorkflow:
    """
    Workflow for extracting criteria from a document.
    """

    def __init__(
        self, openai_client: AsyncOpenAI, logger: Logger, model: str = "gpt-4.1"
    ) -> None:
        self.openai_client = openai_client
        self.logger = logger
        self.model = model

    async def execute(
        self, request: ActivityDesignerRequest
    ) -> ActivityDesignerResponse:
        model = self.model

        uso_ia = "SI" if request.uso_ia is True else "NO"
        total_actividades = request.num_individual + request.num_group

        self.logger.info(
            f"Generando prompts para {total_actividades} actividades (Individual: {request.num_individual}, Grupo: {request.num_group})"
        )
        system_prompt = system_prompt_activity_designer.format(
            NUM_INDIVIDUAL=str(request.num_individual),
            NUM_GROUP=str(request.num_group),
            TOTAL_ACTIVIDADES=str(total_actividades),
        )
        user_prompt = user_prompt_activity_designer.format(
            TEMAS_ASIGNATURA=request.temas_asignatura,
            COMPETENCIAS_ASIGNATURA=request.competencias_asignatura,
            INCLUSIÓN_USO_IA=uso_ia,
            CASO_O_BRIEFING_ACTIVIDADES=request.caso,
            NUM_INDIVIDUAL=str(request.num_individual),
            NUM_GROUP=str(request.num_group),
            TOTAL_ACTIVIDADES=str(total_actividades),
        )
        self.logger.debug(f"System prompt: {system_prompt}")
        self.logger.debug(f"User prompt: {user_prompt}")

        try:
            openai_response = await self.openai_client.responses.create(
                instructions=system_prompt,
                input=user_prompt,
                model=model,
                max_output_tokens=32000,
                temperature=1,
            )
            text_response = openai_response.output_text
            self.logger.info("Respuesta recibida de OpenAI")
        except Exception as e:
            self.logger.error(f"Error al obtener respuesta de OpenAI: {e}")
            raise

        try:
            criteria_table, activities = self.extract_criteria_activities(text_response)
            self.logger.info("Extracción de criterios y actividades completada")
        except Exception as e:
            self.logger.error(f"Error al extraer criterios y actividades: {e}")
            raise

        final_md = f"# Criterios Evaluación\n\n{criteria_table}\n\n# Actividades\n\n{activities}"

        response = ActivityDesignerResponse(markdown=final_md)
        self.logger.debug("Respuesta final construida correctamente")
        return response

    def extract_criteria_activities(self, response: str) -> tuple[str, str]:
        text = "".join(
            c
            for c in response
            if c == "\t"
            or c == "\n"
            or c == "\r"
            or (0x20 <= ord(c) <= 0xD7FF)
            or (0xE000 <= ord(c) <= 0xFFFD)
            or (0x10000 <= ord(c) <= 0x10FFFF)
        )
        pattern_criteria = r"<criterios_evaluación>(.*?)</criterios_evaluación>"
        pattern_activities = r"<actividades>(.*?)</actividades>"
        try:
            criteria = re.findall(pattern_criteria, text, re.DOTALL)[0]
            activities = re.findall(pattern_activities, text, re.DOTALL)[0]
            self.logger.debug("Criterios y actividades extraídos correctamente")
        except Exception as e:
            self.logger.error(f"Error al extraer patrones del texto: {e}")
            raise
        return criteria, activities
