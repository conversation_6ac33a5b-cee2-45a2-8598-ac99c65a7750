import re
from logging import Logger

from openai import AsyncOpenAI
from sqlalchemy.ext.asyncio import AsyncEngine
from sqlmodel import select
from sqlmodel.ext.asyncio.session import AsyncSession
from src.api.workflows.activities.generate_activity_unipro.prompts import (
    system_prompt_activity_designer,
    user_prompt_activity_designer,
)
from src.api.workflows.activities.generate_activity_unipro.request import (
    ActivityDesignerRequestUnipro,
)
from src.api.workflows.activities.generate_activity_unipro.response import (
    ActivityDesignerResponseUnipro,
)
from src.domain.models_toolkit import KnowledgeAreaInfo, Activities, Application


class GenerateActivitiesUniproWorkflow:
    """
    Workflow for extracting criteria from a document.
    """

    def __init__(
        self,
        openai_client: AsyncOpenAI,
        logger: Logger,
        db_engine: AsyncEngine,
        model: str = "gpt-4.1",
    ) -> None:
        self.openai_client = openai_client
        self.logger = logger
        self.model = model
        self.db_engine = db_engine

    async def execute(
        self, request: ActivityDesignerRequestUnipro
    ) -> ActivityDesignerResponseUnipro:
        self.logger.info(
            "iniciando el flujo de trabajo para diseñar actividades Unipro"
        )
        formato_entrega_parts = []

        if request.incluir_ppt:
            formato_entrega_parts.append(
                f"- Presentación PowerPoint con {request.num_diapositivas} diapositivas"
            )

        if request.incluir_texto:
            formato_entrega_parts.append(
                f"- Documento de texto (Word/PDF) con {request.num_paginas} páginas"
            )

        if request.incluir_bibliografia:
            formato_entrega_parts.append(
                f"- Incluir {request.num_citas} citas bibliográficas en formato APA"
            )

        formato_entrega = "\n".join(formato_entrega_parts)

        self.logger.info(
            f"Preparando prompt de generacion de actividades para Unipro con los siguientes detalles: {formato_entrega}"
        )
        async with AsyncSession(self.db_engine) as session:
            statement = (
                select(KnowledgeAreaInfo)
                .join(Application, KnowledgeAreaInfo.application_id == Application.id_application)
                .where(KnowledgeAreaInfo.name == request.estudio)
                .where(Application.name_application == Activities.GENERADOR_ACTIVIDADES_UNIPRO)
            )
            result = await session.exec(statement)
            area_info = result.first()

        if area_info is None:
            raise ValueError("Información del estudio no válida o no encontrada")
        data = (
            f"Estudio: {area_info.name}\nDescripción: {area_info.description}"
        )

        user_prompt = user_prompt_activity_designer.format(
            TEMAS_ASIGNATURA=str(request.temas_asignatura),
            FORMATO_ENTREGA=formato_entrega,
            ESTUDIO=data,
        )
        self.logger.debug(f"System prompt: {system_prompt_activity_designer}")
        self.logger.debug(f"User prompt: {user_prompt}")

        try:
            openai_response = await self.openai_client.responses.create(
                instructions=system_prompt_activity_designer,
                input=user_prompt,
                model=self.model,
                max_output_tokens=32000,
                temperature=1,
            )
            text_response = openai_response.output_text
            self.logger.info("Respuesta recibida de OpenAI")
        except Exception as e:
            self.logger.error(f"Error al obtener respuesta de OpenAI: {e}")
            raise

        try:
            criteria_table, activities = self.extract_criteria_activities(text_response)
            self.logger.info("Extracción de criterios y actividades completada")
        except Exception as e:
            self.logger.error(f"Error al extraer criterios y actividades: {e}")
            raise

        warning = "**Recuerda que la actividad puede tener varios enfoques de solución y que este que aquí se proporciona es una posible orientación, entre otras.**"
        final_md = f"# Advertencia\n\n{warning}\n\n# Criterios Evaluación\n\n{criteria_table}\n\n# Actividades\n\n{activities}"

        response = ActivityDesignerResponseUnipro(markdown=final_md)
        self.logger.debug("Respuesta final construida correctamente")
        return response

    def extract_criteria_activities(self, response: str) -> tuple[str, str]:
        text = "".join(
            c
            for c in response
            if c == "\t"
            or c == "\n"
            or c == "\r"
            or (0x20 <= ord(c) <= 0xD7FF)
            or (0xE000 <= ord(c) <= 0xFFFD)
            or (0x10000 <= ord(c) <= 0x10FFFF)
        )
        pattern_criteria = r"<criterios_evaluación>(.*?)</criterios_evaluación>"
        pattern_activities = r"<actividades>(.*?)</actividades>"
        try:
            criteria = re.findall(pattern_criteria, text, re.DOTALL)[0]
            activities = re.findall(pattern_activities, text, re.DOTALL)[0]
            self.logger.debug("Criterios y actividades extraídos correctamente")
        except Exception as e:
            self.logger.error(f"Error al extraer patrones del texto: {e}")
            raise
        return criteria, activities
