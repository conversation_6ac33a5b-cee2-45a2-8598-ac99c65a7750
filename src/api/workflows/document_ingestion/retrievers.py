from typing import Any, List, Optional

from langchain_core.documents import Document
from langchain_core.retrievers import BaseRetriever
from sqlalchemy import Engine
from sqlalchemy.ext.asyncio import AsyncEngine, AsyncSession
from sqlmodel import Session, text

from src.domain.models import EmbeddingModel

from .document_schemas import RetrievedChunk
from .embedding_functions import OpenAIEmbeddingFunction


class PGVectorRetriever(BaseRetriever):
    """
    Retriever adapted for the Postgres database with the configuration to query ChunkDocumento
    and to filter by different parameters such as epigrafe_ids, doc_ids related to the sources
    of information. Other things could be included like filtering by metadata, doc_type or other db fields.
    """

    openai_api_key: str | None = None
    db_engine: Engine
    async_db_engine: AsyncEngine
    model_name: str
    embedding_function: Any
    epigrafe_ids: Optional[List[int]] = []
    top_k: Optional[int] = 20
    doc_ids: Optional[str] | Optional[List[str]] = []
    min_similarity: Optional[float] = None
    add_document_context: bool = True
    # We can add more things in the future like filtering by sources, pages or metadata fields.
    # We can add also context via relationships, or context like pages, we can create another class to add context.

    def __init__(self, **data) -> None:
        if "embedding_function" not in data:
            data["embedding_function"] = OpenAIEmbeddingFunction(
                api_key=data["openai_api_key"], model_name="text-embedding-3-large"
            )
        if "model_name" not in data:
            data["model_name"] = data["embedding_function"]._model_name
        super().__init__(**data)

    def _get_relevant_documents(self, query: str) -> List[Document]:
        retrieved_documents = self._execute_queries(query)
        if self.add_document_context:
            # Add more info of the document to the metadata with a specific structure of a named tuple we define.
            # Like title, name, summary.
            # Citation could be too.
            # When formatting we can group them. -> Think what is the best approach here.
            # We can introduce things like augmenting the chunk to be the full page where the context is. Or adding image of the page in which is contained
            # This could be different modes of document context augmentation.
            pass
        # Here add context.
        return retrieved_documents

    async def _aget_relevant_documents(
        self, query: str, *, run_manager: Optional[Any] = None
    ) -> List[Document]:
        return await self._a_execute_queries(query)

    def _embed_query(self, query: str | List[str]) -> List[float]:
        if isinstance(query, str):
            return self.embedding_function([query])[0]
        return self.embedding_function(query)[0]

    def _get_query(self, query: str):
        query_embeddings = self._embed_query(query)
        base_query = f"""
        WITH ranked_chunks AS (
            SELECT
                cd.id, cd.doc_id, cd.text, cd.tokens,
                cd.additional_metadata, cd.embedding_model, cd.vector_dimensions,
                1 - (embedding <=> '{query_embeddings}') AS cosine_similarity,
                ROW_NUMBER() OVER (PARTITION BY cd.id ORDER BY 1 - (embedding <=> '{query_embeddings}') DESC) as rn
            FROM chunkdocumento AS cd
        """
        where_clauses = [f"embedding_model = '{EmbeddingModel(self.model_name).name}'"]

        if self.doc_ids:
            if isinstance(self.doc_ids, str):
                where_clauses.append(f"cd.doc_id = '{self.doc_ids}'")
            elif isinstance(self.doc_ids, list):
                doc_ids = "', '".join(self.doc_ids)
                where_clauses.append(f"cd.doc_id IN ('{doc_ids}')")

        if self.min_similarity is not None:
            where_clauses.append(
                f"1 - (embedding <=> '{query_embeddings}') >= {self.min_similarity}"
            )

        if self.epigrafe_ids is not None:
            base_query += "JOIN epigrafedocumento AS ed ON cd.doc_id = ed.id_documento"
            epigrafe_ids = ", ".join(str(e) for e in self.epigrafe_ids)
            where_clauses.append(f"ed.id_epigrafe IN ({epigrafe_ids})")

        if where_clauses:
            base_query += " WHERE " + " AND ".join(where_clauses)

        base_query += f"""
        )
        SELECT
            id, doc_id, text, tokens,
            additional_metadata, embedding_model, vector_dimensions,
            cosine_similarity
        FROM ranked_chunks
        WHERE rn = 1
        ORDER BY cosine_similarity DESC
        LIMIT {self.top_k}
        """
        query = text(base_query)
        return query

    def _execute_queries(self, query: str, return_documents=True):
        with Session(self.db_engine) as session:
            try:
                sql_query = self._get_query(query)
                result = session.exec(sql_query).all()
            except Exception:
                print("Exception ocurred while executing query")
        retrieved_chunks = self._format_retrieved_chunks(result)
        if return_documents:
            return self._rc_to_doc(retrieved_chunks)
        return retrieved_chunks

    async def _a_execute_queries(self, query: str, return_documents=True):
        async with AsyncSession(self.async_db_engine) as session:
            try:
                sql_query = self._get_query(query)
                result = await session.execute(sql_query)
                result = result.fetchall()
            except Exception as e:
                print(f"Error executing query: {e}")
                return []
        retrieved_chunks = self._format_retrieved_chunks(result)
        if return_documents:
            return self._rc_to_doc(retrieved_chunks)
        return retrieved_chunks

    def _format_retrieved_chunks(self, retrieved_chunks: List[Any]):
        return [
            RetrievedChunk(
                id=r[0],
                doc_id=r[1],
                texto=r[2],
                tokens=r[3],
                metadatos=r[4],
                modelo_embedding=EmbeddingModel[r[5]],
                dimensiones_vector=r[6],
                score=r[7],
            )
            for r in retrieved_chunks
        ]

    def _rc_to_doc(self, retrieved_chunks: List[Any]) -> List[Document]:
        documents = []
        for rc in retrieved_chunks:
            metadata = {**rc.metadatos}
            metadata["doc_id"] = str(rc.doc_id)
            metadata["chunk_id"] = str(rc.id)
            metadata["tokens"] = rc.tokens
            metadata["modelo_embedding"] = rc.modelo_embedding
            metadata["dimensiones_vector"] = rc.dimensiones_vector
            metadata["relevance_score"] = rc.score
            doc = Document(page_content=rc.texto, metadata=metadata)
            documents.append(doc)
        return documents
