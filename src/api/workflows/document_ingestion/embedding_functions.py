import logging
from typing import List, Mapping, Optional, cast

import numpy as np
from tenacity import (
    before_sleep_log,
    retry,
    retry_if_exception_type,
    stop_after_attempt,
    wait_exponential,
)

logger = logging.getLogger(__name__)


class OpenAIEmbeddingFunction:
    def __init__(
        self,
        api_key: Optional[str] = None,
        model_name: str = "text-embedding-3-large",
        organization_id: Optional[str] = None,
        api_base: Optional[str] = None,
        api_type: Optional[str] = None,
        api_version: Optional[str] = None,
        deployment_id: Optional[str] = None,
        default_headers: Optional[Mapping[str, str]] = None,
        dimensions: Optional[int] = 1536,
    ):
        """
        Initialize the OpenAIEmbeddingFunction.
        Args:
            api_key (str, optional): Your API key for the OpenAI API. If not
                provided, it will raise an error to provide an OpenAI API key.
            organization_id(str, optional): The OpenAI organization ID if applicable
            model_name (str, optional): The name of the model to use for text
                embeddings. Defaults to "text-embedding-ada-002".
            api_base (str, optional): The base path for the API. If not provided,
                it will use the base path for the OpenAI API. This can be used to
                point to a different deployment, such as an Azure deployment.
            api_type (str, optional): The type of the API deployment. This can be
                used to specify a different deployment, such as 'azure'. If not
                provided, it will use the default OpenAI deployment.
            api_version (str, optional): The api version for the API. If not provided,
                it will use the api version for the OpenAI API. This can be used to
                point to a different deployment, such as an Azure deployment.
            deployment_id (str, optional): Deployment ID for Azure OpenAI.
            default_headers (Mapping, optional): A mapping of default headers to be sent with each API request.

        """
        try:
            import openai
        except ImportError:
            raise ValueError(
                "The openai python package is not installed. Please install it with `pip install openai`"
            )

        if api_key is not None:
            openai.api_key = api_key
        # If the api key is still not set, raise an error
        elif openai.api_key is None:
            raise ValueError(
                "Please provide an OpenAI API key. You can get one at https://platform.openai.com/account/api-keys"
            )

        if api_base is not None:
            openai.api_base = api_base

        if api_version is not None:
            openai.api_version = api_version

        self._api_type = api_type
        if api_type is not None:
            openai.api_type = api_type

        if organization_id is not None:
            openai.organization = organization_id

        self._v1 = openai.__version__.startswith("1.")
        if self._v1:
            if api_type == "azure":
                self._client = openai.AzureOpenAI(
                    api_key=api_key,
                    api_version=api_version,
                    azure_endpoint=api_base,
                    default_headers=default_headers,
                ).embeddings
            else:
                self._client = openai.OpenAI(
                    api_key=api_key, base_url=api_base, default_headers=default_headers
                ).embeddings
        else:
            self._client = openai.Embedding
        self._model_name = model_name
        self._deployment_id = deployment_id
        self._dimensions = dimensions

    @retry(
        reraise=True,
        stop=stop_after_attempt(5),
        wait=wait_exponential(multiplier=1, min=1, max=10),
        retry=retry_if_exception_type(Exception),
        before_sleep=before_sleep_log(logger, logging.WARNING),
    )
    def _create_embeddings(self, input: List[str]) -> List[List[dict]]:
        """
        Helper method to create embeddings with retry logic.

        Args:
            input (List[str]): A list of texts to get embeddings for.

        Returns:
            List[dict]: The raw embedding data returned by the OpenAI API.
        """

        if self._v1:
            response = self._client.create(
                input=input,
                model=self._deployment_id or self._model_name,
                dimensions=self._dimensions,
            )
            embeddings = response.data
        else:
            if self._api_type == "azure":
                response = self._client.create(
                    input=input,
                    engine=self._deployment_id or self._model_name,
                    dimensions=self._dimensions,
                )
            else:
                response = self._client.create(
                    input=input,
                    model=self._model_name,
                    dimensions=self._dimensions,
                )
            embeddings = response["data"]
        return embeddings

    def __call__(self, input_data: List[str]) -> List[List[float]]:
        """
        Generate the embeddings for the given `input`.

        Args:
            input (List[str]): A list of texts to get embeddings for.

        Returns:
            List[List[float]]: The embeddings for the given input sorted by index.
        """
        # Replace newlines, which can negatively affect performance.
        input_data = [t.replace("\n", " ") for t in input_data]

        try:
            embeddings = self._create_embeddings(input_data)
        except Exception as e:
            logger.error(f"Failed to create embeddings after retries: {e}")
            raise

        # Sort resulting embeddings by index
        if self._v1:
            sorted_embeddings = sorted(
                embeddings,
                key=lambda e: e.index,  # type: ignore
            )
            # Return just the embeddings
            return cast(
                List[List[float]], [result.embedding for result in sorted_embeddings]
            )
        else:
            sorted_embeddings = sorted(
                embeddings,
                key=lambda e: e["index"],  # type: ignore
            )
            # Return just the embeddings
            return cast(
                List[List[float]], [result["embedding"] for result in sorted_embeddings]
            )

    @staticmethod
    def calculate_similarity(x: List[float], y: List[float]) -> float:
        xn = np.array(x)
        yn = np.array(y)
        return np.dot(xn, yn) / (np.linalg.norm(xn) * np.linalg.norm(yn))
