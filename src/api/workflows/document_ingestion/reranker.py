try:
    import torch
    from transformers import AutoModelForSequenceClassification, AutoTokenizer
except ImportError:
    pass
from typing import Any, List, Tuple

from langchain_core.documents import Document
from langchain_core.documents.compressor import BaseDocumentCompressor

from .document_schemas import RetrievedChunk


class Reranker(BaseDocumentCompressor):
    device: Any
    model_name: str
    model: Any
    tokenizer: Any
    batch_size: int
    top_k: int = 5

    def __init__(self, **data) -> None:
        if "model_name" not in data:
            data["model_name"] = "BAAI/bge-reranker-v2-m3"

        if "device" not in data:
            data["device"] = torch.device(
                "cuda" if torch.cuda.is_available() else "cpu"
            )

        if "model" not in data:
            data["model"] = AutoModelForSequenceClassification.from_pretrained(
                data["model_name"],
                torch_dtype="auto",
                trust_remote_code=True,
            )
            data["model"].to(data["device"]).eval()

        if "tokenizer" not in data:
            data["tokenizer"] = AutoTokenizer.from_pretrained(data["model_name"])

        if "batch_size" not in data:
            data["batch_size"] = 32

        if "top_k" not in data:
            data["top_k"] = 5

        print(
            f"Initializing Reranker with model: {data['model_name']} on device: {data['device']}"
        )

        super().__init__(**data)

    def _rerank(self, query: str, docs: List[str]) -> List[Tuple[str, float]]:
        all_scores = []
        for i in range(0, len(docs), self.batch_size):
            inputs = self.format_batch_input(query, docs[i : i + self.batch_size])
            with torch.no_grad():
                scores = self.model(**inputs).logits.squeeze(-1)
            all_scores.extend(scores.cpu().tolist())

        all_scores = torch.sigmoid(torch.tensor(all_scores)).tolist()

        doc_scores = list(zip(docs, all_scores))
        doc_scores.sort(key=lambda x: x[1], reverse=True)
        return doc_scores[: self.top_k]

    def _rerank_chunks(
        self, query: str, chunks: List[RetrievedChunk]
    ) -> List[RetrievedChunk]:
        """
        Rerank retrieved chunks.
        """
        all_scores = []
        for i in range(0, len(chunks), self.batch_size):
            inputs = self.format_batch_input(query, chunks[i : i + self.batch_size])
            with torch.no_grad():
                scores = self.model(**inputs).logits.squeeze(-1)
            all_scores.extend(scores.cpu().tolist())
        all_scores = torch.sigmoid(torch.tensor(all_scores)).tolist()
        for i in range(0, len(all_scores)):
            chunks[i].score = all_scores[i]
        chunks.sort(key=lambda x: x.score, reverse=True)
        return chunks[: self.top_k]

    def _rerank_documents(
        self, query: str, documents: List[Document]
    ) -> List[Document]:
        """
        Used to rerank langchain documents.
        """
        all_scores = []
        for i in range(0, len(documents), self.batch_size):
            inputs = self.format_batch_input(query, documents[i : i + self.batch_size])
            with torch.no_grad():
                scores = self.model(**inputs).logits.squeeze(-1)
            all_scores.extend(scores.cpu().tolist())
        all_scores = torch.sigmoid(torch.tensor(all_scores)).tolist()
        for i in range(0, len(all_scores)):
            documents[i].metadata["relevance_score"] = all_scores[i]
        documents.sort(key=lambda x: x.metadata["relevance_score"], reverse=True)
        return documents[: self.top_k]

    def compress_documents(
        self, documents: List[Document], query: str, **kwargs
    ) -> List[Document]:
        documents = self._rerank_documents(query, documents)
        # Additional callbacks not included
        return documents

    def format_batch_input(
        self, query: str, batch: List[RetrievedChunk] | List[str] | List[Document]
    ):
        if not (
            isinstance(batch[0], RetrievedChunk)
            or isinstance(batch[0], str)
            or isinstance(batch[0], Document)
        ):
            raise TypeError("Invalid batch input")
        if isinstance(batch[0], RetrievedChunk):
            batch = [rc.texto for rc in batch]
        if isinstance(batch[0], Document):
            batch = [rc.page_content for rc in batch]
        inputs = self.tokenizer(
            [query] * len(batch),
            batch,
            padding=True,
            truncation=True,
            return_tensors="pt",
        ).to(self.device)
        return inputs
