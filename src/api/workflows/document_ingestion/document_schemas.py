import enum
import uuid
from typing import Any, Dict, List

from pydantic import BaseModel, Field, HttpUrl, field_serializer

from src.domain.models import EmbeddingModel


class FuenteArchivo(str, enum.Enum):
    DUCKDUCKGO = "DUCKDUCKGO"


class TipoArchivo(str, enum.Enum):
    PDF = "PDF"


class DocMessage(BaseModel):
    url: HttpUrl
    tipo_archivo: TipoArchivo
    fuente_archivo: FuenteArchivo
    epigrafe_ids: List[int]
    metadata: Dict[str, str] | None = None

    @field_serializer("url")
    def serialize_url(self, url: HttpUrl, _info):
        return str(url)

    @field_serializer("tipo_archivo", "fuente_archivo")
    def serialize_enum(self, enum_value: enum.Enum, _info):
        return enum_value.value


class DocMessages(BaseModel):
    docs: List[DocMessage]


class RetrievedChunk(BaseModel):
    id: uuid.UUID = Field(default_factory=uuid.uuid4)
    doc_id: uuid.UUID
    tokens: int
    texto: str
    metadatos: dict[str, Any] = Field(default={})
    modelo_embedding: EmbeddingModel
    dimensiones_vector: int
    score: float
