from typing import List, Union

try:
    import torch
    from sentence_transformers import SentenceTransformer
except ImportError:
    pass


class Embedding:
    def __init__(self, model_name: str = "BAAI/bge-m3", batch_size: int = 32):
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        print(
            f"Initializing Embedding with model: {model_name} on device: {self.device}"
        )
        self._model_name = model_name
        self.model = SentenceTransformer(model_name)
        self.model.to(self.device)
        self.batch_size = batch_size

    def __call__(self, texts: Union[str, List[str]]) -> List[List[float]]:
        if isinstance(texts, str):
            texts = [texts]

        all_embeddings = []
        for i in range(0, len(texts), self.batch_size):
            batch = texts[i : i + self.batch_size]
            with torch.no_grad():
                batch_embeddings = self.model.encode(batch, convert_to_tensor=True)
            all_embeddings.append(batch_embeddings)
        combined_embeddings = torch.cat(all_embeddings, dim=0)
        return combined_embeddings.tolist()

    @staticmethod
    def calculate_similarity(a: "torch.Tensor", b: "torch.Tensor") -> "torch.Tensor":
        return torch.nn.functional.cosine_similarity(a, b)
