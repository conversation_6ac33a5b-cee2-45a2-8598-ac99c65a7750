from pydantic import BaseModel


# TODO: Input should be orderId, and optional indexId, in case of not providing indexId,
# the search should be done in last index from orderId, and in case indexId is provided,
# the search should be done in that index, ONLY if the index belongs to the orderId
class SearchReferencesRequest(BaseModel):
    indice_id: int


# TODO: Definir el modelo de respuesta
class SearchReferencesResponse(BaseModel):
    pass
