import asyncio
import json
import uuid
from dataclasses import dataclass
from datetime import datetime, timed<PERSON><PERSON>
from logging import Logger
from typing import Any, List, Literal, Tuple

from fastapi import HTTPException, status
from ia_gen_core.prompts import PromptManager
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import Async<PERSON><PERSON><PERSON>
from sqlalchemy.orm import selectinload
from sqlmodel import select
from sqlmodel.ext.asyncio.session import AsyncSession

from src.api.common.services.index_repository import IndexRepository
from src.api.common.services.message_broker.queue_interface import QueueInterface
from src.api.common.services.search_agent import (
    AgentResult,
    ExtractContentOutputModel,
    SearchAgent,
)
from src.api.common.services.search_engine import SearchEngine, SearchResult
from src.api.common.services.structs import (
    EpigrafeWithContext,
)
from src.api.common.tools.utils import sanitize_string
from src.api.common.utils.check_prompt import check_template
from src.domain.models import (
    Doc,
    DocStatus,
    DocumentSource,
    Epigrafe,
    Indice,
    IndiceStatus,
    Lenguaje,
    TipoArchivo,
    TopicStatus,
)

from .search_references_schemas import SearchReferencesRequest, SearchReferencesResponse


class ProposedSearch(BaseModel):
    reasoning: str
    search_queries: List[str]


class ConceptList(BaseModel):
    concepts: List[str]


@dataclass
class SearchValidationResult:
    temas: List[Any]
    epigrafes_by_tema: dict[int, List[EpigrafeWithContext]]


class SearchReferencesWorkflow:
    def __init__(
        self,
        db_engine: AsyncEngine,
        logger: Logger,
        search_engine: SearchEngine,
        search_agent: SearchAgent,
        model: Any,
        thinking_model: Any,
        prompt_manager: PromptManager,
        index_repository: IndexRepository,
        process_document_queue_interface: QueueInterface,
    ) -> None:
        self._db_engine = db_engine
        self._logger = logger
        self._prompt_manager = prompt_manager
        self._search_engine = search_engine
        self._search_agent = search_agent
        self._model = model
        self._thinking_model = thinking_model
        self._index_repository = index_repository
        self._queue_interface = process_document_queue_interface

    async def validate(
        self, request: SearchReferencesRequest
    ) -> SearchValidationResult:
        """
        Validate the request and return the validation results
        """
        return await self._perform_validations(request.indice_id)

    async def _perform_validations(self, indice_id: int) -> SearchValidationResult:
        """
        Perform all validations and return the retrieved data
        """
        async with AsyncSession(self._db_engine) as session:
            indice = await session.get(Indice, indice_id)
            if not indice:
                self._logger.exception(f"Index with ID {indice_id} not found")
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND, detail="Index not found"
                )

            if indice.status in [
                IndiceStatus.SEARCH,
                IndiceStatus.CONTENT_GENERATION,
                IndiceStatus.CONTENT_REVIEW_PENDING,
                IndiceStatus.IN_DEPTH_GENERATION,
                IndiceStatus.IN_DEPTH_REVIEW_PENDING,
                IndiceStatus.TEST_GENERATION,
                IndiceStatus.TEST_REVIEW_PENDING,
                IndiceStatus.COMPLETED,
            ]:
                self._logger.warning(
                    f"Cannot search references for index with ID {indice_id} because it is already in status {indice.status}"
                )
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Cannot search references for index already in status {indice.status}",
                )

            temas = await self._index_repository.get_index_elements(
                indice_id, structure_type="tema"
            )
            if not temas:
                self._logger.exception(
                    f"Attempted to search without having topics present for indice_id: {indice_id}"
                )
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND, detail="No topics found"
                )
            await self._index_repository.change_index_status(
                indice_id, IndiceStatus.SEARCH
            )
            for tema in temas:
                await self._index_repository.change_topic_status(
                    topic_id=tema.id,
                    status=TopicStatus.CONTENT_IN_PROGRESS,
                    session=session,
                )

        epigrafes_by_tema = {}
        for tema in temas:
            epigrafes_with_context = (
                await self._index_repository.get_epigrafes_with_context(
                    indice_id=indice_id, tema_id=tema.id
                )
            )
            if not epigrafes_with_context:
                self._logger.exception(
                    f"Attempted to search without having epigrafes present for tema_id: {tema.id} and indice_id: {indice_id}"
                )
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND, detail="No epigrafes found"
                )
            epigrafes_by_tema[tema.id] = epigrafes_with_context
        return SearchValidationResult(temas=temas, epigrafes_by_tema=epigrafes_by_tema)

    async def _process_references(
        self,
        references: AgentResult,
        epigrafes_with_context: list[EpigrafeWithContext],
        indice_id: int,
        source: str,
    ) -> list[tuple[str, str]]:
        # Save empty reference in the database
        documents = []
        for ref in references.documents:
            if ref.url not in self._search_agent.url_content_cache.keys():
                self._logger.warning(
                    f"Reference {ref.url} not found in URL content cache, skipping."
                )
                continue
            if ref.is_pay_walled or not ref.is_info_accesbile:
                self._logger.warning(
                    f"Reference {ref.url} is paywalled or not accessible, skipping."
                )
                continue
            async with AsyncSession(self._db_engine) as session:
                try:
                    # Check if the reference is already in the database and handle it based on its status
                    statement = (
                        select(Doc)
                        .options(selectinload(Doc.epigrafes))
                        .where(Doc.document_url == ref.url)
                    )
                    statement_result = await session.exec(statement)
                    doc = statement_result.first()

                    if doc:
                        self._logger.info(
                            f"Reference {ref.url} already exists in the database with status {doc.status}"
                        )
                        # Handle the document based on its current status
                        should_continue = await self._handle_existing_document(
                            session,
                            doc,
                            [epigrafe.id for epigrafe in epigrafes_with_context],
                        )
                        if should_continue:
                            continue
                    # Save reference in the database, only need to save link between epigrafe and reference and the href
                    success, doc, file_uuid = await self._store_empty_document(
                        session=session,
                        file_source=source,
                        reference=ref,
                        epigrafe_ids=[
                            epigrafe.id for epigrafe in epigrafes_with_context
                        ],
                    )
                    if success:
                        self._logger.info(
                            f"[REF] Stored new doc {file_uuid} — committing"
                        )
                        await session.commit()
                        exists = await session.get(Doc, uuid.UUID(file_uuid))
                        documents.append((ref.url, file_uuid))
                        self._logger.info(
                            f"[REF] Post-commit doc: {file_uuid} present: {str(bool(exists))}"
                        )
                except Exception as e:
                    self._logger.exception(
                        f"An exception ocurred while storing reference from {ref.url} and source {source} for index {indice_id} in the database and saving it to cooldown storage: {e}"
                    )
                    await session.rollback()
        return documents

    async def _send_messages_to_queue(
        self,
        request: SearchReferencesRequest,
        documents: list[tuple[str, str]],  # list[tuple[ref_url, doc_uuid]]
    ):
        if not documents:
            self._logger.info(
                f"No documents to send to queue for index {request.indice_id}"
            )
            return
        async with self._queue_interface:
            await self._index_repository.change_index_status(
                request.indice_id, IndiceStatus.CONTENT_GENERATION
            )
            try:
                await self._queue_interface.send_messages(
                    payloads=[{"file_uuid": d[1]} for d in documents]
                )
                self._logger.info(
                    f"Successfully enqueued {len(documents)} documents for index {request.indice_id}"
                )
            except Exception as e:
                self._logger.exception(
                    f"An exception occurred while enqueueing {len(documents)} documents for index {request.indice_id}: {e}"
                )

    async def execute(
        self,
        request: SearchReferencesRequest,
        validation_result: SearchValidationResult | None = None,
    ) -> SearchReferencesResponse:
        try:
            if validation_result is None:
                validation_result = await self._perform_validations(request.indice_id)
            all_documents = []
            all_epigrafes_by_tema = []
            coroutines = []
            references_by_tema = []
            sources_by_tema = []
            for tema in validation_result.temas:
                all_epigrafes_by_tema.append(
                    validation_result.epigrafes_by_tema[tema.id]
                )
            for tema, epigrafes_with_context in zip(
                validation_result.temas, all_epigrafes_by_tema
            ):
                coroutines.append(self._perform_search(epigrafes_with_context))
            searches = await asyncio.gather(*coroutines)
            references_by_tema, sources_by_tema = map(list, zip(*searches))

            for tema, epigrafes_with_context, references, source in zip(
                validation_result.temas,
                all_epigrafes_by_tema,
                references_by_tema,
                sources_by_tema,
            ):
                try:
                    all_documents = await self._process_references(
                        references, epigrafes_with_context, request.indice_id, source
                    )
                    await self._send_messages_to_queue(request, all_documents)
                except Exception as e:
                    self._logger.exception(
                        f"An exception ocurred while processing tema {tema.id} for index {request.indice_id} Error: {e}"
                    )
                    continue
            return SearchReferencesResponse()
        except Exception as e:
            self._logger.exception(
                f"An exception ocurred processing document message: {e}"
            )
            await self._index_repository.change_index_status(
                request.indice_id, IndiceStatus.CONTENT_GENERATION
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Error ocurred executing search references workflow {e}",
            )

    async def _create_search_queries(
        self, epigrafes: List[EpigrafeWithContext]
    ) -> ProposedSearch:
        prompt = self._prompt_manager.get_prompt("search-queries", prompt_type="text")
        prompt_template, _ = check_template(prompt)
        few_shot = self._prompt_manager.get_few_shot_examples(prompt)
        chain = prompt_template | self._model.with_structured_output(ProposedSearch)
        result = chain.invoke(
            {
                "SUBJECT_NAME": epigrafes[0].nombre_asignatura,
                "CONTENT_BLOCK": epigrafes[0].nombre_bloque,
                "CURRENT_TOPIC": epigrafes[0].nombre_tema,
                "SUBTOPICS": [e.nombre for e in epigrafes],
                "EXAMPLES": [
                    f"{example.input}{example.output}" for example in few_shot
                ],
            }
        )
        return result

    async def _create_search_queries_atomic(
        self, epigrafes: list[EpigrafeWithContext]
    ) -> ConceptList:
        prompt = self._prompt_manager.get_prompt(
            "search-queries-atomic", prompt_type="text"
        )
        prompt_template, _ = check_template(prompt)
        chain = prompt_template | self._thinking_model.with_structured_output(
            ConceptList
        )
        result = chain.invoke(
            {
                "SUBJECT_NAME": epigrafes[0].nombre_asignatura,
                "CONTENT_BLOCK": epigrafes[0].nombre_bloque,
                "CURRENT_TOPIC": epigrafes[0].nombre_tema,
                "SUBTOPICS": [e.nombre for e in epigrafes],
                "DIDACTIC_INSTRUCTIONS": [
                    epigrafe.didactic_instructions for epigrafe in epigrafes
                ],
            }
        )
        return result

    async def _perform_search(
        self,
        epigrafes: list[EpigrafeWithContext],
        max_results_per_query: int = 10,
        type: Literal["engine", "agent"] = "agent",
    ) -> tuple[AgentResult, str]:
        """
        Perform search using engine or agent, and return the results.

        Args:
            epigrafes (List[EpigrafeWithContext]): List of Epigrafes with context
            max_results_per_query (int): Maximum number of results per query
            type (str): Type of search to perform ("engine" or "agent"), default is "agent"

        Returns:
            List[RatedResult]: List of rated search results
        """
        source = "agent"
        if type == "engine":
            search_results = await self._search_references_with_search_engine(
                epigrafes, max_results_per_query
            )
            source = search_results[0].source
            search_results = AgentResult(
                documents=[
                    ExtractContentOutputModel(i.title, i.href, [], "", [], "")
                    for i in search_results
                ]
            )
        # TODO: if we want to use the search engine, we need to fill the lacking fields in the RatedResult.
        else:
            search_results = await self._search_references_with_agent(
                epigrafes, max_results_per_query
            )

        return search_results, source

    async def _search_references_with_search_engine(
        self, epigrafes: List[EpigrafeWithContext], max_results_per_query: int = 10
    ) -> List[SearchResult]:
        try:
            proposed_search = await self._create_search_queries_atomic(epigrafes)
        except Exception as e:
            self._logger.exception(
                f"An exception ocurred while creating search queries for epigrafes {epigrafes} with error: {e}"
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Error creating search queries: {e}",
            )
        final_results = []
        for query in proposed_search.search_queries:
            self._logger.info(f"Searching for query: {query}")
            try:
                search_results = await self._search_engine.search(
                    query=query,
                    max_results=max_results_per_query,
                    max_fetch_results=20,
                    use_reranker=True,
                    only_pdfs=True,
                )
            except Exception as e:
                self._logger.exception(
                    f"An exception ocurred while searching for query {query} with error: {e}"
                )

            final_results.extend(search_results)

        # Remove duplicates, a bit hacky to do it using list comprehension (last condition exploits the fact that add returns None and !None is True)
        urls = set()
        final_results = [
            result
            for result in final_results
            if result.href not in urls and not urls.add(result.href)
        ]
        return final_results

    async def _search_references_with_agent(
        self, epigrafes: List[EpigrafeWithContext], max_results_per_query: int = 10
    ) -> AgentResult:
        try:
            proposed_search = await self._create_search_queries_atomic(epigrafes)
            self._logger.info(
                f"\n\ncore concepts {json.dumps(proposed_search.model_dump(), indent=2, ensure_ascii=False)}\n\n"
            )
        except Exception as e:
            self._logger.exception(
                f"An exception ocurred while creating search queries for epigrafes {epigrafes} with error: {e}"
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Error creating search queries: {e}",
            )
        final_results = AgentResult(documents=[])
        futures = []
        for query in proposed_search.concepts:
            didactic_instruction = "\n".join(
                [i.didactic_instructions for i in epigrafes if i.didactic_instructions]
            )
            futures.append(
                self._search_agent.run(
                    query=query,
                    search_context=f"""Estas haciendo una busqueda de las fuentes para basar el contenido de una asignatura denominada \"{epigrafes[0].nombre_asignatura}\".
Concretamente estas en el bloque \"{epigrafes[0].nombre_bloque}\" y el tema \"{epigrafes[0].nombre_tema}\". El concepto principal de la bsuqueda es una linea de investigacion en la que se basa el temario.
Ten en cuenta que para generar el contenido de esta asignatura se han indicado una serie de instrucciones didacticas, en ellos esta indicado el contenido que debe abordar el tema.
cuando busques informacion de la query original, ten en cuenta cualquier informacion relevante que este incluida las instrucciones didacticas asi como enlaces relevantes que puedan encontrarse en ellas
instrucciones:
{didactic_instruction}
las fuentes que encuentren deberan cubrir de la forma mas amplia posible todas las bases del concepto prinicipal.
""",
                    max_results=max_results_per_query,
                )
            )

        results = await asyncio.gather(*futures, return_exceptions=True)
        for r, c in zip(results, proposed_search.concepts):
            if isinstance(r, Exception):
                self._logger.warning(
                    f"An exception ocurred while searching for query {c} with error: {r}"
                )
                continue
            final_results.documents.extend(r.documents)

        # Remove duplicates, a bit hacky to do it using list comprehension (last condition exploits the fact that add returns None and !None is True)
        urls = set()
        final_results = AgentResult(
            documents=[
                result
                for result in final_results.documents
                if result.url not in urls and not urls.add(result.url)
            ]
        )
        return final_results

    async def _store_empty_document(
        self,
        session: AsyncSession,
        file_source: str,
        reference: ExtractContentOutputModel,
        epigrafe_ids: List[int],
    ) -> Tuple[bool, Doc | None, str | None]:
        """
        Store an empty document in the database.

        Args:
            file_source (str): Source identifier for the document
            blob_url (str): URL of the stored blob
            file_uuid (str): Unique identifier for the document
            file_type (str): Type of the document

        Returns:
            Tuple[bool, Documento | None]: Tuple containing:
                - Success status (bool)
                - Created Documento instance (or None if failed)
                - File UUID (str or None if failed)
        """
        try:
            fuente_documento = await self._get_or_create_fuente(session, file_source)
            file_uuid = uuid.uuid4()
            raw_content = (
                self._search_agent.url_content_cache[reference.url]
                if self._search_agent.url_content_cache[reference.url] is not None
                else ""
            )
            sanitized_content = sanitize_string(raw_content)

            sanitized_title = sanitize_string(reference.title)
            sanitized_summary = sanitize_string(
                "\n".join(["- " + i for i in reference.summary])
                if reference.summary
                else ""
            )
            sanitized_authors = [
                sanitize_string(author) for author in (reference.authors or [])
            ]

            document = Doc(
                id=file_uuid,
                document_url=sanitize_string(reference.url),
                name=sanitized_title,
                content=sanitized_content,
                id_fuente_documento=fuente_documento.id,
                token_count=-1,
                file_type=TipoArchivo.PDF
                if reference.url.find("pdf") != -1
                else TipoArchivo.WEB,
                lenguage=Lenguaje.EN if reference.lenguaje == "EN" else Lenguaje.ES,
                status=DocStatus.PENDING,
                summary=sanitized_summary,
                authors=sanitized_authors,
                publication_date=reference.date,
            )

            await self._add_epigrafes_to_document(session, document, epigrafe_ids)

            session.add(document)
            return True, document, str(file_uuid)

        except Exception as e:
            self._logger.exception(
                f"An exception has occurred while storing empty document from {reference.url} of file source {file_source} and type {TipoArchivo.PDF if reference.url.find('pdf') != -1 else TipoArchivo.WEB} in the db: {e}"
            )
            return False, None, None

    async def _handle_existing_document(
        self, session: AsyncSession, document: Doc, epigrafe_ids: List[int]
    ) -> bool:
        """
        Handle existing document based on its status.

        Args:
            session (AsyncSession): SQLModel Session instance
            document (Doc): Document instance
            epigrafe_ids (List[int]): List of Epigrafe IDs

        Returns:
            bool: True if processing should continue to next reference, False if a new document should be created
        """

        if document.status == DocStatus.PENDING:
            # Check if the document has been in PENDING state for too long (more than 24 hours)
            orphan_threshold = datetime.now() - timedelta(hours=24)
            if document.created_at < orphan_threshold:
                # This is an old PENDING document - likely orphaned
                self._logger.warning(
                    f"Found orphaned document {document.id} in PENDING state for over 24 hours. URL: {document.document_url}. Cleaning up..."
                )
                document.epigrafes.clear()
                await session.delete(document)
                await session.commit()
                self._logger.info(f"[ORPHAN] Doc {document.id} deleted and committed")
                return False
            else:
                self._logger.info(
                    f"Document {document.id} is in PENDING state but was created recently ({document.created_at}). Adding new epigrafes."
                )
                await self._add_epigrafes_to_document(session, document, epigrafe_ids)
                await session.commit()
                return True

        elif document.status in [DocStatus.PROCESSED, DocStatus.PROCESSING]:
            await self._add_epigrafes_to_document(session, document, epigrafe_ids)
            await session.commit()
            return True

        elif document.status == DocStatus.FAILED:
            self._logger.warning(
                f"Document {document.id} failed processing. Will recreate."
            )
            # Precargar epigrafes con documentos para evitar lazy load en async
            statement = (
                select(Epigrafe)
                .options(selectinload(Epigrafe.documents))
                .where(
                    Epigrafe.id.in_([epigrafe.id for epigrafe in document.epigrafes])
                )
            )
            statement_result = await session.exec(statement)
            epigrafes = statement_result.all()
            for epigrafe in epigrafes:
                if document in epigrafe.documents:
                    epigrafe.documents.remove(document)
                    session.add(epigrafe)

            await session.delete(document)
            await session.commit()
            return False

        elif document.status == DocStatus.THREAT_DETECTED:
            self._logger.warning(
                f"Document {document.id} was previously marked as containing threats. Skipping."
            )
            return True
        else:
            self._logger.warning(
                f"Unrecognized status in Document: {document.status}, skipping doc and continuing"
            )
            return True

    async def _add_epigrafes_to_document(
        self, session: AsyncSession, document: Doc, epigrafe_ids: List[int]
    ) -> None:
        """
        Add Epigrafes to a Document.

        Args:
            session (AsyncSession): SQLModel Session instance
            document (Doc): Document instance
            epigrafe_ids (List[int]): List of Epigrafe IDs
        """
        self._logger.info(f"Adding epigrafes {epigrafe_ids} to document {document.id}")
        statement = (
            select(Epigrafe)
            .options(selectinload(Epigrafe.documents))
            .where(Epigrafe.id.in_(epigrafe_ids))
        )
        statement_result = await session.exec(statement)
        epigrafes = statement_result.all()
        for epigrafe in epigrafes:
            if document not in epigrafe.documents:
                epigrafe.documents.append(document)
                session.add(epigrafe)
                self._logger.info(
                    f"Added document {document.id} to epigrafe {epigrafe.id}"
                )
            else:
                self._logger.info(
                    f"Document {document.id} already exists in epigrafe {epigrafe.id}"
                )

    async def _get_or_create_fuente(
        self, session: AsyncSession, file_source: str
    ) -> DocumentSource:
        """
        Get existing or create new FuenteDocumento.

        Args:
            file_source (str): Source identifier for the document

        Returns:
            FuenteDocumento | None: FuenteDocumento instance or None if operation fails
        """
        try:
            statement = select(DocumentSource).where(DocumentSource.name == file_source)
            statement_result = await session.exec(statement)
            fuente_documento = statement_result.first()
            if not fuente_documento:
                fuente_documento = DocumentSource(name=file_source)
                session.add(fuente_documento)
                await session.commit()
                await session.refresh(fuente_documento)
            return fuente_documento
        except Exception as e:
            self._logger.exception(
                f"An exception occurred while getting/creating fuente documento: {e}"
            )
            raise e
