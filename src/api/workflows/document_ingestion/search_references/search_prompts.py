search_queries_input_few_shot_1 = """
Input:

<subject_name>Biology</subject_name>
<content_block>Genetics</content_block>
<current_topic>Mendelian Inheritance</current_topic>
<subtopics>
- Dominant and recessive alleles
- Punnett squares
- Monohybrid and dihybrid crosses
</subtopics>
"""
search_queries_output_few_shot_1 = """
Output:
{
  "reasoning": "These search queries are designed to explore key aspects of Mendelian inheritance, covering foundational concepts like dominant and recessive alleles, practical tools like Punnett squares, and modern applications of the theory.",
  "search_queries": [
    "Mendelian inheritance patterns research",
    "Dominant and recessive alleles examples biology",
    "Punnett square applications in genetics",
    "Monohybrid vs dihybrid crosses study",
    "Modern applications of Mendelian genetics"
  ]
}
"""

example_1 = search_queries_input_few_shot_1 + search_queries_output_few_shot_1

search_queries_input_few_shot_2 = """
Input:

<subject_name>Historia de España</subject_name>
<content_block>Edad Moderna</content_block>
<current_topic>El Siglo de Oro</current_topic>
<subtopics>
- Literatura del Siglo de Oro
- Arte barroco español
- Economía y sociedad en la España del siglo XVII
</subtopics>
"""

search_queries_output_few_shot_2 = """
Output:
{
  "reasoning": "These search queries aim to explore the cultural, economic, and artistic aspects of the Spanish Golden Age, focusing on literature, baroque art, societal structure, and economic trends during the 17th century.",
  "search_queries": [
    "Siglo de Oro español características principales",
    "Autores más importantes literatura Siglo de Oro",
    "Arte barroco español pintores y obras destacadas",
    "Economía España siglo XVII crisis y consecuencias",
    "Sociedad española Siglo de Oro clases sociales",
    "Influencia Siglo de Oro cultura española actual"
  ]
}
"""

example_2 = search_queries_input_few_shot_2 + search_queries_output_few_shot_2
examples = "## Few Shot 1" + example_1 + "## Few Shot 2" + example_2

prompt_search_queries = """
## Task Information
You are tasked with generating a list of search queries to find useful external or academic information related to a specific educational subject and topic. These queries will be used in a search engine to gather high-quality, relevant information. Keep into account this search engine will only search in academic repositories.

You will be provided with the following context:

<subject_name>
{{SUBJECT_NAME}}
</subject_name>

<content_block>
{{CONTENT_BLOCK}}
</content_block>

<current_topic>
{{CURRENT_TOPIC}}
</current_topic>

<subtopics>
{{SUBTOPICS}}
</subtopics>

Using this information, generate a list of 5-10 search queries that would be useful for finding external and academic sources related to the subject matter.
Follow these guidelines:

## Guidelines

1. Create queries in either Spanish or English, whichever is more appropriate for the subject.
2. Make the queries concise and specific to get relevant results from a search engine that searches in academic repositories.
3. Focus on academic or reliable sources by including terms like "research", "study", "journal", or "academic" where appropriate.
4. Consider including the subject name, content block, or specific subtopics in the queries.
5. Aim for a mix of broad and specific queries to cover different aspects of the topic.
6. Do not search for really general terms that could be already answered by an artificial intelligence like yourself.

## Output format

You will first reason about the information that was given to you and how to best adhere to the guidelines to make the best queries possible.

Here are some examples to guide you:

{{EXAMPLES}}

Remember to tailor your queries to the specific subject and topic provided, and aim for a balance between breadth and depth in your search terms.
Output json as instructed:"""

prompt_search_queries_atomic = """
## Task Information

Your goal is to identify lines of investigation for a given topic of a corpus of a university subject.

A line of investigation is a conceptual path that leads to a group of knowledge on which the subject is based and, if further studied, can lead to deep understanding of the given topic. These lines represent strategic research directions that encompass related concepts, methodologies, and applications within a coherent thematic framework. **Each line should represent a distinct research avenue that covers theoretical foundations, practical applications, methodological approaches, or emerging trends relevant to the topic.** When dealing with interdisciplinary subjects, ensure you balance lines from **all** relevant domains. You must cover **ALL** different areas of knowledge, even if they are completely disjoint.

The information provided includes subject name, content block name, current topic name, subtopics of the topic and the didactic instruction that explain the body of the content. Using these elements, generate exactly 4 lines of investigation that comprehensively cover the main topic in the corpus. Make sure that you cover the entire content of the topic, including each knowledge area and any specific application contexts.

A line of invesgation must be a general and information dense title that conver the main concepts of the knowledge group.

### Provided Context

<subject_name>
{{SUBJECT_NAME}}
</subject_name>

<content_block>
{{CONTENT_BLOCK}}
</content_block>

<current_topic>
{{CURRENT_TOPIC}}
</current_topic>

<subtopics>
{{SUBTOPICS}}
</subtopics>

<didactic_instructions>
{{DIDACTIC_INSTRUCTIONS}}
</didactic_instructions>

# Output Format

- A list of exactly 4 lines of investigation that comprehensively cover the main topic in the corpus"""

prompt_search_queries_atomic_old = """
## Task Information

Your goal is to extract the core concepts of a given topic of a corpus of a university subject.

A core concept is an concept or piece of knowledge on which the topic is based. These concepts should not be composite; instead, they cover the foundational knowledge that, when combined with the rest, forms the subject's knowledge base. **This definition includes theoretical fundamentals, domain-specific techniques, practical applications, and specialized terminology that are essential to the topic.** When dealing with interdisciplinary subjects, ensure you balance concepts from **all** relevant domains. You must cover **ALL** different areas of knowledge, even if they are completely disjoint.


The information provided includes subject name, content block name, current topic name, subtopics of the topic and the didactic instruction that explain the body of the content. Using these elements, generate a list of as much core concepts as needed to cover the main topic in the corpus. Make sure that you cover the entire content of the topic, including each knowledge area and any specific application contexts.

### Provided Context

<subject_name>
{{SUBJECT_NAME}}
</subject_name>

<content_block>
{{CONTENT_BLOCK}}
</content_block>

<current_topic>
{{CURRENT_TOPIC}}
</current_topic>

<subtopics>
{{SUBTOPICS}}
</subtopics>

<didactic_instructions>
{{DIDACTIC_INSTRUCTIONS}}
</didactic_instructions>

# Output Format

- A list of as much core concepts as needed to cover the main topic in the corpus"""
