import subprocess
import sys
from abc import ABC, abstractmethod
from functools import lru_cache
from itertools import chain
from typing import List

import spacy


class BaseChunker(ABC):
    @abstractmethod
    def split_text(self, text: str) -> List[str]:
        pass


@lru_cache(maxsize=None)
def load_nlp(language):
    print("Language", language)
    try:
        if language.name == "EN":
            nlp = spacy.load("en_core_web_sm")
        elif language.name == "ES":
            nlp = spacy.load("es_core_news_sm")
        else:
            nlp = spacy.load("en_core_web_sm")
        return nlp
    except OSError:
        print(f"Model for {language} not found. Attempting to download...")
        try:
            if language.name == "EN":
                subprocess.check_call(
                    [sys.executable, "-m", "spacy", "download", "en_core_web_sm"]
                )
                nlp = spacy.load("en_core_web_sm")
            elif language.name == "ES":
                subprocess.check_call(
                    [sys.executable, "-m", "spacy", "download", "es_core_news_sm"]
                )
                nlp = spacy.load("es_core_news_sm")
            else:
                raise ValueError(f"Unsupported language: {language}")
            return nlp
        except subprocess.CalledProcessError as e:
            print(f"Failed to download model: {e}")
            raise


class SentenceSplitter(BaseChunker):
    def __init__(self, language: str):
        self.nlp = load_nlp(language)
        self.language = language

    def _process_chunk(self, chunk):
        doc = self.nlp(chunk)
        sent_texts = [sent.text for sent in doc.sents]
        return sent_texts

    def split_text(self, text, max_len=1000000):
        self.nlp.max_length = max_len
        chunks = [text[i : i + max_len] for i in range(0, len(text), max_len)]

        def process_and_filter(chunk):
            processed = self._process_chunk(chunk)
            return [sentence for sentence in processed if sentence.strip()]

        return list(chain.from_iterable(process_and_filter(chunk) for chunk in chunks))
