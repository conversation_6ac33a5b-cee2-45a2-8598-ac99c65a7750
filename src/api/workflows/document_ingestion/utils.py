from typing import List

from src.domain.models import Doc

try:
    import enum

    import instructor
    import tiktoken
except ImportError:
    pass
from langchain.prompts import ChatPromptTemplate
from openai import OpenAI
from pydantic import BaseModel

from src.api.common.tools.utils import extract_content

prompt_summary = """You are tasked with creating a concise summary of a given document. The goal is to produce a brief overview that captures the maximum meaning in a compressed format. This summary should be optimized for document retrieval purposes, focusing solely on conveying the essential information without any explanations or elaborations.

Here is the document to be summarized:

<document>
{document}
</document>

Follow these guidelines to create the summary:

1. Identify the main topics and key information presented in the document.
2. Focus on factual content, important concepts, and central ideas.
3. Omit any explanations, background information, or contextual details unless they are crucial to understanding the core message.
4. Use concise, clear language. Avoid flowery or verbose phrasing.
5. Do not include your own interpretations or opinions.
6. Present the information in bullet point format for easy scanning.
7. Limit the summary to a maximum of 5-7 bullet points.
8. Ensure each bullet point is no longer than one sentence.
9. Give the document a descriptive name based on the original info of the doc, you will include inside <name> tags, if a name is directly mentioned inside the article use it as the name.
10. Identify the auhtors of the document and include each of then inside an <author> tag, each of the author names should be in a different tag.
11. Identify the publishing date of the document and include it inside a <date> tag with the format 'YYYY'.
12. Summarize and name in the original language of the document.

Write your summary inside <summary> tags, the name inside <name> tags, each of the authors inside an <author> tag and the publishing date inside a <date> tag.
Remember, the goal is to create a dense, information-rich summary that can be used effectively for document retrieval purposes and a name for properly identifying the article.
"""


# TODO: Add here the language detection and embedding functions.
def openai_token_count(string: str) -> int:
    """Returns the number of tokens in a text string."""
    encoding = tiktoken.get_encoding("cl100k_base")
    num_tokens = len(encoding.encode(string, disallowed_special=()))
    return num_tokens


def get_text_from_first_n_tokens(string: str, n: int) -> str:
    """Returns text for the first n tokens in a text string."""
    encoding = tiktoken.get_encoding("cl100k_base")
    tokens = encoding.encode(string, disallowed_special=())
    return encoding.decode(tokens[:n])


def break_text_into_chunks(text: str, max_tokens: int) -> List[str]:
    """Breaks a text into chunks of a maximum number of tokens."""
    encoding = tiktoken.get_encoding("cl100k_base")
    tokens = encoding.encode(text, disallowed_special=())
    chunks = [
        encoding.decode(tokens[i : i + max_tokens])
        for i in range(0, len(tokens), max_tokens)
    ]
    return chunks


class Lenguaje(str, enum.Enum):
    EN = "EN"
    ES = "ES"


class SinglePrediction(BaseModel):
    """Class for predicting the language of a given text in a single label. If not spanish or english, classify to the nearest."""

    prediction: Lenguaje


def classify_language(data: str) -> SinglePrediction:
    """Perform single-label classification on the input text."""
    from src.api.common.dependency_container import DependencyContainer

    client = instructor.from_openai(
        OpenAI(api_key=DependencyContainer.get_application_settings().OPENAI_API_KEY)
    )
    return client.chat.completions.create(
        model="gpt-4o",
        response_model=SinglePrediction,
        messages=[
            {
                "role": "user",
                "content": f"Classify the following text language: {data}",
            },
        ],
    )


def detect_language(document_text: str):
    try:
        result = classify_language(document_text)
        language = result.prediction
        return language
    except Exception as e:
        print(f"Error ocurred detecting language: {e}")
        return Lenguaje.EN


def extract_info_document(document: Doc):
    from src.api.common.dependency_container import DependencyContainer

    llm_manager = DependencyContainer.get_llm_manager()
    llm = llm_manager.get_llm(provider="openai", model_name="gpt-4o-mini")
    template = ChatPromptTemplate.from_template(prompt_summary)
    chain = template | llm
    try:
        content_chunks = break_text_into_chunks(document.content, 100000)
        doc_summary = ""
        for index, chunk in enumerate(content_chunks):
            result = chain.invoke({"document": chunk})
            doc_summary += extract_content(result.content, "summary")[0] + "\n"
            if index == 0:
                document.name = extract_content(result.content, "name")[0]
                document.authors = extract_content(result.content, "author")
                document.publication_date = extract_content(result.content, "date")[0]
        # TODO: Might make sense to summarize the summaries in case there is more than one chunk
        document.summary = doc_summary
        document.lenguage = detect_language(document.content[:500])

    except Exception as e:
        raise RuntimeError(f"Error while extracting document's {document.id} info: {e}")


# citation following APA
def generate_document_citation(document: Doc) -> None:
    publishing_date = document.publication_date or "n.d."
    # no author is a special case
    if len(document.authors) == 0:
        final_citation = (
            f"{document.name} ({publishing_date}). {document.document_url}."
        )
        inline_citation = f"{document.name} ({publishing_date})."
        document.final_citation = final_citation
        document.inline_citation = inline_citation
    else:
        if len(document.authors) == 1:
            author_str = document.authors[0]
        elif len(document.authors) == 2:
            author_str = f"{document.authors[0]} & {document.authors[1]}"
        else:
            author_str = f"{document.authors[0]} et al."
        document.final_citation = f"{author_str} ({publishing_date}). {document.name}. {document.document_url}."
        document.inline_citation = f"({author_str}, {publishing_date})"

    return None
