import os
import re
import uuid
from datetime import datetime, timedelta, timezone
from logging import Logger
from typing import Op<PERSON>, Tuple

from azure.storage.blob import (
    BlobClient,
    BlobSasPermissions,
    BlobServiceClient,
    generate_blob_sas,
)

from src.api.common.tools.loaders import DocumentLoader


# TODO: Should have logger inyected in the constructor
class BlobStorageInterface:
    def __init__(self, azure_connect_str: str, logger: Logger) -> None:
        self.azure_connect_str = azure_connect_str
        self.blob_service_client = BlobServiceClient.from_connection_string(
            self.azure_connect_str
        )
        self._logger = logger

    def _get_blob_client(
        self, container: str, folder_structure: str, filename: str
    ) -> Tuple[BlobClient, str]:
        """
        Get a blob client for the specified path.

        Args:
            container (str): The container name
            folder_structure (str): The folder structure for the blob
            filename (str): The filename for the blob

        Returns:
            tuple: (blob_client, blob_name)
        """
        container_client = self.blob_service_client.get_container_client(container)

        blob_name = f"{folder_structure}/{filename}"
        blob_client = container_client.get_blob_client(blob_name)

        return blob_client, blob_name

    # TODO: Move out of this class and make it a utility function
    def _generate_folder_structure(self, file_source: str) -> str:
        """
        Generate the folder structure for blob storage.

        Args:
            file_source (str): Source identifier for the document
            file_uuid (str): UUID for the file

        Returns:
            str: Generated folder structure
        """
        return f"{str(datetime.now().date())}/{file_source}"

    def store_blob(
        self, doc_loader: DocumentLoader, file_source: str, container_name: str
    ) -> Tuple[bool, str | None, str | None]:
        """
        Store a document in Azure Blob Storage from a local file.

        Args:
            doc_loader: DocumentLoader instance containing the document to store
            file_source (str): Source identifier for the document

        Returns:
            Tuple[bool, str | None, str | None]: Tuple containing:
                - Success status (bool)
                - Blob URL (str or None if failed)
                - File UUID (str or None if failed)
        """
        try:
            file_uuid = str(uuid.uuid4())
            folder_structure = self._generate_folder_structure(file_source)
            sanitized_filename = self._sanitize_filename(
                os.path.basename(doc_loader.metadata.get("filename", file_uuid))
            )

            blob_client, blob_name = self._get_blob_client(
                container_name, folder_structure, sanitized_filename
            )

            with open(doc_loader.doc_path, "rb") as data:
                blob_client.upload_blob(data, overwrite=True)

            self._logger.info(f"File {blob_name} uploaded to Azure Blob Storage")
            doc_loader.cleanup()
            return True, blob_client.url, file_uuid
        except Exception as e:
            self._logger.error(f"An exception occurred while storing blob: {e}")
            return False, None, None

    def store_blob_from_url(
        self,
        container_name: str,
        source_url: str,
        file_source: str,
        file_uuid: Optional[str],
    ) -> Tuple[bool, str | None, str | None]:
        """
        Store a document in Azure Blob Storage from a URL.

        Args:
            container_name (str): The container name
            source_url (str): URL of the file to upload
            file_source (str): Source identifier for the document

        Returns:
            Tuple[bool, str | None, str | None]: Tuple containing:
                - Success status (bool)
                - Blob URL (str or None if failed)
                - File UUID (str or None if failed)
        """
        try:
            file_uuid = file_uuid or str(uuid.uuid4())
            folder_structure = self._generate_folder_structure(file_source)

            blob_client, blob_name = self._get_blob_client(
                container_name, folder_structure, file_uuid
            )

            # Upload from URL
            blob_client.upload_blob_from_url(source_url, overwrite=True)

            self._logger.info(
                f"File {blob_name} uploaded to Azure Blob Storage from URL"
            )
            return True, blob_client.url, file_uuid
        except Exception as e:
            self._logger.error(
                f"An exception occurred while storing blob from URL: {e}"
            )
            raise e

    def delete_blob(self, container_name: str, blob_name: str) -> bool:
        """
        Delete a blob from the specified container.

        Args:
            container_name (str): The container name
            blob_name (str): The blob name

        Returns:
            bool: Deletion status
        """
        try:
            container_client = self.blob_service_client.get_container_client(
                container_name
            )
            blob_client = container_client.get_blob_client(blob_name)
            blob_client.delete_blob()
            self._logger.info(f"Blob {blob_name} deleted from Azure Blob Storage")
            return True
        except Exception as e:
            self._logger.info(f"An exception occurred while deleting blob: {e}")
            return False

    # TODO: Move out of this class and make it a utility function
    def _sanitize_filename(self, filename: str) -> str:
        """
        Sanitize the filename by removing invalid characters and limiting length.

        Args:
            filename (str): Original filename

        Returns:
            str: Sanitized filename
        """
        sanitized_filename = re.sub(r"[^\w.\- ]", "_", filename)
        if len(sanitized_filename) > 100:
            name, ext = os.path.splitext(sanitized_filename)
            sanitized_filename = name[:96] + ext
        return sanitized_filename

    def get_blob_sas(
        self, container_name: str, blob_name: str, read_only: bool = True
    ) -> str:
        """
        Generate a Shared Access Signature (SAS) token for the specified blob.

        Args:
            container_name (str): The container name
            blob_name (str): The blob name
            read_only (bool): Whether the SAS token should be read-only

        Returns:
            str: Generated SAS token
        """
        # Remove the base URL + container_name from the blob name in case it was included
        blob_name = blob_name.replace(
            self.blob_service_client.url + container_name + "/", ""
        )
        sas = generate_blob_sas(
            account_name=self.blob_service_client.account_name,
            container_name=container_name,
            blob_name=blob_name,
            account_key=self.blob_service_client.credential.account_key,
            permission=BlobSasPermissions(read=True, write=(not read_only)),
            expiry=datetime.now(timezone.utc) + timedelta(hours=1),
        )
        return f"{self.blob_service_client.url}{container_name}/{blob_name}?{sas}"
