import asyncio
import inspect
import logging
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor

import numpy as np
import tiktoken

try:
    from .sentence_splitter import SentenceSplitter
except ImportError:
    pass
from abc import ABC, abstractmethod
from collections import namedtuple
from functools import lru_cache
from typing import List, Union

from src.api.common.tools.loaders import TextSegment

from .embedding import Embedding
from .utils import Lengua<PERSON>, openai_token_count


class BaseChunker(ABC):
    @abstractmethod
    def split_text(self, text: str) -> List[str]:
        pass


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# from chunking_evaluation.utils import get_openai_embedding_function,
# openai_token_count -> I have to have utils for embedding_functions
# TODO: Adapt this with the previous

Sentence = namedtuple("Sentence", ["text", "vectors", "tokens", "segment"])


class SmartSemanticChunker(BaseChunker):
    def __init__(
        self,
        embedding_function=None,
        max_chunk_size=600,
        min_chunk_size=100,
        length_function=openai_token_count,
        batch_size=1200,
        multiplier=1.35,
        embedding_model_name="BAAI/bge-m3",
        language=Lenguaje.EN,
        threshold=0.3,
        use_threading=True,
        max_concurrent_batches=10,
    ):
        self.splitter = SentenceSplitter(language=language)
        if embedding_function is not None:
            self.embedding_function = embedding_function
        else:
            self.embedding_function = Embedding(
                batch_size=batch_size, model_name=embedding_model_name
            )
        self._min_chunk_size = min_chunk_size
        self._max_chunk_size = max_chunk_size
        self.batch_size = batch_size
        self.multiplier = multiplier
        self.token_counter = tiktoken.encoding_for_model("gpt-4")
        self.length_function = length_function
        self.threshold = threshold
        self.use_threading = use_threading
        self.max_concurrent_batches = max_concurrent_batches

    @classmethod
    def from_text_segments(cls, text_segments: List[TextSegment], **kwargs):
        """Synchronous version - kept for backward compatibility."""
        init_signature = inspect.signature(cls.__init__)
        default_params = {
            k: v.default
            for k, v in init_signature.parameters.items()
            if v.default is not inspect.Parameter.empty
        }
        default_params.update(kwargs)
        chunker = cls(**default_params)
        return chunker.split_text(text=text_segments), chunker

    @classmethod
    async def from_text_segments_async(cls, text_segments: List[TextSegment], **kwargs):
        """
        Async version that prevents blocking the event loop during heavy processing.
        This method runs the entire chunking process in a thread pool to avoid blocking the event loop.
        """
        init_signature = inspect.signature(cls.__init__)
        default_params = {
            k: v.default
            for k, v in init_signature.parameters.items()
            if v.default is not inspect.Parameter.empty
        }
        default_params.update(kwargs)
        chunker = cls(**default_params)
        loop = asyncio.get_event_loop()
        with ThreadPoolExecutor(max_workers=1) as executor:
            result = await loop.run_in_executor(
                executor, chunker.split_text, text_segments
            )
        return result, chunker

    @lru_cache(maxsize=10000)
    def _cached_encode(self, sentence):
        return len(self.token_counter.encode(sentence, disallowed_special=()))

    def _vectorize_sentences(self, sents: List[str]):
        utf8_sents = [
            str(sent).encode("utf-8", errors="ignore").decode("utf-8") for sent in sents
        ]

        if self.use_threading and len(utf8_sents) > self.batch_size:
            return self._vectorize_sentences_threaded(utf8_sents)
        else:
            return self._vectorize_sentences_sequential(utf8_sents)

    def _vectorize_sentences_threaded(self, utf8_sents: List[str]):
        """Threaded version using ThreadPoolExecutor for concurrent processing."""
        from concurrent.futures import as_completed

        batches = [
            utf8_sents[i : i + self.batch_size]
            for i in range(0, len(utf8_sents), self.batch_size)
        ]

        max_workers = min(len(batches), self.max_concurrent_batches)

        def process_batch_with_error_handling(batch_info):
            batch_idx, batch = batch_info
            try:
                result = self.embedding_function(batch)
                return batch_idx, result, None
            except Exception as e:
                return batch_idx, None, e

        all_results = [None] * len(batches)

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_batch = {
                executor.submit(process_batch_with_error_handling, (i, batch)): i
                for i, batch in enumerate(batches)
            }

            for future in as_completed(future_to_batch):
                batch_idx = future_to_batch[future]
                try:
                    result = future.result()
                    all_results[batch_idx] = result
                except Exception as exc:
                    print(f"Batch {batch_idx} generated an exception: {exc}")
                    raise exc

        all_vectors = []
        for result in all_results:
            if result is None:
                raise RuntimeError("Some batches failed to process")

            batch_idx, batch_vectors, error = result
            if error:
                print(f"Problematic batch {batch_idx}: {batches[batch_idx]}")
                raise error

            all_vectors.extend(batch_vectors)

        print("[END] Vectorizing sentences in batch end (threaded)")
        return np.array(all_vectors)

    def _vectorize_sentences_sequential(self, utf8_sents: List[str]):
        """Sequential fallback version."""
        all_vectors = []

        for i in range(0, len(utf8_sents), self.batch_size):
            print(
                f"[START] Vectorizing sentences in batch start, batch size: {self.batch_size}"
            )
            batch = utf8_sents[i : i + self.batch_size]
            try:
                batch_vectors = self.embedding_function(batch)
                all_vectors.extend(batch_vectors)
            except Exception as e:
                print(f"Error during embedding of batch {i // self.batch_size}: {e}")
                print(f"Problematic batch: {batch}")
                raise

        print("[END] Vectorizing sentences in batch end (sequential)")
        return np.array(all_vectors)

    def _tokenize_sentences(self, sents: List[str]) -> np.ndarray:
        return np.array([self._cached_encode(sent) for sent in sents])

    def _prepare_batch_text(
        self, text_segments: List[TextSegment]
    ) -> tuple[str, List[tuple]]:
        """
        Concatenate all segments into one text with boundary tracking.

        Returns:
            tuple: (concatenated_text, segment_boundaries)
            segment_boundaries: List of (start_pos, end_pos, segment) tuples
        """
        all_text = ""
        segment_boundaries = []

        for seg in text_segments:
            start_pos = len(all_text)

            segment_text = seg.text.strip()
            all_text += segment_text

            end_pos = len(all_text)
            segment_boundaries.append((start_pos, end_pos, seg))

            all_text += "\n\n"

        all_text = all_text.rstrip("\n")

        return all_text, segment_boundaries

    def _map_sentences_to_segments(
        self, sentences: List[str], all_text: str, segment_boundaries: List[tuple]
    ) -> List[TextSegment]:
        """
        Map each sentence back to its original segment using character positions.

        Args:
            sentences: List of split sentences
            all_text: The concatenated text that was split
            segment_boundaries: List of (start_pos, end_pos, segment) tuples

        Returns:
            List of segments corresponding to each sentence
        """
        all_segments = []
        current_pos = 0

        for sentence in sentences:
            sentence_start = all_text.find(sentence, current_pos)

            if sentence_start == -1:
                sentence_start = current_pos

            corresponding_segment = None
            for start_pos, end_pos, segment in segment_boundaries:
                if start_pos <= sentence_start < end_pos:
                    corresponding_segment = segment
                    break

            if corresponding_segment is None and segment_boundaries:
                corresponding_segment = segment_boundaries[-1][2]

            all_segments.append(corresponding_segment)

            current_pos = sentence_start + len(sentence)

        return all_segments

    def split_text(
        self, text: Union[str, List[TextSegment]]
    ) -> List[Union[str, TextSegment]]:
        sents = []
        if isinstance(text, str):
            sentences = self.splitter.split_text(text)
            logger.debug(f"Split text into {len(sentences)} sentences")
            vectors = self._vectorize_sentences(sentences)
            tokens = self._tokenize_sentences(sentences)
            logger.debug(f"Token counts for first 10 sentences: {tokens[:10]}")

            all_segments = [None] * len(sentences)
            sents = [
                Sentence(s, v, t, sg)
                for s, v, t, sg in zip(sentences, vectors, tokens, all_segments)
            ]

        if isinstance(text, list):  # List[TextSegment]
            if isinstance(text[0], TextSegment):
                all_text, segment_boundaries = self._prepare_batch_text(text)
                all_sentences = self.splitter.split_text(all_text)
                all_segments = self._map_sentences_to_segments(
                    all_sentences, all_text, segment_boundaries
                )
                vectors = self._vectorize_sentences(all_sentences)
                tokens = self._tokenize_sentences(all_sentences)
                sents = [
                    Sentence(s, v, t, sg)
                    for s, v, t, sg in zip(all_sentences, vectors, tokens, all_segments)
                ]
            else:
                raise TypeError(
                    "Invalid text provided, you need to provide a List of TextSegment if initializing from_text_segments."
                )
        else:
            raise TypeError(
                "Invalid text provided, you need to provide a str or a List[TextSegment] depending on your initialization method."
            )

        logger.debug(f"Created {len(sents)} Sentence objects")
        clusters = self._cluster_recursively(sents)
        logger.debug(f"Clustering resulted in {len(clusters)} clusters")

        if isinstance(text, str):
            result = []
            for i, cluster in enumerate(clusters):
                chunk = "".join([sent.text for sent in cluster])
                total_tokens = sum(sent.tokens for sent in cluster)
                result.append(chunk)
                logger.debug(
                    f"Chunk {i}: {len(chunk)} characters, {total_tokens} tokens"
                )
                logger.debug(f"Chunk {i} content: '{chunk[:100]}...'")
                logger.debug(f"Chunk {i} contains {len(cluster)} sentences")

            logger.debug(f"Final result: {len(result)} text chunks")
            return result
        else:
            result = [self._merge_text_segments(cluster) for cluster in clusters]
            logger.debug(f"Final result: {len(result)} merged TextSegments")
            return result

    def _merge_text_segments(self, cluster: List[Sentence]) -> TextSegment | str:
        merged_text = "".join([sent.text for sent in cluster])
        first_segment = cluster[0].segment
        last_segment = cluster[-1].segment
        total_tokens = sum(sent.tokens for sent in cluster)

        if first_segment and last_segment:
            # Merge metadata
            merged_metadata = (
                first_segment.metadata.copy()
            )  # Assuming metadata is a dict
            merged_metadata.update(last_segment.metadata)
            merged_metadata.update(
                {
                    "page_start": int(first_segment.page_number),
                    "page_end": int(last_segment.page_number),
                    "total_tokens": int(total_tokens),
                }
            )

            merged_segment = TextSegment(
                text=merged_text,
                page_number=first_segment.page_number,  # You might want to handle multi-page segments differently
                metadata=merged_metadata,
                file_name=first_segment.file_name,
                start_char=first_segment.start_char,
                end_char=last_segment.end_char,
                tokens=total_tokens,
            )
            return merged_segment
        else:
            return merged_text

    def _should_split_cluster(self, current_tokens, tokens, i):
        return (
            current_tokens + tokens[i] > self._max_chunk_size
            and current_tokens >= self._min_chunk_size
        )

    def _should_continue_cluster(
        self, i, similarities, current_threshold, current_tokens, tokens
    ):
        return (
            i + 1 < len(tokens)
            and similarities[i] >= current_threshold
            and current_tokens + tokens[i + 1] <= self._max_chunk_size
        )

    def _handle_large_cluster(
        self, current_cluster, depth, max_depth, current_threshold
    ):
        logger.debug("Cluster exceeds max size, recursively clustering")
        new_threshold = self._increase_threshold(current_threshold)
        return self._cluster_recursively(
            current_cluster,
            depth=depth + 1,
            max_depth=max_depth,
            current_threshold=new_threshold,
        )

    def _process_sentence_for_clustering(
        self,
        sents: List[Sentence],
        i: int,
        tokens: np.ndarray,
        similarities: np.ndarray,
        current_threshold: float,
        current_cluster: List[Sentence],
        current_tokens: int,
        clusters: List[List[Sentence]],
        depth: int,
        max_depth: int,
    ) -> (List[Sentence], int):
        """Procesa una oración individual dentro del bucle de clustering."""
        if self._should_split_cluster(current_tokens, tokens, i):
            clusters.append(current_cluster)
            current_cluster = []
            current_tokens = 0

        current_cluster.append(sents[i])
        current_tokens += tokens[i]

        if current_tokens >= self._min_chunk_size:
            if self._should_continue_cluster(
                i, similarities, current_threshold, current_tokens, tokens
            ):
                return (
                    current_cluster,
                    current_tokens,
                )  # Continuar con el cluster actual

            # Si no continuamos, el cluster actual debe ser finalizado o subdividido.
            if current_tokens <= self._max_chunk_size:
                clusters.append(current_cluster)
                current_cluster = []
                current_tokens = 0
            else:  # current_tokens > self._max_chunk_size
                sub_clusters = self._handle_large_cluster(
                    current_cluster, depth, max_depth, current_threshold
                )
                clusters.extend(sub_clusters)
                current_cluster = []
                current_tokens = 0
        return current_cluster, current_tokens

    def _finalize_clusters(
        self,
        current_cluster: List[Sentence],
        current_tokens: int,
        clusters: List[List[Sentence]],
        depth: int,
        max_depth: int,
        current_threshold: float,
    ):
        """Finaliza los clusters después del bucle principal."""
        if current_cluster:
            if current_tokens <= self._max_chunk_size:
                clusters.append(current_cluster)
            else:
                sub_clusters = self._handle_large_cluster(
                    current_cluster, depth, max_depth, current_threshold
                )
                clusters.extend(sub_clusters)

    def _cluster_recursively(
        self, sents: List[Sentence], depth=0, max_depth=10, current_threshold=None
    ):
        logger.debug(
            f"Starting _cluster_recursively at depth {depth} with {len(sents)} sentences"
        )

        if current_threshold is None:
            current_threshold = self.threshold

        if depth >= max_depth:
            logger.warning(f"Max recursion depth ({max_depth}) reached. Forcing split.")
            return self._force_split(sents)

        if len(sents) == 1:
            return [sents]

        vectors = np.array([sent.vectors for sent in sents])
        similarities = np.einsum("ij,ij->i", vectors[:-1], vectors[1:])
        tokens = np.array([sent.tokens for sent in sents])

        clusters = []
        current_cluster = []
        current_tokens = 0

        for i in range(len(sents)):
            current_cluster, current_tokens = self._process_sentence_for_clustering(
                sents,
                i,
                tokens,
                similarities,
                current_threshold,
                current_cluster,
                current_tokens,
                clusters,  # clusters se modifica in-place
                depth,
                max_depth,
            )

        self._finalize_clusters(
            current_cluster,
            current_tokens,
            clusters,  # clusters se modifica in-place
            depth,
            max_depth,
            current_threshold,
        )

        logger.debug(
            f"Finished _cluster_recursively at depth {depth}, returning {len(clusters)} clusters"
        )
        return clusters

    def _increase_threshold(self, current_threshold):
        return min(current_threshold * self.multiplier, 0.99)

    def _force_split(self, sents: List[Sentence]):
        """Force split the sentences into chunks no larger than max_chunk_size"""
        clusters = []
        current_cluster = []
        current_tokens = 0
        for sent in sents:
            if current_tokens + sent.tokens > self._max_chunk_size:
                if current_cluster:
                    clusters.append(current_cluster)
                    current_cluster = []
                    current_tokens = 0
            current_cluster.append(sent)
            current_tokens += sent.tokens
        if current_cluster:
            clusters.append(current_cluster)
        return clusters
