from fastapi import APIRouter, BackgroundTasks, HTTPException
from pydantic import ValidationError

from src.api.common.dependency_container import DependencyContainer
from src.api.workflows.document_ingestion.search_references.search_references_schemas import (
    SearchReferencesRequest,
)

logger = DependencyContainer.get_logger(module_name=__name__)
router = APIRouter(prefix="/api/v1/documents", tags=["Documents"])


@router.post("/search_references", description="Search references for epigrafes")
async def search_references(
    request: SearchReferencesRequest, background_tasks: BackgroundTasks
):
    workflow = DependencyContainer.get_search_references_workflow()
    validation_results = None
    try:
        validation_results = await workflow.validate(request)
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))

    background_tasks.add_task(workflow.execute, request, validation_results)
    return {"message": "Search initiated"}
