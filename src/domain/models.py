import enum
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

from ia_gen_core.database.prompts.sqlmodel_models import (
    FewShotExample,  # noqa
    FewShotType,  # noqa
    Prompt,  # noqa
    PromptType,  # noqa
)
from pgvector.sqlalchemy import Vector  # type: ignore
from sqlalchemy import Column, DateTime, String, func
from sqlalchemy.dialects.postgresql import JSON<PERSON>
from sqlmodel import Field, ForeignKey, Relationship, SQLModel, UniqueConstraint

CASCADE_RULE = "all, delete-orphan"
ORDER_ID = "order.id"
USER_ID = "user.id"
INDICE_ID = "indice.id"
EPIGRAFE_ID = "epigrafe.id"
TEMA_ID = "tema.id"
AI_PROCESS_ID = "aiprocess.id"


class AIProcessStatus(str, enum.Enum):
    PENDING = "PENDING"
    IN_PROGRESS = "IN_PROGRESS"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"


class AIProcessType(str, enum.Enum):
    SEARCH_SOURCES = "SEARCH_SOURCES"
    SCHEMA_GENERATION = "SCHEMA_GENERATION"
    SCHEMA_REGENERATION = "SCHEMA_REGENERATION"
    SCHEMA_BLOCK_REGENERATION = "SCHEMA_BLOCK_REGENERATION"
    SCHEMA_TOPIC_REGENERATION = "SCHEMA_TOPIC_REGENERATION"
    COMPETENCIE_REGENERATION = "COMPETENCIE_REGENERATION"
    DIDACTIC_INSTRUCTIONS = "DIDACTIC_INSTRUCTIONS"
    DIDACTIC_INSTRUCTIONS_REGENERATION = "DIDACTIC_INSTRUCTIONS_REGENERATION"
    CONTENT_GENERATION_WITH_SOURCES = "CONTENT_GENERATION_WITH_SOURCES"
    COMPETENCIE_FROM_SCHEMA = "COMPETENCIE_FROM_SCHEMA"
    COMPETENCIE_WITHOUT_SCHEMA = "COMPETENCIE_WITHOUT_SCHEMA"
    SCHEMA_GENERATION_FROM_COMPETENCIES = "SCHEMA_GENERATION_FROM_COMPETENCIES"
    EXTRACT_SUBJECT_DESCRIPTIONS = "EXTRACT_SUBJECT_DESCRIPTIONS"
    COMPETENCIES_FROM_TITLE_DESCRIPTOR = "COMPETENCIES_FROM_TITLE_DESCRIPTOR"
    GENERATE_TOPIC_QUERIES = "GENERATE_TOPIC_QUERIES"
    GENERATE_CONTENT_FOR_TOPIC = "GENERATE_CONTENT_FOR_TOPIC"
    ADD_PARAGRAPH = "ADD_PARAGRAPH"
    REGENERATE_PARAGRAPH = "REGENERATE_PARAGRAPH"
    GENERATE_IN_DEPTH = "GENERATE_IN_DEPTH"
    GENERATE_TESTS = "GENERATE_TESTS"


class ContentPlanStatus(str, enum.Enum):
    PENDING = "PENDING"
    ITEMS_CREATED = "ITEMS_CREATED"
    CONTENT_CREATION = "CONTENT_CREATION"
    FINISHED = "FINISHED"


class ContentPlanType(str, enum.Enum):
    TEXTO = "TEXTO"
    GRAFICA = "GRAFICA"
    IMAGEN = "IMAGEN"
    TABLA = "TABLA"
    HUMANO = "HUMANO"


class TipoArchivo(str, enum.Enum):
    PDF = "PDF"
    VIDEO = "VIDEO"
    AUDIO = "AUDIO"
    WEB = "WEB"
    TEXTO = "TEXTO"
    OTRO = "OTRO"
    MARKDOWN = "MARKDOWN"


class Lenguaje(str, enum.Enum):
    EN = "EN"
    ES = "ES"
    OTRO = "OTRO"


class EmbeddingModel(str, enum.Enum):
    ALL_MINILM_L6_V2 = "all-MiniLM-L6-v2"
    ADA_002 = "text-embedding-ada-002"
    TEXT_EMBEDDING_3_SMALL = "text-embedding-3-small"
    TEXT_EMBEDDING_3_LARGE = "text-embedding-3-large"
    JINA_EMBEDDING_V2_BASE_ES = "jina-embeddings-v2-base-es"
    BGE_M3 = "BAAI/bge-m3"


class TitleType(str, enum.Enum):
    GRADO = "GRADO"
    MASTER = "MASTER"


class EmailTriggerStatus(str, enum.Enum):
    INDEX = "cambio_estado_indice"
    TOPIC = "cambio_estado_tema"


class IndiceStatus(str, enum.Enum):
    NOT_STARTED = "NOT_STARTED"
    NON_CONFIRMED_COMPETENCIES = "NON_CONFIRMED_COMPETENCIES"
    CONFIRMED_COMPETENCIES = "CONFIRMED_COMPETENCIES"
    INDICE_GENERATION = "INDICE_GENERATION"
    INDICE_REVIEW_PENDING = "INDICE_REVIEW_PENDING"
    INSTRUCTIONS_GENERATION = "INSTRUCTIONS_GENERATION"
    INSTRUCTIONS_REVIEW_PENDING = "INSTRUCTIONS_REVIEW_PENDING"
    SEARCH = "SEARCH"
    CONTENT_GENERATION = "CONTENT_GENERATION"
    CONTENT_REVIEW_PENDING = "CONTENT_REVIEW_PENDING"
    IN_DEPTH_GENERATION = "IN_DEPTH_GENERATION"
    IN_DEPTH_REVIEW_PENDING = "IN_DEPTH_REVIEW_PENDING"
    TEST_GENERATION = "TEST_GENERATION"
    TEST_REVIEW_PENDING = "TEST_REVIEW_PENDING"
    COMPLETED = "COMPLETED"
    CANCELED = "CANCELED"


class TopicStatus(str, enum.Enum):
    NOT_STARTED = "NOT_STARTED"
    CONTENT_IN_PROGRESS = "CONTENT_IN_PROGRESS"
    CONTENT_GENERATION = "CONTENT_GENERATION"
    CONTENT_REVIEW_PENDING = "CONTENT_REVIEW_PENDING"
    CONTENT_FAILED = "CONTENT_FAILED"
    IN_DEPTH_IN_PROGRESS = "IN_DEPTH_IN_PROGRESS"
    IN_DEPTH_GENERATION = "IN_DEPTH_GENERATION"
    IN_DEPTH_REVIEW_PENDING = "IN_DEPTH_REVIEW_PENDING"
    IN_DEPTH_FAILED = "IN_DEPTH_FAILED"
    TEST_IN_PROGRESS = "TEST_IN_PROGRESS"
    TEST_GENERATION = "TEST_GENERATION"
    TEST_REVIEW_PENDING = "TEST_REVIEW_PENDING"
    TEST_FAILED = "TEST_FAILED"
    COMPLETED = "COMPLETED"
    CANCELED = "CANCELED"


class UserRole(str, enum.Enum):
    AUTHOR = "AUTHOR"
    COORDINATOR = "COORDINATOR"
    AUTHOR_COORDINATOR = "AUTHOR_COORDINATOR"


class DocStatus(str, enum.Enum):
    PENDING = "PENDING"
    PROCESSING = "PROCESSING"
    PROCESSED = "PROCESSED"
    FAILED = "FAILED"
    THREAT_DETECTED = "THREAT_DETECTED"


class RelatedSection(str, enum.Enum):
    INDICE = "INDICE"
    INSTRUCTIONS = "INSTRUCTIONS"
    CONTENT = "CONTENT"
    IN_DEPTH = "IN_DEPTH"
    TESTS = "TESTS"


class OrderCoordinator(SQLModel, table=True):
    __tablename__ = "order_coordinator"
    order_id: int = Field(
        sa_column=Column(ForeignKey(ORDER_ID, ondelete="CASCADE"), primary_key=True)
    )
    user_id: str = Field(
        sa_column=Column(ForeignKey(USER_ID, ondelete="CASCADE"), primary_key=True)
    )
    created_by: str = Field(
        default=None, sa_column=Column(String, server_default="ia_gen_user")
    )
    updated_by: str = Field(
        default=None, sa_column=Column(String, onupdate="ia_gen_user")
    )
    created_at: datetime = Field(
        default_factory=datetime.now,
        sa_column=Column(DateTime, server_default=func.now()),
    )
    updated_at: datetime = Field(
        default_factory=datetime.now, sa_column=Column(DateTime, onupdate=func.now())
    )


class OrderAuthor(SQLModel, table=True):
    __tablename__ = "order_author"
    order_id: int = Field(
        sa_column=Column(ForeignKey(ORDER_ID, ondelete="CASCADE"), primary_key=True)
    )
    user_id: str = Field(
        sa_column=Column(ForeignKey(USER_ID, ondelete="CASCADE"), primary_key=True)
    )
    created_by: str = Field(
        default=None, sa_column=Column(String, server_default="ia_gen_user")
    )
    updated_by: str = Field(
        default=None, sa_column=Column(String, onupdate="ia_gen_user")
    )
    created_at: datetime = Field(
        default_factory=datetime.now,
        sa_column=Column(DateTime, server_default=func.now()),
    )
    updated_at: datetime = Field(
        default_factory=datetime.now, sa_column=Column(DateTime, onupdate=func.now())
    )


class User(SQLModel, table=True):
    id: str | None = Field(default=None, primary_key=True)
    name: str
    role: UserRole = Field(default=UserRole.AUTHOR)
    created_by: str = Field(
        default=None, sa_column=Column(String, server_default="ia_gen_user")
    )
    updated_by: str = Field(
        default=None, sa_column=Column(String, onupdate="ia_gen_user")
    )
    created_at: datetime = Field(
        default_factory=datetime.now,
        sa_column=Column(DateTime, server_default=func.now()),
    )
    updated_at: datetime = Field(
        default_factory=datetime.now, sa_column=Column(DateTime, onupdate=func.now())
    )

    coordinated_orders: List["Order"] = Relationship(
        back_populates="coordinators", link_model=OrderCoordinator
    )
    authored_orders: List["Order"] = Relationship(
        back_populates="authors", link_model=OrderAuthor
    )
    comments: List["Comment"] = Relationship(back_populates="user")


class Plan(SQLModel, table=True):
    id: int | None = Field(
        default=None,
        primary_key=True,
        index=True,
        sa_column_kwargs={"autoincrement": True},
    )
    year: int
    created_by: str = Field(
        default=None, sa_column=Column(String, server_default="ia_gen_user")
    )
    updated_by: str = Field(
        default=None, sa_column=Column(String, onupdate="ia_gen_user")
    )
    created_at: datetime = Field(
        default_factory=datetime.now,
        sa_column=Column(DateTime, server_default=func.now()),
    )
    updated_at: datetime = Field(
        default_factory=datetime.now, sa_column=Column(DateTime, onupdate=func.now())
    )


class Title(SQLModel, table=True):
    id: int | None = Field(
        default=None,
        primary_key=True,
        index=True,
        sa_column_kwargs={"autoincrement": True},
    )
    type: TitleType = Field(default=TitleType.GRADO, nullable=True)
    name: str = Field(default=None, index=True)
    description: Optional[str] = Field(default=None)
    plan_id: int | None = Field(default=None)
    created_by: str = Field(
        default=None, sa_column=Column(String, server_default="ia_gen_user")
    )
    updated_by: str = Field(
        default=None, sa_column=Column(String, onupdate="ia_gen_user")
    )
    created_at: datetime = Field(
        default_factory=datetime.now,
        sa_column=Column(DateTime, server_default=func.now()),
    )
    updated_at: datetime = Field(
        default_factory=datetime.now, sa_column=Column(DateTime, onupdate=func.now())
    )

    title_subjects: List["TitleSubject"] = Relationship(
        back_populates="title", sa_relationship_kwargs={"cascade": CASCADE_RULE}
    )
    __table_args__ = (
        UniqueConstraint(
            "name", "description", "type", name="uix_title_name_type_description"
        ),
    )


class Subject(SQLModel, table=True):
    id: int | None = Field(
        default=None,
        primary_key=True,
        index=True,
        sa_column_kwargs={"autoincrement": True},
    )
    name: str = Field(default=None, index=True)
    description: Optional[str] = Field(default=None)
    credits: int = Field(default=60)
    created_by: str = Field(
        default=None, sa_column=Column(String, server_default="ia_gen_user")
    )
    updated_by: str = Field(
        default=None, sa_column=Column(String, onupdate="ia_gen_user")
    )
    created_at: datetime = Field(
        default_factory=datetime.now,
        sa_column=Column(DateTime, server_default=func.now()),
    )
    updated_at: datetime = Field(
        default_factory=datetime.now, sa_column=Column(DateTime, onupdate=func.now())
    )

    title_subjects: List["TitleSubject"] = Relationship(
        back_populates="subject",
        sa_relationship_kwargs={"cascade": CASCADE_RULE},
    )
    __table_args__ = (
        UniqueConstraint("name", "description", name="uix_subject_name_description"),
    )


class TitleSubject(SQLModel, table=True):
    id: int = Field(
        default=None,
        primary_key=True,
        index=True,
        sa_column_kwargs={"autoincrement": True},
    )
    name: str = Field(default=None)
    title_id: int = Field(
        sa_column=Column(
            ForeignKey("title.id", ondelete="CASCADE", onupdate="CASCADE"),
            index=True,
            nullable=False,
        )
    )
    subject_id: int = Field(
        sa_column=Column(
            ForeignKey("subject.id", ondelete="CASCADE", onupdate="CASCADE"),
            nullable=False,
            index=True,
        )
    )
    term_number: int = Field(default=1)
    year_number: int = Field(default=1)
    created_by: str = Field(
        default=None, sa_column=Column(String, server_default="ia_gen_user")
    )
    updated_by: str = Field(
        default=None, sa_column=Column(String, onupdate="ia_gen_user")
    )
    created_at: datetime = Field(
        default_factory=datetime.now,
        sa_column=Column(DateTime, server_default=func.now()),
    )
    updated_at: datetime = Field(
        default_factory=datetime.now, sa_column=Column(DateTime, onupdate=func.now())
    )

    title: Title = Relationship(back_populates="title_subjects")
    subject: Subject = Relationship(back_populates="title_subjects")
    orders: List["Order"] = Relationship(
        back_populates="title_subject",
        sa_relationship_kwargs={"cascade": CASCADE_RULE},
    )


class Order(SQLModel, table=True):
    id: int = Field(
        default=None,
        primary_key=True,
        index=True,
        sa_column_kwargs={"autoincrement": True},
    )
    order_date: datetime = Field(default_factory=datetime.now)
    id_agea: str | None = Field(default=None, index=True)
    title_subject_id: int = Field(
        sa_column=Column(
            ForeignKey("titlesubject.id", ondelete="CASCADE", onupdate="CASCADE"),
            nullable=False,
        )
    )
    created_by: str = Field(
        default=None, sa_column=Column(String, server_default="ia_gen_user")
    )
    updated_by: str = Field(
        default=None, sa_column=Column(String, onupdate="ia_gen_user")
    )
    created_at: datetime = Field(
        default_factory=datetime.now,
        sa_column=Column(DateTime, server_default=func.now()),
    )
    updated_at: datetime = Field(
        default_factory=datetime.now, sa_column=Column(DateTime, onupdate=func.now())
    )

    title_subject: TitleSubject = Relationship(back_populates="orders")
    coordinators: List["User"] = Relationship(
        back_populates="coordinated_orders", link_model=OrderCoordinator
    )
    authors: List["User"] = Relationship(
        back_populates="authored_orders", link_model=OrderAuthor
    )
    indices: List["Indice"] = Relationship(
        back_populates="order", sa_relationship_kwargs={"cascade": CASCADE_RULE}
    )
    comments: List["Comment"] = Relationship(back_populates="order")
    __table_args__ = (UniqueConstraint("id_agea", name="unique_order_id_agea"),)


class Indice(SQLModel, table=True):
    id: Optional[int] = Field(
        default=None,
        primary_key=True,
        index=True,
        sa_column_kwargs={"autoincrement": True},
    )
    status: IndiceStatus = Field(default=IndiceStatus.NOT_STARTED, index=True)
    order_id: int = Field(
        sa_column=Column(
            ForeignKey(ORDER_ID, ondelete="CASCADE", onupdate="CASCADE"),
            nullable=False,
            index=True,
        )
    )
    version: int
    is_displayed: bool = Field(default=False)
    created_by: str = Field(
        default=None, sa_column=Column(String, server_default="ia_gen_user")
    )
    updated_by: str = Field(
        default=None, sa_column=Column(String, onupdate="ia_gen_user")
    )
    created_at: datetime = Field(
        default_factory=datetime.now,
        sa_column=Column(DateTime, server_default=func.now()),
    )
    updated_at: datetime = Field(
        default_factory=datetime.now, sa_column=Column(DateTime, onupdate=func.now())
    )

    ai_processes: List["AIProcess"] = Relationship(
        back_populates="indice",
        sa_relationship_kwargs={"cascade": CASCADE_RULE},
    )
    bloques: List["Bloque"] = Relationship(back_populates="indice")
    competencies: List["CompetenciesIndice"] = Relationship(
        back_populates="indice",
        sa_relationship_kwargs={"cascade": CASCADE_RULE},
    )
    order: Order = Relationship(back_populates="indices")

    __table_args__ = (
        UniqueConstraint("order_id", "version", name="unique_order_version"),
    )


class Bloque(SQLModel, table=True):
    id: int | None = Field(
        default=None,
        primary_key=True,
        index=True,
        sa_column_kwargs={"autoincrement": True},
    )
    name: str = Field(index=True)
    position: int
    indice_id: int = Field(
        sa_column=Column(
            ForeignKey(INDICE_ID, ondelete="CASCADE", onupdate="CASCADE"),
            nullable=False,
            index=True,
        )
    )
    created_by: str = Field(
        default=None, sa_column=Column(String, server_default="ia_gen_user")
    )
    updated_by: str = Field(
        default=None, sa_column=Column(String, onupdate="ia_gen_user")
    )
    created_at: datetime = Field(
        default_factory=datetime.now,
        sa_column=Column(DateTime, server_default=func.now()),
    )
    updated_at: datetime = Field(
        default_factory=datetime.now, sa_column=Column(DateTime, onupdate=func.now())
    )

    indice: "Indice" = Relationship(back_populates="bloques")
    temas: List["Tema"] = Relationship(
        back_populates="bloque",
        sa_relationship_kwargs={"cascade": CASCADE_RULE},
    )
    comments: List["Comment"] = Relationship(back_populates="bloque")


class Tema(SQLModel, table=True):
    id: int | None = Field(
        default=None,
        primary_key=True,
        index=True,
        sa_column_kwargs={"autoincrement": True},
    )
    name: str = Field(index=True)
    status: TopicStatus = Field(index=True, default=TopicStatus.NOT_STARTED)
    position: int
    id_bloque: int | None = Field(
        default=None,
        sa_column=Column(
            ForeignKey("bloque.id", ondelete="CASCADE", onupdate="CASCADE"),
            index=True,
            nullable=False,
        ),
    )
    memories: list[dict[str, Any]] = Field(
        default_factory=list,
        sa_column=Column(JSONB, server_default="[]", nullable=False),
    )
    created_by: str = Field(
        default=None, sa_column=Column(String, server_default="ia_gen_user")
    )
    updated_by: str = Field(
        default=None, sa_column=Column(String, onupdate="ia_gen_user")
    )
    created_at: datetime = Field(
        default_factory=datetime.now,
        sa_column=Column(DateTime, server_default=func.now()),
    )
    updated_at: datetime = Field(
        default_factory=datetime.now, sa_column=Column(DateTime, onupdate=func.now())
    )

    bloque: "Bloque" = Relationship(back_populates="temas")
    comments: List["Comment"] = Relationship(back_populates="topic")
    epigrafes: List["Epigrafe"] = Relationship(
        back_populates="tema", sa_relationship_kwargs={"cascade": CASCADE_RULE}
    )
    in_depth_references: List["InDepthReference"] = Relationship(
        back_populates="tema", sa_relationship_kwargs={"cascade": CASCADE_RULE}
    )


class EpigrafeDocumento(SQLModel, table=True):
    id_epigrafe: int = Field(
        sa_column=Column(ForeignKey(EPIGRAFE_ID, ondelete="CASCADE"), primary_key=True)
    )
    id_documento: uuid.UUID = Field(
        sa_column=Column(ForeignKey("doc.id", ondelete="CASCADE"), primary_key=True)
    )
    created_by: str = Field(
        default=None, sa_column=Column(String, server_default="ia_gen_user")
    )
    updated_by: str = Field(
        default=None, sa_column=Column(String, onupdate="ia_gen_user")
    )
    created_at: datetime = Field(
        default_factory=datetime.now,
        sa_column=Column(DateTime, server_default=func.now()),
    )
    updated_at: datetime = Field(
        default_factory=datetime.now, sa_column=Column(DateTime, onupdate=func.now())
    )


class Epigrafe(SQLModel, table=True):
    id: int | None = Field(
        default=None,
        primary_key=True,
        index=True,
        sa_column_kwargs={"autoincrement": True},
    )
    name: str = Field(index=True)
    position: int
    id_tema: int = Field(
        sa_column=Column(
            ForeignKey(TEMA_ID, ondelete="CASCADE", onupdate="CASCADE"),
            index=True,
            nullable=False,
        )
    )
    keywords: List[str] | None = Field(default=[], sa_column=Column(JSONB()))
    summary: str | None = Field(default=None)
    created_by: str = Field(
        default=None, sa_column=Column(String, server_default="ia_gen_user")
    )
    updated_by: str = Field(
        default=None, sa_column=Column(String, onupdate="ia_gen_user")
    )
    created_at: datetime = Field(
        default_factory=datetime.now,
        sa_column=Column(DateTime, server_default=func.now()),
    )
    updated_at: datetime = Field(
        default_factory=datetime.now, sa_column=Column(DateTime, onupdate=func.now())
    )

    tema: "Tema" = Relationship(back_populates="epigrafes")
    content_plans: List["ContentPlan"] = Relationship(
        back_populates="epigrafe",
        sa_relationship_kwargs={"cascade": CASCADE_RULE},
    )
    documents: List["Doc"] = Relationship(
        back_populates="epigrafes",
        link_model=EpigrafeDocumento,
        sa_relationship_kwargs={"cascade": "delete, all"},
    )
    comments: List["Comment"] = Relationship(back_populates="epigraph")


class Competencies(SQLModel, table=True):
    id: int = Field(
        default=None,
        primary_key=True,
        index=True,
        sa_column_kwargs={"autoincrement": True},
    )
    description: str = Field(unique=True)
    created_by: str = Field(
        default=None, sa_column=Column(String, server_default="ia_gen_user")
    )
    updated_by: str = Field(
        default=None, sa_column=Column(String, onupdate="ia_gen_user")
    )
    created_at: datetime = Field(
        default_factory=datetime.now,
        sa_column=Column(DateTime, server_default=func.now()),
    )
    updated_at: datetime = Field(
        default_factory=datetime.now, sa_column=Column(DateTime, onupdate=func.now())
    )

    indices: List["CompetenciesIndice"] = Relationship(back_populates="competencie")


class CompetenciesIndice(SQLModel, table=True):
    indice_id: int = Field(
        sa_column=Column(
            ForeignKey(INDICE_ID, ondelete="CASCADE", onupdate="CASCADE"),
            primary_key=True,
            index=True,
        )
    )
    competencie_id: int = Field(
        sa_column=Column(
            ForeignKey("competencies.id", ondelete="CASCADE", onupdate="CASCADE"),
            primary_key=True,
            index=True,
        )
    )
    created_by: str = Field(
        default=None, sa_column=Column(String, server_default="ia_gen_user")
    )
    updated_by: str = Field(
        default=None, sa_column=Column(String, onupdate="ia_gen_user")
    )
    created_at: datetime = Field(
        default_factory=datetime.now,
        sa_column=Column(DateTime, server_default=func.now()),
    )
    updated_at: datetime = Field(
        default_factory=datetime.now, sa_column=Column(DateTime, onupdate=func.now())
    )

    competencie: Optional[Competencies] = Relationship(back_populates="indices")
    indice: Optional[Indice] = Relationship(back_populates="competencies")


class AIProcess(SQLModel, table=True):
    id: int = Field(default=None, primary_key=True)
    process_type: AIProcessType
    indice_id: Optional[int] = Field(
        default=None,
        sa_column=Column(ForeignKey(INDICE_ID, ondelete="CASCADE"), index=True),
    )
    tema_id: Optional[int] = Field(
        default=None,
        sa_column=Column(ForeignKey(TEMA_ID, ondelete="CASCADE"), index=True),
    )
    epigrafe_id: Optional[int] = Field(
        default=None,
        sa_column=Column(ForeignKey(EPIGRAFE_ID, ondelete="CASCADE"), index=True),
    )
    parent_id: Optional[int] = Field(
        default=None,
        sa_column=Column(ForeignKey(AI_PROCESS_ID, ondelete="CASCADE"), index=True),
    )
    status: AIProcessStatus = Field(default=AIProcessStatus.PENDING)
    additional_metadata: Dict[Any, Any] = Field(
        default={}, sa_column=Column(JSONB)
    )  # This could be use for additional metadata like plan version for generated content. If we only have one version per topic or index, we won't have to use this a lot. Is something we should decide.
    started_at: Optional[datetime] = Field(default=None)
    completed_at: Optional[datetime] = Field(default=None)
    created_by: str = Field(
        default=None, sa_column=Column(String, server_default="ia_gen_user")
    )
    updated_by: str = Field(
        default=None, sa_column=Column(String, onupdate="ia_gen_user")
    )
    created_at: datetime = Field(
        default_factory=datetime.now,
        sa_column=Column(DateTime, server_default=func.now()),
    )
    updated_at: datetime = Field(
        default_factory=datetime.now, sa_column=Column(DateTime, onupdate=func.now())
    )

    indice: Optional["Indice"] = Relationship(back_populates="ai_processes")
    executions: List["AIExecution"] = Relationship(
        back_populates="ai_process",
        sa_relationship_kwargs={"cascade": CASCADE_RULE},
    )
    ratings: List["AIProcessRating"] = Relationship(
        back_populates="ai_process",
        sa_relationship_kwargs={"cascade": CASCADE_RULE},
    )
    parent: Optional["AIProcess"] = Relationship(
        back_populates="children",
        sa_relationship_kwargs={"remote_side": "AIProcess.id"},
    )
    children: List["AIProcess"] = Relationship(
        back_populates="parent",
        sa_relationship_kwargs={"cascade": CASCADE_RULE},
    )
    # Ignore for now, need to have clear if for all the cases it needs to be unique.


# TODO: This has to be optimized in some way, more values cna be normalized, for example making a schema for metadata and think about how could it be normalized. And if it will be used for write at all or only read.
class AIExecution(SQLModel, table=True):
    id: Optional[int] = Field(default=None, primary_key=True)
    ai_process_id: int = Field(
        sa_column=Column(ForeignKey(AI_PROCESS_ID, ondelete="CASCADE"))
    )
    input_data: dict[Any, Any] = Field(default={}, sa_column=Column(JSONB))
    output_data: dict[Any, Any] = Field(default={}, sa_column=Column(JSONB))
    execution_metadata: dict[Any, Any] = Field(default={}, sa_column=Column(JSONB))
    status: AIProcessStatus = Field(default=AIProcessStatus.PENDING)
    is_retry: bool = Field(default=False)
    error_message: Optional[str] = Field(default=None)
    prompt_id: Optional[int] = Field(default=None, foreign_key="prompt.id", index=True)
    created_by: str = Field(
        default=None, sa_column=Column(String, server_default="ia_gen_user")
    )
    updated_by: str = Field(
        default=None, sa_column=Column(String, onupdate="ia_gen_user")
    )
    created_at: datetime = Field(
        default_factory=datetime.now,
        sa_column=Column(DateTime, server_default=func.now()),
    )
    updated_at: datetime = Field(
        default_factory=datetime.now, sa_column=Column(DateTime, onupdate=func.now())
    )

    ai_process: AIProcess = Relationship(back_populates="executions")
    # prompt: Optional["Prompts"] = Relationship(back_populates="ai_executions")
    # TODO: This can be the results that you can decide to save or not. Decide if do it by module basis. (Ex. Create schema result. Or edit schema result. Add additional info of the prompts and processes used to reach that result in the jsonb.)


class AIProcessRating(SQLModel, table=True):
    id: Optional[int] = Field(default=None, primary_key=True)
    ai_process_id: int = Field(
        sa_column=Column(
            ForeignKey(AI_PROCESS_ID, ondelete="CASCADE", onupdate="CASCADE")
        )
    )
    rating: int = Field(..., ge=1, le=5)
    comment: Optional[str] = Field(default=None)
    evaluator_name: Optional[str] = Field(default=None)
    created_by: str = Field(
        default=None, sa_column=Column(String, server_default="ia_gen_user")
    )
    updated_by: str = Field(
        default=None, sa_column=Column(String, onupdate="ia_gen_user")
    )
    created_at: datetime = Field(
        default_factory=datetime.now,
        sa_column=Column(DateTime, server_default=func.now()),
    )
    updated_at: datetime = Field(
        default_factory=datetime.now, sa_column=Column(DateTime, onupdate=func.now())
    )

    ai_process: "AIProcess" = Relationship(back_populates="ratings")

    __table_args__ = (
        UniqueConstraint(
            "evaluator_name", "ai_process_id", name="unique_evaluator_ai_process"
        ),
    )


class ContentPlan(SQLModel, table=True):  # Plan for the content of epigrafe
    id: Optional[int] = Field(default=None, primary_key=True)
    epigrafe_id: int = Field(
        sa_column=Column(ForeignKey(EPIGRAFE_ID, ondelete="CASCADE"))
    )
    version: int
    didactic_instructions: str | None = Field(default=None)
    created_by: str = Field(
        default=None, sa_column=Column(String, server_default="ia_gen_user")
    )
    updated_by: str = Field(
        default=None, sa_column=Column(String, onupdate="ia_gen_user")
    )
    created_at: datetime = Field(
        default_factory=datetime.now,
        sa_column=Column(DateTime, server_default=func.now()),
    )
    updated_at: datetime = Field(
        default_factory=datetime.now, sa_column=Column(DateTime, onupdate=func.now())
    )

    epigrafe: "Epigrafe" = Relationship(back_populates="content_plans")
    items: List["ContentPlanItem"] = Relationship(
        back_populates="content_plan",
        sa_relationship_kwargs={"cascade": CASCADE_RULE},
    )

    __table_args__ = (
        UniqueConstraint(
            "epigrafe_id", "version", name="uix_contentplan_epigrafe_version"
        ),
    )


class ContentPlanItem(
    SQLModel, table=True
):  # Detailed paragraphs of a given epigrafe.Rename to paragraph.
    id: Optional[int] = Field(default=None, primary_key=True)
    content_plan_id: int = Field(
        sa_column=Column(ForeignKey("contentplan.id", ondelete="CASCADE"))
    )
    plan: str | None = None
    descripcion: str | None = None
    herramienta: ContentPlanType  # TODO: rename to paragraph_type or something like that and specify in other way who edited it.
    position: int
    is_deleted: bool = Field(default=False)
    created_by: str = Field(
        default=None, sa_column=Column(String, server_default="ia_gen_user")
    )
    updated_by: str = Field(
        default=None, sa_column=Column(String, onupdate="ia_gen_user")
    )
    created_at: datetime = Field(
        default_factory=datetime.now,
        sa_column=Column(DateTime, server_default=func.now()),
    )
    updated_at: datetime = Field(
        default_factory=datetime.now, sa_column=Column(DateTime, onupdate=func.now())
    )

    content_plan: ContentPlan = Relationship(back_populates="items")
    contenido_generado: "ContenidoGenerado" = Relationship(
        back_populates="item_plan_contenido"
    )


class Doc(SQLModel, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True, index=True)
    name: Optional[str] = Field(default=None, index=True)
    description: Optional[str] = Field(default=None)
    content: str
    summary: Optional[str] = Field(default=None)
    id_fuente_documento: int = Field(foreign_key="documentsource.id")
    token_count: int
    file_type: TipoArchivo = Field(default=TipoArchivo.PDF)

    authors: List[str] = Field(default=[], sa_column=Column(JSONB))
    publication_date: Optional[int] = Field(default=None)
    document_url: Optional[str] = Field(default=None)
    final_citation: Optional[str] = Field(default=None)
    inline_citation: Optional[str] = Field(default=None)
    quality_score: Optional[float] = Field(default=None)
    freshness_score: Optional[float] = Field(default=None)
    relevance_score: dict[str, float] = Field(default={}, sa_column=Column(JSONB))
    research_source_citation_count: Optional[int] = Field(default=None)
    research_source_publication_year: Optional[int] = Field(default=None)
    research_source_title: Optional[str] = Field(default=None)
    research_source_authors: List[dict[str, Any]] = Field(
        default=[], sa_column=Column(JSONB)
    )
    status: DocStatus = Field(default=DocStatus.PENDING)

    path_blob: Optional[str] = Field(default=None, index=True)
    lenguage: Lenguaje = Field(default=Lenguaje.EN)
    toc: List[dict[str, Any]] | None = Field(default=None, sa_column=Column(JSONB))
    additional_metadata: dict[str, Any] = Field(default={}, sa_column=Column(JSONB))

    created_by: str = Field(
        default=None, sa_column=Column(String, server_default="ia_gen_user")
    )
    updated_by: str = Field(
        default=None, sa_column=Column(String, onupdate="ia_gen_user")
    )
    created_at: datetime = Field(
        default_factory=datetime.now,
        sa_column=Column(DateTime, server_default=func.now()),
    )
    updated_at: datetime = Field(
        default_factory=datetime.now, sa_column=Column(DateTime, onupdate=func.now())
    )

    epigrafes: List["Epigrafe"] = Relationship(
        back_populates="documents",
        link_model=EpigrafeDocumento,
        sa_relationship_kwargs={
            "cascade": "all, delete",
        },
    )
    chunks: List["ChunkDocumento"] = Relationship(
        back_populates="document",
        sa_relationship_kwargs={"cascade": CASCADE_RULE},
    )
    document_source: "DocumentSource" = Relationship(back_populates="documents")
    # categoria_conocimiento: -> Por determinar si clasificamos por de que trata


# Por definir tabla de metadatodocumento donde podamos definir informacion potencialmente repetida mejorx.


class DocumentSource(SQLModel, table=True):
    id: int | None = Field(default=None, primary_key=True)
    name: str = Field(unique=True)
    created_by: str = Field(
        default=None, sa_column=Column(String, server_default="ia_gen_user")
    )
    updated_by: str = Field(
        default=None, sa_column=Column(String, onupdate="ia_gen_user")
    )
    created_at: datetime = Field(
        default_factory=datetime.now,
        sa_column=Column(DateTime, server_default=func.now()),
    )
    updated_at: datetime = Field(
        default_factory=datetime.now, sa_column=Column(DateTime, onupdate=func.now())
    )

    documents: List[Doc] = Relationship(back_populates="document_source")


class ChunkDocumento(SQLModel, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True, index=True)
    doc_id: uuid.UUID = Field(
        sa_column=Column(
            ForeignKey("doc.id", ondelete="CASCADE", onupdate="CASCADE"), index=True
        )
    )
    tokens: int
    text: str
    additional_metadata: dict[str, Any] = Field(default={}, sa_column=Column(JSONB))
    embedding: list[float] = Field(sa_column=Column(Vector))
    # Por meter relaciones como chunk anterior, chunk posterior, chunk padre y niveles.
    embedding_model: EmbeddingModel
    vector_dimensions: int
    llm_included_metadata_keys: List[str] = Field(default=[], sa_column=Column(JSONB))
    embedding_included_metadata_keys: List[str] = Field(
        default=[], sa_column=Column(JSONB)
    )
    created_by: str = Field(
        default=None, sa_column=Column(String, server_default="ia_gen_user")
    )
    updated_by: str = Field(
        default=None, sa_column=Column(String, onupdate="ia_gen_user")
    )
    created_at: datetime = Field(
        default_factory=datetime.now,
        sa_column=Column(DateTime, server_default=func.now()),
    )
    updated_at: datetime = Field(
        default_factory=datetime.now, sa_column=Column(DateTime, onupdate=func.now())
    )

    document: Doc = Relationship(back_populates="chunks")


class ContenidoGenerado(SQLModel, table=True):
    # En metadato añadir el caso donde pueda incluir imágenes ver cómo podría tratarse, de momento simplificando -> unicamente texto y otros cuando el plan incluya otro tipo de datos.
    id: int | None = Field(default=None, primary_key=True, index=True)
    contenido: str
    id_item_plan_contenido: int = Field(
        sa_column=Column(
            ForeignKey("contentplanitem.id", ondelete="CASCADE"), unique=True
        )
    )
    documentos_relacionados: List[str] = Field(default=[], sa_column=Column(JSONB))
    chunks_relacionados: List[str] = Field(default=[], sa_column=Column(JSONB))
    metadatos: dict[Any, Any] = Field(default={}, sa_column=Column(JSONB))
    created_by: str = Field(
        default=None, sa_column=Column(String, server_default="ia_gen_user")
    )
    updated_by: str = Field(
        default=None, sa_column=Column(String, onupdate="ia_gen_user")
    )
    created_at: datetime = Field(
        default_factory=datetime.now,
        sa_column=Column(DateTime, server_default=func.now()),
    )
    updated_at: datetime = Field(
        default_factory=datetime.now, sa_column=Column(DateTime, onupdate=func.now())
    )

    item_plan_contenido: ContentPlanItem = Relationship(
        back_populates="contenido_generado"
    )
    comments: list["Comment"] = Relationship(back_populates="paragraph")


class InDepthReference(SQLModel, table=True):
    __tablename__ = "in_depth_reference"
    id: int | None = Field(
        default=None,
        primary_key=True,
        index=True,
        sa_column_kwargs={"autoincrement": True},
    )
    title: str
    cite: str
    url: str
    justification: str
    topic_id: int = Field(
        sa_column=Column(
            ForeignKey(TEMA_ID, ondelete="CASCADE", onupdate="CASCADE"),
            index=True,
            nullable=False,
        )
    )

    created_by: str = Field(
        default=None, sa_column=Column(String, server_default="ia_gen_user")
    )
    updated_by: str = Field(
        default=None, sa_column=Column(String, onupdate="ia_gen_user")
    )
    created_at: datetime = Field(
        default_factory=datetime.now,
        sa_column=Column(DateTime, server_default=func.now()),
    )
    updated_at: datetime = Field(
        default_factory=datetime.now, sa_column=Column(DateTime, onupdate=func.now())
    )

    tema: "Tema" = Relationship(back_populates="in_depth_references")
    comments: List["Comment"] = Relationship(back_populates="in_depth")


class Comment(SQLModel, table=True):
    id: int | None = Field(
        default=None, primary_key=True, sa_column_kwargs={"autoincrement": True}
    )
    comment: str = Field(max_length=2056)
    comment_parent_id: Optional[int] = Field(
        default=None,
        sa_column=Column(ForeignKey("comment.id", ondelete="CASCADE"), index=True),
    )
    user_id: str = Field(max_length=255, foreign_key=USER_ID)
    order_id: int = Field(foreign_key=ORDER_ID)
    block_id: int | None = Field(default=None, foreign_key="bloque.id")
    topic_id: Optional[int] = Field(default=None, foreign_key=TEMA_ID)
    epigraph_id: int | None = Field(default=None, foreign_key=EPIGRAFE_ID)
    paragraph_id: int | None = Field(default=None, foreign_key="contenidogenerado.id")
    in_depth_id: int | None = Field(default=None, foreign_key="in_depth_reference.id")
    test_id: int | None = Field(default=None, foreign_key="question.question_id")
    related_section: RelatedSection
    viewed: bool = Field(default=False)
    completed: bool = Field(default=False)
    created_by: str | None = Field(default=None, max_length=255)
    updated_by: str | None = Field(default=None, max_length=255)
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime | None = Field(default=None)

    parent: Optional["Comment"] = Relationship(
        back_populates="replies", sa_relationship_kwargs={"remote_side": "Comment.id"}
    )
    replies: list["Comment"] = Relationship(back_populates="parent")
    user: "User" = Relationship(back_populates="comments")
    order: "Order" = Relationship(back_populates="comments")
    bloque: Optional["Bloque"] = Relationship(back_populates="comments")
    topic: Optional["Tema"] = Relationship(back_populates="comments")
    epigraph: Optional["Epigrafe"] = Relationship(back_populates="comments")
    paragraph: Optional["ContenidoGenerado"] = Relationship(back_populates="comments")
    in_depth: Optional["InDepthReference"] = Relationship(back_populates="comments")


class Question(SQLModel, table=True):
    question_id: int | None = Field(default=None, primary_key=True)
    topic_id: int = Field(foreign_key=TEMA_ID)
    question_type: str = Field(default="MULTIPLE_CHOICE", max_length=24)
    wording: str = Field(max_length=2048)
    option_a: str = Field(max_length=2048)
    option_b: str = Field(max_length=2048)
    option_c: str | None = Field(default=None, max_length=2048)
    option_d: str | None = Field(default=None, max_length=2048)
    correct_option: str = Field(max_length=16)
    reasoning: str | None = Field(default=None, max_length=2048)
    is_validated: bool = Field(default=False)
    is_exam_ready: bool = Field(default=False)
    is_from_old_exam: bool = Field(default=False)
    created_by: str = Field(
        default=None, sa_column=Column(String, server_default="ia_gen_user")
    )
    updated_by: str = Field(
        default=None, sa_column=Column(String, onupdate="ia_gen_user")
    )
    created_at: datetime = Field(
        default_factory=datetime.now,
        sa_column=Column(DateTime, server_default=func.now()),
    )
    updated_at: datetime = Field(
        default_factory=datetime.now, sa_column=Column(DateTime, onupdate=func.now())
    )
