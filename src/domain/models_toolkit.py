from datetime import datetime
from enum import Enum

from sqlalchemy import Column, DateTime, String, func
from sqlalchemy.dialects.postgresql import JSONB
from sqlmodel import Field, MetaData, SQLModel

# Unificamos en un solo MetaData (sin schema por defecto) para que SQLAlchemy pueda resolver FKs entre esquemas
metadata = MetaData()


class Activities(str, Enum):
    AGC = "AGC"
    RUBRICAS = "Rúbricas"
    GENERADOR_ACTIVIDADES = "Generador de Actividades"
    ACTUALIZADOR_CONTENIDOS = "Actualizador de Contenidos"
    GENERADOR_ACTIVIDADES_UNIPRO = "Generador Actividades Unipro"
    GENERADOR_GUIONES_TUTORIAS_UNIPRO = "Generador Guiones Tutorias Unipro"
    GENERADOR_TESTS_VALIDACION_UNIPRO = "Generador Tests de Validacion Unipro"


class KnowledgeAreaInfo(SQLModel, table=True):
    __tablename__ = "knowledge_area_info"
    metadata = metadata
    __table_args__ = {"schema": "toolkit"}
    id: int | None = Field(default=None, primary_key=True)
    name: str | None = Field(default=None, sa_column=Column(String, nullable=True))
    application_id: int | None = Field(
        default=None, foreign_key="admin.admin_Application.id_application"
    )
    description: str | None = Field(
        default=None, sa_column=Column(String, nullable=True)
    )
    system_prompt: str | None = Field(
        default=None, sa_column=Column(String, nullable=True)
    )
    user_prompt: str | None = Field(
        default=None, sa_column=Column(String, nullable=True)
    )
    variables: list[dict[str, str]] = Field(
        default_factory=list,
        sa_column=Column(JSONB, server_default="[]", nullable=False),
    )

    created_by: str = Field(
        default="ia_gen_user",
        sa_column=Column(String(255), server_default="ia_gen_user"),
    )
    updated_by: str | None = Field(
        default=None, sa_column=Column(String(255), nullable=True)
    )
    created_at: datetime = Field(
        default_factory=datetime.now,
        sa_column=Column(DateTime, server_default=func.now(), nullable=False),
    )
    updated_at: datetime = Field(
        default=None, sa_column=Column(DateTime, nullable=True, onupdate=func.now())
    )


class Application(SQLModel, table=True):
    __tablename__ = "admin_application"
    metadata = metadata
    __table_args__ = {"schema": "admin"}
    id_application: int | None = Field(default=None, primary_key=True)
    name_application: str = Field(sa_column=Column(String, nullable=False))
    description: str | None = Field(
        default=None, sa_column=Column(String, nullable=True)
    )
    created_by: str = Field(
        default="ia_gen_user",
        sa_column=Column(String(255), server_default="ia_gen_user"),
    )
    updated_by: str | None = Field(
        default=None, sa_column=Column(String(255), nullable=True)
    )
    created_at: datetime = Field(
        default_factory=datetime.now,
        sa_column=Column(DateTime, server_default=func.now(), nullable=False),
    )
    updated_at: datetime = Field(
        default=None, sa_column=Column(DateTime, nullable=True, onupdate=func.now())
    )
