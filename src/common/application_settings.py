import os
from typing import Optional

from azure.identity import DefaultAzureCredential
from pydantic.alias_generators import to_pascal
from pydantic_settings import (
    AzureKeyVaultSettingsSource,
    BaseSettings,
    DotEnvSettingsSource,
    EnvSettingsSource,
    PydanticBaseSettingsSource,
    SettingsConfigDict,
)
from src.common.application_environment import ApplicationEnvironment


class ApplicationSettings(BaseSettings):
    # General Settings
    COMMON_ENVIRONMENT: str | None = "Local"
    LOGGING_LEVEL: str = "INFO"
    WORKERS: int = 1
    PHOENIX_PORT: Optional[int] = 6006
    FASTAPI_API_KEY: str

    # OpenAI and AI Services
    OPENAI_API_KEY: str
    ANTHROPIC_API_KEY: str
    AZURE_OPENAI_ENDPOINT: str | None = None
    AZURE_OPENAI_API_KEY: str | None = None
    AZURE_API_VERSION: str | None = None
    GEN_AI_API_URL: str | None = None
    BASE_URL: str | None = None
    DEFAULT_HEADERS: dict | None = {"use-case-id": "contenidos"}
    ROUTER_API_KEY: str | None = None

    # Database Settings
    DB_PORT: int | None = 5432
    DB_USER: str
    DB_NAME: str
    DB_PASSWORD: str
    DB_HOST: str

    # Queue and Messaging
    QUEUE_MODE: str | None = "local"
    SERVICE_BUS_CONNECTION_STRING: str
    CONTENT_GENERATION_QUEUE_NAME: str
    PROCESS_DOCUMENT_QUEUE_NAME: str | None = None
    PROCESS_DOCUMENT_DIRECT_QUEUE_NAME: str | None = None
    GENERATE_CONTENT_TASK_N_CONCURRENT: int = 1
    GENERATE_DOCUMENT_TASK_N_CONCURRENT: int = 2

    # Storage
    AZURE_STORAGE_CONNECTION_STRING: str | None = None
    COOLDOWN_AZURE_STORAGE_CONNECTION_STRING: str | None = None
    SCAN_CONTAINER_NAME: str | None = None
    DOCUMENT_CONTAINER_NAME: str | None = None

    # Re-Ranking Services
    RERANK_AZURE_API_KEY: str | None = None
    RERANK_AZURE_NAME: str | None = None
    RERANK_AZURE_URL: str | None = None

    # Search Engines
    SEARCH_ENGINE: str | None = "jina"
    ACADEMIC_SEARCH_ENGINE: str | None = "jina"
    URL_EXTRACTOR: str | None = "jina"
    BRAVE_SEARCH_API_KEY: str | None = None
    TAVILY_SEARCH_API_KEY: str | None = None
    JINA_API_KEY: str | None = None

    # Phoenix Settings
    PHOENIX_PROJECT_NAME: str | None = None
    PHOENIX_COLLECTOR_ENDPOINT: str | None = None

    # Observability and Monitoring
    OTEL_EXPORTER_OTLP_HEADERS: str | None = None
    APPLICATIONINSIGHTS_CONNECTION_STRING: str

    # Azure Key Vault
    AZURE_KEY_VAULT_URL: str | None = None

    # Mermaid Validation
    MERMAID_BASE_URL: str = "https://mermaid.ink/img/"

    # Backend
    SEND_EMAIL_API_KEY: str
    MAIL_SEND_ENDPOINT: str

    model_config = SettingsConfigDict(
        alias_generator=to_pascal,
        env_file=[".env", f".env.{ApplicationEnvironment.get_current()}"],
        env_file_encoding="utf-8",
        extra="ignore",
    )

    @classmethod
    def settings_customise_sources(
        cls,
        settings_cls: type[BaseSettings],
        init_settings: PydanticBaseSettingsSource,  # noqa: ARG003
        env_settings: PydanticBaseSettingsSource,  # noqa: ARG003
        dotenv_settings: PydanticBaseSettingsSource,  # noqa: ARG003
        file_secret_settings: PydanticBaseSettingsSource,  # noqa: ARG003
    ) -> tuple[PydanticBaseSettingsSource, ...]:
        env = EnvSettingsSource(settings_cls)
        dotenv = DotEnvSettingsSource(
            settings_cls,
            env_file=[".env", f".env.{ApplicationEnvironment.get_current()}"],
            case_sensitive=True,
        )

        settings = (env, dotenv)

        # Disabled for now
        if ApplicationEnvironment.get_current() != ApplicationEnvironment.LOCAL:
            try:
                azure_key_vault = AzureKeyVaultSettingsSource(
                    settings_cls,
                    os.getenv("AzureKeyVaultUrl"),
                    DefaultAzureCredential(),
                )
                settings += (azure_key_vault,)
            except Exception:
                return (env, dotenv)
        return settings
