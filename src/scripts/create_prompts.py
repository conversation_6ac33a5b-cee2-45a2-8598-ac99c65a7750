# -*- coding: utf-8 -*-
# ---
# jupyter:
#   jupytext:
#     cell_metadata_filter: -all
#     custom_cell_magics: kql
#     text_representation:
#       extension: .py
#       format_name: percent
#       format_version: '1.3'
#       jupytext_version: 1.11.2
#   kernelspec:
#     display_name: venv
#     language: python
#     name: python3
# ---

# %%
# import sys
# from pathlib import Path

# from dotenv import load_dotenv

# # Detect if running as a script or in a notebook.
# # If __file__ is defined, it's likely a script run; if not, probably a notebook.
# if '__file__' in globals():
#     # Running as a script
#     script_dir = Path(__file__).resolve().parent
#     project_root = script_dir.parent
# else:
#     # Running in a notebook
#     project_root = Path.cwd().parent

# # Ensure project_root is in sys.path for imports
# if str(project_root) not in sys.path:
#     sys.path.append(str(project_root))


# load_dotenv()

# %%
# !pip install --index-url "https://${UV_INDEX_UNIR_PASSWORD}@pkgs.dev.azure.com/unirnet/_packaging/Proeduca/pypi/simple/" \
# --trusted-host pkgs.dev.azure.com \
# ia-gen-core==0.0.15

# %%
from ia_gen_core.prompts import (
    ChatPrompt,
    Example,
    Message,
    OutputFewShot,
    PromptManager,
    Role,
    TextPrompt,
)


def create_prompts(prompt_manager: PromptManager):
    # %%
    user_input = "{{user_input}}"
    # %% [markdown]
    # # Index

    # %% [markdown]
    # ## Create index

    # %%
    # Import these inside the function to avoid loading unnecessary modules at import time
    from src.api.workflows.indexes.generate_index.prompt_generate_index import (
        schema_from_competencies_template,
    )

    TextPrompt(
        name="generate-schema-samples-from-competencies",
        prompt=schema_from_competencies_template,
    ).save(prompt_manager)
    # %% [markdown]
    # ## Regenerate index
    # %%
    # Import content regenerator prompt components
    from src.api.common.services.content_regenerator.prompts_regenerar import (
        regenerate_index_schema_few_shot_input,
        regenerate_index_schema_few_shot_output,
        regenerate_index_schema_instructions,
        regenerate_index_schema_user,
    )

    ChatPrompt(
        name="regenerate-index-schema-instructions",
        prompt=[
            Message(role=Role.system, content=regenerate_index_schema_instructions),
            Message(role=Role.user, content=regenerate_index_schema_user),
        ],
        few_shot_examples=[
            Example(
                input=regenerate_index_schema_few_shot_input,
                output=regenerate_index_schema_few_shot_output,
            )
        ],
    ).save(prompt_manager)

    # %%
    # Import block schema regeneration components
    from src.api.common.services.content_regenerator.prompts_regenerar import (
        regenerate_block_schema_few_shot_input,
        regenerate_block_schema_few_shot_output,
        regenerate_block_schema_instructions,
        regenerate_block_schema_user,
    )

    ChatPrompt(
        name="regenerate-block-schema-instructions",
        prompt=[
            Message(role=Role.system, content=regenerate_block_schema_instructions),
            Message(role=Role.user, content=regenerate_block_schema_user),
        ],
        few_shot_examples=[
            Example(
                input=regenerate_block_schema_few_shot_input,
                output=regenerate_block_schema_few_shot_output,
            )
        ],
    ).save(prompt_manager)

    # %%
    # Import topic schema regeneration components
    from src.api.common.services.content_regenerator.prompts_regenerar import (
        regenerate_topic_schema_few_shot_input,
        regenerate_topic_schema_few_shot_output,
        regenerate_topic_schema_instructions,
        regenerate_topic_schema_user,
    )

    ChatPrompt(
        name="regenerate-topic-schema-instructions",
        prompt=[
            Message(role=Role.system, content=regenerate_topic_schema_instructions),
            Message(role=Role.user, content=regenerate_topic_schema_user),
        ],
        few_shot_examples=[
            Example(
                input=regenerate_topic_schema_few_shot_input,
                output=regenerate_topic_schema_few_shot_output,
            )
        ],
    ).save(prompt_manager)

    # %% [markdown]
    # # Competencies

    # %% [markdown]
    # ## Create Competencies

    # %%
    from src.api.workflows.competencies.generate.prompt_generate_competencies import (
        generate_competencies,
    )

    TextPrompt(name="generate-competencies", prompt=generate_competencies).save(
        prompt_manager
    )

    # %% [markdown]
    # ## Competencies From Title

    # %%
    from src.api.workflows.titles.generate.prompts_competencies_title import (
        input_few_shot_extract_descriptions,
        output_few_shot_extract_descriptions,
        system_competencies_from_title,
        system_extract_descriptions_title,
        user_competencies_from_title,
        user_extract_descriptions_title,
    )

    ChatPrompt(
        name="extract-descriptions-title",
        prompt=[
            Message(role=Role.system, content=system_extract_descriptions_title),
            Message(role=Role.system, content=user_extract_descriptions_title),
        ],
        few_shot_examples=[
            Example(
                input=input_few_shot_extract_descriptions,
                output=output_few_shot_extract_descriptions,
            )
        ],
    ).save(prompt_manager)

    ChatPrompt(
        name="competencies-from-title",
        prompt=[
            Message(role=Role.system, content=system_competencies_from_title),
            Message(role=Role.user, content=user_competencies_from_title),
        ],
    ).save(prompt_manager)

    # %% [markdown]
    # ## Regenerate Competencies

    # %%
    from src.api.common.services.content_regenerator.prompts_regenerar import (
        few_shot_input_regenerate_competencies,
        few_shot_output_regenerate_competencies,
        regenerate_competencies_system,
        regenerate_competencies_user,
    )

    ChatPrompt(
        name="regenerate-competencies-instructions",
        prompt=[
            Message(role=Role.system, content=regenerate_competencies_system),
            Message(role=Role.user, content=regenerate_competencies_user),
        ],
        few_shot_examples=[
            Example(
                input=few_shot_input_regenerate_competencies,
                output=few_shot_output_regenerate_competencies,
            )
        ],
    ).save(prompt_manager)

    # %% [markdown]
    # # Didactic Instructions and Plan

    # %% [markdown]
    # ## Create Didactic instructions

    # %%
    from src.api.workflows.topics.generate_didactic_instructions.prompts_didactic_instructions import (
        didactic_instructions_system_prompt,
        didactic_instructions_user_prompt,
    )

    ChatPrompt(
        name="didactic-instructions",
        prompt=[
            Message(role=Role.system, content=didactic_instructions_system_prompt),
            Message(role=Role.user, content=didactic_instructions_user_prompt),
        ],
    ).save(prompt_manager)

    # %% [markdown]
    # We can later add few shot examples.

    # %% [markdown]
    # ## Regenerate Didactic Instructions

    # %% [markdown]
    # ### Regenerate Topic Didactic Instructions

    # %%
    from src.api.common.services.content_regenerator.prompts_regenerar import (
        regenerate_topic_instructions_few_shot_input,
        regenerate_topic_instructions_few_shot_output,
        system_regenerate_didactic_instructions,
        user_regenerate_didactic_instructions_topic,
    )

    # %%
    ChatPrompt(
        name="regenerate-topic-didactic-instructions",
        prompt=[
            Message(role=Role.system, content=system_regenerate_didactic_instructions),
            Message(
                role=Role.user, content=user_regenerate_didactic_instructions_topic
            ),
        ],
        few_shot_examples=[
            Example(
                input=regenerate_topic_instructions_few_shot_input,
                output=regenerate_topic_instructions_few_shot_output,
            )
        ],
    ).save(prompt_manager)

    # %% [markdown]
    # ### Regenerate Epigraph Didactic Instructions

    # %%
    from src.api.common.services.content_regenerator.prompts_regenerar import (
        regenerate_epigraph_instructions_few_shot_input,
        regenerate_epigraph_instructions_few_shot_output,
        user_regenerate_didactic_instructions_epigraph,
    )

    ChatPrompt(
        name="regenerate-epigraph-didactic-instructions",
        prompt=[
            Message(role=Role.system, content=system_regenerate_didactic_instructions),
            Message(
                role=Role.user, content=user_regenerate_didactic_instructions_epigraph
            ),
        ],
        few_shot_examples=[
            Example(
                input=regenerate_epigraph_instructions_few_shot_input,
                output=regenerate_epigraph_instructions_few_shot_output,
            )
        ],
    ).save(prompt_manager)

    # %% [markdown]
    # ## Search references

    # %%
    from src.api.workflows.document_ingestion.search_references.search_prompts import (
        prompt_search_queries,
        prompt_search_queries_atomic,
        search_queries_input_few_shot_1,
        search_queries_input_few_shot_2,
        search_queries_output_few_shot_1,
        search_queries_output_few_shot_2,
    )

    references_few_shot_examples = [
        Example(
            input=search_queries_input_few_shot_1,
            output=search_queries_output_few_shot_1,
        ),
        Example(
            input=search_queries_input_few_shot_2,
            output=search_queries_output_few_shot_2,
        ),
    ]
    TextPrompt(
        name="search-queries",
        prompt=prompt_search_queries,
        few_shot_examples=references_few_shot_examples,
    ).save(prompt_manager)

    # %% [markdown]
    # ## Add paragraph

    # %%
    from src.api.workflows.sections.generate.add_paragraph_prompts import (
        herramienta_diagramas,
        herramienta_tablas,
        paragraph_system_prompt,
        paragraph_user_prompt,
    )

    few_shot_examples = [
        OutputFewShot(output=herramienta_tablas, metadata={"herramienta": "tablas"}),
        OutputFewShot(
            output=herramienta_diagramas, metadata={"herramienta": "diagramas"}
        ),
    ]

    ChatPrompt(
        name="add-paragraph",
        prompt=[
            Message(role=Role.system, content=paragraph_system_prompt),
            Message(role=Role.user, content=paragraph_user_prompt),
        ],
        few_shot_examples=few_shot_examples,
    ).save(prompt_manager)

    # %%
    from src.api.workflows.topics.generate_in_depth.generate_in_depth_prompts import (
        prompt_extract_queries,
        prompt_justify_resource,
    )

    ChatPrompt(
        name="prompt_extract_queries",
        prompt=[
            Message(role=Role.system, content=prompt_extract_queries),
            Message(role=Role.user, content=user_input),
        ],
    ).save(prompt_manager)

    # %%

    ChatPrompt(
        name="prompt_justify_resource",
        prompt=[
            Message(role=Role.system, content=prompt_justify_resource),
            Message(role=Role.user, content=user_input),
        ],
    ).save(prompt_manager)

    TextPrompt(
        name="search-queries-atomic",
        prompt=prompt_search_queries_atomic,
    ).save(prompt_manager)

    from src.api.common.services.search_agent_prompts import (
        agent_prompt,
        extract_content_prompt,
        source_evaluation_prompt,
    )

    TextPrompt(
        name="extract-content-prompt",
        prompt=extract_content_prompt,
    ).save(prompt_manager)

    TextPrompt(
        name="agent-prompt",
        prompt=agent_prompt,
    ).save(prompt_manager)

    TextPrompt(
        name="source-evaluation-prompt",
        prompt=source_evaluation_prompt,
    ).save(prompt_manager)

    # %%
    from src.api.workflows.sections.regenerate.regenerate_paragraph_prompts import (
        augment_paragraph_user_query_prompt,
        regenerate_paragraph_prompt,
    )

    regenerate_paragraph_prompt.save(prompt_manager)
    augment_paragraph_user_query_prompt.save(prompt_manager)
    from src.api.workflows.topics.generate_tests.generate_tests_prompts import (
        generate_tests_prompt,
    )

    generate_tests_prompt.save(prompt_manager)

    from src.api.common.services.content_generator.prompts_content_generator import (
        prompt_contenido_tema,
        prompt_memorias,
    )

    prompt_contenido_tema.save(prompt_manager)
    prompt_memorias.save(prompt_manager)


if __name__ == "__main__":
    create_prompts()
