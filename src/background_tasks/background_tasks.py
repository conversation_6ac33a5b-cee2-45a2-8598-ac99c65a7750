import asyncio
import signal
import sys
import threading
from http.server import BaseHTTPRequest<PERSON>andler, HTTPServer

from src.api.common.dependency_container import DependencyContainer


class HealthCheckHandler(BaseHTTPRequestHandler):
    def do_GET(self):  # noqa: N802
        self.send_response(200)
        self.send_header("Content-Type", "text/plain")
        self.end_headers()
        self.wfile.write(b"OK")


def run_http_server():
    server = HTTPServer(("0.0.0.0", 80), HealthCheckHandler)
    server.serve_forever()


async def main():
    # Identify and initialize only what is needed
    DependencyContainer.initialize(
        observability=True
    )  # TODO: Manually initialize what is needed, separate dependencies for the task container.
    logger = DependencyContainer.get_logger()
    application_settings = DependencyContainer.get_application_settings()
    logger.info("Starting background tasks")
    doc_task = asyncio.create_task(
        DependencyContainer.get_process_document_task(
            n_concurrent=application_settings.GENERATE_DOCUMENT_TASK_N_CONCURRENT
        ).execute()
    )
    content_task = asyncio.create_task(
        DependencyContainer.get_generate_content_task(
            n_concurrent=application_settings.GENERATE_CONTENT_TASK_N_CONCURRENT
        ).execute()
    )

    loop = asyncio.get_running_loop()

    def handle_shutdown():
        logger.info("Shutting down background tasks...")
        for t in [doc_task, content_task]:
            t.cancel()

    for s in (signal.SIGINT, signal.SIGTERM):
        loop.add_signal_handler(s, handle_shutdown)

    results = await asyncio.gather(doc_task, content_task, return_exceptions=True)

    for result in results:
        if isinstance(result, Exception) and not isinstance(
            result, asyncio.CancelledError
        ):
            print(f"A task crashed with exception: {result}")


if __name__ == "__main__":
    threading.Thread(target=run_http_server, daemon=True).start()
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("Interrupted by user")
        sys.exit(0)
