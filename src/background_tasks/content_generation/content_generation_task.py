from __future__ import annotations

import async<PERSON>
from logging import Logger
from typing import Op<PERSON>, <PERSON><PERSON>

from pydantic import BaseModel
from sqlalchemy import Engine, Integer, cast
from sqlmodel import Session, delete, select
from src.api.common.services import MailSender
from src.api.common.services.ai_tracer import AITracer
from src.api.common.services.content_generator import ContentGenerator
from src.api.common.services.index_repository import IndexRepository
from src.api.common.services.message_broker.local_sqlite_broker import (
    LocalSqliteMessage,
)
from src.api.common.services.message_broker.queue_interface import QueueInterface
from src.api.common.services.message_broker.service_bus_queue_interface import (
    ServiceBusReceivedMessage,
)
from src.api.common.services.structs import (
    GeneratedContent,
    ModelInfo,
)
from src.background_tasks.base_service_bus_task import (
    BaseServiceBusTask,
    ServiceBusConfig,
)
from src.domain.models import (
    AIProcess,
    AIProcessStatus,
    AIProcessType,
    ContenidoGenerado,
    ContentPlan,
    ContentPlanItem,
    ContentPlanType,
    EmailTriggerStatus,
    Indice,
    TopicStatus,
)


class ContentQueueItem(BaseModel):
    indice_id: int
    tema_id: int
    ai_process_type: AIProcessType | None = None
    ai_process_id: int | None = None
    plan_version: int | None = None
    model_info: ModelInfo | None = None


class ContentGenerationTask(BaseServiceBusTask):
    def __init__(
        self,
        logger: Logger,
        content_generator: ContentGenerator,
        ai_tracer: AITracer,
        queue_interface: QueueInterface,
        db_engine: Engine,
        config: ServiceBusConfig,  # Could have a class that inherits from ServiceBusConfig in case we need to add more fields
        index_repository: IndexRepository,
        mail_sender: MailSender | None,
    ) -> None:
        super().__init__(logger, queue_interface, config)
        self._content_generator = content_generator
        self._ai_tracer = ai_tracer
        self._db_engine = db_engine
        self._index_repository = index_repository
        self._mail_sender = mail_sender

    async def process_single_message(
        self, message: ServiceBusReceivedMessage | LocalSqliteMessage
    ) -> None:
        """Process a single message from the service bus queue."""

        msg_dict = self.queue_interface.msg_to_dict(message)
        self._logger.info(f"Msg dict is, {msg_dict}")
        queue_item = ContentQueueItem(**msg_dict)
        indice_id, tema_id, plan_version, model_info, parent_ai_process_id = (
            queue_item.indice_id,
            queue_item.tema_id,
            queue_item.plan_version,
            queue_item.model_info,
            queue_item.ai_process_id,
        )
        self._logger.info(f"Queue item is, {queue_item}")

        if self._is_ai_process_running(indice_id, tema_id, parent_ai_process_id):
            self._logger.info(
                f"An AIProcess for indice_id {indice_id}, tema_id {tema_id} is already running."
                f"Skipping message {message.message_id}."
            )
            return

        await self._change_process_status(
            parent_ai_process_id,
            AIProcessStatus.IN_PROGRESS,
            TopicStatus.CONTENT_IN_PROGRESS,
        )

        memories = await self._content_generator.get_memories_for_topic(
            indice_id, tema_id, plan_version
        )
        if not memories:
            await self._content_generator.create_and_store_memories_for_index(
                queue_item.indice_id, queue_item.plan_version
            )

        if model_info:
            (
                generated_contents,
                ai_processes,
            ) = await self._content_generator.generate_topic_pipeline(
                indice_id,
                tema_id,
                model_info.provider,
                model_info.name,
                parent_process_id=parent_ai_process_id,
            )
        else:
            (
                generated_contents,
                ai_processes,
            ) = await self._content_generator.generate_topic_pipeline(
                indice_id,
                tema_id,
                parent_process_id=parent_ai_process_id,
            )

        generated_contents_with_process = [
            (content, ai_process)
            for content, ai_process in zip(generated_contents, ai_processes)
        ]

        all_success = self._store_content(generated_contents_with_process)
        if all_success:
            self._complete_ai_process(queue_item.ai_process_id)
            await self._change_process_status(
                queue_item.ai_process_id,
                AIProcessStatus.COMPLETED,
                TopicStatus.CONTENT_GENERATION,
            )
            with Session(self._db_engine) as session:
                index = session.exec(
                    select(Indice).where(Indice.id == queue_item.indice_id)
                ).first()
            if self._mail_sender is not None:
                await self._mail_sender.async_send_email(
                    order_id=index.order_id,
                    status=EmailTriggerStatus.TOPIC,
                    topic_id=queue_item.tema_id,
                )

        with Session(self._db_engine) as session:
            statement = select(AIProcess).where(
                AIProcess.process_type == AIProcessType.GENERATE_CONTENT_FOR_TOPIC,
                cast(AIProcess.additional_metadata["plan_version"].astext, Integer)
                == queue_item.plan_version,
                AIProcess.indice_id == queue_item.indice_id,
            )
            ai_processes = session.exec(statement).all()
            if ai_processes:
                if all(p.status == AIProcessStatus.COMPLETED for p in ai_processes):
                    self._logger.info(
                        f"Content Generation process has been completed for index: {queue_item.indice_id}"
                    )
                else:
                    self._logger.info(
                        f"Not all topics have been generated for index: {queue_item.indice_id} yet"
                    )

    async def _change_process_status(
        self, ai_process_id: int, status: AIProcessStatus, topic_status: TopicStatus
    ):
        tema_id = self._get_tema_id_from_process(ai_process_id)
        await self._index_repository.change_topic_status(tema_id, topic_status)

        with Session(self._db_engine) as session:
            ai_process = session.get(AIProcess, ai_process_id)
            if not ai_process:
                raise ValueError("AI process not found")
            ai_process.status = status
            session.commit()

    def _get_tema_id_from_process(self, ai_process_id: int) -> int:
        with Session(self._db_engine) as session:
            ai_process = session.get(AIProcess, ai_process_id)
            if not ai_process:
                raise ValueError("AI process not found")
            return ai_process.tema_id

    async def on_max_retries_exceeded(
        self,
        message: ServiceBusReceivedMessage | LocalSqliteMessage,
        exception: Exception,
    ) -> None:
        """Handle when a message exceeds maximum retry attempts."""
        msg_dict = self.queue_interface.msg_to_dict(message)
        queue_item = ContentQueueItem(**msg_dict)
        await self._change_process_status(
            queue_item.ai_process_id, AIProcessStatus.FAILED, TopicStatus.CONTENT_FAILED
        )
        # Ahora mismo si TopicStatus falla -> Volver a not started. Pendiente confirmacion

    def _is_ai_process_running(
        self, indice_id: int, tema_id: int, current_ai_process_id: int
    ) -> bool:
        """Check if there's already an AI process running for the given indices."""
        with Session(self._db_engine) as session:
            ai_process = session.exec(
                select(AIProcess).where(
                    AIProcess.indice_id == indice_id,
                    AIProcess.tema_id == tema_id,
                    AIProcess.process_type == AIProcessType.GENERATE_CONTENT_FOR_TOPIC,
                    AIProcess.status == AIProcessStatus.IN_PROGRESS,
                    AIProcess.id != current_ai_process_id,
                )
            ).first()
            return ai_process is not None

    def _complete_ai_process(self, ai_process_id: int) -> bool:
        """Complete an AI process and check all its children processes."""
        with Session(self._db_engine) as session:
            ai_process = session.exec(
                select(AIProcess).where(AIProcess.id == ai_process_id)
            ).first()
            if not ai_process:
                raise ValueError("AI process not found")
            if ai_process.status == AIProcessStatus.COMPLETED:
                raise RuntimeError("This AI process is already completed")

            all_success = True
            children_processes = ai_process.children

            # Fetch latest process per tema_id
            tema_status = {}
            for process in children_processes:
                if (
                    process.tema_id not in tema_status
                    or tema_status[process.tema_id].id < process.id
                ):  # Select the latest process by id
                    tema_status[process.tema_id] = process

            # Check statuses of the latest processes
            for tema_id, process in tema_status.items():
                if process.status != AIProcessStatus.COMPLETED:
                    all_success = False
                    self._logger.warning(
                        f"Latest process with tema_id: {tema_id} (process id: {process.id}) is not completed."
                    )

            if all_success:
                self._ai_tracer.complete_process(ai_process, AIProcessStatus.COMPLETED)
            else:
                self._logger.warning(
                    f"Not all executions of process with id: {ai_process_id} were succesfull, still in progress."
                )
            return all_success

    def _store_content(
        self,
        generated_contents: list[tuple[GeneratedContent, AIProcess]],
    ) -> bool:
        """Store generated content in batches for a full topic."""
        all_success = True
        created_plan_items = []

        with Session(self._db_engine) as session:
            try:
                for content, ai_process in generated_contents:
                    content_plan = session.exec(
                        select(ContentPlan)
                        .where(ContentPlan.epigrafe_id == ai_process.epigrafe_id)
                        .order_by(ContentPlan.version.desc())
                    ).first()

                    if content_plan:
                        content_plan_item = ContentPlanItem(
                            content_plan_id=content_plan.id,
                            plan="Generated content",
                            descripcion="Content generated by new pipeline",
                            herramienta=ContentPlanType.TEXTO,
                            position=1,
                            is_deleted=False,
                        )
                        session.add(content_plan_item)
                        session.flush()  # Get the ID
                        created_plan_items.append(content_plan_item)

                        content.plan_item_id = content_plan_item.id
                    else:
                        self._logger.warning(
                            f"No ContentPlan found for epigrafe_id: {ai_process.epigrafe_id}"
                        )
                        all_success = False

                contenido_generado_list = self._build_contenido_generado_list(
                    generated_contents
                )

                if not contenido_generado_list:
                    all_success = False

                if contenido_generado_list and all_success:
                    session.add_all(contenido_generado_list)
                    session.commit()
                    self._logger.info(
                        f"Stored {len(contenido_generado_list)} ContenidoGenerado instances to the database."
                    )
                else:
                    self._delete_plan_items(created_plan_items)
                    self._logger.warning(
                        "No ContenidoGenerado instances to store or not all were successful."
                    )
                    raise ValueError("Error in content storage. Trying again")

            except Exception as e:
                session.rollback()
                self._logger.exception(
                    f"Error adding ContenidoGenerado instances to the database: {e}"
                )
                try:
                    self._delete_plan_items(created_plan_items)
                except Exception as e:
                    self._logger.exception(
                        f"Exception while deleting plan items after store content unknown exception: {e}"
                    )
                raise

        return all_success

    def _delete_plan_items(self, plan_items: list[ContentPlanItem]) -> None:
        """Delete content plan items from the database."""
        if not plan_items:
            return

        with Session(self._db_engine) as session:
            try:
                plan_item_ids = [item.id for item in plan_items if item.id]

                if plan_item_ids:
                    session.exec(
                        delete(ContentPlanItem).where(
                            ContentPlanItem.id.in_(plan_item_ids)
                        )
                    )
                    session.commit()
                    self._logger.info(
                        f"Deleted {len(plan_item_ids)} content plan items from the database."
                    )
                else:
                    self._logger.warning("No content plan items to delete.")

            except Exception as e:
                session.rollback()
                self._logger.exception(
                    f"Error deleting content plan items from the database: {e}"
                )
                raise

    def _build_contenido_generado_list(
        self, generated_contents: list[Tuple[GeneratedContent, AIProcess]]
    ) -> list[ContenidoGenerado]:
        """Construye la lista de ContenidoGenerado a partir de los contenidos generados y procesos AI."""
        contenido_generado_list = []
        for content, ai_process in generated_contents:
            if ai_process and ai_process.status == AIProcessStatus.COMPLETED:
                contenido_generado = ContenidoGenerado(
                    contenido=content.content,
                    id_item_plan_contenido=content.plan_item_id,
                    documentos_relacionados=content.related_docs,
                    chunks_relacionados=content.related_chunks,
                    metadatos=content.metadata.model_dump() if content.metadata else {},
                )
                contenido_generado_list.append(contenido_generado)
            else:
                self._logger.warning(
                    f"AIProcess {ai_process.id} did not complete successfully. Status: {ai_process.status}"
                )
        return contenido_generado_list

    def _delete_contents(self, contents: list[Optional[ContenidoGenerado]]) -> None:
        """Delete generated contents from the database."""
        with Session(self._db_engine) as session:
            try:
                content_ids = [
                    content.id for content in contents if content is not None
                ]
                if not content_ids:
                    self._logger.warning("No contents to delete.")
                    return

                session.exec(
                    delete(ContenidoGenerado).where(
                        ContenidoGenerado.id.in_(content_ids)
                    )
                )
                session.commit()
                self._logger.info(
                    f"Deleted {len(content_ids)} contents from the database."
                )
            except Exception as e:
                session.rollback()
                self._logger.exception(
                    f"Error deleting contents from the database: {e}"
                )
                raise


if __name__ == "__main__":
    import logging

    from src.api.common.dependency_container import DependencyContainer

    # Set the logging level for Azure Service Bus
    logging.getLogger("azure.servicebus").setLevel(logging.WARNING)
    DependencyContainer.initialize()
    generate_content_task = DependencyContainer.get_generate_content_task()
    asyncio.run(generate_content_task.execute())
