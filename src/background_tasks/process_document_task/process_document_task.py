from __future__ import annotations

from logging import Logger
from typing import Any

import httpx
from sqlalchemy import Engine
from sqlmodel import Session, select
from src.api.common.services.evaluation import DocumentInfo, DocumentInfoService
from src.api.common.services.evaluation.utils import (
    freshness_score,
    quality_score,
    relevance_score,
)
from src.api.common.services.message_broker.local_sqlite_broker import (
    LocalSqliteMessage,
)
from src.api.common.services.message_broker.queue_interface import QueueInterface
from src.api.common.services.message_broker.service_bus_queue_interface import (
    ServiceBusReceivedMessage,
)
from src.api.common.services.url_extractor_engine import UrlExtractor
from src.api.common.tools.loaders import WebsiteLoader
from src.api.common.tools.utils import sanitize_string
from src.api.workflows.document_ingestion import (
    Embedding,
    OpenAIEmbeddingFunction,
    SmartSemanticChunker,
)
from src.api.workflows.document_ingestion.utils import (
    generate_document_citation,
)
from src.background_tasks.base_service_bus_task import (
    BaseServiceBusTask,
    ServiceBusConfig,
)
from src.common.application_settings import ApplicationSettings
from src.domain.models import (
    Bloque,
    ChunkDocumento,
    Doc,
    DocStatus,
    EmbeddingModel,
    Epigrafe,
    Indice,
    IndiceStatus,
    Tema,
)
from tqdm import tqdm

from .schemas import DocumentData, ProcessQueueItemData
from .utils import sanitize_dict, sanitize_document


class DocProcessingTask(BaseServiceBusTask):
    def __init__(
        self,
        logger: Logger,
        queue_interface: QueueInterface,
        db_engine: Engine,
        config: ServiceBusConfig,
        application_settings: ApplicationSettings,
        url_extractor: UrlExtractor,
        embedding_model: OpenAIEmbeddingFunction,
    ):
        super().__init__(logger, queue_interface, config)
        self._logger = logger
        self._db_engine = db_engine
        self._application_settings = application_settings
        self._url_extractor = url_extractor
        self._embedding_model = embedding_model

    def _check_index_status_for_document(self, session: Session, document: Doc) -> bool:
        """Check if document's index is in CONTENT_GENERATION status."""
        if document.epigrafes:
            index = document.epigrafes[0].tema.bloque.indice
            return index.status == IndiceStatus.CONTENT_GENERATION
        return False

    async def process_single_message(
        self, message: ServiceBusReceivedMessage | LocalSqliteMessage
    ) -> None:
        """
        Process a single message from the queue.
        """
        if message.body:
            msg_dict = self.queue_interface.msg_to_dict(message)
            process_queue_item_data = ProcessQueueItemData(**msg_dict)

            with Session(self._db_engine) as session:
                document = self._get_document_from_data(
                    session, process_queue_item_data
                )
                if not self._check_index_status_for_document(session, document):
                    self._logger.info(
                        f"Skipping document {document.id} - index not in CONTENT_GENERATION status"
                    )
                    return

            document_id, doc_already_processed = await self._doc_process_item(
                process_queue_item_data
            )

            if not doc_already_processed:
                topics_ready = await self._check_topic_docs_processed(document_id)
                default_plan_version = 1
                for topic_ready, tema_id, index in topics_ready:
                    if topic_ready:
                        self._logger.info(
                            f"Topic {tema_id} is ready for content generation"
                        )
                        await self._send_generate_content_message_for_topic(
                            tema_id, index.id, default_plan_version
                        )
                    else:
                        self._logger.info(
                            f"Topic {tema_id} is not ready for content generation yet"
                        )
        else:
            self._logger.info(f"Empty message found: {message.message_id}")

    async def _check_topic_docs_processed(
        self, document_id: str
    ) -> list[tuple[bool, int, Indice]]:
        """
        Check which topics have all their documents processed.

        Args:
            document_id (str): The document ID to check.
        Returns:
            List of tuples (all_docs_processed, tema_id, index) for each topic
            that has documents related to the processed document.
        """
        try:
            with Session(self._db_engine) as session:
                topics_and_docs = session.exec(
                    select(Tema, Doc, Indice)
                    .join(Tema.epigrafes)
                    .join(Epigrafe.documents)
                    .join(Tema.bloque)
                    .join(Bloque.indice)
                    .where(
                        Tema.epigrafes.any(
                            Epigrafe.documents.any(Doc.id == document_id)
                        )
                    )
                ).all()

                if not topics_and_docs:
                    self._logger.error(
                        f"Document {document_id} does not belong to any topic"
                    )
                    raise ValueError(
                        f"Document {document_id} does not belong to any topic. Something went wrong."
                    )

                topics_info = {}
                for tema, _, indice in topics_and_docs:
                    if tema.id not in topics_info:
                        topics_info[tema.id] = (tema, indice)

                results = []

                for tema_id, (tema, indice) in topics_info.items():
                    if indice.status != IndiceStatus.CONTENT_GENERATION:
                        self._logger.info(
                            f"Index {indice.id} is not in CONTENT_GENERATION status, skipping topic {tema_id}"
                        )
                        results.append((False, tema_id, indice))
                        continue

                    topic_docs = (
                        session.exec(
                            select(Doc)
                            .join(Doc.epigrafes)
                            .join(Epigrafe.tema)
                            .where(Tema.id == tema_id)
                        )
                        .unique()
                        .all()
                    )

                    all_processed = all(
                        doc.status != DocStatus.PENDING
                        and doc.status != DocStatus.PROCESSING
                        for doc in topic_docs
                    )

                    if all_processed:
                        self._logger.info(
                            f"All documents have been processed for topic {tema_id} ({tema.name})"
                        )
                    else:
                        pending_count = sum(
                            1
                            for doc in topic_docs
                            if doc.status in [DocStatus.PENDING, DocStatus.PROCESSING]
                        )
                        self._logger.info(
                            f"Topic {tema_id} ({tema.name}) still has {pending_count} documents pending/processing"
                        )

                    results.append((all_processed, tema_id, indice))

                return results

        except Exception as e:
            self._logger.exception(
                f"An error occurred while checking topic documents processed for document id {document_id}: {e}"
            )
            raise

    async def _send_generate_content_message_for_topic(
        self, tema_id: int, index_id: int, plan_version: int = 1
    ) -> None:
        try:
            payload = {
                "indice_id": index_id,
                "tema_id": tema_id,
                "plan_version": plan_version,
            }
            headers = {"x-api-key": self._application_settings.FASTAPI_API_KEY}
            gen_ai_api_url = self._application_settings.GEN_AI_API_URL
            self._logger.info(
                f"Making request to generate content for topic {tema_id} in index {index_id} and plan version {plan_version}. Using url {gen_ai_api_url}/api/v1/indexes/generate_content"
            )
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{gen_ai_api_url}/api/v1/indexes/generate_content",
                    json=payload,
                    headers=headers,
                )
            self._logger.info(
                f"Generate content response for topic {tema_id}: {response}"
            )
        except Exception as e:
            self._logger.exception(
                f"An error occurred while sending the generate content message for topic {tema_id} in index {index_id}: {e}"
            )
            raise e

    async def _doc_process_item(self, data: ProcessQueueItemData) -> tuple[str, bool]:
        """
        Processes a document item from the queue using fetch-process-store pattern.

        Args:
            data (ProcessQueueItemData): The data of the document to be processed.

        Returns:
            tuple[str, bool]: A tuple containing the document ID and a boolean indicating
                              whether the document has been processed before.

        Raises:
            Exception: If the document blob URI is not found in the message data.
            Exception: If the document is not found in the database.
            Exception: If an error occurs while processing the document.
        """
        with Session(self._db_engine) as session:
            document = self._get_document_from_data(session, data)

            if document.status == DocStatus.PROCESSED:
                self._logger.info(f"Document {document.id} has already been processed.")
                return str(document.id), True

            document_data = DocumentData(
                id=document.id,
                document_url=document.document_url,
                content=document.content,
                name=document.name,
                description=document.description,
                summary=document.summary,
                authors=document.authors,
                publication_date=document.publication_date,
                final_citation=document.final_citation,
                inline_citation=document.inline_citation,
                quality_score=document.quality_score,
                freshness_score=document.freshness_score,
                relevance_score=document.relevance_score,
                research_source_citation_count=document.research_source_citation_count,
                research_source_publication_year=document.research_source_publication_year,
                research_source_title=document.research_source_title,
                research_source_authors=document.research_source_authors,
                lenguage=document.lenguage,
                file_type=document.file_type,
                token_count=document.token_count,
                status=document.status,
                path_blob=document.path_blob,
                additional_metadata=document.additional_metadata,
                toc=document.toc,
                id_fuente_documento=document.id_fuente_documento,
                epigrafes_data=[
                    {"id": e.id, "name": e.name} for e in document.epigrafes
                ],
            )

            document.status = DocStatus.PROCESSING
            session.add(document)
            session.commit()

        try:
            (
                processed_document,
                chunker,
                splitted_chunks,
                llm_metadata_keys,
            ) = await self._process_document_external(document_data)
        except Exception as e:
            await self._update_document_status(document_data.id, DocStatus.FAILED)
            raise e

        try:
            await self._store_processed_document(
                processed_document, chunker, splitted_chunks, llm_metadata_keys
            )
            return str(document_data.id), False
        except Exception as e:
            await self._update_document_status(document_data.id, DocStatus.FAILED)
            raise e

    async def on_max_retries_exceeded(
        self,
        message: ServiceBusReceivedMessage | LocalSqliteMessage,
        exception: Exception,
    ) -> None:
        self._logger.warning(
            f"Max retries exceeded for message {message.message_id}: {exception}"
        )
        msg_dict = self.queue_interface.msg_to_dict(message)
        process_queue_item_data = ProcessQueueItemData(**msg_dict)
        with Session(self._db_engine) as session:
            try:
                document = self._get_document_from_data(
                    session, process_queue_item_data
                )
                topics_ready = await self._check_topic_docs_processed(str(document.id))
                default_plan_version = 1

                for topic_ready, tema_id, index in topics_ready:
                    if topic_ready:
                        self._logger.info(
                            f"Topic {tema_id} is ready for content generation after max retries exceeded"
                        )
                        await self._send_generate_content_message_for_topic(
                            tema_id, index.id, default_plan_version
                        )
                    else:
                        self._logger.info(
                            f"Topic {tema_id} is not ready for content generation after max retries exceeded"
                        )
            except Exception as e:
                self._logger.exception(
                    f"An error occurred while on max retries exceeded from message {str(process_queue_item_data)}: {e}"
                )
                session.rollback()

    def _get_document_from_data(
        self, session: Session, data: ProcessQueueItemData
    ) -> Doc:
        uuid = data.file_uuid
        document = session.exec(select(Doc).where(Doc.id == uuid)).first()

        if not document:
            raise ValueError(f"Document with uuid {uuid} not found in database.")

        return document

    async def _process_document_external(
        self, document_data: DocumentData
    ) -> tuple[Doc, any, any, list]:
        """
        Process document externally without database session.
        """
        llm_included_metadata_keys = [
            Doc.name.key,
            Doc.summary.key,
            Doc.authors.key,
            Doc.final_citation.key,
            Doc.inline_citation.key,
            Doc.relevance_score.key,
            "page",
        ]

        document = Doc(
            id=document_data.id,
            name=document_data.name,
            description=document_data.description,
            summary=document_data.summary,
            authors=document_data.authors,
            publication_date=document_data.publication_date,
            final_citation=document_data.final_citation,
            inline_citation=document_data.inline_citation,
            quality_score=document_data.quality_score,
            freshness_score=document_data.freshness_score,
            relevance_score=document_data.relevance_score,
            research_source_citation_count=document_data.research_source_citation_count,
            research_source_publication_year=document_data.research_source_publication_year,
            research_source_title=document_data.research_source_title,
            research_source_authors=document_data.research_source_authors,
            lenguage=document_data.lenguage,
            file_type=document_data.file_type,
            token_count=document_data.token_count,
            document_url=document_data.document_url,
            content=document_data.content,
            path_blob=document_data.path_blob,
            additional_metadata=document_data.additional_metadata,
            toc=document_data.toc,
            id_fuente_documento=document_data.id_fuente_documento,
        )

        doc_loader = WebsiteLoader(
            url=document_data.document_url,
            text=document_data.content,
            url_extractor=self._url_extractor,
        )
        await doc_loader.extract_all(document_data.content == "")
        results = await doc_loader.get_results()

        if "text_segments" in results:
            for segment in results["text_segments"]:
                if hasattr(segment, "text"):
                    segment.text = sanitize_string(segment.text)
                if hasattr(segment, "metadata") and segment.metadata:
                    segment.metadata = sanitize_dict(segment.metadata)

        splitted_chunks, chunker = await SmartSemanticChunker.from_text_segments_async(
            text_segments=results["text_segments"],
            language=document_data.lenguage,
            embedding_function=OpenAIEmbeddingFunction(
                api_type="azure",
                api_base=self._application_settings.AZURE_OPENAI_ENDPOINT,
                api_key=self._application_settings.AZURE_OPENAI_API_KEY,
                api_version=self._application_settings.AZURE_API_VERSION,
                model_name="text-embedding-3-large",
            ),
        )
        if document_data.content == "":
            document.content = sanitize_string(doc_loader._text)
        else:
            document.content = document_data.content
        document.additional_metadata = sanitize_dict(doc_loader._metadata)
        document.token_count = doc_loader._total_tokens
        document.toc = [sanitize_dict(i.model_dump()) for i in doc_loader._toc]

        generate_document_citation(document)

        try:
            doc_info_service = DocumentInfoService(use_semantic_scholar=False)
            document_info: DocumentInfo = await doc_info_service.get_paper_info(
                document_data.name
            )
        except Exception as e:
            self._logger.exception(
                f"An error occurred while getting document info: {e}"
            )
            document_info = DocumentInfo(
                title=document_data.name,
                authors=[],
                published_year=None,
                citation_count=0,
            )

        document.quality_score = quality_score(document_info)
        document.freshness_score = freshness_score(document_info)

        epigrafes_data = document_data.epigrafes_data
        doc_relevance = {}
        epigrafe_ids_str = ", ".join([str(e["id"]) for e in epigrafes_data])
        doc_relevance[epigrafe_ids_str] = relevance_score(
            document_data.summary,
            [sanitize_string(e["name"]) for e in epigrafes_data],
            self._embedding_model,
        )
        document.relevance_score = doc_relevance

        document.research_source_authors = [
            sanitize_dict(author.__dict__) for author in document_info.authors
        ]
        document.research_source_citation_count = document_info.citation_count
        document.research_source_publication_year = document_info.published_year
        document.research_source_title = sanitize_string(document_info.title)

        return document, chunker, splitted_chunks, llm_included_metadata_keys

    async def _update_document_status(self, document_id: str, status: DocStatus):
        """
        Update document status with short session.
        """
        with Session(self._db_engine) as session:
            document = session.get(Doc, document_id)
            if document:
                document.status = status
                document = sanitize_document(document)
                session.add(document)
                session.commit()

    async def _store_processed_document(
        self, document: Doc, chunker: any, splitted_chunks: any, llm_metadata_keys: list
    ):
        """
        Store processed document and chunks with short session.
        """
        with Session(self._db_engine) as session:
            document.status = DocStatus.PROCESSED
            document = sanitize_document(document)
            session.merge(document)

            self.insert_chunks(
                session, chunker, splitted_chunks, document, llm_metadata_keys
            )
            session.commit()

    def _prepare_batch_data(
        self,
        batch: list[Any],
        document: Doc,
        llm_included_metadata_keys: list[str],
    ) -> tuple[list[str], list[tuple]]:
        batch_texts = []
        batch_metadata = []
        for text_segment in batch:
            try:
                if hasattr(text_segment, "metadata"):
                    text_segment.metadata = sanitize_dict(text_segment.metadata)

                for key in llm_included_metadata_keys:
                    if key == "page":
                        if text_segment.metadata.get("page_start"):
                            text_segment.metadata[key] = text_segment.metadata[
                                "page_start"
                            ]
                    else:
                        text_segment.metadata[key] = sanitize_string(
                            getattr(document, key)
                        )

                # Sanitize text before encoding
                text_content = sanitize_string(text_segment.text)
                encoded_text = (
                    str(text_content)
                    .encode("utf-8", errors="ignore")
                    .decode("utf-8")
                    .strip()
                )
                batch_texts.append(encoded_text)
                batch_metadata.append(
                    (
                        text_segment.tokens,
                        text_content,
                        text_segment.metadata,
                        llm_included_metadata_keys,
                    )
                )
            except UnicodeEncodeError:
                self._logger.exception(
                    f"Error: Invalid Unicode in text. Skipping this chunk: {text_segment.text[:100]}..."
                )
                continue
        return batch_texts, batch_metadata

    def _create_chunk_documentos(
        self,
        batch_embeddings: list[list[float]],
        batch_metadata: list[tuple],
        document: Doc,
        model_name: str,
        vector_dimensions: int,
    ) -> list[ChunkDocumento]:
        chunks = []
        for embedding, (tokens, original_text, metadata, keys) in zip(
            batch_embeddings, batch_metadata
        ):
            chunk = ChunkDocumento(
                doc_id=str(document.id),
                tokens=int(tokens),
                text=sanitize_string(original_text),
                additional_metadata=sanitize_dict(metadata),
                embedding=embedding,
                llm_included_metadata_keys=keys,
                embedding_model=EmbeddingModel(model_name),
                vector_dimensions=int(vector_dimensions),
            )
            chunks.append(chunk)
        return chunks

    def insert_chunks(
        self,
        session: Session,
        chunker: SmartSemanticChunker,
        splitted_text,
        document: Doc,
        llm_included_metadata_keys: list[str],
    ):
        embedding_function = chunker.embedding_function

        if isinstance(embedding_function, Embedding):
            self._logger.info("Embedding is type HuggingFace")
            vector_dimensions = (
                chunker.embedding_function.model.get_sentence_embedding_dimension()
            )
        elif isinstance(embedding_function, OpenAIEmbeddingFunction):
            self._logger.info("Embedding is type OpenAI")
            vector_dimensions = 1536  # Adjust as per model
        else:
            raise TypeError("Invalid embedding function")
        model_name = embedding_function._model_name
        chunks = self.embed_chunks(
            splitted_text,
            embedding_function,
            model_name,
            vector_dimensions,
            document,
            llm_included_metadata_keys,
            chunker.batch_size,
        )

        try:
            session.add_all(chunks)
        except Exception as e:
            self._logger.error("Error occurred inserting chunks")
            raise e

    def embed_chunks(
        self,
        texts: list[str],
        embedding_function: callable,
        model_name: str,
        vector_dimensions: int,
        document: Doc,
        llm_included_metadata_keys: list[str],
        batch_size: int = 32,
    ):
        all_chunks = []

        for i in tqdm(range(0, len(texts), batch_size), desc="Processing batches"):
            batch = texts[i : i + batch_size]

            batch_texts, batch_metadata = self._prepare_batch_data(
                batch, document, llm_included_metadata_keys
            )

            if not batch_texts:
                continue

            try:
                batch_embeddings = embedding_function(batch_texts)
            except Exception as e:
                self._logger.exception(f"Error creating embeddings for batch: {e}")
                continue

            # Create ChunkDocumento objects
            chunk_documentos = self._create_chunk_documentos(
                batch_embeddings,
                batch_metadata,
                document,
                model_name,
                vector_dimensions,
            )
            all_chunks.extend(chunk_documentos)

        return all_chunks
