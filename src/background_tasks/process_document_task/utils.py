from __future__ import annotations

from typing import Any

from src.api.common.tools.utils import sanitize_string
from src.domain.models import Doc


def sanitize_dict(d: dict[str, Any]) -> dict[str, Any]:
    """Sanitize all string values in a dictionary recursively."""
    if not isinstance(d, dict):
        return d

    result = {}
    for k, v in d.items():
        if isinstance(v, str):
            result[k] = sanitize_string(v)
        elif isinstance(v, dict):
            result[k] = sanitize_dict(v)
        elif isinstance(v, list):
            result[k] = [
                sanitize_dict(item)
                if isinstance(item, dict)
                else sanitize_string(item)
                if isinstance(item, str)
                else item
                for item in v
            ]
        else:
            result[k] = v
    return result


def sanitize_document(document: Doc) -> Doc:
    """Sanitize all text fields in a document to remove NULL bytes."""
    document.name = sanitize_string(document.name)
    document.description = sanitize_string(document.description)
    document.content = sanitize_string(document.content)
    document.summary = sanitize_string(document.summary)
    document.document_url = sanitize_string(document.document_url)
    document.final_citation = sanitize_string(document.final_citation)
    document.inline_citation = sanitize_string(document.inline_citation)
    document.research_source_title = sanitize_string(document.research_source_title)
    document.path_blob = sanitize_string(document.path_blob)

    if document.additional_metadata:
        document.additional_metadata = sanitize_dict(document.additional_metadata)

    if document.authors:
        document.authors = [sanitize_string(author) for author in document.authors]

    if document.toc:
        document.toc = [sanitize_dict(item) for item in document.toc]

    if document.relevance_score:
        document.relevance_score = sanitize_dict(document.relevance_score)

    if document.research_source_authors:
        document.research_source_authors = [
            sanitize_dict(author) for author in document.research_source_authors
        ]

    return document
