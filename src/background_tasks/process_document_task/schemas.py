from __future__ import annotations

from dataclasses import dataclass
from typing import Any

from pydantic import BaseModel
from src.domain.models import DocStatus, Lenguaje, TipoArchivo


@dataclass
class DocumentData:
    """Data structure for document processing."""

    id: Any
    name: str | None
    content: str
    summary: str | None
    id_fuente_documento: int
    lenguage: Lenguaje
    document_url: str | None
    epigrafes_data: list[dict[str, Any]]
    description: str | None = None
    token_count: int = -1
    file_type: TipoArchivo = TipoArchivo.PDF
    authors: list[str] = None
    publication_date: int | None = None
    final_citation: str | None = None
    inline_citation: str | None = None
    quality_score: float | None = None
    freshness_score: float | None = None
    relevance_score: dict[str, float] = None
    research_source_citation_count: int | None = None
    research_source_publication_year: int | None = None
    research_source_title: str | None = None
    research_source_authors: list[dict[str, Any]] = None
    status: DocStatus = DocStatus.PENDING
    path_blob: str | None = None
    toc: list[dict[str, Any]] | None = None
    additional_metadata: dict[str, Any] = None

    def __post_init__(self):
        """Initialize mutable default values."""
        if self.authors is None:
            self.authors = []
        if self.relevance_score is None:
            self.relevance_score = {}
        if self.research_source_authors is None:
            self.research_source_authors = []
        if self.additional_metadata is None:
            self.additional_metadata = {}


class ProcessQueueItemData(BaseModel):
    file_uuid: str
