import asyncio
from abc import ABC, abstractmethod
from contextlib import asynccontextmanager
from dataclasses import dataclass
from datetime import timed<PERSON><PERSON>
from logging import Logger
from typing import List

from src.api.common.services.message_broker.local_sqlite_broker import (
    LocalSqliteMessage,
)
from src.api.common.services.message_broker.queue_interface import QueueInterface
from src.api.common.services.message_broker.service_bus_queue_interface import (
    ServiceBusReceivedMessage,
)


@dataclass
class ServiceBusConfig:
    """Configuration settings for Service Bus task processing."""

    # TODO: Maybe remove n_concurrent and simply use batch_size, it is a bit confusing since n_concurrent sets
    # the max number of messages to process concurrently and batch_size sets the number of messages to process in a batch
    n_concurrent: int = 1
    max_retry_count: int = 3
    lock_renewal_interval: int = 30
    sleep_interval: int = 30
    max_wait_time: int = 5
    batch_size: int = 1
    use_native_retries: bool = True
    retry_delay_secs: int = 60  # Only used if use_native_retries is False, native retries do not support delay


class BaseServiceBusTask(ABC):
    def __init__(
        self,
        logger: Logger,
        queue_interface: QueueInterface,
        config: ServiceBusConfig,
    ):
        """
        Initialize the Service Bus task processor.

        Args:
            logger: Logger instance for logging
            service_bus_interface: Interface for interacting with Service Bus
            config: Configuration settings for the task processor
        """
        # Configure logger to include child class name and queue name
        logger = logger.getChild(queue_interface.queue_name)
        logger = logger.getChild(self.__class__.__name__)
        self._logger = logger
        self.queue_interface = queue_interface
        self.config = config

    async def execute(self) -> None:
        """
        Continuously process messages from the service bus queue.

        Raises:
            Exception: If there's an error during execution
        """
        while True:
            try:
                async with self.queue_interface:
                    status, messages = await self.queue_interface.receive_messages(
                        max_message_count=self.config.n_concurrent,
                        max_wait_time=self.config.max_wait_time,
                    )

                    if not status or not messages:
                        self._logger.info("No messages found, retrying in 30 seconds")
                        await asyncio.sleep(self.config.sleep_interval)
                        continue

                    # Group messages into batches
                    if len(messages) > self.config.batch_size:
                        message_batches = [
                            messages[i : i + self.config.batch_size]
                            for i in range(0, len(messages), self.config.batch_size)
                        ]
                    else:
                        message_batches = [messages]

                    tasks = [
                        self._process_messages_with_lock(batch)
                        for batch in message_batches
                    ]

                    await asyncio.gather(*tasks)

            except Exception as e:
                self._logger.exception(f"Exception during execution: {e}")
                await asyncio.sleep(self.config.sleep_interval)

    @asynccontextmanager
    async def _messages_processing_context(
        self, messages: List[ServiceBusReceivedMessage | LocalSqliteMessage]
    ):
        """
        Context manager for message processing that handles lock renewal.

        Args:
            messages: The messages being processed
        """
        lock_renewal_task = asyncio.create_task(self._renew_lock_periodically(messages))
        try:
            yield
        finally:
            lock_renewal_task.cancel()
            try:
                await lock_renewal_task
            except asyncio.CancelledError:
                pass

    async def _process_messages_with_lock(
        self, messages: List[ServiceBusReceivedMessage | LocalSqliteMessage]
    ) -> None:
        """
        Process a list of messages while maintaining its lock.

        Args:
            messages: The messages to process
        """
        async with self._messages_processing_context(messages):
            try:
                await self.process_message_batch(messages)
                for message in messages:
                    await self.queue_interface.complete_message(message)
                self._logger.info(
                    f"Processed messages and completed: {[message.message_id for message in messages]}"
                )
            except Exception as e:
                await self.handle_processing_exception(messages, e)

    @abstractmethod
    async def process_single_message(
        self, message: ServiceBusReceivedMessage | LocalSqliteMessage
    ) -> None:
        """
        Process an individual message. Must be implemented by subclasses.

        Args:
            message: The message to process
        """
        pass

    async def process_message_batch(
        self, messages: List[ServiceBusReceivedMessage | LocalSqliteMessage]
    ) -> None:
        """
        Process a batch of messages. By default, processes them one by one.
        Can be overridden by subclasses for true batch processing.
        """
        tasks = [
            asyncio.create_task(self.process_single_message(message))
            for message in messages
        ]
        await asyncio.gather(*tasks)

    async def _renew_lock_periodically(
        self, messages: List[ServiceBusReceivedMessage | LocalSqliteMessage]
    ) -> None:
        """
        Periodically renew the lock on a batch of messages.

        Args:
            messages: The messages whose locks needs to be renewed
        """
        try:
            while True:
                await asyncio.sleep(self.config.lock_renewal_interval)
                for message in messages:
                    await self.queue_interface.renew_message_lock(message)
                    self._logger.info(f"Lock renewed for message {message.message_id}")
        except asyncio.CancelledError:
            # Clean up in case its needed
            pass
        except Exception as e:
            self._logger.exception(
                f"Failed to renew lock for message batch {[message.message_id for message in messages]}: {e}"
            )

    async def handle_processing_exception(
        self,
        messages: List[ServiceBusReceivedMessage | LocalSqliteMessage],
        exception: Exception,
    ) -> None:
        """
        Handle exceptions that occur during message processing.

        Args:
            messages: The messages that failed processing
            exception: The exception that occurred
        """
        self._logger.exception(
            f"Failed to process message batch {[message.message_id for message in messages]}: {exception}"
        )
        for message in messages:
            if self.config.use_native_retries:
                self._logger.info(
                    f"Using native retries for message {message.message_id}"
                )
                if (
                    message.delivery_count
                    and message.delivery_count >= self.config.max_retry_count
                ):
                    await self.queue_interface.dead_letter_message(
                        message, error_description=str(exception)
                    )
                    self._logger.info(
                        f"Message {message.message_id} exceeded max retry count ({self.config.max_retry_count}). "
                        f"Moved to dead letter queue."
                    )
                    await self.on_max_retries_exceeded(message, exception)
                else:
                    await self.queue_interface.abandon_message(message)
                    self._logger.info(
                        f"Message {message.message_id} abandoned "
                        f"Delivery count: {message.delivery_count} "
                    )
            else:
                # Retry message with delay
                self._logger.info(
                    f"Using custom retries for message {message.message_id}"
                )
                message_retry_count = self.queue_interface.get_message_retry_count(
                    message
                )
                if message_retry_count >= self.config.max_retry_count:
                    await self.queue_interface.dead_letter_message(
                        message, error_description=str(exception)
                    )
                    self._logger.info(
                        f"Message {message.message_id} exceeded max retry count ({self.config.max_retry_count}). "
                        f"Moved to dead letter queue."
                    )
                    await self.on_max_retries_exceeded(message, exception)
                else:
                    retry_message_id = (
                        await self.queue_interface.retry_message_with_delay(
                            message,
                            delay=timedelta(seconds=self.config.retry_delay_secs),
                        )
                    )
                    self._logger.info(
                        f"Message {message.message_id} completed (failed). Will retry on message with id {retry_message_id} in {self.config.retry_delay_secs / 60} minutes. "
                        f"Retry count: {message_retry_count + 1}"
                    )

    async def on_max_retries_exceeded(
        self,
        message: ServiceBusReceivedMessage | LocalSqliteMessage,
        exception: Exception,
    ) -> None:
        """
        Handle when a message exceeds maximum retry attempts.

        Args:
            message: The message that exceeded retries
            exception: The last exception that occurred

        Note:
            This method can be overridden by subclasses to add custom handling.
        """
        pass
