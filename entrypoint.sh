#!/bin/bash

HOST=${HOST:-0.0.0.0}
PORT=${PORT:-8000}
WORKERS=${WORKERS:-1}
LOG_LEVEL=${LOG_LEVEL:-info}
WORKER_CLASS=${WORKER_CLASS:-uvicorn.workers.UvicornWorker}

run_uvicorn() {
    echo "Starting uvicorn"
    uv run uvicorn src.main:app \
        --host $HOST \
        --port $PORT \
        --reload \
        --log-level $LOG_LEVEL
}

run_gunicorn() {
    echo "Starting gunicorn"
    uv run gunicorn src.main:app \
        --bind $HOST:$PORT \
        --workers $WORKERS \
        --worker-class $WORKER_CLASS \
        --log-level $LOG_LEVEL \
        --timeout 300
}

if [[ $WORKERS -gt 1 ]]; then
    echo "Using Gunicorn with $WORKERS workers."
    run_gunicorn
else
    echo "Using Uvicorn with $WORKERS worker"
    run_uvicorn
fi 