services:
  app:
    container_name: app_contenidos
    build:
      context: .
      dockerfile: Dockerfile.app
      args:
        UV_INDEX_UNIR_PASSWORD: ${UV_INDEX_UNIR_PASSWORD}
      target: with-cert
      
    volumes:
      - .:/src
    ports:
      - "8000:8000"
    environment:
      CommonEnvironment: ${CommonEnvironment} 
      LoggingLevel: ${LoggingLevel} 
      Workers: ${Workers} 
      OpenaiApiKey: ${OpenaiApiKey} 
      AnthropicApiKey: ${AnthropicApiKey}
      AzureOpenaiEndpoint: ${AzureOpenaiEndpoint} 
      AzureOpenaiApiKey: ${AzureOpenaiApiKey} 
      GenAiApiUrl: ${GenAiApiUrl} 
      FastapiApiKey: ${FastapiApiKey} 
      DbPort: ${DbPort} 
      DbUser: ${DbUser} 
      DbName: ${DbName} 
      DbPassword: ${DbPassword} 
      DbHost: ${DbHost} 
      QueueMode: ${QueueMode}
      ServiceBusConnectionString: ${ServiceBusConnectionString} 
      ContentGenerationQueueName: ${ContentGenerationQueueName} 
      ProcessDocumentQueueName: ${ProcessDocumentQueueName} 
      GenerateContentTaskNConcurrent: ${GenerateContentTaskNConcurrent} 
      AzureStorageConnectionString: ${AzureStorageConnectionString} 
      CooldownAzureStorageConnectionString: ${CooldownAzureStorageConnectionString} 
      ScanContainerName: ${ScanContainerName} 
      RerankAzureApiKey: ${RerankAzureApiKey} 
      RerankAzureName: ${RerankAzureName} 
      RerankAzureUrl: ${RerankAzureUrl} 
      SearchEngine: ${SearchEngine} 
      BraveSearchApiKey: ${BraveSearchApiKey} 
      TavilySearchApiKey: ${TavilySearchApiKey} 
      PhoenixProjectName: ${PhoenixProjectName} 
      PhoenixCollectorEndpoint: ${PhoenixCollectorEndpoint} 
      PhoenixPort: ${PhoenixPort} 
      OtelExporterOtlpHeaders: ${OtelExporterOtlpHeaders} 
      ApplicationinsightsConnectionString: ${ApplicationinsightsConnectionString} 
      AzureKeyVaultUrl: ${AzureKeyVaultUrl}
      GenerateDocumentTaskNConcurrent: ${GenerateDocumentTaskNConcurrent}
    restart: always
  background:
    build: 
      context: .
      dockerfile: Dockerfile.background
      target: with-cert
    ports:
      - "80:80"
    volumes:
      - .:/src
    environment:
      CommonEnvironment: ${CommonEnvironment} 
      LoggingLevel: ${LoggingLevel} 
      Workers: ${Workers} 
      OpenaiApiKey: ${OpenaiApiKey} 
      AnthropicApiKey: ${AnthropicApiKey}
      AzureOpenaiEndpoint: ${AzureOpenaiEndpoint} 
      AzureOpenaiApiKey: ${AzureOpenaiApiKey} 
      GenAiApiUrl: ${GenAiApiUrl} 
      FastapiApiKey: ${FastapiApiKey} 
      DbPort: ${DbPort} 
      DbUser: ${DbUser} 
      DbName: ${DbName} 
      DbPassword: ${DbPassword} 
      DbHost: ${DbHost} 
      QueueMode: ${QueueMode}
      ServiceBusConnectionString: ${ServiceBusConnectionString} 
      ContentGenerationQueueName: ${ContentGenerationQueueName} 
      ProcessDocumentQueueName: ${ProcessDocumentQueueName} 
      GenerateContentTaskNConcurrent: ${GenerateContentTaskNConcurrent} 
      AzureStorageConnectionString: ${AzureStorageConnectionString} 
      CooldownAzureStorageConnectionString: ${CooldownAzureStorageConnectionString} 
      ScanContainerName: ${ScanContainerName} 
      RerankAzureApiKey: ${RerankAzureApiKey} 
      RerankAzureName: ${RerankAzureName} 
      RerankAzureUrl: ${RerankAzureUrl} 
      SearchEngine: ${SearchEngine} 
      BraveSearchApiKey: ${BraveSearchApiKey} 
      TavilySearchApiKey: ${TavilySearchApiKey} 
      PhoenixProjectName: ${PhoenixProjectName} 
      PhoenixCollectorEndpoint: ${PhoenixCollectorEndpoint} 
      PhoenixPort: ${PhoenixPort} 
      OtelExporterOtlpHeaders: ${OtelExporterOtlpHeaders} 
      ApplicationinsightsConnectionString: ${ApplicationinsightsConnectionString} 
      AzureKeyVaultUrl: ${AzureKeyVaultUrl}
      GenerateDocumentTaskNConcurrent: ${GenerateDocumentTaskNConcurrent}
    restart: "always"
#TODO: Make the background container from only needed dependencies. Compartimentalize better
