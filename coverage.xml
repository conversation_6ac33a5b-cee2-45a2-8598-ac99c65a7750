<?xml version="1.0" ?>
<coverage version="7.9.2" timestamp="1754564887836" lines-valid="7227" lines-covered="3568" line-rate="0.4937" branches-valid="1340" branches-covered="271" branch-rate="0.2022" complexity="0">
	<!-- Generated by coverage.py: https://coverage.readthedocs.io/en/7.9.2 -->
	<!-- Based on https://raw.githubusercontent.com/cobertura/web/master/htdocs/xml/coverage-04.dtd -->
	<sources>
		<source>/Users/<USER>/Code/ia_gestorcontenidosiagen_be/src</source>
	</sources>
	<packages>
		<package name="." line-rate="0" branch-rate="1" complexity="0">
			<classes>
				<class name="__init__.py" filename="__init__.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines/>
				</class>
				<class name="main.py" filename="main.py" complexity="0" line-rate="0" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="2" hits="0"/>
						<line number="4" hits="0"/>
						<line number="5" hits="0"/>
						<line number="6" hits="0"/>
						<line number="7" hits="0"/>
						<line number="8" hits="0"/>
						<line number="9" hits="0"/>
						<line number="10" hits="0"/>
						<line number="11" hits="0"/>
						<line number="12" hits="0"/>
						<line number="13" hits="0"/>
						<line number="14" hits="0"/>
						<line number="15" hits="0"/>
						<line number="16" hits="0"/>
						<line number="17" hits="0"/>
						<line number="18" hits="0"/>
						<line number="19" hits="0"/>
						<line number="21" hits="0"/>
						<line number="23" hits="0"/>
						<line number="24" hits="0"/>
						<line number="26" hits="0"/>
						<line number="32" hits="0"/>
						<line number="41" hits="0"/>
						<line number="48" hits="0"/>
						<line number="49" hits="0"/>
						<line number="50" hits="0"/>
						<line number="51" hits="0"/>
						<line number="52" hits="0"/>
						<line number="53" hits="0"/>
						<line number="54" hits="0"/>
						<line number="55" hits="0"/>
						<line number="56" hits="0"/>
						<line number="57" hits="0"/>
						<line number="58" hits="0"/>
						<line number="59" hits="0"/>
						<line number="60" hits="0"/>
					</lines>
				</class>
				<class name="version.py" filename="version.py" complexity="0" line-rate="0" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="2" hits="0"/>
						<line number="3" hits="0"/>
						<line number="4" hits="0"/>
					</lines>
				</class>
				<class name="version_iaext.py" filename="version_iaext.py" complexity="0" line-rate="0" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="2" hits="0"/>
						<line number="3" hits="0"/>
						<line number="4" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="api" line-rate="0" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="api/__init__.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines/>
				</class>
				<class name="exception_middleware.py" filename="api/exception_middleware.py" complexity="0" line-rate="0" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="3" hits="0"/>
						<line number="4" hits="0"/>
						<line number="5" hits="0"/>
						<line number="7" hits="0"/>
						<line number="10" hits="0"/>
						<line number="11" hits="0"/>
						<line number="13" hits="0"/>
						<line number="14" hits="0"/>
						<line number="15" hits="0"/>
						<line number="17" hits="0"/>
						<line number="20" hits="0"/>
						<line number="21" hits="0"/>
						<line number="22" hits="0"/>
						<line number="23" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="24,26"/>
						<line number="24" hits="0"/>
						<line number="26" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="api.common" line-rate="0.7408" branch-rate="0.2442" complexity="0">
			<classes>
				<class name="__init__.py" filename="api/common/__init__.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
					</lines>
				</class>
				<class name="dependency_container.py" filename="api/common/dependency_container.py" complexity="0" line-rate="0.7398" branch-rate="0.2442">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="20" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="32" hits="1"/>
						<line number="34" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="35"/>
						<line number="35" hits="1"/>
						<line number="47" hits="0"/>
						<line number="50" hits="0"/>
						<line number="54" hits="0"/>
						<line number="58" hits="0"/>
						<line number="66" hits="0"/>
						<line number="69" hits="0"/>
						<line number="72" hits="0"/>
						<line number="77" hits="0"/>
						<line number="78" hits="1"/>
						<line number="87" hits="1"/>
						<line number="88" hits="1"/>
						<line number="91" hits="1"/>
						<line number="92" hits="1"/>
						<line number="93" hits="1"/>
						<line number="94" hits="1"/>
						<line number="95" hits="1"/>
						<line number="96" hits="1"/>
						<line number="97" hits="1"/>
						<line number="98" hits="1"/>
						<line number="99" hits="1"/>
						<line number="100" hits="1"/>
						<line number="101" hits="1"/>
						<line number="102" hits="1"/>
						<line number="103" hits="1"/>
						<line number="104" hits="1"/>
						<line number="105" hits="1"/>
						<line number="106" hits="1"/>
						<line number="107" hits="1"/>
						<line number="108" hits="1"/>
						<line number="112" hits="1"/>
						<line number="113" hits="1"/>
						<line number="116" hits="0"/>
						<line number="117" hits="0"/>
						<line number="118" hits="0"/>
						<line number="119" hits="0"/>
						<line number="120" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="122"/>
						<line number="121" hits="1"/>
						<line number="122" hits="0"/>
						<line number="123" hits="0"/>
						<line number="124" hits="1"/>
						<line number="125" hits="1"/>
						<line number="126" hits="0"/>
						<line number="127" hits="0"/>
						<line number="128" hits="1"/>
						<line number="129" hits="1"/>
						<line number="130" hits="0"/>
						<line number="131" hits="0"/>
						<line number="133" hits="1"/>
						<line number="134" hits="1"/>
						<line number="135" hits="0"/>
						<line number="137" hits="1"/>
						<line number="138" hits="1"/>
						<line number="139" hits="0"/>
						<line number="141" hits="1"/>
						<line number="142" hits="1"/>
						<line number="143" hits="0"/>
						<line number="145" hits="1"/>
						<line number="146" hits="1"/>
						<line number="147" hits="0"/>
						<line number="149" hits="1"/>
						<line number="165" hits="1"/>
						<line number="166" hits="1"/>
						<line number="167" hits="0"/>
						<line number="169" hits="1"/>
						<line number="180" hits="1"/>
						<line number="181" hits="1"/>
						<line number="182" hits="1"/>
						<line number="184" hits="1"/>
						<line number="192" hits="1"/>
						<line number="193" hits="1"/>
						<line number="196" hits="1"/>
						<line number="200" hits="1"/>
						<line number="215" hits="1"/>
						<line number="216" hits="1"/>
						<line number="217" hits="0"/>
						<line number="219" hits="0"/>
						<line number="221" hits="1"/>
						<line number="222" hits="1"/>
						<line number="223" hits="0"/>
						<line number="225" hits="1"/>
						<line number="233" hits="1"/>
						<line number="234" hits="1"/>
						<line number="235" hits="0"/>
						<line number="237" hits="1"/>
						<line number="243" hits="1"/>
						<line number="244" hits="1"/>
						<line number="245" hits="1"/>
						<line number="247" hits="0"/>
						<line number="249" hits="1"/>
						<line number="250" hits="1"/>
						<line number="251" hits="1"/>
						<line number="253" hits="0"/>
						<line number="255" hits="1"/>
						<line number="256" hits="1"/>
						<line number="257" hits="0"/>
						<line number="259" hits="1"/>
						<line number="261" hits="1"/>
						<line number="262" hits="1"/>
						<line number="263" hits="1"/>
						<line number="265" hits="1"/>
						<line number="269" hits="1"/>
						<line number="270" hits="1"/>
						<line number="273" hits="1"/>
						<line number="275" hits="1"/>
						<line number="291" hits="1"/>
						<line number="292" hits="1"/>
						<line number="295" hits="1"/>
						<line number="297" hits="1"/>
						<line number="305" hits="1"/>
						<line number="306" hits="1"/>
						<line number="307" hits="1"/>
						<line number="309" hits="1"/>
						<line number="325" hits="1"/>
						<line number="326" hits="1"/>
						<line number="327" hits="1"/>
						<line number="329" hits="1"/>
						<line number="331" hits="1"/>
						<line number="332" hits="1"/>
						<line number="333" hits="1"/>
						<line number="335" hits="1"/>
						<line number="351" hits="1"/>
						<line number="352" hits="1"/>
						<line number="353" hits="1"/>
						<line number="357" hits="1"/>
						<line number="373" hits="1"/>
						<line number="374" hits="1"/>
						<line number="375" hits="1"/>
						<line number="377" hits="1"/>
						<line number="383" hits="1"/>
						<line number="384" hits="1"/>
						<line number="385" hits="1"/>
						<line number="387" hits="0"/>
						<line number="389" hits="1"/>
						<line number="390" hits="1"/>
						<line number="391" hits="0"/>
						<line number="393" hits="1"/>
						<line number="407" hits="1"/>
						<line number="408" hits="1"/>
						<line number="409" hits="1"/>
						<line number="417" hits="1"/>
						<line number="418" hits="1"/>
						<line number="419" hits="1"/>
						<line number="423" hits="1"/>
						<line number="445" hits="1"/>
						<line number="446" hits="1"/>
						<line number="447" hits="1"/>
						<line number="449" hits="1"/>
						<line number="450" hits="1"/>
						<line number="451" hits="1"/>
						<line number="453" hits="1"/>
						<line number="454" hits="1"/>
						<line number="455" hits="1"/>
						<line number="457" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="459"/>
						<line number="458" hits="1"/>
						<line number="459" hits="1"/>
						<line number="461" hits="1"/>
						<line number="462" hits="1"/>
						<line number="463" hits="0"/>
						<line number="465" hits="1"/>
						<line number="466" hits="1"/>
						<line number="467" hits="1"/>
						<line number="469" hits="1"/>
						<line number="470" hits="1"/>
						<line number="471" hits="1"/>
						<line number="472" hits="1"/>
						<line number="473" hits="1"/>
						<line number="474" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="477"/>
						<line number="475" hits="1"/>
						<line number="477" hits="1"/>
						<line number="478" hits="1"/>
						<line number="480" hits="1"/>
						<line number="481" hits="1"/>
						<line number="482" hits="1"/>
						<line number="484" hits="1"/>
						<line number="485" hits="1"/>
						<line number="486" hits="0"/>
						<line number="488" hits="1"/>
						<line number="489" hits="1"/>
						<line number="490" hits="0"/>
						<line number="492" hits="1"/>
						<line number="493" hits="1"/>
						<line number="494" hits="0"/>
						<line number="496" hits="1"/>
						<line number="497" hits="1"/>
						<line number="506" hits="1"/>
						<line number="507" hits="1"/>
						<line number="508" hits="1"/>
						<line number="509" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="559"/>
						<line number="510" hits="1"/>
						<line number="511" hits="1"/>
						<line number="518" hits="0"/>
						<line number="519" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="523"/>
						<line number="520" hits="1"/>
						<line number="523" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="524,533"/>
						<line number="524" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="525,528"/>
						<line number="525" hits="1"/>
						<line number="528" hits="1"/>
						<line number="533" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="534,543"/>
						<line number="534" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="535,538"/>
						<line number="535" hits="1"/>
						<line number="538" hits="1"/>
						<line number="543" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="554"/>
						<line number="544" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="548"/>
						<line number="545" hits="1"/>
						<line number="548" hits="1"/>
						<line number="554" hits="1"/>
						<line number="557" hits="1"/>
						<line number="558" hits="1"/>
						<line number="559" hits="0"/>
						<line number="561" hits="1"/>
						<line number="562" hits="1"/>
						<line number="563" hits="1"/>
						<line number="568" hits="1"/>
						<line number="569" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="570,586"/>
						<line number="570" hits="0"/>
						<line number="571" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="572,575"/>
						<line number="572" hits="1"/>
						<line number="575" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="576" hits="1"/>
						<line number="581" hits="1"/>
						<line number="584" hits="1"/>
						<line number="585" hits="1"/>
						<line number="586" hits="1"/>
						<line number="588" hits="1"/>
						<line number="589" hits="1"/>
						<line number="590" hits="1"/>
						<line number="592" hits="1"/>
						<line number="593" hits="1"/>
						<line number="594" hits="0"/>
						<line number="596" hits="1"/>
						<line number="597" hits="1"/>
						<line number="598" hits="1"/>
						<line number="600" hits="1"/>
						<line number="601" hits="1"/>
						<line number="602" hits="1"/>
						<line number="604" hits="1"/>
						<line number="606" hits="1"/>
						<line number="607" hits="1"/>
						<line number="610" hits="1"/>
						<line number="612" hits="0"/>
						<line number="613" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="614,615"/>
						<line number="614" hits="1"/>
						<line number="615" hits="1" branch="true" condition-coverage="0% (0/2)" missing-branches="616,621"/>
						<line number="616" hits="1"/>
						<line number="621" hits="1" branch="true" condition-coverage="0% (0/2)" missing-branches="exit,622"/>
						<line number="622" hits="1"/>
						<line number="628" hits="1"/>
						<line number="629" hits="1"/>
						<line number="630" hits="0"/>
						<line number="631" hits="0"/>
						<line number="632" hits="0"/>
						<line number="633" hits="0"/>
						<line number="634" hits="0"/>
						<line number="636" hits="1"/>
						<line number="637" hits="1"/>
						<line number="638" hits="1"/>
						<line number="640" hits="1"/>
						<line number="644" hits="1"/>
						<line number="645" hits="1"/>
						<line number="646" hits="0"/>
						<line number="648" hits="0"/>
						<line number="649" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="655"/>
						<line number="650" hits="1"/>
						<line number="655" hits="0"/>
						<line number="657" hits="1"/>
						<line number="658" hits="1"/>
						<line number="659" hits="0"/>
						<line number="661" hits="1"/>
						<line number="672" hits="1"/>
						<line number="673" hits="1"/>
						<line number="680" hits="0"/>
						<line number="682" hits="1"/>
						<line number="693" hits="1"/>
						<line number="706" hits="1"/>
						<line number="707" hits="1"/>
						<line number="710" hits="0"/>
						<line number="711" hits="0"/>
						<line number="713" hits="0"/>
						<line number="715" hits="0"/>
						<line number="716" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="719"/>
						<line number="717" hits="1"/>
						<line number="719" hits="1"/>
						<line number="723" hits="1"/>
						<line number="727" hits="1"/>
						<line number="728" hits="1"/>
						<line number="729" hits="0"/>
						<line number="731" hits="0"/>
						<line number="733" hits="1"/>
						<line number="734" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="746"/>
						<line number="735" hits="1"/>
						<line number="736" hits="1"/>
						<line number="742" hits="1"/>
						<line number="746" hits="1"/>
						<line number="752" hits="1"/>
						<line number="753" hits="1"/>
						<line number="754" hits="0"/>
						<line number="758" hits="1"/>
						<line number="759" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="760,765"/>
						<line number="760" hits="1"/>
						<line number="765" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="766,774"/>
						<line number="766" hits="1"/>
						<line number="774" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="782"/>
						<line number="775" hits="1"/>
						<line number="782" hits="1"/>
						<line number="784" hits="1"/>
						<line number="785" hits="1"/>
						<line number="786" hits="1"/>
						<line number="790" hits="1"/>
						<line number="801" hits="1"/>
						<line number="802" hits="1"/>
						<line number="803" hits="1"/>
						<line number="807" hits="1"/>
						<line number="808" hits="1"/>
						<line number="809" hits="1"/>
						<line number="819" hits="1"/>
						<line number="820" hits="1"/>
						<line number="821" hits="0"/>
						<line number="822" hits="0"/>
						<line number="824" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="827"/>
						<line number="825" hits="1"/>
						<line number="827" hits="1"/>
						<line number="828" hits="1"/>
						<line number="829" hits="0"/>
						<line number="832" hits="0"/>
						<line number="833" hits="0"/>
						<line number="835" hits="0"/>
						<line number="837" hits="1"/>
						<line number="838" hits="1"/>
						<line number="839" hits="1"/>
						<line number="840" hits="1"/>
						<line number="851" hits="1"/>
						<line number="852" hits="1"/>
						<line number="853" hits="1"/>
						<line number="857" hits="1"/>
						<line number="858" hits="1"/>
						<line number="859" hits="0"/>
						<line number="861" hits="1"/>
						<line number="862" hits="1"/>
						<line number="863" hits="0"/>
						<line number="864" hits="0"/>
						<line number="865" hits="0"/>
						<line number="866" hits="0"/>
						<line number="867" hits="0"/>
						<line number="868" hits="0"/>
						<line number="869" hits="0"/>
						<line number="870" hits="0"/>
						<line number="871" hits="0"/>
						<line number="872" hits="1"/>
						<line number="885" hits="1"/>
						<line number="900" hits="0"/>
						<line number="901" hits="0"/>
						<line number="902" hits="0"/>
						<line number="903" hits="0"/>
						<line number="904" hits="0"/>
						<line number="905" hits="0"/>
						<line number="906" hits="0"/>
						<line number="907" hits="1"/>
						<line number="908" hits="1"/>
						<line number="910" hits="1"/>
						<line number="913" hits="0"/>
						<line number="917" hits="0"/>
						<line number="919" hits="1"/>
						<line number="920" hits="1"/>
						<line number="921" hits="0"/>
						<line number="922" hits="0"/>
						<line number="923" hits="1"/>
						<line number="924" hits="1" branch="true" condition-coverage="0% (0/2)" missing-branches="925,933"/>
						<line number="925" hits="1"/>
						<line number="929" hits="1"/>
						<line number="930" hits="1"/>
						<line number="931" hits="0"/>
						<line number="933" hits="0"/>
						<line number="935" hits="1"/>
						<line number="936" hits="1"/>
						<line number="937" hits="0"/>
						<line number="938" hits="0"/>
						<line number="939" hits="0"/>
						<line number="941" hits="1"/>
						<line number="942" hits="1"/>
						<line number="943" hits="1"/>
						<line number="944" hits="1"/>
						<line number="947" hits="1"/>
						<line number="948" hits="1"/>
						<line number="949" hits="1"/>
						<line number="950" hits="1"/>
						<line number="952" hits="1"/>
						<line number="953" hits="1"/>
						<line number="954" hits="0"/>
						<line number="955" hits="0"/>
						<line number="956" hits="0"/>
						<line number="957" hits="1"/>
						<line number="961" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="exit"/>
						<line number="962" hits="1"/>
						<line number="967" hits="0"/>
						<line number="968" hits="0"/>
						<line number="970" hits="1"/>
						<line number="971" hits="1"/>
						<line number="972" hits="0"/>
						<line number="974" hits="1"/>
						<line number="975" hits="1"/>
						<line number="976" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="exit"/>
						<line number="977" hits="1"/>
						<line number="986" hits="1"/>
						<line number="987" hits="1"/>
						<line number="988" hits="1"/>
						<line number="989" hits="1"/>
						<line number="990" hits="1"/>
						<line number="997" hits="0"/>
						<line number="998" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="999,1002"/>
						<line number="999" hits="0"/>
						<line number="1002" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="1003,1012"/>
						<line number="1003" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="1004,1007"/>
						<line number="1004" hits="1"/>
						<line number="1007" hits="0"/>
						<line number="1012" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="1013,1022"/>
						<line number="1013" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="1014,1017"/>
						<line number="1014" hits="1"/>
						<line number="1017" hits="1"/>
						<line number="1022" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="1023,1031"/>
						<line number="1023" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="1024,1025"/>
						<line number="1024" hits="0"/>
						<line number="1025" hits="1"/>
						<line number="1031" hits="1"/>
						<line number="1034" hits="1"/>
						<line number="1035" hits="0"/>
						<line number="1037" hits="1"/>
						<line number="1038" hits="1"/>
						<line number="1039" hits="0"/>
						<line number="1040" hits="0"/>
						<line number="1041" hits="1"/>
						<line number="1046" hits="1"/>
						<line number="1047" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="1051"/>
						<line number="1048" hits="1"/>
						<line number="1051" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="1052,1060"/>
						<line number="1052" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="1056"/>
						<line number="1053" hits="1"/>
						<line number="1056" hits="1"/>
						<line number="1060" hits="1"/>
						<line number="1061" hits="1"/>
						<line number="1062" hits="1"/>
						<line number="1064" hits="1"/>
						<line number="1065" hits="1"/>
						<line number="1066" hits="1"/>
						<line number="1070" hits="1"/>
						<line number="1071" hits="1"/>
						<line number="1072" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="1073,1074"/>
						<line number="1073" hits="0"/>
						<line number="1074" hits="0"/>
						<line number="1076" hits="1"/>
						<line number="1077" hits="1"/>
						<line number="1078" hits="0"/>
						<line number="1079" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="1080,1086"/>
						<line number="1080" hits="1"/>
						<line number="1086" hits="1"/>
						<line number="1092" hits="1"/>
						<line number="1093" hits="1"/>
						<line number="1094" hits="0"/>
						<line number="1095" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="1096,1102"/>
						<line number="1096" hits="1"/>
						<line number="1102" hits="0"/>
						<line number="1108" hits="1"/>
						<line number="1109" hits="1"/>
						<line number="1110" hits="1"/>
						<line number="1112" hits="1"/>
						<line number="1113" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="1117"/>
						<line number="1114" hits="1"/>
						<line number="1117" hits="0"/>
						<line number="1122" hits="1"/>
						<line number="1123" hits="1"/>
						<line number="1124" hits="1"/>
						<line number="1126" hits="1"/>
						<line number="1127" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="1131"/>
						<line number="1128" hits="1"/>
						<line number="1131" hits="0"/>
						<line number="1136" hits="1"/>
						<line number="1137" hits="1"/>
						<line number="1138" hits="1"/>
						<line number="1142" hits="1"/>
						<line number="1152" hits="1"/>
						<line number="1153" hits="1"/>
						<line number="1154" hits="1"/>
						<line number="1158" hits="1"/>
						<line number="1164" hits="1"/>
						<line number="1165" hits="1"/>
						<line number="1166" hits="0"/>
						<line number="1170" hits="0"/>
						<line number="1172" hits="1"/>
						<line number="1173" hits="1"/>
						<line number="1174" hits="0"/>
						<line number="1178" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="api.common.dependencies" line-rate="0" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="api/common/dependencies/__init__.py" complexity="0" line-rate="0" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="3" hits="0"/>
					</lines>
				</class>
				<class name="get_session.py" filename="api/common/dependencies/get_session.py" complexity="0" line-rate="0" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="3" hits="0"/>
						<line number="6" hits="0"/>
						<line number="7" hits="0"/>
						<line number="8" hits="0"/>
						<line number="9" hits="0"/>
					</lines>
				</class>
				<class name="security.py" filename="api/common/dependencies/security.py" complexity="0" line-rate="0" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="3" hits="0"/>
						<line number="5" hits="0"/>
						<line number="8" hits="0"/>
						<line number="9" hits="0"/>
						<line number="10" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="exit,11"/>
						<line number="11" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="api.common.services" line-rate="0.8327" branch-rate="0.5355" complexity="0">
			<classes>
				<class name="__init__.py" filename="api/common/services/__init__.py" complexity="0" line-rate="0.8889" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="0"/>
						<line number="8" hits="0"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="28" hits="1"/>
						<line number="33" hits="1"/>
						<line number="35" hits="1"/>
						<line number="36" hits="1"/>
						<line number="38" hits="1"/>
					</lines>
				</class>
				<class name="ai_tracer.py" filename="api/common/services/ai_tracer.py" complexity="0" line-rate="1" branch-rate="0.9545">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="17" hits="1"/>
						<line number="22" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="30" hits="1"/>
						<line number="43" hits="1"/>
						<line number="44" hits="1"/>
						<line number="56" hits="1"/>
						<line number="57" hits="1"/>
						<line number="58" hits="1"/>
						<line number="59" hits="1"/>
						<line number="61" hits="1"/>
						<line number="75" hits="1"/>
						<line number="76" hits="1"/>
						<line number="77" hits="1"/>
						<line number="87" hits="1"/>
						<line number="88" hits="1"/>
						<line number="89" hits="1"/>
						<line number="90" hits="1"/>
						<line number="92" hits="1"/>
						<line number="101" hits="1"/>
						<line number="102" hits="1"/>
						<line number="103" hits="1"/>
						<line number="104" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="105" hits="1"/>
						<line number="106" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="107" hits="1"/>
						<line number="108" hits="1"/>
						<line number="109" hits="1"/>
						<line number="110" hits="1"/>
						<line number="114" hits="1"/>
						<line number="133" hits="1"/>
						<line number="134" hits="1"/>
						<line number="137" hits="1"/>
						<line number="146" hits="1"/>
						<line number="147" hits="1"/>
						<line number="149" hits="1"/>
						<line number="159" hits="1"/>
						<line number="160" hits="1"/>
						<line number="161" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="162" hits="1"/>
						<line number="163" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="164" hits="1"/>
						<line number="165" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="166" hits="1"/>
						<line number="167" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="168" hits="1"/>
						<line number="169" hits="1"/>
						<line number="171" hits="1"/>
						<line number="178" hits="1"/>
						<line number="179" hits="1"/>
						<line number="182" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="183" hits="1"/>
						<line number="184" hits="1"/>
						<line number="185" hits="1"/>
						<line number="187" hits="1"/>
						<line number="188" hits="1"/>
						<line number="189" hits="1"/>
						<line number="196" hits="1"/>
						<line number="197" hits="1"/>
						<line number="198" hits="1"/>
						<line number="207" hits="1"/>
						<line number="210" hits="1"/>
						<line number="212" hits="1"/>
						<line number="213" hits="1"/>
						<line number="222" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="224" hits="1"/>
						<line number="225" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="231"/>
						<line number="226" hits="1"/>
						<line number="231" hits="1"/>
						<line number="235" hits="1"/>
						<line number="236" hits="1"/>
						<line number="238" hits="1"/>
						<line number="248" hits="1"/>
						<line number="249" hits="1"/>
						<line number="250" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="251" hits="1"/>
						<line number="253" hits="1"/>
						<line number="259" hits="1"/>
						<line number="260" hits="1"/>
						<line number="261" hits="1"/>
						<line number="262" hits="1"/>
						<line number="264" hits="1"/>
						<line number="270" hits="1"/>
						<line number="271" hits="1"/>
						<line number="274" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="275" hits="1"/>
						<line number="278" hits="1"/>
					</lines>
				</class>
				<class name="index_repository.py" filename="api/common/services/index_repository.py" complexity="0" line-rate="0.7214" branch-rate="0.3693">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="25" hits="1"/>
						<line number="39" hits="1"/>
						<line number="40" hits="1"/>
						<line number="43" hits="1"/>
						<line number="44" hits="1"/>
						<line number="49" hits="1"/>
						<line number="50" hits="1"/>
						<line number="53" hits="1"/>
						<line number="54" hits="1"/>
						<line number="57" hits="1"/>
						<line number="58" hits="1"/>
						<line number="61" hits="1"/>
						<line number="62" hits="1"/>
						<line number="65" hits="1"/>
						<line number="66" hits="1"/>
						<line number="69" hits="1"/>
						<line number="70" hits="1"/>
						<line number="73" hits="1"/>
						<line number="74" hits="1"/>
						<line number="75" hits="1"/>
						<line number="76" hits="1"/>
						<line number="77" hits="1"/>
						<line number="79" hits="1"/>
						<line number="85" hits="1"/>
						<line number="86" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="87" hits="1"/>
						<line number="88" hits="1"/>
						<line number="89" hits="1"/>
						<line number="92" hits="1"/>
						<line number="93" hits="1"/>
						<line number="94" hits="1"/>
						<line number="96" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="97" hits="1"/>
						<line number="98" hits="1"/>
						<line number="101" hits="1"/>
						<line number="102" hits="1"/>
						<line number="104" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="105" hits="1"/>
						<line number="108" hits="1"/>
						<line number="109" hits="1"/>
						<line number="111" hits="1"/>
						<line number="113" hits="1"/>
						<line number="116" hits="1"/>
						<line number="117" hits="1"/>
						<line number="119" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="120"/>
						<line number="120" hits="1"/>
						<line number="121" hits="1"/>
						<line number="123" hits="1"/>
						<line number="126" hits="1"/>
						<line number="127" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="128" hits="1"/>
						<line number="129" hits="1"/>
						<line number="130" hits="1"/>
						<line number="139" hits="1"/>
						<line number="140" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="141" hits="1"/>
						<line number="144" hits="1"/>
						<line number="147" hits="1"/>
						<line number="153" hits="1"/>
						<line number="154" hits="1"/>
						<line number="156" hits="1"/>
						<line number="159" hits="1"/>
						<line number="160" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="161" hits="1"/>
						<line number="162" hits="1"/>
						<line number="163" hits="1"/>
						<line number="169" hits="1"/>
						<line number="170" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="171" hits="1"/>
						<line number="172" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="173" hits="1"/>
						<line number="175" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="176" hits="1"/>
						<line number="177" hits="1"/>
						<line number="179" hits="1"/>
						<line number="182" hits="1"/>
						<line number="183" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="184" hits="1"/>
						<line number="185" hits="1"/>
						<line number="186" hits="1"/>
						<line number="187" hits="1"/>
						<line number="192" hits="1"/>
						<line number="193" hits="1"/>
						<line number="194" hits="1"/>
						<line number="195" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="196" hits="1"/>
						<line number="199" hits="1"/>
						<line number="200" hits="1"/>
						<line number="210" hits="1"/>
						<line number="211" hits="1"/>
						<line number="212" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="213" hits="1"/>
						<line number="214" hits="1"/>
						<line number="227" hits="1"/>
						<line number="228" hits="1"/>
						<line number="231" hits="1"/>
						<line number="232" hits="1"/>
						<line number="234" hits="1"/>
						<line number="235" hits="1"/>
						<line number="237" hits="1"/>
						<line number="244" hits="1"/>
						<line number="245" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="246" hits="1"/>
						<line number="248" hits="1"/>
						<line number="249" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="250" hits="1"/>
						<line number="254" hits="1"/>
						<line number="256" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="257" hits="1"/>
						<line number="261" hits="1"/>
						<line number="262" hits="1"/>
						<line number="264" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="265" hits="1"/>
						<line number="268" hits="1"/>
						<line number="269" hits="1"/>
						<line number="270" hits="1"/>
						<line number="271" hits="1"/>
						<line number="273" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="274" hits="1"/>
						<line number="277" hits="1"/>
						<line number="278" hits="1"/>
						<line number="280" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="281" hits="1"/>
						<line number="284" hits="1"/>
						<line number="285" hits="1"/>
						<line number="286" hits="1"/>
						<line number="287" hits="1"/>
						<line number="288" hits="1"/>
						<line number="289" hits="1"/>
						<line number="290" hits="1"/>
						<line number="291" hits="1"/>
						<line number="292" hits="1"/>
						<line number="293" hits="1"/>
						<line number="294" hits="1"/>
						<line number="296" hits="1"/>
						<line number="299" hits="1"/>
						<line number="300" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="301" hits="1"/>
						<line number="302" hits="1"/>
						<line number="303" hits="1"/>
						<line number="304" hits="1"/>
						<line number="305" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="306" hits="1"/>
						<line number="307" hits="1"/>
						<line number="309" hits="1"/>
						<line number="315" hits="1"/>
						<line number="316" hits="1"/>
						<line number="317" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="318" hits="1"/>
						<line number="320" hits="1"/>
						<line number="321" hits="1"/>
						<line number="322" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="326"/>
						<line number="323" hits="1"/>
						<line number="324" hits="1"/>
						<line number="326" hits="1"/>
						<line number="327" hits="1"/>
						<line number="328" hits="1"/>
						<line number="330" hits="1"/>
						<line number="331" hits="1"/>
						<line number="333" hits="1"/>
						<line number="337" hits="1" branch="true" condition-coverage="0% (0/2)" missing-branches="338,340"/>
						<line number="338" hits="1"/>
						<line number="340" hits="1"/>
						<line number="341" hits="1"/>
						<line number="343" hits="1"/>
						<line number="345" hits="1"/>
						<line number="346" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="347"/>
						<line number="347" hits="1"/>
						<line number="348" hits="1"/>
						<line number="350" hits="1"/>
						<line number="359" hits="0"/>
						<line number="360" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="361,362"/>
						<line number="361" hits="1"/>
						<line number="362" hits="0"/>
						<line number="363" hits="1"/>
						<line number="366" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="367,368"/>
						<line number="367" hits="1"/>
						<line number="368" hits="1"/>
						<line number="369" hits="1"/>
						<line number="371" hits="1" branch="true" condition-coverage="0% (0/2)" missing-branches="372,373"/>
						<line number="372" hits="1"/>
						<line number="373" hits="0"/>
						<line number="375" hits="1"/>
						<line number="376" hits="0"/>
						<line number="377" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="379"/>
						<line number="378" hits="1"/>
						<line number="379" hits="1"/>
						<line number="381" hits="1"/>
						<line number="384" hits="1"/>
						<line number="389" hits="1" branch="true" condition-coverage="0% (0/2)" missing-branches="390,391"/>
						<line number="390" hits="1"/>
						<line number="391" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="392"/>
						<line number="392" hits="1"/>
						<line number="393" hits="1"/>
						<line number="395" hits="1"/>
						<line number="402" hits="1"/>
						<line number="408" hits="1" branch="true" condition-coverage="0% (0/2)" missing-branches="409,410"/>
						<line number="409" hits="1"/>
						<line number="410" hits="1" branch="true" condition-coverage="0% (0/2)" missing-branches="411,412"/>
						<line number="411" hits="1"/>
						<line number="412" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="413"/>
						<line number="413" hits="1"/>
						<line number="414" hits="1"/>
						<line number="416" hits="1"/>
						<line number="424" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="425" hits="1"/>
						<line number="426" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="428"/>
						<line number="427" hits="1"/>
						<line number="428" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="433"/>
						<line number="429" hits="1"/>
						<line number="433" hits="1"/>
						<line number="435" hits="1"/>
						<line number="438" hits="0"/>
						<line number="439" hits="1" branch="true" condition-coverage="0% (0/2)" missing-branches="440,441"/>
						<line number="440" hits="0"/>
						<line number="441" hits="1"/>
						<line number="442" hits="1"/>
						<line number="443" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="444,445"/>
						<line number="444" hits="0"/>
						<line number="445" hits="0"/>
						<line number="446" hits="1"/>
						<line number="447" hits="1"/>
						<line number="449" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="450" hits="1"/>
						<line number="451" hits="1"/>
						<line number="453" hits="1"/>
						<line number="454" hits="1"/>
						<line number="458" hits="1"/>
						<line number="460" hits="1"/>
						<line number="463" hits="0"/>
						<line number="464" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="465"/>
						<line number="465" hits="0"/>
						<line number="466" hits="1"/>
						<line number="467" hits="1"/>
						<line number="468" hits="1" branch="true" condition-coverage="0% (0/2)" missing-branches="469,470"/>
						<line number="469" hits="1"/>
						<line number="470" hits="1"/>
						<line number="471" hits="1"/>
						<line number="472" hits="1"/>
						<line number="474" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="475"/>
						<line number="475" hits="1"/>
						<line number="476" hits="1"/>
						<line number="478" hits="1"/>
						<line number="484" hits="0"/>
						<line number="485" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="486"/>
						<line number="486" hits="0"/>
						<line number="487" hits="1"/>
						<line number="488" hits="1"/>
						<line number="489" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="491"/>
						<line number="490" hits="1"/>
						<line number="491" hits="1"/>
						<line number="492" hits="1"/>
						<line number="493" hits="1"/>
						<line number="495" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="496"/>
						<line number="496" hits="1"/>
						<line number="497" hits="1"/>
						<line number="499" hits="1"/>
						<line number="520" hits="0"/>
						<line number="521" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="522,523"/>
						<line number="522" hits="0"/>
						<line number="523" hits="0"/>
						<line number="524" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="525,538"/>
						<line number="525" hits="0"/>
						<line number="526" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="527,532"/>
						<line number="527" hits="0"/>
						<line number="530" hits="0"/>
						<line number="532" hits="1"/>
						<line number="535" hits="1"/>
						<line number="536" hits="1"/>
						<line number="538" hits="1"/>
						<line number="539" hits="1"/>
						<line number="540" hits="1"/>
						<line number="542" hits="1"/>
						<line number="543" hits="1"/>
						<line number="545" hits="1"/>
						<line number="552" hits="0"/>
						<line number="555" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="556,578"/>
						<line number="556" hits="0"/>
						<line number="557" hits="0"/>
						<line number="560" hits="0"/>
						<line number="561" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="562,583"/>
						<line number="562" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="563,576"/>
						<line number="563" hits="0"/>
						<line number="568" hits="0"/>
						<line number="569" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="570,576"/>
						<line number="570" hits="0"/>
						<line number="573" hits="0"/>
						<line number="576" hits="1"/>
						<line number="578" hits="1"/>
						<line number="581" hits="1"/>
						<line number="583" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="584" hits="1"/>
						<line number="585" hits="1" branch="true" condition-coverage="0% (0/2)" missing-branches="586,588"/>
						<line number="586" hits="1"/>
						<line number="587" hits="1"/>
						<line number="588" hits="1"/>
						<line number="590" hits="1"/>
						<line number="612" hits="0"/>
						<line number="613" hits="0"/>
						<line number="614" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="615,616"/>
						<line number="615" hits="0"/>
						<line number="616" hits="0"/>
						<line number="617" hits="1"/>
						<line number="622" hits="0"/>
						<line number="623" hits="0"/>
						<line number="625" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="626,628"/>
						<line number="626" hits="0"/>
						<line number="628" hits="0"/>
						<line number="630" hits="1"/>
						<line number="638" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="639,676"/>
						<line number="639" hits="0"/>
						<line number="641" hits="0"/>
						<line number="644" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="645,649"/>
						<line number="645" hits="0"/>
						<line number="649" hits="1"/>
						<line number="652" hits="1"/>
						<line number="653" hits="1"/>
						<line number="654" hits="0"/>
						<line number="656" hits="0"/>
						<line number="657" hits="0"/>
						<line number="658" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="659,663"/>
						<line number="659" hits="0"/>
						<line number="663" hits="1"/>
						<line number="674" hits="1"/>
						<line number="676" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="677,681"/>
						<line number="677" hits="0"/>
						<line number="678" hits="0"/>
						<line number="681" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="682,684"/>
						<line number="682" hits="1"/>
						<line number="684" hits="0"/>
						<line number="686" hits="1"/>
						<line number="688" hits="1"/>
						<line number="690" hits="1"/>
						<line number="691" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="700"/>
						<line number="692" hits="1"/>
						<line number="693" hits="1"/>
						<line number="695" hits="1"/>
						<line number="696" hits="1" branch="true" condition-coverage="0% (0/2)" missing-branches="697,700"/>
						<line number="697" hits="1"/>
						<line number="698" hits="1"/>
						<line number="700" hits="1"/>
						<line number="702" hits="1"/>
						<line number="714" hits="0"/>
						<line number="715" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="716,718"/>
						<line number="716" hits="0"/>
						<line number="718" hits="0"/>
						<line number="719" hits="0"/>
						<line number="720" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="721,728"/>
						<line number="721" hits="0"/>
						<line number="728" hits="0"/>
						<line number="733" hits="0"/>
						<line number="735" hits="1"/>
						<line number="740" hits="0"/>
						<line number="746" hits="0"/>
						<line number="747" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="748,776"/>
						<line number="748" hits="0"/>
						<line number="749" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="750,776"/>
						<line number="750" hits="0"/>
						<line number="756" hits="0"/>
						<line number="758" hits="1"/>
						<line number="763" hits="0"/>
						<line number="769" hits="1"/>
						<line number="776" hits="1"/>
						<line number="783" hits="1"/>
						<line number="784" hits="1"/>
						<line number="786" hits="1"/>
						<line number="787" hits="1"/>
						<line number="789" hits="1"/>
						<line number="790" hits="1" branch="true" condition-coverage="0% (0/2)" missing-branches="791,808"/>
						<line number="791" hits="1" branch="true" condition-coverage="0% (0/2)" missing-branches="792,793"/>
						<line number="792" hits="0"/>
						<line number="793" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="794,795"/>
						<line number="794" hits="0"/>
						<line number="795" hits="1" branch="true" condition-coverage="0% (0/2)" missing-branches="799,806"/>
						<line number="799" hits="1"/>
						<line number="804" hits="0"/>
						<line number="806" hits="1"/>
						<line number="807" hits="1"/>
						<line number="808" hits="0"/>
						<line number="810" hits="1"/>
						<line number="811" hits="0"/>
						<line number="816" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="exit,817"/>
						<line number="817" hits="1"/>
						<line number="826" hits="1"/>
						<line number="827" hits="1"/>
						<line number="828" hits="1"/>
						<line number="832" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="exit"/>
						<line number="833" hits="1"/>
						<line number="835" hits="1"/>
						<line number="856" hits="0"/>
						<line number="857" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="858,859"/>
						<line number="858" hits="0"/>
						<line number="859" hits="0"/>
						<line number="860" hits="1"/>
						<line number="868" hits="1"/>
						<line number="869" hits="1"/>
						<line number="877" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="878,879"/>
						<line number="878" hits="0"/>
						<line number="879" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="880,882"/>
						<line number="880" hits="0"/>
						<line number="882" hits="0"/>
						<line number="889" hits="0"/>
						<line number="890" hits="0"/>
						<line number="891" hits="0"/>
						<line number="892" hits="1"/>
						<line number="895" hits="1"/>
						<line number="898" hits="1"/>
						<line number="909" hits="1"/>
						<line number="910" hits="0"/>
						<line number="911" hits="1"/>
						<line number="912" hits="1"/>
						<line number="913" hits="1"/>
						<line number="915" hits="1"/>
						<line number="916" hits="1"/>
						<line number="918" hits="1"/>
						<line number="921" hits="0"/>
						<line number="922" hits="0"/>
						<line number="929" hits="0"/>
						<line number="930" hits="1"/>
						<line number="931" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="932,934"/>
						<line number="932" hits="1"/>
						<line number="933" hits="1"/>
						<line number="934" hits="1"/>
						<line number="935" hits="1" branch="true" condition-coverage="0% (0/2)" missing-branches="936,938"/>
						<line number="936" hits="1"/>
						<line number="937" hits="1"/>
						<line number="938" hits="1"/>
						<line number="939" hits="1"/>
						<line number="942" hits="1"/>
						<line number="944" hits="1"/>
						<line number="950" hits="0"/>
						<line number="951" hits="0"/>
						<line number="952" hits="0"/>
						<line number="955" hits="0"/>
						<line number="956" hits="0"/>
						<line number="957" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="958,963"/>
						<line number="958" hits="0"/>
						<line number="961" hits="1"/>
						<line number="962" hits="0"/>
						<line number="963" hits="1"/>
						<line number="964" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="965" hits="1"/>
						<line number="966" hits="1"/>
						<line number="970" hits="1"/>
						<line number="973" hits="1"/>
						<line number="975" hits="1"/>
						<line number="983" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="984,986"/>
						<line number="984" hits="0"/>
						<line number="986" hits="0"/>
						<line number="987" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="988,990"/>
						<line number="988" hits="0"/>
						<line number="990" hits="0"/>
						<line number="991" hits="0"/>
						<line number="1025" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="1026,1027"/>
						<line number="1026" hits="0"/>
						<line number="1027" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="1028,1029"/>
						<line number="1028" hits="0"/>
						<line number="1029" hits="0"/>
						<line number="1030" hits="0"/>
						<line number="1032" hits="0"/>
						<line number="1033" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="1034,1041"/>
						<line number="1034" hits="0"/>
						<line number="1035" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="1036,1039"/>
						<line number="1036" hits="0"/>
						<line number="1039" hits="0"/>
						<line number="1041" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="1044,1046"/>
						<line number="1044" hits="0"/>
						<line number="1046" hits="0"/>
						<line number="1048" hits="0"/>
						<line number="1049" hits="0"/>
					</lines>
				</class>
				<class name="llm.py" filename="api/common/services/llm.py" complexity="0" line-rate="0.9767" branch-rate="0.9167">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="8" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="60" hits="1"/>
						<line number="71" hits="1"/>
						<line number="72" hits="1"/>
						<line number="73" hits="1"/>
						<line number="74" hits="1"/>
						<line number="75" hits="1"/>
						<line number="76" hits="1"/>
						<line number="77" hits="1"/>
						<line number="78" hits="1"/>
						<line number="80" hits="1"/>
						<line number="91" hits="1"/>
						<line number="93" hits="1"/>
						<line number="101" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="102" hits="1"/>
						<line number="104" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="116" hits="1"/>
						<line number="117" hits="1"/>
						<line number="118" hits="1"/>
						<line number="119" hits="1"/>
						<line number="121" hits="1"/>
						<line number="128" hits="1"/>
						<line number="129" hits="1"/>
						<line number="130" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="131" hits="1"/>
						<line number="132" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="133" hits="1"/>
						<line number="134" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="135" hits="1"/>
						<line number="136" hits="1"/>
						<line number="137" hits="1"/>
						<line number="139" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="144" hits="1"/>
						<line number="152" hits="1"/>
						<line number="158" hits="1"/>
						<line number="160" hits="1"/>
						<line number="168" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="169" hits="1"/>
						<line number="179" hits="1"/>
						<line number="188" hits="1"/>
						<line number="190" hits="1"/>
						<line number="199" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="200" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="201" hits="1"/>
						<line number="210" hits="1"/>
						<line number="216" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="217" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="218" hits="1"/>
						<line number="225" hits="1"/>
						<line number="230" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="247"/>
						<line number="231" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="232" hits="1"/>
						<line number="240" hits="1"/>
						<line number="247" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="272"/>
						<line number="248" hits="1"/>
						<line number="249" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="261"/>
						<line number="250" hits="1"/>
						<line number="261" hits="0"/>
						<line number="272" hits="0"/>
						<line number="276" hits="1"/>
						<line number="277" hits="1"/>
						<line number="278" hits="1"/>
						<line number="279" hits="1"/>
						<line number="280" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="281" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="282" hits="1"/>
						<line number="283" hits="1"/>
						<line number="284" hits="1"/>
						<line number="286" hits="1"/>
						<line number="287" hits="1"/>
						<line number="288" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="289" hits="1"/>
						<line number="290" hits="1"/>
						<line number="291" hits="1"/>
						<line number="292" hits="1"/>
						<line number="295" hits="1"/>
						<line number="296" hits="1"/>
						<line number="298" hits="1"/>
						<line number="299" hits="1"/>
					</lines>
				</class>
				<class name="mail_sender.py" filename="api/common/services/mail_sender.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="6" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="19" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="24" hits="1"/>
						<line number="28" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="38" hits="1"/>
						<line number="39" hits="1"/>
						<line number="42" hits="1"/>
						<line number="43" hits="1"/>
						<line number="47" hits="1"/>
						<line number="50" hits="1"/>
						<line number="51" hits="1"/>
						<line number="55" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="56" hits="1"/>
						<line number="57" hits="1"/>
						<line number="65" hits="1"/>
						<line number="66" hits="1"/>
						<line number="69" hits="1"/>
						<line number="70" hits="1"/>
					</lines>
				</class>
				<class name="markdown_service.py" filename="api/common/services/markdown_service.py" complexity="0" line-rate="0" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="2" hits="0"/>
						<line number="4" hits="0"/>
						<line number="6" hits="0"/>
						<line number="7" hits="0"/>
						<line number="10" hits="0"/>
						<line number="14" hits="0"/>
						<line number="17" hits="0"/>
						<line number="20" hits="0"/>
						<line number="23" hits="0"/>
						<line number="28" hits="0"/>
						<line number="34" hits="0"/>
						<line number="35" hits="0"/>
						<line number="37" hits="0"/>
						<line number="58" hits="0"/>
						<line number="60" hits="0"/>
						<line number="61" hits="0"/>
						<line number="62" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="63,83"/>
						<line number="63" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="64,86"/>
						<line number="64" hits="0"/>
						<line number="81" hits="0"/>
						<line number="83" hits="0"/>
						<line number="84" hits="0"/>
						<line number="86" hits="0"/>
						<line number="87" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="88,91"/>
						<line number="88" hits="0"/>
						<line number="91" hits="0"/>
						<line number="93" hits="0"/>
						<line number="108" hits="0"/>
						<line number="110" hits="0"/>
						<line number="116" hits="0"/>
						<line number="146" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="147,148"/>
						<line number="147" hits="0"/>
						<line number="148" hits="0"/>
						<line number="154" hits="0"/>
						<line number="156" hits="0"/>
						<line number="162" hits="0"/>
						<line number="163" hits="0"/>
						<line number="165" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="166,183"/>
						<line number="166" hits="0"/>
						<line number="168" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="169,171"/>
						<line number="169" hits="0"/>
						<line number="171" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="172,174"/>
						<line number="172" hits="0"/>
						<line number="174" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="175,177"/>
						<line number="175" hits="0"/>
						<line number="177" hits="0"/>
						<line number="182" hits="0"/>
						<line number="183" hits="0"/>
						<line number="185" hits="0"/>
						<line number="194" hits="0"/>
						<line number="195" hits="0"/>
						<line number="201" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="202,204"/>
						<line number="202" hits="0"/>
						<line number="204" hits="0"/>
						<line number="209" hits="0"/>
						<line number="210" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="211,212"/>
						<line number="211" hits="0"/>
						<line number="212" hits="0"/>
					</lines>
				</class>
				<class name="observability.py" filename="api/common/services/observability.py" complexity="0" line-rate="0.8778" branch-rate="0.85">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="7" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="20" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="25" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="30,31"/>
						<line number="30" hits="0"/>
						<line number="31" hits="0"/>
						<line number="33" hits="0"/>
						<line number="34" hits="0"/>
						<line number="39" hits="1"/>
						<line number="40" hits="1"/>
						<line number="49" hits="1"/>
						<line number="50" hits="1"/>
						<line number="51" hits="1"/>
						<line number="52" hits="1"/>
						<line number="53" hits="1"/>
						<line number="54" hits="1"/>
						<line number="56" hits="1"/>
						<line number="57" hits="1"/>
						<line number="58" hits="1"/>
						<line number="60" hits="1"/>
						<line number="61" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="62" hits="1"/>
						<line number="63" hits="1"/>
						<line number="64" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="65" hits="1"/>
						<line number="68" hits="1"/>
						<line number="69" hits="1"/>
						<line number="70" hits="1"/>
						<line number="73" hits="1"/>
						<line number="74" hits="1"/>
						<line number="76" hits="1"/>
						<line number="82" hits="1"/>
						<line number="83" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="84"/>
						<line number="84" hits="0"/>
						<line number="85" hits="0"/>
						<line number="86" hits="0"/>
						<line number="96" hits="1"/>
						<line number="97" hits="1"/>
						<line number="100" hits="1"/>
						<line number="101" hits="1"/>
						<line number="103" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="104" hits="1"/>
						<line number="107" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="108" hits="1"/>
						<line number="110" hits="1"/>
						<line number="114" hits="1"/>
						<line number="116" hits="1"/>
						<line number="119" hits="1"/>
						<line number="120" hits="1"/>
						<line number="123" hits="1"/>
						<line number="124" hits="0"/>
						<line number="125" hits="0"/>
						<line number="127" hits="1"/>
						<line number="128" hits="1"/>
						<line number="129" hits="1"/>
						<line number="130" hits="1"/>
						<line number="131" hits="1"/>
						<line number="135" hits="1"/>
						<line number="136" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="137" hits="1"/>
						<line number="140" hits="1"/>
						<line number="142" hits="1"/>
						<line number="143" hits="1"/>
						<line number="144" hits="1"/>
						<line number="145" hits="0"/>
						<line number="147" hits="1"/>
						<line number="148" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="149" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="150" hits="1"/>
						<line number="152" hits="1"/>
						<line number="156" hits="1"/>
						<line number="160" hits="1"/>
						<line number="162" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="163" hits="1"/>
						<line number="165" hits="1"/>
					</lines>
				</class>
				<class name="search_agent.py" filename="api/common/services/search_agent.py" complexity="0" line-rate="0.9882" branch-rate="0.85">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="20" hits="1"/>
						<line number="24" hits="1"/>
						<line number="28" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="34" hits="1"/>
						<line number="35" hits="1"/>
						<line number="36" hits="1"/>
						<line number="37" hits="1"/>
						<line number="40" hits="1"/>
						<line number="41" hits="1"/>
						<line number="42" hits="1"/>
						<line number="45" hits="1"/>
						<line number="46" hits="1"/>
						<line number="49" hits="1"/>
						<line number="52" hits="1"/>
						<line number="55" hits="1"/>
						<line number="60" hits="1"/>
						<line number="61" hits="1"/>
						<line number="62" hits="1"/>
						<line number="65" hits="1"/>
						<line number="66" hits="1"/>
						<line number="71" hits="1"/>
						<line number="72" hits="1"/>
						<line number="73" hits="1"/>
						<line number="74" hits="1"/>
						<line number="77" hits="1"/>
						<line number="78" hits="1"/>
						<line number="79" hits="1"/>
						<line number="80" hits="1"/>
						<line number="81" hits="1"/>
						<line number="84" hits="1"/>
						<line number="85" hits="1"/>
						<line number="88" hits="1"/>
						<line number="89" hits="1"/>
						<line number="90" hits="1"/>
						<line number="93" hits="1"/>
						<line number="94" hits="1"/>
						<line number="95" hits="1"/>
						<line number="96" hits="1"/>
						<line number="97" hits="1"/>
						<line number="101" hits="1"/>
						<line number="104" hits="1"/>
						<line number="108" hits="1"/>
						<line number="112" hits="1"/>
						<line number="118" hits="1"/>
						<line number="119" hits="1"/>
						<line number="120" hits="1"/>
						<line number="123" hits="1"/>
						<line number="124" hits="1"/>
						<line number="125" hits="1"/>
						<line number="126" hits="1"/>
						<line number="129" hits="1"/>
						<line number="130" hits="1"/>
						<line number="133" hits="1"/>
						<line number="134" hits="1"/>
						<line number="163" hits="1"/>
						<line number="164" hits="1"/>
						<line number="165" hits="1"/>
						<line number="166" hits="1"/>
						<line number="167" hits="1"/>
						<line number="168" hits="1"/>
						<line number="169" hits="1"/>
						<line number="170" hits="1"/>
						<line number="171" hits="1"/>
						<line number="174" hits="1"/>
						<line number="175" hits="1"/>
						<line number="176" hits="1"/>
						<line number="180" hits="1"/>
						<line number="190" hits="1"/>
						<line number="191" hits="1"/>
						<line number="209" hits="1"/>
						<line number="218" hits="1"/>
						<line number="220" hits="1"/>
						<line number="234" hits="1"/>
						<line number="235" hits="1"/>
						<line number="236" hits="1"/>
						<line number="238" hits="1"/>
						<line number="252" hits="1"/>
						<line number="269" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="270" hits="1"/>
						<line number="272" hits="1"/>
						<line number="273" hits="0"/>
						<line number="274" hits="1"/>
						<line number="275" hits="0"/>
						<line number="276" hits="1"/>
						<line number="278" hits="1"/>
						<line number="279" hits="1"/>
						<line number="281" hits="1"/>
						<line number="283" hits="1"/>
						<line number="284" hits="1"/>
						<line number="285" hits="1"/>
						<line number="289" hits="1"/>
						<line number="291" hits="1"/>
						<line number="294" hits="1"/>
						<line number="301" hits="1"/>
						<line number="304" hits="1"/>
						<line number="308" hits="1"/>
						<line number="317" hits="1"/>
						<line number="318" hits="1"/>
						<line number="320" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="325"/>
						<line number="321" hits="1"/>
						<line number="325" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="326" hits="1"/>
						<line number="330" hits="1"/>
						<line number="333" hits="1"/>
						<line number="339" hits="1"/>
						<line number="340" hits="1"/>
						<line number="341" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="342" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="343" hits="1"/>
						<line number="344" hits="1"/>
						<line number="345" hits="1"/>
						<line number="347" hits="1"/>
						<line number="351" hits="1"/>
						<line number="352" hits="1"/>
						<line number="353" hits="1"/>
						<line number="355" hits="1"/>
						<line number="357" hits="1"/>
						<line number="361" hits="1"/>
						<line number="366" hits="1"/>
						<line number="382" hits="1"/>
						<line number="383" hits="1"/>
						<line number="391" hits="1"/>
						<line number="393" hits="1"/>
						<line number="394" hits="1"/>
						<line number="397" hits="1"/>
						<line number="398" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="399" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="400"/>
						<line number="400" hits="1"/>
						<line number="401" hits="1"/>
						<line number="402" hits="1"/>
						<line number="408" hits="1"/>
						<line number="410" hits="1"/>
						<line number="411" hits="1"/>
						<line number="413" hits="1"/>
						<line number="416" hits="1"/>
						<line number="417" hits="1"/>
						<line number="418" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="419" hits="1"/>
						<line number="420" hits="1"/>
						<line number="422" hits="1"/>
						<line number="424" hits="1"/>
						<line number="432" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="433" hits="1"/>
						<line number="441" hits="1"/>
						<line number="442" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="432"/>
						<line number="443" hits="1"/>
						<line number="444" hits="1"/>
						<line number="445" hits="1"/>
						<line number="446" hits="1"/>
						<line number="447" hits="1"/>
						<line number="448" hits="1"/>
						<line number="450" hits="1"/>
						<line number="455" hits="1"/>
						<line number="457" hits="1"/>
						<line number="458" hits="1"/>
						<line number="459" hits="1"/>
					</lines>
				</class>
				<class name="search_agent_prompts.py" filename="api/common/services/search_agent_prompts.py" complexity="0" line-rate="0" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="18" hits="0"/>
						<line number="104" hits="0"/>
					</lines>
				</class>
				<class name="search_engine.py" filename="api/common/services/search_engine.py" complexity="0" line-rate="0.8816" branch-rate="0.5312">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="23" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="34" hits="1"/>
						<line number="35" hits="1"/>
						<line number="41" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="42" hits="1"/>
						<line number="43" hits="1"/>
						<line number="44" hits="1"/>
						<line number="45" hits="1"/>
						<line number="46" hits="1"/>
						<line number="48" hits="1"/>
						<line number="52" hits="1"/>
						<line number="53" hits="1"/>
						<line number="55" hits="1"/>
						<line number="59" hits="1"/>
						<line number="60" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="61" hits="1"/>
						<line number="62" hits="1"/>
						<line number="64" hits="1"/>
						<line number="69" hits="1"/>
						<line number="70" hits="1"/>
						<line number="71" hits="1"/>
						<line number="72" hits="1"/>
						<line number="74" hits="1"/>
						<line number="75" hits="1"/>
						<line number="79" hits="1"/>
						<line number="81" hits="1"/>
						<line number="108" hits="1"/>
						<line number="109" hits="1"/>
						<line number="110" hits="1"/>
						<line number="111" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="112" hits="1"/>
						<line number="115" hits="1"/>
						<line number="119" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="120" hits="1"/>
						<line number="124" hits="1"/>
						<line number="125" hits="1"/>
						<line number="126" hits="1"/>
						<line number="127" hits="1"/>
						<line number="130" hits="1"/>
						<line number="131" hits="1"/>
						<line number="133" hits="1"/>
						<line number="134" hits="1"/>
						<line number="135" hits="0"/>
						<line number="136" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="137,154"/>
						<line number="137" hits="0"/>
						<line number="154" hits="1"/>
						<line number="171" hits="1"/>
						<line number="178" hits="1"/>
						<line number="183" hits="0"/>
						<line number="185" hits="1"/>
						<line number="189" hits="1"/>
						<line number="192" hits="0"/>
						<line number="193" hits="0"/>
						<line number="194" hits="0"/>
						<line number="195" hits="1"/>
						<line number="196" hits="1"/>
						<line number="200" hits="1"/>
						<line number="201" hits="1"/>
						<line number="202" hits="1"/>
						<line number="205" hits="1"/>
						<line number="206" hits="1"/>
						<line number="213" hits="1"/>
						<line number="218" hits="1"/>
						<line number="219" hits="1"/>
						<line number="220" hits="1"/>
						<line number="222" hits="1"/>
						<line number="225" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="exit"/>
						<line number="226" hits="1"/>
						<line number="227" hits="1"/>
						<line number="228" hits="1"/>
						<line number="229" hits="1"/>
						<line number="235" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="236" hits="1"/>
						<line number="237" hits="1"/>
						<line number="246" hits="1"/>
						<line number="253" hits="1"/>
						<line number="254" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="255"/>
						<line number="255" hits="1" branch="true" condition-coverage="0% (0/2)" missing-branches="256,260"/>
						<line number="256" hits="1"/>
						<line number="259" hits="1"/>
						<line number="260" hits="1"/>
						<line number="261" hits="1"/>
						<line number="263" hits="1"/>
						<line number="264" hits="1"/>
						<line number="267" hits="1"/>
						<line number="268" hits="1"/>
						<line number="275" hits="1"/>
						<line number="280" hits="1"/>
						<line number="281" hits="1"/>
						<line number="291" hits="1"/>
						<line number="294" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="exit"/>
						<line number="295" hits="1"/>
						<line number="296" hits="1"/>
						<line number="303" hits="1"/>
						<line number="312" hits="1"/>
						<line number="313" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="314"/>
						<line number="314" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="315,319"/>
						<line number="315" hits="0"/>
						<line number="318" hits="0"/>
						<line number="319" hits="0"/>
						<line number="320" hits="0"/>
						<line number="322" hits="1"/>
						<line number="323" hits="1"/>
						<line number="326" hits="1"/>
						<line number="327" hits="1"/>
						<line number="334" hits="1"/>
						<line number="340" hits="1"/>
						<line number="341" hits="1"/>
						<line number="347" hits="1"/>
						<line number="350" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="exit"/>
						<line number="351" hits="1"/>
						<line number="352" hits="1"/>
						<line number="353" hits="1"/>
						<line number="354" hits="1"/>
						<line number="360" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="371"/>
						<line number="361" hits="1"/>
						<line number="362" hits="1"/>
						<line number="371" hits="0"/>
						<line number="378" hits="1"/>
						<line number="379" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="380"/>
						<line number="380" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="381,385"/>
						<line number="381" hits="0"/>
						<line number="384" hits="0"/>
						<line number="385" hits="0"/>
						<line number="386" hits="0"/>
						<line number="388" hits="1"/>
						<line number="389" hits="1"/>
					</lines>
				</class>
				<class name="structs.py" filename="api/common/services/structs.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="4" hits="1"/>
						<line number="6" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="28" hits="1"/>
						<line number="31" hits="1"/>
						<line number="36" hits="1"/>
						<line number="37" hits="1"/>
						<line number="42" hits="1"/>
						<line number="43" hits="1"/>
						<line number="46" hits="1"/>
						<line number="51" hits="1"/>
						<line number="52" hits="1"/>
						<line number="55" hits="1"/>
						<line number="56" hits="1"/>
						<line number="57" hits="1"/>
						<line number="60" hits="1"/>
						<line number="61" hits="1"/>
						<line number="62" hits="1"/>
						<line number="67" hits="1"/>
						<line number="68" hits="1"/>
						<line number="71" hits="1"/>
						<line number="74" hits="1"/>
						<line number="75" hits="1"/>
						<line number="76" hits="1"/>
						<line number="79" hits="1"/>
						<line number="80" hits="1"/>
						<line number="83" hits="1"/>
						<line number="88" hits="1"/>
						<line number="89" hits="1"/>
						<line number="94" hits="1"/>
						<line number="95" hits="1"/>
						<line number="98" hits="1"/>
						<line number="103" hits="1"/>
						<line number="104" hits="1"/>
						<line number="107" hits="1"/>
						<line number="110" hits="1"/>
						<line number="111" hits="1"/>
						<line number="112" hits="1"/>
						<line number="113" hits="1"/>
						<line number="114" hits="1"/>
						<line number="117" hits="1"/>
						<line number="118" hits="1"/>
						<line number="119" hits="1"/>
						<line number="120" hits="1"/>
						<line number="121" hits="1"/>
						<line number="122" hits="1"/>
						<line number="123" hits="1"/>
						<line number="124" hits="1"/>
						<line number="125" hits="1"/>
						<line number="126" hits="1"/>
						<line number="127" hits="1"/>
						<line number="130" hits="1"/>
						<line number="131" hits="1"/>
						<line number="132" hits="1"/>
						<line number="137" hits="1"/>
						<line number="138" hits="1"/>
						<line number="143" hits="1"/>
						<line number="144" hits="1"/>
						<line number="147" hits="1"/>
						<line number="148" hits="1"/>
						<line number="151" hits="1"/>
						<line number="156" hits="1"/>
						<line number="157" hits="1"/>
						<line number="158" hits="1"/>
						<line number="161" hits="1"/>
						<line number="164" hits="1"/>
						<line number="165" hits="1"/>
						<line number="167" hits="1"/>
						<line number="169" hits="1"/>
						<line number="170" hits="1"/>
						<line number="171" hits="1"/>
						<line number="174" hits="1"/>
						<line number="175" hits="1"/>
						<line number="178" hits="1"/>
						<line number="179" hits="1"/>
						<line number="180" hits="1"/>
						<line number="181" hits="1"/>
						<line number="182" hits="1"/>
						<line number="185" hits="1"/>
						<line number="186" hits="1"/>
						<line number="187" hits="1"/>
						<line number="190" hits="1"/>
						<line number="191" hits="1"/>
						<line number="192" hits="1"/>
						<line number="193" hits="1"/>
						<line number="194" hits="1"/>
						<line number="195" hits="1"/>
						<line number="198" hits="1"/>
						<line number="199" hits="1"/>
						<line number="200" hits="1"/>
						<line number="201" hits="1"/>
						<line number="204" hits="1"/>
						<line number="205" hits="1"/>
						<line number="206" hits="1"/>
						<line number="209" hits="1"/>
						<line number="210" hits="1"/>
						<line number="211" hits="1"/>
						<line number="212" hits="1"/>
						<line number="213" hits="1"/>
						<line number="214" hits="1"/>
						<line number="217" hits="1"/>
						<line number="218" hits="1"/>
						<line number="219" hits="1"/>
						<line number="220" hits="1"/>
						<line number="221" hits="1"/>
						<line number="222" hits="1"/>
						<line number="225" hits="1"/>
						<line number="226" hits="1"/>
						<line number="227" hits="1"/>
						<line number="228" hits="1"/>
						<line number="229" hits="1"/>
						<line number="230" hits="1"/>
						<line number="231" hits="1"/>
						<line number="232" hits="1"/>
						<line number="233" hits="1"/>
						<line number="234" hits="1"/>
						<line number="235" hits="1"/>
						<line number="236" hits="1"/>
						<line number="237" hits="1"/>
						<line number="238" hits="1"/>
					</lines>
				</class>
				<class name="url_extractor_engine.py" filename="api/common/services/url_extractor_engine.py" complexity="0" line-rate="0.971" branch-rate="0.875">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="27" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="35" hits="1"/>
						<line number="37" hits="1"/>
						<line number="38" hits="1"/>
						<line number="48" hits="0"/>
						<line number="51" hits="1"/>
						<line number="52" hits="1"/>
						<line number="53" hits="1"/>
						<line number="54" hits="1"/>
						<line number="55" hits="1"/>
						<line number="56" hits="1"/>
						<line number="66" hits="1"/>
						<line number="70" hits="1"/>
						<line number="71" hits="1"/>
						<line number="74" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="75" hits="1"/>
						<line number="82" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="83" hits="1"/>
						<line number="89" hits="1"/>
						<line number="92" hits="1"/>
						<line number="95" hits="1"/>
						<line number="103" hits="1"/>
						<line number="104" hits="1"/>
						<line number="105" hits="1"/>
						<line number="106" hits="1"/>
						<line number="108" hits="1"/>
						<line number="116" hits="1"/>
						<line number="126" hits="1"/>
						<line number="129" hits="1"/>
						<line number="130" hits="1"/>
						<line number="132" hits="1"/>
						<line number="134" hits="1"/>
						<line number="135" hits="1"/>
						<line number="136" hits="1"/>
						<line number="137" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="138" hits="1"/>
						<line number="139" hits="1"/>
						<line number="145" hits="1"/>
						<line number="148" hits="1"/>
						<line number="149" hits="1"/>
						<line number="153" hits="1"/>
						<line number="154" hits="1"/>
						<line number="155" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="156"/>
						<line number="156" hits="0"/>
						<line number="157" hits="1"/>
						<line number="162" hits="1"/>
						<line number="164" hits="1"/>
						<line number="167" hits="1"/>
						<line number="172" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="api.common.services.content_generator" line-rate="0.4903" branch-rate="0.05714" complexity="0">
			<classes>
				<class name="__init__.py" filename="api/common/services/content_generator/__init__.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
					</lines>
				</class>
				<class name="content_generator.py" filename="api/common/services/content_generator/content_generator.py" complexity="0" line-rate="0.472" branch-rate="0.05714">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="19" hits="1"/>
						<line number="22" hits="1"/>
						<line number="27" hits="1"/>
						<line number="32" hits="1"/>
						<line number="34" hits="1"/>
						<line number="36" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="37"/>
						<line number="37" hits="1"/>
						<line number="38" hits="0"/>
						<line number="39" hits="0"/>
						<line number="40" hits="1"/>
						<line number="42" hits="1"/>
						<line number="43" hits="1"/>
						<line number="46" hits="1"/>
						<line number="47" hits="1"/>
						<line number="50" hits="1"/>
						<line number="51" hits="1"/>
						<line number="54" hits="1"/>
						<line number="55" hits="1"/>
						<line number="66" hits="1"/>
						<line number="67" hits="1"/>
						<line number="68" hits="1"/>
						<line number="69" hits="1"/>
						<line number="70" hits="0"/>
						<line number="71" hits="1"/>
						<line number="72" hits="1"/>
						<line number="73" hits="1"/>
						<line number="75" hits="1"/>
						<line number="82" hits="1"/>
						<line number="89" hits="1"/>
						<line number="92" hits="0"/>
						<line number="93" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="94,107"/>
						<line number="94" hits="0"/>
						<line number="107" hits="0"/>
						<line number="108" hits="0"/>
						<line number="110" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="111,128"/>
						<line number="111" hits="0"/>
						<line number="118" hits="1"/>
						<line number="128" hits="1"/>
						<line number="130" hits="1"/>
						<line number="150" hits="0"/>
						<line number="152" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="153,179"/>
						<line number="153" hits="0"/>
						<line number="154" hits="0"/>
						<line number="155" hits="0"/>
						<line number="156" hits="0"/>
						<line number="157" hits="0"/>
						<line number="162" hits="0"/>
						<line number="163" hits="1"/>
						<line number="168" hits="1" branch="true" condition-coverage="0% (0/2)" missing-branches="152,169"/>
						<line number="169" hits="1"/>
						<line number="170" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="152,171"/>
						<line number="171" hits="0"/>
						<line number="172" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="170,173"/>
						<line number="173" hits="0"/>
						<line number="174" hits="0"/>
						<line number="175" hits="0"/>
						<line number="176" hits="0"/>
						<line number="177" hits="0"/>
						<line number="179" hits="1"/>
						<line number="181" hits="1"/>
						<line number="183" hits="1"/>
						<line number="195" hits="0"/>
						<line number="196" hits="0"/>
						<line number="198" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="199,201"/>
						<line number="199" hits="0"/>
						<line number="201" hits="0"/>
						<line number="202" hits="0"/>
						<line number="204" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="205,235"/>
						<line number="205" hits="0"/>
						<line number="207" hits="0"/>
						<line number="209" hits="0"/>
						<line number="210" hits="0"/>
						<line number="218" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="219,222"/>
						<line number="219" hits="0"/>
						<line number="220" hits="0"/>
						<line number="222" hits="0"/>
						<line number="225" hits="0"/>
						<line number="227" hits="0"/>
						<line number="231" hits="0"/>
						<line number="232" hits="1"/>
						<line number="233" hits="0"/>
						<line number="235" hits="1"/>
						<line number="237" hits="1"/>
						<line number="256" hits="0"/>
						<line number="258" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="259,280"/>
						<line number="259" hits="0"/>
						<line number="264" hits="0"/>
						<line number="268" hits="0"/>
						<line number="271" hits="1"/>
						<line number="272" hits="1"/>
						<line number="277" hits="1"/>
						<line number="278" hits="1"/>
						<line number="280" hits="1"/>
						<line number="282" hits="1"/>
						<line number="292" hits="0"/>
						<line number="295" hits="0"/>
						<line number="296" hits="0"/>
						<line number="298" hits="1"/>
						<line number="313" hits="1"/>
						<line number="314" hits="1"/>
						<line number="315" hits="0"/>
						<line number="316" hits="0"/>
						<line number="319" hits="0"/>
						<line number="320" hits="0"/>
						<line number="325" hits="0"/>
						<line number="338" hits="0"/>
						<line number="340" hits="0"/>
						<line number="343" hits="0"/>
						<line number="345" hits="0"/>
						<line number="346" hits="1"/>
						<line number="347" hits="0"/>
						<line number="349" hits="1"/>
						<line number="350" hits="0"/>
						<line number="351" hits="0"/>
						<line number="352" hits="0"/>
						<line number="353" hits="0"/>
						<line number="355" hits="1"/>
						<line number="356" hits="0"/>
						<line number="357" hits="1" branch="true" condition-coverage="0% (0/2)" missing-branches="358,367"/>
						<line number="358" hits="1"/>
						<line number="359" hits="1" branch="true" condition-coverage="0% (0/2)" missing-branches="360,361"/>
						<line number="360" hits="1"/>
						<line number="361" hits="1" branch="true" condition-coverage="0% (0/2)" missing-branches="362,363"/>
						<line number="362" hits="1"/>
						<line number="363" hits="1" branch="true" condition-coverage="0% (0/2)" missing-branches="364,366"/>
						<line number="364" hits="0"/>
						<line number="365" hits="0"/>
						<line number="366" hits="0"/>
						<line number="367" hits="0"/>
						<line number="369" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="370,374"/>
						<line number="370" hits="0"/>
						<line number="371" hits="1"/>
						<line number="372" hits="0"/>
						<line number="373" hits="1"/>
						<line number="374" hits="1"/>
						<line number="376" hits="1"/>
						<line number="390" hits="0"/>
						<line number="397" hits="0"/>
						<line number="399" hits="0"/>
						<line number="400" hits="0"/>
						<line number="403" hits="0"/>
						<line number="404" hits="0"/>
						<line number="405" hits="0"/>
						<line number="407" hits="1"/>
						<line number="417" hits="1"/>
						<line number="425" hits="0"/>
						<line number="426" hits="1"/>
						<line number="427" hits="1"/>
						<line number="428" hits="1" branch="true" condition-coverage="0% (0/2)" missing-branches="429,439"/>
						<line number="429" hits="1"/>
						<line number="437" hits="0"/>
						<line number="439" hits="0"/>
						<line number="441" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="442,469"/>
						<line number="442" hits="0"/>
						<line number="444" hits="0"/>
						<line number="452" hits="0"/>
						<line number="454" hits="0"/>
						<line number="455" hits="0"/>
						<line number="456" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="457,466"/>
						<line number="457" hits="1"/>
						<line number="465" hits="0"/>
						<line number="466" hits="0"/>
						<line number="467" hits="0"/>
						<line number="469" hits="0"/>
						<line number="471" hits="1"/>
						<line number="473" hits="0"/>
						<line number="474" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="475,476"/>
						<line number="475" hits="0"/>
						<line number="476" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="482"/>
						<line number="477" hits="1"/>
						<line number="482" hits="1"/>
						<line number="484" hits="1"/>
						<line number="488" hits="0"/>
						<line number="489" hits="0"/>
						<line number="490" hits="0"/>
						<line number="491" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="492,494"/>
						<line number="492" hits="0"/>
						<line number="494" hits="0"/>
						<line number="495" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="496,498"/>
						<line number="496" hits="1"/>
						<line number="498" hits="1"/>
						<line number="500" hits="1"/>
						<line number="503" hits="1" branch="true" condition-coverage="0% (0/2)" missing-branches="504,511"/>
						<line number="504" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="505,507"/>
						<line number="505" hits="0"/>
						<line number="506" hits="0"/>
						<line number="507" hits="1"/>
						<line number="511" hits="1"/>
						<line number="512" hits="1"/>
						<line number="513" hits="1"/>
						<line number="514" hits="1"/>
						<line number="516" hits="1"/>
						<line number="518" hits="0"/>
						<line number="519" hits="1" branch="true" condition-coverage="0% (0/2)" missing-branches="520,525"/>
						<line number="520" hits="0"/>
						<line number="521" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="519,522"/>
						<line number="522" hits="0"/>
						<line number="523" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="519,524"/>
						<line number="524" hits="1"/>
						<line number="525" hits="0"/>
						<line number="527" hits="1"/>
						<line number="532" hits="0"/>
						<line number="533" hits="0"/>
						<line number="534" hits="1"/>
						<line number="561" hits="0"/>
						<line number="562" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="563,582"/>
						<line number="563" hits="0"/>
						<line number="564" hits="1"/>
						<line number="568" hits="1"/>
						<line number="574" hits="1"/>
						<line number="575" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="576,580"/>
						<line number="576" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="575"/>
						<line number="577" hits="1"/>
						<line number="578" hits="1"/>
						<line number="580" hits="0"/>
						<line number="582" hits="1"/>
						<line number="584" hits="1"/>
						<line number="596" hits="0"/>
						<line number="599" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="exit,600"/>
						<line number="600" hits="1"/>
						<line number="607" hits="1"/>
						<line number="609" hits="1"/>
						<line number="623" hits="0"/>
						<line number="626" hits="0"/>
						<line number="628" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="631"/>
						<line number="629" hits="1"/>
						<line number="631" hits="1" branch="true" condition-coverage="0% (0/2)" missing-branches="632,635"/>
						<line number="632" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="631,633"/>
						<line number="633" hits="0"/>
						<line number="635" hits="1"/>
						<line number="637" hits="1"/>
						<line number="650" hits="0"/>
						<line number="653" hits="0"/>
						<line number="654" hits="0"/>
						<line number="656" hits="0"/>
					</lines>
				</class>
				<class name="prompts_content_generator.py" filename="api/common/services/content_generator/prompts_content_generator.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="295" hits="1"/>
						<line number="323" hits="1"/>
						<line number="387" hits="1"/>
						<line number="394" hits="1"/>
						<line number="402" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="api.common.services.content_generator.utils" line-rate="0.4545" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="api/common/services/content_generator/utils/__init__.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines/>
				</class>
				<class name="formatting.py" filename="api/common/services/content_generator/utils/formatting.py" complexity="0" line-rate="0.5385" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="6" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1" branch="true" condition-coverage="0% (0/2)" missing-branches="12,13"/>
						<line number="12" hits="1"/>
						<line number="13" hits="0"/>
						<line number="14" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="15,24"/>
						<line number="15" hits="0"/>
						<line number="19" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="14,20"/>
						<line number="20" hits="0"/>
						<line number="24" hits="0"/>
					</lines>
				</class>
				<class name="mermaid_context.py" filename="api/common/services/content_generator/utils/mermaid_context.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
					</lines>
				</class>
				<class name="parsing.py" filename="api/common/services/content_generator/utils/parsing.py" complexity="0" line-rate="0.3684" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="5" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="0"/>
						<line number="10" hits="0"/>
						<line number="11" hits="0"/>
						<line number="14" hits="1"/>
						<line number="15" hits="0"/>
						<line number="16" hits="0"/>
						<line number="17" hits="0"/>
						<line number="18" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="19,22"/>
						<line number="19" hits="0"/>
						<line number="20" hits="0"/>
						<line number="21" hits="0"/>
						<line number="22" hits="0"/>
						<line number="25" hits="1"/>
						<line number="26" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="api.common.services.content_regenerator" line-rate="0.1818" branch-rate="0.5" complexity="0">
			<classes>
				<class name="__init__.py" filename="api/common/services/content_regenerator/__init__.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
					</lines>
				</class>
				<class name="content_regenerator.py" filename="api/common/services/content_regenerator/content_regenerator.py" complexity="0" line-rate="0.439" branch-rate="0.5">
					<methods/>
					<lines>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="12" hits="1"/>
						<line number="15" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
						<line number="34" hits="0"/>
						<line number="35" hits="0"/>
						<line number="36" hits="0"/>
						<line number="37" hits="0"/>
						<line number="38" hits="0"/>
						<line number="40" hits="1"/>
						<line number="41" hits="1"/>
						<line number="46" hits="1"/>
						<line number="48" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="57"/>
						<line number="49" hits="1"/>
						<line number="55" hits="0"/>
						<line number="57" hits="0"/>
						<line number="60" hits="0"/>
						<line number="66" hits="0"/>
						<line number="68" hits="1"/>
						<line number="80" hits="0"/>
						<line number="81" hits="0"/>
						<line number="82" hits="0"/>
						<line number="86" hits="0"/>
						<line number="97" hits="0"/>
						<line number="99" hits="0"/>
						<line number="100" hits="0"/>
						<line number="104" hits="0"/>
						<line number="106" hits="0"/>
						<line number="118" hits="0"/>
						<line number="120" hits="0"/>
						<line number="121" hits="0"/>
						<line number="122" hits="0"/>
						<line number="140" hits="0"/>
					</lines>
				</class>
				<class name="prompt_input_schemas.py" filename="api/common/services/content_regenerator/prompt_input_schemas.py" complexity="0" line-rate="0" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="3" hits="0"/>
						<line number="12" hits="0"/>
						<line number="13" hits="0"/>
						<line number="14" hits="0"/>
						<line number="15" hits="0"/>
						<line number="16" hits="0"/>
						<line number="17" hits="0"/>
						<line number="18" hits="0"/>
						<line number="19" hits="0"/>
						<line number="22" hits="0"/>
						<line number="23" hits="0"/>
						<line number="24" hits="0"/>
						<line number="25" hits="0"/>
						<line number="26" hits="0"/>
						<line number="27" hits="0"/>
						<line number="28" hits="0"/>
						<line number="29" hits="0"/>
						<line number="30" hits="0"/>
						<line number="33" hits="0"/>
						<line number="34" hits="0"/>
						<line number="35" hits="0"/>
						<line number="36" hits="0"/>
						<line number="39" hits="0"/>
						<line number="40" hits="0"/>
						<line number="41" hits="0"/>
						<line number="42" hits="0"/>
						<line number="43" hits="0"/>
						<line number="44" hits="0"/>
						<line number="45" hits="0"/>
						<line number="46" hits="0"/>
						<line number="49" hits="0"/>
						<line number="50" hits="0"/>
						<line number="51" hits="0"/>
						<line number="52" hits="0"/>
						<line number="53" hits="0"/>
						<line number="54" hits="0"/>
						<line number="55" hits="0"/>
						<line number="56" hits="0"/>
						<line number="57" hits="0"/>
					</lines>
				</class>
				<class name="prompts_regenerar.py" filename="api/common/services/content_regenerator/prompts_regenerar.py" complexity="0" line-rate="0" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="48" hits="0"/>
						<line number="70" hits="0"/>
						<line number="115" hits="0"/>
						<line number="137" hits="0"/>
						<line number="179" hits="0"/>
						<line number="185" hits="0"/>
						<line number="207" hits="0"/>
						<line number="230" hits="0"/>
						<line number="253" hits="0"/>
						<line number="333" hits="0"/>
						<line number="425" hits="0"/>
						<line number="526" hits="0"/>
						<line number="599" hits="0"/>
						<line number="687" hits="0"/>
						<line number="740" hits="0"/>
						<line number="799" hits="0"/>
						<line number="858" hits="0"/>
						<line number="917" hits="0"/>
						<line number="921" hits="0"/>
						<line number="925" hits="0"/>
						<line number="930" hits="0"/>
						<line number="954" hits="0"/>
						<line number="980" hits="0"/>
						<line number="1055" hits="0"/>
						<line number="1077" hits="0"/>
						<line number="1159" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="api.common.services.evaluation" line-rate="0.4393" branch-rate="0.05556" complexity="0">
			<classes>
				<class name="__init__.py" filename="api/common/services/evaluation/__init__.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="4" hits="1"/>
					</lines>
				</class>
				<class name="document_info_service.py" filename="api/common/services/evaluation/document_info_service.py" complexity="0" line-rate="0.2973" branch-rate="0.1">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="5" hits="1"/>
						<line number="12" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="0"/>
						<line number="18" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="19,20"/>
						<line number="19" hits="0"/>
						<line number="20" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="21,22"/>
						<line number="21" hits="0"/>
						<line number="22" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="exit,23"/>
						<line number="23" hits="0"/>
						<line number="25" hits="1"/>
						<line number="26" hits="0"/>
						<line number="28" hits="1"/>
						<line number="29" hits="0"/>
						<line number="34" hits="0"/>
						<line number="35" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="36,46"/>
						<line number="36" hits="0"/>
						<line number="37" hits="0"/>
						<line number="40" hits="0"/>
						<line number="43" hits="0"/>
						<line number="44" hits="0"/>
						<line number="46" hits="0"/>
						<line number="49" hits="1"/>
						<line number="50" hits="0"/>
						<line number="51" hits="0"/>
						<line number="52" hits="0"/>
						<line number="55" hits="0"/>
						<line number="56" hits="0"/>
						<line number="57" hits="0"/>
						<line number="58" hits="0"/>
						<line number="59" hits="0"/>
						<line number="62" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="63"/>
						<line number="63" hits="0"/>
					</lines>
				</class>
				<class name="document_info_sources.py" filename="api/common/services/evaluation/document_info_sources.py" complexity="0" line-rate="0.4925" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="7" hits="1"/>
						<line number="9" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="33" hits="1"/>
						<line number="34" hits="1"/>
						<line number="35" hits="1"/>
						<line number="36" hits="0"/>
						<line number="39" hits="1"/>
						<line number="40" hits="1"/>
						<line number="42" hits="1"/>
						<line number="43" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="44,46"/>
						<line number="44" hits="0"/>
						<line number="46" hits="0"/>
						<line number="47" hits="0"/>
						<line number="48" hits="0"/>
						<line number="51" hits="0"/>
						<line number="52" hits="0"/>
						<line number="54" hits="0"/>
						<line number="55" hits="0"/>
						<line number="56" hits="0"/>
						<line number="65" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="66,75"/>
						<line number="66" hits="0"/>
						<line number="75" hits="0"/>
						<line number="77" hits="0"/>
						<line number="82" hits="0"/>
						<line number="87" hits="0"/>
						<line number="95" hits="1"/>
						<line number="96" hits="1"/>
						<line number="97" hits="0"/>
						<line number="100" hits="1"/>
						<line number="101" hits="1"/>
						<line number="103" hits="1"/>
						<line number="104" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="105,107"/>
						<line number="105" hits="0"/>
						<line number="107" hits="0"/>
						<line number="108" hits="0"/>
						<line number="109" hits="0"/>
						<line number="110" hits="0"/>
						<line number="111" hits="0"/>
						<line number="112" hits="0"/>
						<line number="114" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="115,117"/>
						<line number="115" hits="0"/>
						<line number="117" hits="0"/>
						<line number="118" hits="0"/>
						<line number="123" hits="0"/>
						<line number="132" hits="0"/>
						<line number="133" hits="0"/>
						<line number="134" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="api.common.services.evaluation.utils" line-rate="0.5263" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="api/common/services/evaluation/utils/__init__.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
					</lines>
				</class>
				<class name="document_score.py" filename="api/common/services/evaluation/utils/document_score.py" complexity="0" line-rate="0.4706" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1" branch="true" condition-coverage="0% (0/2)" missing-branches="12,14"/>
						<line number="12" hits="0"/>
						<line number="14" hits="0"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1" branch="true" condition-coverage="0% (0/2)" missing-branches="21,23"/>
						<line number="21" hits="0"/>
						<line number="23" hits="0"/>
						<line number="26" hits="1"/>
						<line number="33" hits="0"/>
						<line number="34" hits="0"/>
						<line number="35" hits="0"/>
						<line number="36" hits="0"/>
						<line number="40" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="api.common.services.message_broker" line-rate="0.3144" branch-rate="0.01724" complexity="0">
			<classes>
				<class name="local_queue_interface.py" filename="api/common/services/message_broker/local_queue_interface.py" complexity="0" line-rate="0.3117" branch-rate="0.08333">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="6" hits="1"/>
						<line number="11" hits="1"/>
						<line number="14" hits="1"/>
						<line number="21" hits="1"/>
						<line number="23" hits="1"/>
						<line number="26" hits="0"/>
						<line number="27" hits="0"/>
						<line number="28" hits="0"/>
						<line number="29" hits="0"/>
						<line number="32" hits="0"/>
						<line number="34" hits="1"/>
						<line number="36" hits="0"/>
						<line number="37" hits="0"/>
						<line number="39" hits="1"/>
						<line number="41" hits="0"/>
						<line number="43" hits="1"/>
						<line number="52" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="53,55"/>
						<line number="53" hits="0"/>
						<line number="55" hits="0"/>
						<line number="56" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="57,64"/>
						<line number="57" hits="0"/>
						<line number="62" hits="0"/>
						<line number="64" hits="0"/>
						<line number="67" hits="0"/>
						<line number="69" hits="1"/>
						<line number="79" hits="0"/>
						<line number="83" hits="0"/>
						<line number="85" hits="1"/>
						<line number="89" hits="0"/>
						<line number="90" hits="0"/>
						<line number="93" hits="0"/>
						<line number="95" hits="1"/>
						<line number="99" hits="0"/>
						<line number="100" hits="0"/>
						<line number="103" hits="0"/>
						<line number="105" hits="1"/>
						<line number="109" hits="0"/>
						<line number="111" hits="1"/>
						<line number="118" hits="0"/>
						<line number="119" hits="0"/>
						<line number="121" hits="1"/>
						<line number="126" hits="0"/>
						<line number="127" hits="0"/>
						<line number="128" hits="0"/>
						<line number="131" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="132,136"/>
						<line number="132" hits="0"/>
						<line number="136" hits="0"/>
						<line number="139" hits="0"/>
						<line number="140" hits="0"/>
						<line number="141" hits="0"/>
						<line number="144" hits="0"/>
						<line number="146" hits="1"/>
						<line number="155" hits="0"/>
						<line number="156" hits="0"/>
						<line number="162" hits="0"/>
						<line number="166" hits="1"/>
						<line number="169" hits="0"/>
						<line number="173" hits="1"/>
						<line number="174" hits="1"/>
						<line number="178" hits="0"/>
						<line number="186" hits="1"/>
						<line number="187" hits="0"/>
						<line number="188" hits="0"/>
						<line number="190" hits="0"/>
						<line number="192" hits="0"/>
						<line number="194" hits="0"/>
						<line number="197" hits="0"/>
						<line number="198" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="exit,199"/>
						<line number="199" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="exit,200"/>
						<line number="200" hits="0"/>
						<line number="202" hits="0"/>
						<line number="205" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="206"/>
						<line number="206" hits="0"/>
					</lines>
				</class>
				<class name="local_sqlite_broker.py" filename="api/common/services/message_broker/local_sqlite_broker.py" complexity="0" line-rate="0.2396" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="0"/>
						<line number="9" hits="0"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="0"/>
						<line number="19" hits="0"/>
						<line number="20" hits="0"/>
						<line number="23" hits="1"/>
						<line number="29" hits="1"/>
						<line number="34" hits="0"/>
						<line number="35" hits="0"/>
						<line number="37" hits="1"/>
						<line number="41" hits="0"/>
						<line number="49" hits="0"/>
						<line number="57" hits="0"/>
						<line number="58" hits="0"/>
						<line number="59" hits="0"/>
						<line number="60" hits="0"/>
						<line number="62" hits="1"/>
						<line number="69" hits="0"/>
						<line number="70" hits="0"/>
						<line number="75" hits="0"/>
						<line number="79" hits="0"/>
						<line number="80" hits="0"/>
						<line number="81" hits="0"/>
						<line number="82" hits="0"/>
						<line number="84" hits="1"/>
						<line number="94" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="95,97"/>
						<line number="95" hits="0"/>
						<line number="97" hits="0"/>
						<line number="98" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="99,104"/>
						<line number="99" hits="0"/>
						<line number="102" hits="0"/>
						<line number="104" hits="0"/>
						<line number="106" hits="1"/>
						<line number="112" hits="0"/>
						<line number="113" hits="0"/>
						<line number="114" hits="0"/>
						<line number="115" hits="0"/>
						<line number="116" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="exit,117"/>
						<line number="117" hits="0"/>
						<line number="118" hits="0"/>
						<line number="119" hits="0"/>
						<line number="123" hits="0"/>
						<line number="126" hits="0"/>
						<line number="128" hits="0"/>
						<line number="129" hits="0"/>
						<line number="130" hits="0"/>
						<line number="132" hits="1"/>
						<line number="144" hits="0"/>
						<line number="151" hits="0"/>
						<line number="154" hits="0"/>
						<line number="156" hits="0"/>
						<line number="157" hits="0"/>
						<line number="158" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="159,162"/>
						<line number="159" hits="0"/>
						<line number="160" hits="0"/>
						<line number="162" hits="0"/>
						<line number="163" hits="0"/>
						<line number="168" hits="0"/>
						<line number="170" hits="0"/>
						<line number="177" hits="0"/>
						<line number="179" hits="0"/>
						<line number="181" hits="1"/>
						<line number="185" hits="0"/>
						<line number="186" hits="0"/>
						<line number="187" hits="0"/>
						<line number="188" hits="0"/>
						<line number="190" hits="1"/>
						<line number="194" hits="0"/>
						<line number="195" hits="0"/>
						<line number="196" hits="0"/>
						<line number="197" hits="0"/>
						<line number="199" hits="1"/>
						<line number="204" hits="0"/>
						<line number="206" hits="1"/>
						<line number="213" hits="0"/>
						<line number="218" hits="0"/>
						<line number="219" hits="0"/>
						<line number="220" hits="0"/>
						<line number="221" hits="0"/>
						<line number="223" hits="1"/>
						<line number="229" hits="0"/>
						<line number="235" hits="0"/>
						<line number="236" hits="0"/>
						<line number="237" hits="0"/>
						<line number="238" hits="0"/>
					</lines>
				</class>
				<class name="queue_interface.py" filename="api/common/services/message_broker/queue_interface.py" complexity="0" line-rate="0.6842" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="6" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="0"/>
						<line number="11" hits="0"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="20" hits="0"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="27" hits="0"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="32" hits="0"/>
						<line number="34" hits="1"/>
						<line number="35" hits="1"/>
						<line number="37" hits="0"/>
						<line number="39" hits="1"/>
						<line number="40" hits="1"/>
						<line number="42" hits="0"/>
						<line number="44" hits="1"/>
						<line number="45" hits="1"/>
						<line number="46" hits="1"/>
						<line number="48" hits="0"/>
						<line number="50" hits="1"/>
						<line number="51" hits="1"/>
						<line number="58" hits="0"/>
						<line number="60" hits="1"/>
						<line number="61" hits="1"/>
						<line number="63" hits="0"/>
						<line number="65" hits="1"/>
						<line number="66" hits="1"/>
						<line number="68" hits="0"/>
						<line number="70" hits="1"/>
						<line number="71" hits="1"/>
						<line number="73" hits="0"/>
					</lines>
				</class>
				<class name="service_bus_queue_interface.py" filename="api/common/services/message_broker/service_bus_queue_interface.py" complexity="0" line-rate="0.2676" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="6" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="15" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="0"/>
						<line number="23" hits="0"/>
						<line number="24" hits="0"/>
						<line number="25" hits="0"/>
						<line number="26" hits="0"/>
						<line number="27" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="0"/>
						<line number="31" hits="0"/>
						<line number="32" hits="0"/>
						<line number="33" hits="0"/>
						<line number="34" hits="0"/>
						<line number="35" hits="0"/>
						<line number="36" hits="1"/>
						<line number="38" hits="1"/>
						<line number="39" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="40,42"/>
						<line number="40" hits="0"/>
						<line number="41" hits="0"/>
						<line number="42" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="43,45"/>
						<line number="43" hits="0"/>
						<line number="44" hits="0"/>
						<line number="45" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="exit,46"/>
						<line number="46" hits="0"/>
						<line number="47" hits="1"/>
						<line number="49" hits="1"/>
						<line number="59" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="60,62"/>
						<line number="60" hits="0"/>
						<line number="62" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="63,65"/>
						<line number="63" hits="0"/>
						<line number="65" hits="0"/>
						<line number="66" hits="0"/>
						<line number="67" hits="0"/>
						<line number="70" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="71,78"/>
						<line number="71" hits="0"/>
						<line number="72" hits="0"/>
						<line number="75" hits="0"/>
						<line number="76" hits="0"/>
						<line number="78" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="79,87"/>
						<line number="79" hits="0"/>
						<line number="80" hits="0"/>
						<line number="83" hits="0"/>
						<line number="84" hits="0"/>
						<line number="85" hits="0"/>
						<line number="87" hits="1"/>
						<line number="89" hits="1"/>
						<line number="91" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="92,93"/>
						<line number="92" hits="0"/>
						<line number="93" hits="0"/>
						<line number="94" hits="0"/>
						<line number="95" hits="0"/>
						<line number="98" hits="0"/>
						<line number="99" hits="1"/>
						<line number="103" hits="1"/>
						<line number="113" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="114,115"/>
						<line number="114" hits="0"/>
						<line number="115" hits="0"/>
						<line number="116" hits="0"/>
						<line number="119" hits="0"/>
						<line number="120" hits="0"/>
						<line number="121" hits="0"/>
						<line number="122" hits="1"/>
						<line number="124" hits="1"/>
						<line number="130" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="131,132"/>
						<line number="131" hits="0"/>
						<line number="132" hits="0"/>
						<line number="133" hits="0"/>
						<line number="134" hits="0"/>
						<line number="137" hits="0"/>
						<line number="138" hits="0"/>
						<line number="139" hits="1"/>
						<line number="141" hits="1"/>
						<line number="149" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="150,151"/>
						<line number="150" hits="0"/>
						<line number="151" hits="0"/>
						<line number="152" hits="1"/>
						<line number="156" hits="1"/>
						<line number="159" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="160,161"/>
						<line number="160" hits="0"/>
						<line number="161" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="162,163"/>
						<line number="162" hits="0"/>
						<line number="163" hits="0"/>
						<line number="164" hits="0"/>
						<line number="165" hits="0"/>
						<line number="167" hits="0"/>
						<line number="172" hits="0"/>
						<line number="173" hits="0"/>
						<line number="174" hits="0"/>
						<line number="175" hits="0"/>
						<line number="178" hits="1"/>
						<line number="180" hits="1"/>
						<line number="183" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="184,185"/>
						<line number="184" hits="0"/>
						<line number="185" hits="0"/>
						<line number="186" hits="0"/>
						<line number="189" hits="0"/>
						<line number="190" hits="0"/>
						<line number="191" hits="0"/>
						<line number="192" hits="1"/>
						<line number="194" hits="1"/>
						<line number="207" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="208,209"/>
						<line number="208" hits="0"/>
						<line number="209" hits="0"/>
						<line number="210" hits="0"/>
						<line number="213" hits="0"/>
						<line number="216" hits="0"/>
						<line number="217" hits="0"/>
						<line number="218" hits="1"/>
						<line number="220" hits="1"/>
						<line number="221" hits="1"/>
						<line number="228" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="229,232"/>
						<line number="229" hits="0"/>
						<line number="230" hits="0"/>
						<line number="231" hits="0"/>
						<line number="232" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="233,235"/>
						<line number="233" hits="0"/>
						<line number="234" hits="0"/>
						<line number="235" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="236,243"/>
						<line number="236" hits="0"/>
						<line number="237" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="238,241"/>
						<line number="238" hits="0"/>
						<line number="239" hits="0"/>
						<line number="241" hits="0"/>
						<line number="243" hits="0"/>
						<line number="244" hits="0"/>
						<line number="245" hits="0"/>
						<line number="249" hits="1"/>
						<line number="256" hits="1"/>
						<line number="258" hits="1"/>
						<line number="264" hits="0"/>
						<line number="265" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="api.common.tools" line-rate="0.2138" branch-rate="0.0125" complexity="0">
			<classes>
				<class name="__init__.py" filename="api/common/tools/__init__.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines/>
				</class>
				<class name="jina_search.py" filename="api/common/tools/jina_search.py" complexity="0" line-rate="0" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="2" hits="0"/>
						<line number="3" hits="0"/>
						<line number="4" hits="0"/>
						<line number="6" hits="0"/>
						<line number="7" hits="0"/>
						<line number="8" hits="0"/>
						<line number="9" hits="0"/>
						<line number="10" hits="0"/>
						<line number="12" hits="0"/>
						<line number="14" hits="0"/>
						<line number="15" hits="0"/>
						<line number="16" hits="0"/>
						<line number="17" hits="0"/>
						<line number="20" hits="0"/>
						<line number="21" hits="0"/>
						<line number="24" hits="0"/>
						<line number="25" hits="0"/>
						<line number="30" hits="0"/>
						<line number="31" hits="0"/>
						<line number="32" hits="0"/>
						<line number="35" hits="0"/>
						<line number="36" hits="0"/>
						<line number="39" hits="0"/>
						<line number="40" hits="0"/>
						<line number="41" hits="0"/>
						<line number="42" hits="0"/>
						<line number="43" hits="0"/>
						<line number="44" hits="0"/>
						<line number="45" hits="0"/>
						<line number="46" hits="0"/>
						<line number="47" hits="0"/>
						<line number="48" hits="0"/>
						<line number="50" hits="0"/>
						<line number="51" hits="0"/>
						<line number="52" hits="0"/>
						<line number="54" hits="0"/>
						<line number="55" hits="0"/>
						<line number="56" hits="0"/>
						<line number="57" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="exit,58"/>
						<line number="58" hits="0"/>
						<line number="59" hits="0"/>
						<line number="60" hits="0"/>
						<line number="62" hits="0"/>
						<line number="63" hits="0"/>
						<line number="64" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="exit,65"/>
						<line number="65" hits="0"/>
						<line number="68" hits="0"/>
						<line number="69" hits="0"/>
						<line number="70" hits="0"/>
						<line number="73" hits="0"/>
						<line number="80" hits="0"/>
						<line number="88" hits="0"/>
						<line number="91" hits="0"/>
						<line number="92" hits="0"/>
						<line number="93" hits="0"/>
						<line number="94" hits="0"/>
						<line number="95" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="96,98"/>
						<line number="96" hits="0"/>
						<line number="98" hits="0"/>
						<line number="101" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="102,103"/>
						<line number="102" hits="0"/>
						<line number="103" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="104,105"/>
						<line number="104" hits="0"/>
						<line number="105" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="106,107"/>
						<line number="106" hits="0"/>
						<line number="107" hits="0"/>
						<line number="110" hits="0"/>
						<line number="112" hits="0"/>
						<line number="113" hits="0"/>
						<line number="114" hits="0"/>
						<line number="115" hits="0"/>
						<line number="117" hits="0"/>
						<line number="118" hits="0"/>
						<line number="123" hits="0"/>
						<line number="124" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="125,129"/>
						<line number="125" hits="0"/>
						<line number="126" hits="0"/>
						<line number="127" hits="0"/>
						<line number="129" hits="0"/>
						<line number="134" hits="0"/>
						<line number="135" hits="0"/>
						<line number="138" hits="0"/>
						<line number="152" hits="0"/>
						<line number="153" hits="0"/>
						<line number="154" hits="0"/>
						<line number="155" hits="0"/>
						<line number="159" hits="0"/>
						<line number="172" hits="0"/>
						<line number="173" hits="0"/>
						<line number="179" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="180,183"/>
						<line number="180" hits="0"/>
						<line number="181" hits="0"/>
						<line number="183" hits="0"/>
						<line number="184" hits="0"/>
						<line number="186" hits="0"/>
						<line number="200" hits="0"/>
						<line number="201" hits="0"/>
						<line number="202" hits="0"/>
						<line number="203" hits="0"/>
						<line number="208" hits="0"/>
						<line number="221" hits="0"/>
						<line number="224" hits="0"/>
						<line number="228" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="229,232"/>
						<line number="229" hits="0"/>
						<line number="230" hits="0"/>
						<line number="232" hits="0"/>
						<line number="233" hits="0"/>
						<line number="235" hits="0"/>
						<line number="237" hits="0"/>
						<line number="238" hits="0"/>
						<line number="239" hits="0"/>
						<line number="240" hits="0"/>
						<line number="242" hits="0"/>
						<line number="245" hits="0"/>
						<line number="249" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="250,254"/>
						<line number="250" hits="0"/>
						<line number="251" hits="0"/>
						<line number="252" hits="0"/>
						<line number="254" hits="0"/>
						<line number="260" hits="0"/>
						<line number="261" hits="0"/>
						<line number="262" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="263,264"/>
						<line number="263" hits="0"/>
						<line number="264" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="exit,265"/>
						<line number="265" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="266,267"/>
						<line number="266" hits="0"/>
						<line number="267" hits="0"/>
						<line number="270" hits="0"/>
						<line number="272" hits="0"/>
						<line number="286" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="287,288"/>
						<line number="287" hits="0"/>
						<line number="288" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="289,290"/>
						<line number="289" hits="0"/>
						<line number="290" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="291,292"/>
						<line number="291" hits="0"/>
						<line number="292" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="293,294"/>
						<line number="293" hits="0"/>
						<line number="294" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="295,296"/>
						<line number="295" hits="0"/>
						<line number="296" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="297,298"/>
						<line number="297" hits="0"/>
						<line number="298" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="299,300"/>
						<line number="299" hits="0"/>
						<line number="300" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="301,302"/>
						<line number="301" hits="0"/>
						<line number="302" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="303,304"/>
						<line number="303" hits="0"/>
						<line number="304" hits="0"/>
						<line number="307" hits="0"/>
						<line number="308" hits="0"/>
						<line number="309" hits="0"/>
						<line number="312" hits="0"/>
						<line number="314" hits="0"/>
						<line number="315" hits="0"/>
						<line number="321" hits="0"/>
						<line number="324" hits="0"/>
						<line number="339" hits="0"/>
						<line number="340" hits="0"/>
						<line number="343" hits="0"/>
						<line number="346" hits="0"/>
						<line number="349" hits="0"/>
						<line number="351" hits="0"/>
						<line number="352" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="353,358"/>
						<line number="353" hits="0"/>
						<line number="354" hits="0"/>
						<line number="355" hits="0"/>
						<line number="356" hits="0"/>
						<line number="357" hits="0"/>
						<line number="358" hits="0"/>
						<line number="360" hits="0"/>
						<line number="363" hits="0"/>
						<line number="364" hits="0"/>
						<line number="365" hits="0"/>
						<line number="367" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="368,384"/>
						<line number="368" hits="0"/>
						<line number="374" hits="0"/>
						<line number="376" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="377,381"/>
						<line number="377" hits="0"/>
						<line number="378" hits="0"/>
						<line number="379" hits="0"/>
						<line number="381" hits="0"/>
						<line number="382" hits="0"/>
						<line number="384" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="385,388"/>
						<line number="385" hits="0"/>
						<line number="388" hits="0"/>
					</lines>
				</class>
				<class name="loaders.py" filename="api/common/tools/loaders.py" complexity="0" line-rate="0.3603" branch-rate="0.01471">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="32" hits="1"/>
						<line number="35" hits="1"/>
						<line number="38" hits="1"/>
						<line number="39" hits="1"/>
						<line number="40" hits="1"/>
						<line number="45" hits="0"/>
						<line number="47" hits="1"/>
						<line number="48" hits="1"/>
						<line number="53" hits="0"/>
						<line number="55" hits="1"/>
						<line number="56" hits="1"/>
						<line number="61" hits="0"/>
						<line number="63" hits="1"/>
						<line number="64" hits="1"/>
						<line number="68" hits="0"/>
						<line number="70" hits="1"/>
						<line number="71" hits="1"/>
						<line number="75" hits="0"/>
						<line number="77" hits="1"/>
						<line number="78" hits="1"/>
						<line number="82" hits="0"/>
						<line number="84" hits="1"/>
						<line number="85" hits="1"/>
						<line number="89" hits="0"/>
						<line number="92" hits="1"/>
						<line number="93" hits="1"/>
						<line number="94" hits="1"/>
						<line number="95" hits="1"/>
						<line number="97" hits="1"/>
						<line number="98" hits="0"/>
						<line number="99" hits="1"/>
						<line number="100" hits="0"/>
						<line number="101" hits="0"/>
						<line number="102" hits="0"/>
						<line number="103" hits="0"/>
						<line number="104" hits="0"/>
						<line number="105" hits="0"/>
						<line number="106" hits="0"/>
						<line number="107" hits="0"/>
						<line number="108" hits="0"/>
						<line number="110" hits="1"/>
						<line number="111" hits="1"/>
						<line number="112" hits="1"/>
						<line number="114" hits="1"/>
						<line number="115" hits="1"/>
						<line number="117" hits="1"/>
						<line number="118" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="119,122"/>
						<line number="119" hits="0"/>
						<line number="120" hits="0"/>
						<line number="122" hits="0"/>
						<line number="123" hits="0"/>
						<line number="124" hits="0"/>
						<line number="126" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="127,129"/>
						<line number="127" hits="0"/>
						<line number="129" hits="0"/>
						<line number="132" hits="0"/>
						<line number="133" hits="0"/>
						<line number="134" hits="0"/>
						<line number="136" hits="0"/>
						<line number="138" hits="0"/>
						<line number="139" hits="0"/>
						<line number="140" hits="0"/>
						<line number="141" hits="0"/>
						<line number="145" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="146,155"/>
						<line number="146" hits="0"/>
						<line number="152" hits="0"/>
						<line number="153" hits="0"/>
						<line number="154" hits="0"/>
						<line number="155" hits="0"/>
						<line number="157" hits="1"/>
						<line number="158" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="159,165"/>
						<line number="159" hits="1"/>
						<line number="160" hits="0"/>
						<line number="161" hits="0"/>
						<line number="162" hits="0"/>
						<line number="163" hits="0"/>
						<line number="164" hits="0"/>
						<line number="165" hits="0"/>
						<line number="167" hits="1"/>
						<line number="168" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="169,170"/>
						<line number="169" hits="1"/>
						<line number="170" hits="0"/>
						<line number="173" hits="0"/>
						<line number="175" hits="1"/>
						<line number="176" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="177,178"/>
						<line number="177" hits="1"/>
						<line number="178" hits="0"/>
						<line number="179" hits="0"/>
						<line number="180" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="181,190"/>
						<line number="181" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="180,182"/>
						<line number="182" hits="0"/>
						<line number="183" hits="0"/>
						<line number="188" hits="0"/>
						<line number="189" hits="0"/>
						<line number="190" hits="0"/>
						<line number="191" hits="0"/>
						<line number="192" hits="0"/>
						<line number="193" hits="0"/>
						<line number="194" hits="0"/>
						<line number="196" hits="1"/>
						<line number="197" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="198,199"/>
						<line number="198" hits="1"/>
						<line number="199" hits="0"/>
						<line number="200" hits="0"/>
						<line number="201" hits="0"/>
						<line number="203" hits="1"/>
						<line number="204" hits="0"/>
						<line number="205" hits="1" branch="true" condition-coverage="0% (0/2)" missing-branches="206,211"/>
						<line number="206" hits="0"/>
						<line number="207" hits="0"/>
						<line number="208" hits="0"/>
						<line number="209" hits="0"/>
						<line number="211" hits="0"/>
						<line number="214" hits="0"/>
						<line number="216" hits="1"/>
						<line number="217" hits="0"/>
						<line number="218" hits="1" branch="true" condition-coverage="0% (0/2)" missing-branches="219,220"/>
						<line number="219" hits="0"/>
						<line number="220" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="221,222"/>
						<line number="221" hits="0"/>
						<line number="222" hits="0"/>
						<line number="223" hits="0"/>
						<line number="224" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="225,241"/>
						<line number="225" hits="0"/>
						<line number="226" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="227,228"/>
						<line number="227" hits="0"/>
						<line number="228" hits="0"/>
						<line number="229" hits="0"/>
						<line number="230" hits="0"/>
						<line number="239" hits="0"/>
						<line number="240" hits="0"/>
						<line number="241" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="242,243"/>
						<line number="242" hits="0"/>
						<line number="243" hits="0"/>
						<line number="244" hits="0"/>
						<line number="246" hits="1"/>
						<line number="247" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="248,250"/>
						<line number="248" hits="1"/>
						<line number="249" hits="0"/>
						<line number="250" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="exit,251"/>
						<line number="251" hits="0"/>
						<line number="252" hits="0"/>
						<line number="253" hits="0"/>
						<line number="254" hits="0"/>
						<line number="255" hits="0"/>
						<line number="256" hits="0"/>
						<line number="258" hits="1"/>
						<line number="259" hits="0"/>
						<line number="261" hits="1"/>
						<line number="262" hits="1"/>
						<line number="263" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="exit"/>
						<line number="264" hits="1"/>
						<line number="265" hits="0"/>
						<line number="266" hits="0"/>
						<line number="267" hits="0"/>
						<line number="268" hits="0"/>
						<line number="270" hits="1"/>
						<line number="271" hits="1"/>
						<line number="279" hits="1"/>
						<line number="280" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="281,282"/>
						<line number="281" hits="1"/>
						<line number="282" hits="0"/>
						<line number="285" hits="0"/>
						<line number="288" hits="1"/>
						<line number="289" hits="1"/>
						<line number="299" hits="1"/>
						<line number="301" hits="0"/>
						<line number="302" hits="0"/>
						<line number="303" hits="0"/>
						<line number="304" hits="0"/>
						<line number="305" hits="0"/>
						<line number="306" hits="0"/>
						<line number="307" hits="0"/>
						<line number="308" hits="1"/>
						<line number="309" hits="1"/>
						<line number="311" hits="0"/>
						<line number="317" hits="1"/>
						<line number="318" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="319,320"/>
						<line number="319" hits="1"/>
						<line number="320" hits="0"/>
						<line number="321" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="322,323"/>
						<line number="322" hits="0"/>
						<line number="323" hits="0"/>
						<line number="324" hits="0"/>
						<line number="326" hits="1"/>
						<line number="327" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="328,330"/>
						<line number="328" hits="0"/>
						<line number="330" hits="0"/>
						<line number="332" hits="0"/>
						<line number="333" hits="0"/>
						<line number="334" hits="0"/>
						<line number="335" hits="1"/>
						<line number="336" hits="1"/>
						<line number="337" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="338,339"/>
						<line number="338" hits="0"/>
						<line number="339" hits="0"/>
						<line number="340" hits="0"/>
						<line number="342" hits="1"/>
						<line number="343" hits="1"/>
						<line number="344" hits="0"/>
						<line number="345" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="346,352"/>
						<line number="346" hits="1"/>
						<line number="349" hits="0"/>
						<line number="350" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="345,351"/>
						<line number="351" hits="0"/>
						<line number="352" hits="0"/>
						<line number="354" hits="1"/>
						<line number="355" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="356,358"/>
						<line number="356" hits="0"/>
						<line number="358" hits="0"/>
						<line number="360" hits="0"/>
						<line number="362" hits="0"/>
						<line number="363" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="364,365"/>
						<line number="364" hits="0"/>
						<line number="365" hits="0"/>
						<line number="366" hits="0"/>
						<line number="368" hits="1"/>
						<line number="369" hits="1"/>
						<line number="377" hits="1"/>
						<line number="378" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="379,380"/>
						<line number="379" hits="0"/>
						<line number="380" hits="0"/>
						<line number="381" hits="0"/>
						<line number="382" hits="0"/>
						<line number="383" hits="0"/>
						<line number="384" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="385,406"/>
						<line number="385" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="386,389"/>
						<line number="386" hits="0"/>
						<line number="387" hits="0"/>
						<line number="389" hits="0"/>
						<line number="392" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="384,393"/>
						<line number="393" hits="0"/>
						<line number="404" hits="0"/>
						<line number="405" hits="0"/>
						<line number="406" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="407,418"/>
						<line number="407" hits="1"/>
						<line number="418" hits="0"/>
						<line number="420" hits="1"/>
						<line number="421" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="422,424"/>
						<line number="422" hits="0"/>
						<line number="424" hits="1"/>
						<line number="431" hits="0"/>
						<line number="433" hits="1"/>
						<line number="434" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="435,436"/>
						<line number="435" hits="0"/>
						<line number="436" hits="0"/>
						<line number="439" hits="0"/>
					</lines>
				</class>
				<class name="prompts.py" filename="api/common/tools/prompts.py" complexity="0" line-rate="0" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="27" hits="0"/>
					</lines>
				</class>
				<class name="tools.py" filename="api/common/tools/tools.py" complexity="0" line-rate="0" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="2" hits="0"/>
						<line number="4" hits="0"/>
						<line number="6" hits="0"/>
						<line number="9" hits="0"/>
						<line number="10" hits="0"/>
						<line number="11" hits="0"/>
						<line number="15" hits="0"/>
						<line number="16" hits="0"/>
						<line number="17" hits="0"/>
						<line number="19" hits="0"/>
						<line number="20" hits="0"/>
						<line number="21" hits="0"/>
						<line number="22" hits="0"/>
						<line number="23" hits="0"/>
						<line number="25" hits="0"/>
						<line number="28" hits="0"/>
						<line number="29" hits="0"/>
						<line number="30" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="31,33"/>
						<line number="31" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="30,32"/>
						<line number="32" hits="0"/>
						<line number="33" hits="0"/>
						<line number="36" hits="0"/>
						<line number="39" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="40,41"/>
						<line number="40" hits="0"/>
						<line number="41" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="42,51"/>
						<line number="42" hits="0"/>
						<line number="43" hits="0"/>
						<line number="51" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="exit,52"/>
						<line number="52" hits="0"/>
						<line number="53" hits="0"/>
						<line number="54" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="55,56"/>
						<line number="55" hits="0"/>
						<line number="56" hits="0"/>
					</lines>
				</class>
				<class name="utils.py" filename="api/common/tools/utils.py" complexity="0" line-rate="0.2734" branch-rate="0.03571">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="15" hits="1"/>
						<line number="17" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="18"/>
						<line number="18" hits="0"/>
						<line number="20" hits="0"/>
						<line number="23" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="0"/>
						<line number="28" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="32" hits="1"/>
						<line number="35" hits="1"/>
						<line number="36" hits="0"/>
						<line number="37" hits="0"/>
						<line number="38" hits="0"/>
						<line number="41" hits="1"/>
						<line number="45" hits="0"/>
						<line number="46" hits="0"/>
						<line number="47" hits="0"/>
						<line number="52" hits="1"/>
						<line number="58" hits="0"/>
						<line number="59" hits="0"/>
						<line number="60" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="61,62"/>
						<line number="61" hits="0"/>
						<line number="62" hits="0"/>
						<line number="65" hits="1"/>
						<line number="66" hits="0"/>
						<line number="67" hits="0"/>
						<line number="68" hits="0"/>
						<line number="71" hits="1"/>
						<line number="72" hits="0"/>
						<line number="73" hits="0"/>
						<line number="74" hits="0"/>
						<line number="75" hits="0"/>
						<line number="76" hits="0"/>
						<line number="77" hits="0"/>
						<line number="78" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="79,86"/>
						<line number="79" hits="0"/>
						<line number="80" hits="0"/>
						<line number="81" hits="0"/>
						<line number="86" hits="0"/>
						<line number="89" hits="1"/>
						<line number="90" hits="1"/>
						<line number="93" hits="1"/>
						<line number="99" hits="0"/>
						<line number="114" hits="0"/>
						<line number="115" hits="0"/>
						<line number="135" hits="0"/>
						<line number="136" hits="0"/>
						<line number="137" hits="0"/>
						<line number="140" hits="1"/>
						<line number="148" hits="0"/>
						<line number="149" hits="0"/>
						<line number="150" hits="0"/>
						<line number="152" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="153,174"/>
						<line number="153" hits="0"/>
						<line number="154" hits="0"/>
						<line number="155" hits="0"/>
						<line number="158" hits="0"/>
						<line number="159" hits="0"/>
						<line number="160" hits="0"/>
						<line number="161" hits="0"/>
						<line number="162" hits="0"/>
						<line number="163" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="164,168"/>
						<line number="164" hits="0"/>
						<line number="168" hits="0"/>
						<line number="169" hits="0"/>
						<line number="170" hits="0"/>
						<line number="171" hits="0"/>
						<line number="172" hits="0"/>
						<line number="174" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="175,177"/>
						<line number="175" hits="0"/>
						<line number="177" hits="0"/>
						<line number="180" hits="1"/>
						<line number="181" hits="1" branch="true" condition-coverage="0% (0/2)" missing-branches="182,187"/>
						<line number="182" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="183,187"/>
						<line number="183" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="182,184"/>
						<line number="184" hits="0"/>
						<line number="187" hits="0"/>
						<line number="190" hits="1"/>
						<line number="193" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="194,197"/>
						<line number="194" hits="0"/>
						<line number="195" hits="0"/>
						<line number="196" hits="0"/>
						<line number="197" hits="0"/>
						<line number="200" hits="1"/>
						<line number="206" hits="0"/>
						<line number="207" hits="1"/>
						<line number="218" hits="0"/>
						<line number="219" hits="0"/>
						<line number="220" hits="0"/>
						<line number="221" hits="0"/>
						<line number="222" hits="0"/>
						<line number="225" hits="0"/>
						<line number="228" hits="1"/>
						<line number="240" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="241,242"/>
						<line number="241" hits="0"/>
						<line number="242" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="243,244"/>
						<line number="243" hits="0"/>
						<line number="244" hits="0"/>
						<line number="245" hits="0"/>
						<line number="246" hits="0"/>
						<line number="247" hits="0"/>
						<line number="248" hits="0"/>
						<line number="253" hits="0"/>
						<line number="254" hits="0"/>
						<line number="255" hits="0"/>
						<line number="256" hits="0"/>
						<line number="257" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="258,259"/>
						<line number="258" hits="0"/>
						<line number="259" hits="0"/>
						<line number="260" hits="0"/>
						<line number="261" hits="0"/>
						<line number="262" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="exit,263"/>
						<line number="263" hits="0"/>
						<line number="264" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="api.common.utils" line-rate="0.1471" branch-rate="0" complexity="0">
			<classes>
				<class name="check_prompt.py" filename="api/common/utils/check_prompt.py" complexity="0" line-rate="0.1471" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="5" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="0"/>
						<line number="10" hits="0"/>
						<line number="11" hits="0"/>
						<line number="12" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="13,14"/>
						<line number="13" hits="0"/>
						<line number="14" hits="0"/>
						<line number="16" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="17,25"/>
						<line number="17" hits="0"/>
						<line number="19" hits="0"/>
						<line number="20" hits="0"/>
						<line number="21" hits="0"/>
						<line number="22" hits="0"/>
						<line number="23" hits="0"/>
						<line number="24" hits="0"/>
						<line number="25" hits="0"/>
						<line number="28" hits="1"/>
						<line number="29" hits="0"/>
						<line number="30" hits="0"/>
						<line number="31" hits="0"/>
						<line number="32" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="33,34"/>
						<line number="33" hits="0"/>
						<line number="34" hits="0"/>
						<line number="36" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="37,38"/>
						<line number="37" hits="0"/>
						<line number="38" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="39,44"/>
						<line number="39" hits="0"/>
						<line number="40" hits="0"/>
						<line number="41" hits="0"/>
						<line number="42" hits="0"/>
						<line number="44" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="api.workflows" line-rate="1" branch-rate="1" complexity="0">
			<classes>
				<class name="__init__.py" filename="api/workflows/__init__.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines/>
				</class>
			</classes>
		</package>
		<package name="api.workflows.ai_processes" line-rate="0" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="api/workflows/ai_processes/__init__.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines/>
				</class>
				<class name="ai_process_router.py" filename="api/workflows/ai_processes/ai_process_router.py" complexity="0" line-rate="0" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="2" hits="0"/>
						<line number="4" hits="0"/>
						<line number="5" hits="0"/>
						<line number="6" hits="0"/>
						<line number="8" hits="0"/>
						<line number="14" hits="0"/>
						<line number="17" hits="0"/>
						<line number="18" hits="0"/>
						<line number="23" hits="0"/>
						<line number="24" hits="0"/>
						<line number="25" hits="0"/>
						<line number="31" hits="0"/>
						<line number="32" hits="0"/>
						<line number="33" hits="0"/>
						<line number="34" hits="0"/>
						<line number="35" hits="0"/>
						<line number="38" hits="0"/>
						<line number="39" hits="0"/>
						<line number="40" hits="0"/>
						<line number="45" hits="0"/>
						<line number="46" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="47,48"/>
						<line number="47" hits="0"/>
						<line number="48" hits="0"/>
					</lines>
				</class>
				<class name="ai_process_schemas.py" filename="api/workflows/ai_processes/ai_process_schemas.py" complexity="0" line-rate="0" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="3" hits="0"/>
						<line number="6" hits="0"/>
						<line number="7" hits="0"/>
						<line number="8" hits="0"/>
						<line number="9" hits="0"/>
						<line number="12" hits="0"/>
						<line number="13" hits="0"/>
						<line number="14" hits="0"/>
						<line number="15" hits="0"/>
						<line number="16" hits="0"/>
						<line number="19" hits="0"/>
						<line number="20" hits="0"/>
						<line number="21" hits="0"/>
						<line number="22" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="api.workflows.competencies" line-rate="0" branch-rate="1" complexity="0">
			<classes>
				<class name="__init__.py" filename="api/workflows/competencies/__init__.py" complexity="0" line-rate="0" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="5" hits="0"/>
						<line number="8" hits="0"/>
						<line number="12" hits="0"/>
						<line number="16" hits="0"/>
					</lines>
				</class>
				<class name="competencies_router.py" filename="api/workflows/competencies/competencies_router.py" complexity="0" line-rate="0" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="3" hits="0"/>
						<line number="4" hits="0"/>
						<line number="11" hits="0"/>
						<line number="14" hits="0"/>
						<line number="15" hits="0"/>
						<line number="16" hits="0"/>
						<line number="21" hits="0"/>
						<line number="22" hits="0"/>
						<line number="23" hits="0"/>
					</lines>
				</class>
				<class name="competencies_schemas.py" filename="api/workflows/competencies/competencies_schemas.py" complexity="0" line-rate="0" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="4" hits="0"/>
						<line number="5" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="api.workflows.competencies.generate" line-rate="0" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="api/workflows/competencies/generate/__init__.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines/>
				</class>
				<class name="generate_competencies_schemas.py" filename="api/workflows/competencies/generate/generate_competencies_schemas.py" complexity="0" line-rate="0" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="3" hits="0"/>
						<line number="5" hits="0"/>
						<line number="11" hits="0"/>
						<line number="12" hits="0"/>
						<line number="13" hits="0"/>
						<line number="16" hits="0"/>
						<line number="17" hits="0"/>
						<line number="20" hits="0"/>
						<line number="21" hits="0"/>
						<line number="22" hits="0"/>
						<line number="25" hits="0"/>
						<line number="26" hits="0"/>
						<line number="27" hits="0"/>
					</lines>
				</class>
				<class name="generate_competencies_workflow.py" filename="api/workflows/competencies/generate/generate_competencies_workflow.py" complexity="0" line-rate="0" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="3" hits="0"/>
						<line number="4" hits="0"/>
						<line number="5" hits="0"/>
						<line number="7" hits="0"/>
						<line number="8" hits="0"/>
						<line number="9" hits="0"/>
						<line number="10" hits="0"/>
						<line number="11" hits="0"/>
						<line number="12" hits="0"/>
						<line number="18" hits="0"/>
						<line number="24" hits="0"/>
						<line number="27" hits="0"/>
						<line number="28" hits="0"/>
						<line number="36" hits="0"/>
						<line number="37" hits="0"/>
						<line number="38" hits="0"/>
						<line number="39" hits="0"/>
						<line number="40" hits="0"/>
						<line number="42" hits="0"/>
						<line number="45" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="46,50"/>
						<line number="46" hits="0"/>
						<line number="49" hits="0"/>
						<line number="50" hits="0"/>
						<line number="51" hits="0"/>
						<line number="58" hits="0"/>
						<line number="59" hits="0"/>
						<line number="62" hits="0"/>
						<line number="64" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="65,66"/>
						<line number="65" hits="0"/>
						<line number="66" hits="0"/>
						<line number="70" hits="0"/>
						<line number="72" hits="0"/>
						<line number="74" hits="0"/>
						<line number="75" hits="0"/>
						<line number="77" hits="0"/>
						<line number="81" hits="0"/>
						<line number="82" hits="0"/>
						<line number="86" hits="0"/>
						<line number="89" hits="0"/>
						<line number="96" hits="0"/>
						<line number="97" hits="0"/>
						<line number="101" hits="0"/>
						<line number="104" hits="0"/>
						<line number="105" hits="0"/>
						<line number="108" hits="0"/>
						<line number="109" hits="0"/>
						<line number="111" hits="0"/>
						<line number="112" hits="0"/>
						<line number="120" hits="0"/>
						<line number="123" hits="0"/>
						<line number="130" hits="0"/>
					</lines>
				</class>
				<class name="prompt_generate_competencies.py" filename="api/workflows/competencies/generate/prompt_generate_competencies.py" complexity="0" line-rate="0" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
					</lines>
				</class>
				<class name="schemas.py" filename="api/workflows/competencies/generate/schemas.py" complexity="0" line-rate="0" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="3" hits="0"/>
						<line number="6" hits="0"/>
						<line number="7" hits="0"/>
						<line number="8" hits="0"/>
						<line number="9" hits="0"/>
						<line number="10" hits="0"/>
						<line number="11" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="api.workflows.document_ingestion" line-rate="0.3042" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="api/workflows/document_ingestion/__init__.py" complexity="0" line-rate="0.913" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="0"/>
						<line number="4" hits="0"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="21" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="30" hits="1"/>
					</lines>
				</class>
				<class name="chunking.py" filename="api/workflows/document_ingestion/chunking.py" complexity="0" line-rate="0.2261" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="0"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="18" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="0"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="38" hits="1"/>
						<line number="41" hits="1"/>
						<line number="42" hits="1"/>
						<line number="56" hits="0"/>
						<line number="57" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="58,60"/>
						<line number="58" hits="0"/>
						<line number="60" hits="0"/>
						<line number="63" hits="0"/>
						<line number="64" hits="0"/>
						<line number="65" hits="0"/>
						<line number="66" hits="0"/>
						<line number="67" hits="0"/>
						<line number="68" hits="1"/>
						<line number="69" hits="1"/>
						<line number="70" hits="1"/>
						<line number="71" hits="0"/>
						<line number="73" hits="1"/>
						<line number="74" hits="1"/>
						<line number="76" hits="0"/>
						<line number="77" hits="1"/>
						<line number="82" hits="1"/>
						<line number="83" hits="0"/>
						<line number="84" hits="1"/>
						<line number="86" hits="1"/>
						<line number="87" hits="1"/>
						<line number="92" hits="0"/>
						<line number="93" hits="0"/>
						<line number="98" hits="0"/>
						<line number="99" hits="0"/>
						<line number="100" hits="0"/>
						<line number="101" hits="0"/>
						<line number="102" hits="1"/>
						<line number="105" hits="1"/>
						<line number="107" hits="1"/>
						<line number="108" hits="1"/>
						<line number="109" hits="0"/>
						<line number="111" hits="1"/>
						<line number="112" hits="0"/>
						<line number="116" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="117,119"/>
						<line number="117" hits="0"/>
						<line number="119" hits="0"/>
						<line number="121" hits="1"/>
						<line number="123" hits="0"/>
						<line number="125" hits="0"/>
						<line number="130" hits="0"/>
						<line number="132" hits="0"/>
						<line number="133" hits="0"/>
						<line number="134" hits="0"/>
						<line number="135" hits="0"/>
						<line number="136" hits="0"/>
						<line number="137" hits="0"/>
						<line number="138" hits="0"/>
						<line number="140" hits="0"/>
						<line number="142" hits="0"/>
						<line number="143" hits="0"/>
						<line number="148" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="149,157"/>
						<line number="149" hits="0"/>
						<line number="150" hits="0"/>
						<line number="151" hits="0"/>
						<line number="152" hits="0"/>
						<line number="153" hits="0"/>
						<line number="154" hits="0"/>
						<line number="155" hits="0"/>
						<line number="157" hits="0"/>
						<line number="158" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="159,169"/>
						<line number="159" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="160,162"/>
						<line number="160" hits="0"/>
						<line number="162" hits="0"/>
						<line number="163" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="164,167"/>
						<line number="164" hits="0"/>
						<line number="165" hits="0"/>
						<line number="167" hits="0"/>
						<line number="169" hits="1"/>
						<line number="170" hits="0"/>
						<line number="172" hits="1"/>
						<line number="174" hits="0"/>
						<line number="176" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="177,189"/>
						<line number="177" hits="0"/>
						<line number="180" hits="0"/>
						<line number="181" hits="0"/>
						<line number="182" hits="0"/>
						<line number="183" hits="0"/>
						<line number="184" hits="0"/>
						<line number="185" hits="0"/>
						<line number="186" hits="0"/>
						<line number="187" hits="0"/>
						<line number="189" hits="0"/>
						<line number="190" hits="0"/>
						<line number="192" hits="1"/>
						<line number="193" hits="0"/>
						<line number="195" hits="1"/>
						<line number="205" hits="1"/>
						<line number="206" hits="1"/>
						<line number="208" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="209,219"/>
						<line number="209" hits="0"/>
						<line number="211" hits="0"/>
						<line number="212" hits="0"/>
						<line number="214" hits="0"/>
						<line number="215" hits="0"/>
						<line number="217" hits="0"/>
						<line number="219" hits="0"/>
						<line number="221" hits="0"/>
						<line number="223" hits="1"/>
						<line number="237" hits="0"/>
						<line number="238" hits="0"/>
						<line number="240" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="241,259"/>
						<line number="241" hits="0"/>
						<line number="243" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="244,246"/>
						<line number="244" hits="0"/>
						<line number="246" hits="0"/>
						<line number="247" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="248,252"/>
						<line number="248" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="247,249"/>
						<line number="249" hits="0"/>
						<line number="250" hits="0"/>
						<line number="252" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="253,255"/>
						<line number="253" hits="0"/>
						<line number="255" hits="0"/>
						<line number="257" hits="0"/>
						<line number="259" hits="0"/>
						<line number="261" hits="1"/>
						<line number="264" hits="0"/>
						<line number="265" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="266,278"/>
						<line number="266" hits="0"/>
						<line number="267" hits="0"/>
						<line number="268" hits="0"/>
						<line number="269" hits="0"/>
						<line number="270" hits="0"/>
						<line number="272" hits="0"/>
						<line number="273" hits="0"/>
						<line number="278" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="279,296"/>
						<line number="279" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="280,292"/>
						<line number="280" hits="0"/>
						<line number="281" hits="0"/>
						<line number="282" hits="0"/>
						<line number="285" hits="0"/>
						<line number="286" hits="1"/>
						<line number="287" hits="1"/>
						<line number="292" hits="0"/>
						<line number="296" hits="0"/>
						<line number="300" hits="0"/>
						<line number="301" hits="0"/>
						<line number="302" hits="0"/>
						<line number="304" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="305,319"/>
						<line number="305" hits="0"/>
						<line number="306" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="307,316"/>
						<line number="307" hits="0"/>
						<line number="308" hits="0"/>
						<line number="309" hits="0"/>
						<line number="310" hits="0"/>
						<line number="313" hits="0"/>
						<line number="314" hits="0"/>
						<line number="316" hits="0"/>
						<line number="317" hits="0"/>
						<line number="319" hits="0"/>
						<line number="320" hits="0"/>
						<line number="321" hits="0"/>
						<line number="323" hits="1"/>
						<line number="324" hits="0"/>
						<line number="325" hits="0"/>
						<line number="326" hits="0"/>
						<line number="327" hits="0"/>
						<line number="329" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="331,354"/>
						<line number="331" hits="0"/>
						<line number="334" hits="0"/>
						<line number="335" hits="0"/>
						<line number="343" hits="0"/>
						<line number="352" hits="0"/>
						<line number="354" hits="0"/>
						<line number="356" hits="1"/>
						<line number="357" hits="0"/>
						<line number="362" hits="1"/>
						<line number="365" hits="0"/>
						<line number="371" hits="1"/>
						<line number="374" hits="0"/>
						<line number="375" hits="0"/>
						<line number="376" hits="0"/>
						<line number="383" hits="1"/>
						<line number="397" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="398,402"/>
						<line number="398" hits="0"/>
						<line number="399" hits="0"/>
						<line number="400" hits="0"/>
						<line number="402" hits="0"/>
						<line number="403" hits="0"/>
						<line number="405" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="406,426"/>
						<line number="406" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="409,415"/>
						<line number="409" hits="0"/>
						<line number="415" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="416,420"/>
						<line number="416" hits="0"/>
						<line number="417" hits="0"/>
						<line number="418" hits="0"/>
						<line number="420" hits="0"/>
						<line number="423" hits="0"/>
						<line number="424" hits="0"/>
						<line number="425" hits="0"/>
						<line number="426" hits="0"/>
						<line number="428" hits="1"/>
						<line number="438" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="exit,439"/>
						<line number="439" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="440,442"/>
						<line number="440" hits="0"/>
						<line number="442" hits="0"/>
						<line number="445" hits="0"/>
						<line number="447" hits="1"/>
						<line number="450" hits="0"/>
						<line number="454" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="455,457"/>
						<line number="455" hits="0"/>
						<line number="457" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="458,461"/>
						<line number="458" hits="0"/>
						<line number="459" hits="0"/>
						<line number="461" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="462,464"/>
						<line number="462" hits="0"/>
						<line number="464" hits="0"/>
						<line number="465" hits="0"/>
						<line number="466" hits="0"/>
						<line number="468" hits="0"/>
						<line number="469" hits="0"/>
						<line number="470" hits="0"/>
						<line number="472" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="473,486"/>
						<line number="473" hits="0"/>
						<line number="486" hits="0"/>
						<line number="495" hits="0"/>
						<line number="498" hits="0"/>
						<line number="500" hits="1"/>
						<line number="501" hits="0"/>
						<line number="503" hits="1"/>
						<line number="505" hits="0"/>
						<line number="506" hits="0"/>
						<line number="507" hits="0"/>
						<line number="508" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="509,516"/>
						<line number="509" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="510,514"/>
						<line number="510" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="511,514"/>
						<line number="511" hits="0"/>
						<line number="512" hits="0"/>
						<line number="513" hits="0"/>
						<line number="514" hits="0"/>
						<line number="515" hits="0"/>
						<line number="516" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="517,518"/>
						<line number="517" hits="0"/>
						<line number="518" hits="0"/>
					</lines>
				</class>
				<class name="document_router.py" filename="api/workflows/document_ingestion/document_router.py" complexity="0" line-rate="0" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="2" hits="0"/>
						<line number="4" hits="0"/>
						<line number="5" hits="0"/>
						<line number="9" hits="0"/>
						<line number="10" hits="0"/>
						<line number="13" hits="0"/>
						<line number="14" hits="0"/>
						<line number="17" hits="0"/>
						<line number="18" hits="0"/>
						<line number="19" hits="0"/>
						<line number="20" hits="0"/>
						<line number="21" hits="0"/>
						<line number="22" hits="0"/>
						<line number="24" hits="0"/>
						<line number="25" hits="0"/>
					</lines>
				</class>
				<class name="document_schemas.py" filename="api/workflows/document_ingestion/document_schemas.py" complexity="0" line-rate="0.9375" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="5" hits="1"/>
						<line number="7" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="0"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="0"/>
						<line number="34" hits="1"/>
						<line number="35" hits="1"/>
						<line number="38" hits="1"/>
						<line number="39" hits="1"/>
						<line number="40" hits="1"/>
						<line number="41" hits="1"/>
						<line number="42" hits="1"/>
						<line number="43" hits="1"/>
						<line number="44" hits="1"/>
						<line number="45" hits="1"/>
						<line number="46" hits="1"/>
					</lines>
				</class>
				<class name="embedding.py" filename="api/workflows/document_ingestion/embedding.py" complexity="0" line-rate="0.3571" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="0"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="0"/>
						<line number="13" hits="0"/>
						<line number="16" hits="0"/>
						<line number="17" hits="0"/>
						<line number="18" hits="0"/>
						<line number="19" hits="0"/>
						<line number="21" hits="1"/>
						<line number="22" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="23,25"/>
						<line number="23" hits="0"/>
						<line number="25" hits="0"/>
						<line number="26" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="27,31"/>
						<line number="27" hits="0"/>
						<line number="28" hits="0"/>
						<line number="29" hits="0"/>
						<line number="30" hits="0"/>
						<line number="31" hits="0"/>
						<line number="32" hits="0"/>
						<line number="34" hits="1"/>
						<line number="35" hits="1"/>
						<line number="36" hits="0"/>
					</lines>
				</class>
				<class name="embedding_functions.py" filename="api/workflows/document_ingestion/embedding_functions.py" complexity="0" line-rate="0.2" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="13" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="50" hits="0"/>
						<line number="51" hits="0"/>
						<line number="52" hits="0"/>
						<line number="53" hits="0"/>
						<line number="57" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="58,60"/>
						<line number="58" hits="0"/>
						<line number="60" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="61,65"/>
						<line number="61" hits="0"/>
						<line number="65" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="66,68"/>
						<line number="66" hits="0"/>
						<line number="68" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="69,71"/>
						<line number="69" hits="0"/>
						<line number="71" hits="0"/>
						<line number="72" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="73,75"/>
						<line number="73" hits="0"/>
						<line number="75" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="76,78"/>
						<line number="76" hits="0"/>
						<line number="78" hits="0"/>
						<line number="79" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="80,92"/>
						<line number="80" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="81,88"/>
						<line number="81" hits="0"/>
						<line number="88" hits="0"/>
						<line number="92" hits="0"/>
						<line number="93" hits="0"/>
						<line number="94" hits="0"/>
						<line number="95" hits="0"/>
						<line number="97" hits="1"/>
						<line number="104" hits="1"/>
						<line number="115" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="116,123"/>
						<line number="116" hits="0"/>
						<line number="121" hits="0"/>
						<line number="123" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="124,130"/>
						<line number="124" hits="0"/>
						<line number="130" hits="0"/>
						<line number="135" hits="0"/>
						<line number="136" hits="0"/>
						<line number="138" hits="1"/>
						<line number="149" hits="0"/>
						<line number="151" hits="0"/>
						<line number="152" hits="0"/>
						<line number="153" hits="0"/>
						<line number="154" hits="0"/>
						<line number="155" hits="0"/>
						<line number="158" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="159,168"/>
						<line number="159" hits="0"/>
						<line number="164" hits="0"/>
						<line number="168" hits="0"/>
						<line number="173" hits="0"/>
						<line number="177" hits="1"/>
						<line number="178" hits="1"/>
						<line number="179" hits="0"/>
						<line number="180" hits="0"/>
						<line number="181" hits="0"/>
					</lines>
				</class>
				<class name="reranker.py" filename="api/workflows/document_ingestion/reranker.py" complexity="0" line-rate="0.2658" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="0"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="11" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="24,26"/>
						<line number="24" hits="0"/>
						<line number="26" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="27,31"/>
						<line number="27" hits="0"/>
						<line number="31" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="32,39"/>
						<line number="32" hits="0"/>
						<line number="37" hits="0"/>
						<line number="39" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="40,42"/>
						<line number="40" hits="0"/>
						<line number="42" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="43,45"/>
						<line number="43" hits="0"/>
						<line number="45" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="46,48"/>
						<line number="46" hits="0"/>
						<line number="48" hits="0"/>
						<line number="52" hits="0"/>
						<line number="54" hits="1"/>
						<line number="55" hits="0"/>
						<line number="56" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="57,62"/>
						<line number="57" hits="0"/>
						<line number="58" hits="0"/>
						<line number="59" hits="0"/>
						<line number="60" hits="0"/>
						<line number="62" hits="0"/>
						<line number="64" hits="0"/>
						<line number="65" hits="0"/>
						<line number="66" hits="0"/>
						<line number="68" hits="1"/>
						<line number="74" hits="0"/>
						<line number="75" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="76,80"/>
						<line number="76" hits="0"/>
						<line number="77" hits="0"/>
						<line number="78" hits="0"/>
						<line number="79" hits="0"/>
						<line number="80" hits="0"/>
						<line number="81" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="82,83"/>
						<line number="82" hits="0"/>
						<line number="83" hits="0"/>
						<line number="84" hits="0"/>
						<line number="86" hits="1"/>
						<line number="92" hits="0"/>
						<line number="93" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="94,98"/>
						<line number="94" hits="0"/>
						<line number="95" hits="0"/>
						<line number="96" hits="0"/>
						<line number="97" hits="0"/>
						<line number="98" hits="0"/>
						<line number="99" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="100,101"/>
						<line number="100" hits="0"/>
						<line number="101" hits="0"/>
						<line number="102" hits="0"/>
						<line number="104" hits="1"/>
						<line number="107" hits="0"/>
						<line number="109" hits="0"/>
						<line number="111" hits="1"/>
						<line number="114" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="119,120"/>
						<line number="119" hits="0"/>
						<line number="120" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="121,122"/>
						<line number="121" hits="0"/>
						<line number="122" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="123,124"/>
						<line number="123" hits="0"/>
						<line number="124" hits="0"/>
						<line number="131" hits="0"/>
					</lines>
				</class>
				<class name="retrievers.py" filename="api/workflows/document_ingestion/retrievers.py" complexity="0" line-rate="0.299" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="9" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="15" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="35" hits="1"/>
						<line number="36" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="37,40"/>
						<line number="37" hits="0"/>
						<line number="40" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="41,42"/>
						<line number="41" hits="0"/>
						<line number="42" hits="0"/>
						<line number="44" hits="1"/>
						<line number="45" hits="0"/>
						<line number="46" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="53,55"/>
						<line number="53" hits="0"/>
						<line number="55" hits="0"/>
						<line number="57" hits="1"/>
						<line number="60" hits="0"/>
						<line number="62" hits="1"/>
						<line number="63" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="64,65"/>
						<line number="64" hits="0"/>
						<line number="65" hits="0"/>
						<line number="67" hits="1"/>
						<line number="68" hits="0"/>
						<line number="69" hits="0"/>
						<line number="78" hits="0"/>
						<line number="80" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="81,87"/>
						<line number="81" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="82,83"/>
						<line number="82" hits="0"/>
						<line number="83" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="84,87"/>
						<line number="84" hits="0"/>
						<line number="85" hits="0"/>
						<line number="87" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="88,92"/>
						<line number="88" hits="0"/>
						<line number="92" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="93,97"/>
						<line number="93" hits="0"/>
						<line number="94" hits="0"/>
						<line number="95" hits="0"/>
						<line number="97" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="98,100"/>
						<line number="98" hits="0"/>
						<line number="100" hits="0"/>
						<line number="111" hits="0"/>
						<line number="112" hits="0"/>
						<line number="114" hits="1"/>
						<line number="115" hits="0"/>
						<line number="116" hits="0"/>
						<line number="117" hits="0"/>
						<line number="118" hits="0"/>
						<line number="119" hits="0"/>
						<line number="120" hits="0"/>
						<line number="121" hits="0"/>
						<line number="122" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="123,124"/>
						<line number="123" hits="0"/>
						<line number="124" hits="0"/>
						<line number="126" hits="1"/>
						<line number="127" hits="0"/>
						<line number="128" hits="0"/>
						<line number="129" hits="0"/>
						<line number="130" hits="0"/>
						<line number="131" hits="0"/>
						<line number="132" hits="0"/>
						<line number="133" hits="0"/>
						<line number="134" hits="0"/>
						<line number="135" hits="0"/>
						<line number="136" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="137,138"/>
						<line number="137" hits="0"/>
						<line number="138" hits="0"/>
						<line number="140" hits="1"/>
						<line number="141" hits="0"/>
						<line number="155" hits="1"/>
						<line number="156" hits="0"/>
						<line number="157" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="158,167"/>
						<line number="158" hits="0"/>
						<line number="159" hits="0"/>
						<line number="160" hits="0"/>
						<line number="161" hits="0"/>
						<line number="162" hits="0"/>
						<line number="163" hits="0"/>
						<line number="164" hits="0"/>
						<line number="165" hits="0"/>
						<line number="166" hits="0"/>
						<line number="167" hits="0"/>
					</lines>
				</class>
				<class name="sentence_splitter.py" filename="api/workflows/document_ingestion/sentence_splitter.py" complexity="0" line-rate="0.32" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="8" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="0"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="0"/>
						<line number="20" hits="0"/>
						<line number="21" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="22,23"/>
						<line number="22" hits="0"/>
						<line number="23" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="24,26"/>
						<line number="24" hits="0"/>
						<line number="26" hits="0"/>
						<line number="27" hits="0"/>
						<line number="28" hits="0"/>
						<line number="29" hits="0"/>
						<line number="30" hits="0"/>
						<line number="31" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="32,36"/>
						<line number="32" hits="0"/>
						<line number="35" hits="0"/>
						<line number="36" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="37,42"/>
						<line number="37" hits="0"/>
						<line number="40" hits="0"/>
						<line number="42" hits="0"/>
						<line number="43" hits="0"/>
						<line number="44" hits="0"/>
						<line number="45" hits="0"/>
						<line number="46" hits="0"/>
						<line number="49" hits="1"/>
						<line number="50" hits="1"/>
						<line number="51" hits="0"/>
						<line number="52" hits="0"/>
						<line number="54" hits="1"/>
						<line number="55" hits="0"/>
						<line number="56" hits="0"/>
						<line number="57" hits="0"/>
						<line number="59" hits="1"/>
						<line number="60" hits="0"/>
						<line number="61" hits="0"/>
						<line number="63" hits="0"/>
						<line number="64" hits="0"/>
						<line number="65" hits="0"/>
						<line number="67" hits="0"/>
					</lines>
				</class>
				<class name="store.py" filename="api/workflows/document_ingestion/store.py" complexity="0" line-rate="0.2615" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="8" hits="1"/>
						<line number="15" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="0"/>
						<line number="22" hits="0"/>
						<line number="25" hits="0"/>
						<line number="27" hits="1"/>
						<line number="41" hits="0"/>
						<line number="43" hits="0"/>
						<line number="44" hits="0"/>
						<line number="46" hits="0"/>
						<line number="49" hits="1"/>
						<line number="60" hits="0"/>
						<line number="62" hits="1"/>
						<line number="78" hits="0"/>
						<line number="79" hits="0"/>
						<line number="80" hits="0"/>
						<line number="81" hits="0"/>
						<line number="85" hits="0"/>
						<line number="89" hits="0"/>
						<line number="90" hits="0"/>
						<line number="92" hits="0"/>
						<line number="93" hits="0"/>
						<line number="94" hits="0"/>
						<line number="95" hits="0"/>
						<line number="96" hits="0"/>
						<line number="97" hits="0"/>
						<line number="99" hits="1"/>
						<line number="120" hits="0"/>
						<line number="121" hits="0"/>
						<line number="122" hits="0"/>
						<line number="124" hits="0"/>
						<line number="129" hits="0"/>
						<line number="131" hits="0"/>
						<line number="134" hits="0"/>
						<line number="135" hits="0"/>
						<line number="136" hits="0"/>
						<line number="139" hits="0"/>
						<line number="141" hits="1"/>
						<line number="152" hits="0"/>
						<line number="153" hits="0"/>
						<line number="156" hits="0"/>
						<line number="157" hits="0"/>
						<line number="158" hits="0"/>
						<line number="159" hits="0"/>
						<line number="160" hits="0"/>
						<line number="161" hits="0"/>
						<line number="162" hits="0"/>
						<line number="165" hits="1"/>
						<line number="175" hits="0"/>
						<line number="176" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="177,179"/>
						<line number="177" hits="0"/>
						<line number="178" hits="0"/>
						<line number="179" hits="0"/>
						<line number="181" hits="1"/>
						<line number="196" hits="0"/>
						<line number="199" hits="0"/>
						<line number="207" hits="0"/>
					</lines>
				</class>
				<class name="utils.py" filename="api/workflows/document_ingestion/utils.py" complexity="0" line-rate="0.3205" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="16" hits="1"/>
						<line number="18" hits="1"/>
						<line number="47" hits="1"/>
						<line number="49" hits="0"/>
						<line number="50" hits="0"/>
						<line number="51" hits="0"/>
						<line number="54" hits="1"/>
						<line number="56" hits="0"/>
						<line number="57" hits="0"/>
						<line number="58" hits="0"/>
						<line number="61" hits="1"/>
						<line number="63" hits="0"/>
						<line number="64" hits="0"/>
						<line number="65" hits="0"/>
						<line number="69" hits="0"/>
						<line number="72" hits="1"/>
						<line number="73" hits="1"/>
						<line number="74" hits="1"/>
						<line number="77" hits="1"/>
						<line number="80" hits="1"/>
						<line number="83" hits="1"/>
						<line number="85" hits="0"/>
						<line number="87" hits="0"/>
						<line number="90" hits="0"/>
						<line number="102" hits="1"/>
						<line number="103" hits="0"/>
						<line number="104" hits="0"/>
						<line number="105" hits="0"/>
						<line number="106" hits="0"/>
						<line number="107" hits="0"/>
						<line number="108" hits="0"/>
						<line number="109" hits="0"/>
						<line number="112" hits="1"/>
						<line number="113" hits="0"/>
						<line number="115" hits="0"/>
						<line number="116" hits="0"/>
						<line number="117" hits="0"/>
						<line number="118" hits="0"/>
						<line number="119" hits="0"/>
						<line number="120" hits="0"/>
						<line number="121" hits="0"/>
						<line number="122" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="123,130"/>
						<line number="123" hits="0"/>
						<line number="124" hits="0"/>
						<line number="125" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="122,126"/>
						<line number="126" hits="0"/>
						<line number="127" hits="0"/>
						<line number="128" hits="0"/>
						<line number="130" hits="0"/>
						<line number="131" hits="0"/>
						<line number="133" hits="0"/>
						<line number="134" hits="0"/>
						<line number="138" hits="1"/>
						<line number="139" hits="0"/>
						<line number="141" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="142,149"/>
						<line number="142" hits="0"/>
						<line number="145" hits="0"/>
						<line number="146" hits="0"/>
						<line number="147" hits="0"/>
						<line number="149" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="150,151"/>
						<line number="150" hits="0"/>
						<line number="151" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="152,154"/>
						<line number="152" hits="0"/>
						<line number="154" hits="0"/>
						<line number="155" hits="0"/>
						<line number="156" hits="0"/>
						<line number="158" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="api.workflows.document_ingestion.search_references" line-rate="0.3462" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="api/workflows/document_ingestion/search_references/__init__.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines/>
				</class>
				<class name="search_prompts.py" filename="api/workflows/document_ingestion/search_references/search_prompts.py" complexity="0" line-rate="0" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="13" hits="0"/>
						<line number="27" hits="0"/>
						<line number="29" hits="0"/>
						<line number="42" hits="0"/>
						<line number="57" hits="0"/>
						<line number="58" hits="0"/>
						<line number="60" hits="0"/>
						<line number="105" hits="0"/>
						<line number="142" hits="0"/>
					</lines>
				</class>
				<class name="search_references_schemas.py" filename="api/workflows/document_ingestion/search_references/search_references_schemas.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
					</lines>
				</class>
				<class name="search_references_workflow.py" filename="api/workflows/document_ingestion/search_references/search_references_workflow.py" complexity="0" line-rate="0.3469" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="28" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="42" hits="1"/>
						<line number="45" hits="1"/>
						<line number="46" hits="1"/>
						<line number="47" hits="1"/>
						<line number="50" hits="1"/>
						<line number="51" hits="1"/>
						<line number="54" hits="1"/>
						<line number="55" hits="1"/>
						<line number="56" hits="1"/>
						<line number="57" hits="1"/>
						<line number="60" hits="1"/>
						<line number="61" hits="1"/>
						<line number="73" hits="1"/>
						<line number="74" hits="0"/>
						<line number="75" hits="0"/>
						<line number="76" hits="0"/>
						<line number="77" hits="0"/>
						<line number="78" hits="0"/>
						<line number="79" hits="0"/>
						<line number="80" hits="0"/>
						<line number="81" hits="1"/>
						<line number="83" hits="1"/>
						<line number="89" hits="1"/>
						<line number="91" hits="1"/>
						<line number="95" hits="0"/>
						<line number="96" hits="0"/>
						<line number="97" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="98,103"/>
						<line number="98" hits="0"/>
						<line number="99" hits="0"/>
						<line number="103" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="113,121"/>
						<line number="113" hits="0"/>
						<line number="116" hits="0"/>
						<line number="121" hits="0"/>
						<line number="124" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="125,131"/>
						<line number="125" hits="0"/>
						<line number="128" hits="0"/>
						<line number="131" hits="0"/>
						<line number="134" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="135,141"/>
						<line number="135" hits="0"/>
						<line number="141" hits="0"/>
						<line number="142" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="143,156"/>
						<line number="143" hits="0"/>
						<line number="148" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="149,155"/>
						<line number="149" hits="1"/>
						<line number="152" hits="1"/>
						<line number="155" hits="1"/>
						<line number="156" hits="1"/>
						<line number="158" hits="1"/>
						<line number="166" hits="1"/>
						<line number="167" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="168,225"/>
						<line number="168" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="169,173"/>
						<line number="169" hits="0"/>
						<line number="172" hits="0"/>
						<line number="173" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="174,178"/>
						<line number="174" hits="0"/>
						<line number="177" hits="0"/>
						<line number="178" hits="0"/>
						<line number="179" hits="0"/>
						<line number="181" hits="0"/>
						<line number="186" hits="0"/>
						<line number="187" hits="0"/>
						<line number="189" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="190,202"/>
						<line number="190" hits="0"/>
						<line number="194" hits="1"/>
						<line number="199" hits="1" branch="true" condition-coverage="0% (0/2)" missing-branches="200,202"/>
						<line number="200" hits="1"/>
						<line number="202" hits="0"/>
						<line number="210" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="167,211"/>
						<line number="211" hits="0"/>
						<line number="214" hits="0"/>
						<line number="215" hits="0"/>
						<line number="216" hits="0"/>
						<line number="217" hits="1"/>
						<line number="220" hits="0"/>
						<line number="221" hits="1"/>
						<line number="224" hits="0"/>
						<line number="225" hits="0"/>
						<line number="227" hits="1"/>
						<line number="232" hits="0"/>
						<line number="233" hits="0"/>
						<line number="236" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="exit,237"/>
						<line number="237" hits="0"/>
						<line number="238" hits="0"/>
						<line number="239" hits="0"/>
						<line number="242" hits="0"/>
						<line number="243" hits="0"/>
						<line number="246" hits="0"/>
						<line number="248" hits="1"/>
						<line number="253" hits="1"/>
						<line number="254" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="255,256"/>
						<line number="255" hits="0"/>
						<line number="256" hits="0"/>
						<line number="257" hits="0"/>
						<line number="258" hits="0"/>
						<line number="259" hits="0"/>
						<line number="260" hits="0"/>
						<line number="261" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="262,265"/>
						<line number="262" hits="0"/>
						<line number="265" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="268,269"/>
						<line number="268" hits="0"/>
						<line number="269" hits="0"/>
						<line number="270" hits="1"/>
						<line number="272" hits="1" branch="true" condition-coverage="0% (0/2)" missing-branches="278,288"/>
						<line number="278" hits="0"/>
						<line number="279" hits="0"/>
						<line number="282" hits="0"/>
						<line number="283" hits="0"/>
						<line number="284" hits="0"/>
						<line number="287" hits="1"/>
						<line number="288" hits="1"/>
						<line number="289" hits="1"/>
						<line number="290" hits="0"/>
						<line number="293" hits="1"/>
						<line number="296" hits="1"/>
						<line number="301" hits="1"/>
						<line number="304" hits="1"/>
						<line number="305" hits="1"/>
						<line number="306" hits="0"/>
						<line number="307" hits="1"/>
						<line number="308" hits="1"/>
						<line number="319" hits="0"/>
						<line number="321" hits="1"/>
						<line number="324" hits="0"/>
						<line number="327" hits="0"/>
						<line number="328" hits="1"/>
						<line number="331" hits="1"/>
						<line number="342" hits="1"/>
						<line number="344" hits="1"/>
						<line number="361" hits="0"/>
						<line number="362" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="363,375"/>
						<line number="363" hits="0"/>
						<line number="366" hits="0"/>
						<line number="367" hits="1"/>
						<line number="375" hits="0"/>
						<line number="379" hits="1"/>
						<line number="381" hits="1"/>
						<line number="384" hits="0"/>
						<line number="385" hits="0"/>
						<line number="386" hits="0"/>
						<line number="387" hits="0"/>
						<line number="390" hits="0"/>
						<line number="394" hits="0"/>
						<line number="395" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="396,413"/>
						<line number="396" hits="0"/>
						<line number="397" hits="0"/>
						<line number="398" hits="0"/>
						<line number="405" hits="0"/>
						<line number="406" hits="0"/>
						<line number="410" hits="0"/>
						<line number="413" hits="1"/>
						<line number="414" hits="1"/>
						<line number="419" hits="0"/>
						<line number="421" hits="1"/>
						<line number="424" hits="0"/>
						<line number="425" hits="0"/>
						<line number="426" hits="1"/>
						<line number="429" hits="1"/>
						<line number="430" hits="1"/>
						<line number="433" hits="1"/>
						<line number="437" hits="0"/>
						<line number="438" hits="0"/>
						<line number="439" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="440,458"/>
						<line number="440" hits="0"/>
						<line number="443" hits="0"/>
						<line number="458" hits="0"/>
						<line number="459" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="460,468"/>
						<line number="460" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="461,465"/>
						<line number="461" hits="0"/>
						<line number="464" hits="0"/>
						<line number="465" hits="0"/>
						<line number="468" hits="0"/>
						<line number="469" hits="1"/>
						<line number="476" hits="1"/>
						<line number="478" hits="1"/>
						<line number="500" hits="0"/>
						<line number="501" hits="0"/>
						<line number="502" hits="0"/>
						<line number="503" hits="0"/>
						<line number="508" hits="0"/>
						<line number="510" hits="0"/>
						<line number="511" hits="0"/>
						<line number="516" hits="0"/>
						<line number="520" hits="1"/>
						<line number="537" hits="0"/>
						<line number="539" hits="0"/>
						<line number="540" hits="0"/>
						<line number="542" hits="0"/>
						<line number="543" hits="0"/>
						<line number="546" hits="0"/>
						<line number="548" hits="1"/>
						<line number="563" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="565,584"/>
						<line number="565" hits="0"/>
						<line number="566" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="568,577"/>
						<line number="568" hits="0"/>
						<line number="571" hits="0"/>
						<line number="572" hits="0"/>
						<line number="573" hits="0"/>
						<line number="574" hits="0"/>
						<line number="575" hits="0"/>
						<line number="577" hits="1"/>
						<line number="580" hits="1"/>
						<line number="581" hits="1"/>
						<line number="582" hits="1"/>
						<line number="584" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="585,589"/>
						<line number="585" hits="0"/>
						<line number="586" hits="0"/>
						<line number="587" hits="0"/>
						<line number="589" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="590,612"/>
						<line number="590" hits="0"/>
						<line number="594" hits="0"/>
						<line number="601" hits="0"/>
						<line number="602" hits="0"/>
						<line number="603" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="604,608"/>
						<line number="604" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="603,605"/>
						<line number="605" hits="1"/>
						<line number="606" hits="1"/>
						<line number="608" hits="0"/>
						<line number="609" hits="0"/>
						<line number="610" hits="0"/>
						<line number="612" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="613,618"/>
						<line number="613" hits="0"/>
						<line number="616" hits="0"/>
						<line number="618" hits="0"/>
						<line number="621" hits="0"/>
						<line number="623" hits="1"/>
						<line number="634" hits="0"/>
						<line number="635" hits="1"/>
						<line number="640" hits="0"/>
						<line number="641" hits="0"/>
						<line number="642" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="exit,643"/>
						<line number="643" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="644,650"/>
						<line number="644" hits="0"/>
						<line number="645" hits="0"/>
						<line number="646" hits="0"/>
						<line number="650" hits="0"/>
						<line number="654" hits="1"/>
						<line number="666" hits="0"/>
						<line number="667" hits="0"/>
						<line number="668" hits="0"/>
						<line number="669" hits="0"/>
						<line number="670" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="671,675"/>
						<line number="671" hits="0"/>
						<line number="672" hits="0"/>
						<line number="673" hits="0"/>
						<line number="674" hits="0"/>
						<line number="675" hits="0"/>
						<line number="676" hits="0"/>
						<line number="677" hits="0"/>
						<line number="680" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="api.workflows.indexes" line-rate="0" branch-rate="1" complexity="0">
			<classes>
				<class name="__init__.py" filename="api/workflows/indexes/__init__.py" complexity="0" line-rate="0" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="5" hits="0"/>
						<line number="8" hits="0"/>
						<line number="12" hits="0"/>
						<line number="15" hits="0"/>
						<line number="20" hits="0"/>
						<line number="24" hits="0"/>
						<line number="27" hits="0"/>
						<line number="31" hits="0"/>
						<line number="34" hits="0"/>
						<line number="42" hits="0"/>
						<line number="46" hits="0"/>
					</lines>
				</class>
				<class name="indexes_router.py" filename="api/workflows/indexes/indexes_router.py" complexity="0" line-rate="0" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="3" hits="0"/>
						<line number="4" hits="0"/>
						<line number="23" hits="0"/>
						<line number="26" hits="0"/>
						<line number="27" hits="0"/>
						<line number="28" hits="0"/>
						<line number="31" hits="0"/>
						<line number="32" hits="0"/>
						<line number="33" hits="0"/>
						<line number="34" hits="0"/>
						<line number="37" hits="0"/>
						<line number="40" hits="0"/>
						<line number="41" hits="0"/>
						<line number="42" hits="0"/>
						<line number="45" hits="0"/>
						<line number="48" hits="0"/>
						<line number="49" hits="0"/>
						<line number="50" hits="0"/>
						<line number="53" hits="0"/>
						<line number="54" hits="0"/>
						<line number="55" hits="0"/>
						<line number="58" hits="0"/>
						<line number="59" hits="0"/>
						<line number="60" hits="0"/>
						<line number="61" hits="0"/>
						<line number="64" hits="0"/>
						<line number="65" hits="0"/>
						<line number="66" hits="0"/>
						<line number="69" hits="0"/>
						<line number="70" hits="0"/>
						<line number="71" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="api.workflows.indexes.generate_index" line-rate="0" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="api/workflows/indexes/generate_index/__init__.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines/>
				</class>
				<class name="generate_index_schemas.py" filename="api/workflows/indexes/generate_index/generate_index_schemas.py" complexity="0" line-rate="0" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="3" hits="0"/>
						<line number="8" hits="0"/>
						<line number="14" hits="0"/>
						<line number="15" hits="0"/>
						<line number="16" hits="0"/>
						<line number="17" hits="0"/>
						<line number="18" hits="0"/>
						<line number="19" hits="0"/>
						<line number="20" hits="0"/>
						<line number="23" hits="0"/>
						<line number="24" hits="0"/>
						<line number="25" hits="0"/>
						<line number="26" hits="0"/>
						<line number="27" hits="0"/>
						<line number="30" hits="0"/>
						<line number="31" hits="0"/>
						<line number="32" hits="0"/>
						<line number="33" hits="0"/>
						<line number="34" hits="0"/>
						<line number="35" hits="0"/>
						<line number="36" hits="0"/>
						<line number="37" hits="0"/>
					</lines>
				</class>
				<class name="generate_index_workflow.py" filename="api/workflows/indexes/generate_index/generate_index_workflow.py" complexity="0" line-rate="0" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="3" hits="0"/>
						<line number="4" hits="0"/>
						<line number="5" hits="0"/>
						<line number="6" hits="0"/>
						<line number="7" hits="0"/>
						<line number="8" hits="0"/>
						<line number="9" hits="0"/>
						<line number="11" hits="0"/>
						<line number="12" hits="0"/>
						<line number="13" hits="0"/>
						<line number="17" hits="0"/>
						<line number="20" hits="0"/>
						<line number="21" hits="0"/>
						<line number="22" hits="0"/>
						<line number="31" hits="0"/>
						<line number="38" hits="0"/>
						<line number="39" hits="0"/>
						<line number="49" hits="0"/>
						<line number="50" hits="0"/>
						<line number="51" hits="0"/>
						<line number="52" hits="0"/>
						<line number="53" hits="0"/>
						<line number="54" hits="0"/>
						<line number="55" hits="0"/>
						<line number="57" hits="0"/>
						<line number="58" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="59,63"/>
						<line number="59" hits="0"/>
						<line number="62" hits="0"/>
						<line number="63" hits="0"/>
						<line number="64" hits="0"/>
						<line number="71" hits="0"/>
						<line number="72" hits="0"/>
						<line number="73" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="74,75"/>
						<line number="74" hits="0"/>
						<line number="75" hits="0"/>
						<line number="76" hits="0"/>
						<line number="77" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="78,79"/>
						<line number="78" hits="0"/>
						<line number="79" hits="0"/>
						<line number="80" hits="0"/>
						<line number="81" hits="0"/>
						<line number="82" hits="0"/>
						<line number="83" hits="0"/>
						<line number="84" hits="0"/>
						<line number="87" hits="0"/>
						<line number="88" hits="0"/>
						<line number="89" hits="0"/>
						<line number="90" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="95,99"/>
						<line number="95" hits="0"/>
						<line number="99" hits="0"/>
						<line number="102" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="103,127"/>
						<line number="103" hits="0"/>
						<line number="106" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="107,112"/>
						<line number="107" hits="0"/>
						<line number="112" hits="0"/>
						<line number="115" hits="0"/>
						<line number="127" hits="0"/>
						<line number="132" hits="0"/>
						<line number="133" hits="0"/>
						<line number="134" hits="0"/>
						<line number="141" hits="0"/>
						<line number="142" hits="0"/>
						<line number="143" hits="0"/>
						<line number="146" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="147,158"/>
						<line number="147" hits="0"/>
						<line number="150" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="151,153"/>
						<line number="151" hits="0"/>
						<line number="153" hits="0"/>
						<line number="158" hits="0"/>
						<line number="163" hits="0"/>
						<line number="164" hits="0"/>
						<line number="165" hits="0"/>
						<line number="176" hits="0"/>
						<line number="177" hits="0"/>
						<line number="178" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="exit,179"/>
						<line number="179" hits="0"/>
						<line number="182" hits="0"/>
						<line number="187" hits="0"/>
						<line number="193" hits="0"/>
						<line number="194" hits="0"/>
						<line number="195" hits="0"/>
						<line number="199" hits="0"/>
						<line number="200" hits="0"/>
						<line number="203" hits="0"/>
						<line number="204" hits="0"/>
						<line number="205" hits="0"/>
						<line number="206" hits="0"/>
						<line number="209" hits="0"/>
						<line number="216" hits="0"/>
						<line number="217" hits="0"/>
						<line number="227" hits="0"/>
						<line number="228" hits="0"/>
					</lines>
				</class>
				<class name="prompt_generate_index.py" filename="api/workflows/indexes/generate_index/prompt_generate_index.py" complexity="0" line-rate="0" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="api.workflows.indexes.get" line-rate="0" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="api/workflows/indexes/get/__init__.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines/>
				</class>
				<class name="get_index_schemas.py" filename="api/workflows/indexes/get/get_index_schemas.py" complexity="0" line-rate="0" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="3" hits="0"/>
						<line number="5" hits="0"/>
						<line number="6" hits="0"/>
						<line number="9" hits="0"/>
						<line number="10" hits="0"/>
						<line number="11" hits="0"/>
						<line number="12" hits="0"/>
						<line number="13" hits="0"/>
						<line number="16" hits="0"/>
						<line number="17" hits="0"/>
					</lines>
				</class>
				<class name="get_index_workflow.py" filename="api/workflows/indexes/get/get_index_workflow.py" complexity="0" line-rate="0" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="3" hits="0"/>
						<line number="4" hits="0"/>
						<line number="5" hits="0"/>
						<line number="7" hits="0"/>
						<line number="8" hits="0"/>
						<line number="10" hits="0"/>
						<line number="13" hits="0"/>
						<line number="14" hits="0"/>
						<line number="17" hits="0"/>
						<line number="18" hits="0"/>
						<line number="19" hits="0"/>
						<line number="21" hits="0"/>
						<line number="22" hits="0"/>
						<line number="23" hits="0"/>
						<line number="24" hits="0"/>
						<line number="25" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="26,27"/>
						<line number="26" hits="0"/>
						<line number="27" hits="0"/>
						<line number="30" hits="0"/>
						<line number="31" hits="0"/>
						<line number="34" hits="0"/>
						<line number="35" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="36,37"/>
						<line number="36" hits="0"/>
						<line number="37" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="api.workflows.rubrics" line-rate="0.7838" branch-rate="0.5" complexity="0">
			<classes>
				<class name="rubrics_router.py" filename="api/workflows/rubrics/rubrics_router.py" complexity="0" line-rate="0.7838" branch-rate="0.5">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="14" hits="1"/>
						<line number="16" hits="1"/>
						<line number="19" hits="1"/>
						<line number="40" hits="1"/>
						<line number="54" hits="1"/>
						<line number="55" hits="1"/>
						<line number="57" hits="1"/>
						<line number="58" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="79"/>
						<line number="59" hits="1"/>
						<line number="60" hits="1"/>
						<line number="61" hits="0"/>
						<line number="62" hits="1"/>
						<line number="63" hits="0"/>
						<line number="70" hits="1"/>
						<line number="71" hits="1"/>
						<line number="79" hits="0"/>
						<line number="89" hits="0"/>
						<line number="92" hits="1"/>
						<line number="116" hits="1"/>
						<line number="125" hits="1"/>
						<line number="126" hits="1"/>
						<line number="129" hits="1"/>
						<line number="130" hits="1"/>
						<line number="131" hits="0"/>
						<line number="132" hits="1"/>
						<line number="133" hits="0"/>
						<line number="140" hits="1"/>
						<line number="141" hits="1"/>
						<line number="148" hits="0"/>
						<line number="159" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="api.workflows.rubrics.extract_criteria" line-rate="0.875" branch-rate="0.5" complexity="0">
			<classes>
				<class name="prompts.py" filename="api/workflows/rubrics/extract_criteria/prompts.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="36" hits="1"/>
					</lines>
				</class>
				<class name="request.py" filename="api/workflows/rubrics/extract_criteria/request.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
					</lines>
				</class>
				<class name="response.py" filename="api/workflows/rubrics/extract_criteria/response.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
					</lines>
				</class>
				<class name="workflow.py" filename="api/workflows/rubrics/extract_criteria/workflow.py" complexity="0" line-rate="0.7895" branch-rate="0.5">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="10" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="18" hits="1"/>
						<line number="23" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
						<line number="28" hits="1"/>
						<line number="32" hits="1"/>
						<line number="34" hits="1"/>
						<line number="35" hits="1"/>
						<line number="36" hits="1"/>
						<line number="41" hits="1"/>
						<line number="42" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="43"/>
						<line number="43" hits="0"/>
						<line number="44" hits="0"/>
						<line number="45" hits="0"/>
						<line number="49" hits="1"/>
						<line number="55" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="56"/>
						<line number="56" hits="0"/>
						<line number="65" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="66"/>
						<line number="66" hits="0"/>
						<line number="70" hits="1"/>
						<line number="73" hits="1"/>
						<line number="75" hits="1"/>
						<line number="77" hits="1"/>
						<line number="79" hits="1"/>
						<line number="80" hits="1"/>
						<line number="86" hits="1"/>
						<line number="87" hits="0"/>
						<line number="88" hits="0"/>
						<line number="89" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="api.workflows.rubrics.generate_rubrics" line-rate="0.6852" branch-rate="0.5" complexity="0">
			<classes>
				<class name="request.py" filename="api/workflows/rubrics/generate_rubrics/request.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
					</lines>
				</class>
				<class name="response.py" filename="api/workflows/rubrics/generate_rubrics/response.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
					</lines>
				</class>
				<class name="workflow.py" filename="api/workflows/rubrics/generate_rubrics/workflow.py" complexity="0" line-rate="0.575" branch-rate="0.5">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="13" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="22" hits="1"/>
						<line number="27" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="32" hits="1"/>
						<line number="33" hits="1"/>
						<line number="35" hits="1"/>
						<line number="37" hits="1"/>
						<line number="38" hits="1"/>
						<line number="39" hits="1"/>
						<line number="40" hits="1"/>
						<line number="41" hits="1"/>
						<line number="44" hits="0"/>
						<line number="45" hits="0"/>
						<line number="46" hits="0"/>
						<line number="54" hits="1"/>
						<line number="55" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="56"/>
						<line number="56" hits="0"/>
						<line number="57" hits="0"/>
						<line number="58" hits="0"/>
						<line number="59" hits="0"/>
						<line number="64" hits="0"/>
						<line number="65" hits="0"/>
						<line number="66" hits="0"/>
						<line number="74" hits="1"/>
						<line number="78" hits="1"/>
						<line number="79" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="91"/>
						<line number="80" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="81" hits="1"/>
						<line number="82" hits="0"/>
						<line number="83" hits="0"/>
						<line number="84" hits="0"/>
						<line number="91" hits="1"/>
						<line number="93" hits="1"/>
						<line number="103" hits="1"/>
						<line number="105" hits="1"/>
						<line number="106" hits="1"/>
						<line number="107" hits="1"/>
						<line number="108" hits="1"/>
						<line number="110" hits="1"/>
						<line number="117" hits="0"/>
						<line number="118" hits="0"/>
						<line number="119" hits="0"/>
						<line number="126" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="127"/>
						<line number="127" hits="0"/>
						<line number="131" hits="1"/>
						<line number="135" hits="1"/>
						<line number="136" hits="1"/>
						<line number="142" hits="1"/>
						<line number="143" hits="0"/>
						<line number="144" hits="0"/>
						<line number="145" hits="0"/>
						<line number="147" hits="1"/>
						<line number="149" hits="0"/>
						<line number="150" hits="0"/>
						<line number="151" hits="0"/>
						<line number="154" hits="0"/>
						<line number="155" hits="0"/>
						<line number="156" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="157,158"/>
						<line number="157" hits="0"/>
						<line number="158" hits="0"/>
						<line number="159" hits="0"/>
						<line number="160" hits="0"/>
						<line number="161" hits="0"/>
						<line number="168" hits="0"/>
						<line number="169" hits="0"/>
						<line number="170" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="api.workflows.rubrics.services" line-rate="0.6667" branch-rate="0.5" complexity="0">
			<classes>
				<class name="file_service.py" filename="api/workflows/rubrics/services/file_service.py" complexity="0" line-rate="0.6667" branch-rate="0.5">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="4" hits="1"/>
						<line number="7" hits="1"/>
						<line number="10" hits="1"/>
						<line number="18" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="19"/>
						<line number="19" hits="0"/>
						<line number="20" hits="0"/>
						<line number="21" hits="0"/>
						<line number="22" hits="0"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="api.workflows.rubrics.services.utils" line-rate="0.1433" branch-rate="0.04762" complexity="0">
			<classes>
				<class name="utils.py" filename="api/workflows/rubrics/services/utils/utils.py" complexity="0" line-rate="0.1433" branch-rate="0.04762">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="19" hits="1"/>
						<line number="22" hits="1"/>
						<line number="28" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="34" hits="1"/>
						<line number="35" hits="0"/>
						<line number="36" hits="0"/>
						<line number="39" hits="1"/>
						<line number="47" hits="0"/>
						<line number="49" hits="0"/>
						<line number="51" hits="0"/>
						<line number="53" hits="0"/>
						<line number="54" hits="0"/>
						<line number="55" hits="0"/>
						<line number="57" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="58,72"/>
						<line number="58" hits="0"/>
						<line number="59" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="60,62"/>
						<line number="60" hits="0"/>
						<line number="61" hits="0"/>
						<line number="62" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="63,65"/>
						<line number="63" hits="0"/>
						<line number="64" hits="0"/>
						<line number="65" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="66,72"/>
						<line number="66" hits="0"/>
						<line number="67" hits="0"/>
						<line number="70" hits="0"/>
						<line number="72" hits="0"/>
						<line number="74" hits="0"/>
						<line number="76" hits="0"/>
						<line number="77" hits="0"/>
						<line number="79" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="80,86"/>
						<line number="80" hits="0"/>
						<line number="81" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="82,84"/>
						<line number="82" hits="0"/>
						<line number="83" hits="0"/>
						<line number="84" hits="0"/>
						<line number="86" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="87,125"/>
						<line number="87" hits="0"/>
						<line number="88" hits="0"/>
						<line number="90" hits="0"/>
						<line number="92" hits="0"/>
						<line number="93" hits="0"/>
						<line number="94" hits="0"/>
						<line number="95" hits="0"/>
						<line number="96" hits="0"/>
						<line number="98" hits="0"/>
						<line number="99" hits="0"/>
						<line number="101" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="86,102"/>
						<line number="102" hits="0"/>
						<line number="103" hits="0"/>
						<line number="104" hits="0"/>
						<line number="105" hits="0"/>
						<line number="106" hits="0"/>
						<line number="107" hits="0"/>
						<line number="111" hits="0"/>
						<line number="112" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="113,117"/>
						<line number="113" hits="0"/>
						<line number="117" hits="0"/>
						<line number="120" hits="0"/>
						<line number="121" hits="0"/>
						<line number="125" hits="0"/>
						<line number="130" hits="0"/>
						<line number="132" hits="0"/>
						<line number="135" hits="1"/>
						<line number="137" hits="0"/>
						<line number="138" hits="0"/>
						<line number="139" hits="0"/>
						<line number="141" hits="0"/>
						<line number="142" hits="0"/>
						<line number="143" hits="0"/>
						<line number="144" hits="0"/>
						<line number="145" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="146,149"/>
						<line number="146" hits="0"/>
						<line number="149" hits="0"/>
						<line number="150" hits="0"/>
						<line number="151" hits="0"/>
						<line number="152" hits="0"/>
						<line number="153" hits="0"/>
						<line number="154" hits="0"/>
						<line number="155" hits="0"/>
						<line number="156" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="157,162"/>
						<line number="157" hits="0"/>
						<line number="161" hits="0"/>
						<line number="162" hits="0"/>
						<line number="163" hits="0"/>
						<line number="164" hits="0"/>
						<line number="167" hits="0"/>
						<line number="168" hits="0"/>
						<line number="171" hits="1"/>
						<line number="173" hits="0"/>
						<line number="174" hits="0"/>
						<line number="175" hits="0"/>
						<line number="177" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="178,185"/>
						<line number="178" hits="0"/>
						<line number="179" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="180,182"/>
						<line number="180" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="179,181"/>
						<line number="181" hits="0"/>
						<line number="182" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="177,183"/>
						<line number="183" hits="0"/>
						<line number="185" hits="0"/>
						<line number="186" hits="0"/>
						<line number="187" hits="0"/>
						<line number="190" hits="1"/>
						<line number="195" hits="0"/>
						<line number="196" hits="0"/>
						<line number="198" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="199,201"/>
						<line number="199" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="198,200"/>
						<line number="200" hits="0"/>
						<line number="201" hits="0"/>
						<line number="204" hits="1"/>
						<line number="206" hits="1"/>
						<line number="208" hits="1"/>
						<line number="231" hits="1"/>
						<line number="239" hits="1"/>
						<line number="241" hits="1"/>
						<line number="243" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="244"/>
						<line number="244" hits="0"/>
						<line number="245" hits="0"/>
						<line number="246" hits="0"/>
						<line number="254" hits="0"/>
						<line number="256" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="257"/>
						<line number="257" hits="0"/>
						<line number="259" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="260"/>
						<line number="260" hits="0"/>
						<line number="261" hits="0"/>
						<line number="263" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="267"/>
						<line number="264" hits="1"/>
						<line number="265" hits="1"/>
						<line number="267" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="268,271"/>
						<line number="268" hits="0"/>
						<line number="269" hits="0"/>
						<line number="271" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="272,275"/>
						<line number="272" hits="0"/>
						<line number="273" hits="0"/>
						<line number="275" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="276,279"/>
						<line number="276" hits="0"/>
						<line number="277" hits="0"/>
						<line number="279" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="280,284"/>
						<line number="280" hits="0"/>
						<line number="281" hits="0"/>
						<line number="284" hits="0"/>
						<line number="287" hits="1"/>
						<line number="288" hits="0"/>
						<line number="289" hits="0"/>
						<line number="290" hits="0"/>
						<line number="292" hits="0"/>
						<line number="294" hits="0"/>
						<line number="295" hits="0"/>
						<line number="296" hits="0"/>
						<line number="298" hits="0"/>
						<line number="299" hits="0"/>
						<line number="301" hits="0"/>
						<line number="302" hits="0"/>
						<line number="303" hits="0"/>
						<line number="315" hits="0"/>
						<line number="317" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="318,330"/>
						<line number="318" hits="0"/>
						<line number="320" hits="0"/>
						<line number="321" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="322,328"/>
						<line number="322" hits="0"/>
						<line number="323" hits="0"/>
						<line number="324" hits="0"/>
						<line number="325" hits="0"/>
						<line number="326" hits="0"/>
						<line number="327" hits="0"/>
						<line number="328" hits="0"/>
						<line number="330" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="331,333"/>
						<line number="331" hits="0"/>
						<line number="333" hits="0"/>
						<line number="334" hits="0"/>
						<line number="335" hits="0"/>
						<line number="337" hits="0"/>
						<line number="338" hits="0"/>
						<line number="339" hits="0"/>
						<line number="341" hits="0"/>
						<line number="342" hits="0"/>
						<line number="345" hits="1"/>
						<line number="349" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="350,358"/>
						<line number="350" hits="0"/>
						<line number="351" hits="0"/>
						<line number="352" hits="0"/>
						<line number="353" hits="0"/>
						<line number="354" hits="0"/>
						<line number="358" hits="0"/>
						<line number="359" hits="0"/>
						<line number="360" hits="0"/>
						<line number="361" hits="0"/>
						<line number="362" hits="0"/>
						<line number="363" hits="0"/>
						<line number="365" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="366,374"/>
						<line number="366" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="367,370"/>
						<line number="367" hits="0"/>
						<line number="368" hits="0"/>
						<line number="370" hits="0"/>
						<line number="371" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="365,372"/>
						<line number="372" hits="0"/>
						<line number="374" hits="0"/>
						<line number="378" hits="0"/>
						<line number="379" hits="0"/>
						<line number="380" hits="0"/>
						<line number="383" hits="1"/>
						<line number="388" hits="0"/>
						<line number="389" hits="0"/>
						<line number="391" hits="0"/>
						<line number="392" hits="0"/>
						<line number="393" hits="0"/>
						<line number="394" hits="0"/>
						<line number="395" hits="0"/>
						<line number="396" hits="0"/>
						<line number="398" hits="0"/>
						<line number="400" hits="0"/>
						<line number="401" hits="0"/>
						<line number="413" hits="0"/>
						<line number="415" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="416,425"/>
						<line number="416" hits="0"/>
						<line number="418" hits="0"/>
						<line number="419" hits="0"/>
						<line number="422" hits="0"/>
						<line number="423" hits="0"/>
						<line number="425" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="426,428"/>
						<line number="426" hits="0"/>
						<line number="428" hits="0"/>
						<line number="429" hits="0"/>
						<line number="430" hits="0"/>
						<line number="431" hits="0"/>
						<line number="433" hits="0"/>
						<line number="434" hits="0"/>
						<line number="436" hits="0"/>
						<line number="437" hits="0"/>
						<line number="440" hits="0"/>
						<line number="441" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="440,442"/>
						<line number="442" hits="0"/>
						<line number="443" hits="0"/>
						<line number="444" hits="0"/>
						<line number="445" hits="0"/>
						<line number="448" hits="1"/>
						<line number="450" hits="0"/>
						<line number="453" hits="1"/>
						<line number="455" hits="0"/>
						<line number="456" hits="0"/>
						<line number="457" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="458,459"/>
						<line number="458" hits="0"/>
						<line number="459" hits="0"/>
						<line number="460" hits="0"/>
						<line number="461" hits="0"/>
						<line number="466" hits="1"/>
						<line number="468" hits="0"/>
						<line number="469" hits="0"/>
						<line number="470" hits="0"/>
						<line number="471" hits="0"/>
						<line number="474" hits="1"/>
						<line number="476" hits="0"/>
						<line number="477" hits="0"/>
						<line number="478" hits="0"/>
						<line number="479" hits="0"/>
						<line number="482" hits="1"/>
						<line number="484" hits="0"/>
						<line number="485" hits="0"/>
						<line number="486" hits="0"/>
						<line number="487" hits="0"/>
						<line number="488" hits="0"/>
						<line number="489" hits="0"/>
						<line number="491" hits="0"/>
						<line number="493" hits="0"/>
						<line number="495" hits="0"/>
						<line number="507" hits="0"/>
						<line number="509" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="510,517"/>
						<line number="510" hits="0"/>
						<line number="512" hits="0"/>
						<line number="513" hits="0"/>
						<line number="517" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="518,522"/>
						<line number="518" hits="0"/>
						<line number="522" hits="0"/>
						<line number="523" hits="0"/>
						<line number="524" hits="0"/>
						<line number="525" hits="0"/>
						<line number="527" hits="0"/>
						<line number="528" hits="0"/>
						<line number="531" hits="0"/>
						<line number="532" hits="0"/>
						<line number="533" hits="0"/>
						<line number="534" hits="0"/>
						<line number="535" hits="0"/>
						<line number="536" hits="0"/>
						<line number="537" hits="0"/>
						<line number="540" hits="1"/>
						<line number="542" hits="0"/>
						<line number="543" hits="0"/>
						<line number="544" hits="0"/>
						<line number="545" hits="0"/>
						<line number="547" hits="0"/>
						<line number="548" hits="0"/>
						<line number="549" hits="0"/>
						<line number="555" hits="0"/>
						<line number="559" hits="0"/>
						<line number="560" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="561,565"/>
						<line number="561" hits="0"/>
						<line number="565" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="566,570"/>
						<line number="566" hits="0"/>
						<line number="570" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="571,594"/>
						<line number="571" hits="0"/>
						<line number="572" hits="0"/>
						<line number="573" hits="0"/>
						<line number="575" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="576,581"/>
						<line number="576" hits="0"/>
						<line number="579" hits="0"/>
						<line number="581" hits="0"/>
						<line number="582" hits="0"/>
						<line number="584" hits="0"/>
						<line number="585" hits="0"/>
						<line number="587" hits="0"/>
						<line number="588" hits="0"/>
						<line number="590" hits="0"/>
						<line number="591" hits="0"/>
						<line number="592" hits="0"/>
						<line number="594" hits="0"/>
						<line number="595" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="api.workflows.sections" line-rate="0" branch-rate="1" complexity="0">
			<classes>
				<class name="__init__.py" filename="api/workflows/sections/__init__.py" complexity="0" line-rate="0" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="5" hits="0"/>
						<line number="8" hits="0"/>
						<line number="12" hits="0"/>
						<line number="15" hits="0"/>
						<line number="19" hits="0"/>
						<line number="23" hits="0"/>
					</lines>
				</class>
				<class name="sections_router.py" filename="api/workflows/sections/sections_router.py" complexity="0" line-rate="0" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="3" hits="0"/>
						<line number="5" hits="0"/>
						<line number="6" hits="0"/>
						<line number="15" hits="0"/>
						<line number="18" hits="0"/>
						<line number="19" hits="0"/>
						<line number="20" hits="0"/>
						<line number="21" hits="0"/>
						<line number="24" hits="0"/>
						<line number="25" hits="0"/>
						<line number="26" hits="0"/>
						<line number="27" hits="0"/>
						<line number="30" hits="0"/>
						<line number="33" hits="0"/>
						<line number="34" hits="0"/>
						<line number="35" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="api.workflows.sections.get" line-rate="0" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="api/workflows/sections/get/__init__.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines/>
				</class>
				<class name="get_sections_by_topic_schemas.py" filename="api/workflows/sections/get/get_sections_by_topic_schemas.py" complexity="0" line-rate="0" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="4" hits="0"/>
						<line number="5" hits="0"/>
						<line number="8" hits="0"/>
						<line number="9" hits="0"/>
						<line number="10" hits="0"/>
						<line number="11" hits="0"/>
						<line number="12" hits="0"/>
					</lines>
				</class>
				<class name="get_sections_by_topic_workflow.py" filename="api/workflows/sections/get/get_sections_by_topic_workflow.py" complexity="0" line-rate="0" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="3" hits="0"/>
						<line number="4" hits="0"/>
						<line number="5" hits="0"/>
						<line number="7" hits="0"/>
						<line number="9" hits="0"/>
						<line number="12" hits="0"/>
						<line number="13" hits="0"/>
						<line number="14" hits="0"/>
						<line number="15" hits="0"/>
						<line number="17" hits="0"/>
						<line number="18" hits="0"/>
						<line number="19" hits="0"/>
						<line number="24" hits="0"/>
						<line number="25" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="26,29"/>
						<line number="26" hits="0"/>
						<line number="29" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="api.workflows.sections.regenerate" line-rate="0" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="api/workflows/sections/regenerate/__init__.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines/>
				</class>
				<class name="regenerate_paragraph_prompts.py" filename="api/workflows/sections/regenerate/regenerate_paragraph_prompts.py" complexity="0" line-rate="0" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="3" hits="0"/>
						<line number="258" hits="0"/>
						<line number="305" hits="0"/>
						<line number="313" hits="0"/>
					</lines>
				</class>
				<class name="regenerate_paragraph_schemas.py" filename="api/workflows/sections/regenerate/regenerate_paragraph_schemas.py" complexity="0" line-rate="0" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="4" hits="0"/>
						<line number="5" hits="0"/>
						<line number="6" hits="0"/>
						<line number="7" hits="0"/>
						<line number="8" hits="0"/>
						<line number="9" hits="0"/>
						<line number="10" hits="0"/>
						<line number="13" hits="0"/>
						<line number="14" hits="0"/>
						<line number="15" hits="0"/>
						<line number="16" hits="0"/>
						<line number="17" hits="0"/>
					</lines>
				</class>
				<class name="regenerate_paragraph_workflow.py" filename="api/workflows/sections/regenerate/regenerate_paragraph_workflow.py" complexity="0" line-rate="0" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="2" hits="0"/>
						<line number="3" hits="0"/>
						<line number="5" hits="0"/>
						<line number="6" hits="0"/>
						<line number="7" hits="0"/>
						<line number="8" hits="0"/>
						<line number="9" hits="0"/>
						<line number="10" hits="0"/>
						<line number="11" hits="0"/>
						<line number="12" hits="0"/>
						<line number="14" hits="0"/>
						<line number="15" hits="0"/>
						<line number="16" hits="0"/>
						<line number="20" hits="0"/>
						<line number="21" hits="0"/>
						<line number="22" hits="0"/>
						<line number="23" hits="0"/>
						<line number="28" hits="0"/>
						<line number="29" hits="0"/>
						<line number="38" hits="0"/>
						<line number="40" hits="0"/>
						<line number="45" hits="0"/>
						<line number="62" hits="0"/>
						<line number="63" hits="0"/>
						<line number="74" hits="0"/>
						<line number="75" hits="0"/>
						<line number="76" hits="0"/>
						<line number="77" hits="0"/>
						<line number="78" hits="0"/>
						<line number="79" hits="0"/>
						<line number="80" hits="0"/>
						<line number="81" hits="0"/>
						<line number="83" hits="0"/>
						<line number="86" hits="0"/>
						<line number="87" hits="0"/>
						<line number="88" hits="0"/>
						<line number="94" hits="0"/>
						<line number="97" hits="0"/>
						<line number="100" hits="0"/>
						<line number="109" hits="0"/>
						<line number="110" hits="0"/>
						<line number="113" hits="0"/>
						<line number="116" hits="0"/>
						<line number="126" hits="0"/>
						<line number="127" hits="0"/>
						<line number="128" hits="0"/>
						<line number="130" hits="0"/>
						<line number="136" hits="0"/>
						<line number="138" hits="0"/>
						<line number="141" hits="0"/>
						<line number="146" hits="0"/>
						<line number="147" hits="0"/>
						<line number="148" hits="0"/>
						<line number="150" hits="0"/>
						<line number="158" hits="0"/>
						<line number="160" hits="0"/>
						<line number="167" hits="0"/>
						<line number="180" hits="0"/>
						<line number="182" hits="0"/>
						<line number="185" hits="0"/>
						<line number="186" hits="0"/>
						<line number="189" hits="0"/>
						<line number="191" hits="0"/>
						<line number="192" hits="0"/>
						<line number="194" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="195,214"/>
						<line number="195" hits="0"/>
						<line number="212" hits="0"/>
						<line number="214" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="215,219"/>
						<line number="215" hits="0"/>
						<line number="219" hits="0"/>
						<line number="221" hits="0"/>
						<line number="224" hits="0"/>
						<line number="225" hits="0"/>
						<line number="236" hits="0"/>
						<line number="237" hits="0"/>
						<line number="238" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="239,240"/>
						<line number="239" hits="0"/>
						<line number="240" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="241,242"/>
						<line number="241" hits="0"/>
						<line number="242" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="243,247"/>
						<line number="243" hits="0"/>
						<line number="247" hits="0"/>
						<line number="256" hits="0"/>
						<line number="257" hits="0"/>
						<line number="259" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="260,263"/>
						<line number="260" hits="0"/>
						<line number="263" hits="0"/>
						<line number="265" hits="0"/>
						<line number="271" hits="0"/>
						<line number="272" hits="0"/>
						<line number="273" hits="0"/>
						<line number="276" hits="0"/>
						<line number="277" hits="0"/>
						<line number="278" hits="0"/>
						<line number="279" hits="0"/>
						<line number="285" hits="0"/>
						<line number="286" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="287,309"/>
						<line number="287" hits="0"/>
						<line number="288" hits="0"/>
						<line number="289" hits="0"/>
						<line number="294" hits="0"/>
						<line number="299" hits="0"/>
						<line number="301" hits="0"/>
						<line number="304" hits="0"/>
						<line number="309" hits="0"/>
						<line number="311" hits="0"/>
						<line number="318" hits="0"/>
						<line number="320" hits="0"/>
						<line number="321" hits="0"/>
						<line number="322" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="323,324"/>
						<line number="323" hits="0"/>
						<line number="324" hits="0"/>
						<line number="327" hits="0"/>
						<line number="328" hits="0"/>
						<line number="337" hits="0"/>
						<line number="338" hits="0"/>
						<line number="339" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="api.workflows.subjects" line-rate="0" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="api/workflows/subjects/__init__.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines/>
				</class>
				<class name="subjects_router.py" filename="api/workflows/subjects/subjects_router.py" complexity="0" line-rate="0" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="3" hits="0"/>
						<line number="4" hits="0"/>
						<line number="5" hits="0"/>
						<line number="7" hits="0"/>
						<line number="8" hits="0"/>
						<line number="9" hits="0"/>
						<line number="11" hits="0"/>
						<line number="17" hits="0"/>
						<line number="20" hits="0"/>
						<line number="21" hits="0"/>
						<line number="22" hits="0"/>
						<line number="23" hits="0"/>
						<line number="27" hits="0"/>
						<line number="28" hits="0"/>
						<line number="34" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="35,44"/>
						<line number="35" hits="0"/>
						<line number="40" hits="0"/>
						<line number="41" hits="0"/>
						<line number="42" hits="0"/>
						<line number="44" hits="0"/>
						<line number="45" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="46,50"/>
						<line number="46" hits="0"/>
						<line number="50" hits="0"/>
						<line number="59" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="60,71"/>
						<line number="60" hits="0"/>
						<line number="67" hits="0"/>
						<line number="68" hits="0"/>
						<line number="69" hits="0"/>
						<line number="71" hits="0"/>
						<line number="76" hits="0"/>
						<line number="77" hits="0"/>
						<line number="78" hits="0"/>
						<line number="79" hits="0"/>
						<line number="80" hits="0"/>
						<line number="85" hits="0"/>
						<line number="86" hits="0"/>
						<line number="87" hits="0"/>
						<line number="88" hits="0"/>
						<line number="89" hits="0"/>
						<line number="95" hits="0"/>
						<line number="96" hits="0"/>
						<line number="99" hits="0"/>
						<line number="100" hits="0"/>
						<line number="107" hits="0"/>
						<line number="108" hits="0"/>
						<line number="109" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="110,111"/>
						<line number="110" hits="0"/>
						<line number="111" hits="0"/>
						<line number="112" hits="0"/>
						<line number="117" hits="0"/>
						<line number="118" hits="0"/>
						<line number="119" hits="0"/>
						<line number="120" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="121,122"/>
						<line number="121" hits="0"/>
						<line number="122" hits="0"/>
						<line number="123" hits="0"/>
						<line number="126" hits="0"/>
						<line number="127" hits="0"/>
						<line number="131" hits="0"/>
						<line number="132" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="133,134"/>
						<line number="133" hits="0"/>
						<line number="134" hits="0"/>
						<line number="135" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="136,140"/>
						<line number="136" hits="0"/>
						<line number="140" hits="0"/>
						<line number="141" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="142,158"/>
						<line number="142" hits="0"/>
						<line number="148" hits="0"/>
						<line number="149" hits="0"/>
						<line number="151" hits="0"/>
						<line number="158" hits="0"/>
					</lines>
				</class>
				<class name="subjects_schemas.py" filename="api/workflows/subjects/subjects_schemas.py" complexity="0" line-rate="0" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="2" hits="0"/>
						<line number="4" hits="0"/>
						<line number="7" hits="0"/>
						<line number="8" hits="0"/>
						<line number="9" hits="0"/>
						<line number="10" hits="0"/>
						<line number="11" hits="0"/>
						<line number="12" hits="0"/>
						<line number="13" hits="0"/>
						<line number="14" hits="0"/>
						<line number="15" hits="0"/>
						<line number="18" hits="0"/>
						<line number="19" hits="0"/>
						<line number="20" hits="0"/>
						<line number="21" hits="0"/>
						<line number="22" hits="0"/>
						<line number="23" hits="0"/>
						<line number="24" hits="0"/>
						<line number="25" hits="0"/>
						<line number="26" hits="0"/>
						<line number="27" hits="0"/>
						<line number="28" hits="0"/>
						<line number="31" hits="0"/>
						<line number="32" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="api.workflows.texts.generate_reference" line-rate="0.8971" branch-rate="0.8" complexity="0">
			<classes>
				<class name="generate_reference_clases.py" filename="api/workflows/texts/generate_reference/generate_reference_clases.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
					</lines>
				</class>
				<class name="generate_reference_workflow.py" filename="api/workflows/texts/generate_reference/generate_reference_workflow.py" complexity="0" line-rate="0.8852" branch-rate="0.8">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="9" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="23" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="32" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="33" hits="1"/>
						<line number="34" hits="1"/>
						<line number="36" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="37"/>
						<line number="37" hits="0"/>
						<line number="38" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="41"/>
						<line number="39" hits="1"/>
						<line number="41" hits="0"/>
						<line number="42" hits="1"/>
						<line number="45" hits="1"/>
						<line number="47" hits="1"/>
						<line number="48" hits="1"/>
						<line number="53" hits="1"/>
						<line number="54" hits="0"/>
						<line number="57" hits="0"/>
						<line number="58" hits="1"/>
						<line number="59" hits="1"/>
						<line number="60" hits="1"/>
						<line number="62" hits="1"/>
						<line number="65" hits="1"/>
						<line number="66" hits="1"/>
						<line number="67" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="68" hits="1"/>
						<line number="69" hits="1"/>
						<line number="70" hits="1"/>
						<line number="77" hits="1"/>
						<line number="79" hits="1"/>
						<line number="84" hits="1"/>
						<line number="85" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="86" hits="1"/>
						<line number="87" hits="1"/>
						<line number="90" hits="1"/>
						<line number="91" hits="1"/>
						<line number="98" hits="1"/>
						<line number="100" hits="1"/>
						<line number="101" hits="1"/>
						<line number="102" hits="1"/>
						<line number="103" hits="1"/>
						<line number="106" hits="1"/>
						<line number="107" hits="0"/>
						<line number="108" hits="0"/>
						<line number="109" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="api.workflows.titles" line-rate="0" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="api/workflows/titles/__init__.py" complexity="0" line-rate="0" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="4" hits="0"/>
						<line number="8" hits="0"/>
						<line number="14" hits="0"/>
					</lines>
				</class>
				<class name="titles_router.py" filename="api/workflows/titles/titles_router.py" complexity="0" line-rate="0" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="3" hits="0"/>
						<line number="4" hits="0"/>
						<line number="5" hits="0"/>
						<line number="7" hits="0"/>
						<line number="8" hits="0"/>
						<line number="15" hits="0"/>
						<line number="17" hits="0"/>
						<line number="20" hits="0"/>
						<line number="21" hits="0"/>
						<line number="22" hits="0"/>
						<line number="23" hits="0"/>
						<line number="24" hits="0"/>
						<line number="25" hits="0"/>
						<line number="26" hits="0"/>
						<line number="31" hits="0"/>
						<line number="32" hits="0"/>
						<line number="33" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="34,36"/>
						<line number="34" hits="0"/>
						<line number="36" hits="0"/>
						<line number="37" hits="0"/>
						<line number="38" hits="0"/>
						<line number="40" hits="0"/>
						<line number="46" hits="0"/>
						<line number="47" hits="0"/>
						<line number="48" hits="0"/>
						<line number="50" hits="0"/>
						<line number="51" hits="0"/>
						<line number="55" hits="0"/>
						<line number="58" hits="0"/>
						<line number="59" hits="0"/>
						<line number="60" hits="0"/>
						<line number="61" hits="0"/>
						<line number="62" hits="0"/>
						<line number="63" hits="0"/>
						<line number="64" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="65,66"/>
						<line number="65" hits="0"/>
						<line number="66" hits="0"/>
						<line number="71" hits="0"/>
						<line number="72" hits="0"/>
						<line number="73" hits="0"/>
						<line number="74" hits="0"/>
						<line number="75" hits="0"/>
						<line number="76" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="77,78"/>
						<line number="77" hits="0"/>
						<line number="78" hits="0"/>
						<line number="81" hits="0"/>
						<line number="84" hits="0"/>
						<line number="85" hits="0"/>
						<line number="86" hits="0"/>
						<line number="87" hits="0"/>
						<line number="88" hits="0"/>
						<line number="89" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="90,94"/>
						<line number="90" hits="0"/>
						<line number="94" hits="0"/>
						<line number="95" hits="0"/>
						<line number="97" hits="0"/>
						<line number="98" hits="0"/>
						<line number="99" hits="0"/>
						<line number="100" hits="0"/>
						<line number="101" hits="0"/>
						<line number="105" hits="0"/>
						<line number="108" hits="0"/>
						<line number="109" hits="0"/>
						<line number="110" hits="0"/>
						<line number="111" hits="0"/>
						<line number="112" hits="0"/>
						<line number="113" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="114,117"/>
						<line number="114" hits="0"/>
						<line number="117" hits="0"/>
						<line number="118" hits="0"/>
						<line number="119" hits="0"/>
						<line number="122" hits="0"/>
						<line number="127" hits="0"/>
						<line number="130" hits="0"/>
					</lines>
				</class>
				<class name="titles_schemas.py" filename="api/workflows/titles/titles_schemas.py" complexity="0" line-rate="0" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="3" hits="0"/>
						<line number="5" hits="0"/>
						<line number="8" hits="0"/>
						<line number="9" hits="0"/>
						<line number="10" hits="0"/>
						<line number="11" hits="0"/>
						<line number="14" hits="0"/>
						<line number="15" hits="0"/>
						<line number="16" hits="0"/>
						<line number="19" hits="0"/>
						<line number="20" hits="0"/>
						<line number="21" hits="0"/>
						<line number="22" hits="0"/>
						<line number="23" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="api.workflows.topics" line-rate="0" branch-rate="1" complexity="0">
			<classes>
				<class name="__init__.py" filename="api/workflows/topics/__init__.py" complexity="0" line-rate="0" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="5" hits="0"/>
						<line number="8" hits="0"/>
						<line number="11" hits="0"/>
						<line number="15" hits="0"/>
						<line number="18" hits="0"/>
						<line number="22" hits="0"/>
						<line number="25" hits="0"/>
						<line number="29" hits="0"/>
						<line number="32" hits="0"/>
						<line number="36" hits="0"/>
						<line number="39" hits="0"/>
						<line number="43" hits="0"/>
						<line number="46" hits="0"/>
						<line number="50" hits="0"/>
						<line number="54" hits="0"/>
					</lines>
				</class>
				<class name="topics_router.py" filename="api/workflows/topics/topics_router.py" complexity="0" line-rate="0" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="3" hits="0"/>
						<line number="5" hits="0"/>
						<line number="6" hits="0"/>
						<line number="22" hits="0"/>
						<line number="26" hits="0"/>
						<line number="29" hits="0"/>
						<line number="30" hits="0"/>
						<line number="31" hits="0"/>
						<line number="32" hits="0"/>
						<line number="35" hits="0"/>
						<line number="36" hits="0"/>
						<line number="39" hits="0"/>
						<line number="46" hits="0"/>
						<line number="47" hits="0"/>
						<line number="51" hits="0"/>
						<line number="52" hits="0"/>
						<line number="53" hits="0"/>
						<line number="60" hits="0"/>
						<line number="64" hits="0"/>
						<line number="67" hits="0"/>
						<line number="68" hits="0"/>
						<line number="75" hits="0"/>
						<line number="79" hits="0"/>
						<line number="82" hits="0"/>
						<line number="83" hits="0"/>
						<line number="90" hits="0"/>
						<line number="94" hits="0"/>
						<line number="97" hits="0"/>
						<line number="98" hits="0"/>
						<line number="99" hits="0"/>
						<line number="106" hits="0"/>
						<line number="107" hits="0"/>
						<line number="110" hits="0"/>
						<line number="117" hits="0"/>
						<line number="118" hits="0"/>
						<line number="119" hits="0"/>
						<line number="120" hits="0"/>
						<line number="123" hits="0"/>
						<line number="124" hits="0"/>
						<line number="125" hits="0"/>
						<line number="126" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="api.workflows.users" line-rate="0" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="api/workflows/users/__init__.py" complexity="0" line-rate="0" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="3" hits="0"/>
					</lines>
				</class>
				<class name="users_router.py" filename="api/workflows/users/users_router.py" complexity="0" line-rate="0" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="2" hits="0"/>
						<line number="4" hits="0"/>
						<line number="5" hits="0"/>
						<line number="6" hits="0"/>
						<line number="8" hits="0"/>
						<line number="11" hits="0"/>
						<line number="12" hits="0"/>
						<line number="13" hits="0"/>
						<line number="14" hits="0"/>
						<line number="15" hits="0"/>
						<line number="16" hits="0"/>
						<line number="17" hits="0"/>
						<line number="20" hits="0"/>
						<line number="21" hits="0"/>
						<line number="22" hits="0"/>
						<line number="23" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="24,25"/>
						<line number="24" hits="0"/>
						<line number="25" hits="0"/>
						<line number="28" hits="0"/>
						<line number="29" hits="0"/>
						<line number="30" hits="0"/>
						<line number="31" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="32,35"/>
						<line number="32" hits="0"/>
						<line number="35" hits="0"/>
						<line number="36" hits="0"/>
						<line number="37" hits="0"/>
					</lines>
				</class>
				<class name="users_schemas.py" filename="api/workflows/users/users_schemas.py" complexity="0" line-rate="0" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="4" hits="0"/>
						<line number="5" hits="0"/>
						<line number="8" hits="0"/>
						<line number="9" hits="0"/>
						<line number="10" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="background_tasks" line-rate="0.3663" branch-rate="0" complexity="0">
			<classes>
				<class name="base_service_bus_task.py" filename="background_tasks/base_service_bus_task.py" complexity="0" line-rate="0.3663" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="9" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="34" hits="1"/>
						<line number="35" hits="1"/>
						<line number="50" hits="1"/>
						<line number="51" hits="1"/>
						<line number="52" hits="1"/>
						<line number="53" hits="1"/>
						<line number="54" hits="1"/>
						<line number="56" hits="1"/>
						<line number="63" hits="0"/>
						<line number="64" hits="0"/>
						<line number="65" hits="0"/>
						<line number="66" hits="0"/>
						<line number="71" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="72,77"/>
						<line number="72" hits="0"/>
						<line number="73" hits="0"/>
						<line number="74" hits="0"/>
						<line number="77" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="78,83"/>
						<line number="78" hits="0"/>
						<line number="83" hits="0"/>
						<line number="85" hits="0"/>
						<line number="90" hits="0"/>
						<line number="92" hits="0"/>
						<line number="93" hits="0"/>
						<line number="94" hits="0"/>
						<line number="96" hits="1"/>
						<line number="97" hits="1"/>
						<line number="106" hits="0"/>
						<line number="107" hits="0"/>
						<line number="108" hits="0"/>
						<line number="110" hits="0"/>
						<line number="111" hits="0"/>
						<line number="112" hits="0"/>
						<line number="113" hits="0"/>
						<line number="114" hits="0"/>
						<line number="116" hits="1"/>
						<line number="125" hits="0"/>
						<line number="126" hits="0"/>
						<line number="127" hits="0"/>
						<line number="128" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="129,130"/>
						<line number="129" hits="0"/>
						<line number="130" hits="0"/>
						<line number="133" hits="0"/>
						<line number="134" hits="0"/>
						<line number="136" hits="1"/>
						<line number="137" hits="1"/>
						<line number="146" hits="0"/>
						<line number="148" hits="1"/>
						<line number="155" hits="0"/>
						<line number="159" hits="0"/>
						<line number="161" hits="1"/>
						<line number="170" hits="0"/>
						<line number="171" hits="0"/>
						<line number="172" hits="0"/>
						<line number="173" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="171,174"/>
						<line number="174" hits="0"/>
						<line number="175" hits="0"/>
						<line number="176" hits="0"/>
						<line number="178" hits="0"/>
						<line number="179" hits="0"/>
						<line number="180" hits="0"/>
						<line number="184" hits="1"/>
						<line number="196" hits="0"/>
						<line number="199" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="exit,200"/>
						<line number="200" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="201,224"/>
						<line number="201" hits="0"/>
						<line number="204" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="208,217"/>
						<line number="208" hits="0"/>
						<line number="211" hits="0"/>
						<line number="215" hits="0"/>
						<line number="217" hits="0"/>
						<line number="218" hits="0"/>
						<line number="224" hits="0"/>
						<line number="227" hits="0"/>
						<line number="230" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="231,240"/>
						<line number="231" hits="0"/>
						<line number="234" hits="0"/>
						<line number="238" hits="0"/>
						<line number="240" hits="0"/>
						<line number="246" hits="0"/>
						<line number="251" hits="1"/>
						<line number="266" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="background_tasks.content_generation" line-rate="0.5968" branch-rate="0.2885" complexity="0">
			<classes>
				<class name="content_generation_task.py" filename="background_tasks/content_generation/content_generation_task.py" complexity="0" line-rate="0.5968" branch-rate="0.2885">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="21" hits="1"/>
						<line number="25" hits="1"/>
						<line number="29" hits="1"/>
						<line number="43" hits="1"/>
						<line number="44" hits="1"/>
						<line number="45" hits="1"/>
						<line number="46" hits="1"/>
						<line number="47" hits="1"/>
						<line number="48" hits="1"/>
						<line number="49" hits="1"/>
						<line number="52" hits="1"/>
						<line number="53" hits="1"/>
						<line number="64" hits="1"/>
						<line number="65" hits="1"/>
						<line number="66" hits="1"/>
						<line number="67" hits="1"/>
						<line number="68" hits="1"/>
						<line number="69" hits="1"/>
						<line number="71" hits="1"/>
						<line number="76" hits="1"/>
						<line number="77" hits="0"/>
						<line number="78" hits="1"/>
						<line number="79" hits="1"/>
						<line number="86" hits="0"/>
						<line number="88" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="89,95"/>
						<line number="89" hits="0"/>
						<line number="93" hits="0"/>
						<line number="95" hits="0"/>
						<line number="101" hits="0"/>
						<line number="104" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="105,109"/>
						<line number="105" hits="0"/>
						<line number="109" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="110,121"/>
						<line number="110" hits="0"/>
						<line number="121" hits="0"/>
						<line number="130" hits="0"/>
						<line number="135" hits="0"/>
						<line number="136" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="137,154"/>
						<line number="137" hits="0"/>
						<line number="138" hits="0"/>
						<line number="143" hits="1"/>
						<line number="144" hits="1"/>
						<line number="147" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="148,154"/>
						<line number="148" hits="1"/>
						<line number="154" hits="0"/>
						<line number="155" hits="1"/>
						<line number="161" hits="0"/>
						<line number="162" hits="1" branch="true" condition-coverage="0% (0/2)" missing-branches="exit,163"/>
						<line number="163" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="164,168"/>
						<line number="164" hits="1"/>
						<line number="168" hits="1"/>
						<line number="172" hits="1"/>
						<line number="175" hits="1"/>
						<line number="176" hits="1"/>
						<line number="178" hits="1"/>
						<line number="179" hits="1"/>
						<line number="180" hits="1" branch="true" condition-coverage="0% (0/2)" missing-branches="181,182"/>
						<line number="181" hits="0"/>
						<line number="182" hits="0"/>
						<line number="183" hits="0"/>
						<line number="185" hits="1"/>
						<line number="186" hits="1"/>
						<line number="187" hits="1"/>
						<line number="188" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="189" hits="1"/>
						<line number="190" hits="1"/>
						<line number="192" hits="1"/>
						<line number="198" hits="1"/>
						<line number="199" hits="1"/>
						<line number="200" hits="1"/>
						<line number="205" hits="1"/>
						<line number="209" hits="1"/>
						<line number="210" hits="1"/>
						<line number="219" hits="0"/>
						<line number="221" hits="1"/>
						<line number="223" hits="1"/>
						<line number="224" hits="1"/>
						<line number="227" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="228" hits="1"/>
						<line number="229" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="230" hits="1"/>
						<line number="232" hits="1"/>
						<line number="233" hits="1"/>
						<line number="236" hits="1"/>
						<line number="237" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="238" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="237"/>
						<line number="242" hits="1"/>
						<line number="245" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="246" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="247"/>
						<line number="247" hits="1"/>
						<line number="248" hits="1"/>
						<line number="252" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="255"/>
						<line number="253" hits="1"/>
						<line number="255" hits="1"/>
						<line number="258" hits="1"/>
						<line number="260" hits="1"/>
						<line number="265" hits="0"/>
						<line number="266" hits="0"/>
						<line number="268" hits="0"/>
						<line number="269" hits="0"/>
						<line number="270" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="297"/>
						<line number="271" hits="1"/>
						<line number="277" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="278,292"/>
						<line number="278" hits="0"/>
						<line number="286" hits="0"/>
						<line number="287" hits="0"/>
						<line number="288" hits="0"/>
						<line number="290" hits="0"/>
						<line number="292" hits="0"/>
						<line number="295" hits="0"/>
						<line number="297" hits="0"/>
						<line number="301" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="302,304"/>
						<line number="302" hits="0"/>
						<line number="304" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="305,311"/>
						<line number="305" hits="0"/>
						<line number="306" hits="0"/>
						<line number="307" hits="1"/>
						<line number="311" hits="1"/>
						<line number="312" hits="1"/>
						<line number="315" hits="0"/>
						<line number="317" hits="1"/>
						<line number="318" hits="1"/>
						<line number="319" hits="1"/>
						<line number="322" hits="0"/>
						<line number="323" hits="0"/>
						<line number="324" hits="0"/>
						<line number="325" hits="0"/>
						<line number="328" hits="0"/>
						<line number="330" hits="0"/>
						<line number="332" hits="1"/>
						<line number="334" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="335,337"/>
						<line number="335" hits="1"/>
						<line number="337" hits="1"/>
						<line number="338" hits="1"/>
						<line number="339" hits="1"/>
						<line number="341" hits="1" branch="true" condition-coverage="0% (0/2)" missing-branches="342,352"/>
						<line number="342" hits="1"/>
						<line number="347" hits="0"/>
						<line number="348" hits="0"/>
						<line number="352" hits="0"/>
						<line number="354" hits="0"/>
						<line number="355" hits="0"/>
						<line number="356" hits="0"/>
						<line number="359" hits="0"/>
						<line number="361" hits="1"/>
						<line number="365" hits="0"/>
						<line number="366" hits="1" branch="true" condition-coverage="0% (0/2)" missing-branches="367,380"/>
						<line number="367" hits="1" branch="true" condition-coverage="0% (0/2)" missing-branches="368,377"/>
						<line number="368" hits="1"/>
						<line number="375" hits="0"/>
						<line number="377" hits="0"/>
						<line number="380" hits="0"/>
						<line number="382" hits="1"/>
						<line number="384" hits="0"/>
						<line number="385" hits="0"/>
						<line number="386" hits="0"/>
						<line number="389" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="390,393"/>
						<line number="390" hits="0"/>
						<line number="391" hits="0"/>
						<line number="393" hits="1"/>
						<line number="398" hits="1"/>
						<line number="399" hits="1"/>
						<line number="402" hits="1"/>
						<line number="403" hits="0"/>
						<line number="404" hits="1"/>
						<line number="407" hits="1"/>
						<line number="410" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="411"/>
						<line number="411" hits="0"/>
						<line number="413" hits="0"/>
						<line number="416" hits="0"/>
						<line number="417" hits="0"/>
						<line number="418" hits="0"/>
						<line number="419" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="background_tasks.process_document_task" line-rate="0.6485" branch-rate="0.25" complexity="0">
			<classes>
				<class name="process_document_task.py" filename="background_tasks/process_document_task/process_document_task.py" complexity="0" line-rate="0.6935" branch-rate="0.3594">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="15" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="30" hits="1"/>
						<line number="33" hits="1"/>
						<line number="37" hits="1"/>
						<line number="38" hits="1"/>
						<line number="49" hits="1"/>
						<line number="51" hits="1"/>
						<line number="52" hits="1"/>
						<line number="55" hits="1"/>
						<line number="56" hits="1"/>
						<line number="66" hits="1"/>
						<line number="67" hits="1"/>
						<line number="68" hits="1"/>
						<line number="69" hits="1"/>
						<line number="70" hits="1"/>
						<line number="71" hits="1"/>
						<line number="73" hits="1"/>
						<line number="75" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="76,78"/>
						<line number="76" hits="0"/>
						<line number="77" hits="0"/>
						<line number="78" hits="1"/>
						<line number="80" hits="1"/>
						<line number="86" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="87" hits="1"/>
						<line number="88" hits="1"/>
						<line number="90" hits="1"/>
						<line number="91" hits="1"/>
						<line number="94" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="95" hits="1"/>
						<line number="98" hits="1"/>
						<line number="100" hits="1"/>
						<line number="104" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="105" hits="1"/>
						<line number="106" hits="1"/>
						<line number="107" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="108" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="109" hits="1"/>
						<line number="112" hits="1"/>
						<line number="116" hits="1"/>
						<line number="120" hits="1"/>
						<line number="122" hits="1"/>
						<line number="134" hits="1"/>
						<line number="135" hits="1"/>
						<line number="136" hits="1"/>
						<line number="149" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="157"/>
						<line number="150" hits="1"/>
						<line number="153" hits="1"/>
						<line number="157" hits="1"/>
						<line number="158" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="162"/>
						<line number="159" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="160"/>
						<line number="160" hits="1"/>
						<line number="162" hits="1"/>
						<line number="164" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="205"/>
						<line number="165" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="172"/>
						<line number="166" hits="1"/>
						<line number="169" hits="1"/>
						<line number="170" hits="1"/>
						<line number="172" hits="1"/>
						<line number="183" hits="1"/>
						<line number="189" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="190,194"/>
						<line number="190" hits="1"/>
						<line number="194" hits="1"/>
						<line number="199" hits="1"/>
						<line number="203" hits="0"/>
						<line number="205" hits="0"/>
						<line number="207" hits="1"/>
						<line number="208" hits="1"/>
						<line number="211" hits="1"/>
						<line number="213" hits="1"/>
						<line number="216" hits="1"/>
						<line number="217" hits="1"/>
						<line number="222" hits="1"/>
						<line number="223" hits="1"/>
						<line number="224" hits="1"/>
						<line number="227" hits="0"/>
						<line number="228" hits="1"/>
						<line number="233" hits="0"/>
						<line number="236" hits="0"/>
						<line number="237" hits="0"/>
						<line number="240" hits="1"/>
						<line number="242" hits="1"/>
						<line number="258" hits="0"/>
						<line number="259" hits="0"/>
						<line number="261" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="262,265"/>
						<line number="262" hits="0"/>
						<line number="263" hits="1"/>
						<line number="265" hits="1"/>
						<line number="296" hits="0"/>
						<line number="297" hits="0"/>
						<line number="298" hits="0"/>
						<line number="300" hits="1"/>
						<line number="301" hits="1"/>
						<line number="307" hits="1"/>
						<line number="308" hits="0"/>
						<line number="309" hits="1"/>
						<line number="311" hits="1"/>
						<line number="312" hits="1"/>
						<line number="315" hits="1"/>
						<line number="316" hits="1"/>
						<line number="317" hits="1"/>
						<line number="318" hits="1"/>
						<line number="320" hits="1"/>
						<line number="325" hits="1"/>
						<line number="328" hits="1"/>
						<line number="329" hits="1"/>
						<line number="330" hits="1"/>
						<line number="331" hits="1"/>
						<line number="332" hits="1"/>
						<line number="335" hits="1"/>
						<line number="336" hits="1"/>
						<line number="338" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="339" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="340" hits="1"/>
						<line number="343" hits="1"/>
						<line number="347" hits="1"/>
						<line number="350" hits="0"/>
						<line number="351" hits="0"/>
						<line number="354" hits="0"/>
						<line number="356" hits="1"/>
						<line number="359" hits="1"/>
						<line number="360" hits="1"/>
						<line number="362" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="363"/>
						<line number="363" hits="0"/>
						<line number="365" hits="1"/>
						<line number="367" hits="1"/>
						<line number="373" hits="1"/>
						<line number="383" hits="1"/>
						<line number="410" hits="1"/>
						<line number="415" hits="1"/>
						<line number="416" hits="1"/>
						<line number="418" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="425"/>
						<line number="419" hits="1" branch="true" condition-coverage="0% (0/2)" missing-branches="420,425"/>
						<line number="420" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="421,422"/>
						<line number="421" hits="1"/>
						<line number="422" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="419"/>
						<line number="423" hits="1"/>
						<line number="425" hits="1"/>
						<line number="436" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="437,439"/>
						<line number="437" hits="0"/>
						<line number="439" hits="1"/>
						<line number="440" hits="1"/>
						<line number="441" hits="1"/>
						<line number="442" hits="1"/>
						<line number="444" hits="0"/>
						<line number="446" hits="0"/>
						<line number="447" hits="0"/>
						<line number="448" hits="1"/>
						<line number="451" hits="1"/>
						<line number="452" hits="1"/>
						<line number="455" hits="0"/>
						<line number="462" hits="0"/>
						<line number="463" hits="0"/>
						<line number="465" hits="0"/>
						<line number="466" hits="0"/>
						<line number="467" hits="0"/>
						<line number="468" hits="0"/>
						<line number="473" hits="0"/>
						<line number="475" hits="0"/>
						<line number="478" hits="1"/>
						<line number="479" hits="0"/>
						<line number="480" hits="0"/>
						<line number="482" hits="0"/>
						<line number="484" hits="1"/>
						<line number="488" hits="1"/>
						<line number="489" hits="1"/>
						<line number="490" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="exit,491"/>
						<line number="491" hits="1"/>
						<line number="492" hits="1"/>
						<line number="493" hits="0"/>
						<line number="494" hits="0"/>
						<line number="496" hits="1"/>
						<line number="502" hits="0"/>
						<line number="503" hits="0"/>
						<line number="504" hits="0"/>
						<line number="505" hits="0"/>
						<line number="507" hits="0"/>
						<line number="510" hits="0"/>
						<line number="512" hits="1"/>
						<line number="518" hits="1"/>
						<line number="519" hits="0"/>
						<line number="520" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="521,558"/>
						<line number="521" hits="0"/>
						<line number="522" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="523,525"/>
						<line number="523" hits="1"/>
						<line number="525" hits="1" branch="true" condition-coverage="0% (0/2)" missing-branches="526,537"/>
						<line number="526" hits="1" branch="true" condition-coverage="0% (0/2)" missing-branches="527,532"/>
						<line number="527" hits="1" branch="true" condition-coverage="0% (0/2)" missing-branches="525,528"/>
						<line number="528" hits="1"/>
						<line number="532" hits="0"/>
						<line number="537" hits="0"/>
						<line number="538" hits="0"/>
						<line number="544" hits="0"/>
						<line number="545" hits="1"/>
						<line number="553" hits="0"/>
						<line number="554" hits="0"/>
						<line number="557" hits="0"/>
						<line number="558" hits="0"/>
						<line number="560" hits="1"/>
						<line number="568" hits="0"/>
						<line number="569" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="572,583"/>
						<line number="572" hits="0"/>
						<line number="582" hits="0"/>
						<line number="583" hits="0"/>
						<line number="585" hits="1"/>
						<line number="593" hits="1"/>
						<line number="595" hits="1" branch="true" condition-coverage="0% (0/2)" missing-branches="596,600"/>
						<line number="596" hits="1"/>
						<line number="597" hits="1"/>
						<line number="600" hits="1" branch="true" condition-coverage="0% (0/2)" missing-branches="601,604"/>
						<line number="601" hits="1"/>
						<line number="602" hits="0"/>
						<line number="604" hits="0"/>
						<line number="605" hits="0"/>
						<line number="606" hits="1"/>
						<line number="616" hits="0"/>
						<line number="617" hits="0"/>
						<line number="618" hits="1"/>
						<line number="619" hits="1"/>
						<line number="620" hits="1"/>
						<line number="622" hits="1"/>
						<line number="632" hits="0"/>
						<line number="634" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="635,660"/>
						<line number="635" hits="0"/>
						<line number="637" hits="0"/>
						<line number="641" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="642"/>
						<line number="642" hits="0"/>
						<line number="644" hits="1"/>
						<line number="645" hits="1"/>
						<line number="646" hits="1"/>
						<line number="647" hits="1"/>
						<line number="648" hits="1"/>
						<line number="651" hits="1"/>
						<line number="658" hits="1"/>
						<line number="660" hits="1"/>
					</lines>
				</class>
				<class name="schemas.py" filename="background_tasks/process_document_task/schemas.py" complexity="0" line-rate="0.8182" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="32" hits="1"/>
						<line number="33" hits="1"/>
						<line number="34" hits="1"/>
						<line number="35" hits="1"/>
						<line number="36" hits="1"/>
						<line number="37" hits="1"/>
						<line number="38" hits="1"/>
						<line number="39" hits="1"/>
						<line number="41" hits="1"/>
						<line number="43" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="44,45"/>
						<line number="44" hits="0"/>
						<line number="45" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="46,47"/>
						<line number="46" hits="0"/>
						<line number="47" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="48,49"/>
						<line number="48" hits="0"/>
						<line number="49" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="exit,50"/>
						<line number="50" hits="0"/>
						<line number="53" hits="1"/>
						<line number="54" hits="1"/>
					</lines>
				</class>
				<class name="utils.py" filename="background_tasks/process_document_task/utils.py" complexity="0" line-rate="0.1579" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="9" hits="1"/>
						<line number="11" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="12,14"/>
						<line number="12" hits="0"/>
						<line number="14" hits="0"/>
						<line number="15" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="16,31"/>
						<line number="16" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="17,18"/>
						<line number="17" hits="0"/>
						<line number="18" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="19,20"/>
						<line number="19" hits="0"/>
						<line number="20" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="21,30"/>
						<line number="21" hits="0"/>
						<line number="30" hits="0"/>
						<line number="31" hits="0"/>
						<line number="34" hits="1"/>
						<line number="36" hits="0"/>
						<line number="37" hits="0"/>
						<line number="38" hits="0"/>
						<line number="39" hits="0"/>
						<line number="40" hits="0"/>
						<line number="41" hits="0"/>
						<line number="42" hits="0"/>
						<line number="43" hits="0"/>
						<line number="44" hits="0"/>
						<line number="46" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="47,49"/>
						<line number="47" hits="0"/>
						<line number="49" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="50,52"/>
						<line number="50" hits="0"/>
						<line number="52" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="53,55"/>
						<line number="53" hits="0"/>
						<line number="55" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="56,58"/>
						<line number="56" hits="0"/>
						<line number="58" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="59,63"/>
						<line number="59" hits="0"/>
						<line number="63" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="common" line-rate="0.8831" branch-rate="0" complexity="0">
			<classes>
				<class name="application_environment.py" filename="common/application_environment.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
					</lines>
				</class>
				<class name="application_settings.py" filename="common/application_settings.py" complexity="0" line-rate="0.8657" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="14" hits="1"/>
						<line number="17" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="32" hits="1"/>
						<line number="33" hits="1"/>
						<line number="34" hits="1"/>
						<line number="37" hits="1"/>
						<line number="38" hits="1"/>
						<line number="39" hits="1"/>
						<line number="40" hits="1"/>
						<line number="41" hits="1"/>
						<line number="44" hits="1"/>
						<line number="45" hits="1"/>
						<line number="46" hits="1"/>
						<line number="47" hits="1"/>
						<line number="48" hits="1"/>
						<line number="49" hits="1"/>
						<line number="50" hits="1"/>
						<line number="53" hits="1"/>
						<line number="54" hits="1"/>
						<line number="55" hits="1"/>
						<line number="56" hits="1"/>
						<line number="59" hits="1"/>
						<line number="60" hits="1"/>
						<line number="61" hits="1"/>
						<line number="64" hits="1"/>
						<line number="65" hits="1"/>
						<line number="66" hits="1"/>
						<line number="67" hits="1"/>
						<line number="68" hits="1"/>
						<line number="69" hits="1"/>
						<line number="72" hits="1"/>
						<line number="73" hits="1"/>
						<line number="76" hits="1"/>
						<line number="77" hits="1"/>
						<line number="80" hits="1"/>
						<line number="83" hits="1"/>
						<line number="86" hits="1"/>
						<line number="87" hits="1"/>
						<line number="89" hits="1"/>
						<line number="96" hits="1"/>
						<line number="97" hits="1"/>
						<line number="105" hits="1"/>
						<line number="106" hits="0"/>
						<line number="112" hits="0"/>
						<line number="115" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="116,125"/>
						<line number="116" hits="0"/>
						<line number="117" hits="0"/>
						<line number="122" hits="0"/>
						<line number="123" hits="0"/>
						<line number="124" hits="0"/>
						<line number="125" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="domain" line-rate="1" branch-rate="1" complexity="0">
			<classes>
				<class name="models.py" filename="domain/models.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="6" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="33" hits="1"/>
						<line number="34" hits="1"/>
						<line number="35" hits="1"/>
						<line number="36" hits="1"/>
						<line number="37" hits="1"/>
						<line number="38" hits="1"/>
						<line number="39" hits="1"/>
						<line number="40" hits="1"/>
						<line number="41" hits="1"/>
						<line number="42" hits="1"/>
						<line number="43" hits="1"/>
						<line number="44" hits="1"/>
						<line number="45" hits="1"/>
						<line number="46" hits="1"/>
						<line number="47" hits="1"/>
						<line number="48" hits="1"/>
						<line number="49" hits="1"/>
						<line number="50" hits="1"/>
						<line number="51" hits="1"/>
						<line number="52" hits="1"/>
						<line number="53" hits="1"/>
						<line number="56" hits="1"/>
						<line number="57" hits="1"/>
						<line number="58" hits="1"/>
						<line number="59" hits="1"/>
						<line number="60" hits="1"/>
						<line number="63" hits="1"/>
						<line number="64" hits="1"/>
						<line number="65" hits="1"/>
						<line number="66" hits="1"/>
						<line number="67" hits="1"/>
						<line number="68" hits="1"/>
						<line number="71" hits="1"/>
						<line number="72" hits="1"/>
						<line number="73" hits="1"/>
						<line number="74" hits="1"/>
						<line number="75" hits="1"/>
						<line number="76" hits="1"/>
						<line number="77" hits="1"/>
						<line number="78" hits="1"/>
						<line number="81" hits="1"/>
						<line number="82" hits="1"/>
						<line number="83" hits="1"/>
						<line number="84" hits="1"/>
						<line number="87" hits="1"/>
						<line number="88" hits="1"/>
						<line number="89" hits="1"/>
						<line number="90" hits="1"/>
						<line number="91" hits="1"/>
						<line number="92" hits="1"/>
						<line number="93" hits="1"/>
						<line number="96" hits="1"/>
						<line number="97" hits="1"/>
						<line number="98" hits="1"/>
						<line number="101" hits="1"/>
						<line number="102" hits="1"/>
						<line number="103" hits="1"/>
						<line number="106" hits="1"/>
						<line number="107" hits="1"/>
						<line number="108" hits="1"/>
						<line number="109" hits="1"/>
						<line number="110" hits="1"/>
						<line number="111" hits="1"/>
						<line number="112" hits="1"/>
						<line number="113" hits="1"/>
						<line number="114" hits="1"/>
						<line number="115" hits="1"/>
						<line number="116" hits="1"/>
						<line number="117" hits="1"/>
						<line number="118" hits="1"/>
						<line number="119" hits="1"/>
						<line number="120" hits="1"/>
						<line number="121" hits="1"/>
						<line number="122" hits="1"/>
						<line number="125" hits="1"/>
						<line number="126" hits="1"/>
						<line number="127" hits="1"/>
						<line number="128" hits="1"/>
						<line number="129" hits="1"/>
						<line number="130" hits="1"/>
						<line number="131" hits="1"/>
						<line number="132" hits="1"/>
						<line number="133" hits="1"/>
						<line number="134" hits="1"/>
						<line number="135" hits="1"/>
						<line number="136" hits="1"/>
						<line number="137" hits="1"/>
						<line number="138" hits="1"/>
						<line number="139" hits="1"/>
						<line number="140" hits="1"/>
						<line number="143" hits="1"/>
						<line number="144" hits="1"/>
						<line number="145" hits="1"/>
						<line number="146" hits="1"/>
						<line number="149" hits="1"/>
						<line number="150" hits="1"/>
						<line number="151" hits="1"/>
						<line number="152" hits="1"/>
						<line number="153" hits="1"/>
						<line number="154" hits="1"/>
						<line number="157" hits="1"/>
						<line number="158" hits="1"/>
						<line number="159" hits="1"/>
						<line number="160" hits="1"/>
						<line number="161" hits="1"/>
						<line number="162" hits="1"/>
						<line number="165" hits="1"/>
						<line number="166" hits="1"/>
						<line number="167" hits="1"/>
						<line number="170" hits="1"/>
						<line number="173" hits="1"/>
						<line number="176" hits="1"/>
						<line number="179" hits="1"/>
						<line number="183" hits="1"/>
						<line number="188" hits="1"/>
						<line number="189" hits="1"/>
						<line number="190" hits="1"/>
						<line number="193" hits="1"/>
						<line number="196" hits="1"/>
						<line number="199" hits="1"/>
						<line number="202" hits="1"/>
						<line number="206" hits="1"/>
						<line number="211" hits="1"/>
						<line number="212" hits="1"/>
						<line number="213" hits="1"/>
						<line number="214" hits="1"/>
						<line number="215" hits="1"/>
						<line number="218" hits="1"/>
						<line number="221" hits="1"/>
						<line number="225" hits="1"/>
						<line number="229" hits="1"/>
						<line number="232" hits="1"/>
						<line number="235" hits="1"/>
						<line number="238" hits="1"/>
						<line number="239" hits="1"/>
						<line number="245" hits="1"/>
						<line number="246" hits="1"/>
						<line number="249" hits="1"/>
						<line number="252" hits="1"/>
						<line number="256" hits="1"/>
						<line number="261" hits="1"/>
						<line number="262" hits="1"/>
						<line number="268" hits="1"/>
						<line number="269" hits="1"/>
						<line number="270" hits="1"/>
						<line number="271" hits="1"/>
						<line number="272" hits="1"/>
						<line number="275" hits="1"/>
						<line number="278" hits="1"/>
						<line number="282" hits="1"/>
						<line number="286" hits="1"/>
						<line number="289" hits="1"/>
						<line number="296" hits="1"/>
						<line number="297" hits="1"/>
						<line number="303" hits="1"/>
						<line number="304" hits="1"/>
						<line number="305" hits="1"/>
						<line number="306" hits="1"/>
						<line number="309" hits="1"/>
						<line number="312" hits="1"/>
						<line number="316" hits="1"/>
						<line number="320" hits="1"/>
						<line number="324" hits="1"/>
						<line number="329" hits="1"/>
						<line number="330" hits="1"/>
						<line number="336" hits="1"/>
						<line number="337" hits="1"/>
						<line number="344" hits="1"/>
						<line number="351" hits="1"/>
						<line number="352" hits="1"/>
						<line number="353" hits="1"/>
						<line number="356" hits="1"/>
						<line number="359" hits="1"/>
						<line number="363" hits="1"/>
						<line number="367" hits="1"/>
						<line number="368" hits="1"/>
						<line number="369" hits="1"/>
						<line number="375" hits="1"/>
						<line number="376" hits="1"/>
						<line number="382" hits="1"/>
						<line number="383" hits="1"/>
						<line number="384" hits="1"/>
						<line number="390" hits="1"/>
						<line number="393" hits="1"/>
						<line number="396" hits="1"/>
						<line number="400" hits="1"/>
						<line number="404" hits="1"/>
						<line number="405" hits="1"/>
						<line number="408" hits="1"/>
						<line number="411" hits="1"/>
						<line number="414" hits="1"/>
						<line number="415" hits="1"/>
						<line number="418" hits="1"/>
						<line number="419" hits="1"/>
						<line number="425" hits="1"/>
						<line number="426" hits="1"/>
						<line number="433" hits="1"/>
						<line number="434" hits="1"/>
						<line number="435" hits="1"/>
						<line number="438" hits="1"/>
						<line number="441" hits="1"/>
						<line number="445" hits="1"/>
						<line number="449" hits="1"/>
						<line number="453" hits="1"/>
						<line number="454" hits="1"/>
						<line number="458" hits="1"/>
						<line number="460" hits="1"/>
						<line number="465" hits="1"/>
						<line number="466" hits="1"/>
						<line number="472" hits="1"/>
						<line number="473" hits="1"/>
						<line number="474" hits="1"/>
						<line number="481" hits="1"/>
						<line number="484" hits="1"/>
						<line number="487" hits="1"/>
						<line number="491" hits="1"/>
						<line number="495" hits="1"/>
						<line number="496" hits="1"/>
						<line number="500" hits="1"/>
						<line number="503" hits="1"/>
						<line number="504" hits="1"/>
						<line number="510" hits="1"/>
						<line number="511" hits="1"/>
						<line number="512" hits="1"/>
						<line number="513" hits="1"/>
						<line number="521" hits="1"/>
						<line number="525" hits="1"/>
						<line number="528" hits="1"/>
						<line number="531" hits="1"/>
						<line number="535" hits="1"/>
						<line number="539" hits="1"/>
						<line number="540" hits="1"/>
						<line number="541" hits="1"/>
						<line number="544" hits="1"/>
						<line number="549" hits="1"/>
						<line number="550" hits="1"/>
						<line number="553" hits="1"/>
						<line number="556" hits="1"/>
						<line number="559" hits="1"/>
						<line number="562" hits="1"/>
						<line number="566" hits="1"/>
						<line number="571" hits="1"/>
						<line number="572" hits="1"/>
						<line number="578" hits="1"/>
						<line number="579" hits="1"/>
						<line number="580" hits="1"/>
						<line number="587" hits="1"/>
						<line number="588" hits="1"/>
						<line number="589" hits="1"/>
						<line number="592" hits="1"/>
						<line number="595" hits="1"/>
						<line number="599" hits="1"/>
						<line number="603" hits="1"/>
						<line number="604" hits="1"/>
						<line number="608" hits="1"/>
						<line number="613" hits="1"/>
						<line number="616" hits="1"/>
						<line number="617" hits="1"/>
						<line number="623" hits="1"/>
						<line number="624" hits="1"/>
						<line number="627" hits="1"/>
						<line number="630" hits="1"/>
						<line number="634" hits="1"/>
						<line number="638" hits="1"/>
						<line number="641" hits="1"/>
						<line number="642" hits="1"/>
						<line number="649" hits="1"/>
						<line number="656" hits="1"/>
						<line number="659" hits="1"/>
						<line number="662" hits="1"/>
						<line number="666" hits="1"/>
						<line number="670" hits="1"/>
						<line number="671" hits="1"/>
						<line number="674" hits="1"/>
						<line number="675" hits="1"/>
						<line number="676" hits="1"/>
						<line number="677" hits="1"/>
						<line number="681" hits="1"/>
						<line number="685" hits="1"/>
						<line number="689" hits="1"/>
						<line number="693" hits="1"/>
						<line number="694" hits="1"/>
						<line number="697" hits="1"/>
						<line number="698" hits="1"/>
						<line number="699" hits="1"/>
						<line number="702" hits="1"/>
						<line number="705" hits="1"/>
						<line number="709" hits="1"/>
						<line number="713" hits="1"/>
						<line number="714" hits="1"/>
						<line number="718" hits="1"/>
						<line number="722" hits="1"/>
						<line number="726" hits="1"/>
						<line number="734" hits="1"/>
						<line number="735" hits="1"/>
						<line number="736" hits="1"/>
						<line number="739" hits="1"/>
						<line number="740" hits="1"/>
						<line number="741" hits="1"/>
						<line number="742" hits="1"/>
						<line number="743" hits="1"/>
						<line number="744" hits="1"/>
						<line number="745" hits="1"/>
						<line number="746" hits="1"/>
						<line number="749" hits="1"/>
						<line number="752" hits="1"/>
						<line number="756" hits="1"/>
						<line number="760" hits="1"/>
						<line number="765" hits="1"/>
						<line number="766" hits="1"/>
						<line number="767" hits="1"/>
						<line number="772" hits="1"/>
						<line number="773" hits="1"/>
						<line number="774" hits="1"/>
						<line number="775" hits="1"/>
						<line number="778" hits="1"/>
						<line number="781" hits="1"/>
						<line number="785" hits="1"/>
						<line number="789" hits="1"/>
						<line number="791" hits="1"/>
						<line number="798" hits="1"/>
						<line number="799" hits="1"/>
						<line number="800" hits="1"/>
						<line number="803" hits="1"/>
						<line number="804" hits="1"/>
						<line number="805" hits="1"/>
						<line number="808" hits="1"/>
						<line number="811" hits="1"/>
						<line number="815" hits="1"/>
						<line number="819" hits="1"/>
						<line number="820" hits="1"/>
						<line number="825" hits="1"/>
						<line number="832" hits="1"/>
						<line number="835" hits="1"/>
						<line number="836" hits="1"/>
						<line number="839" hits="1"/>
						<line number="840" hits="1"/>
						<line number="841" hits="1"/>
						<line number="842" hits="1"/>
						<line number="843" hits="1"/>
						<line number="844" hits="1"/>
						<line number="847" hits="1"/>
						<line number="850" hits="1"/>
						<line number="854" hits="1"/>
						<line number="858" hits="1"/>
						<line number="859" hits="1"/>
						<line number="864" hits="1"/>
						<line number="865" hits="1"/>
						<line number="866" hits="1"/>
						<line number="867" hits="1"/>
						<line number="868" hits="1"/>
						<line number="869" hits="1"/>
						<line number="870" hits="1"/>
						<line number="871" hits="1"/>
						<line number="872" hits="1"/>
						<line number="874" hits="1"/>
						<line number="875" hits="1"/>
						<line number="876" hits="1"/>
						<line number="877" hits="1"/>
						<line number="878" hits="1"/>
						<line number="879" hits="1"/>
						<line number="880" hits="1"/>
						<line number="881" hits="1"/>
						<line number="882" hits="1"/>
						<line number="883" hits="1"/>
						<line number="884" hits="1"/>
						<line number="885" hits="1"/>
						<line number="888" hits="1"/>
						<line number="890" hits="1"/>
						<line number="891" hits="1"/>
						<line number="892" hits="1"/>
						<line number="893" hits="1"/>
						<line number="895" hits="1"/>
						<line number="898" hits="1"/>
						<line number="901" hits="1"/>
						<line number="905" hits="1"/>
						<line number="909" hits="1"/>
						<line number="916" hits="1"/>
						<line number="920" hits="1"/>
						<line number="927" hits="1"/>
						<line number="928" hits="1"/>
						<line number="929" hits="1"/>
						<line number="930" hits="1"/>
						<line number="933" hits="1"/>
						<line number="936" hits="1"/>
						<line number="940" hits="1"/>
						<line number="944" hits="1"/>
						<line number="947" hits="1"/>
						<line number="948" hits="1"/>
						<line number="949" hits="1"/>
						<line number="954" hits="1"/>
						<line number="955" hits="1"/>
						<line number="956" hits="1"/>
						<line number="957" hits="1"/>
						<line number="959" hits="1"/>
						<line number="960" hits="1"/>
						<line number="961" hits="1"/>
						<line number="962" hits="1"/>
						<line number="965" hits="1"/>
						<line number="968" hits="1"/>
						<line number="971" hits="1"/>
						<line number="975" hits="1"/>
						<line number="979" hits="1"/>
						<line number="982" hits="1"/>
						<line number="984" hits="1"/>
						<line number="985" hits="1"/>
						<line number="986" hits="1"/>
						<line number="991" hits="1"/>
						<line number="992" hits="1"/>
						<line number="993" hits="1"/>
						<line number="994" hits="1"/>
						<line number="997" hits="1"/>
						<line number="1000" hits="1"/>
						<line number="1004" hits="1"/>
						<line number="1008" hits="1"/>
						<line number="1011" hits="1"/>
						<line number="1014" hits="1"/>
						<line number="1015" hits="1"/>
						<line number="1016" hits="1"/>
						<line number="1022" hits="1"/>
						<line number="1023" hits="1"/>
						<line number="1024" hits="1"/>
						<line number="1025" hits="1"/>
						<line number="1026" hits="1"/>
						<line number="1034" hits="1"/>
						<line number="1037" hits="1"/>
						<line number="1040" hits="1"/>
						<line number="1044" hits="1"/>
						<line number="1048" hits="1"/>
						<line number="1049" hits="1"/>
						<line number="1052" hits="1"/>
						<line number="1053" hits="1"/>
						<line number="1056" hits="1"/>
						<line number="1057" hits="1"/>
						<line number="1061" hits="1"/>
						<line number="1062" hits="1"/>
						<line number="1063" hits="1"/>
						<line number="1064" hits="1"/>
						<line number="1065" hits="1"/>
						<line number="1066" hits="1"/>
						<line number="1067" hits="1"/>
						<line number="1068" hits="1"/>
						<line number="1069" hits="1"/>
						<line number="1070" hits="1"/>
						<line number="1071" hits="1"/>
						<line number="1072" hits="1"/>
						<line number="1073" hits="1"/>
						<line number="1074" hits="1"/>
						<line number="1075" hits="1"/>
						<line number="1077" hits="1"/>
						<line number="1080" hits="1"/>
						<line number="1081" hits="1"/>
						<line number="1082" hits="1"/>
						<line number="1083" hits="1"/>
						<line number="1084" hits="1"/>
						<line number="1085" hits="1"/>
						<line number="1086" hits="1"/>
						<line number="1087" hits="1"/>
						<line number="1090" hits="1"/>
						<line number="1091" hits="1"/>
						<line number="1092" hits="1"/>
						<line number="1093" hits="1"/>
						<line number="1094" hits="1"/>
						<line number="1095" hits="1"/>
						<line number="1096" hits="1"/>
						<line number="1097" hits="1"/>
						<line number="1098" hits="1"/>
						<line number="1099" hits="1"/>
						<line number="1100" hits="1"/>
						<line number="1101" hits="1"/>
						<line number="1102" hits="1"/>
						<line number="1103" hits="1"/>
						<line number="1104" hits="1"/>
						<line number="1107" hits="1"/>
						<line number="1110" hits="1"/>
						<line number="1114" hits="1"/>
					</lines>
				</class>
				<class name="models_toolkit.py" filename="domain/models_toolkit.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="6" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="14" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="19" hits="1"/>
						<line number="22" hits="1"/>
						<line number="25" hits="1"/>
						<line number="29" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
	</packages>
</coverage>
