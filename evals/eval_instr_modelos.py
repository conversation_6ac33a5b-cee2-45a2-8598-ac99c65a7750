"""
------------------------------
eval_instrucciones_pydantic.py
• seleccion_modelo   - El juez LLM elige la mejor salida.

CLI:
    --index_id      (obligatorio)  → Índice a evaluar.
    --judge_model   proveedor/modelo(por defecto openai/gpt-4o)
"""

from __future__ import annotations

import argparse
import asyncio
import json
import os
import sys
from collections import defaultdict
from pathlib import Path
from typing import Dict, Tuple

import logfire
from dotenv import load_dotenv
from instructor import from_provider
from pydantic import BaseModel
from pydantic_evals import Case, Dataset
from pydantic_evals.evaluators import (
    Evaluator,
    EvaluatorContext,
    IsInstance,
)

ROOT = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(ROOT))
from src.api.common.dependency_container import DependencyContainer
from src.api.common.services.structs import ModelInfo
from src.api.workflows.indexes import GetIndexRequest
from src.api.workflows.topics import GenerateAllDidacticInstructionsRequest

load_dotenv()
logfire.configure(service_name="evals")


os.environ["OPENAI_API_KEY"] = os.getenv("OpenaiApiKey")
os.environ["ANTHROPIC_API_KEY"] = os.getenv("AnthropicApiKey")
os.environ["DbHost"] = "localhost"
os.environ["DbPort"] = "5436"

DependencyContainer.initialize(observability=False)

MODELS_TO_EVALUATE: list[Tuple[str, str | None, int]] = [
    ("model-router/gemini-2.5-pro", "high", 30_000),
    ("openai/gpt-5", "high", 30000),
    ("openai/gpt-5", "minimal", 30000),
    ("openai/o3", "high", 30_000),
    ("anthropic/claude-opus-4-1-20250805", "high", 30_000),
    ("anthropic/claude-opus-4-1-20250805", "low", 30_000),
    ("openai/gpt-5-mini", "high", 30000),
]

PREF_PROMPT_TEMPLATE = """
Eres un experto en diseño instruccional y pedagogía. Tu tarea es evaluar una lista de instrucciones didácticas para la creación de contenido educativo y seleccionar la mejor opción.

Contexto del Material Educativo

Asignatura: {subject_name}
Nombre tema: {topic_name}
Número del tema: {topic_position}
Subject_index: {subject_index}

Este índice de la asignatura te dará contexto de todos los otros temas y epígrafes de la asignatura y podrás usarlos para verificar cuán bien estructurado y coherente es lo que se propone con respecto a todo el índice.

Criterios de Evaluación

1. Estructura Pedagógica
- ¿La instrucción guía hacia una introducción breve y efectiva?
- ¿Promueve una elaboración narrativa y cohesiva de los conceptos?
- ¿Evita la segmentación excesiva y fomenta la conexión entre conceptos?
- ¿Facilita una progresión lógica del aprendizaje?

2. Coherencia y Contextualización
- ¿Considera el contexto de epígrafes anteriores y posteriores?
- ¿Evita solapamientos innecesarios?
- ¿Mantiene conexión con el objetivo general del tema?
- ¿Se integra bien con la estructura del índice?

3. Calidad Didáctica
- ¿Las instrucciones son claras y específicas?
- ¿Guían hacia un contenido de longitud apropiada (2-5 páginas)?
- ¿Sugieren un balance adecuado entre teoría y práctica?
- ¿Indican cuándo y cómo incluir ejemplos/ejercicios (1-3 por tema)?

4. Elementos de Apoyo (15 %)
- ¿Sugiere el uso moderado y apropiado de elementos visuales?
- ¿Indica cuándo incluir tablas, gráficas o diagramas?
- ¿Evita el abuso de recursos adicionales?

Tarea

Analiza cada una de las opciones de instrucciones proporcionadas y selecciona la **MEJOR**. Devuelve un JSON con los campos:

reason      → explicación detallada
preference  → número de la opción elegida
extra_notes → sugerencias de mejora

{instructions_list}
"""


class InstructionInput(BaseModel):
    topic_name: str
    topic_position: int
    model: str
    instructions_text: str


class QualityOutput(BaseModel):
    content: str


class ModelPreference(BaseModel):
    eval_scratchpad: str
    reason: str
    preference: int
    extra_notes: str


class TopicInput(BaseModel):
    topic_name: str
    topic_position: int


class PreferredModelEvaluator(Evaluator):
    """
    Añade 'preferred_model' por caso y, en resultados globales,
    expone 'winner_counts' con el número de victorias por modelo.
    """

    def __init__(self):
        super().__init__()
        self._tally: Dict[str, int] = defaultdict(int)

    async def evaluate(
        self,
        ctx: EvaluatorContext[TopicInput, ModelPreference, Dict],
    ) -> Dict[str, str]:
        models_order: list[str] = ctx.metadata["models_in_order"]
        pref_id: int = ctx.output.preference
        winner = models_order[pref_id - 1] if 0 < pref_id <= len(models_order) else "??"
        self._tally[winner] += 1
        return {"preferred_model": winner}

    def results(self):
        return {"winner_counts": dict(self._tally)}


def split_model(path: str) -> Tuple[str, str]:
    return tuple(path.split("/", 1))


async def get_index_data(index_id: int):
    resp = await DependencyContainer.get_index_workflow().execute(
        GetIndexRequest(id=index_id)
    )
    order_id = getattr(resp.index, "order_id", None) or getattr(resp, "order_id", None)
    temas = [
        n.nombre
        for bloque in resp.index.estructura.bloques_tematicos
        for n in bloque.temas
    ]
    return order_id, resp.index.estructura.model_dump(), resp.index.nombre, temas


async def generate_instructions(order_id: int, temas: list[str]):
    mapping: Dict[str, Dict[str, str]] = {}
    for model_path, effort, max_tok in MODELS_TO_EVALUATE:
        prov, name = split_model(model_path)
        mi = ModelInfo(
            provider=prov, name=name, reasoning_effort=effort, max_tokens=max_tok
        )
        req = GenerateAllDidacticInstructionsRequest(order_id=order_id, model_info=mi)
        with logfire.span("generate_instructions", model=name, provider=prov):
            resp = await DependencyContainer.get_generate_all_didactic_instructions_workflow().execute(
                req
            )
        model_key = f"{model_path}+{effort}" if effort else model_path

        t_idx = 0
        for inst in resp.didactic_instructions:
            if inst.position == 1:
                topic = temas[t_idx]
                t_idx += 1
                mapping.setdefault(topic, {})
            mapping[topic].setdefault(model_key, "")
            mapping[topic][model_key] += (
                f"\n\nEpígrafe: {inst.name}\n{inst.didactic_instructions}"
            )
    return mapping


def options_block(model_dict: dict[str, str]) -> str:
    return "\n".join(
        f"## Opción {i}\n{text}" for i, (_, text) in enumerate(model_dict.items(), 1)
    )


def build_selection_dataset(mapping):
    cases = [
        Case(
            name=f"topic-{i + 1}-{topic}",
            inputs=TopicInput(topic_name=topic, topic_position=i + 1),
            expected_output=None,
            metadata={"models_in_order": list(model_dict.keys())},
        )
        for i, (topic, model_dict) in enumerate(mapping.items())
    ]
    return Dataset(
        cases=cases,
        evaluators=[
            IsInstance(type_name="ModelPreference"),
            PreferredModelEvaluator(),
        ],
    )


async def choose_best(
    topic: TopicInput, *, mapping, judge_model, subject_name, subject_index
):
    client = from_provider(judge_model)
    prompt = PREF_PROMPT_TEMPLATE.format(
        subject_name=subject_name,
        topic_name=topic.topic_name,
        topic_position=topic.topic_position,
        subject_index=json.dumps(subject_index, ensure_ascii=False),
        instructions_list=options_block(mapping[topic.topic_name]),
    )
    return client.chat.completions.create(
        response_model=ModelPreference,
        messages=[{"role": "user", "content": prompt}],
    )


async def eval_seleccion_modelo(index_id: int, judge_model: str):
    order_id, subj_index, subj_name, temas = await get_index_data(index_id)
    mapping = await generate_instructions(order_id, temas)
    dataset = build_selection_dataset(mapping)

    async def task(inp: TopicInput) -> ModelPreference:
        return await choose_best(
            inp,
            mapping=mapping,
            judge_model=judge_model,
            subject_name=subj_name,
            subject_index=subj_index,
        )

    rep = await dataset.evaluate(task)
    print("\n=== Selección de modelo ===")
    rep.print(include_input=False, include_durations=False)


if __name__ == "__main__":
    ap = argparse.ArgumentParser(description="Evalúa instrucciones con Pydantic-Evals.")
    ap.add_argument("--index_id", type=int, required=True)
    ap.add_argument("--judge_model", default="openai/gpt-5")
    args = ap.parse_args()
    asyncio.run(eval_seleccion_modelo(args.index_id, args.judge_model))
