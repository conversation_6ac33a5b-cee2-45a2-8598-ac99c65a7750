"""
Evaluación de índices generados por diferentes modelos LLM usando pydantic-evals.
CLI: --order_ids (required), --judge_model, --output_file
"""

from __future__ import annotations

import argparse
import asyncio
import json
import os
import sys
from collections import defaultdict
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple

import logfire
from dotenv import load_dotenv
from instructor import from_provider
from pydantic import BaseModel, Field
from pydantic_evals import Case, Dataset
from pydantic_evals.evaluators import Evaluator, EvaluatorContext, IsInstance
from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import select

# Setup
ROOT = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(ROOT))
from src.api.common.dependency_container import DependencyContainer
from src.api.common.services.structs import ModelInfo
from src.api.workflows.indexes import GenerateIndexRequest
from src.domain.models import Indice

load_dotenv()
logfire.configure(service_name="evals")
os.environ.update(
    {
        "OPENAI_API_KEY": os.getenv("OpenaiApiKey"),
        "ANTHROPIC_API_KEY": os.getenv("AnthropicApiKey"),
        "DbHost": "localhost",
        "DbPort": "5436",
    }
)
DependencyContainer.initialize(observability=False, create_prompts=False)

MODELS_TO_EVALUATE: list[Tuple[str, str | None, int]] = [
    ("openai/gpt-5", "high", 8000),
    ("openai/gpt-5", "minimal", 8000),
    ("anthropic/claude-opus-4-1-20250805", "high", 8000),
    ("openai/gpt-5-mini", "high", 8000),
    ("openai/o3", "high", 8000),
    ("anthropic/claude-sonnet-4-20250514", "high", 8000),
]

PROMPT_TEMPLATE_EVAL = """
Eres un experto en diseño curricular y pedagogía. Tu tarea es evaluar una lista de índices educativos para una asignatura y seleccionar la mejor opción.

Contexto del Material Educativo:
Asignatura: {subject_name}
Descripción de la asignatura: {subject_description}
Competencias a desarrollar: {competencies}

Criterios de Evaluación:

1. Estructura y Organización (30%)
- ¿La estructura de bloques temáticos es lógica y coherente?
- ¿Los temas siguen una progresión pedagógica adecuada?
- ¿Los epígrafes están bien organizados dentro de cada tema?
- ¿La distribución de contenidos es equilibrada?

2. Cobertura de Competencias (25%)
- ¿El índice cubre todas las competencias definidas?
- ¿Los temas están alineados con las competencias a desarrollar?
- ¿Existe una distribución adecuada de competencias a lo largo del índice?

3. Coherencia Pedagógica (25%)
- ¿Los nombres de temas y epígrafes son claros y descriptivos?
- ¿Existe una progresión lógica del conocimiento?
- ¿Se evitan solapamientos innecesarios entre temas?
- ¿El nivel de detalle es apropiado para la asignatura?

4. Completitud y Profundidad (20%)
- ¿El índice cubre todos los aspectos esenciales de la asignatura?
- ¿El nivel de profundidad es adecuado?
- ¿Se incluyen temas actuales y relevantes?

Tarea:
Analiza cada una de las opciones de índices proporcionadas y selecciona la **MEJOR**. 
Evalúa en detalle cada opción y explica por qué la opción elegida es superior.

{indices_list}
"""


class IndexInput(BaseModel):
    """Input para la evaluación de un índice"""

    order_id: int
    subject_name: str
    subject_description: str = ""
    competencies: List[str] = Field(default_factory=list)


class ModelPreference(BaseModel):
    """Resultado de la evaluación del modelo"""

    eval_scratchpad: str = Field(description="Evaluación detallada de cada opción")
    preference: int = Field(description="ID de la mejor opción (1-indexed)")
    reason: str = Field(description="Razón por la cual la opción elegida es la mejor")
    extra_notes: str = Field(
        description="Notas adicionales sobre cómo mejorar el proceso de generación de índices"
    )


class IndexEvaluator(Evaluator):
    """
    Evaluador personalizado que rastrea qué modelo gana más veces y sus rankings completos
    """

    def __init__(self):
        super().__init__()
        self._tally: Dict[str, int] = defaultdict(int)
        self._detailed_results: List[Dict] = []
        self._all_rankings: List[Dict[str, int]] = []
        self._ranking_positions: Dict[str, List[int]] = defaultdict(list)

    async def evaluate(
        self,
        ctx: EvaluatorContext[IndexInput, ModelPreference, Dict],
    ) -> Dict[str, str]:
        models_order: list[str] = ctx.metadata["models_in_order"]
        pref_id: int = ctx.output.preference
        winner = models_order[pref_id - 1] if 0 < pref_id <= len(models_order) else "??"
        self._tally[winner] += 1

        ranking = self._extract_ranking(models_order, pref_id)
        self._all_rankings.append(ranking)

        for model, position in ranking.items():
            self._ranking_positions[model].append(position)

        self._detailed_results.append(
            {
                "order_id": ctx.inputs.order_id,
                "subject_name": ctx.inputs.subject_name,
                "winner": winner,
                "full_ranking": ranking,
                "models_evaluated": models_order,
                "reason": ctx.output.reason,
                "extra_notes": ctx.output.extra_notes,
                "eval_scratchpad": ctx.output.eval_scratchpad,
            }
        )

        return {"preferred_model": winner}

    def _extract_ranking(
        self, models_order: list[str], winner_idx: int
    ) -> Dict[str, int]:
        """
        Extract ranking from evaluation text.
        For now, we know the winner (rank 1) and others are tied for rank 2.
        This could be enhanced to parse eval_scratchpad for more detailed rankings.
        """
        ranking = {}

        if 0 < winner_idx <= len(models_order):
            winner_model = models_order[winner_idx - 1]
            ranking[winner_model] = 1

            for model in models_order:
                if model != winner_model:
                    ranking[model] = 2
        else:
            for model in models_order:
                ranking[model] = 1

        return ranking

    def results(self):
        avg_rankings = {}
        for model, positions in self._ranking_positions.items():
            if positions:
                avg_rankings[model] = sum(positions) / len(positions)

        position_counts = defaultdict(lambda: defaultdict(int))
        for ranking in self._all_rankings:
            for model, rank in ranking.items():
                position_counts[model][rank] += 1

        return {
            "winner_counts": dict(self._tally),
            "detailed_results": self._detailed_results,
            "total_evaluations": sum(self._tally.values()),
            "average_rankings": avg_rankings,
            "position_distribution": dict(position_counts),
            "all_rankings": self._all_rankings,
        }


async def get_order_details(order_id: int):
    """Obtiene los detalles de una orden incluyendo asignatura y competencias"""
    try:
        index_repo = DependencyContainer.get_index_repository()
        order, title_subject, subject = await index_repo.get_order_and_subject_details(
            order_id
        )

        competencies = []
        try:
            async with AsyncSession(
                DependencyContainer.get_async_database_engine()
            ) as session:
                statement = (
                    select(Indice)
                    .where(Indice.order_id == order_id)
                    .order_by(Indice.version.desc())
                    .limit(1)
                )
                result = await session.execute(statement)
                latest_index = result.scalars().first()

                if latest_index and latest_index.id:
                    comp_obj = await index_repo.get_competencies(latest_index.id)
                    if comp_obj and hasattr(comp_obj, "competencias"):
                        competencies = [c.nombre for c in comp_obj.competencias]
        except Exception as e:
            print(f"No se pudieron obtener competencias: {e}")

        return {
            "order_id": order_id,
            "subject_name": title_subject.name if title_subject else "",
            "subject_description": subject.description
            if subject and subject.description
            else "",
            "competencies": competencies,
        }
    except Exception as e:
        print(f"Error obteniendo detalles de la orden {order_id}: {e}")
        return None


async def generate_index_for_models(order_id: int) -> Dict[str, Dict]:
    """Genera índices usando diferentes modelos para una orden específica (en paralelo)"""

    async def generate_single_index(
        model_path: str, effort: str | None, max_tok: int
    ) -> Tuple[str, Dict | None]:
        """Genera un índice para un modelo específico"""
        prov, name = model_path.split("/", 1)
        model_key = f"{model_path}+{effort}" if effort else model_path

        try:
            workflow = DependencyContainer.get_generate_index_workflow()

            mi = ModelInfo(
                provider=prov, name=name, reasoning_effort=effort, max_tokens=max_tok
            )

            req = GenerateIndexRequest(
                order_id=order_id,
                model_info=mi,
                store=False,
            )

            with logfire.span(
                "generate_index", model=name, provider=prov, effort=effort
            ):
                resp = await workflow.execute(req)

            filled_prompt = None
            if resp.processes_info and len(resp.processes_info) > 0:
                process_info = resp.processes_info[0]
                if hasattr(process_info, "filled_prompt"):
                    filled_prompt = process_info.filled_prompt

            result = {
                "index": resp.index.model_dump(),
                "competencies": [c.model_dump() for c in resp.competencies]
                if resp.competencies
                else [],
                "filled_prompt": filled_prompt,
            }

            print(f"✓ Generado índice con {model_key}")
            return model_key, result

        except Exception as e:
            print(f"✗ Error generando índice con {model_key}: {e}")
            return model_key, None

    tasks = [
        generate_single_index(model_path, effort, max_tok)
        for model_path, effort, max_tok in MODELS_TO_EVALUATE
    ]

    results = await asyncio.gather(*tasks)
    return dict(results)


def format_indices_for_prompt(indices_dict: Dict[str, Dict]) -> str:
    """Formatea todos los índices para el prompt de evaluación"""
    options = []
    for i, (_, index_data) in enumerate(indices_dict.items(), 1):
        options.append(f"## Opción {i}")

        if not index_data:
            options.append("Error: No se pudo generar el índice")
            continue

        index = index_data["index"]
        options.append(f"Asignatura: {index.get('nombre', 'Sin nombre')}\n")

        for j, bloque in enumerate(
            index.get("estructura", {}).get("bloques_tematicos", []), 1
        ):
            options.append(f"BLOQUE {j}: {bloque.get('nombre', 'Sin nombre')}")
            for k, tema in enumerate(bloque.get("temas", []), 1):
                options.append(
                    f"  Tema {tema.get('position', k)}: {tema.get('nombre', 'Sin nombre')}"
                )
                for idx, epigrafe in enumerate(tema.get("epigrafes", []), 1):
                    options.append(f"    {idx}. {epigrafe}")
            options.append("")

    return "\n".join(options)


async def choose_best_index(
    input_data: IndexInput, indices_dict: Dict[str, Dict], judge_model: str
) -> ModelPreference:
    """Usa el modelo juez para elegir el mejor índice"""
    client = from_provider(judge_model)

    competencies_str = (
        "\n".join(f"- {c}" for c in input_data.competencies)
        if input_data.competencies
        else "No especificadas"
    )

    prompt = PROMPT_TEMPLATE_EVAL.format(
        subject_name=input_data.subject_name,
        subject_description=input_data.subject_description or "No especificada",
        competencies=competencies_str,
        indices_list=format_indices_for_prompt(indices_dict),
    )

    return client.chat.completions.create(
        response_model=ModelPreference,
        messages=[{"role": "user", "content": prompt}],
    )


def build_evaluation_dataset(evaluations: List[Dict]) -> Dataset:
    """Construye el dataset para la evaluación"""
    cases = []

    for eval_data in evaluations:
        case = Case(
            name=f"order-{eval_data['order_id']}-{eval_data['subject_name'][:20]}",
            inputs=IndexInput(
                order_id=eval_data["order_id"],
                subject_name=eval_data["subject_name"],
                subject_description=eval_data.get("subject_description", ""),
                competencies=eval_data.get("competencies", []),
            ),
            expected_output=None,
            metadata={
                "models_in_order": eval_data["models_in_order"],
                "indices": eval_data["indices"],
            },
        )
        cases.append(case)

    return Dataset(
        cases=cases,
        evaluators=[
            IsInstance(type_name="ModelPreference"),
            IndexEvaluator(),
        ],
    )


async def process_single_order(order_id: int) -> Dict | None:
    """Procesa una orden individual de forma asíncrona"""
    print(f"\n--- Procesando orden {order_id} ---")

    order_details = await get_order_details(order_id)
    if not order_details:
        print(f"✗ No se pudieron obtener detalles de la orden {order_id}")
        return None

    print(f"Asignatura: {order_details['subject_name']}")
    print(f"Competencias: {len(order_details['competencies'])}")

    print("\nGenerando índices...")
    indices = await generate_index_for_models(order_id)

    valid_indices = {k: v for k, v in indices.items() if v is not None}

    if len(valid_indices) < 2:
        print(
            f"✗ No hay suficientes índices válidos para comparar (mínimo 2, obtenidos {len(valid_indices)})"
        )
        return None

    return {
        **order_details,
        "indices": valid_indices,
        "models_in_order": list(valid_indices.keys()),
    }


async def eval_indices(order_ids: List[int], judge_model: str, output_file: str):
    """Función principal de evaluación con procesamiento paralelo"""
    print(f"\n{'=' * 60}")
    print("Evaluación de Índices con Pydantic-Evals")
    print(f"{'=' * 60}")
    print(f"Órdenes a evaluar: {order_ids}")
    print(f"Modelo juez: {judge_model}")
    print(f"Modelos a evaluar: {len(MODELS_TO_EVALUATE)}")
    print(f"{'=' * 60}\n")

    print("Procesando todas las órdenes en paralelo...")
    tasks = [process_single_order(order_id) for order_id in order_ids]
    results = await asyncio.gather(*tasks, return_exceptions=True)

    all_evaluations = []
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            print(f"✗ Error procesando orden {order_ids[i]}: {result}")
        elif result is not None:
            all_evaluations.append(result)

    if not all_evaluations:
        print("\n✗ No hay evaluaciones para procesar")
        return

    print(f"\n--- Evaluando {len(all_evaluations)} órdenes ---")
    dataset = build_evaluation_dataset(all_evaluations)

    eval_data_lookup = {data["order_id"]: data for data in all_evaluations}

    async def evaluate_task(inp: IndexInput) -> ModelPreference:
        eval_data = eval_data_lookup.get(inp.order_id)
        if not eval_data:
            raise ValueError(f"No se encontraron datos para orden {inp.order_id}")
        return await choose_best_index(inp, eval_data["indices"], judge_model)

    report = await dataset.evaluate(evaluate_task)

    print("\n" + "=" * 60)
    print("RESULTADOS DE LA EVALUACIÓN")
    print("=" * 60)
    report.print(include_input=False, include_durations=False)

    results_data = {
        "evaluation_config": {
            "order_ids": order_ids,
            "judge_model": judge_model,
            "models_evaluated": MODELS_TO_EVALUATE,
        },
        "evaluations": all_evaluations,
    }

    def make_json_safe(obj):
        """Convert objects to JSON-safe representations"""
        if hasattr(obj, "model_dump"):
            return obj.model_dump()
        elif hasattr(obj, "__dict__"):
            return {k: make_json_safe(v) for k, v in obj.__dict__.items()}
        elif isinstance(obj, list):
            return [make_json_safe(item) for item in obj]
        elif isinstance(obj, dict):
            return {k: make_json_safe(v) for k, v in obj.items()}
        else:
            return obj

    safe_results_data = make_json_safe(results_data)

    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(safe_results_data, f, ensure_ascii=False, indent=2)

    print(f"\n✓ Resultados guardados en: {output_file}")

    if hasattr(report, "evaluators"):
        for evaluator in report.evaluators:
            if isinstance(evaluator, IndexEvaluator):
                results = evaluator.results()
                print("\n" + "=" * 60)
                print("RESUMEN AGREGADO")
                print("=" * 60)
                print(f"Total de evaluaciones: {results['total_evaluations']}")
                print("\nVictorias por modelo:")
                for model, count in sorted(
                    results["winner_counts"].items(), key=lambda x: x[1], reverse=True
                ):
                    percentage = (count / results["total_evaluations"]) * 100
                    print(f"  {model}: {count} victorias ({percentage:.1f}%)")

                if "average_rankings" in results and results["average_rankings"]:
                    print("\nRanking promedio (menor es mejor):")
                    for model, avg_rank in sorted(
                        results["average_rankings"].items(), key=lambda x: x[1]
                    ):
                        print(f"  {model}: {avg_rank:.2f}")

                if (
                    "position_distribution" in results
                    and results["position_distribution"]
                ):
                    print("\nDistribución de posiciones:")
                    for model, positions in sorted(
                        results["position_distribution"].items()
                    ):
                        pos_str = ", ".join(
                            [f"#{k}:{v}" for k, v in sorted(positions.items())]
                        )
                        print(f"  {model}: {pos_str}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Evalúa índices generados por diferentes modelos"
    )
    parser.add_argument(
        "--order_ids",
        type=str,
        required=True,
        help="IDs de órdenes a evaluar (separados por comas)",
    )
    parser.add_argument(
        "--judge_model",
        type=str,
        default="openai/gpt-5",
        help="Modelo juez para la evaluación (default: openai/gpt-5)",
    )
    parser.add_argument(
        "--output_file",
        type=str,
        default=f"eval_index_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
        help="Archivo de salida para los resultados (default: eval_index_results_TIMESTAMP.json)",
    )

    args = parser.parse_args()

    order_ids = [int(id.strip()) for id in args.order_ids.split(",")]

    asyncio.run(eval_indices(order_ids, args.judge_model, args.output_file))
