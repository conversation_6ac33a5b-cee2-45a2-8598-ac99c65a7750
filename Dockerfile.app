FROM ghcr.io/astral-sh/uv:0.5.25-python3.12-bookworm-slim AS builder

ENV UV_KEYRING_PROVIDER=disabled
ENV UV_INDEX_UNIR_USERNAME=VssSessionToken
ENV UV_COMPILE_BYTECODE=1
ENV UV_NO_CACHE=1

ARG UV_INDEX_UNIR_PASSWORD
ENV UV_INDEX_UNIR_PASSWORD=${UV_INDEX_UNIR_PASSWORD}

RUN groupadd -r appgrp && useradd -r -g appgrp -u 10001 appuser

WORKDIR /app

RUN apt-get update && apt-get --yes install gcc

COPY pyproject.toml uv.lock ./
RUN --mount=type=cache,target=/root/.cache/uv \
    uv sync --no-dev --frozen --no-install-project

COPY .env .env.Development .env.Preproduction ./
COPY src src

RUN --mount=type=cache,target=/root/.cache/uv \
    uv sync --group api --no-dev --frozen

# ---- With Cert Stage ----
FROM python:3.12-slim-bookworm AS with-cert

RUN groupadd -r appgrp && useradd -r -g appgrp -u 10001 appuser

ENV COMMON__ENVIRONMENT=""

WORKDIR /app
COPY --from=builder /app .

COPY ca-bundle.crt /usr/local/share/ca-certificates/custom-ca.crt
RUN update-ca-certificates

COPY ca-bundle.crt /tmp/
RUN cat /tmp/ca-bundle.crt >> /app/.venv/lib/python3.12/site-packages/certifi/cacert.pem && rm /tmp/ca-bundle.crt
RUN mkdir -p tmp && chown appuser:appgrp tmp

EXPOSE 8000
USER appuser

CMD [".venv/bin/poe", "start-api"]

# ---- Without Cert Stage ----
FROM python:3.12-slim-bookworm AS without-cert

RUN groupadd -r appgrp && useradd -r -g appgrp -u 10001 appuser

ENV COMMON__ENVIRONMENT=""

WORKDIR /app
COPY --from=builder /app .
RUN mkdir -p tmp && chown appuser:appgrp tmp

EXPOSE 8000

USER appuser

CMD [".venv/bin/poe", "start-api"]