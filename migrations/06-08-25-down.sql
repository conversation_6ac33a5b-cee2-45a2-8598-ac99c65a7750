BEGIN;

-- 1) Recolocar posibles filas con el valor nuevo para poder reconstruir el enum
UPDATE public.aiprocess
   SET process_type = 'SEARCH_SOURCES'::aiprocesstype
 WHERE process_type::text = 'GENERATE_TOPIC_QUERIES';

-- 2) Reconstruir aiprocesstype SIN 'GENERATE_TOPIC_QUERIES'
DO $$
DECLARE
    labels text;
BEGIN
    -- Borrar tipo temporal si existe de intentos anteriores
    IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'aiprocesstype_tmp') THEN
        DROP TYPE aiprocesstype_tmp;
    END IF;

    -- Construir lista de labels sin el valor añadido
    SELECT string_agg(quote_literal(e.enumlabel), ',' ORDER BY e.enumsortorder)
      INTO labels
      FROM pg_enum e
      JOIN pg_type t ON t.oid = e.enumtypid
     WHERE t.typname = 'aiprocesstype'
       AND e.enumlabel <> 'GENERATE_TOPIC_QUERIES';

    IF labels IS NULL THEN
        RAISE EXCEPTION 'No se encontraron labels para aiprocesstype';
    END IF;

    -- Crear tipo temporal
    EXECUTE 'CREATE TYPE aiprocesstype_tmp AS ENUM (' || labels || ')';
END
$$;

-- Cambiar la(s) columna(s) que usan aiprocesstype al tipo temporal
ALTER TABLE public.aiprocess
  ALTER COLUMN process_type TYPE aiprocesstype_tmp
  USING process_type::text::aiprocesstype_tmp;


DROP TYPE aiprocesstype;
ALTER TYPE aiprocesstype_tmp RENAME TO aiprocesstype;

-- 3) Eliminar columna memories de public.tema
ALTER TABLE public.tema
  DROP COLUMN IF EXISTS memories;

COMMIT;
