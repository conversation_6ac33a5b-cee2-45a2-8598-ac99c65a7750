
--
BEGIN;

-- 1. <PERSON><PERSON><PERSON> columna memories
ALTER TABLE tema
ADD COLUMN IF NOT EXISTS memories jsonb NOT NULL DEFAULT '[]'::jsonb;

CREATE INDEX IF NOT EXISTS idx_tema_memories_gin
    ON tema USING gin (memories);

-- 2. A<PERSON>dir field GENERATE_TOPIC_QUERIES
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM pg_enum e
        JOIN pg_type t ON t.oid = e.enumtypid
        WHERE t.typname = 'aiprocesstype'
          AND e.enumlabel = 'GENERATE_TOPIC_QUERIES'
    ) THEN
        ALTER TYPE aiprocesstype ADD VALUE 'GENERATE_TOPIC_QUERIES';
    END IF;
END
$$;

COMMIT;