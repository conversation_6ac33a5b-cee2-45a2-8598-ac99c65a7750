build_and_push_image() {
    local environment=$1
    local app_name=$2
    local acr_name=$3
    local resource_group=$4
    local webapp_name=$5
    local version=$(python -c "import src.version; print(src.version.__version__)")
    local compose_file="docker-compose.yml"
    # Build and start the Docker containers
    #In the case of dev the dockerfile inside docker-compose has to be changed to Dockerfile.app due to memory and workers. As small computer can´t handle that much.
    docker-compose -f $compose_file --project-name $app_name-${environment} build app
    # Tag the Docker image
    docker tag ${app_name}-${environment}-app $acr_name/${app_name}-${environment}-app:${version}

    # Log in to Azure Container Registry
    az acr login -n $acr_name

    # Push the Docker image to Azure Container Registry
    docker push $acr_name/${app_name}-${environment}-app:${version}

    az webapp config container set --name $webapp_name --resource-group $resource_group --container-image-name ${acr_name}/${app_name}-${environment}-app:${version}
}

# Check if env provided
if [ $# -eq 0 ]; then
  echo "Please provide an environment argument: desarrollo, preprod, or prod"
  exit 1
fi

# Get the environment from the command line argument
environment=$1


case $environment in
  dev)
    app_name="contenidos"
    acr_name="acrwedevcontenidos001.azurecr.io"
    resource_group="rg-contenidos-dev-sc-001"
    webapp_name="webapp-contenidos-dev-sc-001"
    ;;
  preprod)
    app_name="contenidos"
    acr_name="acrweprecontenidos001.azurecr.io"
    resource_group="rg-contenidos-pre-we-001"
    webapp_name="webapp-contenidos-dev-we-001"
    ;;
  prod)
    app_name="contenidos"
    acr_name="acrweprocontenidos001.azurecr.io"
    resource_group="rg-contenidos-pro-we-001"
    webapp_name="webapp-contenidos-pro-we-001"
    ;;
  *)
    echo "Invalid environment argument. Please provide: dev, preprod, or prod"
    exit 1
    ;;
esac

# Log in to Azure
echo "Please log in to Azure using the 'az login' command in another terminal window."
echo "Press Enter to continue once you have logged in."
read -p "Waiting for Azure login..."

build_and_push_image "$environment" "$app_name" "$acr_name" "$resource_group" "$webapp_name"