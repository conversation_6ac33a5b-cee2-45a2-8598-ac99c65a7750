# test_content_generation_task.py

from unittest.mock import MagicMock, patch

import pytest
from src.background_tasks.content_generation.content_generation_task import (
    ContentGenerationTask,
)
from src.domain.models import AIProcessStatus


@pytest.fixture
def content_generation_task():
    """
    Fixture to create an instance of ContentGenerationTask with mocked dependencies.
    """
    # Create mocked dependencies
    mock_logger = MagicMock()
    mock_content_generator = MagicMock()
    mock_ai_tracer = MagicMock()
    mock_service_bus_interface = MagicMock()
    mock_db_engine = MagicMock()
    mock_config = MagicMock()
    mock_index_repository = MagicMock()
    mock_mail_sender = MagicMock()

    # Instantiate the task with mocked dependencies
    task = ContentGenerationTask(
        logger=mock_logger,
        content_generator=mock_content_generator,
        ai_tracer=mock_ai_tracer,
        queue_interface=mock_service_bus_interface,
        db_engine=mock_db_engine,
        config=mock_config,
        index_repository=mock_index_repository,
        mail_sender=mock_mail_sender,
    )
    # Attach mocks to the task for access in tests
    task.mock_logger = mock_logger
    task.mock_ai_tracer = mock_ai_tracer
    task.mock_index_repository = mock_index_repository

    return task


@patch("src.background_tasks.content_generation.content_generation_task.Session")
def test_ai_process_not_found(mock_session, content_generation_task):
    """
    Test that an exception is raised when the AI process is not found.
    """
    # Configure the session to return None
    mock_session_instance = mock_session.return_value.__enter__.return_value
    mock_session_instance.exec.return_value.first.return_value = None

    # Assert that the correct exception is raised
    with pytest.raises(Exception) as exc_info:
        content_generation_task._complete_ai_process(ai_process_id=1)

    assert str(exc_info.value) == "AI process not found"


@patch("src.background_tasks.content_generation.content_generation_task.Session")
def test_ai_process_already_completed(mock_session, content_generation_task):
    """
    Test that an exception is raised when the AI process is already completed.
    """
    # Create a mock AIProcess with status COMPLETED
    ai_process_mock = MagicMock()
    ai_process_mock.id = 1
    ai_process_mock.status = AIProcessStatus.COMPLETED.value  # Use the string value
    ai_process_mock.children = []

    # Configure the session to return the completed AI process
    mock_session_instance = mock_session.return_value.__enter__.return_value
    mock_session_instance.exec.return_value.first.return_value = ai_process_mock

    # Assert that the correct exception is raised
    with pytest.raises(Exception) as exc_info:
        content_generation_task._complete_ai_process(ai_process_id=1)

    assert str(exc_info.value) == "This AI process is already completed"


@patch("src.background_tasks.content_generation.content_generation_task.Session")
def test_all_children_completed(mock_session, content_generation_task):
    """
    Test that the AI process is marked as completed when all child processes are completed.
    """
    # Create child processes that are all completed
    child1 = MagicMock()
    child1.id = 2
    child1.tema_id = 101
    child1.status = AIProcessStatus.COMPLETED.value

    child2 = MagicMock()
    child2.id = 3
    child2.tema_id = 102
    child2.status = AIProcessStatus.COMPLETED.value

    ai_process_mock = MagicMock()
    ai_process_mock.id = 1
    ai_process_mock.status = AIProcessStatus.IN_PROGRESS.value
    ai_process_mock.children = [child1, child2]

    # Configure the session to return the AI process with completed children
    mock_session_instance = mock_session.return_value.__enter__.return_value
    mock_session_instance.exec.return_value.first.return_value = ai_process_mock

    # Call the method under test
    result = content_generation_task._complete_ai_process(ai_process_id=1)

    # Verify that complete_process was called with the correct arguments
    content_generation_task.mock_ai_tracer.complete_process.assert_called_once_with(
        ai_process_mock, AIProcessStatus.COMPLETED
    )

    # Verify the return value
    assert result is True


@patch("src.background_tasks.content_generation.content_generation_task.Session")
def test_tema_retry_completed(mock_session, content_generation_task):
    """
    Test that the AI process is marked as completed when failed themes are retried and completed.
    """
    # Simulate themes that initially failed but were retried and now completed
    # Theme 101: One failed, one completed
    child1 = MagicMock()
    child1.id = 2
    child1.tema_id = 101
    child1.status = AIProcessStatus.FAILED.value

    child2 = MagicMock()
    child2.id = 4
    child2.tema_id = 101
    child2.status = AIProcessStatus.COMPLETED.value

    # Theme 102: One failed, one completed
    child3 = MagicMock()
    child3.id = 3
    child3.tema_id = 102
    child3.status = AIProcessStatus.FAILED.value

    child4 = MagicMock()
    child4.id = 5
    child4.tema_id = 102
    child4.status = AIProcessStatus.COMPLETED.value

    ai_process_mock = MagicMock()
    ai_process_mock.id = 1
    ai_process_mock.status = AIProcessStatus.IN_PROGRESS.value
    ai_process_mock.children = [child1, child2, child3, child4]

    # Configure the session to return the AI process with mixed child statuses
    mock_session_instance = mock_session.return_value.__enter__.return_value
    mock_session_instance.exec.return_value.first.return_value = ai_process_mock

    # Call the method under test
    result = content_generation_task._complete_ai_process(ai_process_id=1)

    # Verify that complete_process was called with the correct arguments
    content_generation_task.mock_ai_tracer.complete_process.assert_called_once_with(
        ai_process_mock, AIProcessStatus.COMPLETED
    )

    # Verify that no warnings were logged
    content_generation_task.mock_logger.warning.assert_not_called()

    # Verify the return value
    assert result is True
