from unittest.mock import AsyncMock, MagicMock

import pytest
from src.background_tasks.process_document_task.process_document_task import (
    DocProcessingTask,
)


class DummyMessage:
    def __init__(self, body, message_id="msg1"):
        self.body = body
        self.message_id = message_id


@pytest.fixture
def dummy_dependencies():
    logger = MagicMock()
    queue_interface = MagicMock()
    db_engine = MagicMock()
    config = MagicMock()
    application_settings = MagicMock()
    url_extractor = MagicMock()
    embedding_model = MagicMock()
    return (
        logger,
        queue_interface,
        db_engine,
        config,
        application_settings,
        url_extractor,
        embedding_model,
    )


@pytest.fixture
def task(dummy_dependencies):
    (
        logger,
        queue_interface,
        db_engine,
        config,
        application_settings,
        url_extractor,
        embedding_model,
    ) = dummy_dependencies
    task = DocProcessingTask(
        logger, queue_interface, db_engine, config, application_settings, url_extractor, embedding_model
    )
    # Parchear métodos internos para aislar pruebas
    task._doc_process_item = AsyncMock()
    task._check_topic_docs_processed = AsyncMock()
    task._send_generate_content_message_for_topic = AsyncMock()
    task._check_index_status_for_document = MagicMock(return_value=True)
    queue_interface.msg_to_dict.return_value = {"file_uuid": "dummy"}
    return task


@pytest.mark.asyncio
async def test_process_single_message_empty_body(task, dummy_dependencies):
    logger = dummy_dependencies[0]
    message = DummyMessage(body=None, message_id="test_empty")
    await task.process_single_message(message)
    task._doc_process_item.assert_not_called()
    logger.info.assert_called_with(f"Empty message found: {message.message_id}")


@pytest.mark.asyncio
async def test_process_single_message_already_processed(task):
    task._doc_process_item.return_value = ("123", True)
    message = DummyMessage(body=b"{}")
    await task.process_single_message(message)
    task._check_topic_docs_processed.assert_not_called()
    task._send_generate_content_message_for_topic.assert_not_called()


@pytest.mark.asyncio
async def test_process_single_message_all_docs_processed(task):
    task._doc_process_item.return_value = ("123", False)
    dummy_index = MagicMock(id=5)
    # Mock multiple topics, some ready and some not
    task._check_topic_docs_processed.return_value = [
        (True, 1, dummy_index),  # Topic 1 ready
        (False, 2, dummy_index),  # Topic 2 not ready
        (True, 3, dummy_index),  # Topic 3 ready
    ]
    message = DummyMessage(body=b"{}")
    await task.process_single_message(message)
    task._check_topic_docs_processed.assert_awaited_with("123")
    # Should be called twice for the ready topics (1 and 3)
    assert task._send_generate_content_message_for_topic.await_count == 2
    task._send_generate_content_message_for_topic.assert_any_await(1, dummy_index.id, 1)
    task._send_generate_content_message_for_topic.assert_any_await(3, dummy_index.id, 1)


@pytest.mark.asyncio
async def test_process_single_message_not_all_processed(task, dummy_dependencies):
    logger = dummy_dependencies[0]
    task._doc_process_item.return_value = ("123", False)
    dummy_index = MagicMock(id=7)
    # All topics not ready
    task._check_topic_docs_processed.return_value = [
        (False, 1, dummy_index),  # Topic 1 not ready
        (False, 2, dummy_index),  # Topic 2 not ready
    ]
    message = DummyMessage(body=b"{}")
    await task.process_single_message(message)
    # Should log for each topic that's not ready
    logger.info.assert_any_call("Topic 1 is not ready for content generation yet")
    logger.info.assert_any_call("Topic 2 is not ready for content generation yet")
    task._send_generate_content_message_for_topic.assert_not_called()


@pytest.mark.asyncio
async def test_on_max_retries_exceeded_all_docs(task, dummy_dependencies):
    _, queue_interface, _, _, _, _, _ = dummy_dependencies
    # Simular mensaje y datos del queue_interface
    data = {"file_uuid": "uuid-test"}
    queue_interface.msg_to_dict.return_value = data
    task.queue_interface = queue_interface
    # Parchear obtención de documento y comprobación de temas
    dummy_doc = MagicMock(id="uuid-test")
    task._get_document_from_data = MagicMock(return_value=dummy_doc)
    dummy_index = MagicMock(id=10)
    # Mock topics ready for content generation
    task._check_topic_docs_processed.return_value = [
        (True, 1, dummy_index),  # Topic 1 ready
        (True, 2, dummy_index),  # Topic 2 ready
    ]
    message = DummyMessage(body=b"test")
    await task.on_max_retries_exceeded(message, Exception("error"))
    # Should be called twice for both ready topics
    assert task._send_generate_content_message_for_topic.await_count == 2
    task._send_generate_content_message_for_topic.assert_any_await(1, dummy_index.id, 1)
    task._send_generate_content_message_for_topic.assert_any_await(2, dummy_index.id, 1)


@pytest.mark.asyncio
async def test_on_max_retries_exceeded_not_all(task, dummy_dependencies):
    logger, queue_interface, _, _, _, _, _ = dummy_dependencies
    data = {"file_uuid": "uuid-test"}
    queue_interface.msg_to_dict.return_value = data
    task.queue_interface = queue_interface
    task._get_document_from_data = MagicMock(return_value=MagicMock(id="uuid-test"))
    dummy_index = MagicMock(id=20)
    # Mock topics not ready for content generation
    task._check_topic_docs_processed.return_value = [
        (False, 1, dummy_index),  # Topic 1 not ready
        (False, 2, dummy_index),  # Topic 2 not ready
    ]
    message = DummyMessage(body=b"test2")
    await task.on_max_retries_exceeded(message, Exception("error"))
    # Should log for each topic that's not ready after max retries
    logger.info.assert_any_call("Topic 1 is not ready for content generation after max retries exceeded")
    logger.info.assert_any_call("Topic 2 is not ready for content generation after max retries exceeded")
    task._send_generate_content_message_for_topic.assert_not_called()


@pytest.mark.asyncio
async def test_process_single_message_index_not_ready(task, dummy_dependencies):
    """Test process_single_message when document's index is not in CONTENT_GENERATION status."""
    logger = dummy_dependencies[0]
    task._check_index_status_for_document.return_value = False
    
    mock_document = MagicMock()
    mock_document.id = "dummy"
    task._get_document_from_data = MagicMock(return_value=mock_document)
    
    message = DummyMessage(body=b"{}")
    
    await task.process_single_message(message)
    
    # Should log that document is being skipped
    logger.info.assert_called_with(
        "Skipping document dummy - index not in CONTENT_GENERATION status"
    )
    # Should not process the document
    task._doc_process_item.assert_not_called()
    task._check_topic_docs_processed.assert_not_called()


@pytest.mark.asyncio
async def test_check_topic_docs_processed_unit_test(task):
    """Unit test for _check_topic_docs_processed method behavior."""
    mock_index = MagicMock(id=10)
    task._check_topic_docs_processed.return_value = [
        (True, 1, mock_index),   # Topic 1 ready
        (False, 2, mock_index),  # Topic 2 not ready
    ]
    
    result = await task._check_topic_docs_processed("test-doc-id")
    
    assert len(result) == 2
    assert result[0] == (True, 1, mock_index)
    assert result[1] == (False, 2, mock_index)
    task._check_topic_docs_processed.assert_awaited_once_with("test-doc-id")


@pytest.mark.asyncio
async def test_check_topic_docs_processed_empty_result(task):
    """Unit test for _check_topic_docs_processed with no topics."""
    task._check_topic_docs_processed.return_value = []
    
    result = await task._check_topic_docs_processed("test-doc-id")
    
    assert len(result) == 0
    task._check_topic_docs_processed.assert_awaited_once_with("test-doc-id")


@pytest.mark.asyncio
async def test_check_topic_docs_processed_exception_handling(task):
    """Unit test for _check_topic_docs_processed exception handling."""
    task._check_topic_docs_processed.side_effect = Exception("Database error")
    
    with pytest.raises(Exception, match="Database error"):
        await task._check_topic_docs_processed("test-doc-id")