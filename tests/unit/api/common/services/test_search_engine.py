from unittest.mock import patch

patch(
    "src.api.common.services.search_engine.tenacity.retry",
    lambda *a, **kw: (lambda f: f),
).start()

import logging
from unittest.mock import MagicMock, patch

import pytest
from src.api.common.services import search_engine


@pytest.fixture
def logger():
    return MagicMock(spec=logging.Logger)


@pytest.fixture
def dummy_reranker():
    class DummyReranker:
        def rerank(self, documents, query, top_n):
            # Devuelve los índices en orden inverso para testear orden
            return [{"index": i} for i in reversed(range(min(top_n, len(documents))))]

    return DummyReranker()


class DummyEngine(search_engine.SearchEngine):
    async def _fetch_results_async(self, query, max_fetch_results):
        return []


@pytest.mark.asyncio
async def test_search_engine_generate_query_and_rerank(logger, dummy_reranker):
    engine = DummyEngine(logger)
    engine.default_domains = ["a.com", "b.org"]
    engine.reranker = dummy_reranker
    # Test _generate_query
    q = engine._generate_query("foo", ["a.com"], True)
    assert "filetype:pdf" in q and "site:a.com" in q
    # Test _rerank_results con reranker
    docs = [
        search_engine.SearchResult(href=f"u{i}", title=f"t{i}", body=f"b{i}")
        for i in range(5)
    ]
    ranked = engine._rerank_results("foo", docs, 3)
    assert [d.href for d in ranked] == ["u2", "u1", "u0"]
    # Sin reranker
    engine.reranker = None
    ranked2 = engine._rerank_results("foo", docs, 2)
    assert ranked2 == docs[:2]


@pytest.mark.asyncio
async def test_search_engine_search_success_and_rerank(logger, dummy_reranker):
    class DummyEngine2(search_engine.SearchEngine):
        async def _fetch_results_async(self, query, max_fetch_results):
            return [search_engine.SearchResult(href="u", title="t", body="b")]

    engine = DummyEngine2(logger, reranker_model=dummy_reranker)
    # Sin reranker
    res = await engine.search(
        "q", max_fetch_results=2, max_results=1, use_reranker=False
    )
    assert isinstance(res, list) and res[0].href == "u"
    # Con reranker
    res2 = await engine.search(
        "q", max_fetch_results=2, max_results=1, use_reranker=True
    )
    assert isinstance(res2, list)
    # Con exclude_domains
    with patch.object(
        engine, "_fetch_results_async", wraps=engine._fetch_results_async
    ) as fetch_mock:
        await engine.search("q", exclude_domains=["x.com"])
        fetch_mock.assert_awaited()


@pytest.mark.asyncio
async def test_search_engine_search_error(logger):
    class DummyEngine3(search_engine.SearchEngine):
        async def _fetch_results_async(self, query, max_fetch_results):
            raise RuntimeError("fail")

    engine = DummyEngine3(logger)
    with pytest.raises(Exception):
        await engine.search("q")
    # El logger puede no ser llamado si la excepción se propaga directamente,
    # así que solo comprobamos que se lanza la excepción.


@pytest.mark.asyncio
async def test_brave_fetch_success_and_error(monkeypatch, logger):
    # Mock aiohttp.ClientSession y response para soportar async context manager
    class DummyResp:
        status = 200

        async def json(self):
            return {
                "web": {"results": [{"url": "u", "title": "t", "description": "b"}]}
            }

        async def __aenter__(self):
            return self

        async def __aexit__(self, exc_type, exc, tb):
            """mock async context manager"""
            pass

    class DummySession:
        async def __aenter__(self):
            return self

        async def __aexit__(self, exc_type, exc, tb):
            """mock async context manager"""
            pass

        def get(self, *a, **k):
            return DummyResp()

    # Parchea el decorador de tenacity para que no reintente (solo 1 intento)
    with patch(
        "src.api.common.services.search_engine.tenacity.retry",
        lambda *a, **kw: (lambda f: f),
    ):
        monkeypatch.setattr(
            search_engine.aiohttp, "ClientSession", lambda: DummySession()
        )
        engine = search_engine.BraveSearchEngine(logger, api_key="k")
        out = await engine._fetch_results_async("q", 1)
        assert out[0].source == "BRAVE_SEARCH"

        # Error status
        class BadResp(DummyResp):
            status = 500

            async def json(self):
                return {}

        class BadSession(DummySession):
            def get(self, *a, **k):
                return BadResp()

        monkeypatch.setattr(
            search_engine.aiohttp, "ClientSession", lambda: BadSession()
        )
        with pytest.raises(Exception):
            await engine._fetch_results_async("q", 1)
        assert logger.error.called


@pytest.mark.asyncio
async def test_tavily_fetch_success_and_error(monkeypatch, logger):
    # Mock AsyncTavilyClient
    class DummyClient:
        async def search(self, **kwargs):
            return {"results": [{"url": "u", "title": "t", "content": "b"}]}

    # Parchea el decorador de tenacity para que no reintente (solo 1 intento)
    with patch(
        "src.api.common.services.search_engine.tenacity.retry",
        lambda *a, **kw: (lambda f: f),
    ):
        monkeypatch.setattr(
            search_engine, "AsyncTavilyClient", lambda api_key=None: DummyClient()
        )
        engine = search_engine.TavilySearchEngine(logger, api_key="k")
        out = await engine._fetch_results_async("q", 1)
        assert out[0].source == "TAVILY_SEARCH"

        # Error
        class FailingClient(DummyClient):
            async def search(self, **kwargs):
                raise RuntimeError("fail")

        monkeypatch.setattr(
            search_engine, "AsyncTavilyClient", lambda api_key=None: FailingClient()
        )
        engine = search_engine.TavilySearchEngine(logger, api_key="k")
        with pytest.raises(Exception):
            await engine._fetch_results_async("q", 1)
        assert logger.error.called


@pytest.mark.asyncio
async def test_jina_fetch_success_and_error(monkeypatch, logger):
    # Mock aiohttp.ClientSession y response para soportar async context manager
    class DummyResp:
        status = 200

        async def json(self):
            return {
                "data": [{"url": "u", "title": "t", "description": "b"}]
            }

        async def __aenter__(self):
            return self

        async def __aexit__(self, exc_type, exc, tb):
            pass

    class DummySession:
        async def __aenter__(self):
            return self

        async def __aexit__(self, exc_type, exc, tb):
            pass

        async def get(self, *a, **k):
            return DummyResp()

    monkeypatch.setattr(
        search_engine.aiohttp, "ClientSession", lambda: DummySession()
    )
    engine = search_engine.JinaSearchEngine(logger, api_key="k")
    out = await engine._fetch_results_async("q", 1)
    assert out[0].source == "JINA_SEARCH"

    # Error status
    class BadResp(DummyResp):
        status = 500

        async def json(self):
            return {}

    class BadSession(DummySession):
        def get(self, *a, **k):
            return BadResp()

    monkeypatch.setattr(
        search_engine.aiohttp, "ClientSession", lambda: BadSession()
    )
    with pytest.raises(Exception):
        await engine._fetch_results_async("q", 1)
    assert logger.error.called

def test_search_result_dataclass():
    s = search_engine.SearchResult(href="u", title="t", body="b", source="X")
    assert s.href == "u" and s.title == "t" and s.body == "b" and s.source == "X"
