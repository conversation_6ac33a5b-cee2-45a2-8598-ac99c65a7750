from datetime import datetime

from sqlalchemy.dialects.postgresql import J<PERSON>N<PERSON>  # type: ignore
from sqlalchemy.ext.compiler import compiles  # type: ignore


@compiles(JSONB, "sqlite")
def compile_jsonb_sqlite(type_, compiler, **kwargs):
    return compiler.visit_JSON(type_, **kwargs)


import pytest
from sqlmodel import SQLModel, create_engine
from src.api.common.services.ai_tracer import AITracer
from src.api.common.services.structs import ModelInfo, ProcessMetadata
from src.domain.models import (
    AIProcess,
    AIProcessStatus,
    AIProcessType,
)


@pytest.fixture
def fake_db(tmp_path):
    # Crear motor SQLite en memoria y las tablas
    engine = create_engine("sqlite:///:memory:")
    SQLModel.metadata.create_all(engine)
    return engine  # este fixture simula la base de datos


@pytest.fixture
def fake_tracer(fake_db):
    # Instanciar AITracer con metadatos globales
    metadata = {"global_key": "global_value"}
    return AITracer(fake_db, metadata)


@pytest.mark.parametrize("plan_version, failed_count", [(None, None), (1, 2)])
def test_start_process_and_metadata(fake_db, fake_tracer, plan_version, failed_count):
    pm = ProcessMetadata(
        plan_version=plan_version,
        failed_generation_count=failed_count,
        model_info=ModelInfo(name="m1", provider="p1"),
    )
    proc = fake_tracer.start_process(
        AIProcessType.SEARCH_SOURCES,
        indice_id=5,
        tema_id=3,
        epigrafe_id=2,
        parent_id=1,
        process_metadata=pm,
        status=AIProcessStatus.PENDING,
    )
    assert proc.id is not None
    assert proc.process_type == AIProcessType.SEARCH_SOURCES
    assert proc.indice_id == 5
    assert proc.tema_id == 3
    assert proc.epigrafe_id == 2
    assert proc.parent_id == 1
    assert proc.status == AIProcessStatus.PENDING
    assert isinstance(proc.started_at, datetime)
    # Verificar metadata adicional serializado
    assert proc.additional_metadata.get("plan_version") == plan_version
    assert proc.additional_metadata.get("failed_generation_count") == failed_count
    assert proc.additional_metadata.get("model_info") == {
        "name": "m1",
        "provider": "p1",
        "max_tokens": 8000,
        "temperature": 0,
    }


def test_log_execution_and_merge_metadata(fake_db, fake_tracer):
    # Crear proceso base
    proc = fake_tracer.start_process(AIProcessType.SCHEMA_GENERATION)
    # Registrar dos ejecuciones, una retry y con error
    data_in = {"foo": 1}
    data_out = {"bar": 2}
    exec1 = fake_tracer.log_execution(
        proc,
        data_in,
        data_out,
        {"chain": "c1"},
        is_retry=False,
        error_message=None,
        prompt_id=10,
        status=AIProcessStatus.COMPLETED,
    )
    assert exec1.id is not None
    assert exec1.ai_process_id == proc.id
    # Metadatos global + locales
    assert exec1.execution_metadata == {"global_key": "global_value", "chain": "c1"}
    assert not exec1.is_retry
    assert exec1.error_message is None
    assert exec1.prompt_id == 10
    # Error y retry
    exec2 = fake_tracer.log_execution(
        proc,
        data_in,
        data_out,
        {},
        is_retry=True,
        error_message="err",
        status=AIProcessStatus.FAILED,
    )
    assert exec2.is_retry
    assert exec2.error_message == "err"
    assert exec2.status == AIProcessStatus.FAILED


def test_complete_process_updates_status_and_indice(fake_db, fake_tracer):
    proc = fake_tracer.start_process(AIProcessType.SCHEMA_REGENERATION)
    before = proc.started_at
    # Completar sin cambiar indice
    updated = fake_tracer.complete_process(proc)
    assert updated.status == AIProcessStatus.COMPLETED
    assert updated.completed_at >= before
    # Cambiar status e indice
    updated2 = fake_tracer.complete_process(
        updated, AIProcessStatus.FAILED, indice_id=7
    )
    assert updated2.status == AIProcessStatus.FAILED
    assert updated2.indice_id == 7


def test_trace_process_end_to_end(fake_db, fake_tracer):
    # Flujo completo
    p = fake_tracer.trace_process(
        AIProcessType.ADD_PARAGRAPH,
        {"in": 1},
        {"out": 2},
        {"meta": 3},
        execution_status=AIProcessStatus.COMPLETED,
        process_status=AIProcessStatus.IN_PROGRESS,
        indice_id=9,
        tema_id=8,
        epigrafe_id=7,
        parent_id=6,
        prompt_id=11,
        error_message=None,
        process_metadata=None,
    )
    assert isinstance(p, AIProcess)
    assert p.status == AIProcessStatus.IN_PROGRESS
    assert p.indice_id == 9


def test_get_process_history_filters(fake_db, fake_tracer):
    # Crear procesos con distintos atributos
    fake_tracer.start_process(
        AIProcessType.SEARCH_SOURCES, indice_id=1, tema_id=1, parent_id=1
    )
    fake_tracer.start_process(AIProcessType.SEARCH_SOURCES, indice_id=2)
    fake_tracer.start_process(AIProcessType.SCHEMA_GENERATION, tema_id=1, parent_id=2)
    all_procs = fake_tracer.get_process_history()
    assert len(all_procs) == 3
    assert (
        len(fake_tracer.get_process_history(process_type=AIProcessType.SEARCH_SOURCES))
        == 2
    )
    assert len(fake_tracer.get_process_history(indice_id=1)) == 1
    assert len(fake_tracer.get_process_history(tema_id=1)) == 2
    assert len(fake_tracer.get_process_history(parent_id=2)) == 1


def test_execution_details_and_retry_counts(fake_db, fake_tracer):
    proc = fake_tracer.start_process(AIProcessType.SCHEMA_GENERATION)
    # Crear ejecuciones
    fake_tracer.log_execution(proc, {}, {}, {}, is_retry=False)
    fake_tracer.log_execution(proc, {}, {}, {}, is_retry=True)
    fake_tracer.log_execution(proc, {}, {}, {}, is_retry=True)
    # Detalles
    all_execs = list(fake_tracer.get_execution_details(proc.id, include_retries=True))
    assert len(all_execs) == 3
    no_retries = list(fake_tracer.get_execution_details(proc.id, include_retries=False))
    assert len(no_retries) == 1
    # Conteo y lista ordenada
    assert fake_tracer.get_retry_count(proc.id) == 2
    retries = fake_tracer.get_retries_in_sequence(proc.id)
    assert len(retries) == 2
    # Verificar orden creciente de created_at
    times = [e.created_at for e in retries]
    assert times == sorted(times)


def test_get_execution_with_and_without_chain(fake_db, fake_tracer):
    proc = fake_tracer.start_process(AIProcessType.GENERATE_IN_DEPTH)
    a1 = fake_tracer.log_execution(proc, {}, {}, {"chain": "c1"})
    a2 = fake_tracer.log_execution(proc, {}, {}, {"chain": "c2"})
    # Sin filtro devuelve primero
    first = fake_tracer.get_execution(proc.id, AIProcessType.GENERATE_IN_DEPTH.value)
    assert first.id == a1.id
    # Con filtro
    sel = fake_tracer.get_execution(
        proc.id, AIProcessType.GENERATE_IN_DEPTH.value, chain_name="c2"
    )
    assert sel.id == a2.id
    # Cadena inexistente
    none = fake_tracer.get_execution(
        proc.id, AIProcessType.GENERATE_IN_DEPTH.value, chain_name="x"
    )
    assert none is None


def test_rate_and_get_ratings(fake_db, fake_tracer):
    proc = fake_tracer.start_process(AIProcessType.CONTENT_GENERATION_WITH_SOURCES)
    # Calificar proceso
    rating = fake_tracer.rate_process(proc.id, 4, "user1", comment="ok")
    assert rating.ai_process_id == proc.id
    assert rating.rating == 4
    assert rating.comment == "ok"
    assert rating.evaluator_name == "user1"
    # Excepción para id inválido
    with pytest.raises(Exception):
        fake_tracer.rate_process(999, 5, "user2")
    # Obtener calificaciones
    rlist = fake_tracer.get_process_ratings(proc.id)
    assert len(rlist) == 1
    # Con filtro de tipo (coincide)
    rated = fake_tracer.get_process_ratings(
        proc.id, AIProcessType.CONTENT_GENERATION_WITH_SOURCES
    )
    assert len(rated) == 1
    # Con filtro errado
    zero = fake_tracer.get_process_ratings(proc.id, AIProcessType.ADD_PARAGRAPH)
    assert zero == []
