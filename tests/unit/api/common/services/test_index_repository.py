from types import SimpleNamespace
from unittest.mock import AsyncMock, MagicMock
from typing import cast # Importar cast

import pytest
from sqlmodel.ext.asyncio.session import AsyncSession
from src.api.common.services.index_repository import (
    CompetenciesNotFoundError,
    IndexNotFoundError,
    IndexRepository,
)
from src.api.common.services.structs import Competencias as ExpectedCompetenciasType # Importar el tipo esperado
from src.domain.models import Indice


@pytest.fixture
def fake_logger():
    return MagicMock(name="Logger")


@pytest.fixture
def fake_tracer():
    return MagicMock(name="LLMTracer")


@pytest.fixture
def fake_db():
    return MagicMock(name="AsyncEngine")


@pytest.fixture
def fake_subject():
    fake_epigrafe_1 = SimpleNamespace(nombre="Suma")
    fake_epigrafe_2 = SimpleNamespace(nombre="Resta")
    fake_tema = SimpleNamespace(
        nombre="Aritmética", epigrafes=[fake_epigrafe_1, fake_epigrafe_2]
    )
    fake_bloque = SimpleNamespace(nombre="Números", temas=[fake_tema])
    fake_subject = SimpleNamespace(
        nombre="Matematicas Primaria",
        estructura=SimpleNamespace(bloques_tematicos=[fake_bloque]),
    )
    return fake_subject


@pytest.fixture
def fake_session():
    session = MagicMock(spec=AsyncSession)
    session.exec = AsyncMock()
    session.execute = AsyncMock()
    session.add = MagicMock()
    session.flush = AsyncMock()
    session.commit = AsyncMock()
    session.close = AsyncMock()
    session.refresh = AsyncMock()

    session.__aenter__ = AsyncMock(return_value=session)
    session.__aexit__ = AsyncMock(return_value=None)

    import itertools

    _counter = itertools.count(start=1)

    async def flush_side_effect(*args, **kwargs):
        for call in session.add.call_args_list:
            obj = call.args[0]
            if (
                hasattr(obj, "description")
                and hasattr(obj, "indices")
                and getattr(obj, "id", None) is None
            ):
                object.__setattr__(obj, "id", next(_counter))

    session.flush.side_effect = flush_side_effect
    return session


async def test_get_latest_index_returns(
    fake_db, fake_logger, fake_tracer, fake_session
):
    fake_index = MagicMock(spec=Indice)
    fake_index.version = 3

    fake_result = MagicMock()
    fake_result.first.return_value = fake_index
    fake_session.exec.return_value = fake_result

    repo = IndexRepository(fake_db, fake_logger, fake_tracer)
    result = await repo.get_latest_index(order_id=123, session=fake_session)

    fake_session.exec.assert_awaited_once()
    assert result is fake_index
    assert result.version == 3


async def test_get_latest_index_not_exists(
    fake_db, fake_logger, fake_tracer, fake_session
):
    fake_index = None

    fake_result = MagicMock()
    fake_result.first.return_value = fake_index
    fake_session.exec.return_value = fake_result

    repo = IndexRepository(fake_db, fake_logger, fake_tracer)
    with pytest.raises(IndexNotFoundError):
        await repo.get_latest_index(order_id=1, session=fake_session)


class FakeCompetencias:
    def __init__(self, *descripciones: str):
        self._descripciones = descripciones

    def model_dump(self):
        return {"competencias": [{"descripcion": d} for d in self._descripciones]}


async def test_store_competencies_inserts_new(
    fake_db, fake_logger, fake_tracer, fake_session
):
    indice_id = 7
    competencias_data = FakeCompetencias(
        "Creatividad", "Ser una leyenda", "Aprender a ser ingeniero de IA"
    )
    # Hacer el cast de competencias_data al tipo ExpectedCompetenciasType
    competencias_in = cast(ExpectedCompetenciasType, competencias_data)

    async def exec_side_effect(statement, *args, **kwargs):
        fake_result = MagicMock()
        fake_result.first.return_value = None
        return fake_result

    fake_session.exec.side_effect = exec_side_effect
    fake_session.execute = AsyncMock()

    repo = IndexRepository(fake_db, fake_logger, fake_tracer)

    returned_ids = await repo.store_competencies(
        indice_id=indice_id,
        competencies=competencias_in, # Usar la variable casteada
        session=fake_session
    )
    assert fake_session.add.call_count == 6
    assert fake_session.flush.await_count == 3
    fake_session.commit.assert_awaited_once()

    assert returned_ids == [1, 2, 3]


async def test_store_competencies_exist(
    fake_db, fake_logger, fake_tracer, fake_session
):
    indice_id = 7
    competencias_data = FakeCompetencias("Creatividad")
    competencias_in = cast(ExpectedCompetenciasType, competencias_data)


    existing = MagicMock(description="Creatividad", id=99)

    async def exec_side_effect(statement, *args, **kwargs):
        fake_result = MagicMock()
        fake_result.first.return_value = existing
        return fake_result

    fake_session.exec.side_effect = exec_side_effect
    fake_session.execute = AsyncMock()

    repo = IndexRepository(fake_db, fake_logger, fake_tracer)

    returned_ids = await repo.store_competencies(
        indice_id=indice_id, competencies=competencias_in, session=fake_session
    )

    fake_session.commit.assert_awaited_once()

    assert returned_ids == [99]


async def test_get_competencies_exist(fake_db, fake_session, fake_logger, fake_tracer):
    r1, r2 = MagicMock(description="Hola"), MagicMock(description="Qué tal")

    fake_result = MagicMock()
    fake_result.all.return_value = [r1, r2]

    fake_session.exec.return_value = fake_result

    repo = IndexRepository(fake_db, fake_logger, fake_tracer)
    result = await repo.get_competencies(indice_id=123, session=fake_session)

    assert len(result.competencias) == 2
    fake_session.exec.assert_awaited_once()


async def test_get_competencies_not_found(
    fake_db, fake_session, fake_logger, fake_tracer
):
    fake_result = MagicMock()
    fake_result.all.return_value = None

    fake_session.exec.return_value = fake_result

    repo = IndexRepository(fake_db, fake_logger, fake_tracer)

    with pytest.raises(CompetenciesNotFoundError):
        await repo.get_competencies(indice_id=1, session=fake_session)
    fake_session.exec.assert_awaited_once()


@pytest.mark.asyncio
async def test_get_subject_index_exists(
    fake_db, fake_session, fake_logger, fake_tracer
):
    fake_indice = SimpleNamespace(
        order=SimpleNamespace(title_subject=SimpleNamespace(name="Matemáticas"))
    )

    fake_epigrafe_1 = SimpleNamespace(name="Suma", position = 1)
    fake_epigrafe_2 = SimpleNamespace(name="Resta", position = 2)
    fake_tema = SimpleNamespace(
        name="Aritmética", epigrafes=[fake_epigrafe_1, fake_epigrafe_2], position = 1
    )
    fake_bloque = SimpleNamespace(name="Números", temas=[fake_tema], position = 1)

    result_1 = MagicMock()
    result_1.first.return_value = fake_indice

    result_2 = MagicMock()
    result_2.unique.return_value.all.return_value = [fake_bloque]

    fake_session.exec.side_effect = [result_1, result_2]

    repo = IndexRepository(fake_db, fake_logger, fake_tracer)
    asignatura = await repo.get_subject_index(indice_id=2, session=fake_session)

    assert asignatura.nombre == "Matemáticas"
    bloque = asignatura.estructura.bloques_tematicos[0]
    assert bloque.nombre == "Números"
    tema = bloque.temas[0]
    assert tema.nombre == "Aritmética"
    assert tema.epigrafes == ["Suma", "Resta"]

    assert fake_session.exec.await_count == 2


async def test_get_subject_index_not_found(
    fake_db, fake_session, fake_logger, fake_tracer
):
    result_1 = MagicMock()
    result_1.first.return_value = None

    fake_session.exec.side_effect = [result_1]
    repo = IndexRepository(fake_db, fake_logger, fake_tracer)
    with pytest.raises(IndexNotFoundError):
        await repo.get_subject_index(indice_id=1, session=fake_session)

    fake_session.exec.assert_awaited_once()
    fake_logger.exception.assert_called_once()


async def test_store_subject_index_index_provided(
    fake_db, fake_session, fake_logger, fake_tracer, fake_subject, monkeypatch
):
    import src.api.common.services.index_repository as repo_mod

    monkeypatch.setattr(repo_mod, "AsyncSession", lambda *_a, **_k: fake_session)
    repo = IndexRepository(fake_db, fake_logger, fake_tracer)
    repo.get_index = AsyncMock(return_value=SimpleNamespace(id=5))

    result = await repo.store_subject_index(
        asignatura=fake_subject,
        order_id=1,
        indice_id=5,
    )
    assert result == 5
    repo.get_index.assert_called_once()
    fake_session.commit.assert_awaited_once()
    assert fake_session.add.call_count == 4


async def test_store_subject_index_index_not_provided(
    fake_db, fake_session, fake_logger, fake_tracer, fake_subject, monkeypatch
):
    import src.api.common.services.index_repository as repo_mod

    monkeypatch.setattr(repo_mod, "AsyncSession", lambda *_a, **_k: fake_session)
    repo = IndexRepository(fake_db, fake_logger, fake_tracer)
    repo.create_index = AsyncMock(return_value=SimpleNamespace(id=5))

    result = await repo.store_subject_index(
        asignatura=fake_subject,
        order_id=1,
    )
    assert result == 5
    repo.create_index.assert_called_once()
    fake_session.commit.assert_awaited_once()
    assert fake_session.add.call_count == 4


async def test_store_subject_index_competencies_provided(
    fake_db, fake_session, fake_logger, fake_tracer, fake_subject, monkeypatch
):
    import src.api.common.services.index_repository as repo_mod

    competencies = SimpleNamespace(
        competencias=[
            SimpleNamespace(descripcion="Hola"),
            SimpleNamespace(descripcion="Que tal"),
        ]
    )

    monkeypatch.setattr(repo_mod, "AsyncSession", lambda *_a, **_k: fake_session)
    repo = IndexRepository(fake_db, fake_logger, fake_tracer)
    repo.create_index = AsyncMock(return_value=SimpleNamespace(id=5))
    repo.store_competencies = AsyncMock(return_value=[1, 2])

    result = await repo.store_subject_index(
        asignatura=fake_subject, order_id=1, competencias=competencies
    )
    assert result == 5
    repo.create_index.assert_called_once()
    repo.store_competencies.assert_called_once()
    assert fake_session.commit.call_count == 1
    assert fake_session.add.call_count == 4


async def test_create_index_order_exist_no_index(
    fake_db, fake_session, fake_logger, fake_tracer, monkeypatch
):
    import src.api.common.services.index_repository as repo_mod

    monkeypatch.setattr(repo_mod, "AsyncSession", lambda *_a, **_k: fake_session)

    fake_order = SimpleNamespace(id=4)
    fake_result_1 = MagicMock()
    fake_result_1.first.return_value = fake_order

    fake_result_2 = MagicMock()
    fake_result_2.first.return_value = None

    fake_session.exec.side_effect = [fake_result_1, fake_result_2]

    repo = IndexRepository(fake_db, fake_logger, fake_tracer)

    index = await repo.create_index(order_id=4)

    assert index.version == 1
    assert index.order_id == 4
    fake_session.commit.assert_awaited_once()


async def test_create_index_order_exist_index_present(
    fake_db, fake_session, fake_logger, fake_tracer, monkeypatch
):
    import src.api.common.services.index_repository as repo_mod

    monkeypatch.setattr(repo_mod, "AsyncSession", lambda *_a, **_k: fake_session)

    fake_order = SimpleNamespace(id=4)
    fake_result_1 = MagicMock()
    fake_result_1.first.return_value = fake_order

    fake_index = SimpleNamespace(id=3, version=2, order_id=4)
    fake_result_2 = MagicMock()
    fake_result_2.first.return_value = fake_index

    fake_session.exec.side_effect = [fake_result_1, fake_result_2]

    repo = IndexRepository(fake_db, fake_logger, fake_tracer)

    index = await repo.create_index(order_id=4)

    assert index.version == 3
    assert index.order_id == 4
    fake_session.commit.assert_awaited_once()


async def test_create_index_order_not_exist(
    fake_db, fake_session, fake_logger, fake_tracer, monkeypatch
):
    import src.api.common.services.index_repository as repo_mod

    monkeypatch.setattr(repo_mod, "AsyncSession", lambda *_a, **_k: fake_session)

    fake_order = None
    fake_result = MagicMock()
    fake_result.first.return_value = fake_order

    fake_session.exec.return_value = fake_result

    repo = IndexRepository(fake_db, fake_logger, fake_tracer)

    with pytest.raises(ValueError):
        await repo.create_index(order_id=4)

    fake_logger.error.assert_called_once()
