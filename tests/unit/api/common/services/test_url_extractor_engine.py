import logging
from unittest.mock import MagicMock

import httpx
import pytest
from src.api.common.services import url_extractor_engine as ue


@pytest.fixture
def logger():
    return MagicMock(spec=logging.Logger)


@pytest.mark.asyncio
async def test_extraction_success_and_error_dataclasses():
    s = ue.ExtractionSuccess(url="u", content="c", images=["i1"])
    assert (
        s.status == "success"
        and s.url == "u"
        and s.content == "c"
        and s.images == ["i1"]
    )
    e = ue.ExtractionError(url="u2", error="fail")
    assert e.status == "error" and e.url == "u2" and e.error == "fail"


class DummyExtractor(ue.UrlExtractor):
    async def extract(self, urls):
        return [ue.ExtractionSuccess(url=u, content="ok") for u in urls]


@pytest.mark.asyncio
async def test_url_extractor_abstract():
    d = DummyExtractor(MagicMock())

    out = await d.extract(["a"])
    assert out[0].status == "success"



@pytest.mark.asyncio
async def test_tavily_extractor_success_and_error(logger, monkeypatch):
    # Mock AsyncTavilyClient
    class DummyClient:
        async def extract(self, **kwargs):
            return {
                "results": [{"url": "u1", "raw_content": "c1", "images": ["i1"]}],
                "failed_results": [{"url": "u2", "error": "fail"}],
            }

    monkeypatch.setattr(ue, "AsyncTavilyClient", lambda api_key=None: DummyClient())
    ext = ue.TavilyExtractor(logger, api_key="k")
    out = await ext.extract(["u1", "u2"])
    assert any(x.status == "success" for x in out)
    assert any(x.status == "error" for x in out)


@pytest.mark.asyncio
async def test_tavily_extractor_handles_exception(logger, monkeypatch):
    class FailingClient:
        async def extract(self, **kwargs):
            raise RuntimeError("fail")

    monkeypatch.setattr(ue, "AsyncTavilyClient", lambda api_key=None: FailingClient())
    ext = ue.TavilyExtractor(logger, api_key="k")
    with pytest.raises(Exception):
        await ext.extract(["u1"])


@pytest.mark.asyncio
async def test_jina_extractor_success_and_error(logger, monkeypatch):
    # Mock httpx.AsyncClient
    class DummyResp:
        status_code = 200

        def json(self):
            return {"data": {"content": "c", "images": {"a": "b"}}}

    class DummyClient:
        async def __aenter__(self):
            return self

        async def __aexit__(self, exc_type, exc, tb):
            """mock async context manager"""
            pass

        async def post(self, url, json, headers):
            return DummyResp()

    monkeypatch.setattr(httpx, "AsyncClient", lambda **kwargs: DummyClient())
    ext = ue.JinaExtractor(logger, api_key="k")
    out = await ext.extract(["u1"])
    assert out[0].status == "success"
    assert out[0].content == "c"

    # Error status
    class BadResp:
        status_code = 500
        text = "fail"

        def json(self):
            return {}

    class BadClient(DummyClient):
        async def post(self, url, json, headers):
            return BadResp()

    monkeypatch.setattr(httpx, "AsyncClient", lambda **kwargs: BadClient())
    ext = ue.JinaExtractor(logger, api_key="k")
    out = await ext.extract(["u2"])
    assert out[0].status == "error"
    assert "Failed to extract content" in out[0].error


@pytest.mark.asyncio
async def test_jina_extractor_exception(logger, monkeypatch):
    class DummyClient:
        async def __aenter__(self):
            return self

        async def __aexit__(self, exc_type, exc, tb):
            """mock async context manager"""
            pass

        async def post(self, url, json, headers):
            raise RuntimeError("fail")

    monkeypatch.setattr(httpx, "AsyncClient", lambda **kwargs: DummyClient())
    ext = ue.JinaExtractor(logger, api_key="k")
    out = await ext.extract(["u3"])
    assert out[0].status == "error"
    assert "fail" in out[0].error
    logger.error.assert_called()
