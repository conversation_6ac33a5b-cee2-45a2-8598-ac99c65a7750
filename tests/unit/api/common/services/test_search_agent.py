from types import SimpleNamespace
from unittest.mock import AsyncMock, MagicMock

import pytest  # type: ignore
import tiktoken
from agents import Runner
from src.api.common.services.search_agent import (
    Evaluaciones,
    EvaluationDetail,
    ExtractContentOutputModel,
    ExtractionError,
    AgentResult,
    SearchAgent,
    SearchQueryOutput,
    SearchType,
    ValoracionesOutputModel,
    ValoracionItem,
)


def make_dummy_prompt_manager():
    class DummyPrompt:
        def __init__(self):
            self.prompt = "dummy"

        def compile(self, fecha_actual=None):
            # no-op compile for dummy prompt manager in tests
            return None

    return SimpleNamespace(get_prompt=lambda name: DummyPrompt())


@pytest.fixture
def agent():
    fake_openai = MagicMock()
    fake_ac = MagicMock(name="academic_engine")
    fake_nac = MagicMock(name="non_academic_engine")
    fake_url_ext = MagicMock(name="url_extractor")
    fake_url_ext.extract = AsyncMock()  # Asegura que extract es awaitable
    fake_prompt = make_dummy_prompt_manager()
    return SearchAgent(
        openai_async_client=fake_openai,
        academic_search_engine=fake_ac,
        non_academic_search_engine=fake_nac,
        url_extractor=fake_url_ext,
        prompt_manager=fake_prompt,
    )


def test_break_text_into_chunks(agent):
    text = "abc " * 50
    chunks = agent._break_text_into_chunks(text, max_tokens=10)
    assert chunks, "chunks no debe estar vacío"
    encoding = tiktoken.get_encoding("o200k_base")
    assert all(
        len(encoding.encode(chunk, disallowed_special=())) <= 10 for chunk in chunks
    )
    assert sum(len(encoding.encode(c, disallowed_special=())) for c in chunks) == len(
        encoding.encode(text, disallowed_special=())
    )


def test_break_text_into_chunks_exact():
    # Cubre el caso de chunking exacto
    from src.api.common.services.search_agent import SearchAgent

    agent = SearchAgent.__new__(SearchAgent)
    text = "a " * 100
    agent._break_text_into_chunks = SearchAgent._break_text_into_chunks
    chunks = agent._break_text_into_chunks(agent, text, 10)
    assert isinstance(chunks, list)
    assert sum(len(c) for c in chunks) >= 100


@pytest.mark.asyncio
async def test_extract_content_empty_and_error(agent):
    # caso urls vacíos
    result = await agent._extract_content([])
    assert result == []
    # caso error de extracción
    err_resp = SimpleNamespace(status="error", url="u1", error="fail")
    agent._url_extractor.extract = AsyncMock(return_value=[err_resp])
    out = await agent._extract_content(["u1"])
    assert isinstance(out[0], ExtractionError)
    assert out[0].url == "u1"
    assert out[0].error == "fail"


@pytest.mark.asyncio
async def test_extract_content_success(agent):
    # configurar extractor y llamada al API estructurado
    good = SimpleNamespace(status="ok", url="u2", content="text content")
    agent._url_extractor.extract = AsyncMock(return_value=[good])
    fake_parsed = ExtractContentOutputModel(
        title="T", url="u2", authors=["A"], date=2020, summary=["s1"], lenguaje="es", is_pay_walled=False, is_info_accesbile=True
    )
    agent._call_api_structured = AsyncMock(return_value=fake_parsed)
    out = await agent._extract_content(["u2"])
    assert isinstance(out[0], ExtractContentOutputModel)
    # verificar mayúsculas en lenguaje
    assert out[0].lenguaje == "ES"
    assert out[0].summary == ["s1"]
    assert out[0].title == "T"


@pytest.mark.asyncio
async def test_extract_content_multiple_chunks(agent):
    # Cubre varios chunks y normalización de lenguaje
    good = SimpleNamespace(status="ok", url="u2", content="a " * 300000)
    agent._url_extractor.extract = AsyncMock(return_value=[good])
    fake1 = ExtractContentOutputModel(
        title="T", url="u2", authors=["A"], date=2020, summary=["s1"], lenguaje="es", is_pay_walled=False, is_info_accesbile=True
    )
    fake2 = ExtractContentOutputModel(
        title="", url="u2", authors=[], date=None, summary=["s2"], lenguaje="es", is_pay_walled=False, is_info_accesbile=True
    )

    def fake_side_effect(*args, **kwargs):
        if fake_side_effect.counter == 0:
            fake_side_effect.counter += 1
            return fake1
        return fake2

    fake_side_effect.counter = 0
    agent._call_api_structured = AsyncMock(side_effect=fake_side_effect)
    out = await agent._extract_content(["u2"])
    # El primer resumen debe ser 's1', el resto 's2'
    assert out[0].summary[0] == "s1"
    assert all(s == "s2" for s in out[0].summary[1:])
    assert out[0].lenguaje == "ES"


@pytest.mark.asyncio
async def test_search_empty_when_no_results(agent):
    agent._search_non_academic = AsyncMock(return_value=[])
    agent._search_academic = AsyncMock(return_value=[])
    agent._evaluate_results = AsyncMock()
    res = await agent._search("c", "info", ["q1", "q2"], False, SearchType.both)
    assert res == []
    agent._search_non_academic.assert_called()
    agent._search_academic.assert_called()


@pytest.mark.asyncio
async def test_search_invokes_evaluate(agent):
    # preparar resultados de búsqueda
    r1 = SimpleNamespace(title="t1", href="u1", body="b1")
    r2 = SimpleNamespace(title="t2", href="u2", body="b2")
    agent._search_non_academic = AsyncMock(return_value=[r1])
    agent._search_academic = AsyncMock(return_value=[r2])
    # for una sola consulta, se agrupa en un grupo
    scored = [("t1", "u1", 8.0)]
    agent._evaluate_results = AsyncMock(return_value=scored)
    # Mock _extract_content para devolver un resultado simulado
    fake_doc = ExtractContentOutputModel(
        title="T1", url="u1", authors=["A"], date=2024, summary=["s"], lenguaje="es", is_pay_walled=False, is_info_accesbile=True
    )
    agent._extract_content = AsyncMock(return_value=[fake_doc])
    res = await agent._search("c", "info", ["q"], True, SearchType.both)
    # Debe devolver lista con ExtractContentOutputModel
    assert isinstance(res, list)
    assert isinstance(res[0], ExtractContentOutputModel)
    assert res[0].title == "T1"
    agent._evaluate_results.assert_called_once()
    agent._extract_content.assert_called_once()


@pytest.mark.asyncio
async def test_search_grouping_and_dedup(agent):
    # Cubre agrupamiento y deduplicación
    r = SimpleNamespace(title="t", href="u", body="b")
    agent._search_non_academic = AsyncMock(return_value=[r, r])
    agent._search_academic = AsyncMock(return_value=[])
    agent._evaluate_results = AsyncMock(return_value=[("t", "u", 8.0), ("t", "u", 8.0)])
    fake_doc = ExtractContentOutputModel(
        title="T", url="u", authors=["A"], date=2024, summary=["s"], lenguaje="es", is_pay_walled=False, is_info_accesbile=True
    )
    agent._extract_content = AsyncMock(return_value=[fake_doc])
    res = await agent._search("c", "info", ["q"] * 11, False, SearchType.non_academic)
    # Debe agrupar en más de un grupo si hay más de 10 y devolver ExtractContentOutputModel
    assert isinstance(res, list)
    assert isinstance(res[0], ExtractContentOutputModel)
    assert res[0].title == "T"


@pytest.mark.asyncio
async def test_run_returns_final_output(agent, monkeypatch):
    # Caso max_results=1, debe devolver un AgentResult
    dummy_doc = ExtractContentOutputModel(
        title="T", url="u", authors=["A"], date=2024, summary=["s"], lenguaje="es", is_pay_walled=False, is_info_accesbile=True
    )
    dummy_result = AgentResult(documents=[dummy_doc])
    dummy = SimpleNamespace(final_output=dummy_result)
    monkeypatch.setattr(Runner, "run", AsyncMock(return_value=dummy))
    out = await agent.run("q", "ctx", max_results=1)
    assert isinstance(out, AgentResult)
    assert out.documents[0].title == "T"
    # cuando max_results >1, debe devolver un único AgentResult con todos los documentos combinados
    dummy_doc2 = ExtractContentOutputModel(
        title="T2", url="u2", authors=["B"], date=2023, summary=["s2"], lenguaje="en", is_pay_walled=False, is_info_accesbile=True
    )
    dummy_result2 = AgentResult(documents=[dummy_doc2, dummy_doc])
    dummy2 = SimpleNamespace(final_output=dummy_result2)
    monkeypatch.setattr(Runner, "run", AsyncMock(return_value=dummy2))
    out2 = await agent.run("q2", "ctx2", max_results=3)
    assert isinstance(out2, AgentResult)
    # Debe contener ambos documentos
    titles = [d.title for d in out2.documents]
    assert "T" in titles
    assert "T2" in titles


@pytest.mark.asyncio
async def test_evaluate_results_sorts_and_rounds(agent):
    # Preparar dos documentos de ejemplo
    docs = [
        SearchQueryOutput(title="t1", url="u1", snippet="s1"),
        SearchQueryOutput(title="t2", url="u2", snippet="s2"),
    ]
    import random
    # Mockear llamada al API estructurado para devolver una valoración aleatoria en cada llamada
    async def mock_call_api_structured(*args, **kwargs):
        valor = random.randint(1, 5)
        detail = EvaluationDetail(reasoning="r", valoracion=valor)
        evals = Evaluaciones(
            pertinencia_titulo=detail,
            coherencia_descripcion=detail,
            profundidad_informacion=detail,
            claridad_precision=detail,
        )
        item = ValoracionItem(id=0, evaluaciones=evals)
        return ValoracionesOutputModel(valoraciones=[item])
    agent._call_api_structured = AsyncMock(side_effect=mock_call_api_structured)
    # Ejecutar evaluación
    res = await agent._evaluate_results("concepto", docs, "info")
    # Debe ordenar descendente y redondear los scores (1^1/4=1, 2^1/4=2)
    assert all(isinstance(score, float) for (_, _, score) in res)
    assert sorted(res, key=lambda x: x[2], reverse=True) == res
    assert set((d.title, d.url) for d in docs) == set((t, u) for (t, u, _) in res)


@pytest.mark.asyncio
async def test_call_api_structured_reasoning_effort(agent):
    # Cubre rama reasoning_effort
    agent._openai_client.beta.chat.completions.parse = AsyncMock(
        return_value=SimpleNamespace(
            choices=[SimpleNamespace(message=SimpleNamespace(parsed="ok"))]
        )
    )
    res = await agent._call_api_structured(
        "dev", "m", "msg", ExtractContentOutputModel, reasoning_effort="mucho"
    )
    assert res == "ok"


@pytest.mark.asyncio
async def test_search_non_academic_raises(agent):
    async def fail_search(*args, **kwargs):
        raise RuntimeError("fail")

    agent._non_academic_search_engine.search = fail_search
    with pytest.raises(Exception):
        await agent._search_non_academic("q", False)
