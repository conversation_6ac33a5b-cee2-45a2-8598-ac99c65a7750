import pytest
from unittest.mock import MagicMock, patch, AsyncMock
import httpx
import requests
from src.api.common.services.mail_sender import MailSender
from src.domain.models import EmailTriggerStatus

@pytest.fixture
def logger():
    return MagicMock()

@pytest.fixture
def mail_sender(logger) -> MailSender:
    return MailSender(
        mail_send_endpoint="https://fake-impossible-mail-service.nonexistent/v99/totally-fake-domain.impossible/send-email",
        mail_send_api_key="fake-test-api-key-12345",
        logger=logger
    )

class TestMailSender:
    @pytest.mark.asyncio
    async def test_async_send_email_success(self, mail_sender, logger):
        mock_response = MagicMock()
        mock_response.status_code = 200
        with patch("httpx.AsyncClient") as mock_client:
            mock_client_instance = AsyncMock()
            mock_client.return_value.__aenter__.return_value = mock_client_instance
            mock_client_instance.post.return_value = mock_response
            await mail_sender.async_send_email(123, EmailTriggerStatus.INDEX, 456)
            mock_client_instance.post.assert_called_once_with(
                mail_sender.mail_endpoint,
                headers={
                    "x-api-key": mail_sender.mail_api_key,
                    "Content-Type": "application/json",
                },
                json={"orderId": 123, "triggerStatus": "cambio_estado_indice", "topicId": 456},
            )
            logger.info.assert_called_once()

    @pytest.mark.asyncio
    async def test_async_send_email_success_without_topic(self, mail_sender, logger):
        mock_response = MagicMock()
        mock_response.status_code = 200
        with patch("httpx.AsyncClient") as mock_client:
            mock_client_instance = AsyncMock()
            mock_client.return_value.__aenter__.return_value = mock_client_instance
            mock_client_instance.post.return_value = mock_response
            await mail_sender.async_send_email(123, EmailTriggerStatus.TOPIC)
            mock_client_instance.post.assert_called_once_with(
                mail_sender.mail_endpoint,
                headers={
                    "x-api-key": mail_sender.mail_api_key,
                    "Content-Type": "application/json",
                },
                json={"orderId": 123, "triggerStatus": "cambio_estado_tema"},
            )
            logger.info.assert_called_once()
            
    @pytest.mark.asyncio
    async def test_async_send_email_http_error(self, mail_sender, logger):
        mock_response = MagicMock()
        mock_response.status_code = 400
        mock_response.text = "Bad Request"
        with patch("httpx.AsyncClient") as mock_client:
            mock_client_instance = AsyncMock()
            mock_client.return_value.__aenter__.return_value = mock_client_instance
            mock_client_instance.post.return_value = mock_response
            mock_response.raise_for_status.side_effect = httpx.HTTPStatusError(
                "HTTP Error", request=MagicMock(), response=mock_response
            )
            await mail_sender.async_send_email(123, EmailTriggerStatus.INDEX, 456)
            logger.error.assert_called_once()

    @pytest.mark.asyncio
    async def test_async_send_email_request_exception(self, mail_sender, logger):
        with patch("httpx.AsyncClient") as mock_client:
            mock_client_instance = AsyncMock()
            mock_client.return_value.__aenter__.return_value = mock_client_instance
            mock_client_instance.post.side_effect = httpx.RequestError("Connection error")
            with pytest.raises(httpx.RequestError):
                await mail_sender.async_send_email(123, EmailTriggerStatus.INDEX, 456)

    def test_send_email_success(self, mail_sender, logger):
        mock_response = MagicMock()
        mock_response.status_code = 200
        with patch("requests.post", return_value=mock_response) as mock_post:
            mail_sender.send_email(123, EmailTriggerStatus.INDEX, 456)
            mock_post.assert_called_once_with(
                mail_sender.mail_endpoint,
                headers={
                    "x-api-key": mail_sender.mail_api_key,
                    "Content-Type": "application/json",
                },
                json={"orderId": 123, "triggerStatus": "cambio_estado_indice", "topicId": 456},
            )
            logger.info.assert_called_once()

    def test_send_email_success_without_topic(self, mail_sender, logger):
        mock_response = MagicMock()
        mock_response.status_code = 200
        with patch("requests.post", return_value=mock_response) as mock_post:
            mail_sender.send_email(123, EmailTriggerStatus.TOPIC)
            mock_post.assert_called_once_with(
                mail_sender.mail_endpoint,
                headers={
                    "x-api-key": mail_sender.mail_api_key,
                    "Content-Type": "application/json",
                },
                json={"orderId": 123, "triggerStatus": "cambio_estado_tema"},
            )
            logger.info.assert_called_once()

    def test_send_email_http_error(self, mail_sender, logger):
        mock_response = MagicMock()
        mock_response.status_code = 500
        mock_response.text = "Internal Server Error"
        with patch("requests.post", return_value=mock_response) as _:
            mock_response.raise_for_status.side_effect = requests.HTTPError(response=mock_response)
            mail_sender.send_email(123, EmailTriggerStatus.INDEX, 456)
            logger.error.assert_called_once()

    def test_send_email_request_exception(self, mail_sender, logger):
        with patch("requests.post", side_effect=requests.RequestException("Connection error")):
            with pytest.raises(requests.RequestException):
                mail_sender.send_email(123, EmailTriggerStatus.INDEX, 456)

    def test_send_email_all_statuses(self, mail_sender, logger):
        mock_response = MagicMock()
        mock_response.status_code = 200
        with patch("requests.post", return_value=mock_response) as mock_post:
            for status in EmailTriggerStatus:
                mail_sender.send_email(1, status)
                assert mock_post.call_args[1]["json"]["triggerStatus"] == status.value
