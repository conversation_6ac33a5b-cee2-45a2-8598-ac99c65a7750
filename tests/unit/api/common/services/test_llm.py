from unittest.mock import patch

import pytest
from src.api.common.services.llm import LLM


@pytest.fixture
def llm():
    return LLM(
        openai_api_key="openai-key",
        anthropic_api_key="anthropic-key",
        azure_api_key="azure-key",
        azure_openai_endpoint="https://azure.endpoint",
        azure_api_version="2024-05-01-preview",
    )


def test_models_property_and_get_available_models():
    models = LLM.get_available_models()
    assert "openai" in models and "anthropic" in models and "azure-openai" in models
    openai_models = LLM.get_available_models("openai")
    assert "gpt-4-turbo" in openai_models
    # provider inexistente
    res = LLM.get_available_models("no-existe")
    assert isinstance(res, dict)


def test_models_property_indexing(llm):
    models = llm.models
    assert isinstance(models, dict)
    assert any(m["provider"] == "openai" for m in models.values())


def test_copy_method(llm):
    llm2 = llm.copy()
    assert isinstance(llm2, LLM)
    assert llm2._openai_api_key == llm._openai_api_key
    assert llm2 is not llm


@patch("src.api.common.services.llm.ChatOpenAI")
def test_chat_openai_all_branches(mock_chat):
    # Modelos con reasoning_effort
    llm = LLM("k1", "k2", "k3")
    for m in ["o1", "o3-mini", "o1-mini", "o1-preview", "o4-mini", "o3"]:
        llm.chat_openai(
            model_name=m, max_tokens=10, frequency_penalty=0.1, reasoning_effort="high"
        )
        mock_chat.assert_called_with(
            api_key=None,
            model=m,
            max_completion_tokens=10,
            frequency_penalty=0.1,
            reasoning_effort="high",
            base_url=None,
            default_headers=None,
        )
    # Modelos normales
    llm.chat_openai(
        model_name="gpt-4-turbo", max_tokens=20, temperature=0.5, frequency_penalty=0.2
    )
    mock_chat.assert_called_with(
        api_key=None,
        model="gpt-4-turbo",
        max_completion_tokens=20,
        temperature=0.5,
        frequency_penalty=0.2,
        base_url=None,
        default_headers=None,
    )


@patch("src.api.common.services.llm.ChatAnthropic")
def test_chat_anthropic_all_branches(mock_anthropic):
    llm = LLM("k1", "k2", "k3")
    llm.chat_anthropic(
        model_name="claude-3-7-sonnet-20250219",
        max_tokens=30,
        temperature=0.1,
        reasoning_effort="high",
    )
    mock_anthropic.assert_called_with(
        api_key="k2",
        model_name="claude-3-7-sonnet-20250219",
        max_tokens_to_sample=30,
        temperature=1,
        thinking={"type": "enabled", "budget_tokens": 20000},
    )
    # Modelo normal
    llm.chat_anthropic(
        model_name="claude-3-sonnet-20240229", max_tokens=40, temperature=0.2
    )
    mock_anthropic.assert_called_with(
        api_key="k2",
        model_name="claude-3-sonnet-20240229",
        max_tokens_to_sample=40,
        temperature=0.2,
    )


@patch("src.api.common.services.llm.AzureChatOpenAI")
def test_chat_azure(mock_azure):
    llm = LLM(
        "k1",
        "k2",
        azure_api_key="azk",
        azure_openai_endpoint="https://az",
        azure_api_version="2024-05-01",
    )
    llm.chat_azure(
        model_name="gpt-4o", max_tokens=50, temperature=0.3, frequency_penalty=0.4
    )
    mock_azure.assert_called_with(
        azure_endpoint="https://az",
        api_key="azk",
        model="gpt-4o",
        max_tokens=50,
        temperature=0.3,
        frequency_penalty=0.4,
        api_version="2024-05-01",
    )


def test_get_llm_openai_branches():
    llm = LLM("k1", "k2", "k3")
    with patch("src.api.common.services.llm.ChatOpenAI") as mock_chat:
        # model_name especificado
        llm.get_llm(
            "openai",
            model_name="gpt-4-turbo",
            max_tokens=10,
            temperature=0.1,
            frequency_penalty=0.2,
            reasoning_effort="low",
        )
        mock_chat.assert_called()
        # model_name None
        llm.get_llm(
            "openai",
            model_name=None,
            max_tokens=10,
            temperature=0.1,
            frequency_penalty=0.2,
        )
        mock_chat.assert_called()


def test_get_llm_anthropic_branches():
    llm = LLM("k1", "k2", "k3")
    with patch("src.api.common.services.llm.ChatAnthropic") as mock_anthropic:
        llm.get_llm(
            "anthropic",
            model_name="claude-3-sonnet-20240229",
            max_tokens=10,
            temperature=0.1,
        )
        mock_anthropic.assert_called()
        llm.get_llm("anthropic", model_name=None, max_tokens=10, temperature=0.1)
        mock_anthropic.assert_called()


def test_get_llm_azure_branches():
    llm = LLM(
        "k1",
        "k2",
        azure_api_key="azk",
        azure_openai_endpoint="https://az",
        azure_api_version="2024-05-01",
    )
    with patch("src.api.common.services.llm.AzureChatOpenAI") as mock_azure:
        llm.get_llm(
            "azure-openai",
            model_name="gpt-4o",
            max_tokens=10,
            temperature=0.1,
            frequency_penalty=0.2,
        )
        mock_azure.assert_called()
        llm.get_llm(
            "azure-openai",
            model_name=None,
            max_tokens=10,
            temperature=0.1,
            frequency_penalty=0.2,
        )
        mock_azure.assert_called()
