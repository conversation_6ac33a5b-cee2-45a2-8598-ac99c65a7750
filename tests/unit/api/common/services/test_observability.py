from logging import Logger
from unittest.mock import MagicMock, patch

import pytest
from src.api.common.services.observability import PhoenixObservability


@pytest.fixture
def logger():
    return MagicMock(spec=Logger)


@pytest.fixture
def phoenix(logger):
    return PhoenixObservability(
        mode="local",
        logger=logger,
        collector_endpoint="http://localhost:4318",
        oltp_headers="foo=bar",
        port=6006,
        project_name="TestProject",
    )


def test_check_port_occupied_true(phoenix):
    with patch("socket.socket.connect_ex", return_value=0):
        assert phoenix._PhoenixObservability__check_port_occupied() is True


def test_check_port_occupied_false(phoenix):
    with patch("socket.socket.connect_ex", return_value=1):
        assert phoenix._PhoenixObservability__check_port_occupied() is False


def test_is_endpoint_reachable_success(phoenix, logger):
    with patch("requests.head", return_value=MagicMock(status_code=200)):
        assert (
            phoenix._PhoenixObservability__is_endpoint_reachable(
                "http://x", max_retries=2, delay=0
            )
            is True
        )
    logger.info.assert_any_call("Phoenix endpoint reachable after 1 attempts.")


def test_is_endpoint_reachable_fail(phoenix, logger):
    import requests

    with patch(
        "requests.head", side_effect=requests.exceptions.RequestException("fail")
    ):
        assert (
            phoenix._PhoenixObservability__is_endpoint_reachable(
                "http://x", max_retries=2, delay=0
            )
            is False
        )
    assert any(
        "Phoenix endpoint not reachable" in str(call[0][0])
        for call in logger.info.call_args_list
    )


def test_is_endpoint_reachable_not_200(phoenix, logger):
    with patch("requests.head", return_value=MagicMock(status_code=404)):
        assert (
            phoenix._PhoenixObservability__is_endpoint_reachable(
                "http://x", max_retries=1, delay=0
            )
            is False
        )


def test_instrument_collector_missing(phoenix, logger):
    phoenix._collector_endpoint = None
    phoenix._oltp_headers = "foo=bar"
    phoenix.instrument()
    assert any(
        "PHOENIX_COLLECTOR_ENDPOINT environment variable is not set" in str(call[0][0])
        for call in logger.info.call_args_list
    )


def test_instrument_headers_missing(phoenix, logger):
    phoenix._collector_endpoint = "http://localhost:4318"
    phoenix._oltp_headers = None
    phoenix.instrument()
    assert any(
        "OTLP headers not set" in str(call[0][0]) for call in logger.info.call_args_list
    )


def test_set_observability_local_port_occupied(phoenix, logger):
    with patch.object(
        phoenix, "_PhoenixObservability__check_port_occupied", return_value=True
    ), patch.object(phoenix, "instrument") as instrument:
        phoenix.set_observability_local()
        instrument.assert_called_once()
    logger.info.assert_any_call(
        "Port 6006 is already in use. Server is already started."
    )


def test_set_observability_local_port_not_occupied(phoenix, logger):
    with patch.object(
        phoenix, "_PhoenixObservability__check_port_occupied", return_value=False
    ), patch.object(phoenix, "instrument") as instrument:
        phoenix.set_observability_local()
        instrument.assert_called_once()


def test_set_observability_endpoint_reachable(phoenix, logger):
    with patch.object(
        phoenix, "_PhoenixObservability__is_endpoint_reachable", return_value=True
    ), patch.object(phoenix, "instrument") as instrument:
        phoenix.set_observability_endpoint()
        instrument.assert_called_once()


def test_set_observability_endpoint_not_reachable(phoenix, logger):
    with patch.object(
        phoenix, "_PhoenixObservability__is_endpoint_reachable", return_value=False
    ), patch.object(phoenix, "instrument") as instrument:
        phoenix.set_observability_endpoint()
        instrument.assert_not_called()
    assert any(
        "PHOENIX_COLLECTOR_ENDPOINT is not reachable" in str(call[0][0])
        for call in logger.info.call_args_list
    )


def test_set_observability_endpoint_no_collector(logger):
    p = PhoenixObservability(
        "server", logger, collector_endpoint=None, oltp_headers="foo=bar"
    )
    p.set_observability_endpoint()
    assert any(
        "PHOENIX_COLLECTOR_ENDPOINT environment variable is not set" in str(call[0][0])
        for call in logger.info.call_args_list
    )


def test_set_observability_modes(phoenix, logger):
    with patch.object(phoenix, "set_observability_endpoint") as endpoint, patch.object(
        phoenix, "set_observability_local"
    ) as local:
        phoenix._mode = "server"
        phoenix.set_observability()
        endpoint.assert_called_once()
        phoenix._mode = "local"
        phoenix.set_observability()
        local.assert_called_once()
