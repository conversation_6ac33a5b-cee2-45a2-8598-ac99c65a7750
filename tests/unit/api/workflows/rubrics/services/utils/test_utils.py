from src.api.workflows.rubrics.services.utils.utils import _clean_text, is_processable_file

def test_clean_text_removes_ctrl_chars():
    s = "abc\x00\x01\x02def"
    cleaned = _clean_text(s)
    assert "\x00" not in cleaned and "\x01" not in cleaned and "\x02" not in cleaned

def test_is_processable_file():
    assert is_processable_file("file.docx", [".docx"]) is True
    assert is_processable_file(".hidden.docx", [".docx"]) is False
    assert is_processable_file("file.txt", [".docx"]) is False
    assert is_processable_file("/etc/passwd", [".docx"]) is False
