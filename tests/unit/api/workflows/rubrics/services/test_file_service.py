from src.api.workflows.rubrics.services.file_service import FileService
import io
import pytest

@pytest.mark.asyncio
async def test_a_read_document_returns_content(monkeypatch):
    # Mock a_read_file_content para que no intente abrir un PDF real
    async def fake_a_read_file_content(file, page_range, convert_docx_to_pdf):
        return ("contenido", None)
    monkeypatch.setattr(
        "src.api.workflows.rubrics.services.utils.utils.a_read_file_content",
        fake_a_read_file_content,
    )
    # Mock a_read_pdf_file para evitar que read_pdf_file intente abrir un PDF real
    async def fake_a_read_pdf_file(file, page_range=None):
        return "contenido"
    monkeypatch.setattr(
        "src.api.workflows.rubrics.services.utils.utils.a_read_pdf_file",
        fake_a_read_pdf_file,
    )
    file_service = FileService()
    file = io.BytesIO(b"contenido")
    file.name = "test.pdf"
    result = await file_service.a_read_document(file)
    assert result == "contenido"

