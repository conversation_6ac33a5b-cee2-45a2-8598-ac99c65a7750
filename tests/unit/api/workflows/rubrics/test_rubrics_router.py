import json
import pytest
from fastapi.testclient import TestClient
from src.api.workflows.rubrics.rubrics_router import router
from fastapi import FastAPI

app = FastAPI()
app.include_router(router)

client = TestClient(app)


def test_extract_criteria_422_json_decode():
    # Simula un archivo válido
    files = {"document": ("doc.pdf", b"contenido")}
    # subject_type y language van como query params, previous_criteria como form
    params = {"subject_type": "tipo", "language": "es"}
    data = {"previous_criteria": "{mal json]"}
    response = client.post("/api/v1/rubrics/extract_criteria", params=params, data=data, files=files)
    assert response.status_code == 422
    assert "Error de formato en los criterios" in response.text


def test_generate_rubrics_422_json_decode():
    files = {"document": ("doc.pdf", b"contenido")}
    params = {"subject_type": "tipo", "language": "es"}
    data = {"criteria": "{mal json]"}
    response = client.post("/api/v1/rubrics/generate", params=params, data=data, files=files)
    assert response.status_code == 422
    assert "Error de formato en los criterios" in response.text
