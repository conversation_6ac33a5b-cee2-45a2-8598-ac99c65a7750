from src.api.workflows.rubrics.generate_rubrics.response import <PERSON><PERSON><PERSON><PERSON>eve<PERSON>, RubricCriterionDetail, GenerateRubricsResponse

def test_generate_rubrics_response():
    level = RubricLevel(
        excellent_description="excelente",
        good_description="bueno",
        average_description="medio",
        needs_to_improve_description="mejorar"
    )
    crit = RubricCriterionDetail(
        order=1,
        name="n",
        description="d",
        performance_levels=level,
        educational_guideline=["g1", "g2"]
    )
    resp = GenerateRubricsResponse(criteria=[crit])
    assert resp.criteria[0].performance_levels.excellent_description == "excelente"
    assert resp.criteria[0].educational_guideline == ["g1", "g2"]
