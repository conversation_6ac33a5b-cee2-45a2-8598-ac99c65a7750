import pytest
from unittest.mock import AsyncMock, MagicMock
from src.api.workflows.rubrics.generate_rubrics.workflow import GenerateRubricsWorkflow
from src.api.workflows.rubrics.generate_rubrics.request import GenerateRubricsRequest, Language
from src.api.workflows.rubrics.extract_criteria.response import ExtractCriteriaResponse, Criterion
from src.api.workflows.rubrics.generate_rubrics.response import GenerateRubricsResponse, RubricCriterionDetail, RubricLevel

@pytest.mark.asyncio
async def test_generate_rubrics_workflow_execute(monkeypatch):
    # Mock FileService
    mock_file_service = MagicMock()
    mock_file_service.a_read_document = AsyncMock(return_value=["contenido doc"])
    # Mock openai_client
    mock_openai_client = MagicMock()
    mock_openai_client.responses.parse = AsyncMock(return_value=MagicMock(output_parsed=GenerateRubricsResponse(criteria=[RubricCriterionDetail(order=1, name="n", description="d", performance_levels=RubricLevel(excellent_description="e", good_description="g", average_description="a", needs_to_improve_description="m"), educational_guideline=["g1"])])))
    # Mock bbdd_engine y prompts
    mock_bbdd_engine = MagicMock()
    monkeypatch.setattr(GenerateRubricsWorkflow, "_get_prompt_from_db", AsyncMock(return_value=("sys", "user")))
    # Instancia workflow
    logger = MagicMock()
    workflow = GenerateRubricsWorkflow(mock_openai_client, mock_bbdd_engine, logger)
    workflow.file_service = mock_file_service
    # Datos de entrada
    criterio = Criterion(order=1, name="n", description="d", justification="j")
    resp = ExtractCriteriaResponse(analysis="a", elements=[criterio])
    req = GenerateRubricsRequest(
        subject_type="tipo",
        language=Language.ES,
        document=b"contenido",
        document_filename="doc.pdf",
        syllabus_corpus=None,
        syllabus_corpus_filename=None,
        criteria=resp,
    )
    result = await workflow.execute(req)
    assert result.criteria[0].name == "n"
    assert result.criteria[0].performance_levels.excellent_description == "e"
