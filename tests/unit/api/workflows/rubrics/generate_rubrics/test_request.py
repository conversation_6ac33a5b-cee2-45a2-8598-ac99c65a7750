from src.api.workflows.rubrics.generate_rubrics.request import GenerateRubricsRequest, Language
from src.api.workflows.rubrics.extract_criteria.response import ExtractCriteriaResponse, Criterion

def test_generate_rubrics_request():
    criterio = Criterion(order=1, name="Nombre", description="Desc", justification="Justificación")
    resp = ExtractCriteriaResponse(analysis="Análisis", elements=[criterio])
    req = GenerateRubricsRequest(
        subject_type="tipo",
        language=Language.ES,
        document=b"contenido",
        document_filename="doc.pdf",
        syllabus_corpus=None,
        syllabus_corpus_filename=None,
        criteria=resp,
    )
    assert req.subject_type == "tipo"
    assert req.language == Language.ES
    assert req.criteria.analysis == "Análisis"
