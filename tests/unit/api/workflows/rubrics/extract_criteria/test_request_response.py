import pytest
from src.api.workflows.rubrics.extract_criteria.request import ExtractCriteriaRequest, Language
from src.api.workflows.rubrics.extract_criteria.response import ExtractCriteriaResponse, Criterion


def test_extract_criteria_request_and_response():
    # Crear un criterio de ejemplo
    criterio = Criterion(order=1, name="Nombre", description="Desc", justification="Justificación")
    # Crear respuesta
    resp = ExtractCriteriaResponse(analysis="Análisis", elements=[criterio])
    assert resp.analysis == "Análisis"
    assert len(resp.elements) == 1
    assert resp.elements[0].name == "Nombre"

    # Crear request
    req = ExtractCriteriaRequest(
        subject_type="tipo",
        language=Language.ES,
        document=b"contenido",
        document_filename="doc.pdf",
        syllabus_corpus=None,
        syllabus_corpus_filename=None,
        user_feedback=None,
        previous_criteria=resp,
    )
    assert req.subject_type == "tipo"
    assert req.language == Language.ES
    assert req.document == b"contenido"
    assert req.previous_criteria.analysis == "Análisis"
