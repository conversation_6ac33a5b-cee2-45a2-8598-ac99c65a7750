import pytest
from unittest.mock import AsyncMock, MagicMock
from src.api.workflows.rubrics.extract_criteria.workflow import ExtractCriteriaWorkflow
from src.api.workflows.rubrics.extract_criteria.request import ExtractCriteriaRequest, Language
from src.api.workflows.rubrics.extract_criteria.response import ExtractCriteriaResponse, Criterion

import asyncio

@pytest.mark.asyncio
async def test_extract_criteria_workflow_execute(monkeypatch):
    # Mock FileService
    mock_file_service = MagicMock()
    mock_file_service.a_read_document = AsyncMock(return_value=["contenido doc"])
    # Mock openai_client
    mock_openai_client = MagicMock()
    mock_openai_client.responses.parse = AsyncMock(return_value=MagicMock(output_parsed=ExtractCriteriaResponse(analysis="analisis", elements=[Criterion(order=1, name="n", description="d", justification="j")]) ))
    # Instancia workflow
    logger = MagicMock()
    workflow = ExtractCriteriaWorkflow(mock_openai_client, logger)
    workflow.file_service = mock_file_service
    # Datos de entrada
    req = ExtractCriteriaRequest(
        subject_type="tipo",
        language=Language.ES,
        document=b"contenido",
        document_filename="doc.pdf",
        syllabus_corpus=None,
        syllabus_corpus_filename=None,
        user_feedback=None,
        previous_criteria=None,
    )
    result = await workflow.execute(req)
    assert result.analysis == "analisis"
    assert result.elements[0].name == "n"
