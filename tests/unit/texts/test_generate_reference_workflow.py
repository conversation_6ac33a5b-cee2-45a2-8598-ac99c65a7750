import pytest
from unittest.mock import AsyncMock, MagicMock
from fastapi import <PERSON>TT<PERSON><PERSON>x<PERSON>
from src.api.workflows.texts.generate_reference.generate_reference_workflow import GenerateReferenceFromTextWorkflow
from src.api.workflows.texts.generate_reference.generate_reference_clases import GenerateReferenceRequest, GenerateReferenceResponse

@pytest.mark.asyncio
async def test_execute_no_authors(monkeypatch):
    mock_openai = MagicMock()
    mock_search_agent = MagicMock()
    mock_logger = MagicMock()
    workflow = GenerateReferenceFromTextWorkflow(mock_openai, mock_search_agent, mock_logger)

    doc = MagicMock()
    doc.date = "2024"
    doc.authors = []
    doc.title = "Título"
    doc.document_url = "http://url.com"
    doc.name = "NombreDoc"
    reason = "Razón"

    async def mock_extract_reference(text):
        return doc, reason
    monkeypatch.setattr(workflow, "extract_reference", mock_extract_reference)

    request = GenerateReferenceRequest(text="Texto de prueba")
    response = await workflow.execute(request)
    assert isinstance(response, GenerateReferenceResponse)
    assert response.final_reference == f"{doc.title} ({doc.date}). {doc.url}."
    assert response.inline_reference == f"{doc.title} ({doc.date})."
    assert response.reason == reason

@pytest.mark.asyncio
async def test_execute_with_authors(monkeypatch):
    mock_openai = MagicMock()
    mock_search_agent = MagicMock()
    mock_logger = MagicMock()
    workflow = GenerateReferenceFromTextWorkflow(mock_openai, mock_search_agent, mock_logger)

    doc = MagicMock()
    doc.date = "2024"
    doc.authors = ["Autor1", "Autor2"]
    doc.title = "Título"
    doc.url = "http://url.com"
    reason = "Razón"

    async def mock_extract_reference(text):
        return doc, reason
    monkeypatch.setattr(workflow, "extract_reference", mock_extract_reference)

    request = GenerateReferenceRequest(text="Texto de prueba")
    response = await workflow.execute(request)
    assert isinstance(response, GenerateReferenceResponse)
    assert response.final_reference == f"Autor1 & Autor2 ({doc.date}). {doc.title}. {doc.url}."
    assert response.inline_reference == f"(Autor1 & Autor2, {doc.date})"
    assert response.reason == reason

@pytest.mark.asyncio
async def test_execute_error(monkeypatch):
    mock_openai = MagicMock()
    mock_search_agent = MagicMock()
    mock_logger = MagicMock()
    workflow = GenerateReferenceFromTextWorkflow(mock_openai, mock_search_agent, mock_logger)

    async def mock_extract_reference(text):
        raise Exception("Error")
    monkeypatch.setattr(workflow, "extract_reference", mock_extract_reference)

    request = GenerateReferenceRequest(text="Texto de prueba")
    with pytest.raises(HTTPException):
        await workflow.execute(request)

@pytest.mark.asyncio
async def test_extract_reference_empty_text():
    mock_openai = MagicMock()
    mock_search_agent = MagicMock()
    mock_logger = MagicMock()
    workflow = GenerateReferenceFromTextWorkflow(mock_openai, mock_search_agent, mock_logger)
    with pytest.raises(HTTPException):
        await workflow.extract_reference("")

@pytest.mark.asyncio
async def test_extract_reference_success(monkeypatch):
    mock_openai = MagicMock()
    mock_search_agent = MagicMock()
    mock_logger = MagicMock()
    workflow = GenerateReferenceFromTextWorkflow(mock_openai, mock_search_agent, mock_logger)

    class DummyResponse:
        output_text = "Resumen"
    mock_openai.responses.create = AsyncMock(return_value=DummyResponse())

    class DummySearchResult:
        documents = [MagicMock()]
    # Simular que el método model_dump devuelve un dict serializable
    dummy_doc = DummySearchResult.documents[0]
    dummy_doc.model_dump = MagicMock(return_value={"key": "value"})
    mock_search_agent.run = AsyncMock(return_value=DummySearchResult())

    class DummyResponse2:
        output_text = "Justificación"
    async def dummy_create(*args, **kwargs):
        return DummyResponse2()
    monkeypatch.setattr(mock_openai.responses, "create", dummy_create)

    result, reason = await workflow.extract_reference("Texto")
    assert reason == "Justificación"
    assert result is not None

@pytest.mark.asyncio
async def test_extract_reference_no_documents(monkeypatch):
    mock_openai = MagicMock()
    mock_search_agent = MagicMock()
    mock_logger = MagicMock()
    workflow = GenerateReferenceFromTextWorkflow(mock_openai, mock_search_agent, mock_logger)

    class DummyResponse:
        output_text = "Resumen"
    mock_openai.responses.create = AsyncMock(return_value=DummyResponse())

    class DummySearchResult:
        documents = []
    mock_search_agent.run = AsyncMock(return_value=DummySearchResult())

    with pytest.raises(HTTPException):
        await workflow.extract_reference("Texto")
