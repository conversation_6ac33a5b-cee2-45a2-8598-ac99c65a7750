import pytest
from src.api.common.dependency_container import DependencyContainer


@pytest.fixture(scope="session")
def initialize_dependencies():
    """
    Initializes the DependencyContainer (and therefore all dependencies)
    once per test session.
    """
    from src.domain.models import FewShotExample, Prompt

    DependencyContainer.initialize(observability=False)
    yield
    session_factory = DependencyContainer.get_session_factory()
    with session_factory() as session:
        session.query(FewShotExample).delete()
        session.query(Prompt).delete()


@pytest.fixture
def create_order(initialize_dependencies):
    from src.domain.models import (
        Order,
        Plan,
        Subject,
        Title,
        TitleSubject,
    )

    session_factory = DependencyContainer.get_session_factory()
    with session_factory() as session:
        plan = Plan(year=2024)
        session.add(plan)
        session.commit()
        session.refresh(plan)

        title = Title(plan_id=plan.id, name="Ingeniería industrial")
        session.add(title)
        session.commit()
        session.refresh(title)

        subject = Subject(
            name="Organización industrial",
            descripcion="Organizacion industrial para estudiantes de ingeniería industrial, introduciendo conceptos empresariales.",
        )
        session.add(subject)
        session.commit()
        session.refresh(subject)

        title_subject = TitleSubject(
            name=subject.name + title.name, title_id=title.id, subject_id=subject.id
        )
        session.add(title_subject)
        session.commit()
        session.refresh(title_subject)

        order = Order(
            title_subject_id=title_subject.id,
            author_id="<EMAIL>",
            coordinator_id="<EMAIL>",
        )
        session.add(order)
        session.commit()
        session.refresh(order)

        yield order

        session.query(Order).filter(Order.id == order.id).delete()
        session.query(TitleSubject).filter(TitleSubject.id == title_subject.id).delete()
        session.query(Subject).filter(Subject.id == subject.id).delete()
        session.query(Title).filter(Title.id == title.id).delete()
        session.query(Plan).filter(Plan.id == plan.id).delete()
        session.commit()
