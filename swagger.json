{"openapi": "3.1.0", "info": {"title": "Creador de Contenidos app", "version": "0.2.0"}, "paths": {"/": {"get": {"tags": ["health-checks"], "summary": "Get Health Check", "operationId": "get_health_check__get", "parameters": [{"name": "x-api-key", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Api-Key"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/users": {"post": {"tags": ["Users"], "summary": "Create User", "operationId": "create_user_api_v1_users_post", "parameters": [{"name": "x-api-key", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Api-Key"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RequestUser"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/users/{id}": {"get": {"tags": ["Users"], "summary": "Get User", "operationId": "get_user_api_v1_users__id__get", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Id"}}, {"name": "x-api-key", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Api-Key"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Users"], "summary": "Delete User", "operationId": "delete_user_api_v1_users__id__delete", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Id"}}, {"name": "x-api-key", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Api-Key"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/subjects": {"post": {"tags": ["Subjects"], "summary": "Create Title Subject", "operationId": "create_title_subject_api_v1_subjects_post", "parameters": [{"name": "x-api-key", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Api-Key"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RequestTitleSubject"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/src__api__workflows__subjects__subjects_schemas__TitleSubjectResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["Subjects"], "summary": "Get Title Subjects", "description": "Get subjects", "operationId": "get_title_subjects_api_v1_subjects_get", "parameters": [{"name": "title_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "Filter by course ID", "title": "Title Id"}, "description": "Filter by course ID"}, {"name": "x-api-key", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Api-Key"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TitleSubjectList"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/subjects/{title_subject_id}": {"get": {"tags": ["Subjects"], "summary": "Get Title Subject", "operationId": "get_title_subject_api_v1_subjects__title_subject_id__get", "parameters": [{"name": "title_subject_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Title Subject Id"}}, {"name": "x-api-key", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Api-Key"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/src__api__workflows__subjects__subjects_schemas__TitleSubjectResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/subjects/{id}": {"delete": {"tags": ["Subjects"], "summary": "Delete Title Subject", "operationId": "delete_title_subject_api_v1_subjects__id__delete", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Id"}}, {"name": "x-api-key", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Api-Key"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/titles": {"post": {"tags": ["Titles"], "summary": "Create Title", "operationId": "create_title_api_v1_titles_post", "parameters": [{"name": "x-api-key", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Api-Key"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateTitleRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateTitleResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["Titles"], "summary": "Get Titles", "operationId": "get_titles_api_v1_titles_get", "parameters": [{"name": "x-api-key", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Api-Key"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CreateTitleResponse"}, "title": "Response Get Titles Api V1 Titles Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/titles/{id}": {"get": {"tags": ["Titles"], "summary": "Get Title", "operationId": "get_title_api_v1_titles__id__get", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Id"}}, {"name": "x-api-key", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Api-Key"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateTitleResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "patch": {"tags": ["Titles"], "summary": "Update Title", "operationId": "update_title_api_v1_titles__id__patch", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Id"}}, {"name": "x-api-key", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Api-Key"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateTitleRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateTitleResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Titles"], "summary": "Delete Title", "operationId": "delete_title_api_v1_titles__id__delete", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Id"}}, {"name": "x-api-key", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Api-Key"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/titles/generate_competencies": {"post": {"tags": ["Titles"], "summary": "Generate Competencies From Title", "operationId": "generate_competencies_from_title_api_v1_titles_generate_competencies_post", "parameters": [{"name": "x-api-key", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Api-Key"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenerateSubjectCompetenciesRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenerateSubjectCompetenciesResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/competencies/generate": {"post": {"tags": ["competencies"], "summary": "Generate Competencies", "operationId": "generate_competencies_api_v1_competencies_generate_post", "parameters": [{"name": "x-api-key", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Api-Key"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenerateCompetenciesRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenerateCompetenciesResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/competencies/regenerate": {"post": {"tags": ["competencies"], "summary": "Regenerate Competencies", "operationId": "regenerate_competencies_api_v1_competencies_regenerate_post", "parameters": [{"name": "x-api-key", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Api-Key"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegenerateCompetenciesRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegenerateCompetenciesResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/indexes/generate": {"post": {"tags": ["indexes"], "summary": "Generate Index", "operationId": "generate_index_api_v1_indexes_generate_post", "parameters": [{"name": "x-api-key", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Api-Key"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenerateIndexRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenerateIndexResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/indexes/{order_id}/regenerate": {"post": {"tags": ["indexes"], "summary": "Regenerate Index", "operationId": "regenerate_index_api_v1_indexes__order_id__regenerate_post", "parameters": [{"name": "order_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Order Id"}}, {"name": "x-api-key", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Api-Key"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegenerateIndexSchemaRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegenerateIndexSchemaResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/indexes/{order_id}/regenerate/block": {"post": {"tags": ["indexes"], "summary": "Regenerate Block Index", "operationId": "regenerate_block_index_api_v1_indexes__order_id__regenerate_block_post", "parameters": [{"name": "order_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Order Id"}}, {"name": "x-api-key", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Api-Key"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegenerateBlockSchemaRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegenerateBlockSchemaResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/indexes/{order_id}/regenerate/topic": {"post": {"tags": ["indexes"], "summary": "Regenerate Topic Index", "operationId": "regenerate_topic_index_api_v1_indexes__order_id__regenerate_topic_post", "parameters": [{"name": "order_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Order Id"}}, {"name": "x-api-key", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Api-Key"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegenerateTopicSchemaRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegenerateTopicSchemaResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/indexes": {"get": {"tags": ["indexes"], "summary": "Get All Indexes", "operationId": "get_all_indexes_api_v1_indexes_get", "parameters": [{"name": "title_subject_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Title Subject Id"}}, {"name": "is_displayed", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Displayed"}}, {"name": "limit", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Limit"}}, {"name": "desc", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Desc"}}, {"name": "version", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Version"}}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/IndiceStatus"}, {"type": "null"}], "title": "Status"}}, {"name": "x-api-key", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Api-Key"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/IndexResponse"}, "title": "Response Get All Indexes Api V1 Indexes Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/indexes/{index_id}": {"get": {"tags": ["indexes"], "summary": "Get Index", "operationId": "get_index_api_v1_indexes__index_id__get", "parameters": [{"name": "index_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Index Id"}}, {"name": "x-api-key", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Api-Key"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetIndexResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/indexes/generate_content": {"post": {"tags": ["indexes"], "summary": "Generate Content", "operationId": "generate_content_api_v1_indexes_generate_content_post", "parameters": [{"name": "x-api-key", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Api-Key"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenerateContentRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenerateContentResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/indexes/generate_failed_content": {"post": {"tags": ["indexes"], "summary": "Generate Failed Content", "operationId": "generate_failed_content_api_v1_indexes_generate_failed_content_post", "parameters": [{"name": "x-api-key", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Api-Key"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenerateFailedContentRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenerateFailedContentResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/topics/{index_id}": {"get": {"tags": ["topics"], "summary": "Get Topics By Index", "operationId": "get_topics_by_index_api_v1_topics__index_id__get", "parameters": [{"name": "index_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Index Id"}}, {"name": "x-api-key", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Api-Key"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GetTopicsByIndexResponse"}, "title": "Response Get Topics By Index Api V1 Topics  Index Id  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/topics/{tema_id}/status": {"get": {"tags": ["topics"], "summary": "Get Topic Status", "operationId": "get_topic_status_api_v1_topics__tema_id__status_get", "parameters": [{"name": "tema_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Tema Id"}}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/AIProcessStatus"}, {"type": "null"}], "title": "Status"}}, {"name": "process_type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/AIProcessType"}, {"type": "null"}], "title": "Process Type"}}, {"name": "id_epigrafe", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON><PERSON>"}}, {"name": "x-api-key", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Api-Key"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetTopicStatusResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/topics/{tema_id}/markdown": {"get": {"tags": ["topics"], "summary": "Get Content Markdown", "operationId": "get_content_markdown_api_v1_topics__tema_id__markdown_get", "parameters": [{"name": "tema_id", "in": "path", "required": true, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Tema Id"}}, {"name": "plan_version", "in": "query", "required": true, "schema": {"type": "integer", "title": "Plan Version"}}, {"name": "citations_at_end", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": true, "title": "Citations At End"}}, {"name": "epigrafe_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Epigrafe Id"}}, {"name": "x-api-key", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Api-Key"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetContentMarkdownResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/topics/didactic_instructions/{order_id}/generate_all": {"post": {"tags": ["topics"], "summary": "Generate All Didactic Instructions", "operationId": "generate_all_didactic_instructions_api_v1_topics_didactic_instructions__order_id__generate_all_post", "parameters": [{"name": "order_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Order Id"}}, {"name": "x-api-key", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Api-Key"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenerateAllDidacticInstructionsRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenerateAllDidacticInstructionsResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/topics/didactic_instructions/{tema_id}/regenerate": {"post": {"tags": ["topics"], "summary": "Regenerate Didactic Instructions", "operationId": "regenerate_didactic_instructions_api_v1_topics_didactic_instructions__tema_id__regenerate_post", "parameters": [{"name": "tema_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Tema Id"}}, {"name": "x-api-key", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Api-Key"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegenerateDidacticInstructionsRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegenerateDidacticInstructionsResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/topics/didactic_instructions/{tema_id}/epigraph/{epigrafe_id}/regenerate": {"post": {"tags": ["topics"], "summary": "Regenerate Didactic Instructions Epigraph", "operationId": "regenerate_didactic_instructions_epigraph_api_v1_topics_didactic_instructions__tema_id__epigraph__epigrafe_id__regenerate_post", "parameters": [{"name": "tema_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Tema Id"}}, {"name": "epigrafe_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Epigrafe Id"}}, {"name": "x-api-key", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Api-Key"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegenerateDidacticInstructionsRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegenerateDidacticInstructionsResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/topics/in_depth/{tema_id}/generate": {"post": {"tags": ["topics"], "summary": "Generate In Depth", "operationId": "generate_in_depth_api_v1_topics_in_depth__tema_id__generate_post", "parameters": [{"name": "tema_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Tema Id"}}, {"name": "x-api-key", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Api-Key"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenerateInDepthRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/topics/in_depth/{topic_id}": {"get": {"tags": ["topics"], "summary": "Get In Depth By Index", "operationId": "get_in_depth_by_index_api_v1_topics_in_depth__topic_id__get", "parameters": [{"name": "topic_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Topic Id"}}, {"name": "x-api-key", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Api-Key"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GetInDepthByIndexResponse"}, "title": "Response Get In Depth By Index Api V1 Topics In Depth  Topic Id  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/topics/tests/{topic_id}/generate": {"post": {"tags": ["topics"], "summary": "Generate Tests", "operationId": "generate_tests_api_v1_topics_tests__topic_id__generate_post", "parameters": [{"name": "topic_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Topic Id"}}, {"name": "x-api-key", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Api-Key"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenerateTestsRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenerateTestsResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/ai_processes/ratings/{ai_process_id}": {"post": {"tags": ["AI Proccesses"], "summary": "Rate Ai Process", "operationId": "rate_ai_process_api_v1_ai_processes_ratings__ai_process_id__post", "parameters": [{"name": "ai_process_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Ai Process Id"}}, {"name": "x-api-key", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Api-Key"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RateProcessRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["AI Proccesses"], "summary": "Get Rating By Id", "operationId": "get_rating_by_id_api_v1_ai_processes_ratings__ai_process_id__get", "parameters": [{"name": "ai_process_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Ai Process Id"}}, {"name": "x-api-key", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Api-Key"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetRatingResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/sections/{topic_id}": {"get": {"tags": ["sections"], "summary": "Get Sections By Topic", "operationId": "get_sections_by_topic_api_v1_sections__topic_id__get", "parameters": [{"name": "topic_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Topic Id"}}, {"name": "x-api-key", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Api-Key"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GetSectionsByTopicResponse"}, "title": "Response Get Sections By Topic Api V1 Sections  Topic Id  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/sections/{section_id}/paragraph": {"post": {"tags": ["sections"], "summary": "Add Paragraph", "operationId": "add_paragraph_api_v1_sections__section_id__paragraph_post", "parameters": [{"name": "section_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Section Id"}}, {"name": "x-api-key", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Api-Key"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddParagraphRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddParagraphResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/sections/{section_id}/regenerate-paragraph": {"post": {"tags": ["sections"], "summary": "Regenerate Paragraph", "operationId": "regenerate_paragraph_api_v1_sections__section_id__regenerate_paragraph_post", "parameters": [{"name": "section_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Section Id"}}, {"name": "x-api-key", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Api-Key"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegenerateParagraphRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegenerateParagraphResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/documents/search_references": {"post": {"tags": ["Documents"], "summary": "Search References", "description": "Search references for epigrafes", "operationId": "search_references_api_v1_documents_search_references_post", "parameters": [{"name": "x-api-key", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Api-Key"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchReferencesRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/texts/generate_reference": {"post": {"tags": ["Texts"], "summary": "Create User", "operationId": "create_user_api_v1_texts_generate_reference_post", "parameters": [{"name": "x-api-key", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Api-Key"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenerateReferenceRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenerateReferenceResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/rubrics/extract_criteria": {"post": {"tags": ["Rubrics"], "summary": "Extract Criteria", "description": "Extrae los **criterios** de evaluación de una asignatura a partir de un documento y un corpus de syllabus.\n\nCampos user feedback y criterios previos son opcionales. Si se proporcionan, se utilizarán para repetir la extraccion de criterios mejorando la previa.\nFormato esperado de user_feedback: String\nFormato esperado de criterios previos: JSON con la estructura de RubricCriteria.\n```json\n[\n    {\n    \"order\": 0,\n    \"name\": \"string\",\n    \"description\": \"string\",\n    \"justification\": \"string\"\n    }\n]\n```", "operationId": "extract_criteria_api_v1_rubrics_extract_criteria_post", "parameters": [{"name": "subject_type", "in": "query", "required": true, "schema": {"type": "string", "description": "Tipo de la asignatura", "title": "Subject Type"}, "description": "Tipo de la asignatura"}, {"name": "language", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/Language", "description": "Idioma de la rúbrica", "default": "es"}, "description": "Idioma de la rúbrica"}, {"name": "x-api-key", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Api-Key"}}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_extract_criteria_api_v1_rubrics_extract_criteria_post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExtractCriteriaResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/rubrics/generate": {"post": {"tags": ["Rubrics"], "summary": "Generate Rubrics", "description": "Genera rúbricas basadas en los **criterios** extraídos.\n\n---\n### Formato esperado de `criteria`\n\n```json\n{\n  \"analysis\": \"string\",\n  \"elements\": [\n    {\n      \"order\": 0,\n      \"name\": \"string\",\n      \"description\": \"string\",\n      \"justification\": \"string\"\n    }\n  ]\n}\n```", "operationId": "generate_rubrics_api_v1_rubrics_generate_post", "parameters": [{"name": "subject_type", "in": "query", "required": true, "schema": {"type": "string", "description": "Tipo de la asignatura", "title": "Subject Type"}, "description": "Tipo de la asignatura"}, {"name": "language", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/Language", "description": "Idioma de la rúbrica", "default": "es"}, "description": "Idioma de la rúbrica"}, {"name": "x-api-key", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Api-Key"}}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_generate_rubrics_api_v1_rubrics_generate_post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenerateRubricsResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/activities/generate": {"post": {"tags": ["Activities"], "summary": "Generate Activities", "operationId": "generate_activities_api_v1_activities_generate_post", "parameters": [{"name": "individual_activities", "in": "query", "required": true, "schema": {"type": "integer", "description": "Cantidad de actividades individuales", "title": "Individual Activities"}, "description": "Cantidad de actividades individuales"}, {"name": "group_activities", "in": "query", "required": true, "schema": {"type": "integer", "description": "Cantidad de actividades grupales", "title": "Group Activities"}, "description": "Cantidad de actividades grupales"}, {"name": "include_ai", "in": "query", "required": true, "schema": {"type": "boolean", "description": "Incluir el uso de IA en las actividades", "title": "Include Ai"}, "description": "Incluir el uso de IA en las actividades"}, {"name": "x-api-key", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Api-Key"}}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_generate_activities_api_v1_activities_generate_post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActivityDesignerResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"AIProcessInfo": {"properties": {"ai_process_type": {"type": "string", "title": "Ai Process Type"}, "ai_process_id": {"type": "integer", "title": "Ai Process Id"}}, "type": "object", "required": ["ai_process_type", "ai_process_id"], "title": "AIProcessInfo"}, "AIProcessStatus": {"type": "string", "enum": ["PENDING", "IN_PROGRESS", "COMPLETED", "FAILED"], "title": "AIProcessStatus"}, "AIProcessType": {"type": "string", "enum": ["SEARCH_SOURCES", "SCHEMA_GENERATION", "SCHEMA_REGENERATION", "SCHEMA_BLOCK_REGENERATION", "SCHEMA_TOPIC_REGENERATION", "COMPETENCIE_REGENERATION", "DIDACTIC_INSTRUCTIONS", "DIDACTIC_INSTRUCTIONS_REGENERATION", "CONTENT_PLAN_WITH_INSTRUCTIONS", "CONTENT_PLAN_WITHOUT_INSTRUCTIONS", "CONTENT_GENERATION_WITH_SOURCES", "CONTENT_GENERATION_WITHOUT_SOURCES", "RETRIEVE_CONTENT_SOURCES", "COMPETENCIE_FROM_SCHEMA", "COMPETENCIE_WITHOUT_SCHEMA", "SCHEMA_GENERATION_FROM_COMPETENCIES", "EXTRACT_SUBJECT_DESCRIPTIONS", "COMPETENCIES_FROM_TITLE_DESCRIPTOR", "GENERATE_CONTENT_FOR_TOPIC", "ADD_PARAGRAPH", "REGENERATE_PARAGRAPH", "GENERATE_IN_DEPTH", "GENERATE_TESTS"], "title": "AIProcessType"}, "ActivityDesignerResponse": {"properties": {"markdown": {"type": "string", "title": "<PERSON><PERSON>"}}, "type": "object", "required": ["markdown"], "title": "ActivityDesignerResponse"}, "AddParagraphRequest": {"properties": {"section_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Section Id"}, "prev_position": {"type": "integer", "title": "Prev Position", "description": "Position of the previous paragraph from 0 to n. 0 if there is not a previous paragraph."}, "tool": {"$ref": "#/components/schemas/ContentPlanType", "default": "TEXTO"}, "user_comment": {"type": "string", "title": "User Comment"}, "window_size": {"type": "integer", "title": "Window Size", "description": "How many previous and next items to take contiguous to the current paragraph", "default": 3}, "store": {"type": "boolean", "title": "Store", "default": true}}, "type": "object", "required": ["prev_position", "user_comment"], "title": "AddParagraphRequest"}, "AddParagraphResponse": {"properties": {"content": {"type": "string", "title": "Content"}, "plan_item_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Plan Item Id"}, "metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "related_chunks": {"items": {"type": "string"}, "type": "array", "title": "Related Chunks", "default": []}, "related_docs": {"items": {"type": "string"}, "type": "array", "title": "Related Docs", "default": []}}, "type": "object", "required": ["content"], "title": "AddParagraphResponse"}, "Asignatura": {"properties": {"nombre": {"type": "string", "title": "Nombre"}, "estructura": {"$ref": "#/components/schemas/Estructura"}}, "type": "object", "required": ["nombre", "estructura"], "title": "Asignatura"}, "BloqueTematico": {"properties": {"nombre": {"type": "string", "title": "Nombre"}, "temas": {"items": {"$ref": "#/components/schemas/Tema"}, "type": "array", "title": "<PERSON><PERSON>"}}, "type": "object", "required": ["nombre", "temas"], "title": "BloqueTematico"}, "Body_extract_criteria_api_v1_rubrics_extract_criteria_post": {"properties": {"document": {"type": "string", "format": "binary", "title": "Document", "description": "Archivo principal"}, "syllabus_corpus": {"anyOf": [{"type": "string", "format": "binary"}, {"type": "null"}], "title": "Syllabus Corpus", "description": "Archivo de corpus de syllabus"}, "user_feedback": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "User <PERSON>", "description": "Feedback del usuario (texto plano)"}, "previous_criteria": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Previous Criteria", "description": "Criterios previos en formato JSON"}}, "type": "object", "required": ["document"], "title": "Body_extract_criteria_api_v1_rubrics_extract_criteria_post"}, "Body_generate_activities_api_v1_activities_generate_post": {"properties": {"subject_corpus": {"type": "string", "format": "binary", "title": "Subject Corpus", "description": "<PERSON><PERSON><PERSON> completo o temas de la asignatura sobre los que se basarán las actividades (PDF/DOCX)"}, "activity_briefing": {"anyOf": [{"type": "string", "format": "binary"}, {"type": "null"}], "title": "Activity Briefing", "description": "Caso o briefing para las actividades"}, "subject_competencies": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Subject Competencies", "description": "Competencias de la Asignatura (opcional)"}}, "type": "object", "required": ["subject_corpus"], "title": "Body_generate_activities_api_v1_activities_generate_post"}, "Body_generate_rubrics_api_v1_rubrics_generate_post": {"properties": {"document": {"type": "string", "format": "binary", "title": "Document", "description": "Archivo principal"}, "syllabus_corpus": {"anyOf": [{"type": "string", "format": "binary"}, {"type": "null"}], "title": "Syllabus Corpus", "description": "Archivo de corpus de syllabus"}, "criteria": {"type": "string", "title": "Criteria", "description": "Criterios del ejercicio en formato JSON"}}, "type": "object", "required": ["document", "criteria"], "title": "Body_generate_rubrics_api_v1_rubrics_generate_post"}, "Competencia": {"properties": {"descripcion": {"type": "string", "title": "Descripcion"}}, "type": "object", "required": ["descripcion"], "title": "Competencia", "description": "Concepto academico que proporciona utilidad en el ambito laboral"}, "CompetenciesResponse": {"properties": {"id": {"type": "integer", "title": "Id"}, "description": {"type": "string", "title": "Description"}}, "type": "object", "required": ["id", "description"], "title": "CompetenciesResponse"}, "ContentPlanType": {"type": "string", "enum": ["TEXTO", "GRAFICA", "IMAGEN", "TABLA", "HUMANO"], "title": "ContentPlanType"}, "CreateTitleRequest": {"properties": {"name": {"type": "string", "title": "Name"}, "type": {"anyOf": [{"$ref": "#/components/schemas/TitleType"}, {"type": "null"}]}, "description": {"type": "string", "title": "Description"}}, "type": "object", "required": ["name", "description"], "title": "CreateTitleRequest"}, "CreateTitleResponse": {"properties": {"name": {"type": "string", "title": "Name"}, "type": {"anyOf": [{"$ref": "#/components/schemas/TitleType"}, {"type": "null"}]}, "description": {"type": "string", "title": "Description"}, "id": {"type": "integer", "title": "Id"}, "plan_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Plan Id"}}, "type": "object", "required": ["name", "description", "id"], "title": "CreateTitleResponse"}, "Criterion": {"properties": {"order": {"type": "integer", "title": "Order"}, "name": {"type": "string", "title": "Name"}, "description": {"type": "string", "title": "Description"}, "justification": {"type": "string", "title": "Justification"}}, "type": "object", "required": ["order", "name", "description", "justification"], "title": "Criterion"}, "DidacticInstructionRegenerateItem": {"properties": {"id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Id"}, "position": {"type": "integer", "title": "Position"}, "name": {"type": "string", "title": "Name"}, "didactic_instructions": {"type": "string", "title": "Didactic Instructions"}, "plan_version": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Plan Version"}}, "type": "object", "required": ["position", "name", "didactic_instructions"], "title": "DidacticInstructionRegenerateItem"}, "DidacticInstructionsAllItem": {"properties": {"id": {"type": "integer", "title": "Id"}, "name": {"type": "string", "title": "Name"}, "position": {"type": "integer", "title": "Position"}, "didactic_instructions": {"type": "string", "title": "Didactic Instructions"}, "version": {"type": "integer", "title": "Version"}}, "type": "object", "required": ["id", "name", "position", "didactic_instructions", "version"], "title": "DidacticInstructionsAllItem"}, "Estructura": {"properties": {"bloques_tematicos": {"items": {"$ref": "#/components/schemas/BloqueTematico"}, "type": "array", "title": "<PERSON><PERSON><PERSON>"}}, "type": "object", "required": ["bloques_tematicos"], "title": "Estructura"}, "ExtractCriteriaResponse": {"properties": {"analysis": {"type": "string", "title": "Analysis"}, "elements": {"items": {"$ref": "#/components/schemas/Criterion"}, "type": "array", "title": "Elements"}}, "type": "object", "required": ["analysis", "elements"], "title": "ExtractCriteriaResponse"}, "GenerateAllDidacticInstructionsRequest": {"properties": {"order_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Order Id"}, "didactic_instructions": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Didactic Instructions", "default": "\nLa estructura general de un epígrafe ha de ser:\n\nComenzará por una introducción breve, seguida de la elaboración de los conceptos clave sobre los cuales trata el epígrafe en una forma narrativa.\nTratando de introducirlos de manera progresiva y cohesiva, evitando una excesiva segmentación de conceptos y conectando el contexto de los epígrafes anteriores a lo largo de la narración.\n\nEn cuanto a el orden de estos dentro de un tema:\n\nConsiderarás los otros epígrafes del tema para lo que será tratado en cada uno individualmente, evitando solapamientos.\n\nIntroducirás ejemplos y ejercicios prácticos cuando sea necesario a nivel global. Por tema introducirás entre 1 y 3 en alguno de los epígrafes. Considerás cuando es adecuado para evitar una repetición excesiva y ser moderado.\n\nConsiderarás una longitud aproximada de unas 2-5 páginas por epígrafe, introduciendo las instrucciones adecuadas para que no sea ni mucho más ni mucho menos.\n\nCombinarás cuando sea necesario elementos adicionales como tablas o gráficas, pero considerarás no abusar de ellas, teniendo en cuenta también incluirlas cuando sea adecuado dentro del contexto de todos los temas.\n"}, "plan_version": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Plan Version", "default": 1}, "allow_increase_version": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Allow Increase Version", "default": false}, "model_info": {"anyOf": [{"$ref": "#/components/schemas/ModelInfo"}, {"type": "null"}]}}, "type": "object", "title": "GenerateAllDidacticInstructionsRequest"}, "GenerateAllDidacticInstructionsResponse": {"properties": {"didactic_instructions": {"items": {"$ref": "#/components/schemas/DidacticInstructionsAllItem"}, "type": "array", "title": "Didactic Instructions"}, "processes_info": {"items": {"$ref": "#/components/schemas/AIProcessInfo"}, "type": "array", "title": "Processes Info"}}, "type": "object", "required": ["didactic_instructions", "processes_info"], "title": "GenerateAllDidacticInstructionsResponse"}, "GenerateCompetenciesRequest": {"properties": {"order_id": {"type": "integer", "title": "Order Id"}, "definicion_competencia": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Definicion Competencia", "default": "Habilidad adquirida en el contexto académico de una asignatura, que es aplicable y útil en el ámbito laboral."}, "descripcion": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Descripcion"}, "model_info": {"anyOf": [{"$ref": "#/components/schemas/ModelInfo"}, {"type": "null"}]}}, "type": "object", "required": ["order_id"], "title": "GenerateCompetenciesRequest"}, "GenerateCompetenciesResponse": {"properties": {"competencies": {"items": {"$ref": "#/components/schemas/src__api__workflows__competencies__generate__generate_competencies_schemas__Competencie"}, "type": "array", "title": "Competencies"}, "processes_info": {"items": {"$ref": "#/components/schemas/AIProcessInfo"}, "type": "array", "title": "Processes Info"}}, "type": "object", "required": ["competencies", "processes_info"], "title": "GenerateCompetenciesResponse"}, "GenerateContentRequest": {"properties": {"indice_id": {"type": "integer", "title": "Indice Id"}, "tema_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Tema Id"}, "plan_version": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Plan Version", "default": 1}, "model_info": {"anyOf": [{"$ref": "#/components/schemas/ModelInfo"}, {"type": "null"}]}}, "type": "object", "required": ["indice_id"], "title": "GenerateContentRequest"}, "GenerateContentResponse": {"properties": {"ai_processes": {"items": {"$ref": "#/components/schemas/src__api__workflows__indexes__generate_content__generate_content_schemas__TemaAIProcess"}, "type": "array", "title": "Ai Processes"}}, "type": "object", "required": ["ai_processes"], "title": "GenerateContentResponse"}, "GenerateFailedContentRequest": {"properties": {"indice_id": {"type": "integer", "title": "Indice Id"}, "tema_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Tema Id"}, "plan_version": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Plan Version"}, "model_info": {"anyOf": [{"$ref": "#/components/schemas/ModelInfo"}, {"type": "null"}]}}, "type": "object", "required": ["indice_id"], "title": "GenerateFailedContentRequest"}, "GenerateFailedContentResponse": {"properties": {"ai_processes": {"items": {"$ref": "#/components/schemas/src__api__workflows__indexes__generate_failed_content__generate_failed_content_schemas__TemaAIProcess"}, "type": "array", "title": "Ai Processes"}}, "type": "object", "required": ["ai_processes"], "title": "GenerateFailedContentResponse"}, "GenerateInDepthRequest": {"properties": {"num_references": {"type": "integer", "title": "Num References", "default": 3}}, "type": "object", "title": "GenerateInDepthRequest"}, "GenerateIndexRequest": {"properties": {"order_id": {"type": "integer", "title": "Order Id"}, "descripcion": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Descripcion"}, "numero_bloques": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Numero Bloques", "default": 3}, "numero_temas": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Numero Temas", "default": 10}, "numero_epigrafes": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Numero Epigrafes", "default": 4}, "model_info": {"anyOf": [{"$ref": "#/components/schemas/ModelInfo"}, {"type": "null"}]}}, "type": "object", "required": ["order_id"], "title": "GenerateIndexRequest"}, "GenerateIndexResponse": {"properties": {"index": {"$ref": "#/components/schemas/Asignatura"}, "competencies": {"items": {"$ref": "#/components/schemas/Competencia"}, "type": "array", "title": "Competencies"}, "indice_id": {"type": "integer", "title": "Indice Id"}, "processes_info": {"items": {"$ref": "#/components/schemas/AIProcessInfo"}, "type": "array", "title": "Processes Info"}}, "type": "object", "required": ["index", "competencies", "indice_id", "processes_info"], "title": "GenerateIndexResponse"}, "GenerateReferenceRequest": {"properties": {"text": {"type": "string", "title": "Text"}}, "type": "object", "required": ["text"], "title": "GenerateReferenceRequest"}, "GenerateReferenceResponse": {"properties": {"reason": {"type": "string", "title": "Reason"}, "inline_reference": {"type": "string", "title": "Inline Reference"}, "final_reference": {"type": "string", "title": "Final Reference"}}, "type": "object", "required": ["reason", "inline_reference", "final_reference"], "title": "GenerateReferenceResponse"}, "GenerateRubricsResponse": {"properties": {"criteria": {"items": {"$ref": "#/components/schemas/RubricCriterionDetail"}, "type": "array", "title": "Criteria"}}, "type": "object", "required": ["criteria"], "title": "GenerateRubricsResponse"}, "GenerateSubjectCompetenciesRequest": {"properties": {"title_id": {"type": "integer", "title": "Title Id"}, "year_number": {"type": "integer", "title": "Year Number"}, "term_number": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Term Number"}, "documento": {"type": "string", "title": "Documento"}, "model_info": {"anyOf": [{"$ref": "#/components/schemas/ModelInfo"}, {"type": "null"}]}}, "type": "object", "required": ["title_id", "year_number", "documento"], "title": "GenerateSubjectCompetenciesRequest"}, "GenerateSubjectCompetenciesResponse": {"properties": {"title_subjects": {"items": {"anyOf": [{"$ref": "#/components/schemas/src__api__workflows__titles__generate__subject_competencies_schemas__TitleSubjectResponse"}, {}]}, "type": "array", "title": "Title Subjects", "default": []}, "processes_info": {"anyOf": [{"items": {"$ref": "#/components/schemas/AIProcessInfo"}, "type": "array"}, {"type": "null"}], "title": "Processes Info"}}, "type": "object", "title": "GenerateSubjectCompetenciesResponse"}, "GenerateTestsRequest": {"properties": {"topic_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Topic Id"}, "num_questions": {"type": "integer", "title": "Num Questions", "default": 10}}, "type": "object", "title": "GenerateTestsRequest"}, "GenerateTestsResponse": {"properties": {"question_ids": {"items": {"type": "integer"}, "type": "array", "title": "Question Ids"}}, "type": "object", "required": ["question_ids"], "title": "GenerateTestsResponse"}, "GetContentMarkdownResponse": {"properties": {"markdown_content": {"type": "string", "title": "Markdown Content"}}, "type": "object", "required": ["markdown_content"], "title": "GetContentMarkdownResponse"}, "GetInDepthByIndexResponse": {"properties": {"id": {"type": "integer", "title": "Id"}, "title": {"type": "string", "title": "Title"}, "cite": {"type": "string", "title": "Cite"}, "url": {"type": "string", "title": "Url"}, "justification": {"type": "string", "title": "Justification"}}, "type": "object", "required": ["id", "title", "cite", "url", "justification"], "title": "GetInDepthByIndexResponse"}, "GetIndexResponse": {"properties": {"index": {"$ref": "#/components/schemas/Asignatura"}, "competencies": {"items": {"$ref": "#/components/schemas/Competencia"}, "type": "array", "title": "Competencies"}, "status": {"$ref": "#/components/schemas/IndiceStatus"}}, "type": "object", "required": ["index", "competencies", "status"], "title": "GetIndexResponse"}, "GetRatingResponse": {"properties": {"rating": {"type": "integer", "title": "Rating"}, "comment": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Comment"}, "evaluator_name": {"type": "string", "title": "Evaluator Name"}}, "type": "object", "required": ["rating", "comment", "evaluator_name"], "title": "GetRatingResponse"}, "GetSectionsByTopicResponse": {"properties": {"id": {"type": "integer", "title": "Id"}, "name": {"type": "string", "title": "Name"}, "position": {"type": "integer", "title": "Position"}, "id_tema": {"type": "integer", "title": "<PERSON><PERSON>"}}, "type": "object", "required": ["id", "name", "position", "id_tema"], "title": "GetSectionsByTopicResponse"}, "GetTopicStatusItem": {"properties": {"process_type": {"$ref": "#/components/schemas/AIProcessType"}, "status": {"$ref": "#/components/schemas/AIProcessStatus"}, "completed_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Completed At"}, "additional_metadata": {"additionalProperties": true, "type": "object", "title": "Additional Metadata", "default": {}}}, "type": "object", "required": ["process_type", "status"], "title": "GetTopicStatusItem"}, "GetTopicStatusResponse": {"properties": {"items": {"items": {"$ref": "#/components/schemas/GetTopicStatusItem"}, "type": "array", "title": "Items"}}, "type": "object", "required": ["items"], "title": "GetTopicStatusResponse"}, "GetTopicsByIndexResponse": {"properties": {"id": {"type": "integer", "title": "Id"}, "name": {"type": "string", "title": "Name"}, "position": {"type": "integer", "title": "Position"}, "status": {"$ref": "#/components/schemas/TopicStatus"}, "id_bloque": {"type": "integer", "title": "Id Blo<PERSON>"}}, "type": "object", "required": ["id", "name", "position", "status", "id_bloque"], "title": "GetTopicsByIndexResponse"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "IndexResponse": {"properties": {"id": {"type": "integer", "title": "Id"}, "order_id": {"type": "integer", "title": "Order Id"}, "name": {"type": "string", "title": "Name"}, "version": {"type": "integer", "title": "Version"}, "updated_at": {"title": "Updated At"}, "is_displayed": {"type": "boolean", "title": "Is Displayed"}, "status": {"$ref": "#/components/schemas/IndiceStatus"}}, "type": "object", "required": ["id", "order_id", "name", "version", "updated_at", "is_displayed", "status"], "title": "IndexResponse"}, "IndiceStatus": {"type": "string", "enum": ["NOT_STARTED", "NON_CONFIRMED_COMPETENCIES", "CONFIRMED_COMPETENCIES", "INDICE_GENERATION", "INDICE_REVIEW_PENDING", "INSTRUCTIONS_GENERATION", "INSTRUCTIONS_REVIEW_PENDING", "SEARCH", "CONTENT_GENERATION", "CONTENT_REVIEW_PENDING", "IN_DEPTH_GENERATION", "IN_DEPTH_REVIEW_PENDING", "TEST_GENERATION", "TEST_REVIEW_PENDING", "COMPLETED", "CANCELED"], "title": "IndiceStatus"}, "Language": {"type": "string", "enum": ["en", "es"], "title": "Language"}, "ModelInfo": {"properties": {"name": {"type": "string", "title": "Name"}, "provider": {"type": "string", "title": "Provider"}}, "type": "object", "required": ["name", "provider"], "title": "ModelInfo"}, "RateProcessRequest": {"properties": {"rating": {"type": "integer", "maximum": 5.0, "minimum": 1.0, "title": "Rating", "description": "Rating for the process (1-5)"}, "comment": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Comment", "description": "Optional comment for the rating"}, "evaluator_name": {"type": "string", "title": "Evaluator Name", "description": "Name of the evaluator"}}, "type": "object", "required": ["rating"], "title": "RateProcessRequest"}, "RegenerateBlockSchemaRequest": {"properties": {"order_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Order Id"}, "bloque_id": {"type": "integer", "title": "Bloque Id"}, "comentario": {"type": "string", "title": "Comentario"}, "model_info": {"anyOf": [{"$ref": "#/components/schemas/ModelInfo"}, {"type": "null"}]}}, "type": "object", "required": ["bloque_id", "comentario"], "title": "RegenerateBlockSchemaRequest"}, "RegenerateBlockSchemaResponse": {"properties": {"bloque": {"$ref": "#/components/schemas/BloqueTematico"}, "processes_info": {"items": {"$ref": "#/components/schemas/AIProcessInfo"}, "type": "array", "title": "Processes Info"}}, "type": "object", "required": ["bloque", "processes_info"], "title": "RegenerateBlockSchemaResponse"}, "RegenerateCompetenciesRequest": {"properties": {"order_id": {"type": "integer", "title": "Order Id"}, "comentario": {"type": "string", "title": "Comentario"}, "model_info": {"anyOf": [{"$ref": "#/components/schemas/ModelInfo"}, {"type": "null"}]}}, "type": "object", "required": ["order_id", "comentario"], "title": "RegenerateCompetenciesRequest"}, "RegenerateCompetenciesResponse": {"properties": {"competencies": {"items": {"$ref": "#/components/schemas/src__api__workflows__competencies__competencies_schemas__Competencie"}, "type": "array", "title": "Competencies"}, "processes_info": {"items": {"$ref": "#/components/schemas/AIProcessInfo"}, "type": "array", "title": "Processes Info"}}, "type": "object", "required": ["competencies", "processes_info"], "title": "RegenerateCompetenciesResponse"}, "RegenerateDidacticInstructionsRequest": {"properties": {"topic_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Topic Id"}, "epigraph_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Epigraph Id"}, "comment": {"type": "string", "title": "Comment"}, "model_info": {"anyOf": [{"$ref": "#/components/schemas/ModelInfo"}, {"type": "null"}]}, "plan_version": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Plan Version"}}, "type": "object", "required": ["comment"], "title": "RegenerateDidacticInstructionsRequest"}, "RegenerateDidacticInstructionsResponse": {"properties": {"didactic_instructions": {"items": {"$ref": "#/components/schemas/DidacticInstructionRegenerateItem"}, "type": "array", "title": "Didactic Instructions"}, "processes_info": {"items": {"$ref": "#/components/schemas/AIProcessInfo"}, "type": "array", "title": "Processes Info"}, "topic_id": {"type": "integer", "title": "Topic Id"}}, "type": "object", "required": ["didactic_instructions", "processes_info", "topic_id"], "title": "RegenerateDidacticInstructionsResponse"}, "RegenerateIndexSchemaRequest": {"properties": {"order_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Order Id"}, "comentario": {"type": "string", "title": "Comentario"}, "model_info": {"anyOf": [{"$ref": "#/components/schemas/ModelInfo"}, {"type": "null"}]}}, "type": "object", "required": ["comentario"], "title": "RegenerateIndexSchemaRequest"}, "RegenerateIndexSchemaResponse": {"properties": {"index": {"$ref": "#/components/schemas/Asignatura"}, "processes_info": {"items": {"$ref": "#/components/schemas/AIProcessInfo"}, "type": "array", "title": "Processes Info"}}, "type": "object", "required": ["index", "processes_info"], "title": "RegenerateIndexSchemaResponse"}, "RegenerateParagraphRequest": {"properties": {"section_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Section Id"}, "position": {"type": "integer", "title": "Position"}, "user_comment": {"type": "string", "title": "User Comment"}, "window_size": {"type": "integer", "title": "Window Size", "default": 3}}, "type": "object", "required": ["position", "user_comment"], "title": "RegenerateParagraphRequest"}, "RegenerateParagraphResponse": {"properties": {"content": {"type": "string", "title": "Content"}, "metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "related_chunks": {"items": {"type": "string"}, "type": "array", "title": "Related Chunks", "default": []}, "related_docs": {"items": {"type": "string"}, "type": "array", "title": "Related Docs", "default": []}}, "type": "object", "required": ["content"], "title": "RegenerateParagraphResponse"}, "RegenerateTopicSchemaRequest": {"properties": {"order_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Order Id"}, "tema_id": {"type": "integer", "title": "Tema Id"}, "comentario": {"type": "string", "title": "Comentario"}, "model_info": {"anyOf": [{"$ref": "#/components/schemas/ModelInfo"}, {"type": "null"}]}}, "type": "object", "required": ["tema_id", "comentario"], "title": "RegenerateTopicSchemaRequest"}, "RegenerateTopicSchemaResponse": {"properties": {"tema": {"$ref": "#/components/schemas/Tema"}, "processes_info": {"items": {"$ref": "#/components/schemas/AIProcessInfo"}, "type": "array", "title": "Processes Info"}}, "type": "object", "required": ["tema", "processes_info"], "title": "RegenerateTopicSchemaResponse"}, "RequestTitleSubject": {"properties": {"name": {"type": "string", "title": "Name"}, "description": {"type": "string", "title": "Description"}, "author_id": {"type": "string", "title": "Author Id", "default": "<EMAIL>"}, "coordinator_id": {"type": "string", "title": "Coordinator Id", "default": "<EMAIL>"}, "title_id": {"type": "integer", "title": "Title Id"}, "year_number": {"type": "integer", "title": "Year Number"}, "term_number": {"type": "integer", "title": "Term Number"}, "credits": {"type": "integer", "title": "Credits", "default": 60}}, "type": "object", "required": ["name", "description", "title_id", "year_number", "term_number"], "title": "RequestTitleSubject"}, "RequestUser": {"properties": {"name": {"type": "string", "title": "Name"}}, "type": "object", "required": ["name"], "title": "RequestUser"}, "RubricCriterionDetail": {"properties": {"order": {"type": "integer", "title": "Order"}, "name": {"type": "string", "title": "Name"}, "description": {"type": "string", "title": "Description"}, "performance_levels": {"$ref": "#/components/schemas/RubricLevel"}, "educational_guideline": {"items": {"type": "string"}, "type": "array", "title": "Educational Guideline"}}, "type": "object", "required": ["order", "name", "description", "performance_levels", "educational_guideline"], "title": "RubricCriterionDetail"}, "RubricLevel": {"properties": {"excellent_description": {"type": "string", "title": "Excellent Description"}, "good_description": {"type": "string", "title": "Good Description"}, "average_description": {"type": "string", "title": "Average Description"}, "needs_to_improve_description": {"type": "string", "title": "Needs To Improve Description"}}, "type": "object", "required": ["excellent_description", "good_description", "average_description", "needs_to_improve_description"], "title": "RubricLevel"}, "SearchReferencesRequest": {"properties": {"indice_id": {"type": "integer", "title": "Indice Id"}}, "type": "object", "required": ["indice_id"], "title": "SearchReferencesRequest"}, "Tema": {"properties": {"nombre": {"type": "string", "title": "Nombre"}, "epigrafes": {"items": {"type": "string"}, "type": "array", "title": "Epigrafes"}}, "type": "object", "required": ["nombre", "epigrafes"], "title": "<PERSON><PERSON>"}, "TitleSubjectList": {"properties": {"title_subjects": {"items": {"$ref": "#/components/schemas/src__api__workflows__subjects__subjects_schemas__TitleSubjectResponse"}, "type": "array", "title": "Title Subjects"}}, "type": "object", "required": ["title_subjects"], "title": "TitleSubjectList"}, "TitleType": {"type": "string", "enum": ["GRADO", "MASTER"], "title": "TitleType"}, "TopicStatus": {"type": "string", "enum": ["NOT_STARTED", "CONTENT_IN_PROGRESS", "CONTENT_GENERATION", "CONTENT_REVIEW_PENDING", "CONTENT_FAILED", "IN_DEPTH_IN_PROGRESS", "IN_DEPTH_GENERATION", "IN_DEPTH_REVIEW_PENDING", "IN_DEPTH_FAILED", "TEST_IN_PROGRESS", "TEST_GENERATION", "TEST_REVIEW_PENDING", "TEST_FAILED", "COMPLETED", "CANCELED"], "title": "TopicStatus"}, "UpdateTitleRequest": {"properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "type": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Type"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "owner": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Owner"}}, "type": "object", "title": "UpdateTitleRequest"}, "UserResponse": {"properties": {"id": {"type": "integer", "title": "Id"}, "name": {"type": "string", "title": "Name"}}, "type": "object", "required": ["id", "name"], "title": "UserResponse"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}, "src__api__workflows__competencies__competencies_schemas__Competencie": {"properties": {"description": {"type": "string", "title": "Description"}}, "type": "object", "required": ["description"], "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "src__api__workflows__competencies__generate__generate_competencies_schemas__Competencie": {"properties": {"id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Id"}, "description": {"type": "string", "title": "Description"}}, "type": "object", "required": ["description"], "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "src__api__workflows__indexes__generate_content__generate_content_schemas__TemaAIProcess": {"properties": {"ai_process_type": {"type": "string", "title": "Ai Process Type"}, "ai_process_id": {"type": "integer", "title": "Ai Process Id"}, "tema_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Tema Id"}}, "type": "object", "required": ["ai_process_type", "ai_process_id"], "title": "TemaAIProcess"}, "src__api__workflows__indexes__generate_failed_content__generate_failed_content_schemas__TemaAIProcess": {"properties": {"ai_process_type": {"type": "string", "title": "Ai Process Type"}, "ai_process_id": {"type": "integer", "title": "Ai Process Id"}, "tema_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Tema Id"}, "plan_version": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Plan Version"}}, "type": "object", "required": ["ai_process_type", "ai_process_id"], "title": "TemaAIProcess"}, "src__api__workflows__subjects__subjects_schemas__TitleSubjectResponse": {"properties": {"id": {"type": "integer", "title": "Id"}, "name": {"type": "string", "title": "Name"}, "description": {"type": "string", "title": "Description"}, "term_number": {"type": "integer", "title": "Term Number"}, "year_number": {"type": "integer", "title": "Year Number"}, "subject_id": {"type": "integer", "title": "Subject Id"}, "order_id": {"type": "integer", "title": "Order Id"}, "title_id": {"type": "integer", "title": "Title Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}}, "type": "object", "required": ["id", "name", "description", "term_number", "year_number", "subject_id", "order_id", "title_id", "created_at"], "title": "TitleSubjectResponse"}, "src__api__workflows__titles__generate__subject_competencies_schemas__TitleSubjectResponse": {"properties": {"id": {"type": "integer", "title": "Id"}, "name": {"type": "string", "title": "Name"}, "competencies": {"items": {"$ref": "#/components/schemas/CompetenciesResponse"}, "type": "array", "title": "Competencies"}}, "type": "object", "required": ["id", "name", "competencies"], "title": "TitleSubjectResponse"}}}}