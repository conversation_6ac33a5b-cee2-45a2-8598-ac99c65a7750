#!/usr/bin/env bash
source "$DO_SCRIPTS_PATH/functions.sh"

print "Processing Dockerfile ..."

execute_script "$DO_SCRIPTS_PATH/aws-ecr-login.sh $DO_AWS_PROFILE" && {
  # agrega la plataforma correspondiente (si estamos en MacOS)
  [ "$(uname -s)" = "Darwin" ] && PLATFORM="--platform linux/arm64/v8" || PLATFORM=""
  if [[ -n "$PLATFORM" ]]; then
    print "<purple>platform</purple>: <yellow>$PLATFORM</yellow>"
  fi

  # Ejecuta el docker build de su tipo de aplicacion con sus parametros
  execute_script "$DO_APPTYPE_PATH/docker-build.sh '$PLATFORM'"
}