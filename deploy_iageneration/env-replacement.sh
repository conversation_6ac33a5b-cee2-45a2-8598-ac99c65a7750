#!/usr/bin/env bash
source "$DO_SCRIPTS_PATH/functions.sh"

if [[ -n "$DO_FILE_ENV" ]]; then
  if [[ "$(is_valid_file_path "$DO_FILE_ENV")" == "true" ]]; then
    RESTORE=$1
    FILE=$(echo $DO_FILE_ENV | sed 's/.{env}//g') #obtenemos la ruta sin la cadena {env}
    FILE_ENV=$(echo $DO_FILE_ENV | sed "s/{env}/${DO_ENTORNO}/g") #obtenemos ruta del environment correspondiente al entorno

    if [[ "$RESTORE" = "restore" ]]; then
      print "applying environment replacement: restoring <yellow>$FILE_ENV</yellow> and <yellow>$FILE</yellow>"
      mv "$FILE.tmp" $FILE
      mv "$FILE_ENV.tmp" $FILE_ENV
    else
      print "applying environment replacement: backup <yellow>$FILE_ENV</yellow> and <yellow>$FILE</yellow>"
      cp $FILE "$FILE.tmp"
      cp $FILE_ENV "$FILE_ENV.tmp"

      print "applying environment replacement: renaming <yellow>$FILE_ENV</yellow> to <yellow>$FILE</yellow>"

      rm $FILE
      mv $FILE_ENV $FILE

      if [ "$DO_VERBOSE" == "true" ]; then
        print_file_content "$FILE" "$FILE file content: "
      fi
    fi
  else
    print "<red>Error: File </red> <yellow>$DO_FILE_ENV</yellow> <red>not found</red>"
    exit 1 #ERROR
  fi
else
  print "<yellow>INFO:</yellow> no file to apply environment replacement."
fi

