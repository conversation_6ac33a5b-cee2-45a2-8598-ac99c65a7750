#!/usr/bin/env bash

source "$DO_SCRIPTS_PATH/functions.sh"

print "<green>Begin --> CREATING PULL REQUEST</green>" 

chmod +x $DO_APPTYPE_PATH/get-version.sh

print "Setting pr based on file-version:<yellow>$DO_FILE_VERSION</yellow>"

# Return the complete version number as x.y.z
C_VERSION=`$DO_APPTYPE_PATH/get-version.sh`
print "Actual version: <yellow>$C_VERSION</yellow>"

# Split the value into parts separated by dot
IFS='.' read -r -a version_parts <<< "$C_VERSION"

# Get individual parts of the version
VERSION_MAJOR="${version_parts[0]}"
VERSION_MINOR="${version_parts[1]}"
VERSION_PATCH="0"

# Increment the minor number by 1
NEW_VERSION_MINOR=$((VERSION_MINOR + 1))

# Return the complete version
NEW_VERSION="$VERSION_MAJOR.$NEW_VERSION_MINOR.$VERSION_PATCH"
print "New version:<yellow>$NEW_VERSION</yellow>"

# Edit AssemblyFileVersion & AssemblyVersion
execute_script "$DO_APPTYPE_PATH/set-version.sh $NEW_VERSION 1"
	
# Get the latest version of the develop branch
TARGET_BRANCH="develop"
git checkout $TARGET_BRANCH
git pull origin $TARGET_BRANCH

# Create a new branch (e.g., version-update-1.1.0)
NEW_BRANCH="version-update-$NEW_VERSION"
print "New branch:<yellow>$NEW_BRANCH</yellow>"
git checkout -b $NEW_BRANCH

# Commit the changes
git status 
git add $DO_FILE_VERSION
git commit -m "Update version to $NEW_VERSION"

# Push the new branch to the repository
git push origin $NEW_BRANCH

#install azure-devops extension 
az extension add --name azure-devops

#Azure DevOps login
echo  $DO_PAT_TOKEN | az devops login --organization https://dev.azure.com/unirnet/

print "Project name:<yellow>$DO_DPTO-$DO_APPNAME</yellow>"

#Create Pull Request
az repos pr create --repository $DO_DPTO-$DO_APPNAME --source-branch $NEW_BRANCH --title "Update version to $NEW_VERSION" --description "This PR updates the project verson to $NEW_VERSION."

print "<green>End --> CREATING PULL REQUEST</green>" 