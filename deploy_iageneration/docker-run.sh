#!/usr/bin/env bash
source "$DO_SCRIPTS_PATH/functions.sh"

export MSYS_NO_PATHCONV=1

print "Stop container <yellow>$DO_IMAGE_NAME</yellow> (if it's running)"
docker stop $DO_IMAGE_NAME > /dev/null 2>&1 || true

print "Remove container <yellow>$DO_IMAGE_NAME</yellow> (if exists)"
docker rm $DO_IMAGE_NAME > /dev/null 2>&1 || true

print "docker run: <yellow>$DO_IMAGE_NAME</yellow> on <purple>port</purple>: <yellow>$DO_HOST_PORT</yellow> (<purple>ENTORNO</purple>: <yellow>$DO_ENTORNO</yellow>)..."

# Define un array para los argumentos del comando docker run (ya que deben ir en un orden concreto y algunos parametros son opcionales)
args=()

args+=("-it")
args+=("--name" "$DO_IMAGE_NAME")
args+=("-p" "$DO_HOST_PORT:80")
args+=("-e" "ENTORNO=$DO_ENTORNO")

# Añade la conexión DB si es necesario
if [ -n "$DO_HOST" ]; then
    DB_CONNECTION="Server=${DO_HOST};Database=${DO_DB_NAME};User Id=${DO_DB_USER};Password=${DO_DB_USER_PWD};Integrated Security=false;Trusted_Connection=false;TrustServerCertificate=true"
    args+=("-e" "DBConnectionString=$DB_CONNECTION")
fi

args+=("$DO_IMAGE_NAME:$DO_IMAGE_VERSION")

# imprime log info
print "<yellow>docker run ${args[*]}</yellow>"

# Ejecuta el comando docker run con los argumentos
docker run "${args[@]}"


#REMOTO: -e DBConnectionString="Data Source=ovd-ha.unir.net;Initial Catalog=${DO_DB_NAME};User ID=${DO_DB_USER};Password=${DO_DB_USER_PWD}; MultipleActiveResultSets=True;Connection Timeout=600" \
