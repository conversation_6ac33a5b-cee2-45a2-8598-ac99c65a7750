#!/usr/bin/env bash
# Login into AWS ECR to allow pull/push images
# usage: ./aws-ecr-login.sh <user>
#
# use: `arquitectura.team.admin` in the Architecture project
# use: `teamcity` in Teamcity
source "$DO_SCRIPTS_PATH/functions.sh"

export AWS_PROFILE=${1:-arquitectura.team.admin}
print "Login in AWS with profile: <yellow>${AWS_PROFILE}</yellow>"

aws ecr get-login-password --profile $AWS_PROFILE --region eu-west-1 | \
    docker login \
    --username AWS \
    --password-stdin \
    760557048868.dkr.ecr.eu-west-1.amazonaws.com
