FROM ghcr.io/astral-sh/uv:0.5.25-python3.12-bookworm-slim AS builder

ENV UV_KEYRING_PROVIDER=disabled
ENV UV_INDEX_UNIR_USERNAME=VssSessionToken
ENV UV_COMPILE_BYTECODE=1
ENV UV_NO_CACHE=1

ARG UV_INDEX_UNIR_PASSWORD
ENV UV_INDEX_UNIR_PASSWORD=${UV_INDEX_UNIR_PASSWORD}

RUN groupadd -r appgrp && useradd -r -g appgrp -u 10001 appuser

WORKDIR /app
    
RUN apt-get update && apt-get --yes install gcc

COPY pyproject.toml uv.lock ./
RUN --mount=type=cache,target=/root/.cache/uv \
    uv sync --no-dev --frozen --no-install-project

COPY src src

RUN --mount=type=cache,target=/root/.cache/uv \
    uv sync --group api --no-dev --frozen

FROM python:3.12-slim-bookworm

RUN groupadd -r appgrp && useradd -r -g appgrp -u 10001 appuser

ENV COMMON__ENVIRONMENT=""
    
WORKDIR /app
COPY --from=builder /app .

RUN mkdir -p tmp && chown appuser:appgrp tmp
    
EXPOSE 8000

USER appuser

CMD [".venv/bin/poe", "start-api"]