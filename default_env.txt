# General Settings
export CommonEnvironment=Local
export LoggingLevel=INFO
export Workers=1

# OpenAI and AI Services
export OpenaiApiKey=""
export AnthropicApiKey=""
export AzureOpenaiEndpoint=""
export AzureOpenaiApiKey=""
export GenAiApiUrl="" #if local http://localhost:8000
export FastapiApiKey=""

# Database Settings
#Previous db
# export DbPort=5432
export DbUser=""
export DbName=""
export DbPassword=""
export DbHost=""
#New dev db
# export DbUser=""
# export DbHost=""
# export DbPassword=""
# export DbName=""
# export DbPort=5432

#If local
# export DbPort=5436
# export DbUser=""
# export DbName=""
# export DbPassword=""
# export DbHost=localhost

# Queue and Messaging
export ServiceBusConnectionString=""
export ContentGenerationQueueName=""
export ProcessDocumentQueueName=""
export GenerateContentTaskNConcurrent=1

# Storage
export AzureStorageConnectionString=""
export CooldownAzureStorageConnectionString=""
export ScanContainerName=""

# Re-Ranking Services
export RerankAzureApiKey=""
export RerankAzureName=""
export RerankAzureUrl=""

# Search Engines
export SearchEngine=brave
export BraveSearchApiKey=""
export TavilySearchApiKey=""

# Phoenix Settings and observability
export PhoenixProjectName=""
export PhoenixCollectorEndpoint=""
export PhoenixPort=6006
export OtelExporterOtlpHeaders=""
export ApplicationinsightsConnectionString=""
# Azure Key Vault
export AzureKeyVaultUrl=""
