[project]
name = "ia-gestorcontenidosiagen-be"
version = "0.2.0"
description = "Proyecto gestor de contenidos"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "aiohttp>=3.11.16",
    "asyncpg>=0.30.0",
    "azure-core>=1.33.0",
    "azure-identity>=1.21.0",
    "azure-keyvault-secrets>=4.9.0",
    "azure-monitor-opentelemetry>=1.6.5",
    "azure-monitor-opentelemetry-exporter>=1.0.0b35",
    "azure-servicebus>=7.14.1",
    "azure-storage-blob>=12.25.1",
    "fastapi[standard]>=0.115.14",
    "httpx>=0.28.1",
    "ia-gen-core==0.0.16",
    "langchain>=0.3.26",
    "langchain-anthropic>=0.3.17",
    "cohere==5.13.1",
    "langchain-cohere==0.4.4",
    "langchain-core==0.3.69",
    "langchain-openai==0.3.28",
    "markdown>=3.7",
    "openai==1.93.0",
    "openai-agents>=0.1.0",
    "openinference-instrumentation-langchain==0.1.46",
    "openinference-instrumentation-openai==0.1.30",
    "opentelemetry-api>=1.35.0",
    "opentelemetry-exporter-otlp>=1.35.0",
    "opentelemetry-instrumentation>=0.56b0",
    "opentelemetry-sdk>=1.35.0",
    "pgvector>=0.4.0",
    "poethepoet>=0.33.1",
    "psycopg2-binary>=2.9.10",
    "pydantic-core>=2.33.1",
    "pydantic-settings>=2.8.1",
    "pydantic[email]>=2.11.2",
    "sqlalchemy[asyncio]>=2.0.40",
    "sqlmodel>=0.0.24",
    "tavily-python>=0.5.4",
    "tiktoken>=0.9.0",
    "aiosqlite>=0.21.0",
    "openinference-instrumentation-openai-agents>=0.1.14",
    "openai==1.93.0",
    "aiofiles>=24.1.0",
]
 
[[tool.uv.index]]
name = "unir"
url = "https://<EMAIL>/unirnet/_packaging/Proeduca/pypi/simple/"

[tool.uv]
required-version = ">=0.5.25"
keyring-provider = "subprocess"
 
[tool.ruff]
src = ["src"]
exclude = ["**/*.ipynb", "notebooks", "tests"]
 
[tool.ruff.lint]
select = ["F", "E", "W", "N", "I"]
ignore = ["E501"]
 
[tool.pytest.ini_options]
pythonpath = [".", "src", "tests"]
asyncio_mode = "auto"
addopts = "--cov-branch"
asyncio_default_fixture_loop_scope = "function"
 
[tool.mypy]
files = ["src"]
warn_unused_configs = true
check_untyped_defs = true
ignore_missing_imports = true
explicit_package_bases = true
warn_redundant_casts = true
warn_unreachable = true
warn_unused_ignores = true
disallow_untyped_defs = false
disallow_incomplete_defs = false
 
[[tool.mypy.overrides]]
module = "sqlmodel.*"
ignore_errors = true
 
[[tool.mypy.overrides]]
module = "sqlalchemy.*"
ignore_errors = true
 
[[tool.mypy.overrides]]
module = "src.api.*"
disable_error_code = ["attr-defined", "union-attr", "arg-type", "return-value"]
 
[[tool.mypy.overrides]]
module = "src.background_tasks.*"
disable_error_code = ["attr-defined", "union-attr", "arg-type", "return-value"]
 
[dependency-groups]
api = [
    "gunicorn>=23.0.0",
    "tavily-python>=0.5.4",
    "pymupdf>=1.25.5",
    "spacy>=3.8.5",
    "tqdm>=4.67.1",
    "uvicorn>=0.35.0",
    "numpy>=2.3.1",
    "openpyxl>=3.1.5",
    "xlrd>=2.0.2",
    "python-docx>=1.2.0",
    "ftfy>=6.3.1",
    "Pillow>=11.3.0",
    "python-pptx>=1.0.2",
]
dev = [
    "azure-keyvault-secrets>=4.9.0",
    "black>=25.1.0",
    "instructor>=1.8.3",
    "ipykernel>=6.29.5",
    "matplotlib>=3.10.3",
    "mypy>=1.15.0",
    "pandas>=2.3.1",
    "pytest>=8.3.5",
    "pytest-asyncio>=0.26.0",
    "pytest-cov>=6.1.1",
    "python-dotenv>=1.1.0",
    "ruff>=0.11.3",
    "scikit-learn>=1.7.0",
    "seaborn>=0.13.2",
    "langchain-community>=0.3.27",
    "pydantic-evals[logfire]>=0.6.2",
]
doc-ingestion = [
    "azure-keyvault-secrets>=4.9.0",
    "pymupdf>=1.25.5",
    "spacy>=3.8.5",
    "tqdm>=4.67.1",
]
 
[tool.poe.tasks]
start-api-local = "uvicorn src.main:app --host 0.0.0.0 --port 8000 --reload --log-level 'info'"
start-background = "python -m src.background_tasks.background_tasks"
start-api.shell = "workers=$(nproc) && gunicorn src.main:app --bind 0.0.0.0:8000 --workers $workers --worker-class uvicorn.workers.UvicornWorker --log-level info --timeout 0"
start-tests = "uv run --frozen -- pytest --cov src --cov-append --cov-report xml tests/unit"
