{"cells": [{"cell_type": "markdown", "id": "eaa6a4fc", "metadata": {}, "source": ["## Code set up"]}, {"cell_type": "code", "execution_count": 1, "id": "f1f5ce31", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-31 13:33:19,686 - logger - ERROR - Error creating extensions: (psycopg2.OperationalError) could not translate host name \"db\" to address: nodename nor servname provided, or not known\n", "\n", "(Background on this error at: https://sqlalche.me/e/20/e3q8)\n"]}, {"ename": "OperationalError", "evalue": "(psycopg2.OperationalError) could not translate host name \"db\" to address: nodename nor servname provided, or not known\n\n(Background on this error at: https://sqlalche.me/e/20/e3q8)", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mOperationalError\u001b[39m                          <PERSON><PERSON> (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/ia_gestorcontenidosiagen_be/.venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py:145\u001b[39m, in \u001b[36mConnection.__init__\u001b[39m\u001b[34m(self, engine, connection, _has_events, _allow_revalidate, _allow_autobegin)\u001b[39m\n\u001b[32m    144\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m145\u001b[39m     \u001b[38;5;28mself\u001b[39m._dbapi_connection = \u001b[43mengine\u001b[49m\u001b[43m.\u001b[49m\u001b[43mraw_connection\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    146\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m dialect.loaded_dbapi.Error \u001b[38;5;28;01mas\u001b[39;00m err:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/ia_gestorcontenidosiagen_be/.venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py:3297\u001b[39m, in \u001b[36mEngine.raw_connection\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m   3276\u001b[39m \u001b[38;5;250m\u001b[39m\u001b[33;03m\"\"\"Return a \"raw\" DBAPI connection from the connection pool.\u001b[39;00m\n\u001b[32m   3277\u001b[39m \n\u001b[32m   3278\u001b[39m \u001b[33;03mThe returned object is a proxied version of the DBAPI\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m   3295\u001b[39m \n\u001b[32m   3296\u001b[39m \u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m-> \u001b[39m\u001b[32m3297\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mpool\u001b[49m\u001b[43m.\u001b[49m\u001b[43mconnect\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/ia_gestorcontenidosiagen_be/.venv/lib/python3.12/site-packages/sqlalchemy/pool/base.py:449\u001b[39m, in \u001b[36mPool.connect\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    442\u001b[39m \u001b[38;5;250m\u001b[39m\u001b[33;03m\"\"\"Return a DBAPI connection from the pool.\u001b[39;00m\n\u001b[32m    443\u001b[39m \n\u001b[32m    444\u001b[39m \u001b[33;03mThe connection is instrumented such that when its\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m    447\u001b[39m \n\u001b[32m    448\u001b[39m \u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m449\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_ConnectionFairy\u001b[49m\u001b[43m.\u001b[49m\u001b[43m_checkout\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/ia_gestorcontenidosiagen_be/.venv/lib/python3.12/site-packages/sqlalchemy/pool/base.py:1264\u001b[39m, in \u001b[36m_ConnectionFairy._checkout\u001b[39m\u001b[34m(cls, pool, threadconns, fairy)\u001b[39m\n\u001b[32m   1263\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m fairy:\n\u001b[32m-> \u001b[39m\u001b[32m1264\u001b[39m     fairy = \u001b[43m_ConnectionRecord\u001b[49m\u001b[43m.\u001b[49m\u001b[43mcheckout\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpool\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1266\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m threadconns \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/ia_gestorcontenidosiagen_be/.venv/lib/python3.12/site-packages/sqlalchemy/pool/base.py:713\u001b[39m, in \u001b[36m_ConnectionRecord.checkout\u001b[39m\u001b[34m(cls, pool)\u001b[39m\n\u001b[32m    712\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m713\u001b[39m     rec = \u001b[43mpool\u001b[49m\u001b[43m.\u001b[49m\u001b[43m_do_get\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    715\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/ia_gestorcontenidosiagen_be/.venv/lib/python3.12/site-packages/sqlalchemy/pool/impl.py:179\u001b[39m, in \u001b[36mQueuePool._do_get\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    178\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m179\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01mwith\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mutil\u001b[49m\u001b[43m.\u001b[49m\u001b[43msafe_reraise\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m:\u001b[49m\n\u001b[32m    180\u001b[39m \u001b[43m        \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_dec_overflow\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/ia_gestorcontenidosiagen_be/.venv/lib/python3.12/site-packages/sqlalchemy/util/langhelpers.py:224\u001b[39m, in \u001b[36msafe_reraise.__exit__\u001b[39m\u001b[34m(self, type_, value, traceback)\u001b[39m\n\u001b[32m    223\u001b[39m     \u001b[38;5;28mself\u001b[39m._exc_info = \u001b[38;5;28;01mNone\u001b[39;00m  \u001b[38;5;66;03m# remove potential circular references\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m224\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m exc_value.with_traceback(exc_tb)\n\u001b[32m    225\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/ia_gestorcontenidosiagen_be/.venv/lib/python3.12/site-packages/sqlalchemy/pool/impl.py:177\u001b[39m, in \u001b[36mQueuePool._do_get\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    176\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m177\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_create_connection\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    178\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/ia_gestorcontenidosiagen_be/.venv/lib/python3.12/site-packages/sqlalchemy/pool/base.py:390\u001b[39m, in \u001b[36mPool._create_connection\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    388\u001b[39m \u001b[38;5;250m\u001b[39m\u001b[33;03m\"\"\"Called by subclasses to create a new ConnectionRecord.\"\"\"\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m390\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_ConnectionRecord\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/ia_gestorcontenidosiagen_be/.venv/lib/python3.12/site-packages/sqlalchemy/pool/base.py:675\u001b[39m, in \u001b[36m_ConnectionRecord.__init__\u001b[39m\u001b[34m(self, pool, connect)\u001b[39m\n\u001b[32m    674\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m connect:\n\u001b[32m--> \u001b[39m\u001b[32m675\u001b[39m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m__connect\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    676\u001b[39m \u001b[38;5;28mself\u001b[39m.finalize_callback = deque()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/ia_gestorcontenidosiagen_be/.venv/lib/python3.12/site-packages/sqlalchemy/pool/base.py:901\u001b[39m, in \u001b[36m_ConnectionRecord.__connect\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    900\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mBaseException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[32m--> \u001b[39m\u001b[32m901\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01mwith\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mutil\u001b[49m\u001b[43m.\u001b[49m\u001b[43msafe_reraise\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m:\u001b[49m\n\u001b[32m    902\u001b[39m \u001b[43m        \u001b[49m\u001b[43mpool\u001b[49m\u001b[43m.\u001b[49m\u001b[43mlogger\u001b[49m\u001b[43m.\u001b[49m\u001b[43mdebug\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mError on connect(): \u001b[39;49m\u001b[38;5;132;43;01m%s\u001b[39;49;00m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43me\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/ia_gestorcontenidosiagen_be/.venv/lib/python3.12/site-packages/sqlalchemy/util/langhelpers.py:224\u001b[39m, in \u001b[36msafe_reraise.__exit__\u001b[39m\u001b[34m(self, type_, value, traceback)\u001b[39m\n\u001b[32m    223\u001b[39m     \u001b[38;5;28mself\u001b[39m._exc_info = \u001b[38;5;28;01mNone\u001b[39;00m  \u001b[38;5;66;03m# remove potential circular references\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m224\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m exc_value.with_traceback(exc_tb)\n\u001b[32m    225\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/ia_gestorcontenidosiagen_be/.venv/lib/python3.12/site-packages/sqlalchemy/pool/base.py:897\u001b[39m, in \u001b[36m_ConnectionRecord.__connect\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    896\u001b[39m \u001b[38;5;28mself\u001b[39m.starttime = time.time()\n\u001b[32m--> \u001b[39m\u001b[32m897\u001b[39m \u001b[38;5;28mself\u001b[39m.dbapi_connection = connection = \u001b[43mpool\u001b[49m\u001b[43m.\u001b[49m\u001b[43m_invoke_creator\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m    898\u001b[39m pool.logger.debug(\u001b[33m\"\u001b[39m\u001b[33mCreated new connection \u001b[39m\u001b[38;5;132;01m%r\u001b[39;00m\u001b[33m\"\u001b[39m, connection)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/ia_gestorcontenidosiagen_be/.venv/lib/python3.12/site-packages/sqlalchemy/engine/create.py:646\u001b[39m, in \u001b[36mcreate_engine.<locals>.connect\u001b[39m\u001b[34m(connection_record)\u001b[39m\n\u001b[32m    644\u001b[39m             \u001b[38;5;28;01mreturn\u001b[39;00m connection\n\u001b[32m--> \u001b[39m\u001b[32m646\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mdialect\u001b[49m\u001b[43m.\u001b[49m\u001b[43mconnect\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43mcargs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mcparams\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/ia_gestorcontenidosiagen_be/.venv/lib/python3.12/site-packages/sqlalchemy/engine/default.py:625\u001b[39m, in \u001b[36mDefaultDialect.connect\u001b[39m\u001b[34m(self, *cargs, **cparams)\u001b[39m\n\u001b[32m    623\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mconnect\u001b[39m(\u001b[38;5;28mself\u001b[39m, *cargs: Any, **cparams: Any) -> DBAPIConnection:\n\u001b[32m    624\u001b[39m     \u001b[38;5;66;03m# inherits the docstring from interfaces.Dialect.connect\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m625\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mloaded_dbapi\u001b[49m\u001b[43m.\u001b[49m\u001b[43mconnect\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43mcargs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mcparams\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/ia_gestorcontenidosiagen_be/.venv/lib/python3.12/site-packages/psycopg2/__init__.py:122\u001b[39m, in \u001b[36mconnect\u001b[39m\u001b[34m(dsn, connection_factory, cursor_factory, **kwargs)\u001b[39m\n\u001b[32m    121\u001b[39m dsn = _ext.make_dsn(dsn, **kwargs)\n\u001b[32m--> \u001b[39m\u001b[32m122\u001b[39m conn = \u001b[43m_connect\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdsn\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mconnection_factory\u001b[49m\u001b[43m=\u001b[49m\u001b[43mconnection_factory\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwasync\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    123\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m cursor_factory \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "\u001b[31mOperationalError\u001b[39m: could not translate host name \"db\" to address: nodename nor servname provided, or not known\n", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[31mOperationalError\u001b[39m                          <PERSON><PERSON> (most recent call last)", "\u001b[36mC<PERSON>\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[1]\u001b[39m\u001b[32m, line 8\u001b[39m\n\u001b[32m      6\u001b[39m sys.path.append(\u001b[38;5;28mstr\u001b[39m(parent_dir))\n\u001b[32m      7\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mapi\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mcommon\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mdependency_container\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m DependencyContainer\n\u001b[32m----> \u001b[39m\u001b[32m8\u001b[39m \u001b[43mDependencyContainer\u001b[49m\u001b[43m.\u001b[49m\u001b[43minitialize\u001b[49m\u001b[43m(\u001b[49m\u001b[43mobservability\u001b[49m\u001b[43m \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01m<PERSON><PERSON><PERSON>\u001b[39;49;00m\u001b[43m)\u001b[49m\n\u001b[32m      9\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mapi\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mcommon\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mservices\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mstructs\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m ModelInfo\n\u001b[32m     10\u001b[39m index_repository = DependencyContainer.get_index_repository()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/ia_gestorcontenidosiagen_be/src/api/common/dependency_container.py:121\u001b[39m, in \u001b[36mDependencyContainer.initialize\u001b[39m\u001b[34m(cls, observability)\u001b[39m\n\u001b[32m    119\u001b[39m \u001b[38;5;28mcls\u001b[39m._initialize_logger()\n\u001b[32m    120\u001b[39m \u001b[38;5;28mcls\u001b[39m._initialize_application_insights()\n\u001b[32m--> \u001b[39m\u001b[32m121\u001b[39m \u001b[38;5;28;43mcls\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_initialize_database_engines\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    122\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m observability:\n\u001b[32m    123\u001b[39m     \u001b[38;5;28mcls\u001b[39m._initialize_observability()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/ia_gestorcontenidosiagen_be/src/api/common/dependency_container.py:938\u001b[39m, in \u001b[36mDependencyContainer._initialize_database_engines\u001b[39m\u001b[34m(cls)\u001b[39m\n\u001b[32m    936\u001b[39m         logger.error(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mError creating extensions: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00me\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n\u001b[32m    937\u001b[39m         session.rollback()\n\u001b[32m--> \u001b[39m\u001b[32m938\u001b[39m \u001b[43mSQLModel\u001b[49m\u001b[43m.\u001b[49m\u001b[43mmetadata\u001b[49m\u001b[43m.\u001b[49m\u001b[43mcreate_all\u001b[49m\u001b[43m(\u001b[49m\u001b[43mbind\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mcls\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_db_engine\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    940\u001b[39m \u001b[38;5;28mcls\u001b[39m._session_factory = sessionmaker(\n\u001b[32m    941\u001b[39m     bind=\u001b[38;5;28mcls\u001b[39m._db_engine, class_=Session, expire_on_commit=\u001b[38;5;28;01mFalse\u001b[39;00m\n\u001b[32m    942\u001b[39m )\n\u001b[32m    943\u001b[39m \u001b[38;5;28mcls\u001b[39m._async_session_factory = sessionmaker(\n\u001b[32m    944\u001b[39m     bind=\u001b[38;5;28mcls\u001b[39m._async_db_engine, class_=AsyncSession, expire_on_commit=\u001b[38;5;28;01mFalse\u001b[39;00m\n\u001b[32m    945\u001b[39m )\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/ia_gestorcontenidosiagen_be/.venv/lib/python3.12/site-packages/sqlalchemy/sql/schema.py:5924\u001b[39m, in \u001b[36mMetaData.create_all\u001b[39m\u001b[34m(self, bind, tables, checkfirst)\u001b[39m\n\u001b[32m   5900\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mcreate_all\u001b[39m(\n\u001b[32m   5901\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m   5902\u001b[39m     bind: _CreateDropBind,\n\u001b[32m   5903\u001b[39m     tables: Optional[_typing_Sequence[Table]] = \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[32m   5904\u001b[39m     checkfirst: \u001b[38;5;28mbool\u001b[39m = \u001b[38;5;28;01mTrue\u001b[39;00m,\n\u001b[32m   5905\u001b[39m ) -> \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m   5906\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"Create all tables stored in this metadata.\u001b[39;00m\n\u001b[32m   5907\u001b[39m \n\u001b[32m   5908\u001b[39m \u001b[33;03m    Conditional by default, will not attempt to recreate tables already\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m   5922\u001b[39m \n\u001b[32m   5923\u001b[39m \u001b[33;03m    \"\"\"\u001b[39;00m\n\u001b[32m-> \u001b[39m\u001b[32m5924\u001b[39m     \u001b[43mbind\u001b[49m\u001b[43m.\u001b[49m\u001b[43m_run_ddl_visitor\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   5925\u001b[39m \u001b[43m        \u001b[49m\u001b[43mddl\u001b[49m\u001b[43m.\u001b[49m\u001b[43mSchemaGenerator\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcheckfirst\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcheckfirst\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtables\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtables\u001b[49m\n\u001b[32m   5926\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/ia_gestorcontenidosiagen_be/.venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py:3247\u001b[39m, in \u001b[36mEngine._run_ddl_visitor\u001b[39m\u001b[34m(self, visitorcallable, element, **kwargs)\u001b[39m\n\u001b[32m   3241\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m_run_ddl_visitor\u001b[39m(\n\u001b[32m   3242\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m   3243\u001b[39m     visitorcallable: Type[InvokeDDLBase],\n\u001b[32m   3244\u001b[39m     element: SchemaVisitable,\n\u001b[32m   3245\u001b[39m     **kwargs: Any,\n\u001b[32m   3246\u001b[39m ) -> \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m3247\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01mwith\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mbegin\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mas\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mconn\u001b[49m\u001b[43m:\u001b[49m\n\u001b[32m   3248\u001b[39m \u001b[43m        \u001b[49m\u001b[43mconn\u001b[49m\u001b[43m.\u001b[49m\u001b[43m_run_ddl_visitor\u001b[49m\u001b[43m(\u001b[49m\u001b[43mvisitorcallable\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43melement\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.local/share/uv/python/cpython-3.12.9-macos-aarch64-none/lib/python3.12/contextlib.py:137\u001b[39m, in \u001b[36m_GeneratorContextManager.__enter__\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    135\u001b[39m \u001b[38;5;28;01mdel\u001b[39;00m \u001b[38;5;28mself\u001b[39m.args, \u001b[38;5;28mself\u001b[39m.kwds, \u001b[38;5;28mself\u001b[39m.func\n\u001b[32m    136\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m137\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mnext\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mgen\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    138\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mStopIteration\u001b[39;00m:\n\u001b[32m    139\u001b[39m     \u001b[38;5;28;01m<PERSON>se\u001b[39;00m \u001b[38;5;167;01mRunt<PERSON><PERSON><PERSON>r\u001b[39;00m(\u001b[33m\"\u001b[39m\u001b[33mgenerator didn\u001b[39m\u001b[33m'\u001b[39m\u001b[33mt yield\u001b[39m\u001b[33m\"\u001b[39m) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/ia_gestorcontenidosiagen_be/.venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py:3237\u001b[39m, in \u001b[36mEngine.begin\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m   3212\u001b[39m \u001b[38;5;129m@contextlib\u001b[39m.contextmanager\n\u001b[32m   3213\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mbegin\u001b[39m(\u001b[38;5;28mself\u001b[39m) -> Iterator[Connection]:\n\u001b[32m   3214\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"Return a context manager delivering a :class:`_engine.Connection`\u001b[39;00m\n\u001b[32m   3215\u001b[39m \u001b[33;03m    with a :class:`.Transaction` established.\u001b[39;00m\n\u001b[32m   3216\u001b[39m \n\u001b[32m   (...)\u001b[39m\u001b[32m   3235\u001b[39m \n\u001b[32m   3236\u001b[39m \u001b[33;03m    \"\"\"\u001b[39;00m  \u001b[38;5;66;03m# noqa: E501\u001b[39;00m\n\u001b[32m-> \u001b[39m\u001b[32m3237\u001b[39m     \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mconnect\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m \u001b[38;5;28;01mas\u001b[39;00m conn:\n\u001b[32m   3238\u001b[39m         \u001b[38;5;28;01mwith\u001b[39;00m conn.begin():\n\u001b[32m   3239\u001b[39m             \u001b[38;5;28;01myield\u001b[39;00m conn\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/ia_gestorcontenidosiagen_be/.venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py:3273\u001b[39m, in \u001b[36mEngine.connect\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m   3250\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mconnect\u001b[39m(\u001b[38;5;28mself\u001b[39m) -> Connection:\n\u001b[32m   3251\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"Return a new :class:`_engine.Connection` object.\u001b[39;00m\n\u001b[32m   3252\u001b[39m \n\u001b[32m   3253\u001b[39m \u001b[33;03m    The :class:`_engine.Connection` acts as a Python context manager, so\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m   3270\u001b[39m \n\u001b[32m   3271\u001b[39m \u001b[33;03m    \"\"\"\u001b[39;00m\n\u001b[32m-> \u001b[39m\u001b[32m3273\u001b[39m     \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_connection_cls\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/ia_gestorcontenidosiagen_be/.venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py:147\u001b[39m, in \u001b[36mConnection.__init__\u001b[39m\u001b[34m(self, engine, connection, _has_events, _allow_revalidate, _allow_autobegin)\u001b[39m\n\u001b[32m    145\u001b[39m         \u001b[38;5;28mself\u001b[39m._dbapi_connection = engine.raw_connection()\n\u001b[32m    146\u001b[39m     \u001b[38;5;28;01mexcept\u001b[39;00m dialect.loaded_dbapi.Error \u001b[38;5;28;01mas\u001b[39;00m err:\n\u001b[32m--> \u001b[39m\u001b[32m147\u001b[39m         \u001b[43mConnection\u001b[49m\u001b[43m.\u001b[49m\u001b[43m_handle_dbapi_exception_noconnection\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    148\u001b[39m \u001b[43m            \u001b[49m\u001b[43merr\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdialect\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mengine\u001b[49m\n\u001b[32m    149\u001b[39m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    150\u001b[39m         \u001b[38;5;28;01mraise\u001b[39;00m\n\u001b[32m    151\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/ia_gestorcontenidosiagen_be/.venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py:2436\u001b[39m, in \u001b[36mConnection._handle_dbapi_exception_noconnection\u001b[39m\u001b[34m(cls, e, dialect, engine, is_disconnect, invalidate_pool_on_disconnect, is_pre_ping)\u001b[39m\n\u001b[32m   2434\u001b[39m \u001b[38;5;28;01melif\u001b[39;00m should_wrap:\n\u001b[32m   2435\u001b[39m     \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m sqlalchemy_exception \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mN<PERSON>\u001b[39;00m\n\u001b[32m-> \u001b[39m\u001b[32m2436\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m sqlalchemy_exception.with_traceback(exc_info[\u001b[32m2\u001b[39m]) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01me\u001b[39;00m\n\u001b[32m   2437\u001b[39m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m:\n\u001b[32m   2438\u001b[39m     \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m exc_info[\u001b[32m1\u001b[39m] \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/ia_gestorcontenidosiagen_be/.venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py:145\u001b[39m, in \u001b[36mConnection.__init__\u001b[39m\u001b[34m(self, engine, connection, _has_events, _allow_revalidate, _allow_autobegin)\u001b[39m\n\u001b[32m    143\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m connection \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m    144\u001b[39m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m145\u001b[39m         \u001b[38;5;28mself\u001b[39m._dbapi_connection = \u001b[43mengine\u001b[49m\u001b[43m.\u001b[49m\u001b[43mraw_connection\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    146\u001b[39m     \u001b[38;5;28;01mexcept\u001b[39;00m dialect.loaded_dbapi.Error \u001b[38;5;28;01mas\u001b[39;00m err:\n\u001b[32m    147\u001b[39m         Connection._handle_dbapi_exception_noconnection(\n\u001b[32m    148\u001b[39m             err, dialect, engine\n\u001b[32m    149\u001b[39m         )\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/ia_gestorcontenidosiagen_be/.venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py:3297\u001b[39m, in \u001b[36mEngine.raw_connection\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m   3275\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mraw_connection\u001b[39m(\u001b[38;5;28mself\u001b[39m) -> PoolProxiedConnection:\n\u001b[32m   3276\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"Return a \"raw\" DBAPI connection from the connection pool.\u001b[39;00m\n\u001b[32m   3277\u001b[39m \n\u001b[32m   3278\u001b[39m \u001b[33;03m    The returned object is a proxied version of the DBAPI\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m   3295\u001b[39m \n\u001b[32m   3296\u001b[39m \u001b[33;03m    \"\"\"\u001b[39;00m\n\u001b[32m-> \u001b[39m\u001b[32m3297\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mpool\u001b[49m\u001b[43m.\u001b[49m\u001b[43mconnect\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/ia_gestorcontenidosiagen_be/.venv/lib/python3.12/site-packages/sqlalchemy/pool/base.py:449\u001b[39m, in \u001b[36mPool.connect\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    441\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mconnect\u001b[39m(\u001b[38;5;28mself\u001b[39m) -> PoolProxiedConnection:\n\u001b[32m    442\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"Return a DBAPI connection from the pool.\u001b[39;00m\n\u001b[32m    443\u001b[39m \n\u001b[32m    444\u001b[39m \u001b[33;03m    The connection is instrumented such that when its\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m    447\u001b[39m \n\u001b[32m    448\u001b[39m \u001b[33;03m    \"\"\"\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m449\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_ConnectionFairy\u001b[49m\u001b[43m.\u001b[49m\u001b[43m_checkout\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/ia_gestorcontenidosiagen_be/.venv/lib/python3.12/site-packages/sqlalchemy/pool/base.py:1264\u001b[39m, in \u001b[36m_ConnectionFairy._checkout\u001b[39m\u001b[34m(cls, pool, threadconns, fairy)\u001b[39m\n\u001b[32m   1256\u001b[39m \u001b[38;5;129m@classmethod\u001b[39m\n\u001b[32m   1257\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m_checkout\u001b[39m(\n\u001b[32m   1258\u001b[39m     \u001b[38;5;28mcls\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m   1261\u001b[39m     fairy: Optional[_ConnectionFairy] = \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[32m   1262\u001b[39m ) -> _ConnectionFairy:\n\u001b[32m   1263\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m fairy:\n\u001b[32m-> \u001b[39m\u001b[32m1264\u001b[39m         fairy = \u001b[43m_ConnectionRecord\u001b[49m\u001b[43m.\u001b[49m\u001b[43mcheckout\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpool\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1266\u001b[39m         \u001b[38;5;28;01mif\u001b[39;00m threadconns \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m   1267\u001b[39m             threadconns.current = weakref.ref(fairy)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/ia_gestorcontenidosiagen_be/.venv/lib/python3.12/site-packages/sqlalchemy/pool/base.py:713\u001b[39m, in \u001b[36m_ConnectionRecord.checkout\u001b[39m\u001b[34m(cls, pool)\u001b[39m\n\u001b[32m    711\u001b[39m     rec = cast(_ConnectionRecord, pool._do_get())\n\u001b[32m    712\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m713\u001b[39m     rec = \u001b[43mpool\u001b[49m\u001b[43m.\u001b[49m\u001b[43m_do_get\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    715\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m    716\u001b[39m     dbapi_connection = rec.get_connection()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/ia_gestorcontenidosiagen_be/.venv/lib/python3.12/site-packages/sqlalchemy/pool/impl.py:179\u001b[39m, in \u001b[36mQueuePool._do_get\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    177\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m._create_connection()\n\u001b[32m    178\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m179\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01mwith\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mutil\u001b[49m\u001b[43m.\u001b[49m\u001b[43msafe_reraise\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m:\u001b[49m\n\u001b[32m    180\u001b[39m \u001b[43m        \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_dec_overflow\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    181\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/ia_gestorcontenidosiagen_be/.venv/lib/python3.12/site-packages/sqlalchemy/util/langhelpers.py:224\u001b[39m, in \u001b[36msafe_reraise.__exit__\u001b[39m\u001b[34m(self, type_, value, traceback)\u001b[39m\n\u001b[32m    222\u001b[39m     \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m exc_value \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m    223\u001b[39m     \u001b[38;5;28mself\u001b[39m._exc_info = \u001b[38;5;28;01mNone\u001b[39;00m  \u001b[38;5;66;03m# remove potential circular references\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m224\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m exc_value.with_traceback(exc_tb)\n\u001b[32m    225\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m    226\u001b[39m     \u001b[38;5;28mself\u001b[39m._exc_info = \u001b[38;5;28;01mNone\u001b[39;00m  \u001b[38;5;66;03m# remove potential circular references\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/ia_gestorcontenidosiagen_be/.venv/lib/python3.12/site-packages/sqlalchemy/pool/impl.py:177\u001b[39m, in \u001b[36mQueuePool._do_get\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    175\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m._inc_overflow():\n\u001b[32m    176\u001b[39m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m177\u001b[39m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_create_connection\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    178\u001b[39m     \u001b[38;5;28;01mexcept\u001b[39;00m:\n\u001b[32m    179\u001b[39m         \u001b[38;5;28;01mwith\u001b[39;00m util.safe_reraise():\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/ia_gestorcontenidosiagen_be/.venv/lib/python3.12/site-packages/sqlalchemy/pool/base.py:390\u001b[39m, in \u001b[36mPool._create_connection\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    387\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m_create_connection\u001b[39m(\u001b[38;5;28mself\u001b[39m) -> ConnectionPoolEntry:\n\u001b[32m    388\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"Called by subclasses to create a new ConnectionRecord.\"\"\"\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m390\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_ConnectionRecord\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/ia_gestorcontenidosiagen_be/.venv/lib/python3.12/site-packages/sqlalchemy/pool/base.py:675\u001b[39m, in \u001b[36m_ConnectionRecord.__init__\u001b[39m\u001b[34m(self, pool, connect)\u001b[39m\n\u001b[32m    673\u001b[39m \u001b[38;5;28mself\u001b[39m.__pool = pool\n\u001b[32m    674\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m connect:\n\u001b[32m--> \u001b[39m\u001b[32m675\u001b[39m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m__connect\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    676\u001b[39m \u001b[38;5;28mself\u001b[39m.finalize_callback = deque()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/ia_gestorcontenidosiagen_be/.venv/lib/python3.12/site-packages/sqlalchemy/pool/base.py:901\u001b[39m, in \u001b[36m_ConnectionRecord.__connect\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    899\u001b[39m     \u001b[38;5;28mself\u001b[39m.fresh = \u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[32m    900\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mBaseException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[32m--> \u001b[39m\u001b[32m901\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01mwith\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mutil\u001b[49m\u001b[43m.\u001b[49m\u001b[43msafe_reraise\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m:\u001b[49m\n\u001b[32m    902\u001b[39m \u001b[43m        \u001b[49m\u001b[43mpool\u001b[49m\u001b[43m.\u001b[49m\u001b[43mlogger\u001b[49m\u001b[43m.\u001b[49m\u001b[43mdebug\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mError on connect(): \u001b[39;49m\u001b[38;5;132;43;01m%s\u001b[39;49;00m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43me\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    903\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m    904\u001b[39m     \u001b[38;5;66;03m# in SQLAlchemy 1.4 the first_connect event is not used by\u001b[39;00m\n\u001b[32m    905\u001b[39m     \u001b[38;5;66;03m# the engine, so this will usually not be set\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/ia_gestorcontenidosiagen_be/.venv/lib/python3.12/site-packages/sqlalchemy/util/langhelpers.py:224\u001b[39m, in \u001b[36msafe_reraise.__exit__\u001b[39m\u001b[34m(self, type_, value, traceback)\u001b[39m\n\u001b[32m    222\u001b[39m     \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m exc_value \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m    223\u001b[39m     \u001b[38;5;28mself\u001b[39m._exc_info = \u001b[38;5;28;01mNone\u001b[39;00m  \u001b[38;5;66;03m# remove potential circular references\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m224\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m exc_value.with_traceback(exc_tb)\n\u001b[32m    225\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m    226\u001b[39m     \u001b[38;5;28mself\u001b[39m._exc_info = \u001b[38;5;28;01mNone\u001b[39;00m  \u001b[38;5;66;03m# remove potential circular references\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/ia_gestorcontenidosiagen_be/.venv/lib/python3.12/site-packages/sqlalchemy/pool/base.py:897\u001b[39m, in \u001b[36m_ConnectionRecord.__connect\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    895\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m    896\u001b[39m     \u001b[38;5;28mself\u001b[39m.starttime = time.time()\n\u001b[32m--> \u001b[39m\u001b[32m897\u001b[39m     \u001b[38;5;28mself\u001b[39m.dbapi_connection = connection = \u001b[43mpool\u001b[49m\u001b[43m.\u001b[49m\u001b[43m_invoke_creator\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m    898\u001b[39m     pool.logger.debug(\u001b[33m\"\u001b[39m\u001b[33mCreated new connection \u001b[39m\u001b[38;5;132;01m%r\u001b[39;00m\u001b[33m\"\u001b[39m, connection)\n\u001b[32m    899\u001b[39m     \u001b[38;5;28mself\u001b[39m.fresh = \u001b[38;5;28;01mTrue\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/ia_gestorcontenidosiagen_be/.venv/lib/python3.12/site-packages/sqlalchemy/engine/create.py:646\u001b[39m, in \u001b[36mcreate_engine.<locals>.connect\u001b[39m\u001b[34m(connection_record)\u001b[39m\n\u001b[32m    643\u001b[39m         \u001b[38;5;28;01mif\u001b[39;00m connection \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m    644\u001b[39m             \u001b[38;5;28;01mreturn\u001b[39;00m connection\n\u001b[32m--> \u001b[39m\u001b[32m646\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mdialect\u001b[49m\u001b[43m.\u001b[49m\u001b[43mconnect\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43mcargs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mcparams\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/ia_gestorcontenidosiagen_be/.venv/lib/python3.12/site-packages/sqlalchemy/engine/default.py:625\u001b[39m, in \u001b[36mDefaultDialect.connect\u001b[39m\u001b[34m(self, *cargs, **cparams)\u001b[39m\n\u001b[32m    623\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mconnect\u001b[39m(\u001b[38;5;28mself\u001b[39m, *cargs: Any, **cparams: Any) -> DBAPIConnection:\n\u001b[32m    624\u001b[39m     \u001b[38;5;66;03m# inherits the docstring from interfaces.Dialect.connect\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m625\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mloaded_dbapi\u001b[49m\u001b[43m.\u001b[49m\u001b[43mconnect\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43mcargs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mcparams\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/ia_gestorcontenidosiagen_be/.venv/lib/python3.12/site-packages/psycopg2/__init__.py:122\u001b[39m, in \u001b[36mconnect\u001b[39m\u001b[34m(dsn, connection_factory, cursor_factory, **kwargs)\u001b[39m\n\u001b[32m    119\u001b[39m     kwasync[\u001b[33m'\u001b[39m\u001b[33masync_\u001b[39m\u001b[33m'\u001b[39m] = kwargs.pop(\u001b[33m'\u001b[39m\u001b[33masync_\u001b[39m\u001b[33m'\u001b[39m)\n\u001b[32m    121\u001b[39m dsn = _ext.make_dsn(dsn, **kwargs)\n\u001b[32m--> \u001b[39m\u001b[32m122\u001b[39m conn = \u001b[43m_connect\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdsn\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mconnection_factory\u001b[49m\u001b[43m=\u001b[49m\u001b[43mconnection_factory\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwasync\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    123\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m cursor_factory \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m    124\u001b[39m     conn.cursor_factory = cursor_factory\n", "\u001b[31mOperationalError\u001b[39m: (psycopg2.OperationalError) could not translate host name \"db\" to address: nodename nor servname provided, or not known\n\n(Background on this error at: https://sqlalche.me/e/20/e3q8)"]}], "source": ["import sys\n", "from pathlib import Path\n", "from dotenv import load_dotenv\n", "load_dotenv()\n", "parent_dir = Path.cwd().parent.parent\n", "sys.path.append(str(parent_dir))\n", "from src.api.common.dependency_container import DependencyContainer\n", "DependencyContainer.initialize(observability = False)\n", "from src.api.common.services.structs import ModelInfo\n", "index_repository = DependencyContainer.get_index_repository()\n", "\n", "from openai import AsyncOpenAI\n", "import os\n", "from dotenv import load_dotenv\n", "load_dotenv()\n", "client = AsyncOpenAI(api_key=os.getenv(\"OpenaiApiKey\"))"]}, {"cell_type": "markdown", "id": "906ae222", "metadata": {}, "source": ["### Utilities"]}, {"cell_type": "code", "execution_count": null, "id": "76de5e8b", "metadata": {}, "outputs": [], "source": ["from IPython.display import display, Markdown\n", "from src.api.common.services.structs import Asignatura\n", "\n", "def display_index_markdown(subject_index: Asignatura) -> str:\n", "    \"\"\"\n", "    Convierte un objeto de índice de asignatura en formato Markdown y lo muestra en un notebook.\n", "    \n", "    Args:\n", "        subject_index: Objeto GenerateIndexResponse con la estructura de la asignatura\n", "        \n", "    Returns:\n", "        str: El contenido en formato Markdown\n", "    \"\"\"\n", "    asignatura = subject_index.index\n", "    md_content = []\n", "    md_content.append(f\"# {asignatura.nombre}\\n\")\n", "    md_content.append(\"## Competencias\\n\")\n", "    for i, comp in enumerate(subject_index.competencies, 1):\n", "        md_content.append(f\"{i}. {comp.descripcion}\")\n", "    md_content.append(\"\")\n", "    md_content.append(\"## Estructura Temática\\n\")\n", "    for bloque_num, bloque in enumerate(asignatura.estructura.bloques_tematicos, 1):\n", "        md_content.append(f\"### Bloque {bloque_num}: {bloque.nombre}\\n\")\n", "        for tema_num, tema in enumerate(bloque.temas, 1):\n", "            md_content.append(f\"#### {bloque_num}.{tema_num}. {tema.nombre}\\n\")\n", "            for epigrafe_num, epigrafe in enumerate(tema.epigrafes, 1):\n", "                md_content.append(f\"##### {bloque_num}.{tema_num}.{epigrafe_num} {epigrafe}\")\n", "            md_content.append(\"\")\n", "    \n", "    markdown_text = \"\\n\".join(md_content)\n", "    \n", "    display(Markdown(markdown_text))"]}, {"cell_type": "markdown", "id": "bb40f515", "metadata": {}, "source": ["## Previous steps"]}, {"cell_type": "markdown", "id": "c9bec8ea", "metadata": {}, "source": ["### Visualizing index and competencies"]}, {"cell_type": "code", "execution_count": 4, "id": "375a3450", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-11 12:49:36,837 - logger - INFO - Index is status=<IndiceStatus.CONTENT_GENERATION: 'CONTENT_GENERATION'> is_displayed=False created_at=datetime.datetime(2025, 6, 26, 9, 1, 27, 91031) id=46 order_id=3 version=7 created_by='ia_gen_user' updated_by='ia_gen_user' updated_at=datetime.datetime(2025, 6, 26, 9, 13, 43, 939622)\n", "2025-07-11 12:49:36,838 - logger - INFO - Entered in async session\n", "2025-07-11 12:49:36,842 - logger - INFO - Indice inside get subject index is, status=<IndiceStatus.CONTENT_GENERATION: 'CONTENT_GENERATION'> is_displayed=False created_at=datetime.datetime(2025, 6, 26, 9, 1, 27, 91031) id=46 order_id=3 version=7 created_by='ia_gen_user' updated_by='ia_gen_user' updated_at=datetime.datetime(2025, 6, 26, 9, 13, 43, 939622)\n", "2025-07-11 12:49:36,844 - logger - INFO - Bloques inside get subject index is, [<PERSON><PERSON><PERSON>(name='Fundamentos de la Geopolítica Aplicada', indice_id=46, created_by='ia_gen_user', updated_by=None, created_at=datetime.datetime(2025, 6, 26, 9, 1, 27, 111620), id=59, position=1, updated_at=datetime.datetime(2025, 6, 26, 9, 1, 27, 111878))]\n", "2025-07-11 12:49:36,844 - logger - INFO - Asignatura is, nombre='Introducción a la geopolítica - Master en Relaciones internacionales' estructura=Estructura(bloques_tematicos=[BloqueTematico(nombre='Fundamentos de la Geopolítica Aplicada', temas=[Tema(nombre='Bases Conceptuales y Metodológicas de la Geopolítica', epigrafes=['Orígenes y evolución histórica de la geopolítica: de Mackinder a la era digital', 'Escuelas y marcos teóricos clásicos y contemporáneos', 'Herramientas de análisis geopolítico: cartografía, SIG y métricas de poder', 'Estudios de caso aplicados: riesgo país, recursos estratégicos y toma de decisiones'])])])\n", "2025-07-11 12:49:36,844 - logger - INFO - Asignatura is: nombre='Introducción a la geopolítica - Master en Relaciones internacionales' estructura=Estructura(bloques_tematicos=[BloqueTematico(nombre='Fundamentos de la Geopolítica Aplicada', temas=[Tema(nombre='Bases Conceptuales y Metodológicas de la Geopolítica', epigrafes=['Orígenes y evolución histórica de la geopolítica: de Mackinder a la era digital', 'Escuelas y marcos teóricos clásicos y contemporáneos', 'Herramientas de análisis geopolítico: cartografía, SIG y métricas de poder', 'Estudios de caso aplicados: riesgo país, recursos estratégicos y toma de decisiones'])])])\n", "2025-07-11 12:49:36,846 - logger - INFO - Competencies are: competencias=[Competencia(descripcion='Capacidad para analizar de forma rigurosa las dinámicas de poder y la distribución de influencia a escala global, aplicándolas a escenarios profesionales de política exterior y consultoría.'), Competencia(descripcion='Habilidad para identificar y evaluar los intereses nacionales y estratégicos de distintos actores estatales y no estatales, aportando valor a labores de asesoría y diseño de políticas públicas.'), Competencia(descripcion='Competencia para aplicar los principios de la geografía política en la formulación de estrategias y políticas internacionales dentro de organismos gubernamentales o corporaciones multinacionales.'), Competencia(descripcion='Destreza en la interpretación y elaboración de mapas y representaciones geopolíticas como herramienta para la toma de decisiones en contextos diplomáticos y empresariales.'), Competencia(descripcion='Capacidad para evaluar el impacto geopolítico de recursos naturales, infraestructuras y rutas de transporte, generando análisis útiles para la gestión de riesgos y oportunidades de inversión.'), Competencia(descripcion='Habilidad para elaborar informes de riesgo país fundamentados en variables geopolíticas, útiles para entidades financieras, ONGs y departamentos de estrategia corporativa.'), Competencia(descripcion='Facilidad para utilizar marcos teóricos clásicos y contemporáneos de la geopolítica en el análisis de crisis internacionales, aportando perspectiva estratégica en entornos laborales multiactor.'), Competencia(descripcion='Destreza en la comunicación clara y fundamentada de análisis geopolíticos a audiencias profesionales y medios, mejorando la capacidad de incidencia y divulgación.'), Competencia(descripcion='Competencia para integrar datos cuantitativos y cualitativos en estudios geopolíticos aplicados, empleando metodologías de investigación pertinentes al sector laboral.'), Competencia(descripcion='Desarrollo de pensamiento estratégico orientado a la negociación y resolución de conflictos internacionales, valioso para funciones en diplomacia, mediación y organismos multilaterales.')]\n"]}, {"data": {"text/markdown": ["# Introducción a la geopolítica - Master en Relaciones internacionales\n", "\n", "## Competencias\n", "\n", "1. Capacidad para analizar de forma rigurosa las dinámicas de poder y la distribución de influencia a escala global, aplicándolas a escenarios profesionales de política exterior y consultoría.\n", "2. Habilidad para identificar y evaluar los intereses nacionales y estratégicos de distintos actores estatales y no estatales, aportando valor a labores de asesoría y diseño de políticas públicas.\n", "3. Competencia para aplicar los principios de la geografía política en la formulación de estrategias y políticas internacionales dentro de organismos gubernamentales o corporaciones multinacionales.\n", "4. Destreza en la interpretación y elaboración de mapas y representaciones geopolíticas como herramienta para la toma de decisiones en contextos diplomáticos y empresariales.\n", "5. Capacidad para evaluar el impacto geopolítico de recursos naturales, infraestructuras y rutas de transporte, generando análisis útiles para la gestión de riesgos y oportunidades de inversión.\n", "6. Habilidad para elaborar informes de riesgo país fundamentados en variables geopolíticas, útiles para entidades financieras, ONGs y departamentos de estrategia corporativa.\n", "7. Facilidad para utilizar marcos teóricos clásicos y contemporáneos de la geopolítica en el análisis de crisis internacionales, aportando perspectiva estratégica en entornos laborales multiactor.\n", "8. Destreza en la comunicación clara y fundamentada de análisis geopolíticos a audiencias profesionales y medios, mejorando la capacidad de incidencia y divulgación.\n", "9. Competencia para integrar datos cuantitativos y cualitativos en estudios geopolíticos aplicados, empleando metodologías de investigación pertinentes al sector laboral.\n", "10. Desarrollo de pensamiento estratégico orientado a la negociación y resolución de conflictos internacionales, valioso para funciones en diplomacia, mediación y organismos multilaterales.\n", "\n", "## Estructura Temática\n", "\n", "### Bloque 1: Fundamentos de la Geopolítica Aplicada\n", "\n", "#### 1.1. Bases Conceptuales y Metodológicas de la Geopolítica\n", "\n", "##### 1.1.1 Orígenes y evolución histórica de la geopolítica: de Mackinder a la era digital\n", "##### 1.1.2 Escuelas y marcos teóricos clásicos y contemporáneos\n", "##### 1.1.3 Herramientas de análisis geopolítico: cartografía, SIG y métricas de poder\n", "##### 1.1.4 Estudios de caso aplicados: riesgo pa<PERSON>, recursos estratégicos y toma de decisiones\n"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["INDEX_ID = 46\n", "ORDER_ID = 3\n", "from src.api.workflows.indexes import GetIndexRequest\n", "request = GetIndexRequest(id = INDEX_ID)\n", "index_response = await DependencyContainer.get_index_workflow().execute(request)\n", "display_index_markdown(index_response)"]}, {"cell_type": "markdown", "id": "4380127d", "metadata": {}, "source": ["### Getting instructions and context"]}, {"cell_type": "markdown", "id": "ea493205", "metadata": {}, "source": ["We get info from instructions and topics so we can build the context for the model."]}, {"cell_type": "code", "execution_count": 4, "id": "1974daa5", "metadata": {}, "outputs": [], "source": ["content_generator = DependencyContainer.get_content_generator_new()"]}, {"cell_type": "code", "execution_count": 5, "id": "6004d0bb", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n", "2025-07-11 12:35:21,288 - logger - INFO - Topic 123 memories updated\n", "2025-07-11 12:35:21,292 - logger - INFO - Topic 124 memories updated\n", "2025-07-11 12:35:21,297 - logger - INFO - Topic 125 memories updated\n", "2025-07-11 12:35:21,302 - logger - INFO - Topic 126 memories updated\n", "2025-07-11 12:35:21,306 - logger - INFO - Topic 127 memories updated\n", "2025-07-11 12:35:21,309 - logger - INFO - Topic 128 memories updated\n", "2025-07-11 12:35:21,313 - logger - INFO - Topic 129 memories updated\n", "2025-07-11 12:35:21,319 - logger - INFO - Topic 130 memories updated\n", "2025-07-11 12:35:21,323 - logger - INFO - Topic 131 memories updated\n", "2025-07-11 12:35:21,327 - logger - INFO - Topic 132 memories updated\n"]}, {"data": {"text/plain": ["{0: [],\n", " 1: ['Editor y entorno recomendados: Visual Studio Code y Node.js (se usará ambos para todo el curso).',\n", "  'Tablas para versiones de ECMAScript y extensiones de VS Code deben usarse cuando se traten esos temas.',\n", "  'Se adopta el pseudocódigo con mayúsculas para palabras reservadas, sangrías y comentarios con // como convención general.',\n", "  'Extensión esperada por sección: 1 100-1 300 palabras para introducción (con pseudocódigo y diagrama), 950-1 150 palabras para historia de JavaScript (con tabla y diagrama opcional), 850-1 050 para instalación (con tabla de extensiones y comandos), 800-1 000 para primer script (con dos listados de código, sin tablas/diagramas).',\n", "  'Los ejercicios de pseudocódigo deben incluir micro-ejemplo de cada estructura básica (secuencia, selección, repetición) y al menos un ejercicio integral con pseudocódigo y diagrama mermaid simple (menos de diez nodos).',\n", "  'Los diagramas de flujo y temporales deben ser en formato mermaid y limitar nodos/hitos a lo imprescindible para la comprensión del bloque.'],\n", " 2: ['Se recomienda declarar variables con const por defecto, usar let si se requiere mutabilidad, y evitar var salvo motivos concretos.',\n", "  'Editor y entorno recomendados: Visual Studio Code y Node.js (se usará ambos para todo el curso).',\n", "  'Tablas para versiones de ECMAScript y extensiones de VS Code deben usarse cuando se traten esos temas.',\n", "  'Se adopta el pseudocódigo con mayúsculas para palabras reservadas, sangrías y comentarios con // como convención general.',\n", "  'Extensión esperada por sección: 1 100-1 300 palabras para introducción (con pseudocódigo y diagrama), 950-1 150 palabras para historia de JavaScript (con tabla y diagrama opcional), 850-1 050 para instalación (con tabla de extensiones y comandos), 800-1 000 para primer script (con dos listados de código, sin tablas/diagramas).',\n", "  'Los ejercicios de pseudocódigo deben incluir micro-ejemplo de cada estructura básica (secuencia, selección, repetición) y al menos un ejercicio integral con pseudocódigo y diagrama mermaid simple (menos de diez nodos).',\n", "  'Se adopta camelCase como convención para nombrar variables.',\n", "  'Los diagramas de flujo y temporales deben ser en formato mermaid y limitar nodos/hitos a lo imprescindible para la comprensión del bloque.'],\n", " 3: ['Se recomienda declarar variables con const por defecto, usar let si se requiere mutabilidad, y evitar var salvo motivos concretos.',\n", "  'Editor y entorno recomendados: Visual Studio Code y Node.js (se usará ambos para todo el curso).',\n", "  'Tablas para versiones de ECMAScript y extensiones de VS Code deben usarse cuando se traten esos temas.',\n", "  'Se adopta el pseudocódigo con mayúsculas para palabras reservadas, sangrías y comentarios con // como convención general.',\n", "  'Extensión esperada por sección: 1 100-1 300 palabras para introducción (con pseudocódigo y diagrama), 950-1 150 palabras para historia de JavaScript (con tabla y diagrama opcional), 850-1 050 para instalación (con tabla de extensiones y comandos), 800-1 000 para primer script (con dos listados de código, sin tablas/diagramas).',\n", "  'Los ejercicios de pseudocódigo deben incluir micro-ejemplo de cada estructura básica (secuencia, selección, repetición) y al menos un ejercicio integral con pseudocódigo y diagrama mermaid simple (menos de diez nodos).',\n", "  'Se adopta camelCase como convención para nombrar variables.',\n", "  'Los diagramas de flujo y temporales deben ser en formato mermaid y limitar nodos/hitos a lo imprescindible para la comprensión del bloque.'],\n", " 4: ['Se recomienda declarar variables con const por defecto, usar let si se requiere mutabilidad, y evitar var salvo motivos concretos.',\n", "  'Editor y entorno recomendados: Visual Studio Code y Node.js (se usará ambos para todo el curso).',\n", "  'Tablas para versiones de ECMAScript y extensiones de VS Code deben usarse cuando se traten esos temas.',\n", "  'Se adopta el pseudocódigo con mayúsculas para palabras reservadas, sangrías y comentarios con // como convención general.',\n", "  'Extensión esperada por sección: 1 100-1 300 palabras para introducción (con pseudocódigo y diagrama), 950-1 150 palabras para historia de JavaScript (con tabla y diagrama opcional), 850-1 050 para instalación (con tabla de extensiones y comandos), 800-1 000 para primer script (con dos listados de código, sin tablas/diagramas).',\n", "  'Los ejercicios de pseudocódigo deben incluir micro-ejemplo de cada estructura básica (secuencia, selección, repetición) y al menos un ejercicio integral con pseudocódigo y diagrama mermaid simple (menos de diez nodos).',\n", "  'Se adopta camelCase como convención para nombrar variables.',\n", "  'Los diagramas de flujo y temporales deben ser en formato mermaid y limitar nodos/hitos a lo imprescindible para la comprensión del bloque.'],\n", " 5: ['Se recomienda declarar variables con const por defecto, usar let si se requiere mutabilidad, y evitar var salvo motivos concretos.',\n", "  'Editor y entorno recomendados: Visual Studio Code y Node.js (se usará ambos para todo el curso).',\n", "  'Tablas para versiones de ECMAScript y extensiones de VS Code deben usarse cuando se traten esos temas.',\n", "  'Se adopta el pseudocódigo con mayúsculas para palabras reservadas, sangrías y comentarios con // como convención general.',\n", "  'Extensión esperada por sección: 1 100-1 300 palabras para introducción (con pseudocódigo y diagrama), 950-1 150 palabras para historia de JavaScript (con tabla y diagrama opcional), 850-1 050 para instalación (con tabla de extensiones y comandos), 800-1 000 para primer script (con dos listados de código, sin tablas/diagramas).',\n", "  'Los ejercicios de pseudocódigo deben incluir micro-ejemplo de cada estructura básica (secuencia, selección, repetición) y al menos un ejercicio integral con pseudocódigo y diagrama mermaid simple (menos de diez nodos).',\n", "  'Se adopta camelCase como convención para nombrar variables.',\n", "  'Los diagramas de flujo y temporales deben ser en formato mermaid y limitar nodos/hitos a lo imprescindible para la comprensión del bloque.'],\n", " 6: ['Se recomienda declarar variables con const por defecto, usar let si se requiere mutabilidad, y evitar var salvo motivos concretos.',\n", "  'Editor y entorno recomendados: Visual Studio Code y Node.js (se usará ambos para todo el curso).',\n", "  'Uso de medición de tiempos (console.time) recomendado cuando se compare eficiencia en ejemplos relevantes.',\n", "  'Tablas para versiones de ECMAScript y extensiones de VS Code deben usarse cuando se traten esos temas.',\n", "  'Se adopta el pseudocódigo con mayúsculas para palabras reservadas, sangrías y comentarios con // como convención general.',\n", "  'Ejemplos de arrays deben cubrir tanto Node.js como navegador.',\n", "  'Extensión esperada por sección: 1 100-1 300 palabras para introducción (con pseudocódigo y diagrama), 950-1 150 palabras para historia de JavaScript (con tabla y diagrama opcional), 850-1 050 para instalación (con tabla de extensiones y comandos), 800-1 000 para primer script (con dos listados de código, sin tablas/diagramas).',\n", "  'Los ejercicios de pseudocódigo deben incluir micro-ejemplo de cada estructura básica (secuencia, selección, repetición) y al menos un ejercicio integral con pseudocódigo y diagrama mermaid simple (menos de diez nodos).',\n", "  'Se adopta camelCase como convención para nombrar variables.',\n", "  'Cada transformación o estructura debe incluir al menos un ejemplo de código comentado y ejercicio integrado.',\n", "  'Los ejercicios algorítmicos deben requerir manipular arreglos/matrices sin estructuras auxiliares externas cuando el objetivo sea afianzar lógica interna.',\n", "  'Diagramas mermaid simples requeridos para ilustrar flujos o estructuras clave (pila, cola, chaining funcional, matrices).',\n", "  'Los diagramas de flujo y temporales deben ser en formato mermaid y limitar nodos/hitos a lo imprescindible para la comprensión del bloque.',\n", "  'Clasificación obligatoria de métodos de arrays en tablas comparativas (efecto sobre longitud, copia o referencia, complejidad temporal).'],\n", " 7: ['No se permite aún hablar de métricas de complejidad ni balanceo antes del epígrafe correspondiente; se deben respetar estos límites narrativos.',\n", "  'Se recomienda declarar variables con const por defecto, usar let si se requiere mutabilidad, y evitar var salvo motivos concretos.',\n", "  'Uso de medición de tiempos (console.time) recomendado cuando se compare eficiencia en ejemplos relevantes.',\n", "  'Tablas para versiones de ECMAScript y extensiones de VS Code deben usarse cuando se traten esos temas.',\n", "  'Se adopta el pseudocódigo con mayúsculas para palabras reservadas, sangrías y comentarios con // como convención general.',\n", "  'Ejemplos de arrays deben cubrir tanto Node.js como navegador.',\n", "  'Los ejercicios algorítmicos deben requerir manipular arreglos/matrices sin estructuras auxiliares externas cuando el objetivo sea afianzar lógica interna.',\n", "  'Los ejercicios de pseudocódigo deben incluir micro-ejemplo de cada estructura básica (secuencia, selección, repetición) y al menos un ejercicio integral con pseudocódigo y diagrama mermaid simple (menos de diez nodos).',\n", "  'Los códigos de ejemplo en todos los subtemas se implementarán en JavaScript, adoptando clases y sintaxis moderna (ES6+).',\n", "  'Cada transformación o estructura debe incluir al menos un ejemplo de código comentado y ejercicio integrado.',\n", "  'Los ejercicios deberán solicitar implementaciones iterativas de métodos y evitar explícitamente el uso de variables globales.',\n", "  'Editor y entorno recomendados: Visual Studio Code y Node.js (se usará ambos para todo el curso).',\n", "  'Se requiere introducir y usar diagramas Mermaid para visualización de nodos y estructuras enlazadas (listas y árboles).',\n", "  'Buenas prácticas como validación de índices, manejo de estructuras vacías y ruptura de referencias se considerarán estándar en el manejo de listas y árboles.',\n", "  'Se recomienda mostrar y justificar la complejidad temporal/espacial mediante micro-tablas asociadas a cada operación fundamental.',\n", "  'Se adopta camelCase como convención para nombrar variables.',\n", "  'Las comparativas entre estructuras deberán presentarse en tablas de costes (arrays vs listas, listas vs árboles, DFS vs BFS).',\n", "  'Extensión esperada por sección: 1 100-1 300 palabras para introducción (con pseudocódigo y diagrama), 950-1 150 palabras para historia de JavaScript (con tabla y diagrama opcional), 850-1 050 para instalación (con tabla de extensiones y comandos), 800-1 000 para primer script (con dos listados de código, sin tablas/diagramas).',\n", "  'Diagramas mermaid simples requeridos para ilustrar flujos o estructuras clave (pila, cola, chaining funcional, matrices).',\n", "  'Los diagramas de flujo y temporales deben ser en formato mermaid y limitar nodos/hitos a lo imprescindible para la comprensión del bloque.',\n", "  'Clasificación obligatoria de métodos de arrays en tablas comparativas (efecto sobre longitud, copia o referencia, complejidad temporal).',\n", "  'Se utilizará el patrón de construir manualmente nodos y estructuras antes de encapsular la lógica en clases (Node y LinkedList, TreeNode).',\n", "  'Snippets de micro-benchmark (performance.now()) deben emplearse al tratar eficiencia para ilustrar diferencias entre implementaciones.'],\n", " 8: ['No se permite aún hablar de métricas de complejidad ni balanceo antes del epígrafe correspondiente; se deben respetar estos límites narrativos.',\n", "  'Se recomienda declarar variables con const por defecto, usar let si se requiere mutabilidad, y evitar var salvo motivos concretos.',\n", "  'Uso de medición de tiempos (console.time) recomendado cuando se compare eficiencia en ejemplos relevantes.',\n", "  'Tablas para versiones de ECMAScript y extensiones de VS Code deben usarse cuando se traten esos temas.',\n", "  'Se adopta el pseudocódigo con mayúsculas para palabras reservadas, sangrías y comentarios con // como convención general.',\n", "  'Ejemplos de arrays deben cubrir tanto Node.js como navegador.',\n", "  'Los ejercicios algorítmicos deben requerir manipular arreglos/matrices sin estructuras auxiliares externas cuando el objetivo sea afianzar lógica interna.',\n", "  'Los ejercicios de pseudocódigo deben incluir micro-ejemplo de cada estructura básica (secuencia, selección, repetición) y al menos un ejercicio integral con pseudocódigo y diagrama mermaid simple (menos de diez nodos).',\n", "  'Los códigos de ejemplo en todos los subtemas se implementarán en JavaScript, adoptando clases y sintaxis moderna (ES6+).',\n", "  'Cada transformación o estructura debe incluir al menos un ejemplo de código comentado y ejercicio integrado.',\n", "  'Los ejercicios deberán solicitar implementaciones iterativas de métodos y evitar explícitamente el uso de variables globales.',\n", "  'Editor y entorno recomendados: Visual Studio Code y Node.js (se usará ambos para todo el curso).',\n", "  'Se requiere introducir y usar diagramas Mermaid para visualización de nodos y estructuras enlazadas (listas y árboles).',\n", "  'Buenas prácticas como validación de índices, manejo de estructuras vacías y ruptura de referencias se considerarán estándar en el manejo de listas y árboles.',\n", "  'Se recomienda mostrar y justificar la complejidad temporal/espacial mediante micro-tablas asociadas a cada operación fundamental.',\n", "  'Se adopta camelCase como convención para nombrar variables.',\n", "  'Las comparativas entre estructuras deberán presentarse en tablas de costes (arrays vs listas, listas vs árboles, DFS vs BFS).',\n", "  'Extensión esperada por sección: 1 100-1 300 palabras para introducción (con pseudocódigo y diagrama), 950-1 150 palabras para historia de JavaScript (con tabla y diagrama opcional), 850-1 050 para instalación (con tabla de extensiones y comandos), 800-1 000 para primer script (con dos listados de código, sin tablas/diagramas).',\n", "  'Diagramas mermaid simples requeridos para ilustrar flujos o estructuras clave (pila, cola, chaining funcional, matrices).',\n", "  'Los diagramas de flujo y temporales deben ser en formato mermaid y limitar nodos/hitos a lo imprescindible para la comprensión del bloque.',\n", "  'Clasificación obligatoria de métodos de arrays en tablas comparativas (efecto sobre longitud, copia o referencia, complejidad temporal).',\n", "  'Se utilizará el patrón de construir manualmente nodos y estructuras antes de encapsular la lógica en clases (Node y LinkedList, TreeNode).',\n", "  'Snippets de micro-benchmark (performance.now()) deben emplearse al tratar eficiencia para ilustrar diferencias entre implementaciones.'],\n", " 9: ['No se permite aún hablar de métricas de complejidad ni balanceo antes del epígrafe correspondiente; se deben respetar estos límites narrativos.',\n", "  'Se recomienda declarar variables con const por defecto, usar let si se requiere mutabilidad, y evitar var salvo motivos concretos.',\n", "  'Uso de medición de tiempos (console.time) recomendado cuando se compare eficiencia en ejemplos relevantes.',\n", "  'Tablas para versiones de ECMAScript y extensiones de VS Code deben usarse cuando se traten esos temas.',\n", "  'Se adopta el pseudocódigo con mayúsculas para palabras reservadas, sangrías y comentarios con // como convención general.',\n", "  'Ejemplos de arrays deben cubrir tanto Node.js como navegador.',\n", "  'Los ejercicios algorítmicos deben requerir manipular arreglos/matrices sin estructuras auxiliares externas cuando el objetivo sea afianzar lógica interna.',\n", "  'Los ejercicios de pseudocódigo deben incluir micro-ejemplo de cada estructura básica (secuencia, selección, repetición) y al menos un ejercicio integral con pseudocódigo y diagrama mermaid simple (menos de diez nodos).',\n", "  'Los códigos de ejemplo en todos los subtemas se implementarán en JavaScript, adoptando clases y sintaxis moderna (ES6+).',\n", "  'Cada transformación o estructura debe incluir al menos un ejemplo de código comentado y ejercicio integrado.',\n", "  'Los ejercicios deberán solicitar implementaciones iterativas de métodos y evitar explícitamente el uso de variables globales.',\n", "  'Editor y entorno recomendados: Visual Studio Code y Node.js (se usará ambos para todo el curso).',\n", "  'Se requiere introducir y usar diagramas Mermaid para visualización de nodos y estructuras enlazadas (listas y árboles).',\n", "  'Buenas prácticas como validación de índices, manejo de estructuras vacías y ruptura de referencias se considerarán estándar en el manejo de listas y árboles.',\n", "  'Se recomienda mostrar y justificar la complejidad temporal/espacial mediante micro-tablas asociadas a cada operación fundamental.',\n", "  'Se adopta camelCase como convención para nombrar variables.',\n", "  'Las comparativas entre estructuras deberán presentarse en tablas de costes (arrays vs listas, listas vs árboles, DFS vs BFS).',\n", "  'Extensión esperada por sección: 1 100-1 300 palabras para introducción (con pseudocódigo y diagrama), 950-1 150 palabras para historia de JavaScript (con tabla y diagrama opcional), 850-1 050 para instalación (con tabla de extensiones y comandos), 800-1 000 para primer script (con dos listados de código, sin tablas/diagramas).',\n", "  'Diagramas mermaid simples requeridos para ilustrar flujos o estructuras clave (pila, cola, chaining funcional, matrices).',\n", "  'Los diagramas de flujo y temporales deben ser en formato mermaid y limitar nodos/hitos a lo imprescindible para la comprensión del bloque.',\n", "  'Clasificación obligatoria de métodos de arrays en tablas comparativas (efecto sobre longitud, copia o referencia, complejidad temporal).',\n", "  'Se utilizará el patrón de construir manualmente nodos y estructuras antes de encapsular la lógica en clases (Node y LinkedList, TreeNode).',\n", "  'Snippets de micro-benchmark (performance.now()) deben emplearse al tratar eficiencia para ilustrar diferencias entre implementaciones.']}"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["await content_generator.create_and_store_memories_for_index(INDEX_ID)"]}, {"cell_type": "code", "execution_count": 8, "id": "d8a2fe42", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-11 12:42:06,078 - logger - INFO - Entered in async session\n", "2025-07-11 12:42:06,082 - logger - INFO - Indice inside get subject index is, status=<IndiceStatus.CONTENT_GENERATION: 'CONTENT_GENERATION'> is_displayed=False created_at=datetime.datetime(2025, 6, 30, 13, 39, 58, 429390) id=48 order_id=4 version=1 created_by='ia_gen_user' updated_by='ia_gen_user' updated_at=datetime.datetime(2025, 7, 2, 9, 6, 25, 403435)\n", "2025-07-11 12:42:06,086 - logger - INFO - <PERSON><PERSON>ques inside get subject index is, [<PERSON><PERSON><PERSON>(name='Fundamentos de Programación y Estructuras de Datos en JavaScript', indice_id=48, created_by='ia_gen_user', updated_by=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 34060), id=63, position=1, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 34220))]\n", "2025-07-11 12:42:06,087 - logger - INFO - Asignatura is, nombre='Introducción a la programación, algoritmos y estructuras de datos con javascript - Informatica' estructura=Estructura(bloques_tematicos=[BloqueTematico(nombre='Fundamentos de Programación y Estructuras de Datos en JavaScript', temas=[Tema(nombre='Introducción al pensamiento algorítmico y al entorno JavaScript', epigrafes=['Conceptos de algoritmo y pseudocódigo', 'Historia y evolución de JavaScript', 'Instalación del entorno de desarrollo (Node.js, VS Code)', 'Primer script: \"Hola, mundo\" en consola y navegador']), <PERSON><PERSON>(nombre='Variables, tipos de datos y operadores en JavaScript', epigrafes=['Declaración de variables (var, let, const)', 'Tipos primitivos y coerción de datos', 'Operadores aritméticos, lógicos y comparativos', 'Entrada y salida básica con prompt, alert y console.log']), Te<PERSON>(nombre='Arrays y métodos funcionales', epigrafes=['Creación y mutación de arreglos', 'Métodos iterativos forEach, map, filter, reduce', 'Pilas y colas implementadas con arrays', 'Declaración y recorrido de matrices multidimensionales']), Tema(nombre='Tablas hash y conjuntos', epigrafes=['Fundamentos de hashing y funciones hash', 'Resolución de colisiones (encadenamiento y open addressing)', 'Implementación de Map, Set, WeakMap y WeakSet', 'Aplicaciones y análisis de rendimiento']), Tema(nombre='Recursión, búsqueda y ordenación', epigrafes=['Principios de recursión y diseño de casos base', 'Búsqueda lineal y búsqueda binaria', 'Algoritmos de ordenación sencillos (bubble, selection, insertion)', 'QuickSort y MergeSort: enfoque divide-and-conquer']), Tema(nombre='Calidad de software, pruebas y control de versiones', epigrafes=['Depuración con DevTools y Node Inspector', 'Pruebas unitarias y de integración con Jest', 'Manejo de errores y excepciones en JavaScript', 'Uso de Git y GitHub en flujo colaborativo']), Tema(nombre='Control de flujo y estructuras condicionales', epigrafes=['Estructuras de decisión if, else y switch', 'Bucles for, while y do-while', 'Control de bucle (break, continue) y patrones de iteración', 'Buenas prácticas para evitar bucles infinitos']), Tema(nombre='Funciones y modularidad', epigrafes=['Declaración y expresión de funciones', 'Parámetros, retorno y funciones flecha', 'Closures y alcance léxico', 'Módulos ES6: import y export']), Tema(nombre='Complejidad algorítmica y análisis Big-O', epigrafes=['Concepto de eficiencia temporal y espacial', 'Reglas para determinar la notación Big-O', 'Casos mejor, promedio y peor', 'Herramientas de benchmarking en JavaScript']), Tema(nombre='Listas enlazadas y árboles binarios', epigrafes=['Implementación de nodos y punteros en JS', 'Lista enlazada simple: inserción y eliminación', 'Estructura de árbol binario y tipos de recorrido', 'Análisis de complejidad de listas y árboles'])])])\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://cohere-rerank-v3-5-svhyz.swedencentral.models.ai.azure.com/v2/rerank \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://cohere-rerank-v3-5-svhyz.swedencentral.models.ai.azure.com/v2/rerank \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://cohere-rerank-v3-5-svhyz.swedencentral.models.ai.azure.com/v2/rerank \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://cohere-rerank-v3-5-svhyz.swedencentral.models.ai.azure.com/v2/rerank \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://cohere-rerank-v3-5-svhyz.swedencentral.models.ai.azure.com/v2/rerank \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://cohere-rerank-v3-5-svhyz.swedencentral.models.ai.azure.com/v2/rerank \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://cohere-rerank-v3-5-svhyz.swedencentral.models.ai.azure.com/v2/rerank \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://cohere-rerank-v3-5-svhyz.swedencentral.models.ai.azure.com/v2/rerank \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://cohere-rerank-v3-5-svhyz.swedencentral.models.ai.azure.com/v2/rerank \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://cohere-rerank-v3-5-svhyz.swedencentral.models.ai.azure.com/v2/rerank \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://cohere-rerank-v3-5-svhyz.swedencentral.models.ai.azure.com/v2/rerank \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://cohere-rerank-v3-5-svhyz.swedencentral.models.ai.azure.com/v2/rerank \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://cohere-rerank-v3-5-svhyz.swedencentral.models.ai.azure.com/v2/rerank \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://cohere-rerank-v3-5-svhyz.swedencentral.models.ai.azure.com/v2/rerank \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://cohere-rerank-v3-5-svhyz.swedencentral.models.ai.azure.com/v2/rerank \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://cohere-rerank-v3-5-svhyz.swedencentral.models.ai.azure.com/v2/rerank \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://cohere-rerank-v3-5-svhyz.swedencentral.models.ai.azure.com/v2/rerank \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n"]}], "source": ["generated_content = await content_generator.generate_topic_pipeline(INDEX_ID, 132)"]}, {"cell_type": "code", "execution_count": 9, "id": "13bafc9c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["## Calidad de software, pruebas y control de versiones\n", "\n", "### Depuración con DevTools y Node Inspector\n", "\n", "<fuentes>[0]</fuentes>\n", "\n", "La depuración representa la primera línea de defensa en el aseguramiento de la calidad del software, permitiendo a los desarrolladores inspeccionar el comportamiento real del código durante su ejecución. Esta práctica resulta fundamental para validar que las estructuras de datos, algoritmos de ordenación y funciones recursivas implementadas en los temas anteriores funcionen correctamente bajo diferentes condiciones.\n", "\n", "La importancia de la depuración radica en su capacidad para revelar discrepancias entre el comportamiento esperado y el real del código. A diferencia de las pruebas automatizadas que veremos más adelante, la depuración nos permite observar el estado interno del programa en tiempo real, siguiendo paso a paso la ejecución y examinando el valor de las variables en cada momento.\n", "\n", "#### Herramientas de inspección: navegador y Node.js\n", "\n", "JavaScript ofrece dos entornos principales de ejecución que comparten el mismo motor V8 pero proporcionan herramientas de depuración ligeramente diferentes: los **DevTools** del navegador y el **Node Inspector** para aplicaciones de servidor.\n", "\n", "Los DevTools del navegador están integrados en Chrome, Firefox, Safari y Edge, ofreciendo una interfaz gráfica completa para inspeccionar el código que se ejecuta en el contexto del navegador. <PERSON>r su parte, Node Inspector utiliza el mismo protocolo de depuración del navegador, pero adaptado para aplicaciones de servidor, lo que permite aprovechar la familiaridad de la interfaz de DevTools para depurar código backend.\n", "\n", "La similitud entre ambas herramientas no es casual: Node.js utiliza el motor V8 de Chrome, por lo que las capacidades de inspección son prácticamente idénticas. La principal diferencia radica en el contexto de ejecución: mientras que los DevTools del navegador tienen acceso directo a la DOM y a las APIs web, Node Inspector proporciona acceso a las APIs del sistema operativo y al entorno de servidor.\n", "\n", "| **Funcionalidad** | **DevTools navegador** | **Node Inspector** |\n", "|:------------------|:----------------------|:------------------|\n", "| **Puntos de ruptura** | Interfaz gráfica integrada | Interfaz gráfica a través del navegador |\n", "| **Inspección de variables** | Panel de Scope nativo | Panel de Scope vía DevTools |\n", "| **Consola interactiva** | Consola del navegador | Consola del navegador conectada |\n", "| **<PERSON><PERSON><PERSON><PERSON>ndi<PERSON>nto** | Timeline/Performance nativo | Timeline/Performance remoto |\n", "| **Conexión** | Automática | Requiere `--inspect` flag |\n", "\n", "*Tabla 1. Comparación entre DevTools del navegador y Node Inspector*\n", "\n", "#### Flujo básico de depuración\n", "\n", "El proceso de depuración sigue un flujo sistemático que comienza con la inserción de puntos de ruptura (**breakpoints**). Estos pueden establecerse de dos formas principales: insertando la instrucción `debugger;` directamente en el código o utilizando la interfaz gráfica para colocar breakpoints en líneas específicas.\n", "\n", "Una vez que la ejecución se detiene en un breakpoint, el depurador nos permite examinar el **scope chain** (cadena de alcance) y la **pila de llamadas** (call stack). El scope chain muestra todas las variables disponibles en el contexto actual, incluidas las variables locales, parámetros de función y variables del ámbito superior. La pila de llamadas revela la secuencia de llamadas de función que llevó al punto actual de ejecución.\n", "\n", "Las **watch expressions** (expresiones de seguimiento) permiten monitorear valores específicos durante la ejecución, actualizándose automáticamente cuando cambian. La **evaluación on-the-fly** en la consola nos permite ejecutar código JavaScript en el contexto actual, modificar variables o probar expresiones sin alterar el código fuente.\n", "\n", "Consideremos un ejemplo práctico con una implementación defectuosa de búsqueda binaria:\n", "\n", "```javascript\n", "function busquedaBinaria(arr, objetivo) {\n", "    let inicio = 0;\n", "    let fin = arr.length; // Error: debería ser arr.length - 1\n", "    \n", "    while (inicio <= fin) {\n", "        debugger; // Punto de ruptura para inspección\n", "        \n", "        const medio = Math.floor((inicio + fin) / 2);\n", "        const valorMedio = arr[medio];\n", "        \n", "        if (valorMedio === objetivo) {\n", "            return medio;\n", "        } else if (valorMedio < objetivo) {\n", "            inicio = medio + 1;\n", "        } else {\n", "            fin = medio - 1;\n", "        }\n", "    }\n", "    \n", "    return -1;\n", "}\n", "\n", "// <PERSON>je<PERSON><PERSON> de uso problemático\n", "const numeros = [1, 3, 5, 7, 9, 11, 13];\n", "console.log(busquedaBinaria(numeros, 7));\n", "```\n", "\n", "Para depurar este código, colocaríamos un breakpoint en la línea `debugger;` y otro en `const valorMedio = arr[medio];`. Al ejecutar la función, podremos observar cómo el valor de `fin` inicialmente es 7 (longitud del array) en lugar de 6 (índice máximo), causando un acceso fuera de los límites del array.\n", "\n", "#### Herramientas de análisis de rendimiento\n", "\n", "Las herramientas de rendimiento permiten cuantificar los cuellos de botella del código, complementando el análisis teórico de complejidad Big-O con mediciones reales. El **Timeline** (o **Performance monitor** en versiones más recientes) registra la ejecución del código, mostrando cuánto tiempo consume cada función.\n", "\n", "Para el ejemplo anterior, podríamos medir el rendimiento de diferentes implementaciones de búsqueda:\n", "\n", "```javascript\n", "// Medición de rendimiento\n", "const arrayGrande = Array.from({length: 100000}, (_, i) => i * 2);\n", "\n", "console.time('Búsqueda binaria');\n", "busquedaBinaria(arrayGrande, 50000);\n", "console.timeEnd('Búsqueda binaria');\n", "\n", "console.time('Búsqueda lineal');\n", "arrayGrande.indexOf(50000);\n", "console.timeEnd('Búsqueda lineal');\n", "```\n", "\n", "#### Depuración remota con Node Inspector\n", "\n", "Para aplicaciones Node.js, la depuración remota se activa utilizando el flag `--inspect` al ejecutar el script:\n", "\n", "```bash\n", "node --inspect servidor.js\n", "```\n", "\n", "Esto inicia el servidor de depuración en el puerto 9229 por defecto. Posteriormente, se puede conectar desde Chrome navegando a `chrome://inspect` y seleccionando el proceso Node.js en ejecución.\n", "\n", "Los **breakpoints condicionales** resultan especialmente útiles en este contexto, ya que permiten pausar la ejecución solo cuando se cumple una condición específica:\n", "\n", "```javascript\n", "// Breakpoint condicional: pausar solo si el array está vacío\n", "function procesarDatos(datos) {\n", "    // Condición del breakpoint: datos.length === 0\n", "    if (datos.length === 0) {\n", "        throw new Error('Array vacío');\n", "    }\n", "    // ... resto del código\n", "}\n", "```\n", "\n", "#### Buenas prácticas de depuración\n", "\n", "El uso selectivo de instrucciones de logging es crucial para mantener un código limpio y eficiente. `console.debug()` debe preferirse sobre `console.log()` para mensajes de depuración, ya que puede deshabilitarse en producción mediante la configuración del navegador.\n", "\n", "Los **source maps** permiten depurar código transpilado o minificado, manteniendo la correspondencia entre el código fuente y el código ejecutado. En entornos de producción, es fundamental evitar instrucciones `console` que puedan afectar el rendimiento o exponer información sensible.\n", "\n", "La depuración sistemática debe documentarse, registrando los hallazgos y las soluciones aplicadas. Esto crea un conocimiento acumulativo que facilita la resolución de problemas similares en el futuro.\n", "\n", "¿Qué pasaría si pudiéramos formalizar estos hallazgos de depuración en un conjunto de verificaciones automatizadas que se ejecuten cada vez que modificamos el código? Esta necesidad nos lleva naturalmente hacia las pruebas automatizadas, donde podemos validar sistemáticamente el comportamiento correcto sin intervención manual.\n", "\n", "### Pruebas unitarias y de integración con Jest\n", "\n", "<fuentes>[1, 7]</fuentes>\n", "\n", "Las pruebas automatizadas formalizan el proceso de verificación del comportamiento correcto que hemos explorado empíricamente mediante la depuración. Mientras que la depuración nos permite observar qué sucede durante la ejecución, las pruebas automatizadas nos permiten verificar que el código se comporte según nuestras expectativas de manera consistente y repetible.\n", "\n", "La automatización de pruebas responde a una necesidad fundamental: garantizar que el código funcione correctamente no solo en el momento de su creación, sino también después de modificaciones futuras. Esta práctica establece las bases de una cultura **test-first** que respaldará las colaboraciones futuras en sistemas de control de versiones.\n", "\n", "#### Diferencias entre pruebas unitarias e integración\n", "\n", "Las **pruebas unitarias** se enfocan en verificar el comportamiento de una función o módulo específico de manera aislada, sin dependencias externas. Su alcance es limitado y específico, permitiendo identificar exactamente dónde ocurre un fallo. Las **pruebas de integración**, por el contrario, verifican que múltiples componentes trabajen correctamente en conjunto, validando la interacción entre diferentes módulos del sistema.\n", "\n", "La distinción es crucial: una prueba unitaria validaría que una función de ordenación específica funcione correctamente con diferentes tipos de entrada, mientras que una prueba de integración verificaría que el sistema completo de procesamiento de datos funcione correctamente desde la entrada hasta la salida.\n", "\n", "#### Ecosist<PERSON> Jest\n", "\n", "Jest representa el marco de pruebas más popular para JavaScript, proporcionando una solución completa que incluye el corredor de pruebas, las aserciones y las utilidades de mockeo. Su instalación es sencilla mediante npm:\n", "\n", "```bash\n", "npm install --save-dev jest\n", "```\n", "\n", "El comando de ejecución se configura en el archivo `package.json`:\n", "\n", "```json\n", "{\n", "  \"scripts\": {\n", "    \"test\": \"jest\"\n", "  }\n", "}\n", "```\n", "\n", "Jest utiliza un sistema de auto-descubrimiento que ejecuta automáticamente todos los archivos que terminan en `.test.js` o `.spec.js`, o que se encuentran en carpetas llamadas `__tests__`.\n", "\n", "#### Anatomía de un archivo de prueba\n", "\n", "La estructura básica de una prueba Jest sigue un patrón reconocible que organiza las pruebas en bloques `describe` (para agrupar pruebas relacionadas) y bloques `it` (para pruebas individuales):\n", "\n", "```javascript\n", "// arrayUtils.test.js\n", "describe('Utilidades de arrays', () => {\n", "    it('debería filtrar elementos pares correctamente', () => {\n", "        // Arrange: preparar los datos\n", "        const numeros = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];\n", "        const resultadoEsperado = [2, 4, 6, 8, 10];\n", "        \n", "        // Act: ejecutar la función\n", "        const resultado = filtrarPares(numeros);\n", "        \n", "        // Assert: verificar el resultado\n", "        expect(resultado).toEqual(resultadoEsperado);\n", "    });\n", "    \n", "    it('de<PERSON><PERSON> manejar arrays vacíos', () => {\n", "        const arrayVacio = [];\n", "        const resultado = filtrarPares(arrayVacio);\n", "        expect(resultado).toEqual([]);\n", "    });\n", "    \n", "    it('de<PERSON><PERSON> lanzar error con entrada inválida', () => {\n", "        expect(() => filtrarPares(null)).toThrow('Array requerido');\n", "    });\n", "});\n", "\n", "// Función bajo prueba\n", "function filtrarPares(array) {\n", "    if (!Array.isArray(array)) {\n", "        throw new Error('<PERSON><PERSON><PERSON> requerido');\n", "    }\n", "    \n", "    return array.filter(numero => numero % 2 === 0);\n", "}\n", "```\n", "\n", "Este ejemplo implementa la estructura **AAA** (Arrange-Act-Assert): preparar los datos de prueba, ejecutar la función y verificar los resultados.\n", "\n", "#### Matchers principales\n", "\n", "Jest proporciona una amplia gama de matchers para diferentes tipos de verificaciones:\n", "\n", "| **Matcher** | **Propós<PERSON>** | **Ejemplo** |\n", "|:------------|:-------------|:------------|\n", "| `toBe()` | Igualdad estricta (===) | `expect(resultado).toBe(5)` |\n", "| `toEqual()` | Igualdad profunda de objetos | `expect(objeto).toEqual({a: 1})` |\n", "| `toThrow()` | Verificar que se lance excepción | `expect(() => funcion()).toThrow()` |\n", "| `toContain()` | Verificar inclusión en array | `expect(array).toContain(elemento)` |\n", "| `toBeNull()` | Verificar valor null | `expect(valor).toBeNull()` |\n", "| `toBeDefined()` | Verificar que esté definido | `expect(variable).toBeDefined()` |\n", "\n", "*Tabla 2. Matchers esenciales de Jest*\n", "\n", "#### <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\n", "\n", "El código asíncrono requiere técnicas específicas de prueba para manejar correctamente las promesas y callbacks. Jest ofrece tres enfoques principales:\n", "\n", "**<PERSON><PERSON><PERSON> async/await:**\n", "```javascript\n", "describe('Operaciones asíncronas', () => {\n", "    it('debería obtener datos del servidor', async () => {\n", "        const datos = await obtenerDatosServidor();\n", "        expect(datos).toBeDefined();\n", "        expect(datos.length).toBeGreaterThan(0);\n", "    });\n", "    \n", "    it('de<PERSON><PERSON> manejar errores de red', async () => {\n", "        await expect(obtenerDatosInvalidos()).rejects.toThrow('Error de red');\n", "    });\n", "});\n", "```\n", "\n", "**<PERSON><PERSON><PERSON> resolves/rejects:**\n", "```javascript\n", "it('debería resolver promesa exitosa', () => {\n", "    return expect(promesaExitosa()).resolves.toBe('éxito');\n", "});\n", "\n", "it('debería rechazar promesa fallida', () => {\n", "    return expect(promesa<PERSON><PERSON><PERSON>()).rejects.toThrow('<PERSON><PERSON><PERSON> esperado');\n", "});\n", "```\n", "\n", "#### Pruebas de integración con mocks\n", "\n", "Las pruebas de integración ligeras utilizan mocks para simular dependencias externas como archivos o peticiones HTTP:\n", "\n", "```javascript\n", "// Ejemplo de prueba de integración con mock\n", "jest.mock('fs');\n", "const fs = require('fs');\n", "\n", "describe('Procesador de archivos', () => {\n", "    it('debería procesar archivo correctamente', async () => {\n", "        // Simular lectura de archivo\n", "        fs.readFile.mockResolvedValue('dato1,dato2,dato3');\n", "        \n", "        const resultado = await procesarArchivo('datos.csv');\n", "        \n", "        expect(resultado).toEqual(['dato1', 'dato2', 'dato3']);\n", "        expect(fs.readFile).toHaveBeenCalledWith('datos.csv', 'utf8');\n", "    });\n", "});\n", "```\n", "\n", "#### Cobertura de código\n", "\n", "La cobertura de código mide qué porcentaje del código fuente es ejecutado durante las pruebas. Jest incluye esta funcionalidad activándola con el flag `--coverage`:\n", "\n", "```bash\n", "npm test -- --coverage\n", "```\n", "\n", "El reporte de cobertura incluye cuatro métricas principales:\n", "- **Líneas**: porcentaje de líneas ejecutadas\n", "- **Funciones**: porcentaje de funciones invocadas\n", "- **Ramas**: porcentaje de ramas condicionales ejecutadas\n", "- **Declaraciones**: porcentaje de declaraciones ejecutadas\n", "\n", "Una cobertura alta no garantiza la calidad de las pruebas, pero una cobertura baja indica áreas del código que no están siendo verificadas.\n", "\n", "#### Integración con desarrollo colaborativo\n", "\n", "Las pruebas automatizadas se integran naturalmente con los flujos de trabajo de desarrollo colaborativo. En cada **pull request**, los tests se ejecutan automáticamente, asegurando que los cambios propuestos no rompan la funcionalidad existente. Esta práctica de **integración continua** será fundamental en el trabajo con sistemas de control de versiones.\n", "\n", "La escritura de pruebas efectivas requiere pensar en los casos límite y las condiciones de error que el código debe manejar. ¿Cómo podemos asegurar que nuestras pruebas cubran adequadamente estos escenarios de fallo? La respuesta radica en un manejo robusto de errores y excepciones, tema que abordaremos a continuación.\n", "\n", "### Manejo de errores y excepciones en JavaScript\n", "\n", "<fuentes>[2, 3, 4, 5, 6]</fuentes>\n", "\n", "El manejo adecuado de errores representa un aspecto crucial en el desarrollo de software robusto, permitiendo que las aplicaciones respondan de manera elegante ante condiciones inesperadas. A diferencia de los errores de lógica que detectamos mediante depuración, las excepciones representan condiciones anómalas que pueden surgir durante la ejecución normal del programa.\n", "\n", "La inevitabilidad de los fallos en sistemas complejos requiere una estrategia proactiva para anticipar, detectar y gestionar estas situaciones. Es fundamental distinguir entre tres conceptos relacionados pero diferentes: **errores** (problemas en la lógica del código), **excepciones** (condiciones anómalas manejables) y **bugs** (comportamientos no intencionados del programa).\n", "\n", "#### Mecanismos básicos del lenguaje\n", "\n", "JavaScript proporciona los mecanismos fundamentales `throw`, `try`, `catch` y `finally` para el manejo de excepciones. La instrucción `throw` permite lanzar una excepción con cualquier valor, aunque la mejor práctica es utilizar objetos `Error` para proporcionar información contextual:\n", "\n", "```javascript\n", "// Lanzar excepciones con diferentes tipos\n", "throw \"Error simple\";           // No recomendado\n", "throw 42;                      // No recomendado\n", "throw new Error(\"Error descriptivo\"); // Recomendado\n", "throw new TypeError(\"Tipo inválido\"); // Específico y recomendado\n", "```\n", "\n", "Los bloques `try/catch/finally` permiten capturar y manejar excepciones de manera controlada:\n", "\n", "```javascript\n", "function procesarDatos(datos) {\n", "    try {\n", "        // Código que puede fallar\n", "        if (!datos || datos.length === 0) {\n", "            throw new Error('<PERSON>tos requeridos');\n", "        }\n", "        \n", "        return datos.map(item => item.toUpperCase());\n", "    } catch (error) {\n", "        // Manejo específico del error\n", "        console.error('Error procesando datos:', error.message);\n", "        return [];\n", "    } finally {\n", "        // Código que siempre se ejecuta\n", "        console.log('Procesamiento completado');\n", "    }\n", "}\n", "```\n", "\n", "La propagación implícita de excepciones significa que si una función no captura una excepción, esta se propaga automáticamente hacia la función que la invocó, continuando hasta encontrar un bloque `catch` o hasta llegar al nivel superior del programa.\n", "\n", "#### Clases de error personalizadas\n", "\n", "La creación de clases de error personalizadas permite aportar contexto específico del dominio de la aplicación, facilitando el manejo diferenciado de distintos tipos de errores:\n", "\n", "```javascript\n", "class ValidationError extends Error {\n", "    constructor(message, field) {\n", "        super(message);\n", "        this.name = 'ValidationError';\n", "        this.field = field;\n", "    }\n", "}\n", "\n", "class NetworkError extends Error {\n", "    constructor(message, statusCode) {\n", "        super(message);\n", "        this.name = 'NetworkError';\n", "        this.statusCode = statusCode;\n", "    }\n", "}\n", "\n", "// Uso de errores personalizados\n", "function validarUsuario(usuario) {\n", "    if (!usuario.email) {\n", "        throw new ValidationError('Email requerido', 'email');\n", "    }\n", "    \n", "    if (!usuario.email.includes('@')) {\n", "        throw new ValidationError('Email inválido', 'email');\n", "    }\n", "}\n", "\n", "// <PERSON><PERSON><PERSON> di<PERSON>enciado\n", "try {\n", "    validarUsuario({ email: 'usuario-invalido' });\n", "} catch (error) {\n", "    if (error instanceof ValidationError) {\n", "        console.log(`Error de validación en ${error.field}: ${error.message}`);\n", "    } else {\n", "        console.log('Error desconocido:', error.message);\n", "    }\n", "}\n", "```\n", "\n", "#### Integración con operaciones asíncronas\n", "\n", "Las operaciones asíncronas requieren técnicas específicas para el manejo de errores. Las promesas pueden rechazarse, y este rechazo debe capturarse apropiadamente:\n", "\n", "```javascript\n", "// <PERSON><PERSON><PERSON> de errores en promesas\n", "function obtenerDatosServidor(url) {\n", "    return fetch(url)\n", "        .then(response => {\n", "            if (!response.ok) {\n", "                throw new NetworkError(\n", "                    `Error HTTP ${response.status}`, \n", "                    response.status\n", "                );\n", "            }\n", "            return response.json();\n", "        })\n", "        .catch(error => {\n", "            console.error('Error obteniendo datos:', error.message);\n", "            throw error; // Re-lanzar para permitir manejo upstream\n", "        });\n", "}\n", "\n", "// Uso con async/await\n", "async function procesarDatosServidor(url) {\n", "    try {\n", "        const datos = await obtenerDatosServidor(url);\n", "        return datos.map(item => item.nombre);\n", "    } catch (error) {\n", "        if (error instanceof NetworkError) {\n", "            console.log(`Error de red: ${error.statusCode}`);\n", "            return [];\n", "        }\n", "        throw error;\n", "    }\n", "}\n", "```\n", "\n", "#### Captura global de errores\n", "\n", "Para errores no capturados, JavaScript proporciona mecanismos de captura global que actúan como red de seguridad:\n", "\n", "```javascript\n", "// En Node.js\n", "process.on('uncaughtException', (error) => {\n", "    console.error('Excepción no capturada:', error);\n", "    // Registrar el error y cerrar gracefully\n", "    process.exit(1);\n", "});\n", "\n", "process.on('unhandledRejection', (reason, promise) => {\n", "    console.error('Promesa rechazada no manejada:', reason);\n", "    // Registrar la promesa rechazada\n", "});\n", "\n", "// En navegador\n", "window.onerror = (message, source, lineno, colno, error) => {\n", "    console.error('Error global:', { message, source, lineno, error });\n", "    return true; // Prevenir el comportamiento por defecto\n", "};\n", "\n", "window.addEventListener('unhandledrejection', (event) => {\n", "    console.error('<PERSON><PERSON><PERSON> rechazada:', event.reason);\n", "    event.preventDefault();\n", "});\n", "```\n", "\n", "#### Estrategias de logging estructurado\n", "\n", "El registro estructurado de errores facilita el análisis y la resolución de problemas en producción:\n", "\n", "```javascript\n", "class Logger {\n", "    static logError(error, context = {}) {\n", "        const errorInfo = {\n", "            timestamp: new Date().toISOString(),\n", "            message: error.message,\n", "            name: error.name,\n", "            stack: error.stack,\n", "            context: context,\n", "            severity: 'error'\n", "        };\n", "        \n", "        // En desarrollo\n", "        if (process.env.NODE_ENV === 'development') {\n", "            console.error('Error:', errorInfo);\n", "        }\n", "        \n", "        // En producción, enviar a servicio de logging\n", "        // this.sendToLoggingService(errorInfo);\n", "    }\n", "    \n", "    static logWarning(message, context = {}) {\n", "        const warningInfo = {\n", "            timestamp: new Date().toISOString(),\n", "            message: message,\n", "            context: context,\n", "            severity: 'warning'\n", "        };\n", "        \n", "        console.warn('Warning:', warningInfo);\n", "    }\n", "}\n", "```\n", "\n", "#### Integración con pruebas\n", "\n", "Las pruebas deben verificar tanto el comportamiento normal como el manejo de errores:\n", "\n", "```javascript\n", "describe('Validación de usuarios', () => {\n", "    it('debería lanzar ValidationError para email faltante', () => {\n", "        const usuarioSinEmail = { nombre: '<PERSON>' };\n", "        \n", "        expect(() => validarUsuario(usuarioSinEmail))\n", "            .toThrow(ValidationError);\n", "    });\n", "    \n", "    it('debería lanzar error específico para email inválido', () => {\n", "        const usuarioEmailInvalido = { email: 'sin-arroba' };\n", "        \n", "        expect(() => validarUsuario(usuarioEmailInvalido))\n", "            .toThrow('<PERSON><PERSON> inválido');\n", "    });\n", "    \n", "    it('debería incluir campo en error de validación', () => {\n", "        try {\n", "            validarUsuario({ email: '' });\n", "        } catch (error) {\n", "            expect(error.field).toBe('email');\n", "        }\n", "    });\n", "});\n", "```\n", "\n", "#### Buenas prácticas\n", "\n", "El manejo efectivo de errores requiere seguir principios establecidos:\n", "\n", "1. **No usar excepciones para control de flujo**: las excepciones deben reservarse para condiciones verdaderamente excepcionales\n", "2. **Mantener mensajes claros**: los mensajes de error deben ser informativos pero no exponer información sensible\n", "3. **<PERSON><PERSON><PERSON> sensibles**: nunca incluir credenciales o información personal en logs de error\n", "4. **Fallar r<PERSON>o**: detectar y reportar errores tan pronto como sea posible\n", "5. **Documentar errores esperados**: especificar qué errores puede lanzar cada función\n", "\n", "El manejo robusto de errores garantiza que nuestras aplicaciones puedan recuperarse de situaciones imprevistas y proporcionar información útil para el diagnóstico. Sin embargo, todas estas salvaguardas y verificaciones deben residir en un entorno de desarrollo colaborativo que permita compartir, revisar y mantener el código de manera efectiva. Esto nos lleva al control de versiones, herramienta fundamental para el desarrollo de software en equipo.\n", "\n", "### Uso de Git y GitHub en flujo colaborativo\n", "\n", "<fuentes>[8]</fuentes>\n", "\n", "El desarrollo de software moderno es fundamentalmente colaborativo, requiriendo herramientas que permitan a múltiples desarrolladores trabajar simultáneamente en el mismo proyecto sin conflictos. Los sistemas de control de versiones distribuidos, específicamente Git combinado con plataformas como GitHub, han revolucionado la forma en que los equipos desarrollan software de manera coordinada.\n", "\n", "La problemática del \"envía-me-el-zip\" ilustra claramente la necesidad de estos sistemas: cuando múltiples desarrolladores intentan trabajar en el mismo proyecto intercambiando archivos por email o carpetas compartidas, inevitablemente surgen conflictos de versiones, pérdida de cambios y dificultades para rastrear quién modificó qué y cuándo.\n", "\n", "#### Conceptos fundamentales de Git\n", "\n", "Git maneja el historial del proyecto mediante **instantáneas** (snapshots) llamadas **commits**, donde cada commit representa un estado completo del proyecto en un momento específico. A diferencia de otros sistemas que almacenan diferencias entre versiones, Git almacena el estado completo de cada archivo en cada commit.\n", "\n", "Las **referencias** son punteros ligeros que permiten navegar por el historial del proyecto. La referencia más común es `HEAD`, que apunta al commit actual, y las ramas (branches) que representan líneas de desarrollo independientes.\n", "\n", "El **área de preparación** (staging area) actúa como un espacio intermedio donde se preparan los cambios antes de crear un commit. Este concepto permite seleccionar específicamente qué cambios incluir en cada commit, manteniendo un historial limpio y coherente.\n", "\n", "Las **ramas ligeras** de Git permiten crear líneas de desarrollo paralelas sin el overhead de otros sistemas de control de versiones. El flujo **feature branch** se ha convertido en el estándar de la industria, donde cada nueva característica se desarrolla en una rama independiente que posteriormente se integra a la rama principal.\n", "\n", "#### Comandos esenciales en narrativa\n", "\n", "El flujo típico de trabajo con Git sigue una secuencia lógica que comienza con la inicialización o clonación de un repositorio:\n", "\n", "```bash\n", "# Inicializar un nuevo repositorio\n", "git init mi-proyecto\n", "cd mi-proyecto\n", "\n", "# O clonar un repositorio existente\n", "git clone https://github.com/usuario/repositorio.git\n", "cd repositorio\n", "```\n", "\n", "La verificación del estado del repositorio es fundamental antes de realizar cualquier acción:\n", "\n", "```bash\n", "# Verificar estado actual\n", "git status\n", "\n", "# Ver el historial de commits\n", "git log --oneline\n", "```\n", "\n", "El proceso de agregar cambios al área de preparación y crear commits sigue un patrón consistente:\n", "\n", "```bash\n", "# Agregar archivos específicos\n", "git add archivo1.js archivo2.js\n", "\n", "# Agregar todos los archivos modificados\n", "git add .\n", "\n", "# <PERSON><PERSON><PERSON> commit con mensaje descriptivo\n", "git commit -m \"Implementar algoritmo de búsqueda binaria\n", "\n", "- Agregar función busquedaBinaria con validación de entrada\n", "- Incluir pruebas unitarias para casos límite\n", "- <PERSON><PERSON><PERSON><PERSON> rendimiento para arrays grandes\"\n", "```\n", "\n", "El trabajo con ramas permite desarrollo paralelo sin interferencias:\n", "\n", "```bash\n", "# Crear nueva rama para feature\n", "git branch feature/ordenamiento-quicksort\n", "git checkout feature/ordenamiento-quicksort\n", "\n", "# O crear y cambiar en un solo comando\n", "git checkout -b feature/manejo-errores\n", "\n", "# Desarrollar en la rama...\n", "# Hacer commits...\n", "\n", "# <PERSON><PERSON><PERSON> a rama principal\n", "git checkout main\n", "\n", "# Integrar cambios\n", "git merge feature/manejo-errores\n", "```\n", "\n", "#### Buenas prácticas para mensajes de commit\n", "\n", "Los mensajes de commit efectivos siguen convenciones establecidas que facilitan la comprensión del historial del proyecto:\n", "\n", "```bash\n", "# Formato recomendado: imperativo, 50 caracteres máximo\n", "git commit -m \"Agregar validación de entrada a busquedaBinaria\"\n", "\n", "# Para cambios complejos, usar descripción extendida\n", "git commit -m \"Refactor<PERSON>r sistema de manejo de errores\n", "\n", "- Crear clases de error personalizadas para diferentes contextos\n", "- Implementar logging estructurado con niveles de severidad\n", "- Actualizar pruebas para cubrir casos de error\n", "- Documentar nuevas excepciones en README\n", "\n", "Fixes #123\"\n", "```\n", "\n", "#### Integración con GitHub\n", "\n", "GitHub transforma Git de una herramienta local en una plataforma de colaboración completa. Los **repositorios remotos** actúan como punto central de sincronización:\n", "\n", "```bash\n", "# Agregar repositorio remoto\n", "git remote add origin https://github.com/usuario/proyecto.git\n", "\n", "# Enviar cambios al repositorio remoto\n", "git push -u origin main\n", "\n", "# Obtener cambios del repositorio remoto\n", "git pull origin main\n", "```\n", "\n", "Los **forks** permiten crear copias personales de repositorios de otros usuarios, facilitando las contribuciones a proyectos open source. Los **pull requests** (PR) representan el mecanismo central para proponer cambios, permitiendo revisión de código y discusión antes de la integración.\n", "\n", "<mermaid>\n", "flowchart TD\n", "    A[Desarrollador crea rama] --> B[Desarrolla funcionalidad]\n", "    B --> C[<PERSON><PERSON> commits]\n", "    C --> D[Push a repositorio remoto]\n", "    D --> <PERSON>[<PERSON><PERSON> Request]\n", "    E --> F[Revisión de código]\n", "    F --> G{<PERSON><PERSON><PERSON>?}\n", "    G -->|Sí| H[CI ejecuta pruebas]\n", "    G -->|No| I[Solicita cambios]\n", "    I --> B\n", "    H --> J{<PERSON><PERSON><PERSON> pasan?}\n", "    J -->|Sí| K[Merge a rama principal]\n", "    J -->|No| L[Notifica fallo]\n", "    L --> B\n", "    K --> <PERSON>[Despliega a producción]\n", "</mermaid>\n", "\n", "*Figura 1. Flujo colaborativo con Git y GitHub incluyendo integración continua*\n", "\n", "#### Integración con calidad de software\n", "\n", "La verdadera potencia de Git y GitHub emerge cuando se integra con las prácticas de calidad establecidas en los epígrafes anteriores. Las **acciones de CI** (Continuous Integration) ejecutan automáticamente las pruebas Jest en cada pull request:\n", "\n", "```yaml\n", "# .github/workflows/ci.yml\n", "name: CI\n", "on: [push, pull_request]\n", "jobs:\n", "  test:\n", "    runs-on: ubuntu-latest\n", "    steps:\n", "      - uses: actions/checkout@v2\n", "      - name: <PERSON><PERSON> Node.js\n", "        uses: actions/setup-node@v2\n", "        with:\n", "          node-version: '18'\n", "      - run: npm install\n", "      - run: npm test\n", "```\n", "\n", "Las **políticas de protección de rama** aseguran que ningún código llegue a producción sin pasar las verificaciones establecidas:\n", "\n", "- Requerir revisión de código antes del merge\n", "- Exigir que las pruebas automatizadas pasen\n", "- Mantener el historial lineal mediante rebase\n", "- <PERSON><PERSON><PERSON> quién puede hacer push directo a la rama principal\n", "\n", "#### Gestión de conflictos\n", "\n", "Los conflictos de merge son una realidad inevitable en el desarrollo colaborativo. Consideremos un escenario típico donde dos desarrolladores modifican el mismo archivo:\n", "\n", "```javascript\n", "// Desarrollador A modifica arrayUtils.js\n", "function ordenarArray(arr) {\n", "    return arr.sort((a, b) => a - b); // Ordenación ascendente\n", "}\n", "\n", "// Desarrollador B modifica arrayUtils.js\n", "function ordenarArray(arr) {\n", "    return arr.sort((a, b) => b - a); // Ordenación descendente\n", "}\n", "```\n", "\n", "Git marca los conflictos en el archivo:\n", "\n", "```javascript\n", "function ordenarArray(arr) {\n", "<<<<<<< HEAD\n", "    return arr.sort((a, b) => a - b); // Ordenación ascendente\n", "=======\n", "    return arr.sort((a, b) => b - a); // Ordenación descendente\n", ">>>>>>> feature/ordenamiento-desc\n", "}\n", "```\n", "\n", "La resolución manual implica decidir qué cambios conservar y limpiar las marcas del conflicto:\n", "\n", "```javascript\n", "// Resolución: permitir especificar dirección\n", "function ordenarArray(arr, descendente = false) {\n", "    return descendente \n", "        ? arr.sort((a, b) => b - a)\n", "        : arr.sort((a, b) => a - b);\n", "}\n", "```\n", "\n", "#### Archivos de configuración útiles\n", "\n", "El archivo `.gitignore` especifica qué archivos y carpetas debe ignorar Git:\n", "\n", "```giti<PERSON>re\n", "# Dependencias\n", "node_modules/\n", "\n", "# Archivos de configuración local\n", ".env\n", ".env.local\n", "\n", "# Archivos de prueba\n", "coverage/\n", "*.log\n", "\n", "# Archivos del sistema\n", ".DS_Store\n", "Thumbs.db\n", "```\n", "\n", "El archivo `.gitattributes` normaliza el manejo de archivos entre diferentes sistemas operativos:\n", "\n", "```gitattributes\n", "# Normalizar finales de línea\n", "* text=auto\n", "\n", "# Archivos que siempre deben usar LF\n", "*.js text eol=lf\n", "*.json text eol=lf\n", "*.md text eol=lf\n", "```\n", "\n", "#### Seguridad básica\n", "\n", "La seguridad en Git y GitHub requiere consideraciones específicas:\n", "\n", "**Llaves SSH** proporcionan autenticación segura sin contraseñas:\n", "\n", "```bash\n", "# <PERSON>rar llave SSH\n", "ssh-keygen -t ed25519 -C \"<EMAIL>\"\n", "\n", "# Ag<PERSON>gar llave al agente SSH\n", "ssh-add ~/.ssh/id_ed25519\n", "\n", "# Configurar Git para usar SSH\n", "git remote set-<NAME_EMAIL>:usuario/repositorio.git\n", "```\n", "\n", "**Personal Access Tokens** permiten autenticación programática con permisos específicos, especialmente útiles para scripts y aplicaciones que interactúan con la API de GitHub.\n", "\n", "La integración entre Git, GitHub y las prácticas de calidad del software crea un ecosistema robusto donde el código se desarrolla, prueba y despliega de manera coordinada. Las pruebas automatizadas con Jest se ejecutan en cada pull request, el manejo de errores asegura que las aplicaciones funcionen correctamente en producción, y las herramientas de depuración permiten resolver problemas rápidamente cuando surgen.\n", "\n", "Dominar estas prácticas y herramientas no solo mejora la calidad del código individual, sino que establece las bases para colaboraciones efectivas en equipos de desarrollo. La combinación de depuración sistemática, pruebas automatizadas, manejo robusto de errores y control de versiones colaborativo forma el núcleo de las prácticas modernas de desarrollo de software, preparando a los desarrolladores para enfrentar los desafíos de proyectos complejos y equipos distribuidos.\n"]}], "source": ["print(generated_content)"]}, {"cell_type": "code", "execution_count": 4, "id": "945d107f", "metadata": {}, "outputs": [], "source": ["CONTENT_PLAN_VERSION = 1"]}, {"cell_type": "code", "execution_count": 13, "id": "b206c4d2", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'epigraph_name': 'Conceptos de algoritmo y pseudocódigo',\n", "  'epigraph_id': 390,\n", "  'epigraph_position': 1,\n", "  'didactic_instructions': '#### Introducción\\nComienza estableciendo la relación entre la resolución de problemas cotidianos y la formulación de un algoritmo, usando un ejemplo sencillo (p. ej., \"preparar una taza de té\"). Destaca que la programación parte de la capacidad humana para describir procesos paso a paso y que esos pasos, una vez formalizados, se convierten en una solución «computable».\\n\\n#### Desarrollo de conceptos clave\\n1. Presenta una definición rigurosa pero accesible de algoritmo; seguidamente enumera sus propiedades (finito, definido, efectivo).\\n2. Diferencia algoritmo, programa y lenguaje de programación, subrayando que el algoritmo es independiente de la sintaxis concreta.\\n3. Explica brevemente la utilidad del pseudocódigo como puente entre la idea y la implementación. Fija unas reglas sintácticas mínimas (uso de mayúsculas para palabras reservadas, sangrías, comentarios con `//`).\\n4. Introduce las tres estructuras básicas (secuencia, selección y repetición) y expón un micro-ejemplo para cada una en pseudocódigo.\\n5. Incluye un ejemplo integral: «calcular el máximo de tres números». Proporciona la descripción en lenguaje natural, el pseudocódigo y un diagrama de flujo mermaid sencillo que contenga inicio, comparaciones y fin. Indica al redactor que mantenga el diagrama en menos de diez nodos.\\n\\n#### Ejercicio propuesto (1 de 2 en todo el tema)\\nPlantea al lector redactar el pseudocódigo para \"contar el número de vocales en una palabra\" y, opcionalmente, bosquejar el diagrama de flujo. Sugiere comprobar la claridad y exhaustividad de los pasos.\\n\\n#### Conexión con epígrafes adyacentes\\nCierra la sección señalando que, aunque los algoritmos pueden escribirse en pseudocódigo, necesitan un lenguaje real para ejecutarse; adelanta que JavaScript será el vehículo elegido y que su historia se explora a continuación.\\n\\n#### Extensión esperada\\nIndica que el contenido resultante debe oscilar entre 1 100 y 1 300 palabras, incorporando un bloque de pseudocódigo y un diagrama mermaid.'},\n", " {'epigraph_name': 'Historia y evolución de JavaScript',\n", "  'epigraph_id': 391,\n", "  'epigraph_position': 2,\n", "  'didactic_instructions': '#### Introducción\\nContextualiza la aparición de JavaScript a mediados de los años 90 como respuesta a la necesidad de dotar de dinamismo al navegador. Enlaza con la idea previa: las computadoras necesitaban un lenguaje sencillo para plasmar algoritmos directamente en la web.\\n\\n#### Desarrollo de conceptos clave\\n1. Traza una narrativa cronológica que cubra: creación por <PERSON> (1995), estandarización ECMAScript (1997), Ajax (2005), ES6/ES2015 (2015) y la irrupción de Node.js (2009) hasta la actualidad.\\n2. Explica brevemente la diferencia entre JavaScript, ECMAScript y los motores de ejecución (V8, SpiderMonkey). Destaca cómo cada avance amplió la capacidad de expresar algoritmos complejos.\\n3. Incluye una tabla con tres columnas (Versión, Año, Novedades relevantes) para ES3, ES5, ES6, ES7+, seña<PERSON>o caracterís<PERSON>s que serán ú<PERSON> en el curso (p. ej., `let/const`, arrow functions, `Map`, `Set`, `async/await`).\\n4. Aclara la expansión de JavaScript más allá del navegador: introducción de Node.js y su impacto en la programación de servidores, desarrollo de herramientas (npm, frameworks) y la convergencia \"full-stack\".\\n5. Ofrece un pequeño diagrama mermaid de línea temporal (opcional) para ilustrar cinco hitos esenciales, indicando al redactor mantenerlo simple y lineal.\\n\\n#### Conexión con epígrafes adyacentes\\nConcluye señalando que, para beneficiarse de toda esta evolución, el estudiante instalará en la siguiente sección el entorno de ejecución (Node.js) y un editor moderno (VS Code).\\n\\n#### Extensión esperada\\nSugiere entre 950 y 1 150 palabras, con una tabla y, opcionalmente, un diagrama mermaid.'},\n", " {'epigraph_name': 'Instalación del entorno de desarrollo (Node.js, VS Code)',\n", "  'epigraph_id': 392,\n", "  'epigraph_position': 3,\n", "  'didactic_instructions': '#### Introducción\\nMotiva la necesidad de un entorno local que permita ejecutar los algoritmos del curso sin depender del navegador. Resalta la dualidad ejecución-servidor (Node.js) y edición (VS Code) como estándar de facto.\\n\\n#### Desarrollo de conceptos clave\\n1. Describe brevemente qué es Node.js y cómo actúa como motor en línea de comandos; diferencia entre LTS y versión actual.\\n2. Proporciona una guía paso a paso para instalar Node.js en Windows, macOS y Linux (descarga, asistente o gestor de paquetes). Incluye comandos de verificación `node -v` y `npm -v` en bloques de código.\\n3. Introduce el concepto de npm y enumsiona su papel como gestor de dependencias, pero pospone el uso avanzado para temas posteriores.\\n4. Explica la instalación de VS Code: descarga, configuración inicial y localización del terminal integrado.\\n5. Recomienda extensiones mínimas (ESLint, <PERSON><PERSON><PERSON>, JavaScript ES6 snippets) y muestra cómo habilitarlas en una tabla (Nombre, Propósito, Comando/Ruta de instalación).\\n6. Enseña a crear una carpeta de proyecto, abrirla en VS Code, generar `index.js` y ejecutar `node index.js` desde el terminal.\\n\\n#### Ejercicio propuesto (2 / 2 en todo el tema)\\nPide al estudiante crear la carpeta `intro-js`, añadir `index.js` con `console.log(\\'Entorno listo\\');`, ejecutarlo y hacer una captura de la versión de Node (se menciona que la captura no se incluirá en la redacción; basta con comprobar la salida en el terminal).\\n\\n#### Conexión con epígrafes adyacentes\\nSeñala que el entorno ya listo permitirá ejecutar el primer programa real del curso en la siguiente sección, enlazando con la inminente introducción al \"Hola, mundo\".\\n\\n#### Extensión esperada\\nRecomienda entre 850 y 1 050 palabras e incorporar una tabla de extensiones y varios bloques de comandos.'},\n", " {'epigraph_name': 'Primer script: \"Hola, mundo\" en consola y navegador',\n", "  'epigraph_id': 393,\n", "  'epigraph_position': 4,\n", "  'didactic_instructions': \"#### Introducción\\nPresenta la primera oportunidad de transformar el pseudocódigo en un programa real. Destaca la gratificación inmediata de ver un resultado tangible y cómo esto afianza el vínculo entre teoría y práctica.\\n\\n#### Desarrollo de conceptos clave\\n1. Muestra el código mínimo en Node.js:\\n```javascript\\nconsole.log('Hola, mundo');\\n```\\n   • Explica la función `console.log`, su salida en la terminal y cómo ejecutarla con `node`.\\n2. Replica la experiencia en el navegador con un archivo HTML elemental que incluya un `<script>` interno y, en un segundo paso, un script externo. Indica cómo abrir DevTools y visualizar la consola.\\n3. Contrasta la ejecución en consola con la instrucción `alert('Hola, mundo');`, comentando brevemente la naturaleza modal de `alert` y su utilidad limitada.\\n4. Introduce la noción de interacción mínima mediante `prompt` para recoger el nombre del usuario y saludar usando template literals.\\n```javascript\\nconst nombre = prompt('¿Cómo te llamas?');\\nconsole.log(`¡Hola, ${nombre}!`);\\n```\\n5. Explica someramente la diferencia de alcance entre código que corre en Node y en el navegador (objetos `window`, `global`) sin profundizar todavía.\\n\\n#### Ejercicio propuesto\\nPide al estudiante modificar el script para que muestre el momento actual (con `Date`) y salude indicando la hora. Deja abierta la preparación para el próximo tema sobre variables y tipos de datos.\\n\\n#### Conexión con epígrafes adyacentes\\nFinaliza subrayando que, para enriquecer este simple programa, se necesitará entender cómo se declaran y utilizan variables y operadores, tema que se abordará a continuación.\\n\\n#### Extensión esperada\\nEntre 800 y 1 000 palabras, con dos listados de código y sin necesidad de tablas ni diagramas.\"}]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["ti_result.epigraphs"]}, {"cell_type": "code", "execution_count": 9, "id": "6e6573d0", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-11 12:04:09,472 - logger - INFO - Entered in async session\n", "2025-07-11 12:04:09,476 - logger - INFO - Indice inside get subject index is, status=<IndiceStatus.CONTENT_GENERATION: 'CONTENT_GENERATION'> is_displayed=False created_at=datetime.datetime(2025, 6, 30, 13, 39, 58, 429390) id=48 order_id=4 version=1 created_by='ia_gen_user' updated_by='ia_gen_user' updated_at=datetime.datetime(2025, 7, 2, 9, 6, 25, 403435)\n", "2025-07-11 12:04:09,479 - logger - INFO - <PERSON><PERSON>ques inside get subject index is, [<PERSON><PERSON><PERSON>(name='Fundamentos de Programación y Estructuras de Datos en JavaScript', indice_id=48, created_by='ia_gen_user', updated_by=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 34060), id=63, position=1, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 34220))]\n", "2025-07-11 12:04:09,479 - logger - INFO - Asignatura is, nombre='Introducción a la programación, algoritmos y estructuras de datos con javascript - Informatica' estructura=Estructura(bloques_tematicos=[BloqueTematico(nombre='Fundamentos de Programación y Estructuras de Datos en JavaScript', temas=[Tema(nombre='Introducción al pensamiento algorítmico y al entorno JavaScript', epigrafes=['Conceptos de algoritmo y pseudocódigo', 'Historia y evolución de JavaScript', 'Instalación del entorno de desarrollo (Node.js, VS Code)', 'Primer script: \"Hola, mundo\" en consola y navegador']), <PERSON><PERSON>(nombre='Variables, tipos de datos y operadores en JavaScript', epigrafes=['Declaración de variables (var, let, const)', 'Tipos primitivos y coerción de datos', 'Operadores aritméticos, lógicos y comparativos', 'Entrada y salida básica con prompt, alert y console.log']), <PERSON><PERSON>(nombre='Control de flujo y estructuras condicionales', epigrafes=['Estructuras de decisión if, else y switch', 'Bucles for, while y do-while', 'Control de bucle (break, continue) y patrones de iteración', 'Buenas prácticas para evitar bucles infinitos']), Tema(nombre='Funciones y modularidad', epigrafes=['Declaración y expresión de funciones', 'Parámetros, retorno y funciones flecha', 'Closures y alcance léxico', 'Módulos ES6: import y export']), Tema(nombre='Complejidad algorítmica y análisis Big-O', epigrafes=['Concepto de eficiencia temporal y espacial', 'Reglas para determinar la notación Big-O', 'Casos mejor, promedio y peor', 'Herramientas de benchmarking en JavaScript']), Tema(nombre='Arrays y métodos funcionales', epigrafes=['Creación y mutación de arreglos', 'Métodos iterativos forEach, map, filter, reduce', 'Pilas y colas implementadas con arrays', 'Declaración y recorrido de matrices multidimensionales']), Tema(nombre='Listas enlazadas y árboles binarios', epigrafes=['Implementación de nodos y punteros en JS', 'Lista enlazada simple: inserción y eliminación', 'Estructura de árbol binario y tipos de recorrido', 'Análisis de complejidad de listas y árboles']), Tema(nombre='Tablas hash y conjuntos', epigrafes=['Fundamentos de hashing y funciones hash', 'Resolución de colisiones (encadenamiento y open addressing)', 'Implementación de Map, Set, WeakMap y WeakSet', 'Aplicaciones y análisis de rendimiento']), Tema(nombre='Recursión, búsqueda y ordenación', epigrafes=['Principios de recursión y diseño de casos base', 'Búsqueda lineal y búsqueda binaria', 'Algoritmos de ordenación sencillos (bubble, selection, insertion)', 'QuickSort y MergeSort: enfoque divide-and-conquer']), Tema(nombre='Calidad de software, pruebas y control de versiones', epigrafes=['Depuración con DevTools y Node Inspector', 'Pruebas unitarias y de integración con Jest', 'Manejo de errores y excepciones en JavaScript', 'Uso de Git y GitHub en flujo colaborativo'])])])\n"]}], "source": ["ti_result, ti_list = await index_repository.get_instructions_per_topic(INDEX_ID, topic_position=1)\n", "subject_index = await index_repository.get_subject_index(INDEX_ID)"]}, {"cell_type": "code", "execution_count": 37, "id": "27c5d204", "metadata": {}, "outputs": [{"data": {"text/plain": ["[('Introducción a la programación, algoritmos y estructuras de datos con javascript - Informatica', 'Introducción al pensamiento algorítmico y al entorno JavaScript', 123, 1, [{'epigraph_name': 'Conceptos de algoritmo y pseudocódigo', 'epigraph_id': 390, 'epigraph_position': 1, 'didactic_instructions': '#### Introducción\\nC ... (7465 characters truncated) ... abordará a continuación.\\n\\n#### Extensión esperada\\nEntre 800 y 1 000 palabras, con dos listados de código y sin necesidad de tablas ni diagramas.\"}])]"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["ti_result"]}, {"cell_type": "markdown", "id": "0b6cfcd1", "metadata": {}, "source": ["### Creating memories of the instructions"]}, {"cell_type": "code", "execution_count": 6, "id": "4056f89c", "metadata": {}, "outputs": [], "source": ["from openai.types.responses import ResponseFunctionToolCall\n", "import json\n", "from typing import Iterable"]}, {"cell_type": "code", "execution_count": 7, "id": "febe25d4", "metadata": {}, "outputs": [], "source": ["async def create_memories_for_topics(topics_info: list) -> dict[int, list]:\n", "    \"\"\"\n", "    Devuelve un dict donde la clave es el índice del tema (posicion - 1) y el valor\n", "    son las memorias de TODOS los temas previos a ese índice.\n", "    \"\"\"\n", "    accumulated: dict[int, list] = {}\n", "    previous: set = set()\n", "\n", "    for i, tema in enumerate(topics_info):\n", "        accumulated[i] = list(previous)\n", "\n", "        response = await client.responses.create(\n", "            reasoning={},\n", "            prompt={\n", "                \"id\": \"pmpt_685a5f9e4d408196bac06582445813390c97560367a99503\",\n", "                \"version\": \"12\",\n", "                \"variables\": {\n", "                    \"tema\": tema,\n", "                    \"memorias\": str(previous),\n", "                    \"tema_id\": str(i),\n", "                },\n", "            },\n", "        )\n", "\n", "        new_memories: Iterable = []\n", "        for r in response.output:\n", "            if isinstance(r, ResponseFunctionToolCall):\n", "                new_memories = json.loads(r.arguments).get(\"memories\", [])\n", "                break\n", "\n", "        previous.update(new_memories)\n", "\n", "    return accumulated"]}, {"cell_type": "markdown", "id": "cce7a811", "metadata": {}, "source": ["Quick eval\n", "\n", "#### o3 (version 11) reasoning effort medio\n", "* **Time**: 2m 54s\n", "* **All memories**: \n", "```\n", "{'Cada epígrafe debe ajustarse a rangos de extensión específicos (≈800-1300 palabras)',\n", " 'Diagramas de flujo en Mermaid simples y lineales, con menos de 10 nodos',\n", " 'Los ejemplos de código se escribirán en JavaScript moderno usando clases ES6',\n", " 'Los rangos de extensión concretos por epígrafe prevalecen sobre el rango genérico, pudiendo llegar hasta 1 400 palabras',\n", " 'Pseudocódigo con palabras reservadas en mayúsculas, sangrado consistente y comentarios con //'}\n", " ```\n", "* **Quality**:\n", "\n", "#### gpt 4.1 (version 12)\n", "* **Time**: 1m 25s\n", "* **All memories**:\n", "```\n", "{'Comandos estándar de comprobación de entorno: node -v, npm -v',\n", " 'Convención para pseudocódigo: palabras reservadas en mayúsculas, sangrías y comentarios con //',\n", " 'Est<PERSON><PERSON> m<PERSON> ES6+ (let/const, arrow functions, template literals, etc.)',\n", " 'JavaScript será el lenguaje base a lo largo de la asignatura',\n", " 'Los ejemplos y ejercicios deberán incorporar pseudocódigo y diagramas mermaid cuando corresponda',\n", " 'Node.js se adopta como entorno de ejecución principal ´uera del navegador',\n", " 'Se recomienda configurar el editor con ESLint, <PERSON><PERSON><PERSON> y JavaScript ES6 snippets como estándar mínimo de extensiones',\n", " 'VS Code se establece como editor de referencia para los ejercicios`}\n", " ```\n", "* **Quality**: \n", "\n", "#### gpt 4.1-mini (version 13)\n", "* **Time**: 57s\n", "* **All memories**: I dont include it, there were so many\n", "* **Quality**: bad, creates too much fields\n", "\n", "#### gpt o4-mini (version 14)\n", "* **Time**: 1m 47s\n", "* **All memories**:\n", "```\n", "{'Diagramas Mermaid deben contener menos de diez nodos',\n", " 'Ejemplos de código deben incluir comentarios en línea que vinculen la lógica con la teoría explicada',\n", " 'Extensión esperada por epígrafe: Recursión 1 000–1 200 palabras; Búsqueda 900–1 100; Algoritmos sencillos 1 100–1 300; Divide-and-conquer 1 200–1 400',\n", " 'Reglas sintácticas de pseudocódigo: palabras reservadas en mayúsculas, sangrías y comentarios con //',\n", " 'Uso de camelCase para identificadores, preferir const sobre let, evitar var salvo motivos concretos',\n", " 'Uso de operador de igualdad estricta (===) para comparaciones de igualdad'}\n", "```\n", "* **Quality**: \n", "\n", "\n", "Equilibrio calidad latencia gpt-4.1 parece muy buena opcion, si se observaran memorias innecesarias se podría ir a o3"]}, {"cell_type": "code", "execution_count": 20, "id": "b7d039b3", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n"]}], "source": ["acc_mem_topics = await create_memories_for_topics(ti_list)"]}, {"cell_type": "code", "execution_count": 21, "id": "2370cba7", "metadata": {}, "outputs": [{"data": {"text/plain": ["{0: [],\n", " 1: ['VS Code es el editor recomendado y estándar para el curso',\n", "  'Se utiliza pseudocódigo con mayúsculas para palabras reservadas, sangrías y comentarios con //',\n", "  'Diagramas de flujo se representan con la sintaxis mermaid y deben tener menos de diez nodos en ejemplos introductorios',\n", "  'Se elige JavaScript como lenguaje de programación principal para la asignatura',\n", "  'Node.js se emplea como entorno de ejecución principal fuera del navegador',\n", "  'Extensiones mínimas en VS Code: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, JavaScript ES6 snippets'],\n", " 2: ['VS Code es el editor recomendado y estándar para el curso',\n", "  'Se utiliza pseudocódigo con mayúsculas para palabras reservadas, sangrías y comentarios con //',\n", "  'Diagramas de flujo se representan con la sintaxis mermaid y deben tener menos de diez nodos en ejemplos introductorios',\n", "  'Se elige JavaScript como lenguaje de programación principal para la asignatura',\n", "  'Node.js se emplea como entorno de ejecución principal fuera del navegador',\n", "  'Extensiones mínimas en VS Code: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, JavaScript ES6 snippets'],\n", " 3: ['VS Code es el editor recomendado y estándar para el curso',\n", "  'Se utiliza pseudocódigo con mayúsculas para palabras reservadas, sangrías y comentarios con //',\n", "  'Diagramas de flujo se representan con la sintaxis mermaid y deben tener menos de diez nodos en ejemplos introductorios',\n", "  'Se elige JavaScript como lenguaje de programación principal para la asignatura',\n", "  'Node.js se emplea como entorno de ejecución principal fuera del navegador',\n", "  'Extensiones mínimas en VS Code: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, JavaScript ES6 snippets'],\n", " 4: ['VS Code es el editor recomendado y estándar para el curso',\n", "  'Se utiliza pseudocódigo con mayúsculas para palabras reservadas, sangrías y comentarios con //',\n", "  'Diagramas de flujo se representan con la sintaxis mermaid y deben tener menos de diez nodos en ejemplos introductorios',\n", "  'Se elige JavaScript como lenguaje de programación principal para la asignatura',\n", "  'Node.js se emplea como entorno de ejecución principal fuera del navegador',\n", "  'Extensiones mínimas en VS Code: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, JavaScript ES6 snippets'],\n", " 5: ['VS Code es el editor recomendado y estándar para el curso',\n", "  'Se utiliza pseudocódigo con mayúsculas para palabras reservadas, sangrías y comentarios con //',\n", "  'Diagramas de flujo se representan con la sintaxis mermaid y deben tener menos de diez nodos en ejemplos introductorios',\n", "  'Se elige JavaScript como lenguaje de programación principal para la asignatura',\n", "  'Node.js se emplea como entorno de ejecución principal fuera del navegador',\n", "  'Extensiones mínimas en VS Code: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, JavaScript ES6 snippets'],\n", " 6: ['VS Code es el editor recomendado y estándar para el curso',\n", "  'Se utiliza pseudocódigo con mayúsculas para palabras reservadas, sangrías y comentarios con //',\n", "  'Diagramas de flujo se representan con la sintaxis mermaid y deben tener menos de diez nodos en ejemplos introductorios',\n", "  'Se elige JavaScript como lenguaje de programación principal para la asignatura',\n", "  'Node.js se emplea como entorno de ejecución principal fuera del navegador',\n", "  'Extensiones mínimas en VS Code: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, JavaScript ES6 snippets'],\n", " 7: ['VS Code es el editor recomendado y estándar para el curso',\n", "  'Se utiliza pseudocódigo con mayúsculas para palabras reservadas, sangrías y comentarios con //',\n", "  'Diagramas de flujo se representan con la sintaxis mermaid y deben tener menos de diez nodos en ejemplos introductorios',\n", "  'Se elige JavaScript como lenguaje de programación principal para la asignatura',\n", "  'Node.js se emplea como entorno de ejecución principal fuera del navegador',\n", "  'Extensiones mínimas en VS Code: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, JavaScript ES6 snippets'],\n", " 8: ['VS Code es el editor recomendado y estándar para el curso',\n", "  'Se utiliza pseudocódigo con mayúsculas para palabras reservadas, sangrías y comentarios con //',\n", "  'Diagramas de flujo se representan con la sintaxis mermaid y deben tener menos de diez nodos en ejemplos introductorios',\n", "  'Se elige JavaScript como lenguaje de programación principal para la asignatura',\n", "  'Node.js se emplea como entorno de ejecución principal fuera del navegador',\n", "  'Extensiones mínimas en VS Code: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, JavaScript ES6 snippets'],\n", " 9: ['VS Code es el editor recomendado y estándar para el curso',\n", "  'Se utiliza pseudocódigo con mayúsculas para palabras reservadas, sangrías y comentarios con //',\n", "  'Diagramas de flujo se representan con la sintaxis mermaid y deben tener menos de diez nodos en ejemplos introductorios',\n", "  'Se elige JavaScript como lenguaje de programación principal para la asignatura',\n", "  'Node.js se emplea como entorno de ejecución principal fuera del navegador',\n", "  'Extensiones mínimas en VS Code: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, JavaScript ES6 snippets']}"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["acc_mem_topics"]}, {"cell_type": "markdown", "id": "3e62a78b", "metadata": {}, "source": ["Then we would store this by topic in the db and by version, and we will override the version 1 each time for now. We skip for now, but with that memories we already have all the context for generating each topic."]}, {"cell_type": "markdown", "id": "e6fe5196", "metadata": {}, "source": ["### Searching sources with Search Agent\n", "\n", "Note: Would be good if from the search_references we could specify topic, model to use and so on.\n", "\n", "We have searched sources for index_id 48. But put an invalid gen ai api so it doesn´t trigger the current content generation. We will use the sources from there."]}, {"cell_type": "markdown", "id": "13ab9978", "metadata": {}, "source": ["#### Analyzing search Agent urls per topic."]}, {"cell_type": "markdown", "id": "ea88f81d", "metadata": {}, "source": ["Create functions to analyze this, cluster and create statistics on what was found\n", "\n", "Identified from searches for now: it founds a lot of paywalled pages."]}, {"cell_type": "code", "execution_count": 9, "id": "8e07bf01", "metadata": {}, "outputs": [], "source": ["from src.domain.models import Doc, EpigrafeDocumento, Tema, Epigrafe\n", "from sqlmodel import select\n", "from src.api.common.dependencies.get_session import get_session\n", "\n", "\n", "\n", "def get_docs_per_topic(topic_id: int, session = None):\n", "       if not session:\n", "              session = next(get_session())\n", "       stm = (select(Doc.name,\n", "                     Doc.summary,\n", "                     Doc.document_url,\n", "                     Doc.token_count)\n", "              .distinct()\n", "              .join(EpigrafeDocumento, Doc.id == EpigrafeDocumento.id_documento)\n", "              .join(Epigrafe, EpigrafeDocumento.id_epigrafe == Epigrafe.id)\n", "              .join(Te<PERSON>, Epigrafe.id_tema == Tema.id)\n", "              .where(Tema.id == topic_id))\n", "       result = session.exec(stm).all()\n", "       return result\n"]}, {"cell_type": "markdown", "id": "34ffc6d5", "metadata": {}, "source": ["#### Document Analysis and Clustering for Index "]}, {"cell_type": "code", "execution_count": null, "id": "f73c9b7a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[2K\u001b[37m⠧\u001b[0m \u001b[2mia-gestorcontenidosiagen-be==0.2.0                                            \u001b[0m[Information] [CredentialProvider]VstsCredentialProvider - Acquired bearer token using 'MSAL Silent'\n", "[Information] [CredentialProvider]VstsCredentialProvider - Attempting to exchange the bearer token for an Azure DevOps session token.\n", "\u001b[2K\u001b[2mResolved \u001b[1m271 packages\u001b[0m \u001b[2min 8.67s\u001b[0m\u001b[0m                                       \u001b[0m\n", "\u001b[2mAudited \u001b[1m241 packages\u001b[0m \u001b[2min 0.25ms\u001b[0m\u001b[0m\n"]}], "source": ["!uv add --dev scikit-learn seaborn matplot<PERSON>b pandas"]}, {"cell_type": "code", "execution_count": null, "id": "f2428fff", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "from sklearn.cluster import KMeans\n", "from sklearn.decomposition import PCA\n", "from sklearn.metrics import silhouette_score\n", "from collections import Counter\n", "from urllib.parse import urlparse\n", "from typing import List, Dict, Tuple, Any\n", "\n", "def analyze_documents_per_topic(topic_id: int, topic_name: str = None, session=None) -> Dict[str, Any]:\n", "    \"\"\"\n", "    Comprehensive document analysis for a specific topic.\n", "    \n", "    Args:\n", "        topic_id: ID of the topic to analyze\n", "        topic_name: Optional name of the topic for display\n", "        session: Database session (optional)\n", "    \n", "    Returns:\n", "        Dictionary with analysis results including clustering, statistics, and visualizations\n", "    \"\"\"\n", "    docs = get_docs_per_topic(topic_id, session)\n", "    \n", "    if not docs:\n", "        print(f\"No documents found for topic {topic_id}\")\n", "        return {}\n", "    \n", "    df = pd.DataFrame([{\n", "        'name': d.name,\n", "        'summary': d.summary,\n", "        'url': d.document_url,\n", "        'token_count': d.token_count\n", "    } for d in docs])\n", "    \n", "    print(f\"=== ANALYSIS FOR TOPIC: {topic_name or f'ID {topic_id}'} ===\")\n", "    print(f\"📊 Total documents: {len(df)}\")\n", "    \n", "    cluster_results = perform_document_clustering(df)\n", "    create_topic_visualizations(df, cluster_results, topic_name or f\"Topic {topic_id}\")\n", "    print_analysis_summary(df, cluster_results)\n", "    \n", "    return {\n", "        'topic_id': topic_id,\n", "        'topic_name': topic_name,\n", "        'dataframe': df,\n", "        'cluster_results': cluster_results,\n", "        'total_docs': len(df),\n", "        'total_tokens': df['token_count'].sum() if 'token_count' in df.columns else 0\n", "    }, docs\n", "\n", "def perform_document_clustering(df: pd.DataFrame) -> Dict[str, Any]:\n", "    \"\"\"\n", "    Perform TF-IDF clustering on document summaries.\n", "    \n", "    Returns:\n", "        Dictionary with clustering results\n", "    \"\"\"\n", "    summaries = df['summary'].dropna().tolist()\n", "    \n", "    if len(summaries) < 2:\n", "        return {'status': 'insufficient_data', 'message': 'Not enough summaries for clustering'}\n", "    \n", "    vectorizer = TfidfVectorizer(\n", "        max_features=1000,\n", "        stop_words='english',\n", "        ngram_range=(1, 2),\n", "        min_df=2,\n", "        max_df=0.8\n", "    )\n", "    \n", "    try:\n", "        X = vectorizer.fit_transform(summaries)\n", "        max_clusters = min(8, len(summaries) - 1)\n", "        silhouette_scores = []\n", "        K_range = range(2, max_clusters + 1)\n", "        \n", "        for k in K_range:\n", "            kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)\n", "            cluster_labels = kmeans.fit_predict(X)\n", "            silhouette_avg = silhouette_score(X, cluster_labels)\n", "            silhouette_scores.append(silhouette_avg)\n", "        \n", "        optimal_k = K_range[np.argmax(silhouette_scores)]\n", "        kmeans = KMeans(n_clusters=optimal_k, random_state=42, n_init=10)\n", "        cluster_labels = kmeans.fit_predict(X)\n", "        \n", "        cluster_descriptions = create_cluster_descriptions(\n", "            vectorizer, kmeans, optimal_k, df[df['summary'].notna()].copy()\n", "        )\n", "        \n", "        return {\n", "            'status': 'success',\n", "            'vectorizer': vectorizer,\n", "            'X': X,\n", "            'kmeans': kmeans,\n", "            'cluster_labels': cluster_labels,\n", "            'optimal_k': optimal_k,\n", "            'silhouette_scores': silhouette_scores,\n", "            'K_range': K_range,\n", "            'cluster_descriptions': cluster_descriptions,\n", "            'df_with_clusters': df[df['summary'].notna()].copy().assign(cluster=cluster_labels)\n", "        }\n", "        \n", "    except Exception as e:\n", "        return {'status': 'error', 'error': str(e)}\n", "\n", "def create_cluster_descriptions(vectorizer, kmeans, optimal_k: int, df_subset: pd.DataFrame) -> Dict[int, str]:\n", "    \"\"\"Create descriptions for each cluster based on actual top terms.\"\"\"\n", "    feature_names = vectorizer.get_feature_names_out()\n", "    centroids = kmeans.cluster_centers_\n", "    descriptions = {}\n", "    \n", "    for cluster_id in range(optimal_k):\n", "        top_indices = centroids[cluster_id].argsort()[-3:][::-1]\n", "        top_terms = [feature_names[i] for i in top_indices]\n", "        descriptions[cluster_id] = f\"{', '.join(top_terms)}\"\n", "    \n", "    return descriptions\n", "\n", "def create_topic_visualizations(df: pd.DataFrame, cluster_results: Dict, topic_name: str):\n", "    \"\"\"Create comprehensive visualizations for topic analysis.\"\"\"\n", "    \n", "    if cluster_results.get('status') != 'success':\n", "        print(f\"⚠️ Clustering failed: {cluster_results.get('message', 'Unknown error')}\")\n", "        create_basic_visualizations(df, topic_name)\n", "        return\n", "    \n", "    X = cluster_results['X']\n", "    cluster_labels = cluster_results['cluster_labels']\n", "    kmeans = cluster_results['kmeans']\n", "    optimal_k = cluster_results['optimal_k']\n", "    cluster_descriptions = cluster_results['cluster_descriptions']\n", "    df_with_clusters = cluster_results['df_with_clusters']\n", "    \n", "    fig = plt.figure(figsize=(20, 15))\n", "    gs = fig.add_gridspec(3, 3, height_ratios=[2, 1.5, 1], width_ratios=[1.5, 1, 1])\n", "    \n", "    ax1 = fig.add_subplot(gs[0, :2])\n", "    create_enhanced_pca_plot(ax1, X, cluster_labels, kmeans, optimal_k, cluster_descriptions)\n", "    \n", "    ax2 = fig.add_subplot(gs[0, 2])\n", "    create_cluster_summary_table(ax2, df_with_clusters, cluster_results, optimal_k)\n", "    \n", "    ax3 = fig.add_subplot(gs[1, 0])\n", "    create_token_distribution(ax3, df)\n", "    \n", "    ax4 = fig.add_subplot(gs[1, 1])\n", "    create_domain_analysis(ax4, df)\n", "    \n", "    ax5 = fig.add_subplot(gs[1, 2])\n", "    create_silhouette_plot(ax5, cluster_results)\n", "    \n", "    ax6 = fig.add_subplot(gs[2, 0])\n", "    create_cluster_size_plot(ax6, df_with_clusters, cluster_descriptions)\n", "    \n", "    ax7 = fig.add_subplot(gs[2, 1:])\n", "    create_summary_stats_table(ax7, df, cluster_results)\n", "    \n", "    plt.suptitle(f'📊 Document Analysis Dashboard: {topic_name}', fontsize=18, fontweight='bold', y=0.98)\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "def create_enhanced_pca_plot(ax, X, cluster_labels, kmeans, optimal_k, cluster_descriptions):\n", "    \"\"\"Create enhanced PCA plot with meaningful labels.\"\"\"\n", "    pca = PCA(n_components=2, random_state=42)\n", "    X_pca = pca.fit_transform(X.toarray())\n", "    \n", "    colors = plt.cm.Set3(np.linspace(0, 1, optimal_k))\n", "    markers = ['o', 's', '^', 'D', 'v', '<', '>', 'p', '*', 'h']\n", "    \n", "    for cluster_id in range(optimal_k):\n", "        mask = cluster_labels == cluster_id\n", "        ax.scatter(X_pca[mask, 0], X_pca[mask, 1], \n", "                  c=[colors[cluster_id]], \n", "                  marker=markers[cluster_id % len(markers)],\n", "                  alpha=0.7, s=60, \n", "                  label=f\"C{cluster_id}: {cluster_descriptions[cluster_id]}\")\n", "    \n", "    centroids_pca = pca.transform(kmeans.cluster_centers_)\n", "    for cluster_id in range(optimal_k):\n", "        ax.scatter(centroids_pca[cluster_id, 0], centroids_pca[cluster_id, 1], \n", "                  c='red', marker='X', s=200, linewidths=2, edgecolors='black')\n", "        ax.annotate(f'C{cluster_id}', \n", "                   (centroids_pca[cluster_id, 0], centroids_pca[cluster_id, 1]),\n", "                   xytext=(5, 5), textcoords='offset points', fontsize=10, fontweight='bold',\n", "                   bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.8))\n", "    \n", "    ax.set_title('📍 Document Clusters (PCA)', fontweight='bold')\n", "    ax.set_xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.1%} variance)')\n", "    ax.set_ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.1%} variance)')\n", "    ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=9)\n", "    ax.grid(True, alpha=0.3)\n", "\n", "def create_basic_visualizations(df: pd.DataFrame, topic_name: str):\n", "    \"\"\"Create basic visualizations when clustering fails.\"\"\"\n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "    \n", "    if 'token_count' in df.columns and df['token_count'].notna().any():\n", "        axes[0, 0].hist(df['token_count'].dropna(), bins=15, alpha=0.7, color='skyblue')\n", "        axes[0, 0].set_title('Token Distribution')\n", "        axes[0, 0].set_xlabel('Tokens')\n", "    \n", "    if df['summary'].notna().any():\n", "        axes[0, 1].hist(df['summary'].dropna().str.len(), bins=15, alpha=0.7, color='lightgreen')\n", "        axes[0, 1].set_title('Summary Lengths')\n", "    \n", "    if df['url'].notna().any():\n", "        domains = [urlparse(str(url)).netloc for url in df['url'].dropna()]\n", "        domain_counts = Counter(domains).most_common(5)\n", "        if domain_counts:\n", "            names, counts = zip(*domain_counts)\n", "            axes[1, 0].barh(range(len(names)), counts)\n", "            axes[1, 0].set_yticks(range(len(names)))\n", "            axes[1, 0].set_yticklabels(names)\n", "            axes[1, 0].set_title('Top Domains')\n", "    \n", "    axes[1, 1].text(0.5, 0.5, f'📊 {len(df)} documents\\n🔍 Clustering unavailable', \n", "                   ha='center', va='center', transform=axes[1, 1].transAxes, fontsize=14)\n", "    axes[1, 1].set_title('Summary')\n", "    \n", "    plt.suptitle(f'📊 Basic Analysis: {topic_name}', fontsize=16, fontweight='bold')\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "def create_token_distribution(ax, df):\n", "    \"\"\"Create token distribution plot with statistics.\"\"\"\n", "    if 'token_count' in df.columns and df['token_count'].notna().any():\n", "        token_data = df['token_count'].dropna()\n", "        ax.hist(token_data, bins=15, alpha=0.7, color='lightcoral', edgecolor='black')\n", "        ax.axvline(token_data.median(), color='red', linestyle='--', label=f'Median: {token_data.median():,.0f}')\n", "        ax.axvline(token_data.mean(), color='orange', linestyle='--', label=f'Mean: {token_data.mean():,.0f}')\n", "        ax.set_yscale('log')\n", "        ax.set_title('📊 Token Distribution (Log Scale)')\n", "        ax.set_xlabel('Token Count')\n", "        ax.legend()\n", "        ax.grid(True, alpha=0.3)\n", "\n", "def create_domain_analysis(ax, df):\n", "    \"\"\"Create domain analysis chart.\"\"\"\n", "    if df['url'].notna().any():\n", "        domains = [urlparse(str(url)).netloc for url in df['url'].dropna() if urlparse(str(url)).netloc]\n", "        if domains:\n", "            domain_counts = Counter(domains).most_common(10)\n", "            names, counts = zip(*domain_counts)\n", "            bars = ax.barh(range(len(names)), counts, color='mediumpurple', alpha=0.7)\n", "            ax.set_yticks(range(len(names)))\n", "            ax.set_yticklabels([n[:25] + '...' if len(n) > 25 else n for n in names])\n", "            ax.set_title('🌐 Top Source Domains')\n", "            for i, (bar, count) in enumerate(zip(bars, counts)):\n", "                ax.text(bar.get_width() + 0.1, bar.get_y() + bar.get_height()/2, \n", "                       str(count), va='center', fontweight='bold')\n", "\n", "def create_silhouette_plot(ax, cluster_results):\n", "    \"\"\"Create silhouette score plot.\"\"\"\n", "    if cluster_results.get('silhouette_scores'):\n", "        K_range = cluster_results['K_range']\n", "        scores = cluster_results['silhouette_scores']\n", "        optimal_k = cluster_results['optimal_k']\n", "        \n", "        ax.plot(K_range, scores, 'bo-', linewidth=2, markersize=6)\n", "        ax.axvline(optimal_k, color='red', linestyle='--', \n", "                  label=f'Optimal K = {optimal_k}')\n", "        ax.set_title('🎯 Clustering Quality')\n", "        ax.set_xlabel('Number of Clusters')\n", "        ax.set_ylabel('Silhouette Score')\n", "        ax.legend()\n", "        ax.grid(True, alpha=0.3)\n", "\n", "def create_cluster_size_plot(ax, df_with_clusters, cluster_descriptions):\n", "    \"\"\"Create cluster size distribution plot.\"\"\"\n", "    if 'cluster' in df_with_clusters.columns:\n", "        cluster_counts = df_with_clusters['cluster'].value_counts().sort_index()\n", "        labels = [f\"C{i}: {cluster_descriptions.get(i, f'Cluster {i}')}\" for i in cluster_counts.index]\n", "        \n", "        wedges, texts, autotexts = ax.pie(cluster_counts.values, labels=None, autopct='%1.0f',\n", "                                         colors=plt.cm.Set3(np.linspace(0, 1, len(cluster_counts))))\n", "        ax.set_title('📈 Cluster Distribution')\n", "        \n", "        ax.legend(wedges, [f\"{label} ({count})\" for label, count in zip(labels, cluster_counts.values)],\n", "                 loc=\"center left\", bbox_to_anchor=(1, 0, 0.5, 1), fontsize=8)\n", "\n", "def create_cluster_summary_table(ax, df_with_clusters, cluster_results, optimal_k):\n", "    \"\"\"Create cluster summary as table.\"\"\"\n", "    ax.axis('tight')\n", "    ax.axis('off')\n", "    \n", "    if 'cluster' in df_with_clusters.columns:\n", "        vectorizer = cluster_results['vectorizer']\n", "        kmeans = cluster_results['kmeans']\n", "        cluster_descriptions = cluster_results['cluster_descriptions']\n", "        \n", "        table_data = []\n", "        feature_names = vectorizer.get_feature_names_out()\n", "        centroids = kmeans.cluster_centers_\n", "        \n", "        for cluster_id in range(optimal_k):\n", "            cluster_docs = df_with_clusters[df_with_clusters['cluster'] == cluster_id]\n", "            top_indices = centroids[cluster_id].argsort()[-3:][::-1]\n", "            top_terms = [feature_names[i] for i in top_indices]\n", "            \n", "            table_data.append([\n", "                f\"C{cluster_id}\",\n", "                cluster_descriptions.get(cluster_id, ''),\n", "                len(cluster_docs),\n", "                ', '.join(top_terms[:2])\n", "            ])\n", "        \n", "        table = ax.table(cellText=table_data,\n", "                        colLabels=['ID', 'Description', 'Docs', 'Top Terms'],\n", "                        cellLoc='left',\n", "                        loc='center')\n", "        table.auto_set_font_size(False)\n", "        table.set_fontsize(9)\n", "        table.scale(1, 1.5)\n", "        ax.set_title('📋 Cluster Summary', fontweight='bold', pad=20)\n", "\n", "def create_summary_stats_table(ax, df, cluster_results):\n", "    \"\"\"Create summary statistics table.\"\"\"\n", "    ax.axis('tight')\n", "    ax.axis('off')\n", "    \n", "    stats_data = [\n", "        ['Total Documents', len(df)],\n", "        ['With Summaries', df['summary'].notna().sum()],\n", "        ['With URLs', df['url'].notna().sum()],\n", "    ]\n", "    \n", "    if 'token_count' in df.columns and df['token_count'].notna().any():\n", "        token_data = df['token_count'].dropna()\n", "        stats_data.extend([\n", "            ['Total Tokens', f\"{token_data.sum():,}\"],\n", "            ['Avg <PERSON>', f\"{token_data.mean():,.0f}\"],\n", "            ['Token Range', f\"{token_data.min():,} - {token_data.max():,}\"]\n", "        ])\n", "    \n", "    if cluster_results.get('status') == 'success':\n", "        stats_data.extend([\n", "            ['Clusters Found', cluster_results['optimal_k']],\n", "            ['Silhouette Score', f\"{max(cluster_results['silhouette_scores']):.3f}\"]\n", "        ])\n", "    \n", "    table = ax.table(cellText=stats_data,\n", "                    colLabels=['Metric', 'Value'],\n", "                    cellLoc='left',\n", "                    loc='center')\n", "    table.auto_set_font_size(False)\n", "    table.set_fontsize(10)\n", "    table.scale(1, 1.2)\n", "    ax.set_title('📈 Summary Statistics', fontweight='bold', pad=20)\n", "\n", "def print_analysis_summary(df: pd.DataFrame, cluster_results: Dict):\n", "    \"\"\"Print detailed analysis summary.\"\"\"\n", "    print(f\"\\n{'='*60}\")\n", "    print(\"📊 DOCUMENT ANALYSIS SUMMARY\")\n", "    print(f\"{'='*60}\")\n", "    \n", "    if cluster_results.get('status') == 'success':\n", "        df_with_clusters = cluster_results['df_with_clusters']\n", "        cluster_descriptions = cluster_results['cluster_descriptions']\n", "        \n", "        print(f\"🎯 Clustering Results:\")\n", "        print(f\"   • Optimal clusters: {cluster_results['optimal_k']}\")\n", "        print(f\"   • Silhouette score: {max(cluster_results['silhouette_scores']):.3f}\")\n", "        \n", "        print(f\"\\n📋 Cluster Breakdown:\")\n", "        for cluster_id in range(cluster_results['optimal_k']):\n", "            cluster_docs = df_with_clusters[df_with_clusters['cluster'] == cluster_id]\n", "            desc = cluster_descriptions.get(cluster_id, f'Cluster {cluster_id}')\n", "            print(f\"   • C{cluster_id} ({desc}): {len(cluster_docs)} documents\")\n", "    \n", "    if 'token_count' in df.columns and df['token_count'].notna().any():\n", "        token_data = df['token_count'].dropna()\n", "        print(f\"\\n📊 Token Statistics:\")\n", "        print(f\"   • Total tokens: {token_data.sum():,}\")\n", "        print(f\"   • Average: {token_data.mean():,.0f}\")\n", "        print(f\"   • Range: {token_data.min():,} - {token_data.max():,}\")\n", "    \n", "    print(f\"\\n✅ Analysis completed successfully!\")\n", "\n", "def analyze_multiple_topics(topic_configs: List[Tuple[int, str]]) -> Dict[int, Dict]:\n", "    \"\"\"\n", "    Analyze multiple topics at once.\n", "    \n", "    Args:\n", "        topic_configs: List of tuples (topic_id, topic_name)\n", "    \n", "    Returns:\n", "        Dictionary mapping topic_id to analysis results\n", "    \"\"\"\n", "    results = {}\n", "    \n", "    for topic_id, topic_name in topic_configs:\n", "        print(f\"\\n{'='*80}\")\n", "        print(f\"🔍 ANALYZING TOPIC: {topic_name} (ID: {topic_id})\")\n", "        print(f\"{'='*80}\")\n", "        \n", "        results[topic_id] = analyze_documents_per_topic(topic_id, topic_name)\n", "        \n", "    return results"]}, {"cell_type": "code", "execution_count": 127, "id": "9022b12a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== ANALYSIS FOR TOPIC: Geopolítica de las grandes potencias y sistemas regionales ===\n", "📊 Total documents: 39\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/4b/djmst1cd65390hn17bdx76km0000gp/T/ipykernel_73203/1311764639.py:162: UserWarning: Glyph 128205 (\\N{ROUND PUSHPIN}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/4b/djmst1cd65390hn17bdx76km0000gp/T/ipykernel_73203/1311764639.py:162: UserWarning: Glyph 128203 (\\N{CLIPBOARD}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/4b/djmst1cd65390hn17bdx76km0000gp/T/ipykernel_73203/1311764639.py:162: UserWarning: Glyph 128202 (\\N{BAR CHART}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/4b/djmst1cd65390hn17bdx76km0000gp/T/ipykernel_73203/1311764639.py:162: UserWarning: Glyph 127760 (\\N{GLOBE WITH MERIDIANS}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/4b/djmst1cd65390hn17bdx76km0000gp/T/ipykernel_73203/1311764639.py:162: UserWarning: Glyph 127919 (\\N{DIRECT HIT}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/var/folders/4b/djmst1cd65390hn17bdx76km0000gp/T/ipykernel_73203/1311764639.py:162: UserWarning: Glyph 128200 (\\N{CHART WITH UPWARDS TREND}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/Users/<USER>/Code/ia_gestorcontenidosiagen_be/.venv/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 128205 (\\N{ROUND PUSHPIN}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/Code/ia_gestorcontenidosiagen_be/.venv/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 128203 (\\N{CLIPBOARD}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/Code/ia_gestorcontenidosiagen_be/.venv/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 128202 (\\N{BAR CHART}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/Code/ia_gestorcontenidosiagen_be/.venv/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 127760 (\\N{GLOBE WITH MERIDIANS}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/Code/ia_gestorcontenidosiagen_be/.venv/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 127919 (\\N{DIRECT HIT}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/Users/<USER>/Code/ia_gestorcontenidosiagen_be/.venv/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 128200 (\\N{CHART WITH UPWARDS TREND}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n"]}, {"data": {"image/png": "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***************************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*****************************************************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*********************************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", "text/plain": ["<Figure size 2000x1500 with 7 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "📊 DOCUMENT ANALYSIS SUMMARY\n", "============================================================\n", "🎯 Clustering Results:\n", "   • Optimal clusters: 4\n", "   • Silhouette score: 0.103\n", "\n", "📋 Cluster Breakdown:\n", "   • C0 (la, en, el): 20 documents\n", "   • C1 (russia, nato, security): 12 documents\n", "   • C2 (asean, power, institutional): 2 documents\n", "   • C3 (brics, development, growth): 5 documents\n", "\n", "📊 Token Statistics:\n", "   • Total tokens: 643,114\n", "   • Average: 16,490\n", "   • Range: 1,575 - 111,097\n", "\n", "✅ Analysis completed successfully!\n"]}], "source": ["temas = await index_repository.get_index_elements(42, structure_type=\"tema\")\n", "result, docs = analyze_documents_per_topic(temas[4].id, temas[4].name)"]}, {"cell_type": "code", "execution_count": 128, "id": "2fcc016a", "metadata": {}, "outputs": [{"data": {"text/plain": ["[('6 Swing States Will Decide the Future of Geopolitics', '- <PERSON>is potencias intermedias del Sur Global —Brasil, India, Indonesia, Arabia Saudita, Sudáfrica y Turquía— influyen decisivamente en la geopolítica. ... (417 characters truncated) ...  los equilibrios globales.\\n- La cooptación por China y Rusia en el Sur Global enfrenta límites por los intereses diversos y los lazos con Occidente.', 'https://foreignpolicy.com/2023/06/06/geopolitics-global-south-middle-powers-swing-states-india-brazil-turkey-indonesia-saudi-arabia-south-africa/', 20470),\n", " ('A Decade of Emergence: The BRICS’ Institutional Densification Process', '- Analyzes the institutionalization and expansion of BRICS through its eight summits.\\n- Highlights international political economy and security as c ... (230 characters truncated) ... ormations in contemporary capitalism.\\n- Identifies distinct patterns of institutional densification shaping BRICS’ role in the evolving world order.', 'https://www.academia.edu/36756184/A_Decade_of_Emergence_The_BRICS_Institutional_Densification_Process', 20818),\n", " ('A New Vision for the Transatlantic Alliance: The Future of European Security, the United States, and the World Order after Russia’s War in Ukraine', '- The 2022 invasion of Ukraine exposed flaws in post-Cold War deterrence and galvanized NATO unity.\\n- NATO refocused on collective defense and deter ... (690 characters truncated) ... talization, and innovation in European defense.\\n- Global partnerships beyond Europe, including with India, are crucial for transatlantic resilience.', 'https://cepa.org/comprehensive-reports/a-new-vision-for-the-transatlantic-alliance-the-future-of-european-security-the-united-states-and-the-world-order-after-russias-war-in-ukraine/', 107793),\n", " ('ASEAN Institutions and Institutionalisation: Theoretical and Empirical Perspectives', \"- Assesses ASEAN’s institutional framework post-Charter (2008), highlighting persistent deficits in mandate, funding, and staffing.\\n- Applies constr ... (262 characters truncated) ... ancial models, and engaging non-state actors.\\n- Contrasts ASEAN’s non-interference ('ASEAN Way') with deeper supranational ambitions seen in the EU.\", 'https://pmc.ncbi.nlm.nih.gov/articles/**********/', 14395),\n", " ('Asia in Flux: U.S.-China and the Search for a New Equilibrium', \"- Intensifying U.S.-China military competition está remodelando las percepciones de amenaza y aumentando la militarización en Asia.\\n- China ha moder ... (573 characters truncated) ... cias.\\n- Se recomienda combinar disuasión y diplomacia, clarificar líneas rojas y mejorar canales de comunicación militar para estabilizar la región.\", 'https://www.crisisgroup.org/asia/north-east-asia/china-united-states/347-asia-flux-us-china-and-search-new-equilibrium', 35145),\n", " ('Brazil: A Voice for All?', '- Brasil busca recuperar influencia global mediante diplomacia pragmática y reforma de la arquitectura global.\\n- Persigue asociaciones estratégicas  ... (511 characters truncated) ... an a las potencias emergentes.\\n- Pretende fortalecer organizaciones regionales para la integración latinoamericana y reducir la influencia de EE.UU.', 'https://www.gmfus.org/news/new-geopolitics-alliances-rethinking-transatlantic-engagement-global-swing-states/brazil', 1807),\n", " (\"Brazil's Role in World Affairs and Regional Dynamics\", '- Brasil es la principal economía de Sudamérica y actor clave en asuntos políticos y ambientales.\\n- Históricamente enfatiza diplomacia y multilatera ... (397 characters truncated) ... ón, degradación ambiental y desigualdad.\\n- Es un actor estratégico para temas globales como clima, seguridad alimentaria y gobernanza internacional.', 'https://www.cfr.org/backgrounder/brazils-global-ambitions', 4799),\n", " ('BRICS+ and the Future of Global Order', '- Covers the inaugural BRICS+ summit in Kazan, expanding membership to key emerging economies.\\n- Assesses BRICS+’ potential to challenge Western dom ... (220 characters truncated) ... than a rigid anti-Western bloc.\\n- Recommends constructive engagement by Western institutions to integrate emerging powers and prevent fragmentation.', 'https://carnegieendowment.org/research/2024/10/brics-summit-emerging-middle-powers-g7-g20', 8761),\n", " ('BRICS Expansion and the Future of World Order: Perspectives from Member States, Partners, and Aspirants', '- Details BRICS’ 2024–2025 expansion to include Egypt, Ethiopia, Iran, UAE, and Indonesia.\\n- Examines motivations of member, partner, and aspirant s ... (235 characters truncated) ... amid diverse economic and political systems.\\n- Projects future trajectories based on member motivations and implications for multipolar world order.', 'https://carnegieendowment.org/research/2025/03/brics-expansion-and-the-future-of-world-order-perspectives-from-member-states-partners-and-aspirants?lang=en', 18534),\n", " ('China’s Military Strategy', \"- Documento oficial de estrategia militar China (white paper) del 26 de mayo de 2015.\\n- Define la estrategia de 'defensa activa' con énfasis en desa ... (285 characters truncated) ... ncipios de flexibilidad y movilidad.\\n- Promueve integración civil-militar y cooperación de seguridad internacional no alineada y no confrontacional.\", 'https://news.usni.org/2015/05/26/document-chinas-military-strategy', 8893),\n", " ('Chinese Perspectives on the Indo-Pacific as a Geostrategic Construct', '- El concepto Indo-Pacífico une océanos Pacífico e Índico, destacando seguridad multilateral, integración económica y competencia estratégica.\\n- Exp ... (395 characters truncated) ... esar de la preocupación, desacuerdos internos entre socios de EE. UU. y sus lazos económicos con China limitan la efectividad de un frente unificado.', 'https://strategicspace.nbr.org/chinese-perspectives-on-the-indo-pacific-as-a-geostrategic-construct/', 7366),\n", " ('Conflict Roots and Ukraine’s Reconstruction Significance', '- Ukraine’s post-war reconstruction depends on military victory, a just peace agreement, or security guarantees.\\n- The 2014 Maidan Revolution and Cr ... (353 characters truncated) ... connectivity and NATO posture.\\n- Reconstruction involves internal reforms, integration into European and Eurasian frameworks, and sustained support.', 'https://link.springer.com/chapter/10.1007/978-3-031-66434-2_2', 7709),\n", " ('Euro-Atlantic Security', '- US–Russia adversarial relationship centers on disputes over post–Cold War European security architecture.\\n- Cooperative frameworks in arms control ... (214 characters truncated) ... Atlantic structures fueled regional instability.\\n- Understanding Euro-Atlantic security dynamics is crucial for conflict resolution and cooperation.', 'https://www.academia.edu/35243952/Euro_Atlantic_Security', 13132),\n", " ('For NATO in 2027, European Leadership Will Be Key to Deterrence Against Russia', '- Europe cannot deter rapid Russian aggression in the Baltic without bolstered US and European defense capabilities.\\n- The NATO Force Mix Analysis ( ... (384 characters truncated) ... xercises strengthen alliance readiness.\\n- Emphasis on industrial coordination, joint training, and logistics networks for sustained defense posture.', 'https://www.atlanticcouncil.org/in-depth-research-reports/issue-brief/for-nato-in-2027-european-leadership-will-be-key-to-deterrence-against-russia/', 7769),\n", " ('Geostrategic competition and US, Chinese, and Russian overseas basing', '- Las bases militares en el extranjero son instrumentos clave de proyección de poder de EE. UU., China y Rusia.\\n- Brookings organizó nueve análisis  ... (330 characters truncated) ... ca por acceso y logística de bases.\\n- China y Rusia han invertido en puertos y bases en Europa, África y Pacífico para ampliar su proyección global.', 'https://www.brookings.edu/collection/geostrategic-competition-and-us-chinese-and-russian-overseas-basing/', 10315),\n", " ('Great Power Competition: United States, China, and Russia Strategic Assessment', '- Compara los objetivos estratégicos de EE. UU., China y Rusia de 2000 a 2017, mostrando la transición de la cooperación a la rivalidad.\\n- Evalúa do ... (361 characters truncated) ... orden liberal liderado por EE. UU.\\n- Rusia es vista como riesgo de seguridad regional con una estrategia disruptiva y visión global menos coherente.', 'https://ndupress.ndu.edu/Media/News/News-Article-View/Article/2404308/3a-contemporary-great-power-geostrategic-dynamics-relations-and-strategies/', 22442),\n", " (\"Indonesia's conception of international order: an Asian middle power's engagement with the liberal international order\", '- Indonesia basa su visión del orden internacional en la autonomía, la igualdad soberana y el multilateralismo.\\n- El vicepresidente <PERSON> ( ... (578 characters truncated) ... e el orden liberal.\\n- El estudio desafía la visión binaria liberal-revisionista, resaltando roles complejos de potencias intermedias como Indonesia.', 'https://academic.oup.com/ia/article/99/4/1459/7216732', 14254),\n", " ('Institutional Strategies of ASEAN and ASEAN-led Institutions in Managing South China Sea Disputes (1990–2020)', '- Analyzes ASEAN’s use of balancing, bandwagoning, hedging, and co-option in regional security institutions.\\n- Reviews the ASEAN Foreign Ministers’  ... (238 characters truncated) ... tonomy and prevent great-power dominance.\\n- Evaluates institutional effectiveness based on internal unity and adaptability to external power shifts.', 'https://link.springer.com/chapter/10.1007/978-***********-2_4', 111097),\n", " ('Introduction to special issue of International Politics', '- Central Eurasia’s transformation requires understanding local dynamics for global responses.\\n- Russia’s ambiguous position between East and West s ... (169 characters truncated) ... ian stances and Sino-Russian ties influence Eurasian order.\\n- Adaptability and contestation are essential for maintaining regional and global order.', 'https://pmc.ncbi.nlm.nih.gov/articles/PMC7359758/', 12289),\n", " ('Iran’s Regional Policy Under President <PERSON><PERSON><PERSON>: Continuity and Constraints', '- La política regional de Irán baj<PERSON> mantendrá el apoyo al Eje de Resistencia y la influencia regional.\\n- El Líder Supremo y el CGRI dirig ... (437 characters truncated) ... ados del Golfo, pero se opondrá a la normalización con Israel.\\n- La política de EE.UU. debe anticipar hostilidad y expansión iraní en Oriente Medio.', 'https://www.fpri.org/article/2024/07/pezeshkian-and-irans-regional-policy-continuity-and-grappling-with-structural-constraints/', 3717),\n", " ('La Revue internationale et stratégique (RIS) - Issue 135, Automne 2024', '- Russia uses media control, disinformation, financial support to pro-Russian parties, and cyberattacks.\\n- Frozen conflicts and ethnic minorities ar ... (226 characters truncated) ... dia, Iran, and Gulf states via BRICS and SCO.\\n- State media narratives exploit anti-Western resentment in the Global South for multipolar influence.', 'https://www.iris-france.org/en/russia-in-the-post-soviet-space-what-strategies-of-influence/', 2767),\n", " ('Liquid Alliances in the Middle East: Regionalism and Alliance Formation after the Arab Uprisings', '- Analiza el regionalismo y la formación de alianzas en Oriente Medio (2011–2021) tras las primaveras árabes.\\n- El fracaso de la Liga Árabe y el CCG ... (500 characters truncated) ...  y políticas contradictorias.\\n- Prevén que la inseguridad y las divergencias continuarán favoreciendo alianzas líquidas sobre instituciones sólidas.', 'https://www.tandfonline.com/doi/full/10.1080/14650045.2023.2268542', 19142),\n", " ('Multilateralism in the Twenty-First Century', '- Analyzes how U.S.–China rivalry reshapes international cooperation and multilateral governance.\\n- Identifies leadership quality, legitimacy, and r ... (256 characters truncated) ... sive coalitions set by powerful states.\\n- Recommends oversight, ethical standards, and stakeholder engagement for effective multilateral governance.', 'https://online.ucpress.edu/gp/article/4/1/68310/195239/Multilateralism-in-the-Twenty-First-Century', 10181),\n", " ('NATO Out-of-the-Box vs. the BRICS-SCO System: The Rising World Order', '- Explores NATO’s Out-of-the-Box (OoB) strategy and its validation through global crises since 2014.\\n- Details NATO’s enlargement from 12 to 28 memb ... (195 characters truncated) ... nd anticipate mid- to long-term challenges.\\n- Highlights the role of think-tanks like the Atlantic Club of Bulgaria in shaping NATO’s future debate.', 'https://www.academia.edu/35108709/NATO_Out_of_the_Box_vs_the_BRICS_SCO_System_The_Rising_World_Order', 6901),\n", " ('Responding to Indo-Pacific Rivalry: Middle-Power Coalitions', '- La alianza Australia-EE.UU. sigue siendo clave para la seguridad en el Indo-Pacífico, pero no basta ante dinámicas cambiantes.\\n- La asertividad de ... (608 characters truncated) ... es coaliciones reforzarían la resiliencia regional ante tensiones EE.UU.-China y complementarían las asociaciones existentes sin antagonizar a China.', 'https://nationalinterest.org/feature/the-us-china-rivalry-has-asia-edge-can-middle-powers-create-11085', 3038),\n", " ('Russian-Led Eurasia: Regional Leadership, Hegemony, and the Impact of the Russo–Ukraine War', '- Russia’s hegemony in post-Soviet Eurasia is socially conferred but unstable due to uneven obligations.\\n- The English School and regional bodies (C ... (287 characters truncated) ... ancing and seek diversified partnerships beyond Russia.\\n- Emerging trend: collective regional hegemony may involve China and India alongside Russia.', 'https://academic.oup.com/isq/article/68/3/sqae088/7700246', 19279),\n", " ('Russia’s Neighbors in the Shadow of War', '- Post-Soviet states are reasserting sovereignty and diversifying partnerships beyond Russia.\\n- Public opinion in neighboring countries has shifted  ... (163 characters truncated) ...  but remain fragile and contingent on politics.\\n- Western support must balance democratization goals with avoiding empowerment of autocratic elites.', 'https://carnegieendowment.org/research/2024/05/the-end-of-the-near-abroad?lang=en&center=europe', 21622),\n", " ('Russia’s New Eurasian Security Architecture: Institutional Competition and Identity Cognition', '- Russia’s 2024 proposal aims to reshape Eurasian security based on equality, indivisibility, and multilateralism.\\n- Framework addresses military, i ... (203 characters truncated) ... re central to Russia’s cooperative security vision.\\n- Challenges include Western pressure, diverse regional interests, and complex security threats.', 'https://www.cirsd.org/en/horizons/horizons-winter-2025-issue-no-29/russias-new-eurasian-security-architecture', 4150),\n", " ('Securing a Free and Open World: A US-EU Blueprint to Counter China and Russia', '- El Indo-Pacífico es central para la economía y seguridad global; EE. UU. debe reforzar alianzas en Europa y Asia contra China y Rusia.\\n- La alianz ... (384 characters truncated) ... rtico-Nórdico-Báltico.\\n- La colaboración digital y tecnológica debe enfocarse en IA, computación cuántica, biotecnología e infraestructuras seguras.', 'https://www.atlanticcouncil.org/in-depth-research-reports/report/securing-a-free-and-open-world-a-us-eu-blueprint-to-counter-china-and-russia/', 6851),\n", " ('Southeast Asia and US–China Competition: Contours, Realities and Implications', '- La competencia EE. UU.-China en el Sudeste Asiático es uno de muchos desafíos regionales, con diferentes capacidades diplomáticas, militares y econ ... (461 characters truncated) ... esgo.\\n- Se recomienda fortalecer la agencia regional mediante multi-compromiso, involucrar actores no estatales y evitar socavar la autonomía local.', 'https://www.wilsoncenter.org/article/southeast-asia-and-us-china-competition-contours-realities-and-implications-indo-pacific', 3996),\n", " ('The BRICS’s Bank, Institutional Framework, and Other Current Limitations', '- Examines the New Development Bank (NDB) through <PERSON><PERSON><PERSON>’s concepts of embeddedness and the double movement.\\n- Outlines BRICS’ economic strengths,  ... (218 characters truncated) ... al norms despite NDB’s ambitions.\\n- Concludes the bank’s market orientation constrains its ability to replicate Bretton Woods–style growth dynamics.', 'https://www.tandfonline.com/doi/full/10.1080/********.2020.1720584', 9858),\n", " ('The Era of Coalitions: The Shifting Nature of Alignments in Asia', '- La transición global de la unipolaridad a la multipolaridad genera alineamientos internacionales más complejos y flexibles en lugar de alianzas fij ... (687 characters truncated) ... La era de las coaliciones exige que estados e instituciones se adapten para seguir siendo relevantes en un orden internacional basado en coaliciones.', 'https://fulcrum.sg/the-era-of-coalitions-the-shifting-nature-of-alignments-in-asia/', 4192),\n", " ('The Indo-Pacific Competitive Space: China’s Vision and the Post–World War II American Order', \"- Analiza las visiones divergentes del Indo-Pacífico: EE. UU. defiende un 'Indo-Pacífico Libre y Abierto' frente a la 'comunidad de intereses comunes ... (552 characters truncated) ... nómica está marcada por la inversión china en infraestructura versus el enfoque estadounidense en acuerdos bilaterales e infraestructura alternativa.\", 'https://ndupress.ndu.edu/Media/News/News-Article-View/Article/2404551/9-the-indo-pacific-competitive-space-chinas-vision-and-the-postworld-war-ii-ame/', 27833),\n", " ('The Long Game: China’s Grand Strategy to Displace American Order', '- La competencia EE. UU.-China gira en torno al orden regional y global, con China desplegando tres estrategias secuenciales de desplazamiento milita ... (521 characters truncated) ... rategia asimétrica de EE. UU. que niegue la hegemonía regional china y reinvierta en las bases del orden estadounidense sin competir dólar por dólar.', 'https://www.brookings.edu/articles/the-long-game-chinas-grand-strategy-to-displace-american-order/', 12408),\n", " ('The Middle Power Dynamic in the Indo-Pacific: Unpacking How Vietnam and Indonesia Can Shape Regional Security and Economic Issues', '- Propone una jerarquía regional de cinco niveles: grandes potencias, potencias mayores, potencias intermedias, potencias menores y pequeños estados. ... (519 characters truncated) ... nomía y estabilidad regional.\\n- Potencias intermedias pueden influir significativamente en un orden basado en reglas mediante diplomacia adaptativa.', 'https://www.airuniversity.af.edu/JIPA/Display/Article/2927137/the-middle-power-dynamic-in-the-indo-pacific-unpacking-how-vietnam-and-indonesi/', 11733),\n", " ('The Origins of the Ukraine Crisis and the Need for Collective Security between Russia and the West', '- The Ukraine crisis reflects deeper tensions in the post–Cold War European security architecture.\\n- NATO’s eastward expansion and failure to integr ... (439 characters truncated) ... expansion policies that may undermine their interests.\\n- Long-term stability requires moving from power balancing to collective security principles.', 'https://www.academia.edu/108220996/The_Crisis_of_the_European_Security_in_Response_to_Russian_Aggression_Against_Ukraine', 3598),\n", " ('The positive externalities of US–China institutional balancing in the Indo-Pacific', '- La rivalidad EE. UU.-China incluye competencia institucional donde ambos buscan influir o restringir al otro vía instituciones.\\n- El balancing inc ... (333 characters truncated) ... mbio climático.\\n- Iniciativas como Belt and Road y Blue Dot Network generan bienes públicos e infraestructura, beneficiando la estabilidad regional.', 'https://eastasiaforum.org/2025/06/11/us-china-competition-has-productive-potential-for-the-indo-pacific/', 1575),\n", " ('The Rise of a Multipolar West Asia: Why the Middle East Resists Hegemony', '- Históricamente, Oriente Medio se ha equilibrado entre potencias regionales, evitando la dominación externa.\\n- Tras la Segunda Guerra Mundial, EE.U ... (560 characters truncated) ... icos y de seguridad.\\n- Oriente Medio transita a un orden multipolar donde la hegemonía de cualquier gran potencia resulta estructuralmente inviable.', 'https://peacediplomacy.org/2025/01/29/the-rise-of-a-multipolar-west-asia-why-the-middle-east-resists-hegemony/', 3167),\n", " ('The Shanghai Cooperation Organization and Eurasian Geopolitics: New Directions, Perspectives, and Challenges', '- Positions SCO as a key actor in Eurasian regional security, economic cooperation, and counterterrorism.\\n- Lists SCO members (China, Russia, Centra ... (249 characters truncated) ... explicit opposition to global powers.\\n- Assesses SCO’s future based on member states’ ability to manage internal competition and deepen cooperation.', 'https://www.academia.edu/59857968/The_Shanghai_Cooperation_Organization_and_Eurasian_geopolitics_new_directions_perspectives_and_challenges', 19318)]"]}, "execution_count": 128, "metadata": {}, "output_type": "execute_result"}], "source": ["docs"]}, {"cell_type": "markdown", "id": "4f9ad7bc", "metadata": {}, "source": ["Dominios paywalled o bloqueo de acceso encontrados:\n", "* medium\n", "* springer\n", "* sciencedirect (da info pero no todo accesible)\n", "* routledge. (También no hay info, es una tienda de libros)\n", "* sagepub journals.\n", "* amazon\n", "* scribd (alguno se lo puede leer pero en general aquí suele haber cosas paywalled, o la gente puede subir libros pirateado)\n", "\n", "\n", "Categorias a excluir:\n", "* Paywalled\n", "* Tiendas de libros\n", "* Docs cortos o que no expliquen la info. Igual se puede incluso excluir por cantidad de tokens.\n", "* Enlaces de papers que no contienen la info, en vez de pdf. Ej: https://www.academia.edu/59857968/The_Shanghai_Cooperation_Organization_and_Eurasian_geopolitics_new_directions_perspectives_and_challenges"]}, {"cell_type": "markdown", "id": "5109b803", "metadata": {}, "source": ["### Cont gen process"]}, {"cell_type": "markdown", "id": "a4fc966d", "metadata": {}, "source": ["Here iterate on the instructions and create memories.\n", "\n", "Think about a way of doing this with celery so each task is handled differently. May be good to start using it and set it up to work similar as our tasks. Do some tweaks so it can be scaled with no problem\n", "\n", "Note. Would be great to split into paragraphs, detect if paragraphs have tables or diagrams or even other elements. (Ask <PERSON> for help)"]}, {"cell_type": "code", "execution_count": 22, "id": "db60f4c4", "metadata": {}, "outputs": [], "source": ["llm_manager = DependencyContainer.get_llm_manager()"]}, {"cell_type": "code", "execution_count": 23, "id": "8839f5b1", "metadata": {}, "outputs": [], "source": ["from cont_gen_prompts import prompt_contenido_tema"]}, {"cell_type": "code", "execution_count": 24, "id": "5efb4e82", "metadata": {}, "outputs": [{"data": {"text/plain": ["('Introducción al pensamiento algorítmico y al entorno JavaScript',\n", " [{'epigraph_name': 'Conceptos de algoritmo y pseudocódigo',\n", "   'epigraph_id': 390,\n", "   'epigraph_position': 1,\n", "   'didactic_instructions': '#### Introducción\\nComienza estableciendo la relación entre la resolución de problemas cotidianos y la formulación de un algoritmo, usando un ejemplo sencillo (p. ej., \"preparar una taza de té\"). Destaca que la programación parte de la capacidad humana para describir procesos paso a paso y que esos pasos, una vez formalizados, se convierten en una solución «computable».\\n\\n#### Desarrollo de conceptos clave\\n1. Presenta una definición rigurosa pero accesible de algoritmo; seguidamente enumera sus propiedades (finito, definido, efectivo).\\n2. Diferencia algoritmo, programa y lenguaje de programación, subrayando que el algoritmo es independiente de la sintaxis concreta.\\n3. Explica brevemente la utilidad del pseudocódigo como puente entre la idea y la implementación. Fija unas reglas sintácticas mínimas (uso de mayúsculas para palabras reservadas, sangrías, comentarios con `//`).\\n4. Introduce las tres estructuras básicas (secuencia, selección y repetición) y expón un micro-ejemplo para cada una en pseudocódigo.\\n5. Incluye un ejemplo integral: «calcular el máximo de tres números». Proporciona la descripción en lenguaje natural, el pseudocódigo y un diagrama de flujo mermaid sencillo que contenga inicio, comparaciones y fin. Indica al redactor que mantenga el diagrama en menos de diez nodos.\\n\\n#### Ejercicio propuesto (1 de 2 en todo el tema)\\nPlantea al lector redactar el pseudocódigo para \"contar el número de vocales en una palabra\" y, opcionalmente, bosquejar el diagrama de flujo. Sugiere comprobar la claridad y exhaustividad de los pasos.\\n\\n#### Conexión con epígrafes adyacentes\\nCierra la sección señalando que, aunque los algoritmos pueden escribirse en pseudocódigo, necesitan un lenguaje real para ejecutarse; adelanta que JavaScript será el vehículo elegido y que su historia se explora a continuación.\\n\\n#### Extensión esperada\\nIndica que el contenido resultante debe oscilar entre 1 100 y 1 300 palabras, incorporando un bloque de pseudocódigo y un diagrama mermaid.'},\n", "  {'epigraph_name': 'Historia y evolución de JavaScript',\n", "   'epigraph_id': 391,\n", "   'epigraph_position': 2,\n", "   'didactic_instructions': '#### Introducción\\nContextualiza la aparición de JavaScript a mediados de los años 90 como respuesta a la necesidad de dotar de dinamismo al navegador. Enlaza con la idea previa: las computadoras necesitaban un lenguaje sencillo para plasmar algoritmos directamente en la web.\\n\\n#### Desarrollo de conceptos clave\\n1. Traza una narrativa cronológica que cubra: creación por <PERSON> (1995), estandarización ECMAScript (1997), Ajax (2005), ES6/ES2015 (2015) y la irrupción de Node.js (2009) hasta la actualidad.\\n2. Explica brevemente la diferencia entre JavaScript, ECMAScript y los motores de ejecución (V8, SpiderMonkey). Destaca cómo cada avance amplió la capacidad de expresar algoritmos complejos.\\n3. Incluye una tabla con tres columnas (Versión, Año, Novedades relevantes) para ES3, ES5, ES6, ES7+, seña<PERSON>o caracterís<PERSON>s que serán ú<PERSON> en el curso (p. ej., `let/const`, arrow functions, `Map`, `Set`, `async/await`).\\n4. Aclara la expansión de JavaScript más allá del navegador: introducción de Node.js y su impacto en la programación de servidores, desarrollo de herramientas (npm, frameworks) y la convergencia \"full-stack\".\\n5. Ofrece un pequeño diagrama mermaid de línea temporal (opcional) para ilustrar cinco hitos esenciales, indicando al redactor mantenerlo simple y lineal.\\n\\n#### Conexión con epígrafes adyacentes\\nConcluye señalando que, para beneficiarse de toda esta evolución, el estudiante instalará en la siguiente sección el entorno de ejecución (Node.js) y un editor moderno (VS Code).\\n\\n#### Extensión esperada\\nSugiere entre 950 y 1 150 palabras, con una tabla y, opcionalmente, un diagrama mermaid.'},\n", "  {'epigraph_name': 'Instalación del entorno de desarrollo (Node.js, VS Code)',\n", "   'epigraph_id': 392,\n", "   'epigraph_position': 3,\n", "   'didactic_instructions': '#### Introducción\\nMotiva la necesidad de un entorno local que permita ejecutar los algoritmos del curso sin depender del navegador. Resalta la dualidad ejecución-servidor (Node.js) y edición (VS Code) como estándar de facto.\\n\\n#### Desarrollo de conceptos clave\\n1. Describe brevemente qué es Node.js y cómo actúa como motor en línea de comandos; diferencia entre LTS y versión actual.\\n2. Proporciona una guía paso a paso para instalar Node.js en Windows, macOS y Linux (descarga, asistente o gestor de paquetes). Incluye comandos de verificación `node -v` y `npm -v` en bloques de código.\\n3. Introduce el concepto de npm y enumsiona su papel como gestor de dependencias, pero pospone el uso avanzado para temas posteriores.\\n4. Explica la instalación de VS Code: descarga, configuración inicial y localización del terminal integrado.\\n5. Recomienda extensiones mínimas (ESLint, <PERSON><PERSON><PERSON>, JavaScript ES6 snippets) y muestra cómo habilitarlas en una tabla (Nombre, Propósito, Comando/Ruta de instalación).\\n6. Enseña a crear una carpeta de proyecto, abrirla en VS Code, generar `index.js` y ejecutar `node index.js` desde el terminal.\\n\\n#### Ejercicio propuesto (2 / 2 en todo el tema)\\nPide al estudiante crear la carpeta `intro-js`, añadir `index.js` con `console.log(\\'Entorno listo\\');`, ejecutarlo y hacer una captura de la versión de Node (se menciona que la captura no se incluirá en la redacción; basta con comprobar la salida en el terminal).\\n\\n#### Conexión con epígrafes adyacentes\\nSeñala que el entorno ya listo permitirá ejecutar el primer programa real del curso en la siguiente sección, enlazando con la inminente introducción al \"Hola, mundo\".\\n\\n#### Extensión esperada\\nRecomienda entre 850 y 1 050 palabras e incorporar una tabla de extensiones y varios bloques de comandos.'},\n", "  {'epigraph_name': 'Primer script: \"Hola, mundo\" en consola y navegador',\n", "   'epigraph_id': 393,\n", "   'epigraph_position': 4,\n", "   'didactic_instructions': \"#### Introducción\\nPresenta la primera oportunidad de transformar el pseudocódigo en un programa real. Destaca la gratificación inmediata de ver un resultado tangible y cómo esto afianza el vínculo entre teoría y práctica.\\n\\n#### Desarrollo de conceptos clave\\n1. Muestra el código mínimo en Node.js:\\n```javascript\\nconsole.log('Hola, mundo');\\n```\\n   • Explica la función `console.log`, su salida en la terminal y cómo ejecutarla con `node`.\\n2. Replica la experiencia en el navegador con un archivo HTML elemental que incluya un `<script>` interno y, en un segundo paso, un script externo. Indica cómo abrir DevTools y visualizar la consola.\\n3. Contrasta la ejecución en consola con la instrucción `alert('Hola, mundo');`, comentando brevemente la naturaleza modal de `alert` y su utilidad limitada.\\n4. Introduce la noción de interacción mínima mediante `prompt` para recoger el nombre del usuario y saludar usando template literals.\\n```javascript\\nconst nombre = prompt('¿Cómo te llamas?');\\nconsole.log(`¡Hola, ${nombre}!`);\\n```\\n5. Explica someramente la diferencia de alcance entre código que corre en Node y en el navegador (objetos `window`, `global`) sin profundizar todavía.\\n\\n#### Ejercicio propuesto\\nPide al estudiante modificar el script para que muestre el momento actual (con `Date`) y salude indicando la hora. Deja abierta la preparación para el próximo tema sobre variables y tipos de datos.\\n\\n#### Conexión con epígrafes adyacentes\\nFinaliza subrayando que, para enriquecer este simple programa, se necesitará entender cómo se declaran y utilizan variables y operadores, tema que se abordará a continuación.\\n\\n#### Extensión esperada\\nEntre 800 y 1 000 palabras, con dos listados de código y sin necesidad de tablas ni diagramas.\"}])"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["ti_result[0].topic_name, ti_result[0].epigraphs"]}, {"cell_type": "markdown", "id": "795e3078", "metadata": {}, "source": ["Vamos a probar búsqueda de queries sobre fuentes que tenemos con esta info.\n", "\n", "Y sacar suficientes queries y docs reelevantes para buscar sobre ello. \n", "\n", "Vamos a probar dos opciones:\n", "\n", "* 1. Modelo crea queries de búsqueda y acaba dando info reelevante. Aquí duda si dejar coger un doc entero -> Más determinista y fácil de evaluar. Da info sobre qué queries se han hecho (ayudará a darle contexto sobre en qué epigrafes puede usar)\n", "* 2. Mientras genera el modelo puede buscar entre medio, darle tool, menos determinista pero admite más potencial de mejora.\n", "* 3. <PERSON><PERSON> el<PERSON> los docs enteros que quiere leerse\n", "\n", "Estaría guay que pudieramos almacenar en la file api de openai los archivos para poder usarlos como contexto enteros a veces.\n", "\n", "Otras cosas que se pueden probar: de<PERSON><PERSON> leerse más info, darle más top k del rerank."]}, {"cell_type": "code", "execution_count": 25, "id": "c9f477eb", "metadata": {}, "outputs": [], "source": ["from openai import AsyncOpenAI"]}, {"cell_type": "code", "execution_count": 26, "id": "70fe07f9", "metadata": {}, "outputs": [], "source": ["client = AsyncOpenAI(api_key=os.getenv(\"OpenaiApiKey\"))"]}, {"cell_type": "markdown", "id": "a2s2xek59h9", "metadata": {}, "source": ["#### Option 1: Multiple Query Search Approach\n", "\n", "This approach allows the model to generate multiple search queries and retrieve relevant documents for each query before generating content."]}, {"cell_type": "code", "execution_count": null, "id": "vhcumta4opt", "metadata": {}, "outputs": [], "source": ["from src.api.common.services.content_generator.utils.formatting import format_docs\n", "from pydantic import BaseModel\n", "\n", "class Query(BaseModel):\n", "    query: str\n", "    \n", "class SearchQueries(BaseModel):\n", "    queries: list[Query]\n", "\n", "async def generate_search_queries(\n", "    topic_info: dict, \n", "    model: str = \"gpt-4.1\"\n", ") -> list[str]:\n", "    \n", "    system_prompt = \"\"\"\n", "    Tu tarea es generar queries de búsqueda generales para generar un tema sobre un contexto que se te proporcione.\n", "    Se te proporcionará un plan de lo que se va a escribir en el tema, y has de hacer queries que permitan cubrir todas las areas generales necesarias que puedan ser complementadas con fuentes.\n", "    Ya se ha encontrado información reelevante para esta información.\n", "    Lo que debes hacer es generar queries para encontrar pasajes granulares sobre las fuentes que permitan asistir en la generación de contenido.\n", "    Apuntarás a hacer entre 6-10 queries totales que permitan cubrir las partes más importantes para las cuales sería reelevante la inclusión de fuentes.\n", "    \"\"\"\n", "    for e in topic_info.epigraphs: \n", "        e.pop(\"epigraph_id\")\n", "    response = await client.responses.parse(\n", "        instructions=system_prompt,\n", "        text_format=SearchQueries,\n", "        model=model,\n", "        input = f\"Topic name: {topic_info.topic_name}\\n\\nEpigraphs info and instructions: {topic_info.epigraphs}\"\n", "    )\n", "    \n", "    return response.output_parsed"]}, {"cell_type": "code", "execution_count": 28, "id": "t5fwrxmou3m", "metadata": {}, "outputs": [], "source": ["from typing import Any\n", "\n", "async def search_documents_multi_query(\n", "    queries: SearchQueries,\n", "    epigrafe_ids: list[int],\n", "    retriever: Any,\n", "    top_k: int = 5,\n", "    min_score: float = 0.6,\n", ") -> dict[str, any]:\n", "    \"\"\"\n", "    Search documents using multiple queries and aggregate results.\n", "    \n", "    Args:\n", "        queries: SearchQueries object with list of queries\n", "        epigrafe_ids: List of epigraph IDs to search within\n", "        retriever: Vector retriever instance\n", "        reranker: <PERSON><PERSON><PERSON> instance\n", "        top_k_retrieve: Number of documents to retrieve from vector search\n", "    \n", "    Returns:\n", "        Dictionary with aggregated results and metadata\n", "    \"\"\"\n", "    all_chunks = []\n", "    query_results = {}\n", "    seen_chunk_ids = set()\n", "    \n", "    for query_obj in queries.queries:\n", "        query = query_obj.query\n", "        try:\n", "            retriever.base_retriever.epigrafe_ids = epigrafe_ids\n", "            chunks = await retriever.ainvoke(query, top_k = top_k)\n", "            valid_chunks = [chunk for chunk in chunks if chunk.metadata.get('relevance_score', 0) > min_score]\n", "            if valid_chunks:\n", "                query_results[query] = {\n", "                        'chunks': valid_chunks,\n", "                        'count': len(valid_chunks),\n", "                        'scores': [chunk.metadata.get('relevance_score', 0) for chunk in valid_chunks]\n", "                    }\n", "                for chunk in valid_chunks:\n", "                    chunk_id = f\"{chunk.metadata.get('doc_id')}_{chunk.metadata.get('chunk_id', '')}\"\n", "                    if chunk_id not in seen_chunk_ids:\n", "                        seen_chunk_ids.add(chunk_id)\n", "                        chunk.metadata['search_query'] = query\n", "                        all_chunks.append(chunk)\n", "                        \n", "        except Exception as e:\n", "            print(f\"Error searching for query '{query}': {e}\")\n", "            query_results[query] = {'error': str(e), 'chunks': [], 'count': 0}\n", "    \n", "    formatted_docs = format_docs(all_chunks) if all_chunks else \"\"\n", "    \n", "    return {\n", "        'all_chunks': all_chunks,\n", "        'formatted_docs': formatted_docs,\n", "        'query_results': query_results,\n", "        'total_chunks': len(all_chunks),\n", "        'queries_executed': len(queries.queries)\n", "    }"]}, {"cell_type": "code", "execution_count": 29, "id": "ehp2b73qk", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 Testing with topic: Control de flujo y estructuras condicionales\n", "📝 First epigraph: Estructuras de decisión if, else y switch\n", "\n", "1️⃣ Generating search queries...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Generated 10 queries:\n", "   1. explicación sobre el concepto de flujo de control en programación y ejemplos de estructuras condicionales (if, else, switch) en JavaScript\n", "   2. cómo funcionan las expresiones booleanas en JavaScript y qué valores son considerados truthy y falsy\n", "   3. diferencias entre el operador de igualdad (==) y el operador de igualdad estricta (===) en JavaScript con ejemplos prácticos\n", "   4. ventajas de utilizar switch sobre múltiples else if en estructuras de control y cómo se utiliza el break para evitar el fall-through\n", "   5. representaciones visuales (diagramas de flujo o Mermaid) simples sobre bifurcaciones de decisión en algoritmos\n", "   6. ejemplos prácticos de scripts que clasifican valores usando if, else if y else en JavaScript\n", "   7. có<PERSON> diseñar ejercicios para practicar el uso de switch en casos discretos, como la simulación de un semáforo\n", "   8. introducción general sobre la importancia de los bucles en la programación para automatizar tareas repetitivas\n", "   9. sintaxis, estructura y diferencias de for, while y do-while en JavaScript, con ejemplos orientados a principiantes\n", "   10. riesgos comunes y mejores prácticas para evitar bucles infinitos en JavaScript, incluyendo ejemplos de código y estrategias de depuración\n", "\n", "2️⃣ Searching documents...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://cohere-rerank-v3-5-svhyz.swedencentral.models.ai.azure.com/v2/rerank \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://cohere-rerank-v3-5-svhyz.swedencentral.models.ai.azure.com/v2/rerank \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://cohere-rerank-v3-5-svhyz.swedencentral.models.ai.azure.com/v2/rerank \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://cohere-rerank-v3-5-svhyz.swedencentral.models.ai.azure.com/v2/rerank \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://cohere-rerank-v3-5-svhyz.swedencentral.models.ai.azure.com/v2/rerank \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://cohere-rerank-v3-5-svhyz.swedencentral.models.ai.azure.com/v2/rerank \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://cohere-rerank-v3-5-svhyz.swedencentral.models.ai.azure.com/v2/rerank \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://cohere-rerank-v3-5-svhyz.swedencentral.models.ai.azure.com/v2/rerank \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://cohere-rerank-v3-5-svhyz.swedencentral.models.ai.azure.com/v2/rerank \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://cohere-rerank-v3-5-svhyz.swedencentral.models.ai.azure.com/v2/rerank \"HTTP/1.1 200 OK\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "📊 Results summary:\n", "   • Total chunks found: 23\n", "   • Queries executed: 10\n", "\n", "📋 Results per query:\n", "   • 'explicación sobre el concepto de flujo de control ...': 5 chunks\n", "     Top score: 0.788\n", "   • 'cómo funcionan las expresiones booleanas en JavaSc...': 1 chunks\n", "     Top score: 0.646\n", "   • 'ejemplos prácticos de scripts que clasifican valor...': 5 chunks\n", "     Top score: 0.741\n", "   • 'introducción general sobre la importancia de los b...': 5 chunks\n", "     Top score: 0.825\n", "   • 'sintaxis, estructura y diferencias de for, while y...': 5 chunks\n", "     Top score: 0.843\n", "   • 'riesgos comunes y mejores prácticas para evitar bu...': 5 chunks\n", "     Top score: 0.759\n"]}], "source": ["# Test the pipeline with a sample topic\n", "async def test_multi_query_pipeline():\n", "    retriever = DependencyContainer.get_retriever()\n", "    \n", "    topic_info = ti_result[2]\n", "    first_epigraph = topic_info.epigraphs[0]\n", "    epigrafe_ids = [ep['epigraph_id'] for ep in topic_info.epigraphs]\n", "    \n", "    print(f\"🔍 Testing with topic: {topic_info.topic_name}\")\n", "    print(f\"📝 First epigraph: {first_epigraph['epigraph_name']}\")\n", "    print(\"\\n1️⃣ Generating search queries...\")\n", "    queries = await generate_search_queries(topic_info)\n", "    print(f\"Generated {len(queries.queries)} queries:\")\n", "    for i, q in enumerate(queries.queries):\n", "        print(f\"   {i+1}. {q.query}\")\n", "    \n", "    print(\"\\n2️⃣ Searching documents...\")\n", "    results = await search_documents_multi_query(\n", "        queries=queries,\n", "        epigrafe_ids=epigrafe_ids,\n", "        retriever=retriever\n", "    )\n", "    \n", "    print(f\"\\n📊 Results summary:\")\n", "    print(f\"   • Total chunks found: {results['total_chunks']}\")\n", "    print(f\"   • Queries executed: {results['queries_executed']}\")\n", "    \n", "    print(f\"\\n📋 Results per query:\")\n", "    for query, data in results['query_results'].items():\n", "        if 'error' not in data:\n", "            print(f\"   • '{query[:50]}...': {data['count']} chunks\")\n", "            if data['scores']:\n", "                print(f\"     Top score: {max(data['scores']):.3f}\")\n", "    \n", "    return results\n", "\n", "test_results = await test_multi_query_pipeline()"]}, {"cell_type": "code", "execution_count": 16, "id": "2522f9a5", "metadata": {}, "outputs": [], "source": ["docs, *_ = test_results[\"formatted_docs\"]"]}, {"cell_type": "code", "execution_count": 30, "id": "8784caec", "metadata": {}, "outputs": [], "source": ["from cont_gen_prompts import prompt_contenido_tema"]}, {"cell_type": "code", "execution_count": null, "id": "9f1808bb", "metadata": {}, "outputs": [], "source": ["llm = llm_manager.get_llm(provider=\"anthropic\", model_name=\"claude-sonnet-4-20250514\", max_tokens=40000, temperature = 0, reasoning_effort=\"medium\")"]}, {"cell_type": "markdown", "id": "146a79dd", "metadata": {}, "source": ["Tengo que meterle para que use fuentes en el prompt"]}, {"cell_type": "code", "execution_count": 32, "id": "f6d070ef", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n"]}], "source": ["compiled_prompt = prompt_contenido_tema.compile(asignatura = ti_result[2].subject_name, indice_asignatura = subject_index, plan = ti_list[2],nombre_tema = ti_result[2].topic_name, memorias = acc_mem_topics[1], fuentes_relacionadas = docs).to_langchain()\n", "result = llm.invoke(compiled_prompt)"]}, {"cell_type": "markdown", "id": "1c36d77e", "metadata": {}, "source": ["Parse the topic info.\n", "\n", "<PERSON>llar tablas, diagramas y separar los epígrafes, hacer validación de que los proporcione bien y exactos, sino hacer retry.\n", "\n", "Pedirselo igual en el campo de reasoning. O pedirle que haga un function call con los que decida usar. O un xml al principio, o intercalados incluso después de cada epígrafe.\n", "\n", "```\n", "<fuentes></fuentes\n", "```"]}, {"cell_type": "code", "execution_count": 34, "id": "cc9e00d6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["## Control de flujo y estructuras condicionales\n", "\n", "### Estructuras de decisión if, else y switch\n", "\n", "<fuentes>[0,1,2,3,5]</fuentes>\n", "\n", "Después de dominar la declaración de variables y el uso de operadores, el siguiente paso natural en la construcción de algoritmos es aprender a tomar decisiones en función de condiciones lógicas. Los programas no siempre siguen una secuencia lineal; a menudo necesitan evaluar situaciones y responder de manera diferente según las circunstancias. Las estructuras de decisión que presentaremos en esta sección permiten alterar el flujo secuencial del programa, creando bifurcaciones que evalúan expresiones booleanas para determinar qué bloque de código ejecutar. Esta capacidad de tomar decisiones es fundamental para crear programas inteligentes y adaptativos, y más adelante veremos cómo se combinan con bucles para resolver problemas complejos.\n", "\n", "#### Conceptos clave y sintaxis básica\n", "\n", "El **flujo** de un programa se refiere al orden en que se ejecutan las instrucciones. <PERSON><PERSON>, JavaScript ejecuta las sentencias línea por línea, de arriba hacia abajo, pero las estructuras de control nos permiten modificar este comportamiento natural.\n", "\n", "La base de las estructuras de decisión es la evaluación de **expresiones booleanas**. Como vimos en el tema anterior sobre tipos primitivos, JavaScript utiliza conceptos de \"truthy\" y \"falsy\" para determinar qué valores se consideran verdaderos o falsos en contextos booleanos. Los valores falsy incluyen `false`, `0`, `\"\"`, `null`, `undefined` y `NaN`, mientras que todos los demás valores son truthy.\n", "\n", "La estructura `if` y `else` constituye el mecanismo fundamental para tomar decisiones en JavaScript. Su sintaxis completa es:\n", "\n", "```javascript\n", "if (condición) {\n", "    // código a ejecutar si la condición es verdadera\n", "} else {\n", "    // código a ejecutar si la condición es falsa\n", "}\n", "```\n", "\n", "Es crucial utilizar llaves para delimitar los bloques de código, mantener una indentación consistente para mejorar la legibilidad, y emplear el operador de igualdad estricta (`===`) para evitar coerciones inesperadas de tipo:\n", "\n", "```javascript\n", "let edad = 18;\n", "\n", "if (edad === 18) {\n", "    console.log(\"Acabas de cumplir la mayoría de edad\");\n", "} else {\n", "    console.log(\"Tu edad es diferente a 18 años\");\n", "}\n", "```\n", "\n", "Para evaluar múltiples condiciones, podemos encadenar estructuras usando `else if`:\n", "\n", "```javascript\n", "let edad = 25;\n", "\n", "if (edad < 18) {\n", "    console.log(\"Menor de edad\");\n", "} else if (edad >= 18 && edad < 65) {\n", "    console.log(\"Adulto\");\n", "} else {\n", "    console.log(\"Adulto mayor\");\n", "}\n", "```\n", "\n", "Es importante tener en cuenta que cada condición adicional incrementa el costo computacional, ya que JavaScript debe evaluar las expresiones secuencialmente hasta encontrar una verdadera.\n", "\n", "La estructura `switch` ofrece una alternativa más elegante cuando necesitamos comparar una variable con múltiples valores discretos. Es especialmente útil para evitar largas cadenas de `else if` que pueden resultar difíciles de leer:\n", "\n", "```javascript\n", "let color = \"rojo\";\n", "\n", "switch (color) {\n", "    case \"rojo\":\n", "        console.log(\"Color cálido\");\n", "        break;\n", "    case \"azul\":\n", "        console.log(\"Color frío\");\n", "        break;\n", "    case \"verde\":\n", "        console.log(\"Color natural\");\n", "        break;\n", "    default:\n", "        console.log(\"Color no reconocido\");\n", "        break;\n", "}\n", "```\n", "\n", "Una característica fundamental del `switch` es el \"fall-through\" o \"caída libre\". Si omitimos la declaración `break`, la ejecución continuará hacia el siguiente caso, lo que puede ser útil en ciertos escenarios pero también una fuente común de errores. Por esta razón, es una práctica recomendada incluir siempre `break` al final de cada caso, incluso en el `default`.\n", "\n", "#### Visualización del flujo\n", "\n", "Para comprender mejor cómo las estructuras de decisión alteran el flujo del programa, observemos el siguiente diagrama:\n", "\n", "<mermaid>\n", "flowchart TD\n", "    A[Inicio] --> B{Condición}\n", "    B -->|true| C[Ejecutar bloque if]\n", "    B -->|false| D[<PERSON><PERSON><PERSON><PERSON> bloque else]\n", "    C --> <PERSON>[Con<PERSON>uar]\n", "    D --> <PERSON>[<PERSON><PERSON>uar]\n", "</mermaid>\n", "\n", "*Figura 1. Flujo de control en una estructura if-else. El diagrama muestra cómo el programa toma diferentes caminos según el resultado de la evaluación de la condición.*\n", "\n", "#### <PERSON><PERSON><PERSON><PERSON> gui<PERSON>\n", "\n", "Veamos un ejemplo completo que demuestra el uso práctico de las estructuras de decisión:\n", "\n", "```javascript\n", "// Solicitar la edad al usuario\n", "let edad = parseInt(prompt(\"Ingresa tu edad:\"));\n", "\n", "// Validar que la entrada sea un número válido\n", "if (isNaN(edad) || edad < 0) {\n", "    console.log(\"Por favor, ingresa una edad válida\");\n", "} else if (edad < 18) {\n", "    console.log(\"Eres menor de edad\");\n", "    console.log(\"No puedes votar ni conducir\");\n", "} else if (edad >= 18 && edad < 65) {\n", "    console.log(\"Eres adulto\");\n", "    console.log(\"Tienes todos los derechos civiles\");\n", "} else {\n", "    console.log(\"Eres adulto mayor\");\n", "    console.log(\"Puedes acceder a descuentos especiales\");\n", "}\n", "\n", "// Mensaje final que siempre se ejecuta\n", "console.log(\"<PERSON><PERSON><PERSON> por usar nuestro sistema\");\n", "```\n", "\n", "En este ejemplo, primero validamos que la entrada sea un número válido utilizando `isNaN()`. <PERSON>ego clasificamos al usuario según su edad usando una cadena de `else if`. Los comentarios en línea conectan cada sección con la teoría: la primera condición maneja casos de error, las siguientes evalúan rangos de edad específicos, y la estructura garantiza que solo se ejecute un bloque de código.\n", "\n", "#### <PERSON><PERSON><PERSON><PERSON> propuesto\n", "\n", "Crea un script que simule un semáforo. El programa debe solicitar al usuario que ingrese un color (\"rojo\", \"amarillo\", \"verde\") y mostrar el mensaje correspondiente usando una estructura `switch`:\n", "\n", "- \"rojo\": \"Alto - Detente completamente\"\n", "- \"amarillo\": \"Precaución - Prepárate para detenerte\"\n", "- \"verde\": \"Siga - Puedes avanzar\"\n", "- <PERSON><PERSON><PERSON><PERSON> otro valor: \"Color no válido\"\n", "\n", "```javascript\n", "let color = prompt(\"Ingresa el color del semáforo (rojo, amarillo, verde):\");\n", "\n", "// Tu código aquí\n", "```\n", "\n", "#### Transición al siguiente epígrafe\n", "\n", "Las estructuras de decisión que hemos estudiado nos permiten controlar el flujo del programa tomando decisiones únicas basadas en condiciones específicas. Sin embargo, en muchas situaciones necesitamos repetir acciones múltiples veces, como procesar listas de datos, realizar cálculos iterativos o automatizar tareas repetitivas. En la siguiente sección exploraremos los bucles, que nos proporcionarán las herramientas necesarias para ejecutar bloques de código de manera repetitiva y eficiente.\n", "\n", "### B<PERSON>les for, while y do-while\n", "\n", "<fuentes>[7,8,9,11,14]</fuentes>\n", "\n", "Después de aprender a tomar decisiones únicas con estructuras condicionales, el siguiente paso es dominar la repetición de acciones. Los bucles son estructuras fundamentales que permiten ejecutar bloques de código múltiples veces, lo que resulta esencial para tareas como procesar listas de datos, realizar cálculos iterativos, automatizar procesos repetitivos y crear interfaces interactivas. Sin los bucles, tendríamos que escribir el mismo código una y otra vez, lo que resultaría impracticable para resolver problemas reales.\n", "\n", "#### Idea general de iteración\n", "\n", "Un bucle implementa el concepto de **iteración**: la repetición controlada de un conjunto de instrucciones. Todos los bucles comparten tres elementos fundamentales:\n", "\n", "1. **Condición de continuación**: determina si el bucle debe seguir ejecutándose\n", "2. **<PERSON>uer<PERSON> del bucle**: el bloque de código que se repite\n", "3. **Actualización de variables de control**: modifica las variables que afectan la condición\n", "\n", "Para ilustrar este concepto, imaginemos contar los escalones mientras subimos una escalera: comenzamos en el escalón 0, verificamos si hemos llegado al final (condición), subimos un escalón (cuerpo), aumentamos nuestro contador (actualización), y repetimos el proceso hasta llegar arriba.\n", "\n", "#### Sintaxis y estructura de los bucles principales\n", "\n", "##### Bucle `for` clásico\n", "\n", "El bucle `for` es ideal cuando conocemos de antemano el número de iteraciones que queremos realizar. Su sintaxis compacta incluye los tres elementos fundamentales en una sola línea:\n", "\n", "```javascript\n", "for (inicialización; condición; actualización) {\n", "    // cuerpo del bucle\n", "}\n", "```\n", "\n", "Ejemplo práctico para mostrar los números del 1 al 5:\n", "\n", "```javascript\n", "for (let i = 1; i <= 5; i++) {\n", "    console.log(`Número: ${i}`);\n", "}\n", "```\n", "\n", "La variable `i` se declara con `let` dentro del bucle, lo que limita su visibilidad al ámbito del bucle, evitando conflictos con otras variables del mismo nombre.\n", "\n", "##### Bucle `while`\n", "\n", "El bucle `while` evalúa la condición antes de cada iteración y es perfecto cuando no conocemos el número exacto de repeticiones:\n", "\n", "```javascript\n", "while (condición) {\n", "    // cuerpo del bucle\n", "    // actualización de variables\n", "}\n", "```\n", "\n", "Ejemplo para leer valores hasta que el usuario introduzca \"salir\":\n", "\n", "```javascript\n", "let entrada = \"\";\n", "while (entrada !== \"salir\") {\n", "    entrada = prompt(\"Escribe 'salir' para terminar:\");\n", "    if (entrada !== \"salir\") {\n", "        console.log(`Escribiste: ${entrada}`);\n", "    }\n", "}\n", "console.log(\"¡Hasta luego!\");\n", "```\n", "\n", "##### Bucle `do-while`\n", "\n", "El bucle `do-while` garantiza que el código se ejecute al menos una vez, ya que evalúa la condición después de la primera ejecución:\n", "\n", "```javascript\n", "do {\n", "    // cuerpo del bucle\n", "    // actualización de variables\n", "} while (condición);\n", "```\n", "\n", "Ejemplo para crear un menú interactivo:\n", "\n", "```javascript\n", "let opcion;\n", "do {\n", "    console.log(\"=== MENÚ ===\");\n", "    console.log(\"1. Opción A\");\n", "    console.log(\"2. Opción B\");\n", "    console.log(\"3. <PERSON>ir\");\n", "    \n", "    opcion = parseInt(prompt(\"Selecciona una opción:\"));\n", "    \n", "    switch (opcion) {\n", "        case 1:\n", "            console.log(\"Ejecutando opción A\");\n", "            break;\n", "        case 2:\n", "            console.log(\"Ejecutando opción B\");\n", "            break;\n", "        case 3:\n", "            console.log(\"Saliendo del programa\");\n", "            break;\n", "        default:\n", "            console.log(\"Opción no válida\");\n", "    }\n", "} while (opcion !== 3);\n", "```\n", "\n", "#### Tabla comparativa\n", "\n", "| **Bucle** | **Se evalúa la condición** | **Uso típico** | **Riesgo común** |\n", "|:---------:|:--------------------------:|:--------------:|:----------------:|\n", "| `for` | Antes de cada iteración | Número conocido de iteraciones | Condición mal formulada |\n", "| `while` | Antes de cada iteración | Condición de parada variable | Olvidar actualizar variable de control |\n", "| `do-while` | Después de cada iteración | Ejecutar al menos una vez | Condición que nunca se cumple |\n", "\n", "*Tabla 1. Comparación de los tipos de bucles en JavaScript.*\n", "\n", "#### <PERSON>je<PERSON><PERSON> integrado\n", "\n", "Veamos un ejemplo que calcula la suma y el producto de los primeros `n` números naturales:\n", "\n", "```javascript\n", "// Versión con bucle for\n", "let n = parseInt(prompt(\"Ingresa un número:\"));\n", "let sumaFor = 0;\n", "let productoFor = 1;\n", "\n", "console.log(\"=== Usando bucle for ===\");\n", "for (let i = 1; i <= n; i++) {\n", "    sumaFor += i;           // Acumular suma\n", "    productoFor *= i;       // Acumular producto\n", "    console.log(`Iteración ${i}: suma=${sumaFor}, producto=${productoFor}`);\n", "}\n", "\n", "console.log(`Suma total: ${sumaFor}`);\n", "console.log(`Producto total: ${productoFor}`);\n", "\n", "// Versión equivalente con bucle while\n", "let sumaWhile = 0;\n", "let productoWhile = 1;\n", "let contador = 1;\n", "\n", "console.log(\"\\n=== Usando bucle while ===\");\n", "while (contador <= n) {\n", "    sumaWhile += contador;\n", "    productoWhile *= contador;\n", "    console.log(`Iteración ${contador}: suma=${sumaWhile}, producto=${productoWhile}`);\n", "    contador++;  // Crucial: actualizar el contador\n", "}\n", "\n", "console.log(`Suma total: ${sumaWhile}`);\n", "console.log(`Producto total: ${productoWhile}`);\n", "```\n", "\n", "La comparación entre ambas implementaciones muestra que el bucle `for` es más compacto y menos propenso a errores, ya que la actualización del contador está integrada en la estructura del bucle.\n", "\n", "#### Breve mención a la legibilidad\n", "\n", "La elección entre diferentes tipos de bucles debe basarse en la naturaleza del problema y la claridad del código resultante. El bucle `for` es generalmente preferible cuando conocemos el número de iteraciones, mientras que `while` es más apropiado para condiciones de parada dinámicas. El bucle `do-while` debe reservarse para situaciones donde garantizar al menos una ejecución es crítico para la lógica del programa. La legibilidad y mantenibilidad del código deben ser consideraciones primordiales al elegir la estructura de bucle más apropiada.\n", "\n", "### Control de bucle (break, continue) y patrones de iteración\n", "\n", "<fuentes>[4,6,9,14]</fuentes>\n", "\n", "Una vez que dominamos la creación de bucles básicos, es esencial aprender a refinar su control desde el interior. Los bucles no siempre necesitan ejecutarse completamente; a menudo requerimos mecanismos para terminar la iteración prematuramente cuando se cumple una condición específica, o para saltar ciertos pasos sin detener todo el proceso. Las palabras clave `break` y `continue` proporcionan este control granular sobre el flujo de iteración.\n", "\n", "#### <PERSON><PERSON><PERSON> de <PERSON>break`\n", "\n", "La declaración `break` detiene inmediatamente la ejecución del bucle actual y transfiere el control a la primera línea de código que sigue después del bucle. Es especialmente útil en algoritmos de búsqueda donde queremos detener el proceso tan pronto como encontremos lo que buscamos.\n", "\n", "Ejemplo de búsqueda lineal en un arreglo:\n", "\n", "```javascript\n", "let numeros = [3, 7, 12, 8, 21, 15, 4];\n", "let objetivo = 21;\n", "let encontrado = false;\n", "let posicion = -1;\n", "\n", "for (let i = 0; i < numeros.length; i++) {\n", "    console.log(`Verificando posición ${i}: ${numeros[i]}`);\n", "    \n", "    if (numeros[i] === objetivo) {\n", "        encontrado = true;\n", "        posicion = i;\n", "        break;  // Detener la búsqueda inmediatamente\n", "    }\n", "}\n", "\n", "if (encontrado) {\n", "    console.log(`Número ${objetivo} encontrado en la posición ${posicion}`);\n", "} else {\n", "    console.log(`Número ${objetivo} no encontrado en el arreglo`);\n", "}\n", "```\n", "\n", "El uso de `break` en este ejemplo tiene un impacto significativo en la complejidad temporal: en el mejor caso (elemento encontrado al principio), el algoritmo termina en O(1), mientras que en el peor caso (elemento no encontrado o al final), la complejidad es O(n).\n", "\n", "#### <PERSON><PERSON><PERSON> de <PERSON>continue`\n", "\n", "La declaración `continue` salta la iteración actual y pasa directamente a la siguiente evaluación de la condición del bucle. A diferencia de `break`, no termina el bucle sino que simplemente omite el resto del código en la iteración actual.\n", "\n", "Ejemplo para imprimir números del 1 al 20, excluyendo los múltiplos de 3:\n", "\n", "```javascript\n", "console.log(\"Números del 1 al 20 (excluyendo múltiplos de 3):\");\n", "\n", "for (let i = 1; i <= 20; i++) {\n", "    if (i % 3 === 0) {\n", "        continue;  // Saltar esta iteración\n", "    }\n", "    console.log(i);\n", "}\n", "```\n", "\n", "El uso de `continue` en este caso evita tener que escribir un bloque `if` anidado innecesario, manteniendo el código más limpio y legible.\n", "\n", "#### Patrones de iteración frecuentes\n", "\n", "##### Acumulador\n", "\n", "El patrón acumulador es fundamental para sumar, multiplicar o concatenar valores:\n", "\n", "```javascript\n", "let suma = 0;\n", "for (let i = 1; i <= 10; i++) {\n", "    suma += i;\n", "}\n", "console.log(`Suma: ${suma}`);\n", "```\n", "\n", "##### B<PERSON><PERSON><PERSON> con parada temprana\n", "\n", "Utiliza `break` para optimizar b<PERSON>:\n", "\n", "```javascript\n", "let usuarios = [\"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\"];\n", "let buscado = \"María\";\n", "\n", "for (let usuario of usuarios) {\n", "    if (usuario === buscado) {\n", "        console.log(`Usuario ${buscado} encontrado`);\n", "        break;\n", "    }\n", "}\n", "```\n", "\n", "##### <PERSON><PERSON><PERSON> mediante `continue`\n", "\n", "Procesa solo elementos que cumplan ciertos criterios:\n", "\n", "```javascript\n", "for (let i = 0; i < 10; i++) {\n", "    if (i % 2 === 0) continue;  // Saltar números pares\n", "    console.log(`Número impar: ${i}`);\n", "}\n", "```\n", "\n", "##### Cuenta regresiva\n", "\n", "Utiliza decrementos para iterar hacia atrás:\n", "\n", "```javascript\n", "for (let i = 10; i >= 1; i--) {\n", "    console.log(`Cuenta regresiva: ${i}`);\n", "}\n", "console.log(\"¡Despegue!\");\n", "```\n", "\n", "#### Diagrama de flujo opcional\n", "\n", "<mermaid>\n", "flowchart TD\n", "    A[Inicio del bucle] --> B{Condición}\n", "    B -->|true| C[Ejecutar cuerpo]\n", "    B -->|false| F[Fin del bucle]\n", "    C --> D{¿break?}\n", "    D -->|sí| F\n", "    D -->|no| E{¿continue?}\n", "    E -->|sí| B\n", "    E -->|no| G[Continuar cuerpo]\n", "    G --> <PERSON>[Act<PERSON><PERSON><PERSON> contador]\n", "    H --> B\n", "</mermaid>\n", "\n", "*Figura 2. Flujo de control en bucles con break y continue. El diagrama ilustra cómo estas declaraciones modifican el flujo normal de iteración.*\n", "\n", "#### Buenas prácticas\n", "\n", "Al trabajar con `break` y `continue`, es importante seguir estas recomendaciones:\n", "\n", "- **No abusar de interrupciones múltiples**: <PERSON><PERSON><PERSON><PERSON> `break` y `continue` pueden hacer el código difícil de seguir\n", "- **Documentar bien los saltos**: Incluir comentarios que expliquen por qué se interrumpe o salta una iteración\n", "- **Evitar `break` en bucles anidados**: A menos que sea absolutamente necesario, ya que puede confundir el flujo de control\n", "- **Considerar refactorización**: Si un bucle tiene muchas interrupciones, podría ser mejor dividirlo en funciones más pequeñas\n", "\n", "Estos conceptos de control de flujo son esenciales para escribir bucles eficientes y legibles, preparando el terreno para abordar uno de los problemas más comunes en programación: los bucles infinitos.\n", "\n", "### Buenas prácticas para evitar bucles infinitos\n", "\n", "<fuentes>[10,12,13]</fuentes>\n", "\n", "Los bucles son herramientas poderosas, pero mal utilizados pueden convertirse en un problema serio que puede bloquear completamente una aplicación. Un bucle infinito ocurre cuando la condición de terminación nunca se cumple, causando que el programa se ejecute indefinidamente, consumiendo recursos del sistema y potencialmente bloqueando el navegador o el entorno de ejecución de Node.js. Comprender cómo prevenir y detectar estos bucles es crucial para desarrollar aplicaciones robustas y confiables.\n", "\n", "#### Diagnóstico de bucles infinitos\n", "\n", "Para identificar bucles infinitos, es esencial entender las causas más comunes que los provocan:\n", "\n", "**Eje<PERSON>lo problemá<PERSON>o con `for`:**\n", "```javascript\n", "// ERROR: Con<PERSON>or que nunca se actualiza correctamente\n", "for (let i = 0; i < 10; i--) {  // Decremento en lugar de incremento\n", "    console.log(\"Este bucle nunca terminará\");\n", "}\n", "```\n", "\n", "**<PERSON><PERSON><PERSON><PERSON> problemá<PERSON> con `while`:**\n", "```javascript\n", "// ERROR: Condición que nunca cambia\n", "let contador = 0;\n", "while (contador < 5) {\n", "    console.log(\"Iteración infinita\");\n", "    // Falta: contador++; - La variable nunca se actualiza\n", "}\n", "```\n", "\n", "**Ejemplo problemático con coerción inesperada:**\n", "```javascript\n", "// ERROR: Coerción de tipos causa comportamiento inesperado\n", "let valor = \"5\";\n", "while (valor != 0) {  // Debería ser !== para evitar coerción\n", "    valor = valor - 1;  // \"5\" - 1 = 4, \"4\" - 1 = 3, etc.\n", "    console.log(valor);\n", "    // Nunca llega a 0 como string\n", "}\n", "```\n", "\n", "#### Estrategias preventivas\n", "\n", "**Formular condiciones con límites superiores claros:**\n", "```javascript\n", "// Buena práctica: Condición clara y alcanzable\n", "for (let i = 0; i < 10; i++) {\n", "    console.log(`Iteración ${i}`);\n", "}\n", "```\n", "\n", "**Actualizar la variable de control adecuadamente:**\n", "```javascript\n", "// Buena práctica: Actualización explícita en while\n", "let contador = 0;\n", "while (contador < 5) {\n", "    console.log(`Contador: ${contador}`);\n", "    contador++;  // Actualización crucial\n", "}\n", "```\n", "\n", "**Emplear valores centinela o de corte:**\n", "```javascript\n", "// Buena práctica: Usar valores centinela\n", "let entrada = \"\";\n", "while (entrada !== \"salir\") {\n", "    entrada = prompt(\"Escribe 'salir' para terminar:\");\n", "    if (entrada && entrada !== \"salir\") {\n", "        console.log(`Procesando: ${entrada}`);\n", "    }\n", "}\n", "```\n", "\n", "**Incorporar contadores de seguridad:**\n", "```javascript\n", "// Buena práctica: Contador de seguridad\n", "let intentos = 0;\n", "const maxIntentos = 100;\n", "let procesando = true;\n", "\n", "while (procesando && intentos < maxIntentos) {\n", "    // Lógica del bucle\n", "    let resultado = realizarOperacion();\n", "    \n", "    if (resultado === \"completado\") {\n", "        procesando = false;\n", "    }\n", "    \n", "    intentos++;\n", "}\n", "\n", "if (intentos >= maxIntentos) {\n", "    console.log(\"Se alcanzó el límite máximo de intentos\");\n", "}\n", "```\n", "\n", "**Revisar coerciones implícitas y usar comparaciones estrictas:**\n", "```javascript\n", "// Buena práctica: Comparación estricta\n", "let valor = \"5\";\n", "while (valor !== \"0\") {  // Uso de !== en lugar de !=\n", "    valor = String(Number(valor) - 1);\n", "    console.log(valor);\n", "}\n", "```\n", "\n", "#### Herramientas de depuración\n", "\n", "**Uso de `console.log` para monitorear variables:**\n", "```javascript\n", "let i = 0;\n", "while (i < 10) {\n", "    console.log(`Valor actual de i: ${i}`);  // Monitorear progreso\n", "    \n", "    // Lógica del bucle\n", "    if (algunaCondicion) {\n", "        i++;\n", "    }\n", "    \n", "    // Detectar posibles problemas\n", "    if (i > 1000) {\n", "        console.error(\"Posible bucle infinito detectado\");\n", "        break;\n", "    }\n", "}\n", "```\n", "\n", "**Uso de breakpoints en DevTools:**\n", "- Colocar puntos de interrupción en líneas críticas del bucle\n", "- Inspeccionar valores de variables en cada iteración\n", "- Utilizar la funcionalidad \"Step Over\" para avanzar línea por línea\n", "\n", "**Atajos para Node.js:**\n", "- Usar `Ctrl+C` para detener un proceso bloqueado\n", "- Implementar timeouts para operaciones críticas\n", "- U<PERSON><PERSON>r her<PERSON> como `node --inspect` para depuración avanzada\n", "\n", "#### Ejemplo de refactorización\n", "\n", "Transformemos un `while` problemático en un `for` más seguro:\n", "\n", "**Versión problemática:**\n", "```javascript\n", "// <PERSON><PERSON><PERSON> propenso a errores\n", "let i = 0;\n", "while (i < 10) {\n", "    console.log(`Procesando item ${i}`);\n", "    // Si olvidamos incrementar i, bucle infinito\n", "    // i++;  // Esta línea podría olvidarse\n", "}\n", "```\n", "\n", "**Versión mejorada:**\n", "```javascript\n", "// Código más seguro y legible\n", "for (let i = 0; i < 10; i++) {\n", "    console.log(`Procesando item ${i}`);\n", "    // La actualización está garantizada en la estructura del for\n", "}\n", "```\n", "\n", "La versión con `for` es más segura porque la inicialización, condición y actualización están explícitamente definidas en la declaración del bucle, reduciendo la posibilidad de olvidar actualizar la variable de control. Además, la variable `i` tiene un ámbito limitado al bucle, evitando conflictos con otras variables.\n", "\n", "#### Conexión con temas futuros\n", "\n", "El dominio de estas técnicas para prevenir bucles infinitos es fundamental para escribir código robusto y eficiente. En el próximo bloque de la asignatura, cuando abordemos el tema de complejidad algorítmica, veremos cómo medir y analizar el coste computacional de nuestros bucles, y cómo las técnicas de control de flujo que hemos aprendido contribuyen a crear algoritmos más eficientes y verificables.\n"]}], "source": ["print(result.content[1][\"text\"])"]}, {"cell_type": "markdown", "id": "a3d21e29", "metadata": {}, "source": ["**Co<PERSON>s a tener en cuenta** con nuevo método. \n", "\n", "Output máximo, validar que no se pase de tokens de thinking + output. En el caso de encontrar esa stop reason, hacer que siga generando continuando el mensaje.\n", "\n", "Validar los epígrafes, hacer validación de epígrafes después, si no la pasa retry con el mismo modelo y pedirle que tiene que meter esos epígrafes."]}, {"cell_type": "code", "execution_count": null, "id": "b3b56648", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ia_gestorcontenidosiagen_be (3.12.9)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}