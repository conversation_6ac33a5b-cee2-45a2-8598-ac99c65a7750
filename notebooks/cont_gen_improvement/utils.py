from IPython.display import display, Markdown
from src.api.common.services.structs import Asignatura

def display_index_markdown(subject_index: Asignatura) -> str:
    """
    Convierte un objeto de índice de asignatura en formato Markdown y lo muestra en un notebook.
    
    Args:
        subject_index: Objeto GenerateIndexResponse con la estructura de la asignatura
        
    Returns:
        str: El contenido en formato Markdown
    """
    asignatura = subject_index.index
    md_content = []
    md_content.append(f"# {asignatura.nombre}\n")
    md_content.append("## Competencias\n")
    for i, comp in enumerate(subject_index.competencies, 1):
        md_content.append(f"{i}. {comp.descripcion}")
    md_content.append("")
    md_content.append("## Estructura Temática\n")
    for bloque_num, bloque in enumerate(asignatura.estructura.bloques_tematicos, 1):
        md_content.append(f"### Bloque {bloque_num}: {bloque.nombre}\n")
        for tema_num, tema in enumerate(bloque.temas, 1):
            md_content.append(f"#### {bloque_num}.{tema_num}. {tema.nombre}\n")
            for epigrafe_num, epigrafe in enumerate(tema.epigrafes, 1):
                md_content.append(f"##### {bloque_num}.{tema_num}.{epigrafe_num} {epigrafe}")
            md_content.append("")
    
    markdown_text = "\n".join(md_content)
    
    display(Markdown(markdown_text))
    return