{"cells": [{"cell_type": "markdown", "id": "6f061888", "metadata": {}, "source": ["Pydantic evals to collect traces. Maybe try to migrate there instead of phoenix? See the current phoenix.\n", "\n", "It gave me bugs I didn´t like.\n", "\n", "Estaría bien como refactor cargarnos langchain y usar router de modelos cuando vaya ok."]}, {"cell_type": "code", "execution_count": 1, "id": "9ef106c0", "metadata": {}, "outputs": [], "source": ["import sys\n", "from pathlib import Path\n", "from dotenv import load_dotenv\n", "load_dotenv()\n", "parent_dir = Path.cwd().parent.parent\n", "sys.path.append(str(parent_dir))"]}, {"cell_type": "markdown", "id": "7c5918d8", "metadata": {}, "source": ["We want to get instructions from subject to create the case.\n", "\n", "We want to go from the instructions, evaluate how they are generated and so on.\n", "\n", "First local then from the ones in develop.\n", "\n", "Try this to sync phoenix traces with pydantic evals\n", "https://arize.com/docs/phoenix/integrations/pydantic/pydantic-evals"]}, {"cell_type": "code", "execution_count": 2, "id": "bf1fa3c1", "metadata": {}, "outputs": [], "source": ["ORDER_ID = 4"]}, {"cell_type": "code", "execution_count": 3, "id": "173a2e7a", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-10 09:16:16,303 - logger - INFO - Initial user 'Unir demo' already exists.\n", "2025-07-10 09:16:16,309 - logger - INFO - Attempt 1: Phoenix endpoint not reachable. Error: HTTPConnectionPool(host='phoenix', port=6006): Max retries exceeded with url: /v1/traces (Caused by NameResolutionError(\"<urllib3.connection.HTTPConnection object at 0x14274b290>: Failed to resolve 'phoenix' ([Errno 8] nodename nor servname provided, or not known)\"))\n", "2025-07-10 09:16:21,321 - logger - INFO - Attempt 2: Phoenix endpoint not reachable. Error: HTTPConnectionPool(host='phoenix', port=6006): Max retries exceeded with url: /v1/traces (Caused by NameResolutionError(\"<urllib3.connection.HTTPConnection object at 0x14274b9e0>: Failed to resolve 'phoenix' ([Errno 8] nodename nor servname provided, or not known)\"))\n", "2025-07-10 09:16:26,330 - logger - INFO - Attempt 3: Phoenix endpoint not reachable. Error: HTTPConnectionPool(host='phoenix', port=6006): Max retries exceeded with url: /v1/traces (Caused by NameResolutionError(\"<urllib3.connection.HTTPConnection object at 0x14277c290>: Failed to resolve 'phoenix' ([Errno 8] nodename nor servname provided, or not known)\"))\n", "2025-07-10 09:16:31,344 - logger - INFO - Attempt 4: Phoenix endpoint not reachable. Error: HTTPConnectionPool(host='phoenix', port=6006): Max retries exceeded with url: /v1/traces (Caused by NameResolutionError(\"<urllib3.connection.HTTPConnection object at 0x14277cb90>: Failed to resolve 'phoenix' ([Errno 8] nodename nor servname provided, or not known)\"))\n", "2025-07-10 09:16:36,354 - logger - INFO - Attempt 5: Phoenix endpoint not reachable. Error: HTTPConnectionPool(host='phoenix', port=6006): Max retries exceeded with url: /v1/traces (Caused by NameResolutionError(\"<urllib3.connection.HTTPConnection object at 0x14277d460>: Failed to resolve 'phoenix' ([Errno 8] nodename nor servname provided, or not known)\"))\n", "2025-07-10 09:16:41,357 - logger - INFO - PHOENIX_COLLECTOR_ENDPOINT is not reachable after multiple attempts. Skipping instrumentation.\n", "2025-07-10 09:16:41,755 - logger - INFO - Created prompts successfully\n"]}], "source": ["from src.api.common.dependency_container import DependencyContainer\n", "from src.api.workflows.competencies import GenerateCompetenciesRequest\n", "DependencyContainer.initialize(observability = True)"]}, {"cell_type": "markdown", "id": "0b0313b4", "metadata": {}, "source": ["He generado una asignatura de Fundamentos de Programación con python.Order 3."]}, {"cell_type": "code", "execution_count": 4, "id": "bc78b9fa", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-30 15:36:41,841 - logger - INFO - Order and details are: order: 1, title_subject: 1, subject: 1\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "2025-06-30 15:36:54,847 - logger - INFO - Existing index is: id=39 status=<IndiceStatus.SEARCH: 'SEARCH'> is_displayed=False created_at=datetime.datetime(2025, 6, 23, 13, 8, 14, 681340) order_id=1 version=5 created_by='ia_gen_user' updated_by='ia_gen_user' updated_at=datetime.datetime(2025, 6, 25, 14, 24, 31, 901295)\n", "2025-06-30 15:36:54,899 - logger - INFO - Index number: 47 status changed to: IndiceStatus.NON_CONFIRMED_COMPETENCIES\n", "2025-06-30 15:36:54,900 - logger - INFO - Generated competencies are: [Compete<PERSON>ie(id=55, description='Diseñar y desarrollar scripts en Python que empleen tipos de datos y estructuras de control básicas para resolver problemas laborales concretos.'), Comp<PERSON><PERSON><PERSON>(id=56, description='Aplicar principios de lógica y algoritmia para descomponer problemas complejos y convertirlos en soluciones computacionales eficientes.'), Comp<PERSON><PERSON><PERSON>(id=57, description='Depurar y mantener código Python identificando errores y optimizando el rendimiento con herramientas estándar de la industria.'), Compete<PERSON>ie(id=58, description='Implementar programación orientada a objetos en Python para crear aplicaciones modulares, reutilizables y escalables.'), Competencie(id=59, description='Integrar bibliotecas estándar y de terceros en proyectos Python para automatizar tareas y procesar datos en entornos empresariales.'), Competencie(id=60, description='Emplear buenas prácticas de documentación y estilo de código (PEP 8) para facilitar la colaboración en equipos de desarrollo.'), Competencie(id=61, description='Utilizar sistemas de control de versiones como Git para gestionar el ciclo de vida de proyectos de software.'), Competencie(id=62, description='Realizar pruebas unitarias en Python para asegurar la calidad y fiabilidad del software entregado.')]\n"]}], "source": ["request = GenerateCompetenciesRequest(order_id = ORDER_ID)\n", "response = await DependencyContainer.get_generate_competencies_workflow().execute(request)"]}, {"cell_type": "code", "execution_count": 4, "id": "e633d79b", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'response' is not defined", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[4]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[33m\"\u001b[39m.join([\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mi\u001b[38;5;250m \u001b[39m+\u001b[38;5;250m \u001b[39m\u001b[32m1\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mc.description\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m i, c \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28menumerate\u001b[39m(\u001b[43mresponse\u001b[49m.competencies)]))\n", "\u001b[31mNameError\u001b[39m: name 'response' is not defined"]}], "source": ["print(\"\\n\".join([f\"{i + 1}: {c.description}\" for i, c in enumerate(response.competencies)]))"]}, {"cell_type": "markdown", "id": "79b01623", "metadata": {}, "source": ["En base a esto ahora generaremos el índice."]}, {"cell_type": "code", "execution_count": null, "id": "76ff5132", "metadata": {}, "outputs": [], "source": ["from IPython.display import display, Markdown\n", "from src.api.common.services.structs import Asignatura\n", "\n", "def display_index_markdown(subject_index: Asignatura) -> str:\n", "    \"\"\"\n", "    Convierte un objeto de índice de asignatura en formato Markdown y lo muestra en un notebook.\n", "    \n", "    Args:\n", "        subject_index: Objeto GenerateIndexResponse con la estructura de la asignatura\n", "        \n", "    Returns:\n", "        str: El contenido en formato Markdown\n", "    \"\"\"\n", "    asignatura = subject_index.index\n", "    md_content = []\n", "    md_content.append(f\"# {asignatura.nombre}\\n\")\n", "    md_content.append(\"## Competencias\\n\")\n", "    for i, comp in enumerate(subject_index.competencies, 1):\n", "        md_content.append(f\"{i}. {comp.descripcion}\")\n", "    md_content.append(\"\")\n", "    md_content.append(\"## Estructura Temática\\n\")\n", "    for bloque_num, bloque in enumerate(asignatura.estructura.bloques_tematicos, 1):\n", "        md_content.append(f\"### Bloque {bloque_num}: {bloque.nombre}\\n\")\n", "        for tema_num, tema in enumerate(bloque.temas, 1):\n", "            md_content.append(f\"#### {bloque_num}.{tema_num}. {tema.nombre}\\n\")\n", "            for epigrafe_num, epigrafe in enumerate(tema.epigrafes, 1):\n", "                md_content.append(f\"##### {bloque_num}.{tema_num}.{epigrafe_num} {epigrafe}\")\n", "            md_content.append(\"\")\n", "    \n", "    markdown_text = \"\\n\".join(md_content)\n", "    \n", "    display(Markdown(markdown_text))"]}, {"cell_type": "code", "execution_count": 7, "id": "72db4b89", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-30 15:37:10,026 - logger - INFO - Order and details are: order: 1, title_subject: 1, subject: 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-30 15:37:10,034 - logger - INFO - Latest index at check if with competencies is: id=47 status=<IndiceStatus.NON_CONFIRMED_COMPETENCIES: 'NON_CONFIRMED_COMPETENCIES'> is_displayed=False created_at=datetime.datetime(2025, 6, 30, 15, 36, 54, 848318) order_id=1 version=6 created_by='ia_gen_user' updated_by='ia_gen_user' updated_at=datetime.datetime(2025, 6, 30, 13, 36, 54, 894077)\n", "2025-06-30 15:37:10,035 - logger - INFO - Latest index competencies are: [CompetenciesIndice(competencie_id=55, created_by='ia_gen_user', indice_id=47, updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 15, 36, 54, 868021), created_at=datetime.datetime(2025, 6, 30, 15, 36, 54, 867881)), CompetenciesIndice(competencie_id=56, created_by='ia_gen_user', indice_id=47, updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 15, 36, 54, 873784), created_at=datetime.datetime(2025, 6, 30, 15, 36, 54, 873708)), CompetenciesIndice(competencie_id=57, created_by='ia_gen_user', indice_id=47, updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 15, 36, 54, 876541), created_at=datetime.datetime(2025, 6, 30, 15, 36, 54, 876030)), CompetenciesIndice(competencie_id=58, created_by='ia_gen_user', indice_id=47, updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 15, 36, 54, 880423), created_at=datetime.datetime(2025, 6, 30, 15, 36, 54, 880357)), CompetenciesIndice(competencie_id=59, created_by='ia_gen_user', indice_id=47, updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 15, 36, 54, 882084), created_at=datetime.datetime(2025, 6, 30, 15, 36, 54, 882031)), CompetenciesIndice(competencie_id=60, created_by='ia_gen_user', indice_id=47, updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 15, 36, 54, 883957), created_at=datetime.datetime(2025, 6, 30, 15, 36, 54, 883871)), CompetenciesIndice(competencie_id=61, created_by='ia_gen_user', indice_id=47, updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 15, 36, 54, 886213), created_at=datetime.datetime(2025, 6, 30, 15, 36, 54, 886141)), CompetenciesIndice(competencie_id=62, created_by='ia_gen_user', indice_id=47, updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 15, 36, 54, 889007), created_at=datetime.datetime(2025, 6, 30, 15, 36, 54, 888942))]\n", "2025-06-30 15:37:10,043 - logger - INFO - Competences are competencias=[Competencia(descripcion='Diseñar y desarrollar scripts en Python que empleen tipos de datos y estructuras de control básicas para resolver problemas laborales concretos.'), Competencia(descripcion='Aplicar principios de lógica y algoritmia para descomponer problemas complejos y convertirlos en soluciones computacionales eficientes.'), Competencia(descripcion='Depurar y mantener código Python identificando errores y optimizando el rendimiento con herramientas estándar de la industria.'), Competencia(descripcion='Implementar programación orientada a objetos en Python para crear aplicaciones modulares, reutilizables y escalables.'), Competencia(descripcion='Integrar bibliotecas estándar y de terceros en proyectos Python para automatizar tareas y procesar datos en entornos empresariales.'), Competencia(descripcion='Emplear buenas prácticas de documentación y estilo de código (PEP 8) para facilitar la colaboración en equipos de desarrollo.'), Competencia(descripcion='Utilizar sistemas de control de versiones como Git para gestionar el ciclo de vida de proyectos de software.'), Competencia(descripcion='Realizar pruebas unitarias en Python para asegurar la calidad y fiabilidad del software entregado.')]\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "2025-06-30 15:37:34,785 - logger - INFO - Schema has been generated\n", "2025-06-30 15:37:34,859 - logger - INFO - Index number: 47 status changed to: IndiceStatus.INDICE_GENERATION\n"]}], "source": ["from src.api.workflows.indexes import GenerateIndexRequest\n", "request = GenerateIndexRequest(order_id = ORDER_ID, descripcion=\"En el primer tema es importante que se haga una introducción a la asignatura. Ya que será la primera asignatura de programación que los alumnos den. Luego ya se puede ahondar mejor en conceptos\")\n", "response = await DependencyContainer.get_generate_index_workflow().execute(request)"]}, {"cell_type": "code", "execution_count": 4, "id": "dc178830", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-10 09:16:47,671 - logger - INFO - Index is status=<IndiceStatus.CONTENT_GENERATION: 'CONTENT_GENERATION'> is_displayed=False created_at=datetime.datetime(2025, 6, 30, 13, 39, 58, 429390) id=48 order_id=4 version=1 created_by='ia_gen_user' updated_by='ia_gen_user' updated_at=datetime.datetime(2025, 7, 2, 9, 6, 25, 403435)\n", "2025-07-10 09:16:47,671 - logger - INFO - Entered in async session\n", "2025-07-10 09:16:47,679 - logger - INFO - Indice inside get subject index is, status=<IndiceStatus.CONTENT_GENERATION: 'CONTENT_GENERATION'> is_displayed=False created_at=datetime.datetime(2025, 6, 30, 13, 39, 58, 429390) id=48 order_id=4 version=1 created_by='ia_gen_user' updated_by='ia_gen_user' updated_at=datetime.datetime(2025, 7, 2, 9, 6, 25, 403435)\n", "2025-07-10 09:16:47,692 - logger - INFO - <PERSON><PERSON>ques inside get subject index is, [<PERSON><PERSON><PERSON>(name='Fundamentos de Programación y Estructuras de Datos en JavaScript', indice_id=48, created_by='ia_gen_user', updated_by=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 34060), id=63, position=1, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 34220))]\n", "2025-07-10 09:16:47,692 - logger - INFO - Asignatura is, nombre='Introducción a la programación, algoritmos y estructuras de datos con javascript - Informatica' estructura=Estructura(bloques_tematicos=[BloqueTematico(nombre='Fundamentos de Programación y Estructuras de Datos en JavaScript', temas=[Tema(nombre='Introducción al pensamiento algorítmico y al entorno JavaScript', epigrafes=['Conceptos de algoritmo y pseudocódigo', 'Historia y evolución de JavaScript', 'Instalación del entorno de desarrollo (Node.js, VS Code)', 'Primer script: \"Hola, mundo\" en consola y navegador']), <PERSON><PERSON>(nombre='Variables, tipos de datos y operadores en JavaScript', epigrafes=['Declaración de variables (var, let, const)', 'Tipos primitivos y coerción de datos', 'Operadores aritméticos, lógicos y comparativos', 'Entrada y salida básica con prompt, alert y console.log']), <PERSON><PERSON>(nombre='Control de flujo y estructuras condicionales', epigrafes=['Estructuras de decisión if, else y switch', 'Bucles for, while y do-while', 'Control de bucle (break, continue) y patrones de iteración', 'Buenas prácticas para evitar bucles infinitos']), Tema(nombre='Funciones y modularidad', epigrafes=['Declaración y expresión de funciones', 'Parámetros, retorno y funciones flecha', 'Closures y alcance léxico', 'Módulos ES6: import y export']), Tema(nombre='Complejidad algorítmica y análisis Big-O', epigrafes=['Concepto de eficiencia temporal y espacial', 'Reglas para determinar la notación Big-O', 'Casos mejor, promedio y peor', 'Herramientas de benchmarking en JavaScript']), Tema(nombre='Arrays y métodos funcionales', epigrafes=['Creación y mutación de arreglos', 'Métodos iterativos forEach, map, filter, reduce', 'Pilas y colas implementadas con arrays', 'Declaración y recorrido de matrices multidimensionales']), Tema(nombre='Listas enlazadas y árboles binarios', epigrafes=['Implementación de nodos y punteros en JS', 'Lista enlazada simple: inserción y eliminación', 'Estructura de árbol binario y tipos de recorrido', 'Análisis de complejidad de listas y árboles']), Tema(nombre='Tablas hash y conjuntos', epigrafes=['Fundamentos de hashing y funciones hash', 'Resolución de colisiones (encadenamiento y open addressing)', 'Implementación de Map, Set, WeakMap y WeakSet', 'Aplicaciones y análisis de rendimiento']), Tema(nombre='Recursión, búsqueda y ordenación', epigrafes=['Principios de recursión y diseño de casos base', 'Búsqueda lineal y búsqueda binaria', 'Algoritmos de ordenación sencillos (bubble, selection, insertion)', 'QuickSort y MergeSort: enfoque divide-and-conquer']), Tema(nombre='Calidad de software, pruebas y control de versiones', epigrafes=['Depuración con DevTools y Node Inspector', 'Pruebas unitarias y de integración con Jest', 'Manejo de errores y excepciones en JavaScript', 'Uso de Git y GitHub en flujo colaborativo'])])])\n", "2025-07-10 09:16:47,692 - logger - INFO - Asignatura is: nombre='Introducción a la programación, algoritmos y estructuras de datos con javascript - Informatica' estructura=Estructura(bloques_tematicos=[BloqueTematico(nombre='Fundamentos de Programación y Estructuras de Datos en JavaScript', temas=[Tema(nombre='Introducción al pensamiento algorítmico y al entorno JavaScript', epigrafes=['Conceptos de algoritmo y pseudocódigo', 'Historia y evolución de JavaScript', 'Instalación del entorno de desarrollo (Node.js, VS Code)', 'Primer script: \"Hola, mundo\" en consola y navegador']), <PERSON><PERSON>(nombre='Variables, tipos de datos y operadores en JavaScript', epigrafes=['Declaración de variables (var, let, const)', 'Tipos primitivos y coerción de datos', 'Operadores aritméticos, lógicos y comparativos', 'Entrada y salida básica con prompt, alert y console.log']), <PERSON><PERSON>(nombre='Control de flujo y estructuras condicionales', epigrafes=['Estructuras de decisión if, else y switch', 'Bucles for, while y do-while', 'Control de bucle (break, continue) y patrones de iteración', 'Buenas prácticas para evitar bucles infinitos']), Tema(nombre='Funciones y modularidad', epigrafes=['Declaración y expresión de funciones', 'Parámetros, retorno y funciones flecha', 'Closures y alcance léxico', 'Módulos ES6: import y export']), Tema(nombre='Complejidad algorítmica y análisis Big-O', epigrafes=['Concepto de eficiencia temporal y espacial', 'Reglas para determinar la notación Big-O', 'Casos mejor, promedio y peor', 'Herramientas de benchmarking en JavaScript']), Tema(nombre='Arrays y métodos funcionales', epigrafes=['Creación y mutación de arreglos', 'Métodos iterativos forEach, map, filter, reduce', 'Pilas y colas implementadas con arrays', 'Declaración y recorrido de matrices multidimensionales']), Tema(nombre='Listas enlazadas y árboles binarios', epigrafes=['Implementación de nodos y punteros en JS', 'Lista enlazada simple: inserción y eliminación', 'Estructura de árbol binario y tipos de recorrido', 'Análisis de complejidad de listas y árboles']), Tema(nombre='Tablas hash y conjuntos', epigrafes=['Fundamentos de hashing y funciones hash', 'Resolución de colisiones (encadenamiento y open addressing)', 'Implementación de Map, Set, WeakMap y WeakSet', 'Aplicaciones y análisis de rendimiento']), Tema(nombre='Recursión, búsqueda y ordenación', epigrafes=['Principios de recursión y diseño de casos base', 'Búsqueda lineal y búsqueda binaria', 'Algoritmos de ordenación sencillos (bubble, selection, insertion)', 'QuickSort y MergeSort: enfoque divide-and-conquer']), Tema(nombre='Calidad de software, pruebas y control de versiones', epigrafes=['Depuración con DevTools y Node Inspector', 'Pruebas unitarias y de integración con Jest', 'Manejo de errores y excepciones en JavaScript', 'Uso de Git y GitHub en flujo colaborativo'])])])\n", "2025-07-10 09:16:47,696 - logger - INFO - Competencies are: competencias=[Competencia(descripcion='Analizar un problema y traducirlo en una solución algorítmica estructurada y eficiente.'), Competencia(descripcion='Desarrollar programas en JavaScript aplicando buenas prácticas de sintaxis y estilo de código.'), Competencia(descripcion='Implementar y manipular estructuras de datos fundamentales como arreglos, listas enlazadas, pilas, colas, árboles y tablas hash.'), Competencia(descripcion='Evaluar la complejidad temporal y espacial de algoritmos para seleccionar la solución más adecuada en un contexto productivo.'), Competencia(descripcion='Utilizar técnicas de depuración, pruebas unitarias y manejo de errores para garantizar la calidad del software.'), Competencia(descripcion='Aplicar principios de diseño modular y reutilizable mediante funciones, módulos y patrones básicos de programación.'), Competencia(descripcion='Integrar herramientas de control de versiones, como Git, en el flujo de desarrollo para colaborar de forma efectiva en proyectos de software.'), Competencia(descripcion='Documentar el código y comunicar los resultados técnicos a audiencias tanto técnicas como no técnicas en un entorno laboral.')]\n"]}], "source": ["from src.api.workflows.indexes import GetIndexRequest\n", "request = GetIndexRequest(id = 48)\n", "index_response = await DependencyContainer.get_index_workflow().execute(request)"]}, {"cell_type": "markdown", "id": "4450b215", "metadata": {}, "source": ["Indice propuesto por o3 en base a esas competencias"]}, {"cell_type": "code", "execution_count": 5, "id": "3b458ddf", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'display_index_markdown' is not defined", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[5]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[43mdisplay_index_markdown\u001b[49m(index_response)\n", "\u001b[31mNameError\u001b[39m: name 'display_index_markdown' is not defined"]}], "source": ["display_index_markdown(index_response)"]}, {"cell_type": "markdown", "id": "f91bce31", "metadata": {}, "source": ["Generate the didactic instructions"]}, {"cell_type": "markdown", "id": "bff686b8", "metadata": {}, "source": ["En realidad para cada cosa se puede hacer evals. Tomar un poco pautas de competencias del asistente de generar competencias, esto sería bueno con gemini flash."]}, {"cell_type": "code", "execution_count": 9, "id": "d798ab4f", "metadata": {}, "outputs": [], "source": ["from src.api.common.services.structs import ModelInfo"]}, {"cell_type": "markdown", "id": "d05bfa19", "metadata": {}, "source": ["Store this at the end in pickle or so. Use logfire for various evals. \n", "\n", "Probar opus"]}, {"cell_type": "markdown", "id": "819562cd", "metadata": {}, "source": ["## Instrucciones didácticas"]}, {"cell_type": "markdown", "id": "66792983", "metadata": {}, "source": ["Optimizar y evaluar instrucciones didácticas.\n", "En base a esto ver cómo se podría mejorar el prompt.\n", "La primera evaluación es en base sólo a el modelo, una vez identificado el que funciona mejor podría optimizar también más el prompt para que de lugar a un mejor contenido después"]}, {"cell_type": "code", "execution_count": 10, "id": "23813957", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-30 16:34:47,768 - logger - INFO - Received GenerateAllDidacticInstructionsRequest: order_id=4 didactic_instructions='\\nLa estructura general de un epígrafe ha de ser:\\n\\nComenzará por una introducción breve, seguida de la elaboración de los conceptos clave sobre los cuales trata el epígrafe en una forma narrativa.\\nTratando de introducirlos de manera progresiva y cohesiva, evitando una excesiva segmentación de conceptos y conectando el contexto de los epígrafes anteriores a lo largo de la narración.\\n\\nEn cuanto a el orden de estos dentro de un tema:\\n\\nConsiderarás los otros epígrafes del tema para lo que será tratado en cada uno individualmente, evitando solapamientos.\\n\\nIntroducirás ejemplos y ejercicios prácticos cuando sea necesario a nivel global. Por tema introducirás entre 1 y 3 en alguno de los epígrafes. Considerás cuando es adecuado para evitar una repetición excesiva y ser moderado.\\n\\nConsiderarás una longitud aproximada de unas 2-5 páginas por epígrafe, introduciendo las instrucciones adecuadas para que no sea ni mucho más ni mucho menos.\\n\\nCombinarás cuando sea necesario elementos adicionales como tablas o gráficas, pero considerarás no abusar de ellas, teniendo en cuenta también incluirlas cuando sea adecuado dentro del contexto de todos los temas.\\n' plan_version=1 allow_increase_version=False model_info=ModelInfo(name='o3', provider='openai', reasoning_effort='high', max_tokens=30000, temperature=0)\n", "2025-06-30 16:34:47,769 - logger - INFO - Using custom model with provider: openai and model name: o3\n", "2025-06-30 16:34:47,779 - logger - INFO - Retrieved 10 Temas for Indice ID 48.\n", "2025-06-30 16:34:47,941 - logger - INFO - Index number: 48 status changed to: IndiceStatus.INSTRUCTIONS_GENERATION\n", "2025-06-30 16:34:47,943 - logger - INFO - Entered in async session\n", "2025-06-30 16:34:47,947 - logger - INFO - Indice inside get subject index is, status=<IndiceStatus.INSTRUCTIONS_GENERATION: 'INSTRUCTIONS_GENERATION'> is_displayed=False created_at=datetime.datetime(2025, 6, 30, 13, 39, 58, 429390) id=48 order_id=4 version=1 created_by='ia_gen_user' updated_by='ia_gen_user' updated_at=datetime.datetime(2025, 6, 30, 13, 42, 4, 476992)\n", "2025-06-30 16:34:47,951 - logger - INFO - <PERSON><PERSON><PERSON> inside get subject index is, [<PERSON><PERSON><PERSON>(name='Fundamentos de Programación y Estructuras de Datos en JavaScript', indice_id=48, created_by='ia_gen_user', updated_by=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 34060), id=63, position=1, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 34220))]\n", "2025-06-30 16:34:47,952 - logger - INFO - Asignatura is, nombre='Introducción a la programación, algoritmos y estructuras de datos con javascript - Informatica' estructura=Estructura(bloques_tematicos=[BloqueTematico(nombre='Fundamentos de Programación y Estructuras de Datos en JavaScript', temas=[Tema(nombre='Introducción al pensamiento algorítmico y al entorno JavaScript', epigrafes=['Conceptos de algoritmo y pseudocódigo', 'Historia y evolución de JavaScript', 'Instalación del entorno de desarrollo (Node.js, VS Code)', 'Primer script: \"Hola, mundo\" en consola y navegador']), <PERSON><PERSON>(nombre='Variables, tipos de datos y operadores en JavaScript', epigrafes=['Declaración de variables (var, let, const)', 'Tipos primitivos y coerción de datos', 'Operadores aritméticos, lógicos y comparativos', 'Entrada y salida básica con prompt, alert y console.log']), <PERSON><PERSON>(nombre='Control de flujo y estructuras condicionales', epigrafes=['Estructuras de decisión if, else y switch', 'Bucles for, while y do-while', 'Control de bucle (break, continue) y patrones de iteración', 'Buenas prácticas para evitar bucles infinitos']), Tema(nombre='Funciones y modularidad', epigrafes=['Declaración y expresión de funciones', 'Parámetros, retorno y funciones flecha', 'Closures y alcance léxico', 'Módulos ES6: import y export']), Tema(nombre='Complejidad algorítmica y análisis Big-O', epigrafes=['Concepto de eficiencia temporal y espacial', 'Reglas para determinar la notación Big-O', 'Casos mejor, promedio y peor', 'Herramientas de benchmarking en JavaScript']), Tema(nombre='Arrays y métodos funcionales', epigrafes=['Creación y mutación de arreglos', 'Métodos iterativos forEach, map, filter, reduce', 'Pilas y colas implementadas con arrays', 'Declaración y recorrido de matrices multidimensionales']), Tema(nombre='Listas enlazadas y árboles binarios', epigrafes=['Implementación de nodos y punteros en JS', 'Lista enlazada simple: inserción y eliminación', 'Estructura de árbol binario y tipos de recorrido', 'Análisis de complejidad de listas y árboles']), Tema(nombre='Tablas hash y conjuntos', epigrafes=['Fundamentos de hashing y funciones hash', 'Resolución de colisiones (encadenamiento y open addressing)', 'Implementación de Map, Set, WeakMap y WeakSet', 'Aplicaciones y análisis de rendimiento']), Tema(nombre='Recursión, búsqueda y ordenación', epigrafes=['Principios de recursión y diseño de casos base', 'Búsqueda lineal y búsqueda binaria', 'Algoritmos de ordenación sencillos (bubble, selection, insertion)', 'QuickSort y MergeSort: enfoque divide-and-conquer']), Tema(nombre='Calidad de software, pruebas y control de versiones', epigrafes=['Depuración con DevTools y Node Inspector', 'Pruebas unitarias y de integración con Jest', 'Manejo de errores y excepciones en JavaScript', 'Uso de Git y GitHub en flujo colaborativo'])])])\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "2025-06-30 16:37:02,558 - logger - INFO - Index id is: 48\n", "2025-06-30 16:37:02,570 - logger - INFO - Epigraphs by topic are: defaultdict(<class 'list'>, {123: [Epigra<PERSON>(name='Conceptos de algoritmo y pseudocódigo', id_tema=123, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 42076), id=390, position=1, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 42167)), Epigrafe(name='Historia y evolución de JavaScript', id_tema=123, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 44806), id=391, position=2, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 44892)), Epigra<PERSON>(name='Instalación del entorno de desarrollo (Node.js, VS Code)', id_tema=123, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 45621), id=392, position=3, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 45821)), Epigrafe(name='Primer script: \"Hola, mundo\" en consola y navegador', id_tema=123, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 48597), id=393, position=4, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 48687))], 124: [Epigrafe(name='Declaración de variables (var, let, const)', id_tema=124, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 49898), id=394, position=1, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 49968)), Epigrafe(name='Tipos primitivos y coerción de datos', id_tema=124, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 50425), id=395, position=2, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 50490)), Epigrafe(name='Operadores aritméticos, lógicos y comparativos', id_tema=124, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 51000), id=396, position=3, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 51156)), Epigrafe(name='Entrada y salida básica con prompt, alert y console.log', id_tema=124, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 51727), id=397, position=4, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 51956))], 125: [Epigrafe(name='Estructuras de decisión if, else y switch', id_tema=125, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 53178), id=398, position=1, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 53338)), Epigrafe(name='Bucles for, while y do-while', id_tema=125, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 53762), id=399, position=2, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 53902)), Epigrafe(name='Control de bucle (break, continue) y patrones de iteración', id_tema=125, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 54325), id=400, position=3, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 54458)), Epigrafe(name='Buenas prácticas para evitar bucles infinitos', id_tema=125, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 54897), id=401, position=4, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 55060))], 126: [Epigrafe(name='Declaración y expresión de funciones', id_tema=126, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 56665), id=402, position=1, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 56830)), Epigrafe(name='Parámetros, retorno y funciones flecha', id_tema=126, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 57361), id=403, position=2, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 57476)), Epigrafe(name='Closures y alcance léxico', id_tema=126, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 59125), id=404, position=3, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 59190)), Epigrafe(name='Módulos ES6: import y export', id_tema=126, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 59574), id=405, position=4, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 59637))], 127: [Epigrafe(name='Concepto de eficiencia temporal y espacial', id_tema=127, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 60547), id=406, position=1, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 60632)), Epigrafe(name='Reglas para determinar la notación Big-O', id_tema=127, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 61046), id=407, position=2, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 61131)), Epigrafe(name='Casos mejor, promedio y peor', id_tema=127, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 61541), id=408, position=3, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 61628)), Epigrafe(name='Herramientas de benchmarking en JavaScript', id_tema=127, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 62037), id=409, position=4, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 62123))], 128: [Epigrafe(name='Creación y mutación de arreglos', id_tema=128, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 62995), id=410, position=1, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 63078)), Epigrafe(name='Métodos iterativos forEach, map, filter, reduce', id_tema=128, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 63453), id=411, position=2, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 63547)), Epigrafe(name='Pilas y colas implementadas con arrays', id_tema=128, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 64199), id=412, position=3, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 64296)), Epigrafe(name='Declaración y recorrido de matrices multidimensionales', id_tema=128, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 64777), id=413, position=4, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 64874))], 129: [Epigrafe(name='Implementación de nodos y punteros en JS', id_tema=129, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 65809), id=414, position=1, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 65901)), Epigrafe(name='Lista enlazada simple: inserción y eliminación', id_tema=129, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 66365), id=415, position=2, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 66494)), Epigrafe(name='Estructura de árbol binario y tipos de recorrido', id_tema=129, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 66896), id=416, position=3, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 66987)), Epigrafe(name='Análisis de complejidad de listas y árboles', id_tema=129, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 68646), id=417, position=4, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 68724))], 130: [Epigrafe(name='Fundamentos de hashing y funciones hash', id_tema=130, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 70053), id=418, position=1, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 70136)), Epigrafe(name='Resolución de colisiones (encadenamiento y open addressing)', id_tema=130, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 70527), id=419, position=2, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 70611)), Epigrafe(name='Implementación de Map, Set, WeakMap y WeakSet', id_tema=130, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 71006), id=420, position=3, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 71090)), Epigrafe(name='Aplicaciones y análisis de rendimiento', id_tema=130, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 71483), id=421, position=4, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 71569))], 131: [Epigrafe(name='Principios de recursión y diseño de casos base', id_tema=131, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 72643), id=422, position=1, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 72749)), Epigrafe(name='Búsqueda lineal y búsqueda binaria', id_tema=131, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 73113), id=423, position=2, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 73202)), Epigrafe(name='Algoritmos de ordenación sencillos (bubble, selection, insertion)', id_tema=131, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 73569), id=424, position=3, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 73658)), Epigrafe(name='QuickSort y MergeSort: enfoque divide-and-conquer', id_tema=131, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 74043), id=425, position=4, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 74129))], 132: [Epigrafe(name='Depuración con DevTools y Node Inspector', id_tema=132, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 74936), id=426, position=1, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 75021)), Epigrafe(name='Pruebas unitarias y de integración con Jest', id_tema=132, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 75377), id=427, position=2, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 75465)), Epigrafe(name='Manejo de errores y excepciones en JavaScript', id_tema=132, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 75836), id=428, position=3, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 75927)), Epigrafe(name='Uso de Git y GitHub en flujo colaborativo', id_tema=132, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 76282), id=429, position=4, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 76381))]})\n", "2025-06-30 16:37:02,572 - logger - INFO - Ordered instr are : ['**Extensión aproximada:** 900-1100 palabras  \\n\\nEl texto comenzará con una introducción de uno o dos párrafos que relacione una situación cotidiana (por ejemplo, seguir una receta de cocina) con la idea de resolver problemas mediante instrucciones paso a paso. El objetivo es que el lector intuya que programar es formalizar esos pasos para que los ejecute una máquina.  \\n\\n### Desarrollo conceptual (≈60 %)  \\n* Definir claramente qué es un procedimiento y qué requisitos convierten a un procedimiento en algoritmo: finitud, no ambigüedad, efectividad, entradas definidas y salidas esperadas.  \\n* Explicar la estructura Entrada-Proceso-Salida y la importancia de diseñar la solución antes de elegir un lenguaje concreto.  \\n* Introducir la representación en pseudocódigo: propósito, nivel de abstracción y ventajas frente al lenguaje natural o a la sintaxis específica de un lenguaje.  \\n* Describir las convenciones sintácticas más extendidas: palabras clave en mayúsculas, indentación, comentarios y delimitación de bloques.  \\n* Diferenciar entre declaración de variables en pseudocódigo y en un lenguaje real, enfatizando que aquí sólo interesa la claridad lógica.  \\n\\n### Ejemplificación (≈25 %)  \\n1. **Ejemplo cotidiano**: convertir la descripción \"preparar un sándwich\" en pseudocódigo, numerando los pasos y resaltando palabras clave (SI, ENTONCES, FIN-SI).  \\n2. **Ejemplo numérico breve**: calcular el área de un rectángulo mostrando explícitamente entradas (base, altura) y salida (área).  \\n\\n### Elementos dinamizadores  \\n* **Tabla** con tres columnas (Característica | Descripción | Importancia) que resuma finito, definido, correcto, eficiente y verificable.  \\n* **Tabla/ficha** de referencia rápida con palabras clave de pseudocódigo y su significado funcional.  \\n\\n### Conexión con la sección siguiente  \\nCerrar recordando que los algoritmos son independientes del lenguaje, pero que uno de los más influyentes hoy en día es JavaScript, cuya evolución histórica se presentará a continuación. Evitar conclusiones definitivas; reservar la validación y ejecución real para más adelante.', '**Extensión aproximada:** 1000-1200 palabras  \\n\\n### Introducción (1-2 párrafos)  \\nRetomar la idea de que los algoritmos requieren un vehículo para ser ejecutados y presentar JavaScript como el lenguaje que dará vida a las soluciones diseñadas. Plantear una pregunta motivadora: ¿cómo pasó de ser un lenguaje de 10 días de desarrollo a uno de los más influyentes del mundo?  \\n\\n### Narrativa histórica  \\n* Origen en 1995: contexto de la guerra de navegadores; objetivo inicial (scripts en páginas web) y creación en tiempo récord.  \\n* Evolución a través de los estándares ECMAScript: explicar cómo la estandarización evita la fragmentación.  \\n* Hitos relevantes:  \\n  * ES3 (1999) – primera especificación ampliamente adoptada.  \\n  * ES5 (2009) – modo estricto y mejoras de seguridad.  \\n  * ES6/ES2015 – salto cualitativo: `let`, `const`, arrow functions, módulos.  \\n  * Llegada de *Node.js* (2009) – JavaScript en el servidor.  \\n  * Evolución anual de especificaciones hasta ES2023: promesas, async/await, optional chaining, *top-level await*, etc.  \\n* Explicar brevemente el ecosistema: NPM, comunidad, frameworks (React, Vue, Angular) sin profundizar en ellos (se mencionan para dar contexto).  \\n\\n### Recursos visuales  \\n* **Cronograma mermaid** con los hitos más importantes (máximo 8-10 eventos). El redactor podrá simplificar si lo desea.  \\n* **Tabla** de dos columnas que relacione cada versión importante de ECMAScript con 2-3 características clave.  \\n\\n### Conexión con la práctica  \\nResaltar cómo cada avance ha acercado el lenguaje a la resolución de problemas más complejos con menos código. Terminar apuntando que, para empezar a escribir código, primero se necesita un entorno adecuado, tema que se abordará a continuación.  \\n\\n### Consideraciones de estilo  \\nEvitar enumerar versiones de forma exhaustiva; centrarse en hitos significativos y en por qué fueron decisivos. Mantener un tono narrativo, no una lista de fechas. Conectar cada hito con la ampliación de posibilidades para el programador principiante.  \\n\\nNo incluir todavía instrucciones de instalación ni fragmentos de código; eso corresponde al siguiente epígrafe.', \"**Extensión aproximada:** 850-1000 palabras  \\n\\n### Apertura  \\nIntroducir la idea de que un algoritmo sólo se materializa cuando existe un intérprete o motor que lo ejecute. Presentar Node.js como el motor JavaScript fuera del navegador y VS Code como editor moderno, gratuito y multiplataforma.  \\n\\n### Paso a paso (núcleo del texto)  \\n1. **Descarga de Node.js**  \\n   * Elegir la *LTS* recomendada.  \\n   * Clarificar diferencias entre instaladores de Windows, macOS y gestores de paquetes en Linux.  \\n   * Comando de verificación: `node -v` y `npm -v`.  \\n2. **Instalación de VS Code**  \\n   * Enlace oficial.  \\n   * Opciones de instalación por SO.  \\n   * Extensiones recomendadas: *ESLint*, *Prettier* y *JavaScript & TypeScript Nightly* (mencionar brevemente su función sin profundizar en configuración).  \\n3. **Configuración mínima del entorno**  \\n   * Crear carpeta de proyectos.  \\n   * Abrir terminal integrada.  \\n   * Comando `npm init -y` para generar un proyecto básico y explicar para qué servirá en el futuro.  \\n4. **Prueba rápida**  \\n   * Crear `test.js` con `console.log('Instalación correcta');`.  \\n   * Ejecutar `node test.js`.  \\n   * Interpretar el resultado en terminal.  \\n\\n### Recursos de apoyo  \\n* **Tabla** comparativa de gestores de paquetes (Winget, Homebrew, apt) con comando de instalación de Node.js para cada sistema.  \\n* **Diagrama mermaid** sencillo que muestre el flujo «Código fuente → Motor Node.js → Consola» para subrayar el camino de ejecución.  \\n\\n### Buenas prácticas iniciales  \\n* Mantener actualizadas las versiones LTS.  \\n* Uso de carpetas dedicadas por proyecto.  \\n* Primer contacto con el sistema de módulos comunes (`require` vs ES Modules) se mencionará, pero se profundizará en temas posteriores.  \\n\\n### Cierre  \\nEnlazar con la siguiente sección explicando que la primera verificación real del entorno será crear un archivo que imprima *Hola, mundo* tanto en consola como en el navegador, ejercicio que inaugura la escritura efectiva de código. Evitar aquí cualquier código de navegador; reservarlo para el próximo apartado.\", '**Extensión aproximada:** 800-900 palabras  \\n\\n### Introducción  \\nRecordar que el entorno está listo y que es momento de transformar un algoritmo mínimo en código ejecutable. Presentar el mensaje *Hola, mundo* como un ritual de iniciación que verifica tanto la instalación como la comprensión de la ruta de ejecución.  \\n\\n### Ejecución en la consola (Node.js)  \\n1. Crear archivo `hola.js` en la carpeta del proyecto.  \\n2. Escribir `console.log(\\'Hola, mundo\\');`.  \\n3. Ejecutar con `node hola.js` y explicar brevemente qué hace el comando.  \\n4. Mencionar la convención de usar comillas simples/dobles y el punto-y-coma opcional según estilo.  \\n5. Proponer modificación: usar *template literals* con back-ticks e interpolar el día de la semana (`console.log(\\x7f`Hola, hoy es ${dia}`);`).  \\n\\n### Ejecución en el navegador  \\n1. Crear archivo `index.html` mínimo con etiqueta `<script src=\"hola.js\"></script>`.  \\n2. Abrir en el navegador y utilizar las Herramientas de Desarrollador → pestaña *Console* para ver el mensaje.  \\n3. Explicar brevemente que el script se ejecuta en el contexto del motor *V8* y que `console.log` envía la salida al panel de consola.  \\n4. Advertir diferencias clave entre entorno de servidor y navegador (APIs disponibles, objeto `window`, etc.) sin profundizar aún.  \\n\\n### Elementos dinamizadores  \\n* **Tabla** con dos columnas que contraste \"Ejecución en Node\" vs \"Ejecución en Navegador\" (comando de arranque, salida, ámbito global, uso típico).  \\n* **Ejercicio práctico extra**: pedir al lector que cree una variable `nombre` a partir de `prompt` (sólo en navegador) y la combine en el mensaje. Indicar de forma concisa cómo habilitar `prompt` y mostrar el resultado en consola.  \\n\\n### Conexión con epígrafes futuros  \\nCerrar señalando que para avanzar es necesario comprender cómo declarar variables, tipos de datos y operadores, áreas que se explorarán en el siguiente tema. Evitar conclusiones generales sobre la programación; limitarse a verificar que el ciclo «editar → guardar → ejecutar → ver resultado» funciona correctamente.', 'Comienza con un breve recordatorio del primer programa \"Hola, mundo\" y explica que, para que un algoritmo manipule información real, necesita disponer de contenedores en memoria. Introduce inmediatamente el concepto de variable como ese contenedor y enlaza con la idea de identificadores ya vista al describir los scripts iniciales.\\n\\n1. Presenta la sintaxis básica de declaración en JavaScript en una narrativa progresiva: primero **var**, después **let** y finalmente **const**. Explica por qué **var** aparece históricamente primero y señala que hoy se considera obsoleto en la mayor parte de los casos, pero sigue existiendo por retrocompatibilidad.\\n2. Dedica un apartado a los tres modelos de alcance: global, de función y de bloque. Incluye un diagrama mermaid muy sencillo en el que se vea un bloque _function_ conteniendo otro bloque interno y dónde se queda cada variable según fuera declarada con **var**, **let** o **const**. No es necesario detallar cada nivel de profundidad; basta con ilustrar el principio.\\n3. Introduce el concepto de _hoisting_ únicamente asociado a **var**, describe su funcionamiento paso a paso y enlázalo con la recomendación de preferir **let** y **const**. Incluye un breve fragmento de código que muestre una referencia a una variable antes de su declaración y el resultado en consola.\\n4. Explica la diferencia entre reasignación y redeclaración. Señala que **const** impide la reasignación del identificador pero no vuelve inmutable al objeto o arreglo referenciado; incluye un micro-ejemplo con un arreglo modificado tras declararse con **const**.\\n5. Añade reglas estilísticas sobre nombres (camelCase, evitar palabras reservadas, claridad semántica), insistiendo en la importancia de la legibilidad para el mantenimiento del código.\\n6. Proporciona tres fragmentos cortos de código que muestren: a) alcance global frente a bloque; b) redeclaración con **var** y su consecuencia; c) intento de reasignar una **const**. Después de cada fragmento, indica con una frase (no un párrafo extenso) el resultado que se espera en consola.\\n7. Finaliza el epígrafe con una lista concisa de buenas prácticas (máximo 5 puntos) que sirva de transición hacia los tipos de datos, a tratar a continuación.\\n\\nExtensión prevista: 1100-1300 palabras.', 'Introduce el epígrafe enlazando con la idea de que una variable, además de un nombre, alberga un tipo de dato. Recuerda brevemente que JavaScript es un lenguaje de tipado dinámico, para diferenciarlo del pseudocódigo fuertemente tipado que el alumnado pudo imaginar previamente.\\n\\n1. Desglosa los tipos primitivos oficiales: **number**, **string**, **boolean**, **undefined**, **null**, **symbol** y **bigint**. Dedica uno o dos párrafos a cada tipo, ejemplificando con una literal representativa (por ejemplo, `42n` para **bigint**) y mostrando qué devuelve `typeof`.\\n2. Aclara la diferenciación entre **undefined** y **null** desde un punto de vista semántico, dejando claro que ambos representan \"ausencia\", pero en contextos distintos. Incluye un micro-ejemplo donde una función sin `return` devuelve **undefined** y otro donde una variable se inicializa explícitamente con **null**.\\n3. Presenta el concepto de coerción implícita y explora su mecánica con una narrativa apoyada en fragmentos de código: concatenación con el operador `+`, coerción booleana en contextos condicionales y comparación no estricta (`==`). A continuación, muestra la coerción explícita con `Number()`, `String()`, `Boolean()`, `parseInt`, `parseFloat` y el operador unario `+`.\\n4. Incluye una pequeña tabla que resuma las conversiones más frecuentes (por ejemplo, `\"5\" - 2 => 3`) y advierte de las operaciones inesperadas (`\"five\" * 2 => NaN`). Mantén la tabla en 6-8 filas como máximo.\\n5. Ejercicio guiado (único para todo el tema en este epígrafe): plantea cinco líneas de código donde se mezclen números y cadenas; pide al lector predecir el resultado antes de revelar la ejecución real. Después del bloque, proporciona la salida esperada y una breve explicación de cada línea.\\n6. Explica la diferencia entre igualdad suelta (`==`) e igualdad estricta (`===`) reforzando que el uso de `===` evita sorpresas por coerción implícita. Ofrece dos ejemplos contrastados.\\n7. Cierra el epígrafe con una transición: «Ahora que comprendemos los valores y cómo se convierten, necesitamos herramientas para operarlos», anticipando el estudio de operadores.\\n\\nExtensión prevista: 1200-1400 palabras.', 'Abre con la idea de que los algoritmos manipulan datos a través de operadores; retoma la tabla de coerciones para recordar por qué conocer el tipo de resultado es vital al combinarlos.\\n\\n1. Presenta de forma narrativa los operadores aritméticos básicos (`+`, `-`, `*`, `/`, `%`) y enseguida introduce `**` (exponenciación) conectando con la noción matemática vista en bachillerato. Utiliza ejemplos muy breves con comentarios en línea para cada caso.\\n2. Explica los operadores de asignación compuesta (`+=`, `-=`, `*=`, etc.) enlazando con la substitución de expresiones repetitivas. Ofrece un fragmento de código que incremente un contador con estilos alternativos.\\n3. Dedica un apartado a los operadores comparativos (`>`, `<`, `>=`, `<=`, `==`, `===`, `!=`, `!==`). Relaciona el tema con el próximo bloque sobre estructuras condicionales, remarcando la importancia de que su resultado sea booleano.\\n4. Introduce los operadores lógicos (`&&`, `||`, `!`) y el concepto de evaluación de corto circuito; muestra cómo pueden usarse para asignar valores por defecto (`const port = envPort || 3000;`).\\n5. Menciona brevemente el operador ternario como forma compacta de decisión simple, sin entrar aún en control de flujo detallado (eso se desarrollará en el tema siguiente). Incluye un fragmento con ternario y la misma lógica expresada con `if` para contrastar.\\n6. Incorpora una tabla compacta de precedencia (máximo 10 filas, de mayor a menor), resaltando con negrita aritméticos, comparativos y lógicos para facilitar la memorización.\\n7. Incluye un ejemplo único algo más extenso que combine aritméticos, lógicos y comparación estricta. Pide al lector seguir la precedencia paso a paso; después del código, añade la resolución comentada.\\n8. Termina abriendo la puerta a la interacción usuario-programa remarcando que, para ver el efecto de estos operadores en tiempo real, se necesita introducir y mostrar datos, preludio del siguiente epígrafe.\\n\\nExtensión prevista: 1000-1200 palabras.', 'Inicia el texto recordando que hasta este punto todo se ha visto en forma de fragmentos aislados y que ahora es necesario comunicarse con el usuario o con la consola para observar resultados más dinámicos.\\n\\n1. Presenta rápidamente las tres funciones clásicas del entorno navegador: `alert`, `prompt` y `confirm`. Explica su naturaleza modal y síncrona y muestra un ejemplo breve de cada una.\\n2. Introduce `console.log`, `console.error` y `console.table` en el contexto de desarrollador; subraya que son multiplataforma (navegador y Node) y que permiten depurar con mayor flexibilidad.\\n3. Incluye una nota sobre el entorno Node.js: explicar que `prompt` no existe de forma nativa y mencionar `readline` solo de forma referencial (sin profundizar) para no exceder el nivel de complejidad.\\n4. Ejemplo práctico (segundo y último ejercicio del tema): propone construir una \"calculadora mínima\" que\\n   – solicite dos números mediante `prompt`,\\n   – pregunte la operación mediante otro `prompt` (suma, resta, multiplicación o división) y\\n   – muestre el resultado con `alert` y `console.log` empleando plantillas literales.\\n   Tras el código, incluye una breve explicación en pasos, reforzando la conversión de cadena a número y el uso de operadores aprendidos.\\n5. Añade un cuadro de buenas prácticas para salida en consola (evitar logs redundantes, formateo, uso de etiquetas).\\n6. Conecta con el próximo tema señalando que, al recibir datos externos, se hace indispensable decidir qué ruta seguir según su valor; de ahí surge la necesidad de estructuras condicionales.\\n\\nExtensión prevista: 850-1000 palabras.', '**Extensión esperada:** 1000–1200 palabras.\\n\\n**Objetivo general**  \\nGuiar la transición desde la manipulación de datos (tema anterior) hacia el primer mecanismo formal de control de flujo, presentando cómo ejecutar bloques de código de manera selectiva según condiciones evaluadas en tiempo de ejecución.\\n\\n**Estructura sugerida**\\n\\n1. *Puente contextual* (≈100 palabras)  \\n   - Explica la necesidad de tomar decisiones en un programa: “los datos por sí solos no son suficientes; hay que decidir qué hacer con ellos”.  \\n   - Conecta con los operadores comparativos ya aprendidos.\\n\\n2. *Sintaxis y semántica del condicional simple* (≈250 palabras)  \\n   - Muestra la forma general `if (condición) { … }`.  \\n   - Señala la obligatoriedad de las llaves cuando se sigue la guía de estilo recomendada.  \\n   - Advierte sobre la coerción implícita y promueve el uso de `===` frente a `==`.\\n\\n3. *Encadenamiento de alternativas* (≈180 palabras)  \\n   - Introduce `else` y `else if` como cascada de evaluaciones.  \\n   - Ilustra la evaluación de arriba abajo y la detención tras la primera condición verdadera.\\n\\n4. *Selección múltiple con `switch`* (≈250 palabras)  \\n   - Presenta la sintaxis general `switch (expresión) { case valor: … break; … }`.  \\n   - Explica el concepto de *fall-through* y cómo evitarlo con `break`.  \\n   - Discute cuándo resulta preferible a una cadena extensa de `else if`.\\n\\n5. *Comparativa resumida* (≈60 palabras)  \\n   - Inserta una tabla con columnas: «Estructura», «Uso recomendable», «Ventajas», «Riesgos».\\n\\n6. *Ejemplo integrado* (≈120 palabras)  \\n   - Escenario: calculadora de tarifas de envío que decide precio según peso y destino.  \\n   - Incluye una versión con `if/else` y otra con `switch` para reforzar la equivalencia conceptual.\\n\\n7. *Ejercicio propuesto* (≈40 palabras)  \\n   - Solicita implementar un clasificador de calificaciones (A–F) a partir de una nota numérica.\\n\\n8. *Buenas prácticas y estilo* (≈80 palabras)  \\n   - Recomienda nombrar claramente las variables lógicas, evitar anidamientos profundos y preferir expresiones booleanas simples.\\n\\n9. *Cierre transitorio* (≈40 palabras)  \\n   - Anticipa que los próximos controles de flujo permitirán repetir bloques de código, no solo elegir entre ellos.', '**Extensión esperada:** 1100–1300 palabras.\\n\\n**Objetivo general**  \\nPresentar los mecanismos de repetición, demostrando cómo automatizar tareas que requieren ejecutar la misma lógica varias veces y preparando el terreno para patrones de iteración más avanzados.\\n\\n**Estructura sugerida**\\n\\n1. *Introducción motivacional* (≈120 palabras)  \\n   - Cuestiona la repetición manual de instrucciones y relaciona la necesidad de loops con la eficiencia y la reducción de errores.\\n\\n2. *Principio fundamental de la iteración* (≈100 palabras)  \\n   - Explica la tríada «inicialización → condición → actualización» como núcleo de cualquier bucle.\\n\\n3. *Bucle controlado por contador (`for`)* (≈280 palabras)  \\n   - Desglosa la cabecera en sus tres componentes.  \\n   - Muestra la posibilidad de omitir cualquiera de ellas con precaución.  \\n   - Incluye un ejemplo que suma los números del 1 al 100 y destaca el riesgo de modificar la variable de control dentro del cuerpo.\\n\\n4. *Bucle controlado por condición (`while`)* (≈220 palabras)  \\n   - Diferencia entre bucles de contador y de condición abierta.  \\n   - Ejemplo: lectura de un `prompt` hasta que el usuario introduce \"salir\".  \\n   - Subraya la importancia de actualizar la condición dentro del cuerpo.\\n\\n5. *Bucle post-condicional (`do … while`)* (≈160 palabras)  \\n   - Explica que garantiza al menos una ejecución.  \\n   - Ejemplo: menú interactivo que se muestra una vez antes de evaluar la salida.\\n\\n6. *Comparación sincrónica* (≈80 palabras)  \\n   - Tabla síntesis: «Ejecución mínima», «Momento de evaluación», «Casos de uso típicos».\\n\\n7. *Diagrama mermaid* (≈60 palabras)  \\n   - Sugiere representar el flujo general de un `while` con sus decisiones para reforzar la comprensión visual.\\n\\n8. *Ejercicio guiado* (≈140 palabras)  \\n   - Desarrollar un programa que calcule el factorial de un número usando las tres estructuras de bucle y medir con `console.time` la diferencia de rendimiento (anticipa el análisis Big-O sin profundizar).\\n\\n9. *Errores comunes* (≈70 palabras)  \\n   - Olvidar la actualización, usar comparaciones con coma flotante, modificar la colección iterada.\\n\\n10. *Enlace con el siguiente epígrafe* (≈40 palabras)  \\n    - Introduce la idea de que es posible alterar el comportamiento de un bucle desde dentro mediante sentencias especiales, lo cual se detalla luego.', '**Extensión esperada:** 1000–1200 palabras.\\n\\n**Objetivo general**  \\nExplorar las instrucciones que alteran el flujo interno de los bucles y presentar patrones de iteración recurrentes, fomentando la escritura de código legible y eficiente.\\n\\n**Estructura sugerida**\\n\\n1. *Recordatorio breve* (≈80 palabras)  \\n   - Recupera la estructura de un bucle y motiva la necesidad de abandonarlo o saltar iteraciones.\\n\\n2. *Sentencia `break`* (≈220 palabras)  \\n   - Sintaxis y efecto inmediato sobre cualquier bucle o `switch`.  \\n   - Caso de uso: búsqueda de un elemento en un array; muestra cómo detener el recorrido una vez encontrado.\\n\\n3. *Sentencia `continue`* (≈200 palabras)  \\n   - Explica que finaliza la iteración actual y pasa a la siguiente.  \\n   - Ejemplo: impresión de números impares omitiendo los pares.\\n\\n4. *Advertencias de legibilidad* (≈120 palabras)  \\n   - Exceso de saltos dificulta el seguimiento lógico; recomienda condicionar expresiones cuando sea posible.\\n\\n5. *Patrones de iteración* (≈260 palabras)  \\n   - *Contador clásico*: índice que avanza un paso fijo.  \\n   - *Acumulador*: suma o combina valores en la iteración.  \\n   - *Búsqueda y corte*: combinación de recorrido y `break`.  \\n   - *Centinela*: bucle que depende de un valor especial para terminar.  \\n   - Incluye ejemplos breves y resalta su frecuencia en algoritmos.\\n\\n6. *Tabla resumen* (≈60 palabras)  \\n   - Columnas: «Patrón», «Descripción», «Estructura sugerida», «Elementos clave».\\n\\n7. *Ejercicio aplicado* (≈140 palabras)  \\n   - Implementar una función que determine si un array es *palíndromo*. Debe recorrer la mitad del array, usar `break` al detectar la primera desigualdad y devolver un booleano.\\n\\n8. *Transición temática* (≈40 palabras)  \\n   - Menciona que usar adecuadamente estas sentencias evita bucles innecesarios, pero también puede provocar que un bucle nunca acabe si no se programan correctamente las condiciones, tema que se abordará a continuación.', '**Extensión esperada:** 900–1100 palabras.\\n\\n**Objetivo general**  \\nProporcionar criterios y estrategias para diseñar bucles seguros que se detengan siempre y funcionen dentro de los límites de recursos disponibles.\\n\\n**Estructura sugerida**\\n\\n1. *Contextualización* (≈80 palabras)  \\n   - Resalta el impacto negativo de un bucle infinito en el entorno JavaScript (consumo de CPU, bloqueo de la interfaz, detención del event loop).\\n\\n2. *Causas típicas* (≈250 palabras)  \\n   - No actualizar la variable de control.  \\n   - Condición que nunca se vuelve falsa por errores lógicos.  \\n   - Precisión limitada de números en incrementos decimales.  \\n   - Modificación concurrente de estructuras de datos dentro del bucle.\\n\\n3. *Estrategias preventivas* (≈300 palabras)  \\n   - Diseñar la condición de parada antes de escribir el cuerpo.  \\n   - Validar límites y establecer contadores máximos de seguridad.  \\n   - Utilizar funciones puras que no alteren estados externos.  \\n   - Adoptar pruebas unitarias y *watchdogs* temporales (`setTimeout`) en entornos de larga ejecución.\\n\\n4. *Herramientas de depuración* (≈140 palabras)  \\n   - Uso de puntos de interrupción (DevTools) para vigilar la variable de control.  \\n   - Comando `Ctrl + C` en Node.js y método de análisis de pila para cortar ejecución.\\n\\n5. *Ejemplos contrastados* (≈150 palabras)  \\n   - Caso erróneo: `while (x !== 10) { x += 0.2 }`.  \\n   - Corrección con margen de tolerancia `Math.abs(x - 10) < Number.EPSILON`.\\n\\n6. *Diagrama mermaid simple* (≈60 palabras)  \\n   - Representa un bucle con verificación secundaria de contador límite que fuerza salida.\\n\\n7. *Recomendaciones finales* (≈60 palabras)  \\n   - Sugiere revisión por pares y linters que advierten sobre actualizaciones faltantes.  \\n   - Conecta con el próximo tema de funciones, donde la modularidad ayudará a encapsular lógica repetitiva sin exponerse a errores de bucle.', '**Extensión orientativa: 850-1 100 palabras**\\n\\n- Abre con dos frases que recuerden brevemente la necesidad de la reutilización de código vista en los temas anteriores y enlázala con la idea de agrupar instrucciones bajo un identificador.\\n- Introduce el concepto de función como primera unidad de modularidad. Evita aún hablar de parámetros o retorno; céntrate en la sintaxis básica.\\n- Presenta consecutivamente la _function declaration_ (palabra reservada `function` al inicio de la línea) y la _function expression_ (asignación de una función a una variable). Explica la diferencia de comportamiento en fase de _hoisting_ con un tono narrativo, reservando una tabla comparativa de dos columnas («Visibilidad antes de la ejecución», «Tipo de identificador») para clarificar sin extenderse demasiado.\\n- Señala cuándo optar por cada forma en proyectos reales (declaraciones para funciones “de uso global”, expresiones para callbacks o paso de funciones como argumentos) evitando emitir todavía conclusiones cerradas.\\n- Incorpora el **Ejemplo 1** del tema: un pequeño programa de calculadora con cuatro operaciones básicas escritas primero como declaraciones y luego como expresiones. El código debe ocupar como máximo 15-20 líneas y aparecer en un bloque único.\\n  - Tras el código, ofrece un párrafo explicativo que subraye dónde se observa el _hoisting_ y la posibilidad de re-usar expresiones como callbacks.\\n- Cierra con una breve transición hacia la siguiente parte del tema, indicando que, una vez vistas las formas de definir funciones, se estudiará cómo intercambiar información con ellas.\\n\\n_Evitar:_ Hablar de `this`, parámetros, flechas o módulos; se cubrirán en los epígrafes posteriores.', '**Extensión orientativa: 900-1 200 palabras**\\n\\n- Inicia enlazando con la definición de funciones vista anteriormente: “Ahora que sabemos declarar funciones, necesitamos dotarlas de ‘entradas’ y ‘salidas’…”.\\n- Expón, en orden creciente de complejidad:\\n  1. Parámetros posicionales simples.\\n  2. Valores por defecto (mencionar ES6, mantener sintaxis ligera).\\n  3. El operador _rest_ para captar n argumentos y el _spread_ para descomponer colecciones al invocar.\\n- Dedica un par de párrafos al `return`: qué ocurre cuando se omite, cómo finaliza la ejecución y la imposibilidad de devolver varias expresiones directamente (sugerir uso de objetos o arrays sin desarrollarlo en exceso).\\n- Introduce la sintaxis de funciones flecha mostrando primero la forma corta `()=>{}` y luego la forma con cuerpo implícito. Subraya las diferencias importantes respecto a las funciones tradicionales, pero restringe la discusión de `this` hasta el epígrafe 3 (solo anótalo como “diferencia clave que veremos más adelante”).\\n- Incluye el **Ejemplo 2** del tema: transformación de la calculadora del ejemplo anterior para aceptar un número arbitrario de operandos usando _rest_, devolviendo promedios y mediana con funciones flecha de una sola línea. Usa 12-15 líneas.\\n- Anima al redactor a insertar breves notas en comentario dentro del código para mostrar la ergonomía de parámetros por defecto y flechas.\\n- Añade un minielemento visual opcional: una tabla con tres filas (Parámetro simple, Con valor por defecto, Rest) y dos columnas (Sintaxis, Caso de uso) para reforzar la memorización.\\n- Termina conectando con la idea de funciones dentro de funciones: “Poder pasar funciones como datos nos llevará al concepto de cierres y al estudio del alcance…”.', '**Extensión orientativa: 1 000-1 300 palabras**\\n\\n- Arranca recordando que las funciones pueden anidarse y que en JavaScript cada función crea un nuevo “entorno”.\\n- Desarrolla el concepto de alcance léxico de manera narrativa: define _scope chain_ y relata cómo el motor busca identificadores desde el contexto más interno hacia el global.\\n- Introduce formalmente el término _closure_ como la combinación de una función y el entorno donde fue creada. Ilustra paso a paso con un hilo argumental:  \\n  1. Función `makeCounter` que declara una variable privada `count`.  \\n  2. Devuelve una función interna que incrementa y muestra `count`.  \\n  3. Se invoca `makeCounter()` dos veces para demostrar estados independientes.\\n- Este bloque sirve como **Ejemplo 3** del tema. El código no debe superar 25 líneas y ha de ir acompañado de un diagrama _mermaid_ sencillo que represente la cadena de ámbitos (`Global -> makeCounter -> innerFn`). Indica al redactor que use rectángulos simples y flechas verticales.\\n- Tras el ejemplo, profundiza: comportamiento de variables capturadas, riesgos de “capturar” variables mutables dentro de bucles y la importancia de `let`/`const` vistos en temas anteriores.\\n- Incluye **un único ejercicio práctico** para todo el tema: “Implemente una función `memoize` que reciba otra función costosa y devuelva una versión optimizada mediante cierre”. Describe los requisitos en 3 bullets (entradas, almacenamiento en un objeto, devolver resultado cacheado) y reserva cualquier solución detallada para futuros materiales.\\n- Conecta sutilmente con módulos, señalando que los cierres ofrecen encapsulación en pequeña escala, pero las aplicaciones reales requieren agrupar código en archivos separados.', '**Extensión orientativa: 800-1 100 palabras**\\n\\n- Abre con la necesidad de organización a gran escala: “Cuando el proyecto crece, no basta con encapsular variables mediante cierres…”.\\n- Introduce la especificación ES6 Modules: qué problema soluciona y su aceptación nativa por navegadores y Node (sin detallar _bundlers_ para no desviar el foco).\\n- Describir en orden:\\n  1. Sintaxis de `export` nombrado.  \\n  2. Sintaxis de `export default`.  \\n  3. Correspondientes formas de `import` y alias.\\n- Usa como hilo argumental la evolución del contador del ejemplo anterior: dividirlo en dos archivos `counter.js` (que exporta `makeCounter` por defecto) y `main.js` (que lo importa y lo utiliza). No añadas el código completo; basta con fragmentos de 4-5 líneas por archivo que el redactor completará.\\n- Incluye una tabla comparativa con cuatro filas (Export nombrado, Export default, Import destructuring, Import con alias) y una columna de sintaxis abreviada y otra de “cuándo usar”.\\n- Menciona brevemente la política de un solo módulo por archivo y la forma de evitar contaminación global.\\n- Cierra el tema preparando la transición a los análisis de eficiencia algorítmica del siguiente bloque: “Con los cimientos de modularidad asentados, estamos listos para evaluar el rendimiento de nuestros algoritmos en términos de tiempo y memoria…”.\\n\\n_No agregar ejemplos nuevos ni ejercicios aquí; reutilizar el caso del contador como referencia suficiente_.', '### Propósito y encuadre\\nIntroduce al estudiante en la necesidad de evaluar la calidad de un programa más allá de que simplemente \"funcione\". Conecta con los temas anteriores recordando que ya saben escribir funciones y controlar el flujo, pero aún no han reflexionado sobre el coste que implica cada instrucción cuando los datos crecen.\\n\\n### Esquema narrativo sugerido\\n1. **Motivación práctica**\\n   * Caso de uso: un script de búsqueda que tarda milisegundos con 100 elementos pero segundos con un millón.  \\n   * Pregunta guía: «¿Cómo podemos anticipar este crecimiento antes de ejecutar el código?»\\n2. **Definición de coste computacional**\\n   * Distingue entre número de operaciones (tiempo) y unidades de memoria ocupadas (espacio).  \\n   * Aclara que se habla de *orden* de magnitud, no de segundos ni de bytes concretos.\\n3. **Modelo de cómputo simplificado**\\n   * Una operación elemental = asignación, acceso a arreglo, comparación, etc.  \\n   * Justifica la abstracción y menciona que más adelante se refinará con mediciones reales.\\n4. **Creación progresiva de ejemplos**\\n   * Ejemplo 1: asignar una variable → coste constante.  \\n   * Ejemplo 2: recorrer un arreglo con `for` → coste proporcional al tamaño.  \\n   * Ejemplo 3: doble `for` anidado → crecimiento cuadrático.  \\n   * Ejemplo 4: función recursiva simple (factorial) → coste lineal en llamadas y lineal en espacio por la pila.\\n5. **Introducción a las clases de crecimiento**\\n   Lista breve de O(1), O(log n), O(n), O(n log n), O(n²) y O(2ⁿ) con descripción intuitiva. Utiliza una tabla para contrastar velocidad de crecimiento con n = 10, 100, 1000.\\n6. **Espacio frente a tiempo**\\n   * Muestra cómo un algoritmo puede intercambiar memoria por rapidez (ej. tabla de búsqueda precalculada).\\n   * Incluye mini–ejemplo práctico con un objeto cacheado frente a un cálculo reiterado.\\n7. **Conclusión transitoria**\\n   * Resume la idea de que analizar coste evita sorpresas y prepara el terreno para expresar formalmente ese análisis con notación Big-O en el siguiente bloque.\\n\\n### Recursos didácticos sugeridos\\n* **Tabla** comparativa de clases de complejidad (filas = n, columnas = clase, celdas = número de operaciones estimadas).\\n* **Diagrama mermaid** sencillo que relacione tamaño de entrada → número de operaciones → tiempo de ejecución perceptible.\\n\\n### Ejercicio práctico (1)\\nProporciona dos funciones que calculan la suma de los números del 1 al n, una iterativa y otra usando la fórmula matemática. Pide al alumno estimar tiempo y espacio de cada opción y justificar qué pasará cuando n = 10⁹.\\n\\n### Extensión esperada\\n900 – 1100 palabras.', '### Propósito y encuadre\\nFormaliza la intuición previa presentando la notación Big-O como lenguaje estándar para describir crecimiento. Evita repeticiones retomando brevemente lo estudiado y enfocando el epígrafe en **cómo** obtener la notación a partir de código real.\\n\\n### Esquema narrativo sugerido\\n1. **Recordatorio motivacional**  \\n   Recapitula que se busca una forma compacta de expresar las magnitudes vistas en el apartado anterior.\\n2. **Definición formal de Big-O**  \\n   * Presenta la idea de función g(n) que acota superiormente el número de operaciones f(n).  \\n   * Menciona (sin profundizar en demostraciones) que existen Big-Θ y Big-Ω, pero el foco es Big-O.\\n3. **Reglas prácticas de cálculo**  \\n   * Ignorar constantes multiplicativas y sumandos menores.  \\n   * Bucles simples → sumar complejidades.  \\n   * Bucles anidados → multiplicar complejidades.  \\n   * Sentencias condicionales → tomar el camino con mayor coste.  \\n   * Llamadas a funciones → usar la complejidad de la función llamada.  \\n   * Recursión: introducir la ecuación de recurrencia básica (ej. T(n) = T(n/2) + 1).\\n4. **Ejemplos paso a paso**  \\n   * Código que suma elementos de un arreglo → O(n).  \\n   * Código que compara cada par de elementos → O(n²).  \\n   * Búsqueda binaria iterativa → O(log n).  \\n   * Recursión de Fibonacci ingenua → O(2ⁿ).\\n5. **Tabla resumen**  \\n   Incluye una tabla con patrón de código, ecuación resultante y Big-O final.\\n6. **Visualización de jerarquía de complejidades**  \\n   Pequeño diagrama mermaid en forma de pirámide, de menor a mayor coste.\\n7. **Relación con la práctica**  \\n   Discute por qué no se cuentan micro-operaciones: portabilidad entre lenguajes, optimizaciones del compilador, etc.  \\n   Enlaza con el epígrafe siguiente indicando que los diferentes casos (mejor/promedio/peor) usan la misma notación.\\n\\n### Ejercicio práctico (1)\\nEntrega un fragmento de código que implementa ordenamiento burbuja y pide:  \\n   a) Derivar la ecuación que representa el número de comparaciones.  \\n   b) Simplificarla a su Big-O.  \\n   c) Identificar si existen caminos de ejecución con menor coste.\\n\\n### Recursos didácticos sugeridos\\n* Tabla de patrones frecuentes (bucle doble, bucle logarítmico, recursión divide-and-conquer) con su regla de cálculo.\\n\\n### Extensión esperada\\n1000 – 1200 palabras.', '### Propósito y encuadre\\nProfundiza en la idea de que un mismo algoritmo no siempre se comporta igual. Se introduce la medición en diferentes escenarios y cuándo interesa cada una.\\n\\n### Esquema narrativo sugerido\\n1. **Distinción conceptual**  \\n   * Define los tres casos con una narrativa basada en ejemplos cotidianos (buscar un archivo en un cajón desordenado: puede que esté arriba, medio o al fondo).\\n2. **Representación con notación Big-O y noaciones complementarias**  \\n   Explica que se utiliza la misma notación pero tomada sobre distintos subconjuntos de entradas.  \\n   Introduce, solo de forma nominal, Ω y Θ para reforzar que O es una cota superior.\\n3. **Ejemplos ilustrativos**  \\n   * Búsqueda lineal en un arreglo desordenado.  \\n      - Mejor: O(1), Peor: O(n), Promedio: O(n/2) → O(n).  \\n   * Quicksort con pivote extremo frente a pivote aleatorio.  \\n   * Hash table con buena función hash frente a colisiones masivas.\\n4. **Ingredientes de un análisis promedio**  \\n   Aborda brevemente la noción de distribución de probabilidad y suponer entradas equiprobables. Sin matemáticas avanzadas.\\n5. **Visualización comparativa**  \\n   Propón una tabla con tres columnas (mejor, promedio, peor) y filas para distintos algoritmos vistos.\\n6. **Aplicación práctica**  \\n   Discute por qué en industria se suele reportar promedio, mientras que en seguridad informática interesa el peor caso. Conecta con análisis de rendimiento real que se cubrirá en el epígrafe siguiente.\\n\\n### Ejercicio práctico (1)\\nPresenta tres implementaciones de búsqueda en un arreglo de objetos:\\n* a) Recorrido lineal.  \\n* b) Recorrido lineal con parada temprana cuando el índice es mayor que la posición ordenada esperada.  \\n* c) Búsqueda binaria (arreglo ordenado).\\n\\nPide a los alumnos identificar y justificar los tres casos para cada implementación.\\n\\n### Recursos didácticos sugeridos\\n* Tabla comparativa de complejidades.  \\n* Gráfico mermaid de líneas sobre el mismo eje mostrando cómo crecen mejor/promedio/peor con n.\\n\\n### Extensión esperada\\n850 – 1000 palabras.', '### Propósito y encuadre\\nRelaciona la teoría con la práctica enseñando a medir el rendimiento real de un programa JavaScript. Prepara el terreno para las estructuras de datos que se implementarán en los siguientes temas, donde validar la eficiencia será crucial.\\n\\n### Esquema narrativo sugerido\\n1. **Por qué medir**  \\n   * Breve historia: la JIT de V8 y optimizaciones inesperadas.  \\n   * Diferencia entre complejidad asintótica y tiempo real.\\n2. **Herramientas nativas en navegador**  \\n   * `performance.now()` para obtener timestamps de alta resolución.  \\n   * `console.time` / `console.timeEnd` para mediciones rápidas.  \\n   * Uso del panel *Performance* de Chrome DevTools para visualizar la línea de tiempo.\\n3. **Herramientas nativas en Node.js**  \\n   * `process.hrtime.bigint()` y `process.memoryUsage()`.  \\n   * Demostración de script CLI que calcula duración y consumo de memoria.  \\n   * Breve mención de `--prof` y reporte de perfiles.\\n4. **Librerías especializadas**  \\n   * Introduce Benchmark.js y muestra un *suite* básico con calentamiento, repetición y presentación de resultados.  \\n   * Explica cómo evitar interferencias externas (garbage collector, I/O, variación del turbo fan).\\n5. **Metodología de micro-benchmarking**  \\n   * Calentamiento para JIT.  \\n   * Promediar sobre muchas iteraciones.  \\n   * Aíslate de I/O y operaciones asíncronas.  \\n   * Consejo: medir algoritmos, no tiempos de redibujado.\\n6. **Ejemplo comparativo**  \\n   Siguiendo los algoritmos de suma de números (iterativo vs `reduce`), muestra cómo instrumentar y recoger resultados en tablas e interpretar qué implementación es más rápida y por qué.\\n7. **Visualización de resultados**  \\n   * Propón una tabla con columnas *implementación*, *ops/sec*, *desviación estándar*, *memoria media*.  \\n   * Via diagrama mermaid, bosqueja cómo crece el tiempo cuando n aumenta de 1 000 a 1 000 000.\\n8. **Buenas prácticas y errores comunes**  \\n   * No probar varias cosas dentro del mismo bucle.  \\n   * No fiarse de la primera ejecución.  \\n   * Documentar la configuración de hardware y versión de Node/navegador.\\n9. **Conexión hacia adelante**  \\n   Anticipa que las próximas estructuras (arrays, listas, árboles) se podrán medir con estas técnicas para contrastar su complejidad asintótica.\\n\\n### Ejercicio práctico (1)\\nPide al alumno crear un script que compare la complejidad real de tres métodos de búsqueda:\\n* Búsqueda lineal.  \\n* Búsqueda binaria sobre arreglo ordenado.  \\n* Método `indexOf` nativo.\\n\\nDebe registrar tiempo y memoria para n = 10³, 10⁴, 10⁵ y presentar los resultados en una tabla.\\n\\n### Recursos didácticos sugeridos\\n* Tabla de resultados de ejemplo.  \\n* Diagrama mermaid de escalado temporal.\\n\\n### Extensión esperada\\n1000 – 1200 palabras.', '### Contextualización inicial\\nBrevemente conecta el uso de colecciones lineales con lo ya estudiado sobre tipos primitivos y estructuras de control, subrayando que una *colección indexada* es la forma natural de agrupar datos relacionados cuando el número de elementos es variable y potencialmente grande.\\n\\n### Cuerpo narrativo propuesto (orden recomendado)\\n1. **Definir el concepto general de colección y su representación en JavaScript**\\n   - Array como objeto especializado (`typeof [] ➜ \"object\"`).\\n   - Diferencia con *array-like objects* (p.ej. `arguments`, `NodeList`).\\n2. **Formas de creación**  \\n   - Literal (`[]`), constructor (`new Array()`), `Array.of`, `Array.from` (incluye ejemplo de conversión de `NodeList`).  \\n   - Ventajas y riesgos de cada sintaxis (p.ej. el constructor con un solo argumento numérico).\\n3. **Estructura interna y propiedad `length`**  \\n   - Índices enteros, huecos (sparse arrays), autoadaptación de tamaño.  \\n   - Cuándo y por qué la asignación a un índice alto rellena con `undefined`.\\n4. **Operaciones de mutación fundamentales**  \\n   - Asignación directa.  \\n   - Inserción y extracción en extremos: `push`, `pop`, `shift`, `unshift`.  \\n   - Inserciones en posiciones arbitrarias con `splice`.  \\n   - Reordenación (`sort`, `reverse`), relleno (`fill`), copia interna (`copyWithin`).  \\n   - Clasifica cada método según si devuelve el tamaño nuevo, el elemento retirado, o el propio array.\\n5. **Inmutabilidad vs. mutabilidad práctica**  \\n   - Diferencia entre *referencia* y *valor* (recordatoria breve con analogía a objetos).  \\n   - Clonado superficial con `slice`, operador *spread* y `structuredClone`.  \\n   - Impacto en depuración y efectos colaterales.\\n6. **Coste computacional de las operaciones**  \\n   - Vuelve a Big-O ya presentado:  \\n     • Acceso O(1).  \\n     • `push` / `pop` O(1).  \\n     • `shift` / `unshift` O(n) (reindexado).  \\n     - Incluye tabla comparativa.\\n7. **Buenas prácticas de codificación**  \\n   - Declarar referencias con `const` cuando el identificador no cambie.  \\n   - Evitar sparse arrays salvo necesidad específica.  \\n   - Preferir métodos descriptivos sobre manipulación manual de índices.\\n\\n### Ejemplo guiado\\n```js\\n// Registrar temperaturas y actualizar valores incorrectos\\nafterInspection = [22, 19, 21, 24];\\n// Se detecta error en el segundo registro ➜ sobreescritura\\nafterInspection[1] = 20;\\nafterInspection.push(23); // nuevo registro del día\\n```\\nIncluye comentario línea a línea sobre qué está cambiando en memoria y en la propiedad `length`.\\n\\n### Ejercicio propuesto\\nSolicita al estudiante leer un conjunto de nombres introducidos mediante `prompt`, almacenarlos en una colección y:\\n1. Añadir un nombre al principio y otro al final.\\n2. Remover el tercer elemento.\\n3. Mostrar resultado y tamaño final con `console.table`.\\n\\n### Elemento de apoyo opcional\\nTabla comparativa *Método ➜ Efecto sobre length ➜ Devuelve ➜ Big-O* (máximo 8 filas).\\n\\n### Extensión esperada\\nAproximadamente **850-1100 palabras**, sin contar fragmentos de código ni tablas.', '### Conexión con el epígrafe anterior\\nExplica que, una vez comprendidas las operaciones imperativas sobre colecciones, se introduce ahora un enfoque **declarativo** basado en *funciones de orden superior*, reforzando el paso de \"cómo\" a \"qué\" queremos obtener de los datos.\\n\\n### Despliegue temático sugerido\\n1. **Recordatorio de callbacks y funciones flecha** (máximo 1 párrafo) para justificar las firmas de los métodos.\\n2. **Iteración controlada con `forEach`**  \\n   - Firma, parámetros y contexto (`thisArg`).  \\n   - Uso legítimo: efectos secundarios.  \\n   - Ejemplo de registro con numeración de índice.\\n3. **Transformación con `map`**  \\n   - Inmutabilidad: genera nueva colección.  \\n   - Ejemplo: convertir precios de EUR a USD conservando 2 decimales.  \\n   - Nota sobre profundidad (map anidado ≠ deep clone).\\n4. **Filtrado con `filter`**  \\n   - Semántica booleana del callback.  \\n   - Ejemplo: seleccionar usuarios mayores de edad.  \\n   - Comparativa con `find` (devuelve uno, no lo desarrolles en profundidad para no salir del alcance).\\n5. **Reducción con `reduce`**  \\n   - Parámetros: acumulador, valor actual, índice, array original.  \\n   - Importancia del valor inicial.  \\n   - Ejemplo completo: media ponderada de calificaciones.  \\n   - Mención breve de `reduceRight` sin ahondar.\\n6. **Cadena de métodos (method chaining)**  \\n   - Lectura de izquierda a derecha.  \\n   - Riesgos de crear arrays intermedios extensos; introduce la idea de *pipeline* sin profundizar.\\n7. **Tabla resumen** en 5 columnas: Método | Devuelve | Mutación | Caso de uso | Complejidad.\\n8. **Buenas prácticas**  \\n   - Evitar mutar elementos dentro del callback salvo casos justificados.  \\n   - Nombrar callbacks descriptivamente para mejorar legibilidad.  \\n   - Prefiere `const` en la referencia del array original.\\n\\n### Ejemplos ilustrativos\\n1. **Cálculo de promedio de tiempos de respuesta** (map ➜ reduce).  \\n2. **Normalización de cadenas** (filter vacíos ➜ map toLowerCase).  \\n   - Incluye comentarios concisos sobre cada fase.\\n\\n### Ejercicio de aplicación\\nProporciona un arreglo de objetos `books` con propiedades `title`, `pages` y `author`.  \\nObjetivos:  \\n- Obtener un arreglo con los títulos de libros de más de 300 páginas (map + filter).  \\n- Calcular el total de páginas de esos libros (reduce).  \\nMuestra el resultado con `console.log` y comenta complejidad.\\n\\n### Extensión esperada\\nEntre **1000 y 1200 palabras**, sin contar código, tabla ni enunciado de ejercicio.', '### Puente con lo ya estudiado\\nAborda cómo los métodos de inserción y extracción vistos previamente permiten emular **estructuras lógicas de acceso restringido**, esenciales en el diseño de algoritmos (referencia breve a Big-O para enlazar contenido).\\n\\n### Desarrollo argumental propuesto\\n1. **Definición de tipos abstractos de datos** (TAD) e introducción a la noción LIFO y FIFO.  \\n2. **Implementación de una pila** usando `push` y `pop`  \\n   - Firma de las operaciones.  \\n   - Control de desbordamiento y subdesbordamiento lógicos (lanzar error o devolver `undefined`).  \\n   - Ejemplo: conversión de número decimal a binario acumulando restos.\\n3. **Implementación de una cola**  \\n   - Elección entre `push` + `shift` o `unshift` + `pop`.  \\n   - Eficiencia comparativa con la pila (reindexado en `shift`).  \\n   - Ejemplo: simulación de impresora (en cola de trabajos).  \\n4. **Análisis de complejidad**  \\n   - Tabla simple: Operación | Pila | Cola | Complejidad.  \\n   - Destaca coste O(n) de `shift` y posibles optimizaciones (menciona brevemente índice circular, sin implementarlo).\\n5. **Ventajas, desventajas y casos de uso típicos**  \\n   - Pila: backtracking, undo stack, evaluación de expresiones.  \\n   - Cola: BFS, sistemas de mensajería, planificadores.\\n6. **Ejemplo avanzado resumido**  \\n   - Evaluación de una expresión en Notación Polaca Inversa: describe algoritmo paso a paso y muestra fragmento de código.\\n\\n### Ejercicio de refuerzo\\nSolicita construir un *historial de navegador* con dos pilas (`backStack`, `forwardStack`) que permita las operaciones `visit(url)`, `back()`, `forward()`.  \\nIndica qué operaciones son O(1) y pide escribir pruebas sencillas con `console.assert`.\\n\\n### Elemento visual opcional\\nDiagrama Mermaid mostrando los punteros `top` (pila) y `front/rear` (cola) antes y después de una operación.\\n\\n### Extensión esperada\\nAlrededor de **900-1200 palabras**, excluyendo código, tabla y diagrama.', '### Introducción contextual\\nExplica que las colecciones lineales se pueden anidar para representar **estructuras tabulares** y que esto abre la puerta a algoritmos matriciales, juegos de tablero y procesamiento de datos bidimensionales.\\n\\n### Secuencia narrativa sugerida\\n1. **Concepto de array de arrays**  \\n   - Diferencia entre matriz densa y arreglo irregular (*jagged*).  \\n   - Representation mental (fila → array, columna → índice).  \\n   - Considera nota sobre que JavaScript no reserva memoria contigua.\\n2. **Creación de matrices**  \\n   - Literales anidados.  \\n   - Inicialización programática con `Array.from({length: n}, () => Array(m).fill(0))`.  \\n   - Error común: múltiplos alias al usar `fill` con la misma referencia.\\n3. **Acceso y modificación de elementos**  \\n   - Índices `[fila][columna]`.  \\n   - Validación de límites para evitar `TypeError` en filas indefinidas.  \\n   - Ejemplo: marcar celdas visitadas en un laberinto.\\n4. **Recorridos habituales**  \\n   - Bucles anidados `for`.  \\n   - `for...of` externo + `forEach` interno.  \\n   - Uso de `map` doble y coste de crear nuevas matrices (relación con epígrafe 2).\\n5. **Operaciones elementales**  \\n   - Transposición, suma de matrices y búsqueda de máximo.  \\n   - Explica, para cada una, la estrategia de recorrido y complejidad O(n·m).\\n6. **Visualización y depuración**  \\n   - Uso de `console.table`.  \\n   - Propuesta de diagrama Mermaid sencillo para mostrar un grid 3×3 con coordenadas.\\n\\n### Ejemplo ilustrativo\\nCódigo para transponer una matriz cuadrada de 3×3 usando `map` y destrucción de filas/columnas con *destructuring* en el retorno.\\n\\n### Ejercicio aplicado\\nPlantea implementar una función `nextGeneration(board)` que calcule un paso del *Juego de la Vida* sobre un tablero booleano 10×10.  \\nPide dividir la solución en:  \\n1. Conteo de vecinos.  \\n2. Aplicación de reglas.  \\n3. Generación de nueva matriz sin mutar la original.\\n\\n### Extensión esperada\\nEntre **950 y 1300 palabras**, sin contar código, diagrama ni enunciado de ejercicio.', 'Extensión orientativa: 800-1 000 palabras.\\n\\nIntroducción (≈ 1 párrafo)\\n• Sitúa al lector: tras haber trabajado con arrays, se pasa a estructuras que se construyen dinámicamente en memoria y se enlazan por referencias.\\n• Expón la necesidad: flexibilidad para insertar/eliminar sin costes de realineamiento.\\n\\nNarrativa conceptual\\n1. Modelo de memoria en JavaScript\\n   - Diferencia entre valores primitivos (stack) y objetos (heap) y cómo las variables actúan como referencias.\\n   - Aclarar que, aun sin “punteros” como en C, las referencias cumplen el mismo rol conceptual.\\n2. Diseño de la unidad básica\\n   - Presenta la idea de “nodo” como contenedor de dato + referencia(s).\\n   - Compara dos estilos de implementación:\\n     • Función constructora/prototipo.\\n     • Clase ES6 (preferida por legibilidad).\\n   - Ejemplo detallado: clase Node con propiedades `value`, `next` inicialmente `null`.\\n3. Mutabilidad y seguridad de datos\\n   - Discute cuándo congelar (`Object.freeze`) el `value` o el nodo completo.\\n   - Buenas prácticas de nomenclatura y encapsulamiento (propiedades privadas con `#next`).\\n4. Extensión a estructuras no lineales\\n   - Muestra cómo la misma unidad puede albergar dos referencias (`left`, `right`) para futuros árboles binarios, enlazando con el epígrafe siguiente.\\n5. Manejo de referencias nulas\\n   - Importancia de comprobar `null` antes de acceder a métodos/propiedades.\\n   - Introduce el operador de encadenamiento opcional `?.`.\\n\\nElementos dinamizadores\\n• Tabla comparativa entre array y colección enlazada (operaciones básicas y coste estimado sin entrar aún en Big-O detallado).\\n• Diagrama mermaid simple para ilustrar tres nodos enlazados.\\n\\nEjemplo práctico (breve)\\n```js\\nclass Node {\\n  constructor(value) {\\n    this.value = value;\\n    this.next = null; // referencia a otro nodo o null\\n  }\\n}\\n```\\nPedir al lector que instancie tres nodos y los encadene manualmente en consola.\\n\\nCierre transicional\\n• Anuncia que, con la unidad básica definida, se profundizará en la gestión de colecciones enlazadas y sus operaciones fundamentales en el próximo apartado, evitando conclusiones prematuras.', 'Extensión orientativa: 900-1 200 palabras.\\n\\nIntroducción breve\\n• Conecta con la construcción de nodos y explica que ahora se agruparán bajo una estructura lineal dinámica.\\n\\nEstructura narrativa\\n1. Definición lógica de la colección\\n   - Rol del puntero `head` y, opcionalmente, `tail`.\\n   - Abstracción de la clase contenedora `LinkedList`.\\n2. Operaciones de inserción\\n   - Inserción al inicio (`unshift`): pasos, diagrama de antes/después.\\n   - Inserción al final (`push`) con y sin `tail` para O(1).\\n   - Inserción intermedia tras un nodo dado (`insertAfter`).\\n3. Operaciones de eliminación\\n   - Eliminación al inicio (`shift`).\\n   - Eliminación por valor: búsqueda + reajuste de enlaces.\\n   - Gestión de casos borde: lista vacía, único nodo, valor inexistente.\\n4. Recorrido y utilidades auxiliares\\n   - Método `find`, `size`, `toArray` para depuración.\\n   - Uso de iteradores ES6 (`[Symbol.iterator]`) para permitir for-of.\\n5. Manejo de memoria implícito en JS\\n   - Por qué no hay `free`, recolección de basura y referencias colgantes.\\n\\nCódigo guía (segmentado y comentado)\\n```js\\nclass LinkedList {\\n  constructor() {\\n    this.head = null;\\n    this.tail = null;\\n  }\\n  unshift(value) { /*...*/ }\\n  push(value) { /*...*/ }\\n  remove(value) { /*...*/ }\\n  // resto de métodos\\n}\\n```\\n\\nElemento visual\\n• Diagrama mermaid que represente los enlaces antes y después de ejecutar `unshift` y `push`.\\n\\nEjercicio propuesto (1 de los 3 del tema)\\n• \"Implementa un método `insertAt(index, value)` que mantenga O(n) en el peor caso y actualice `tail` cuando sea necesario. Valida índices inválidos lanzando `RangeError`.\"\\n\\nTransición\\n• Destaca que la estructura lineal, aunque flexible, puede ser ineficiente para ciertas búsquedas; se preparará el terreno para estructuras jerárquicas que optimizan el acceso.', 'Extensión orientativa: 1 000-1 300 palabras.\\n\\nPuerta de entrada\\n• Explica por qué una organización jerárquica reduce la profundidad de búsqueda en promedio frente a listas lineales.\\n\\nDesarrollo temático\\n1. Concepto y representación\\n   - Definición de árbol binario, raíz, hijos, hojas, altura.\\n   - Reutilización de la clase `Node` con referencias `left` y `right` (vistos en el primer apartado).\\n2. Clasificaciones básicas\\n   - Completo, perfecto, balanceado vs degenerado.\\n3. Construcción práctica\\n   - Clase `BinaryTree` con referencia a `root`.\\n   - Inserción simple tipo “binary search tree” (BST) aclarando que se profundizará en equilibrio en cursos avanzados.\\n4. Estrategias de recorrido\\n   - Profundidad primero: preorder, inorder, postorder (recursivo).\\n   - Anchura primero: nivel por nivel usando cola (array + `shift`).\\n   - ofrécete un patrón genérico para cambiar entre recursión e iteración con pila explícita.\\n5. Salidas útiles\\n   - Convertir el árbol a array según el recorrido elegido.\\n   - Visualización textual simple: sangrías o branch-diagram ASCII.\\n\\nCódigo ilustrativo\\n• Función `inOrder(node, visit)` recursiva con esquema.\\n• Implementación de `levelOrder()` que emplee una cola.\\n\\nElemento dinamizador\\n• Tabla que mapee cada tipo de recorrido a sus aplicaciones típicas (BST ordenado, cálculo de altura, serialización, etc.).\\n• Diagrama mermaid (máximo 7-8 nodos) que ilustre un árbol y el orden de visita usando numeración.\\n\\nEjercicio propuesto (2º del tema)\\n\"Construye un árbol de expresiones aritméticas a partir de una notación postfija y calcula su resultado empleando un recorrido postorder.\"\\n\\nConexión final\\n• Deja planteada la pregunta sobre qué coste tienen las operaciones cuando el árbol se desbalancea, enlazando así con el análisis de complejidad del último apartado.', 'Extensión orientativa: 900-1 200 palabras.\\n\\nIntroducción concisa\\n• Recordatorio de la notación Big-O vista en bloques anteriores y motivación de aplicarla ahora a las nuevas estructuras.\\n\\nEstructura de exposición\\n1. Parámetros de análisis\\n   - Tiempo: número de comparaciones/enlaces visitados.\\n   - Espacio: memoria adicional más allá de los datos almacenados.\\n2. Lista lineal enlazada\\n   - Busca, inserta al inicio, inserta al final con y sin puntero `tail`, eliminación por valor.\\n   - Sección en tabla: operación | mejor | promedio | peor | notas.\\n3. Árbol binario de búsqueda (sin balancear)\\n   - Inserción, búsqueda, eliminación.\\n   - Diferencia entre casos promedio O(log n) y peor O(n) cuando degenera.\\n4. Recorridos\\n   - Complejidad lineal O(n) para DFS y BFS y justificación.\\n5. Comparativa con arrays\\n   - Ventajas/desventajas según operación.\\n   - Pequeña discusión sobre constantes ocultas y locality of reference.\\n6. Herramientas empíricas\\n   - Uso de `console.time()` y paquetes npm como `benchmark` para medir.\\n   - Demostración mínima: generar n aleatorios, construir lista vs array y medir búsqueda.\\n\\nElemento visual\\n• Tabla síntesis de complejidades (lista vs árbol vs array).\\n\\nEjercicio final (3º del tema)\\n• “Implementa un script que genere 10⁵ números enteros, los almacene en array, lista y BST; mida el tiempo de búsqueda de un elemento presente y uno ausente, y reporte resultados en consola con formateo de tabla.”\\n\\nCierre global del tema\\n• Resume en ≤ 2 frases la relevancia de elegir la estructura adecuada y anticipa que, para colisiones y conjuntos, se explorarán tablas hash en el siguiente bloque sin profundizar en ello ahora.', '**Extensión orientativa**: 900-1 100 palabras.\\n\\n### Propósito general \\nGuiar al lector desde la necesidad de un acceso rápido a los datos hasta la comprensión de cómo una función de dispersión convierte claves de longitud arbitraria en posiciones de una estructura indexada (habitualmente un arreglo). Se debe enlazar con las estructuras lineales vistas en el tema anterior y preparar el terreno para el manejo de colisiones del siguiente epígrafe.\\n\\n### Esquema narrativo sugerido\\n1. **Situación‐problema introductoria (≈100 pal.)**  \\n   Plantee un ejemplo cotidiano –p.e. buscar un número de teléfono en una agenda digital– y relacione la dificultad de hacerlo con un recorrido secuencial. Conecte con el objetivo de reducir el tiempo de búsqueda a O(1).\\n2. **Definición y principios (≈200 pal.)**  \\n   • Concepto de \"hash\" como función total determinística.  \\n   • Propiedades deseables: uniformidad, rapidez de cálculo, capacidad de compresión, baja tasa de colisiones, insensibilidad a patrones.  \\n   • Diferencia entre funciones de dispersión y cifrado (mencionar solo para distinguir, sin profundizar en criptografía).\\n3. **Construcción paso a paso de una función simple (≈250 pal.)**  \\n   Describa una función basada en código ASCII y módulo sobre el tamaño de la tabla:\\n```js\\nconst hash = (str, size) => {\\n  let h = 0;\\n  for (let i = 0; i < str.length; i++) {\\n    h = (h + str.charCodeAt(i)) % size;\\n  }\\n  return h;\\n};\\n```\\n   Enfatice cómo la constante `size` enlaza con el arreglo subyacente y cómo cambia la dispersión al modificarla.\\n4. **Criterios de calidad y métricas (≈200 pal.)**  \\n   • Distribución uniforme vs. clustering.  \\n   • Relación entre _load factor_ (α) y rendimiento.  \\n   • Coste temporal esperado frente a coste en el peor caso.  \\n   Incluya una tabla comparativa de tres familias de funciones (simple modular, polinómica, cryptográfica) con columnas: complejidad, distribución típica, uso habitual.\\n5. **Visualización del proceso (≈50 pal.)**  \\n   Sugerir un diagrama `mermaid` que represente: `clave → función hash → índice → celda del arreglo`.\\n6. **Transición al siguiente epígrafe (≈50 pal.)**  \\n   Subraye que incluso con buenas funciones persisten las colisiones y anticipe las estrategias para resolverlas.\\n\\n### Recursos didácticos adicionales\\n• Inserte una pequeña tabla con valores de ejemplo (`clave`, `hash`, `índice`) para cinco cadenas.  \\n• No incluya ejercicios extensos aquí; resérvelos para cuando se disponga de las técnicas de colisión.', '**Extensión orientativa**: 1 000-1 100 palabras.\\n\\n### Meta didáctica \\nPresentar las razones por las que se producen colisiones y describir en detalle dos familias de estrategias para resolverlas, destacando ventajas, limitaciones y complejidad. Conectar con la implementación manual de un contenedor disperso y preparar al lector para comparar su trabajo con las colecciones nativas del lenguaje en la siguiente sección.\\n\\n### Orden sugerido de contenido\\n1. **Recordatorio de la problemática (≈100 pal.)**  \\n   Explique por qué las colisiones son inevitables cuando el dominio de las claves es mayor que el rango de índices.\\n2. **Encadenamiento externo (≈350 pal.)**  \\n   • Idea principal: cada posición de la tabla referencia una lista (tradicionalmente enlazada).  \\n   • Pseudocódigo de inserción, búsqueda y borrado. Indique cómo el coste promedio depende de α y de la distribución de la función.  \\n   • Ejemplo práctico: implementar un directorio de usuarios donde cada casilla contiene un `Array` de pares `[clave, valor]`.\\n3. **Abierto (open addressing) (≈350 pal.)**  \\n   • Explicar lineal, cuadrático y doble dispersión de forma progresiva.  \\n   • Mostrar pseudocódigo con `while` y manejo de marcas de borrado.  \\n   • Discuta el fenómeno de clustering primario y secundario.  \\n   • Ejemplo breve en JavaScript de lineal y de doble hash.\\n4. **Comparación tabular (≈100 pal.)**  \\n   Prepare una tabla con filas: consumo de memoria, facilidad de implementación, coste de redimensión, sensibilidad al factor de carga; columnas: encadenamiento, lineal, cuadrático, doble hash.\\n5. **Ejercicio guiado (≈150 pal.)**  \\n   Proponga implementar un buscador de palabras únicas de un texto usando encadenamiento, pidiendo medir tiempo de inserción para diferentes α.  \\n   Indique al redactor que incluya pequeñas secciones de código y muestre cómo calcular α.\\n6. **Cierre y enlace (≈50 pal.)**  \\n   Destaque que, a pesar de la complejidad, la mayoría de lenguajes proporcionan colecciones que abstraen estas decisiones, preparando la transición a Map/Set.\\n\\n### Elementos visuales\\n• Diagrama `mermaid` simple que muestre un recorrido lineal de índices al aplicar _linear probing_.\\n\\n### Observaciones\\nEvite concluir sobre la idoneidad final de cada técnica; el análisis comparativo más amplio se abordará cuando se estudien las implementaciones nativas y el rendimiento global.', '**Extensión orientativa**: 950-1 050 palabras.\\n\\n### Objetivo pedagógico \\nComprender cómo las colecciones embebidas del lenguaje aprovechan la dispersión interna, identificar cuándo utilizar cada una y contrastarlas con la implementación manual vista anteriormente. Se debe enfatizar la semántica, las particularidades de iteración y la gestión de memoria en las versiones _weak_.\\n\\n### Estructura recomendada\\n1. **Introducción contextual (≈80 pal.)**  \\n   Retome la implementación propia desarrollada antes y motive el uso de abstracciones estándar para mejorar productividad y seguridad.\\n2. **Map frente a Object (≈250 pal.)**  \\n   • Sintaxis de construcción, operaciones básicas (`set`, `get`, `has`, `delete`).  \\n   • Preservación de orden de inserción y tipos permitidos como clave.  \\n   • Comparación con un objeto literal mediante una tabla: claves posibles, iterabilidad, _prototype pollution_, rendimiento.\\n3. **Set y supresión de duplicados (≈200 pal.)**  \\n   • Operaciones fundamentales (`add`, `has`, `delete`).  \\n   • Ejemplo práctico: eliminación de duplicados en un arreglo de correos electrónicos.\\n4. **Colecciones débiles (≈250 pal.)**  \\n   • Motivación: permitir liberación automática de la memoria sin riesgo de _memory leaks_.  \\n   • Limitaciones: sólo admiten objetos como clave y carecen de método de iteración.  \\n   • Ejemplo: caché de resultados de una función pesada donde las entradas son objetos DOM.\\n5. **Ejercicio práctico (≈120 pal.)**  \\n   Diseñar un contador de frecuencia de palabras con `Map` y comparar, mediante `console.time`, con la versión basada en objeto.  \\n   Indicar qué métricas tomar y cómo interpretar las diferencias.\\n6. **Enlace al rendimiento global (≈50 pal.)**  \\n   Anticipe que, aunque estas estructuras proporcionan O(1) promedio, varían según la función de dispersión interna y el factor de carga; esto se analizará en la última sección.\\n\\n### Recursos complementarios\\n• Se sugiere incluir un cuadro con la API completa resumida (método, descripción breve).  \\n• No repetir la implementación de colisiones; referenciarla sólo para comparar abstracción vs. control.', '**Extensión orientativa**: 1 000-1 200 palabras.\\n\\n### Foco didáctico \\nAnalizar escenarios donde las estructuras de dispersión resultan ventajosas, relacionar su complejidad con la notación Big-O ya vista y enseñar cómo medir empíricamente su rendimiento en JavaScript. Este es el cierre del tema, por lo que puede contener recapitulación y mirada prospectiva, evitando generalizaciones más allá del alcance del curso.\\n\\n### Secuencia de contenido sugerida\\n1. **Panorama de aplicaciones (≈250 pal.)**  \\n   • Detección de duplicados en colecciones grandes.  \\n   • Implementación de caches memorizadas.  \\n   • Conjuntos de adyacencia en grafos (preámbulo útil para algoritmos recursivos del tema siguiente).  \\n   • Índices de bases de datos en memoria.\\n2. **Análisis teórico (≈250 pal.)**  \\n   • Recordar la notación O(1) promedio.  \\n   • Impacto del factor de carga y de la estrategia de colisión en el coste.  \\n   • Coste de redimensión (rehash) amortizado.\\n3. **Benchmarking en JavaScript (≈200 pal.)**  \\n   • Uso de `performance.now()` o `console.time`.  \\n   • Sugerir un esquema de medición repetida y promediada.  \\n   • Ejemplo de comparación entre búsqueda en `Array.includes` vs `Set.has` sobre 1 000 000 elementos.\\n4. **Estudio de caso guiado (≈200 pal.)**  \\n   Proponer la tarea de contar el número de palabras únicas en una novela (p.e. _Alicia en el país de las maravillas_).  \\n   Indicar pasos: lectura del archivo, tokenización, inserción en `Set`, reporte de tiempo y memoria usando `process.memoryUsage()`.\\n5. **Tabla de complejidad resumida (≈50 pal.)**  \\n   Elabore una matriz con operaciones (`insertar`, `buscar`, `eliminar`) y estructuras (`Array`, `Binary Search Tree`, `Hash Table`) mostrando coste promedio y peor caso.\\n6. **Reflexión final y conexión futura (≈50 pal.)**  \\n   Enfatice que muchas optimizaciones adicionales (p.e. _cuckoo hashing_, tablas perfectas) existen, pero exceden el nivel introductorio. Concluya apuntando a cómo la estructura permitirá algoritmos más sofisticados (búsqueda y ordenación recursiva) en el próximo tema.\\n\\n### Elementos de apoyo\\n• Diagrama `mermaid` opcional mostrando evolución del factor de carga al hacer _resize_.  \\n• Invitación a que el redactor incluya gráficos simples (ASCII) de tiempos obtenidos, si lo considera útil.\\n\\n### Precauciones\\nEvitar convertir esta sección en un tratado exhaustivo sobre rendimiento; mantenga el foco en los conceptos y técnicas que el estudiante puede experimentar de manera autónoma con un equipo doméstico.', '**Extensión aproximada:** 900–1 200 palabras  \\n\\n### Objetivos narrativos\\n1. Abrir el tema introduciendo la idea de «pensar en términos de sub-problemas» y cómo ello conduce naturalmente al uso de llamadas recursivas.\\n2. Conectar con la experiencia previa del estudiantado (uso de funciones y complejidad Big-O vistos en temas anteriores) para anclar la nueva técnica.\\n3. Presentar la recursión como patrón de diseño; enfatizar la definición de un caso base sólido y el progreso hacia él.\\n4. Describir el impacto en la pila de llamadas y el consumo de memoria, enlazándolo con la noción de complejidad espacial.\\n\\n### Estructura sugerida\\n1. **Introducción breve (≈100 palabras)**  \\n   • Contextualizar la recursión como técnica clave para algoritmos de búsqueda/ordenación que se estudiarán después.  \\n2. **Concepto de recursión (≈200 palabras)**  \\n   • Definición formal y analogía cotidiana (muñecas rusas, espejos).  \\n   • Vinculación con la definición matemática recursiva (factorial).  \\n3. **Diseño del caso base (≈250 palabras)**  \\n   • Reglas: debe ser alcanzable, excluir llamadas adicionales, devolver resultado directo.  \\n   • Consecuencias de un caso base incorrecto (bucles infinitos, desbordamiento de pila).  \\n4. **Progreso hacia el caso base (≈200 palabras)**  \\n   • Patrón de decrecimiento (n-1), partición de datos, o desplazamiento del índice.  \\n   • Ejemplo guiado: suma de un arreglo de números.  \\n5. **Trazado de la pila de llamadas (≈150 palabras)**  \\n   • Ilustrar con mermaid un árbol de ejecución reducido (factorial de 4).  \\n6. **Complejidad temporal y espacial (≈150 palabras)**  \\n   • Explicar O(n) para factorial y suma, O(2ⁿ) para Fibonacci ingenuo.  \\n   • Relacionar con el costo en memoria por profundidad.  \\n7. **Ventajas, desventajas y cuándo evitarla (≈100 palabras)**  \\n   • Claridad declarativa vs costo de pila.  \\n   • Mención breve de optimización de cola (no profundizar todavía).  \\n\\n### Elementos dinamizadores\\n• **Tabla comparativa** Recursión vs iteración: claridad, líneas de código, uso de memoria, facilidad de paralelización.  \\n• **Ejercicio guiado** (primer ejercicio del tema): Implementar `power(base, exp)` recursivo y luego iterativo, midiendo tiempo con `console.time()` y contando profundidad.\\n\\n### Transiciones\\n• Cerrar recordando que la recursión es la base de algoritmos de búsqueda y de ordenación que vendrán a continuación, evitando conclusiones definitivas.', '**Extensión aproximada:** 1 000–1 300 palabras  \\n\\n### Objetivos narrativos\\n1. Mostrar la necesidad de algoritmos de búsqueda para localizar información en colecciones vistas en temas anteriores (arrays, listas, conjuntos).\\n2. Comparar enfoques lineal vs binario y vincular la elección con la organización previa de los datos.\\n3. Practicar la recursión recién aprendida en la implementación binaria.\\n4. Introducir al alumnado en la medición empírica de rendimiento con pequeños benchmarks.\\n\\n### Estructura sugerida\\n1. **Planteamiento del problema (≈120 palabras)**  \\n   • Ejemplo cotidiano: buscar un nombre en una lista telefónica desordenada vs ordenada.  \\n2. **Búsqueda lineal (≈300 palabras)**  \\n   • Descripción, pseudocódigo e implementación JS iterativa.  \\n   • Análisis Big-O (temporal y espacial).  \\n   • Mejores y peores casos, referencia al early-exit.  \\n3. **Búsqueda binaria (≈350 palabras)**  \\n   • Requisito de lista ordenada y justificación conceptual (divide-and-conquer).  \\n   • Implementación iterativa y recursiva, resaltando la reutilización de los conocimientos del epígrafe anterior.  \\n   • Análisis de complejidad O(log n) y comparación gráfica simplificada.  \\n4. **Comparación y elección de algoritmo (≈150 palabras)**  \\n   • Escenario de datos pequeños, estructuras dinámicas, coste de ordenar, etc.  \\n5. **Benchmark práctico (≈250 palabras)**  \\n   • Script Node.js que genera arreglos de tamaños crecientes, busca un elemento aleatorio con ambos métodos y muestra el tiempo medio.  \\n   • Instrucciones sobre cómo interpretar los resultados.\\n\\n### Elementos dinamizadores\\n• **Tabla resumen** con criterios: requisito de orden, complejidad temporal promedio/peor, complejidad espacial, implementación recursiva viable.  \\n• **Ejercicio práctico** (segundo del tema): dar un arreglo de 1 000 nombres, medir cuántas comparaciones realiza cada algoritmo para encontrar uno inexistente.\\n\\n### Transiciones\\n• Conectar con el siguiente epígrafe adelantando que el rendimiento de algoritmos de ordenación influirá en la viabilidad de aplicar búsqueda binaria en datos desordenados.', '**Extensión aproximada:** 1 100–1 400 palabras  \\n\\n### Objetivos narrativos\\n1. Presentar la necesidad de ordenar como requisito previo para la búsqueda binaria y para otras aplicaciones.\\n2. Describir y contrastar tres algoritmos de comparación simples, preparando el terreno para divide-and-conquer más avanzados.\\n3. Mostrar la relación entre la estructura de control (bucles anidados) y la complejidad temporal.\\n\\n### Estructura sugerida\\n1. **Motivación (≈120 palabras)**  \\n   • Ejemplo: ordenar calificaciones antes de calcular percentiles.  \\n2. **Bubble Sort (≈300 palabras)**  \\n   • Idea del paso burbuja, pseudocódigo, implementación JS con optimización de bandera `swapped`.  \\n   • Análisis O(n²) y estabilidad.  \\n3. **Selection Sort (≈250 palabras)**  \\n   • Concepto de seleccionar mínimo, pseudocódigo e implementación.  \\n   • Comparación con Bubble en número de swaps y estabilidad.  \\n4. **Insertion Sort (≈250 palabras)**  \\n   • Analogía con ordenar cartas, implementación que desplaza elementos.  \\n   • Mejor caso O(n) y por qué es útil para arreglos casi ordenados.  \\n5. **Comparativa global (≈150 palabras)**  \\n   • Tabla con complejidad temporal (mejor/promedio/peor), estabilidad, adaptabilidad, memoria extra.  \\n6. **Ejemplo paso a paso (≈200 palabras)**  \\n   • Arreglo `[8, 5, 2, 9, 5]` mostrado en tabla de etapas para cada algoritmo (una fila por iteración principal).  \\n\\n### Elementos dinamizadores\\n• **Mermaid** diagrama sencillo que ilustre el flujo de Insertion Sort.  \\n• **Ejercicio práctico** (tercero del tema): Crear una función que detecte automáticamente si el arreglo está “casi ordenado”; usar Insertion si es así, de lo contrario Bubble.\\n\\n### Transiciones\\n• Enlazar con el próximo epígrafe señalando que algorítmicamente se puede mejorar la complejidad O(n²) mediante estrategias divide-and-conquer recursivas (QuickSort, MergeSort).', '**Extensión aproximada:** 1 200–1 500 palabras  \\n\\n### Objetivos narrativos\\n1. Consolidar el patrón divide-and-conquer introducido tangencialmente en la búsqueda binaria.\\n2. Profundizar en QuickSort y MergeSort como versiones eficientes que aprovechan la recursión, comparando su rendimiento frente a los métodos O(n²).\\n3. Introducir criterios de eficiencia práctica: elección de pivote, memoria adicional, estabilidad.\\n\\n### Estructura sugerida\\n1. **Divide-and-Conquer en contexto (≈150 palabras)**  \\n   • Descomposición, resolución recursiva y combinación de sub-problemas.  \\n2. **MergeSort (≈450 palabras)**  \\n   • División hasta sub-arreglos de tamaño 1, proceso de mezcla ordenada.  \\n   • Implementación recursiva JS con función `merge(left, right)`.  \\n   • Complejidad O(n log n) y coste espacial O(n).  \\n   • Estabilidad y aplicaciones (ordenar por múltiples claves).  \\n3. **QuickSort (≈450 palabras)**  \\n   • Selección de pivote, particionamiento in-place con índices `i`, `j`.  \\n   • Estrategias de pivote (primero, último, aleatorio, mediana-de-tres).  \\n   • Peor caso O(n²) y medidas de mitigación.  \\n   • Complejidad promedio O(n log n) y ventajas de localidad de caché.  \\n4. **Comparativa detallada (≈250 palabras)**  \\n   • Tabla: tiempo promedio, peor caso, uso de memoria, estabilidad, idoneidad para stream de datos.  \\n   • Discusión de implementación interna de `Array.prototype.sort()` en V8 (solo mención conceptual).  \\n5. **Visualización de la recursión (≈200 palabras)**  \\n   • Mermaid de árbol de llamadas de QuickSort con pivote central, tamaño 8.  \\n   • Señalar profundidad máxima esperada.  \\n6. **Consideraciones prácticas (≈150 palabras)**  \\n   • Umbral híbrido: usar Insertion Sort para sub-arreglos pequeños.  \\n   • Manejo de arreglos con claves repetidas.  \\n\\n### Elementos dinamizadores\\n• **Ejercicio integrador:**  \\n  1. Implementar ambos algoritmos.  \\n  2. Desplegar un benchmark que comparelos con los algoritmos sencillos sobre arreglos de 1 000 a 1 000 000 elementos aleatorios y casi ordenados.  \\n  3. Generar un reporte en consola con tiempos y memoria usando `process.memoryUsage()`.  \\n\\n### Cierre del tema\\n• Conducir al epígrafe final del bloque temático (“Calidad de software, pruebas y control de versiones”) sugiriendo que las implementaciones desarrolladas podrán convertirse en unidades de prueba y ejemplos para profiling y refactorización, sin profundizar más.', '**Extensión orientativa: 900-1 200 palabras**\\n\\n1. Breve contextualización (1-2 párrafos)\\n   * Empieza enlazando con los algoritmos y estructuras ya estudiados: subraya que su correcta ejecución requiere visibilidad interna del estado del programa.\\n   * Introduce el concepto de *debugging* como disciplina sistemática para localizar y solventar defectos antes de pasar al testeo formal.\\n\\n2. Panorama general de las herramientas\\n   * Presenta, sin titular la sección, las dos piezas clave: Chrome/Edge DevTools para aplicaciones que corren en navegador y `node --inspect` + Node Inspector para programas de línea de comandos.\\n   * Incluye una tabla simple comparando los ámbitos más comunes (entorno, punto de entrada, modo de invocación, principales ventajas).\\n\\n3. Flujo de trabajo con DevTools (≈50 % del espacio)\\n   * Demuestra conexión paso a paso:\\n     1. Apertura de panel *Sources* y panel *Console* enlazados.\\n     2. **Breakpoints**: clic en el *gutter*, breakpoints condicionales (incluye breve sintaxis de condición booleana) y *XHR/fetch breakpoints*.\\n     3. Controles de ejecución: *step over*, *step into*, *step out* y *resume*; explica cuándo utilizar cada uno.\\n     4. Inspector de *Scope* y *Closure*: enlaza con el tema de alcance léxico visto anteriormente.\\n     5. *Watch expressions* y *live editing*; menciona riesgos de modificar el estado en caliente.\\n   * Propón un pequeño ejercicio práctico: depurar un algoritmo de ordenación que entra en bucle infinito cuando la lista contiene valores `undefined`.\\n\\n4. Flujo de trabajo con Node Inspector (≈30 % del espacio)\\n   * Compara la activación con bandera `--inspect` y `--inspect-brk`.\\n   * Describe la conexión al *DevTools* remoto (chrome://inspect).\\n   * Usa un ejemplo basado en la implementación de una lista enlazada; invita a observar la pila de llamadas mientras se inserta un nodo.\\n   * Introduce la inspección de *heap snapshots* y *memory leaks* de forma ligera, sin profundizar en profiling avanzado (eso se reservaría para cursos posteriores).\\n\\n5. Estrategias y mejores prácticas transversales\\n   * Anima a adoptar la técnica *printf debugging* con `console.log` sólo como primera aproximación; refuerza la necesidad de transicionar pronto a breakpoints.\\n   * Presenta la idea de *logging de nivel* (info, warn, error) para registrar contexto sin frenar la ejecución.\\n   * Inserta una tabla mínima con patrones de mensaje de log (timestamp, nivel, componente, mensaje).\\n\\n6. Conexión con el siguiente epígrafe\\n   * Concluye resaltando que la depuración garantiza que el código que pasará a la fase de pruebas unitarias tenga defectos reducidos, preparando mentalmente al lector para la automatización con Jest.\\n\\n> No cierres el tema completo: la conclusión definitiva llegará en la última sección. Guarda tono de transición.', '**Extensión orientativa: 1 000-1 400 palabras**\\n\\n1. Apertura motivacional breve\\n   * Conecta con la depuración manual vista previamente y plantea la necesidad de una red de seguridad automatizada que evite regresiones.\\n\\n2. Delimitación de conceptos\\n   * Define *pruebas unitarias* frente a *pruebas de integración* usando un cuadro comparativo (unidad de código bajo prueba, dependencias permitidas, velocidad esperada, fallos típicos detectados).\\n   * Sitúa Jest como *test runner* y *assertion library* dominante en el ecosistema JavaScript.\\n\\n3. Instalación mínima y estructura de proyecto\\n   * Comando `npm install --save-dev jest` y adición de script `\"test\": \"jest\"` en `package.json`.\\n   * Recomienda estructura `src/` y `tests/` o convención `*.test.js` junto al módulo, justificando ventajas.\\n\\n4. Anatomía de un archivo de prueba unitaria\\n   * Explica las funciones `describe`, `test`/`it` y los *matchers* (`toBe`, `toEqual`, `toThrow`, etc.).\\n   * Proporciona un ejemplo concreto que verifique la función `reduce` implementada en el tema de arrays: muestra tres casos, uno exitoso, uno con valores vacíos y otro que lanza error cuando no se pasa callback.\\n   * Añade un diagrama de flujo simple (mermaid) que muestre la secuencia *Ejecutar test runner → Cargar módulo → Ejecutar casos → Reportar resultado*.\\n\\n5. Configuración avanzada para pruebas de integración\\n   * Introduce `jest.mock` para aislar dependencias (p.ej. simular acceso a tabla hash).\\n   * Describe uso de `beforeAll`, `beforeEach`, `afterEach`, `afterAll` para gestionar estado.\\n   * Incluye un pequeño ejemplo que valide la interacción entre un servicio de búsqueda binaria y un repositorio de datos simulado.\\n\\n6. Cobertura y feedback\\n   * Explica el flag `--coverage`, significado de *statements*, *branches*, *functions*, *lines*.\\n   * Menciona criterios pragmáticos: cobertura alta es deseable pero no sustituye el análisis de calidad.\\n\\n7. Integración continua (intro ligera)\\n   * Muestra, en menos de 15 líneas, un archivo YAML de GitHub Actions que instale dependencias y ejecute Jest. No profundices en CI compleja; esto servirá de puente hacia el epígrafe final de control de versiones.\\n\\n8. Actividad práctica sugerida\\n   * Redacta instrucciones para que el estudiante escriba un test de integración que compruebe un algoritmo `mergeSort` sobre grandes volúmenes, midiendo tiempo con `performance.now()`, y fallando si supera un umbral de 100 ms.\\n\\n9. Conexión con el siguiente epígrafe\\n   * Cierra destacando que las pruebas revelan fallos, pero hay que saber manejarlos de forma controlada dentro del código, preparando la transición al tratamiento de excepciones.\\n\\n> No elabores conclusiones globales aún; la reflexión final se reservará para la última sección.', '**Extensión orientativa: 900-1 200 palabras**\\n\\n1. Introducción situacional\\n   * Enlaza con la salida de errores observada en las pruebas: convierte los fallos en oportunidades para mejorar robustez mediante gestión estructurada de excepciones.\\n\\n2. Tipología de errores en JavaScript\\n   * Distingue *errores de sintaxis* (capturados en parseo), *errores de tiempo de ejecución* y *errores lógicos*.\\n   * Presenta la jerarquía estándar (`Error`, `TypeError`, `ReferenceError`, etc.) y la posibilidad de extenderla.\\n\\n3. Mecanismo `try…catch…finally`\\n   * Explica flujo interno con un diagrama mermaid de tres nodos: *try block* → *(error?)* → *catch* → *finally*.\\n   * Ejemplifica con la lectura de un archivo JSON usando API `fs.promises` (ejemplo breve) y muestra cómo capturar fallos de parseo.\\n\\n4. Lanzamiento de errores personalizados\\n   * Sintaxis `throw new CustomError(msg)`; muestra cómo añadir propiedades como `code` o `status`.\\n   * Incorpora un ejemplo asociado a la estructura de árbol binario: lanzar `NodeExistsError` al intentar insertar un valor duplicado en un BST.\\n\\n5. Propagación de errores en operaciones asíncronas\\n   * Explica el patrón *callback → err-first*, luego evolución a Promises y finalmente `async/await`.\\n   * Incluye fragmento donde una función `async` captura errores con `try…catch`, vuelve a lanzar (`throw`) para que la capa superior decida, conectando con integración de Jest mediante `expect(asyncFn()).rejects.toThrow()`.\\n\\n6. Registro y monitoreo\\n   * Vuelve sobre la tabla de niveles de log vista en la primera sección y muestra cómo incluir *stack traces*.\\n   * Introduce, de forma ligera, herramientas externas (Sentry, LogRocket) sin entrar en configuración detallada.\\n\\n7. Estrategias de diseño defensivo\\n   * Validación temprana de argumentos (type guards, coerción), *assertions* internas.\\n   * Menciona que un control de versiones eficaz permite rastrear cuándo apareció un manejo de error deficiente, preparando el enlace con Git.\\n\\n8. Mini-ejercicio\\n   * Plantea al estudiante encapsular una función de búsqueda binaria para que lance `RangeError` cuando el arreglo no esté ordenado; proporcionar test Jest que confirme la excepción.\\n\\n9. Transición\\n   * Finaliza destacando que una codificación segura, probada y con errores controlados adquiere su máximo valor cuando se versiona y comparte correctamente, dando paso a la colaboración con Git y GitHub.', '**Extensión orientativa: 1 000-1 300 palabras**\\n\\n1. Introducción conectiva\\n   * Retoma los conceptos de pruebas y control de errores como parte del *ciclo de calidad*, subrayando que el control de versiones es el pegamento que une codebase, historial de bugs y colaboración.\\n\\n2. Fundamentos de los sistemas de control de versiones (VCS)\\n   * Explica brevemente el modelo *snapshot* de Git versus el modelo *delta* de sistemas clásicos.\\n   * Define repositorio local, remoto y staging area.\\n\\n3. Flujo de trabajo básico individual\\n   * Comandos esenciales (`git init`, `clone`, `add`, `commit`, `status`, `log`), con tabla resumen que incluya función, alias habitual y estado del proyecto afectado.\\n   * Ejemplo paso a paso: versionar el proyecto de algoritmos, cometarios de mensaje semántico (`feat: add recursion example`).\\n\\n4. Branching y estrategias de fusión\\n   * Introduce concepto de ramas, motivo de su existencia (desacoplar características).\\n   * Diferencia *merge* vs *rebase* con diagrama simple (mermaid) con nodos *master/main*, *feature-x*.\\n   * Menciona ramas de corta vida y políticas como *Git Flow* o *GitHub Flow* sin profundizar.\\n\\n5. Colaboración mediante GitHub\\n   * Creación de *fork*, *remote add origin/upstream*, *pull requests* (PR).\\n   * Explica el ciclo PR → revisión de código → integración en `main` → despliegue; enlaza con acciones automáticas que corren Jest como *status checks*.\\n\\n6. Resolución de conflictos\\n   * Proceso `git pull --rebase` y conflicto típico en archivo `arrayMethods.js`; muestra ejemplo de marcas `<<<<<<<`, `=======`, `>>>>>>>` y cómo elegir la versión correcta.\\n   * Anima a ejecutar pruebas nuevamente para garantizar que la resolución no ha roto funcionalidad.\\n\\n7. Versionado semántico y tags de lanzamiento\\n   * Presenta esquema MAJOR.MINOR.PATCH; liga incremento de versión a historial de cambios y compatibilidad.\\n   * Demuestra `git tag v1.0.0` y subida con `git push --tags`.\\n\\n8. Actividad integradora\\n   * Instruye para crear un repositorio público con el algoritmo `mergeSort`, configurar una acción de CI que ejecute las pruebas del epígrafe 2 y bloquee la PR si fallan.\\n   * Incluye paso de abrir un *issue* describiendo un bug forzado (lanzar excepción) y referenciarlo en el commit de arreglado (`fix: handle empty array closes #1`).\\n\\n9. Conclusión del tema (ahora sí)\\n   * Realiza síntesis de cómo depuración, pruebas, manejo de errores y control de versiones conforman un flujo de desarrollo profesional.\\n   * Invita a la práctica continua: cada nueva estructura de datos o algoritmo introducido en la asignatura debe seguir este pipeline.\\n\\n> Al cerrar, evita introducir contenidos de cursos avanzados (release pipelines, microservicios, etc.); mantén el foco en fundamentos y colaboración estudiantil.'] and epigraphs are: [Epigrafe(name='Conceptos de algoritmo y pseudocódigo', id_tema=123, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 42076), id=390, position=1, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 42167)), Epigrafe(name='Historia y evolución de JavaScript', id_tema=123, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 44806), id=391, position=2, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 44892)), Epigrafe(name='Instalación del entorno de desarrollo (Node.js, VS Code)', id_tema=123, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 45621), id=392, position=3, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 45821)), Epigrafe(name='Primer script: \"Hola, mundo\" en consola y navegador', id_tema=123, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 48597), id=393, position=4, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 48687)), Epigrafe(name='Declaración de variables (var, let, const)', id_tema=124, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 49898), id=394, position=1, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 49968)), Epigrafe(name='Tipos primitivos y coerción de datos', id_tema=124, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 50425), id=395, position=2, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 50490)), Epigrafe(name='Operadores aritméticos, lógicos y comparativos', id_tema=124, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 51000), id=396, position=3, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 51156)), Epigrafe(name='Entrada y salida básica con prompt, alert y console.log', id_tema=124, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 51727), id=397, position=4, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 51956)), Epigrafe(name='Estructuras de decisión if, else y switch', id_tema=125, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 53178), id=398, position=1, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 53338)), Epigrafe(name='Bucles for, while y do-while', id_tema=125, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 53762), id=399, position=2, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 53902)), Epigrafe(name='Control de bucle (break, continue) y patrones de iteración', id_tema=125, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 54325), id=400, position=3, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 54458)), Epigrafe(name='Buenas prácticas para evitar bucles infinitos', id_tema=125, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 54897), id=401, position=4, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 55060)), Epigrafe(name='Declaración y expresión de funciones', id_tema=126, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 56665), id=402, position=1, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 56830)), Epigrafe(name='Parámetros, retorno y funciones flecha', id_tema=126, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 57361), id=403, position=2, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 57476)), Epigrafe(name='Closures y alcance léxico', id_tema=126, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 59125), id=404, position=3, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 59190)), Epigrafe(name='Módulos ES6: import y export', id_tema=126, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 59574), id=405, position=4, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 59637)), Epigrafe(name='Concepto de eficiencia temporal y espacial', id_tema=127, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 60547), id=406, position=1, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 60632)), Epigrafe(name='Reglas para determinar la notación Big-O', id_tema=127, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 61046), id=407, position=2, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 61131)), Epigrafe(name='Casos mejor, promedio y peor', id_tema=127, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 61541), id=408, position=3, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 61628)), Epigrafe(name='Herramientas de benchmarking en JavaScript', id_tema=127, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 62037), id=409, position=4, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 62123)), Epigrafe(name='Creación y mutación de arreglos', id_tema=128, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 62995), id=410, position=1, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 63078)), Epigrafe(name='Métodos iterativos forEach, map, filter, reduce', id_tema=128, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 63453), id=411, position=2, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 63547)), Epigrafe(name='Pilas y colas implementadas con arrays', id_tema=128, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 64199), id=412, position=3, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 64296)), Epigrafe(name='Declaración y recorrido de matrices multidimensionales', id_tema=128, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 64777), id=413, position=4, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 64874)), Epigrafe(name='Implementación de nodos y punteros en JS', id_tema=129, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 65809), id=414, position=1, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 65901)), Epigrafe(name='Lista enlazada simple: inserción y eliminación', id_tema=129, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 66365), id=415, position=2, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 66494)), Epigrafe(name='Estructura de árbol binario y tipos de recorrido', id_tema=129, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 66896), id=416, position=3, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 66987)), Epigrafe(name='Análisis de complejidad de listas y árboles', id_tema=129, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 68646), id=417, position=4, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 68724)), Epigrafe(name='Fundamentos de hashing y funciones hash', id_tema=130, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 70053), id=418, position=1, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 70136)), Epigrafe(name='Resolución de colisiones (encadenamiento y open addressing)', id_tema=130, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 70527), id=419, position=2, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 70611)), Epigrafe(name='Implementación de Map, Set, WeakMap y WeakSet', id_tema=130, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 71006), id=420, position=3, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 71090)), Epigrafe(name='Aplicaciones y análisis de rendimiento', id_tema=130, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 71483), id=421, position=4, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 71569)), Epigrafe(name='Principios de recursión y diseño de casos base', id_tema=131, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 72643), id=422, position=1, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 72749)), Epigrafe(name='Búsqueda lineal y búsqueda binaria', id_tema=131, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 73113), id=423, position=2, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 73202)), Epigrafe(name='Algoritmos de ordenación sencillos (bubble, selection, insertion)', id_tema=131, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 73569), id=424, position=3, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 73658)), Epigrafe(name='QuickSort y MergeSort: enfoque divide-and-conquer', id_tema=131, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 74043), id=425, position=4, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 74129)), Epigrafe(name='Depuración con DevTools y Node Inspector', id_tema=132, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 74936), id=426, position=1, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 75021)), Epigrafe(name='Pruebas unitarias y de integración con Jest', id_tema=132, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 75377), id=427, position=2, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 75465)), Epigrafe(name='Manejo de errores y excepciones en JavaScript', id_tema=132, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 75836), id=428, position=3, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 75927)), Epigrafe(name='Uso de Git y GitHub en flujo colaborativo', id_tema=132, summary=None, created_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 76282), id=429, position=4, keywords=[], created_by='ia_gen_user', updated_by=None, updated_at=datetime.datetime(2025, 6, 30, 13, 41, 3, 76381))]\n"]}], "source": ["from src.api.workflows.topics import GenerateAllDidacticInstructionsRequest\n", "model_info = ModelInfo(provider=\"openai\", name = \"o3\", max_tokens=30000, reasoning_effort=\"high\")\n", "request = GenerateAllDidacticInstructionsRequest(order_id = ORDER_ID,model_info=model_info)\n", "instructions_responses = await DependencyContainer.get_generate_all_didactic_instructions_workflow().execute(request)"]}, {"cell_type": "markdown", "id": "539287ff", "metadata": {}, "source": ["Objet<PERSON>, tener unas 700-1500 palabras por epígrafe y entre 3000-6000 palabras por tema.\n", "\n", "Es decir objetivo total rango palabras aprox: \n", "\n", "palabras_objetivo = n_epigrafes * 700-1500 palabras\n", "\n", "Ej:\n", "4 epígrafes \n", "palabras_objetivo = 2800-6000\n", "Ahora mismo se tiende a pasar basatante en principio por la generación de planes. Medir esos casos con pydantic evals.\n", "Evaluar métodos que consigan que el contenido generado acabe en ese rango.\n", "\n", "Probar también mejorar reranker y modelo de embeddings. \n", "\n", "Comparar enfoque actual vs generar directamente de instrucciones didácticas y a ver cómo es el resultado del contenido. \n", "\n", "Probablemente los modelos son suficientemenete buenos ahora para generar un epígrafe entero. Al menos sonnet-4 con thinking.\n", "Y tema entero está por ver.\n", "\n", "Adaptar prompt para que coja bien las fuentes originales al generar y que no use otras que se mencionen.\n", "\n", "Ver si mejora al dejarle buscar mientras genera tema o epígrafe.\n", "\n", "Evaluar de forma sistemática."]}, {"cell_type": "markdown", "id": "a7cd8b70", "metadata": {}, "source": ["## Evaluando generar contenido directo desde instrucciones\n", "\n", "De momento ignorando la búsqueda de fuentes simplemente para ver qué tal genera el contenido."]}, {"cell_type": "markdown", "id": "67180c35", "metadata": {}, "source": ["### <PERSON><PERSON> epí<PERSON>s"]}, {"cell_type": "markdown", "id": "80d88637", "metadata": {}, "source": ["User instructor para esto por simplificar"]}, {"cell_type": "code", "execution_count": 16, "id": "7f28f282", "metadata": {}, "outputs": [], "source": ["llm_manager = DependencyContainer.get_llm_manager()"]}, {"cell_type": "markdown", "id": "256e60bf", "metadata": {}, "source": ["{{asignatura}} (La primera parte es la asignatura y la segunda el título dentro del cual se encuentra)\n", "\n", "De momento fuentes ha sido omitido\n", "\n", "\n", "<fuentes_relacionadas>\n", "Uso de fuentes:\n", "\n", "-Se te proporcionarán segmentos de texto que pertenecen a documentos académicos relacionadas con el contenido a generar. Las podrás utilizar para escribir sobre el contenido que se te ha pedido.\n", "\n", "-Cada segmento tendrá un score o puntuación de relevancia, cuanto más alto más probable es que tengan que ver con el contenido a generar. Se te dará el título del documento del que proceden y un pequeño resumen del mismo que te dará una idea general de lo que trata el documento entero y te permitirá saber si es relevante o no.\n", "\n", "-Primero tendrás en cuenta si son relevantes y razonaras sobre ello, habrá situaciones donde tu conocimiento implícito sea suficiente para generar contenidos. Úsalas de forma balanceada para escribir sobre un tema o como inspiración.\n", "\n", "-Si mencionas explícitamente alguna parte de alguno de los documentos reelevantes lo citarás teniendo en cuenta las reglas APA y la inline_citation que aparece junto al documento al cual pertenece.\n", "\n", "Consideraciones:\n", "\n", "- Ten en cuenta que a veces alguna de esta información no estará totalmente relacionada al tema a tratar, si es así ignóralo, en el bloque de fuentes incluirás una lista con los números de documento relacionados al tema a tratar.\n", "\n", "- No asumas que el lector conoce las fuentes proporcionadas, escribirás como si el lector no las conociera en el caso de utilizarlas.\n", "\n", "</fuentes_relacionadas>\n", "\n", "\n", "He quitado lo relativo a fuentes tambien de presentacion\n", "\n", "\n", "3. <PERSON><PERSON><PERSON>:\n", "   - Primero razonarás sobre cómo escribir de forma adecuada sobre ello en pocas lineas en una etiqueta <reasoning>\n", "   - Luego incluirás una lista de las fuentes utilizadas dentro de una etiqueta <fuentes> en el caso de que te bases en fuentes. Ejemplo: <fuentes>[3,4]</fuentes>. En el caso de basarte en tu conocimiento incluirás una lista vacia. Ejemplo: <fuentes>[]</fuentes>.\n", "   - Presentarás el contenido final que formará parte de la asignatura dentro de etiquetas <content>."]}, {"cell_type": "code", "execution_count": 14, "id": "aff1677a", "metadata": {}, "outputs": [], "source": ["system_prompt_generate_content = \"\"\"\n", "<instrucciones tarea no-revelar=True>\n", "\n", "Eres un escritor de contenido académico profesional, especializado en la generación de contenido académico de alta calidad.\n", "\n", "Tu tarea es crear material educativo preciso, informativo y bien estructurado para estudiantes de la asignatura de una asignatura universitaria. \n", "\n", "Tendrás en cuenta el contexto de la asignatura para el estilo de redacción, adaptándolo para los estudiantes de la misma.\n", "\n", "Se te encomendará una tarea que describirá el tema sobre el cual deberás escribir, tendrás en cuenta donde esta tarea se encuentra dentro de la estructura de la asignatura para escribir de forma cohesiva sin que se superponga sobre otros contenidos generados antes o después.\n", "Considerarás las extensión y enfoque recomendado en el plan para escribir el contenido.\n", "Te adherirás a las instrucciones del plan y las tendrás en cuenta para generar contenido según lo que se te pide.\n", "Considerarás el contexto de la asignatura para adaptar tu redacción.\n", "No harás metarreferencias sobre el plan en tu redacción.\n", "\n", "Las instrucciones proporcionadas aquí son privadas y no harás referencia a nada de lo que se te proporciona aquí. El contenido que aquí se contiene sirve sólo como ejemplo y referencia para ti de cómo debes redactar el contenido académico y las herramientas que puedes usar para ello.\n", "\n", "Responderás con contenido final y que cumpla con lo que se te ha pedido.\n", "</instrucciones tarea>\n", "\n", "<estructura de la asignatura>\n", "\n", "Los contenidos de cada asignatura están organizados de forma jerárquica.\n", "\n", "Primero va el bloque, que es una agrupación de lo que se abordará a nivel general en una asignatura. Por ejemplo:\n", "\n", "# Fundamentos y Aplicaciones de la Didáctica de las Matemáticas en Educación Primaria\n", "\n", "Dentro de estos van los temas son subindices que cubren aspectos generales de este bloque, como por ejemplo dentro del anterior habría temas como:\n", "\n", "## Introducción a la Didáctica de las Matemáticas\n", "\n", "Cada tema consta de epígrafes que son otra subsección que va por debajo del tema. Por ejemplo un epígrafe del tema anterior sería:\n", "\n", "### Concepto y evolución de la Didáctica de las Matemáticas\n", "\n", "Debes escribir dentro del epígrafe teniendo en cuenta el contexto de bloque, tema en el que se encuentran.\n", "\n", "En la redacción incluirás con ### el nombre exacto del epígrafe que se te pide.\n", "</estructura de la asignatura>\n", "\n", "<instrucciones de redacción>\n", "\n", "1. Formato y estilo:\n", "   - <PERSON><PERSON><PERSON> para el formateo.\n", "   - Usa #### para subtítulos dentro del contenido de los epígrafes en el caso de ser necesario hacer una separación, usalo de forma equilibrada y considera si el párrafo actual puede ser una continuación del anterior.\n", "   - <PERSON><PERSON><PERSON> solo ### para epígrafes y #### para subtitulos de epígrafes si fuera necesario. No se te permite usar ningun hashtag markdown de nivel superior.\n", "   - Emplea elementos Markdown como listas, negrita y cursiva para mejorar la legibilidad. Sin embargo, evita abusar de listas o enumeraciones, ya que el contenido resultante de muchas de estas puede resultar robótico.\n", "   - Escribe siguiendo un hilo narrativo para hacer el contenido cohesivo. Ten en cuenta el contenido anterior si lo hubiera para escribir del actual.\n", "   - Mantén un tono profesional y académico, evitando usar jerga y lenguaje coloquial.\n", "   - Escribe de forma clara, precisa y adecuada al nivel académico de la asignatura.\n", "   - En el caso de utilizar fórmulas, utilizarás latex y lo precederás antes y después con los signos $$ para que se muestren adecuadamente en el formato markdown. Ej:\n", "   - No incluyas más elementos markdown de los que se te menciona.\n", "$$\n", "E = mc^2\n", "$$\n", "   - Para introducir bloques de código utilizarás bloques de código con el formato:\n", "\n", "```\n", "def saludar(nombre):\n", "    return \"¡Hola\" + nombre + \"Bienvenido al mundo de la programación.\"\n", "\n", "# Ejemplo de uso de la función\n", "nombre_usuario = \"María\"\n", "mensaje = saludar(nombre_usuario)\n", "print(mensaje)\n", "```\n", "\n", "2. <PERSON><PERSON><PERSON>:\n", "   - Crea material académicamente riguroso y apropiado para la materia que siga las instrucciones que se te proporcionan.\n", "   - Asegúrate de que el contenido se alinee con el contexto de bloque, tema y asignatura proporcionada.\n", "   - Realiza transiciones suaves a contenido anterior si es relevante.\n", "   - Si hay contenido previo, continúa desde donde este termina.\n", "   - Sigue el plan y escribe sobre lo que se te pide.\n", "   - Incluirás al principio de la redacción el nombre del epígrafe sobre el que estás escribiendo seguido del contenido que contiene respetando el nombre exacto del epígrafe que se te proporciona\n", "\n", "</instrucciones de redacción>\n", "\n", "<herramientas>\n", "Dispones de algunas herramientas que puedes usar para la creación de contenidos cuya representación va más allá del texto.\n", "Debes ser consciente de tus limitaciones y ceñirte a usar las herraminetas como se describe aquí.\n", "\n", "Las herramientas que puedes usar actualmente son:\n", "\n", "* Tablas: Las tablas se crean utilizando formato markdown y se encierran dentro de las etiquetas <tabla></tabla>. Después de la tabla, se incluye una leyenda concisa junto con el número de tabla. Las tablas son útiles para presentar datos estructurados de manera organizada y fácil de leer.\n", "* Diagramas: Los diagramas se crean utilizando la sintaxis de Mermaid y se encierran dentro de las etiquetas <mermaid></mermaid>. Se pueden crear varios tipos de visualizaciones, como diagramas de flujo, diagramas de secuencia, diagramas de estado, gráficos de pastel, mapas mentales, líneas de tiempo y más. Cada diagrama va acompañado de una leyenda concisa en cursiva utilizando formato markdown.\n", "\n", "   <tablas>\n", "        Puedes crear tablas en formato markdown. Cuando necesites crear una tabla la incluirás dentro de las etiquetas <tabla></tabla> y fuera incluirás una leyenda concisa junto al numero de tabla. Uso típico: Las tablas son ideales para presentar datos estructurados, comparar valores entre diferentes categorías o mostrar tendencias a lo largo del tiempo.\n", "\n", "        Las leyendas de las tablas irán en cursiva y proporcionarán una descripción concisa. Al principio de cada tabla irá: *Tabla [x]* (siendo [x] el numero de la tabla). Ej: *Tabla 3. La evolución del comercio en el siglo XX*..\n", "\n", "        Aquí tienes un ejemplo de cómo puedes usarlas.\n", "\n", "        Consideraciones:\n", "        * Usa alineaciones coherentes y mantén el mismo número de columnas en cada fila.\n", "        * Utiliza <br> o viñetas para listas dentro de una misma celda.\n", "        * Rodea la tabla con <tabla></tabla> y añade una leyenda breve al final en cursiva (e.g., *Tabla X. Descripción.*).\n", "        * Destaca encabezados con negritas o separadores :---: para facilitar la lectura.\n", "\n", "        Ejemplos:\n", "        <tabla>\n", "        | **Marca**       | **Q1**  | **Q2**  | **Q3**  | **Q4**  | **Total** |\n", "        |:----------------|--------:|--------:|--------:|--------:|----------:|\n", "        | Samsung         |  22.10% |  20.80% |  19.70% |  19.40% |    20.50% |\n", "        | Apple           |  21.10% |  16.70% |  16.20% |  24.70% |    19.68% |\n", "        | Xiaomi          |  12.70% |  13.90% |  13.40% |  12.90% |    13.23% |\n", "        | OPPO            |   8.80% |   9.60% |  10.00% |   8.80% |     9.30% |\n", "        | Otros           |  35.30% |  39.00% |  40.70% |  34.20% |    37.30% |\n", "        | **Total**       | 100.00% | 100.00% | 100.00% | 100.00% |   100.00% |\n", "        </tabla>\n", "        *Tabla 3. Cuota de Mercado Global de Teléfonos Inteligentes (Samsung, Apple, Xiaomi, OPPO).*\n", "\n", "        <tabla>\n", "        | **País**      | **Características**                        | **Indicadores Clave**               |\n", "        |:-------------:|:------------------------------------------:|:------------------------------------:|\n", "        | **Alemania**  | - Innovación<br>- Alta productividad       | - PIB: 3.8T<br>- Exportaciones: 1.6T |\n", "        | **España**    | - Turismo<br>- Agroindustria               | - PIB: 1.4T<br>- Exportaciones: 0.3T |\n", "        | **Japón**     | - Tecnología<br>- Longevidad               | - PIB: 5.0T<br>- Patentes: 295k      |\n", "        </tabla>\n", "        *Tabla 4. <PERSON><PERSON> y datos clave*\n", "    </tablas>\n", "\n", "   <diagramas>\n", "   Dispones de la capacidad de crear visualizaciones mermaid. Lo usarás cuando sea relevante y se te pida usar gráficos, visualizaciones, lineas de tiempo u otro tipo de ilustraciones. Tendrás en cuenta las limitaciones de esto ya que no permite generar todo tipo de visualizaciones.\n", "\n", "    Las leyendas los diagramas irán en cursiva y proporcionarán una descripción concisa. Al principio de cada gráfico o diagrama irá: *Figura [x] + [Descripción breve]*. Ej: *Figura 5. Tipos de joins en base de datos SQL*.\n", "\n", "    Dentro del contexto de la redacción siempre los mencionarás como figuras.\n", "\n", "    Algunas visualizaciones que se pueden crear con mermaid y puedes usar son:\n", "    <ejemplos diagramas>\n", "        1. Diagramas de flujo. Uso típico: Los diagramas de flujo son excelentes para representar procesos, algoritmos o flujos de trabajo paso a paso. Ejemplo:\n", "        <mermaid>\n", "        flowchart TD\n", "            A[Start] --> B[Decision: Continue?]\n", "            B -- Yes --> <PERSON>[OK]\n", "            C --> D[Rethink]\n", "            D --> B\n", "            B -- No ----> E[End]\n", "        </mermaid>\n", "        Figura 1. *Proceso de toma de decisiones simple. Este diagrama ilustra un proceso básico de toma de decisiones con un bucle de retroalimentación.*\n", "\n", "        2. Recorridos de usuario. Uso típico: Modelado o ilustración de experiencia de usuario.Ejemplo:\n", "        <mermaid>\n", "        journey\n", "            title My working day\n", "            section Go to work\n", "            Make tea: 5: Me\n", "            Go upstairs: 3: Me\n", "            Do work: 1: <PERSON>, <PERSON>\n", "            section Go home\n", "            Go downstairs: 5: Me\n", "            Sit down: 5: Me\n", "        </mermaid>\n", "        Figura 2. *Jornada laboral típica de un empleado.*\n", "\n", "        3. Pie charts. Uso tipico: Proporciones de un todo Ejemplo:\n", "        <mermaid>\n", "        pie title Pets adopted by volunteers in 2023\n", "            \"Dogs\" : 386\n", "            \"Cats\" : 85\n", "            \"Rats\" : 15\n", "        </mermaid>\n", "        Figura 3. *Distribución de mascotas adaptadas por voluntarios en 2023*\n", "\n", "        4. Mapas mentales. Uso típico: Organización de ideas. Ejemplo:\n", "        <mermaid>\n", "        mindmap\n", "        root((Future Tech))\n", "            AI & ML\n", "            NLP\n", "                Natural Language Processing\n", "            Robotics\n", "                Advanced Automation\n", "            Quantum\n", "            Cryptography\n", "                Unbreakable Encryption\n", "            Simulations\n", "                Complex System Modeling\n", "            Bio-Tech\n", "            CRISPR\n", "                Gene Editing\n", "            Bionics\n", "                Human Augmentation\n", "            Green Tech\n", "            Solar\n", "                Advanced Photovoltaics\n", "            Fusion\n", "                Clean Energy Revolution\n", "            XR\n", "            VR/AR\n", "                Immersive Experiences\n", "            BCI\n", "                Brain-Computer Interfaces\n", "            IoT\n", "            Smart Homes\n", "                Automated Living\n", "            Wearables\n", "                Health & Fitness Tracking\n", "        </mermaid>\n", "        Figura 4. *Panorama de tecnologías futuras*.\n", "        5. Lineas de tiempo. Uso tí<PERSON>: mostrar eventos cronológicos. Ejemplo:\n", "        <mermaid>\n", "        timeline\n", "            title La Invención de Internet\n", "            section Orígenes\n", "                1969 : ARPANET establece su primera conexión\n", "                1973 : Desarrollo del protocolo TCP/IP\n", "            section Evolución\n", "                1983 : ARPANET adopta TCP/IP\n", "                1989 : <PERSON>-<PERSON> propone la World Wide Web\n", "            section Expansión\n", "                1991 : La WWW se hace pública\n", "                1993 : Lanzamiento del navegador Mosaic\n", "            section Era Moderna\n", "                1998 : Google es fundado\n", "                2004 : Lanzamiento de Facebook\n", "        </mermaid>\n", "        Figura 5. *Hitos en la historia de Internet*. Adaptado de \"Where Wizards Stay Up Late: The Origins of the Internet\" p<PERSON> <PERSON>, K., & Lyon, M. (1998).\n", "\n", "        6. Graf<PERSON><PERSON> xy. <PERSON><PERSON>: Relaciones numéricas. Ejemplo:\n", "        <mermaid>\n", "        xychart-beta\n", "        title \"Ingresos de Ventas TechCorp Inc 2023\"\n", "        x-axis [ene, feb, mar, abr, may, jun, jul, ago, sep, oct, nov, dic]\n", "        y-axis \"Ingresos (en $)\" 4000 --> 11000\n", "        bar [5000, 6000, 7500, 8200, 9500, 10500, 11000, 10200, 9200, 8500, 7000, 6000]\n", "        line [5000, 6000, 7500, 8200, 9500, 10500, 11000, 10200, 9200, 8500, 7000, 6000]\n", "        </mermaid>\n", "        Figura 6. *Ingresos mensuales de ventas de TechCorp Inc 2023*.\n", "\n", "        <mermaid>\n", "        xychart-beta\n", "        title \"Relacion entre felicidad y desempeño organizacional\"\n", "        x-axis \"Felicidad\" 0 --> 100\n", "        y-axis \"Desempeño organizacional\" 0 --> 100\n", "        line [10,30,45,55,70,80,85,88,90]\n", "        </mermaid>\n", "        Figura 7. *Relacion entre felicidad y desempeño organizacional*.\n", "    </ejemplos diagramas>\n", "\n", "    Limitaciones:\n", "\n", "    - Mantén las visualizaciones básicas y no utilices ningún estilo personalizado mermaid. Utilizar estilos personalizados puede dañar la estética o dificultar la visualización en visores de documentos básicos.\n", "\n", "    - **No creees diagramas demasiado complejos** o con muchos elementos, ya que pueden volverse ilegibles o dar errores.\n", "\n", "    - Ten en cuenta que \"las listas markdown dentro de los diagramas mermaid no están soportadas\". Eso implica que usar dentro del texto de diagramas cosas como [1. Lo que sea] o [- <PERSON><PERSON>], resultará en un error. Manten el formato del texto sencillo cuando uses mermaid y no trates de hacer cosas las cuales su estandar no soporte.\n", "\n", "    - Es importante que lo uses cuando se te pida explicitamente, para evitar sobrecargar el contenido de diagramas.\n", "    - Ten en cuenta que los caracteres especiales hay que escaparlos mediante comillas.\n", "      <PERSON>r e<PERSON><PERSON><PERSON>, el siguiente elemento de un flowchart resultará en error:\n", "        O --> P[Constitución Española (art. 117.3)]\n", "      <PERSON><PERSON><PERSON><PERSON>o:\n", "        O --> P[\"Constitución Española (art. 117.3)\"]\n", "\n", "    - En mindmaps, no utilices NUNCA comillas (\"\"), porque se verán de la siguiente manera:\n", "        &quot;Teoría de la AcciónRazonada y Teoría de laConducta Planeada&quot;\n", "        Evita generaciones que conduzcan al uso de las comillas en mindmaps.\n", "   </diagramas>\n", "\n", "</herramientas>\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 15, "id": "622eceb0", "metadata": {}, "outputs": [], "source": ["user_prompt_generate_content_epigraph = \"\"\"\n", "Plan para generar el contenido:\n", "<plan>\n", "{{plan}}\n", "</plan>\n", "\n", "<contexto asignatura>\n", "Asignatura: {{asignatura}} (La primera parte es la asignatura y la segunda el título dentro del cual se encuentra. Tenlo en cuenta para escribir sobre ello)\n", "Indice asignatura: {{indice_asignatura}}\n", "\n", "Usa esto para contextualizar sobre lo que has de escribir.\n", "</contexto asignatura>\n", "\n", "<contenido previo>\n", "{{contenido_previo}}\n", "</contenido previo>\n", "\n", "Estás escribiendo sobre el tema: {{nombre_tema}}\n", "Específicamente has de escribir contenido siguiendo el plan para el epígrafe: {{nombre_epigrafe}}\n", "\n", "Contenido final para el epígrafe que se te pide:\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 16, "id": "c5d21c0b", "metadata": {}, "outputs": [], "source": ["from ia_gen_core.prompts import ChatPrompt, Role, Message\n", "\n", "prompt = ChatPrompt(name = \"prompt_contenido_epigrafe\", prompt = [Message(role = Role.system, content = system_prompt_generate_content),\n", "                                                                  Message(role =Role.user, content = user_prompt_generate_content_epigraph)])"]}, {"cell_type": "code", "execution_count": 15, "id": "2c9ea75b", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'llm_manager' is not defined", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[15]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m llm = \u001b[43mllm_manager\u001b[49m.get_llm(provider=\u001b[33m\"\u001b[39m\u001b[33manthropic\u001b[39m\u001b[33m\"\u001b[39m, model_name=\u001b[33m\"\u001b[39m\u001b[33mclaude-sonnet-4-20250514\u001b[39m\u001b[33m\"\u001b[39m, max_tokens=\u001b[32m30000\u001b[39m, temperature = \u001b[32m0\u001b[39m, reasoning_effort=\u001b[33m\"\u001b[39m\u001b[33mmedium\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[31mNameError\u001b[39m: name 'llm_manager' is not defined"]}], "source": ["llm = llm_manager.get_llm(provider=\"anthropic\", model_name=\"claude-sonnet-4-20250514\", max_tokens=30000, temperature = 0, reasoning_effort=\"medium\")"]}, {"cell_type": "code", "execution_count": 18, "id": "123ace96", "metadata": {}, "outputs": [], "source": ["from src.api.common.services.content_generator.utils.parsing import extract_content"]}, {"cell_type": "markdown", "id": "646afedc", "metadata": {}, "source": ["Sonnet con razonamiento en vez de tag reasoning"]}, {"cell_type": "code", "execution_count": 127, "id": "04921160", "metadata": {}, "outputs": [], "source": ["temas = [n.nombre for t in index_response.index.estructura.bloques_tematicos for n in t.temas]"]}, {"cell_type": "code", "execution_count": 49, "id": "88a15ced", "metadata": {}, "outputs": [], "source": ["temas = [n.nombre for t in index_response.index.estructura.bloques_tematicos for n in t.temas]\n", "subject_name = index_response.index.nombre\n", "bloque_idx = 0\n", "topic_idx = 1\n", "tema = temas[topic_idx]\n", "subject_index = index_response.index.estructura.model_dump()\n", "epigrafes = subject_index['bloques_tematicos'][bloque_idx]['temas'][topic_idx]['epigrafes']\n", "epigrafe_instrucciones = {d.name: d.didactic_instructions for d in instructions_responses if d.name in epigrafes}"]}, {"cell_type": "code", "execution_count": 50, "id": "af937bc1", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'Declaración de variables (var, let, const)': 'Comienza con un breve recordatorio del primer programa \"Hola, mundo\" y explica que, para que un algoritmo manipule información real, necesita disponer de contenedores en memoria. Introduce inmediatamente el concepto de variable como ese contenedor y enlaza con la idea de identificadores ya vista al describir los scripts iniciales.\\n\\n1. Presenta la sintaxis básica de declaración en JavaScript en una narrativa progresiva: primero **var**, después **let** y finalmente **const**. Explica por qué **var** aparece históricamente primero y señala que hoy se considera obsoleto en la mayor parte de los casos, pero sigue existiendo por retrocompatibilidad.\\n2. Dedica un apartado a los tres modelos de alcance: global, de función y de bloque. Incluye un diagrama mermaid muy sencillo en el que se vea un bloque _function_ conteniendo otro bloque interno y dónde se queda cada variable según fuera declarada con **var**, **let** o **const**. No es necesario detallar cada nivel de profundidad; basta con ilustrar el principio.\\n3. Introduce el concepto de _hoisting_ únicamente asociado a **var**, describe su funcionamiento paso a paso y enlázalo con la recomendación de preferir **let** y **const**. Incluye un breve fragmento de código que muestre una referencia a una variable antes de su declaración y el resultado en consola.\\n4. Explica la diferencia entre reasignación y redeclaración. Señala que **const** impide la reasignación del identificador pero no vuelve inmutable al objeto o arreglo referenciado; incluye un micro-ejemplo con un arreglo modificado tras declararse con **const**.\\n5. Añade reglas estilísticas sobre nombres (camelCase, evitar palabras reservadas, claridad semántica), insistiendo en la importancia de la legibilidad para el mantenimiento del código.\\n6. Proporciona tres fragmentos cortos de código que muestren: a) alcance global frente a bloque; b) redeclaración con **var** y su consecuencia; c) intento de reasignar una **const**. Después de cada fragmento, indica con una frase (no un párrafo extenso) el resultado que se espera en consola.\\n7. Finaliza el epígrafe con una lista concisa de buenas prácticas (máximo 5 puntos) que sirva de transición hacia los tipos de datos, a tratar a continuación.\\n\\nExtensión prevista: 1100-1300 palabras.',\n", " 'Tipos primitivos y coerción de datos': 'Introduce el epígrafe enlazando con la idea de que una variable, además de un nombre, alberga un tipo de dato. Recuerda brevemente que JavaScript es un lenguaje de tipado dinámico, para diferenciarlo del pseudocódigo fuertemente tipado que el alumnado pudo imaginar previamente.\\n\\n1. Desglosa los tipos primitivos oficiales: **number**, **string**, **boolean**, **undefined**, **null**, **symbol** y **bigint**. Dedica uno o dos párrafos a cada tipo, ejemplificando con una literal representativa (por ejemplo, `42n` para **bigint**) y mostrando qué devuelve `typeof`.\\n2. Aclara la diferenciación entre **undefined** y **null** desde un punto de vista semántico, dejando claro que ambos representan \"ausencia\", pero en contextos distintos. Incluye un micro-ejemplo donde una función sin `return` devuelve **undefined** y otro donde una variable se inicializa explícitamente con **null**.\\n3. Presenta el concepto de coerción implícita y explora su mecánica con una narrativa apoyada en fragmentos de código: concatenación con el operador `+`, coerción booleana en contextos condicionales y comparación no estricta (`==`). A continuación, muestra la coerción explícita con `Number()`, `String()`, `Boolean()`, `parseInt`, `parseFloat` y el operador unario `+`.\\n4. Incluye una pequeña tabla que resuma las conversiones más frecuentes (por ejemplo, `\"5\" - 2 => 3`) y advierte de las operaciones inesperadas (`\"five\" * 2 => NaN`). Mantén la tabla en 6-8 filas como máximo.\\n5. Ejercicio guiado (único para todo el tema en este epígrafe): plantea cinco líneas de código donde se mezclen números y cadenas; pide al lector predecir el resultado antes de revelar la ejecución real. Después del bloque, proporciona la salida esperada y una breve explicación de cada línea.\\n6. Explica la diferencia entre igualdad suelta (`==`) e igualdad estricta (`===`) reforzando que el uso de `===` evita sorpresas por coerción implícita. Ofrece dos ejemplos contrastados.\\n7. Cierra el epígrafe con una transición: «Ahora que comprendemos los valores y cómo se convierten, necesitamos herramientas para operarlos», anticipando el estudio de operadores.\\n\\nExtensión prevista: 1200-1400 palabras.',\n", " 'Operadores aritméticos, lógicos y comparativos': 'Abre con la idea de que los algoritmos manipulan datos a través de operadores; retoma la tabla de coerciones para recordar por qué conocer el tipo de resultado es vital al combinarlos.\\n\\n1. Presenta de forma narrativa los operadores aritméticos básicos (`+`, `-`, `*`, `/`, `%`) y enseguida introduce `**` (exponenciación) conectando con la noción matemática vista en bachillerato. Utiliza ejemplos muy breves con comentarios en línea para cada caso.\\n2. Explica los operadores de asignación compuesta (`+=`, `-=`, `*=`, etc.) enlazando con la substitución de expresiones repetitivas. Ofrece un fragmento de código que incremente un contador con estilos alternativos.\\n3. Dedica un apartado a los operadores comparativos (`>`, `<`, `>=`, `<=`, `==`, `===`, `!=`, `!==`). Relaciona el tema con el próximo bloque sobre estructuras condicionales, remarcando la importancia de que su resultado sea booleano.\\n4. Introduce los operadores lógicos (`&&`, `||`, `!`) y el concepto de evaluación de corto circuito; muestra cómo pueden usarse para asignar valores por defecto (`const port = envPort || 3000;`).\\n5. Menciona brevemente el operador ternario como forma compacta de decisión simple, sin entrar aún en control de flujo detallado (eso se desarrollará en el tema siguiente). Incluye un fragmento con ternario y la misma lógica expresada con `if` para contrastar.\\n6. Incorpora una tabla compacta de precedencia (máximo 10 filas, de mayor a menor), resaltando con negrita aritméticos, comparativos y lógicos para facilitar la memorización.\\n7. Incluye un ejemplo único algo más extenso que combine aritméticos, lógicos y comparación estricta. Pide al lector seguir la precedencia paso a paso; después del código, añade la resolución comentada.\\n8. Termina abriendo la puerta a la interacción usuario-programa remarcando que, para ver el efecto de estos operadores en tiempo real, se necesita introducir y mostrar datos, preludio del siguiente epígrafe.\\n\\nExtensión prevista: 1000-1200 palabras.',\n", " 'Entrada y salida básica con prompt, alert y console.log': 'Inicia el texto recordando que hasta este punto todo se ha visto en forma de fragmentos aislados y que ahora es necesario comunicarse con el usuario o con la consola para observar resultados más dinámicos.\\n\\n1. Presenta rápidamente las tres funciones clásicas del entorno navegador: `alert`, `prompt` y `confirm`. Explica su naturaleza modal y síncrona y muestra un ejemplo breve de cada una.\\n2. Introduce `console.log`, `console.error` y `console.table` en el contexto de desarrollador; subraya que son multiplataforma (navegador y Node) y que permiten depurar con mayor flexibilidad.\\n3. Incluye una nota sobre el entorno Node.js: explicar que `prompt` no existe de forma nativa y mencionar `readline` solo de forma referencial (sin profundizar) para no exceder el nivel de complejidad.\\n4. Ejemplo práctico (segundo y último ejercicio del tema): propone construir una \"calculadora mínima\" que\\n   – solicite dos números mediante `prompt`,\\n   – pregunte la operación mediante otro `prompt` (suma, resta, multiplicación o división) y\\n   – muestre el resultado con `alert` y `console.log` empleando plantillas literales.\\n   Tras el código, incluye una breve explicación en pasos, reforzando la conversión de cadena a número y el uso de operadores aprendidos.\\n5. Añade un cuadro de buenas prácticas para salida en consola (evitar logs redundantes, formateo, uso de etiquetas).\\n6. Conecta con el próximo tema señalando que, al recibir datos externos, se hace indispensable decidir qué ruta seguir según su valor; de ahí surge la necesidad de estructuras condicionales.\\n\\nExtensión prevista: 850-1000 palabras.'}"]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": ["epigrafe_instrucciones"]}, {"cell_type": "code", "execution_count": 29, "id": "d9d0a2ab", "metadata": {}, "outputs": [{"data": {"text/plain": ["('Tema 1. Introducción a la programación y al lenguaje Python',\n", " dict_keys(['Presentación de la asignatura y herramientas de trabajo', 'Concepto de algoritmo y diagramas de flujo', 'Instalación de Python y configuración del entorno de desarrollo', 'Primer programa: sintaxis básica y uso de print()']))"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["tema, epigrafe_instrucciones.keys()"]}, {"cell_type": "code", "execution_count": 25, "id": "b79b8b43", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["**Extensión orientativa: 850–1 000 palabras**\n", "\n", "- Comienza con un breve párrafo que sitúe la disciplina de la programación dentro del ámbito de la informática y motive su estudio (ej. automatizar tareas, resolver problemas y crear productos digitales). Aclara que el lenguaje elegido es Python por su sintaxis sencilla y su ecosistema.\n", "- Enumera los objetivos generales que el lector alcanzará al terminar la asignatura (pensar algorítmicamente, dominar las estructuras básicas, escribir programas robustos y colaborar en proyectos). Relaciónalos con los bloques temáticos del programa para que el alumnado visualice el recorrido completo sin profundizar todavía en los contenidos concretos que se abordarán más adelante.\n", "- Describe las competencias prácticas que se desarrollarán: comprensión de problemas, diseño de algoritmos, traducción a código, prueba y depuración, documentación y trabajo colaborativo.\n", "- Presenta las herramientas que se utilizarán durante el semestre, diferenciándolas por categoría en una tabla (lenguaje, interprete, editores/IDEs, terminal/comandos, sistema de control de versiones):\n", "  | Categoría                | Ejemplos propuestos | Propósito principal |\n", "  |--------------------------|---------------------|---------------------|\n", "  | Intérprete Python 3.x    | CPython, Anaconda   | Ejecutar código     |\n", "  | Editor/IDE              | VS Code, PyCharm CE | Escribir y depurar  |\n", "  | Terminal                | bash, PowerShell    | Ejecutar comandos   |\n", "  | Entornos virtuales      | venv, conda         | Aislar dependencias |\n", "  | Control de versiones    | Git + GitHub/GitLab | Trabajo en equipo   |\n", "- Añade un apartado sobre requisitos mínimos de hardware/software y recomendaciones de buenas prácticas (guardar código en la nube, uso responsable del copy-paste, hábito de comentar). Evita todavía detallar la instalación de Python: eso se cubrirá en el epígrafe siguiente.\n", "- Introduce la dinámica de trabajo: sesiones teóricas, laboratorios, entregas y foros de dudas. Incluye un micro-ejemplo narrativo para mostrar cómo un estudiante pasará de la idea a la solución: “imagina que quieres clasificar tus gastos mensuales…”. No muestres código en este punto.\n", "- Conecta con el tema siguiente explicando que, antes de escribir un programa, hay que entender qué es un algoritmo y cómo representarlo gráficamente.\n", "- Cierra con una invitación a la exploración y al aprendizaje activo, evitando conclusiones definitivas y dejando claro que los detalles técnicos empiezan en el siguiente epígrafe.\n", "\n"]}], "source": ["print(epigrafe_instrucciones[\"Presentación de la asignatura y herramientas de trabajo\"])"]}, {"cell_type": "code", "execution_count": 26, "id": "51406112", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n"]}], "source": ["contenido_previo = \"\"\n", "for epigrafe_name, epigrafe_instructions in epigrafe_instrucciones.items():\n", "    epigrafes = list(epigrafe_instrucciones)\n", "    compiled_prompt = prompt.compile(asignatura = subject_name, indice_asignatura = subject_index, nombre_epigrafe = epigrafe_name, plan = epigrafe_instructions, contenido_previo = contenido_previo, nombre_tema = tema).to_langchain()\n", "    result = llm.invoke(compiled_prompt)\n", "    contenido_previo += \"\\n\" + result.content[1]['text']"]}, {"cell_type": "code", "execution_count": 27, "id": "e392bfb2", "metadata": {}, "outputs": [], "source": ["contenido_generado_por_epigrafes = contenido_previo"]}, {"cell_type": "code", "execution_count": 28, "id": "da90dfcb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "### Presentación de la asignatura y herramientas de trabajo\n", "\n", "La programación constituye uno de los pilares fundamentales de la informática moderna, representando el puente entre las ideas y su materialización en soluciones digitales concretas. En un mundo cada vez más digitalizado, la capacidad de **automatizar tareas repetitivas**, **resolver problemas complejos** mediante la descomposición en pasos más simples y **crear productos tecnológicos** que mejoren la vida de las personas se ha convertido en una competencia esencial. Esta asignatura te introducirá en este fascinante universo a través de **Python**, un lenguaje de programación que hemos seleccionado por su sintaxis clara y legible, su amplio ecosistema de librerías y su versatilidad para abordar desde problemas sencillos hasta aplicaciones empresariales de gran envergadura.\n", "\n", "#### Objetivos de aprendizaje y conexión con el programa\n", "\n", "Al finalizar esta asignatura, habrás desarrollado las competencias necesarias para **pensar algorítmicamente**, descomponiendo problemas en secuencias lógicas de pasos ejecutables. Dominarás las **estructuras básicas de programación** que encontrarás en cualquier lenguaje moderno, desde variables y operadores hasta estructuras de control de flujo. Además, serás capaz de **escribir programas robustos** que manejen errores de forma elegante y **colaborar efectivamente** en proyectos de desarrollo de software utilizando herramientas profesionales.\n", "\n", "Estos objetivos se articulan a través de los tres bloques temáticos que estructuran el programa. El **Bloque I** te proporcionará los cimientos conceptuales y prácticos, desde la comprensión de qué es un algoritmo hasta el manejo fluido de tipos de datos y estructuras de control. El **Bloque II** profundizará en las estructuras de datos que te permitirán organizar información compleja y en la programación modular que favorece la reutilización y mantenibilidad del código. Finalmente, el **Bloque III** te conectará con el mundo real, mostrándote cómo persistir datos, aprovechar librerías especializadas y trabajar colaborativamente en proyectos que integren múltiples tecnologías.\n", "\n", "#### Competencias prácticas fundamentales\n", "\n", "Durante el desarrollo de la asignatura cultivarás competencias que van más allá del mero conocimiento sintáctico del lenguaje. La **comprensión profunda de problemas** te permitirá identificar los requerimientos reales antes de comenzar a programar. El **diseño de algoritmos** te capacitará para planificar soluciones eficientes antes de escribir la primera línea de código. La **traducción efectiva a código** conectará tu pensamiento lógico con la implementación práctica en Python.\n", "\n", "Igualmente importante será tu habilidad para **probar y depurar** programas sistemáticamente, identificando y corrigiendo errores de manera metódica. La **documentación clara** de tu código garantizará que tanto tú como otros desarrolladores puedan entender y mantener las soluciones creadas. Por último, el **trabajo colaborativo** te preparará para los entornos profesionales actuales, donde el desarrollo de software es fundamentalmente un esfuerzo de equipo.\n", "\n", "#### Herramientas de trabajo profesionales\n", "\n", "El ecosistema de herramientas que utilizaremos durante el semestre refleja las prácticas estándar de la industria del software, preparándote para un entorno profesional real:\n", "\n", "<tabla>\n", "| **Categoría** | **Ejemplos propuestos** | **<PERSON><PERSON><PERSON>ito principal** |\n", "|:--------------|:-----------------------|:------------------------|\n", "| **Intérprete Python 3.x** | CPython, Anaconda | Ejecutar código Python de forma interactiva y en batch |\n", "| **Editor/IDE** | VS Code, PyCharm Community Edition | Escribir, editar y depurar código con asistencia inteligente |\n", "| **Terminal** | bash (Linux/macOS), PowerShell (Windows) | Ejecutar comandos del sistema y scripts |\n", "| **Entornos virtuales** | venv, conda | Aislar dependencias entre proyectos |\n", "| **Control de versiones** | Git + GitHub/GitLab | Gestionar historial de cambios y trabajo en equipo |\n", "</tabla>\n", "\n", "*Tabla 1. Herramientas de desarrollo utilizadas en la asignatura, organizadas por categoría funcional.*\n", "\n", "#### Requisitos técnicos y buenas prácticas\n", "\n", "Para participar efectivamente en la asignatura necesitarás un ordenador con al menos 4GB de RAM y 2GB de espacio libre en disco. Cualquier sistema operativo moderno (Windows 10+, macOS 10.14+, o distribuciones Linux recientes) será adecuado. Recomendamos encarecidamente **mantener respaldos de tu código en la nube**, ya sea a través de repositorios Git o servicios de almacenamiento sincronizado.\n", "\n", "Desde el inicio, fomentaremos el **uso responsable del copy-paste**: entender el código antes de incorporarlo a tus proyectos es fundamental para el aprendizaje genuino. Desarrollarás el **hábito de comentar tu código**, explicando no solo qué hace sino por qué lo hace de esa manera específica. Esta práctica será invaluable cuando revises tu propio código semanas después o cuando colabores con otros desarrolladores.\n", "\n", "#### Dinámica de trabajo y metodología\n", "\n", "La asignatura combina **sesiones teóricas** donde exploraremos conceptos fundamentales con **laboratorios prácticos** donde aplicarás inmediatamente lo aprendido. Las **entregas periódicas** te permitirán recibir retroalimentación continua sobre tu progreso, mientras que los **foros de dudas** facilitarán el aprendizaje colaborativo entre compañeros.\n", "\n", "<PERSON><PERSON><PERSON><PERSON>, por ejemplo, que quieres desarrollar una aplicación para clasificar tus gastos mensuales automáticamente. Comenzarías identificando el problema: necesitas categorizar transacciones bancarias según patrones específicos. Luego diseñarías un algoritmo que analice la descripción de cada transacción y la asigne a categorías como \"alimentación\", \"transporte\" o \"entretenimiento\". Finalmente, implementarías esta lógica en Python, probarías con datos reales, documentarías el proceso y, potencialmente, compartirías tu solución con la comunidad. Este recorrido desde la idea hasta la solución funcional encapsula perfectamente el proceso que dominarás a lo largo del semestre.\n", "\n", "#### Preparando el camino hacia el pensamiento algorítmico\n", "\n", "Antes de sumergirnos en la escritura de código Python, es crucial que comprendas qué constituye un algoritmo y cómo representar visualmente la lógica de resolución de problemas. En el siguiente epígrafe exploraremos estas representaciones gráficas que te servirán como herramientas de planificación antes de traducir tus ideas a código ejecutable.\n", "\n", "Te invitamos a embarcarte en este viaje de descubrimiento con una mentalidad abierta y experimentadora. La programación se aprende haciendo, equivocándose, corrigiendo y volviendo a intentar. Cada error es una oportunidad de aprendizaje, cada programa que funciona es un paso hacia la maestría técnica. Los fundamentos que construiremos juntos en estas primeras semanas te acompañarán durante toda tu carrera profesional, independientemente de los lenguajes o tecnologías específicas que utilices en el futuro.\n", "### Concepto de algoritmo y diagramas de flujo\n", "\n", "Ya hemos establecido los objetivos que perseguimos como programadores y las herramientas que nos acompañarán en este viaje. Ahora es momento de descubrir el concepto fundamental que sustenta cualquier programa informático, desde la aplicación más simple hasta los sistemas más complejos: el algoritmo. Comprender qué es un algoritmo y cómo representarlo visualmente te proporcionará los cimientos conceptuales necesarios para convertir ideas en soluciones ejecutables.\n", "\n", "#### Definición y propiedades fundamentales del algoritmo\n", "\n", "Un **algoritmo** es una secuencia finita, ordenada y no ambigua de pasos que transforma una o más entradas en una salida determinada. Esta definición aparentemente técnica cobra vida cuando la ilustramos con situaciones cotidianas. Consideremos, por ejemplo, el proceso de preparar una taza de café:\n", "\n", "1. Llenar la cafetera con agua hasta la marca correspondiente\n", "2. <PERSON><PERSON><PERSON> el filtro en el portafiltros\n", "3. <PERSON><PERSON><PERSON> cucharadas de café molido\n", "4. Encender la cafetera y esperar a que termine el proceso\n", "5. <PERSON><PERSON> en una taza\n", "\n", "Este sencillo ejemplo nos permite identificar las **tres propiedades clave** que debe cumplir todo algoritmo:\n", "\n", "La **finitud** garantiza que el proceso terminará en un número determinado de pasos. En nuestro ejemplo, no preparamos café indefinidamente; el algoritmo concluye cuando servimos la bebida. La **determinismo** o no ambigüedad asegura que cada instrucción sea suficientemente clara para que cualquier persona pueda ejecutarla sin interpretaciones múltiples. \"Añadir dos cucharadas de café molido\" es más preciso que \"echar café al gusto\". Finalmente, la **efectividad** requiere que cada paso sea realizable con los recursos disponibles y conduzca hacia el objetivo final.\n", "\n", "#### El pseudocódigo como puente conceptual\n", "\n", "Mientras que los algoritmos pueden expresarse en lenguaje natural, como acabamos de hacer con el café, el **pseudocódigo** constituye un puente invaluable entre nuestro pensamiento y el código ejecutable. Se trata de una notación semiformal que utiliza estructuras lógicas similares a los lenguajes de programación, pero manteniendo la flexibilidad del lenguaje humano.\n", "\n", "El pseudocódigo resulta especialmente útil cuando necesitamos **comunicar lógica compleja** a otros programadores, **planificar la estructura** de un programa antes de implementarlo, o **documentar algoritmos** de manera que sean comprensibles independientemente del lenguaje de programación utilizado. Su flexibilidad sintáctica permite enfocarse en la lógica del problema sin preocuparse por detalles específicos de sintaxis o compilación.\n", "\n", "#### Diagramas de flujo: representación visual de la lógica\n", "\n", "Los **diagramas de flujo** ofrecen una alternativa visual para representar algoritmos, especialmente valiosa cuando trabajamos con procesos que involucran decisiones múltiples o bucles complejos. Utilizan símbolos geométricos estandarizados conectados por flechas que indican el flujo de ejecución.\n", "\n", "<tabla>\n", "| **Símbolo** | **Significado** |\n", "|:------------|:----------------|\n", "| Óvalo | Inicio o fin del algoritmo |\n", "| Rectángulo | Proceso o acción a ejecutar |\n", "| Rombo | Decisión o condición (requiere sí/no) |\n", "| Paralelogramo | Entrada de datos o salida de resultados |\n", "| <PERSON><PERSON><PERSON><PERSON> pe<PERSON> | Conector (enlaza partes del diagrama) |\n", "| Flecha | Dirección del flujo de ejecución |\n", "</tabla>\n", "\n", "*Tabla 2. Símbolos básicos utilizados en diagramas de flujo y su significado funcional.*\n", "\n", "#### <PERSON><PERSON><PERSON><PERSON> g<PERSON>: suma de números del 1 al N\n", "\n", "Para consolidar estos conceptos, desarrollemos juntos un algoritmo que calcule la suma de todos los números enteros desde 1 hasta un valor N dado por el usuario. Comenzaremos con el pseudocódigo:\n", "\n", "```\n", "ALGORITMO SumaNumeros\n", "ENTRADA: N (número entero positivo)\n", "SALIDA: suma (total acumulado)\n", "\n", "INICIO\n", "    Leer N\n", "    suma ← 0\n", "    contador ← 1\n", "    \n", "    MIENTRAS contador ≤ N HACER\n", "        suma ← suma + contador\n", "        contador ← contador + 1\n", "    FIN MIENTRAS\n", "    \n", "    Mostrar suma\n", "FIN\n", "```\n", "\n", "La misma lógica puede representarse visualmente mediante el siguiente diagrama de flujo:\n", "\n", "<mermaid>\n", "flowchart TD\n", "    A([Inicio]) --> B[/Leer N/]\n", "    B --> C[suma = 0<br>contador = 1]\n", "    C --> D{contador ≤ N?}\n", "    D -->|Sí| E[suma = suma + contador<br>contador = contador + 1]\n", "    E --> D\n", "    D -->|No| F[/Mostrar suma/]\n", "    F --> G([Fin])\n", "</mermaid>\n", "\n", "*Figura 1. Diagrama de flujo para el cálculo de la suma de números del 1 al N. El rombo representa la condición del bucle, mientras que el rectángulo contiene las operaciones de actualización.*\n", "\n", "#### Ventajas y limitaciones de cada representación\n", "\n", "Cada forma de representar algoritmos presenta ventajas específicas según el contexto de uso. Los **diagramas de flujo** destacan en la comunicación con personas no familiarizadas con programación, facilitando la comprensión visual de procesos complejos y la identificación rápida de caminos de ejecución alternativos. Sin embargo, pueden volverse excesivamente extensos para algoritmos complejos y no capturan elegantemente ciertos conceptos de programación moderna como la recursión o el manejo de estructuras de datos avanzadas.\n", "\n", "El **pseudocódigo**, por su parte, ofrece mayor **precisión técnica** al incorporar estructuras lógicas similares a los lenguajes de programación reales. Resulta más compacto para algoritmos complejos y facilita la transición posterior a código ejecutable. No obstante, puede resultar menos intuitivo para audiencias no técnicas y su nivel de detalle puede variar significativamente entre diferentes autores.\n", "\n", "La **descripción en lenguaje natural** mantiene la ventaja de la comprensibilidad universal, pero sacrifica precisión y puede introducir ambigüedades que posteriormente generen errores de implementación.\n", "\n", "#### Preparando el terreno para la implementación\n", "\n", "Estos conceptos y representaciones que acabamos de explorar constituyen la fase de **planificación** que todo programador experimentado realiza antes de escribir la primera línea de código. Así como un arquitecto dibuja planos antes de comenzar la construcción, nosotros diseñamos algoritmos antes de implementarlos en Python.\n", "\n", "El ejercicio de la suma que hemos planteado permanecerá como referencia conceptual hasta que, en próximos epígrafes, contemos con las herramientas necesarias para convertirlo en un programa Python ejecutable. Para ello, necesitaremos instalar el intérprete de Python y configurar un entorno de desarrollo adecuado que nos permita escribir, probar y depurar nuestro código de manera eficiente.\n", "\n", "La metáfora que mejor describe esta progresión es la de un chef que planifica un menú: primero selecciona los platos (algoritmos), después redacta las recetas detalladas (pseudocódigo o diagramas), y solo entonces se dirige a la cocina con los ingredientes y utensilios apropiados para crear el banquete final. En nuestro caso, Python será tanto nuestra cocina como nuestro conjunto de utensilios especializados, pero la calidad del resultado final dependerá fundamentalmente de qué tan bien hayamos planificado nuestros algoritmos desde el principio.\n", "### Instalación de Python y configuración del entorno de desarrollo\n", "\n", "Con los fundamentos algorítmicos ya establecidos y el concepto de diagrama de flujo claramente comprendido, ha llegado el momento de **transformar nuestras ideas en programas ejecutables**. El algoritmo de suma de números del 1 al N que diseñamos anteriormente permanece como una abstracción hasta que contemos con las herramientas necesarias para implementarlo. Python será nuestro vehículo para materializar estos conceptos, pero primero debemos preparar adecuadamente nuestro entorno de trabajo.\n", "\n", "#### Python como intérprete y ecosistema\n", "\n", "Python no es simplemente un lenguaje de programación, sino un **ecosistema completo** que incluye el intérprete, bibliotecas estándar y una vasta colección de paquetes de terceros. Cuando hablamos de instalar Python, en realidad nos referimos a configurar este ecosistema en nuestro sistema operativo.\n", "\n", "Existen varias **distribuciones de Python** disponibles, cada una optimizada para diferentes casos de uso. **CPython** representa la implementación de referencia oficial, desarrollada en lenguaje C, y constituye la opción más estable y compatible para aprendizaje. **Anaconda** incluye CPython junto con numerosas librerías científicas preinstaladas y herramientas de gestión de entornos, resultando ideal para análisis de datos pero ocupando considerablemente más espacio en disco. Otras alternativas como **PyPy** optimizan la velocidad de ejecución, mientras que **IronPython** y **Jython** permiten integración con plataformas .NET y Java respectivamente.\n", "\n", "Para esta asignatura recomendamos **CPython versión 3.x** (específicamente 3.9 o superior), ya que ofrece el equilibrio perfecto entre estabilidad, compatibilidad y características modernas del lenguaje. Las versiones 2.x se consideran obsoletas desde enero de 2020 y no reciben actualizaciones de seguridad.\n", "\n", "#### Proceso de descarga e instalación multiplataforma\n", "\n", "El primer paso consiste en **descargar el instalador oficial** desde python.org, garantizando así que obtenemos una versión auténtica y actualizada. En **Windows**, ejecutamos el archivo .exe descargado y marcamos obligatoriamente la casilla \"Add Python to PATH\" antes de proceder con la instalación. Esta configuración permite invocar Python desde cualquier directorio del sistema sin especificar su ruta completa.\n", "\n", "Para usuarios de **macOS**, el instalador .pkg proporciona una experiencia similar, aunque muchos desarrolladores prefieren utilizar gestores de paquetes como Homebrew (`brew install python3`) que facilitan futuras actualizaciones. En sistemas **Linux**, la mayoría de distribuciones incluyen Python preinstalado, pero podemos asegurar la versión más reciente utilizando el gestor de paquetes correspondiente (`sudo apt install python3` en Ubuntu/Debian o `sudo dnf install python3` en Fedora).\n", "\n", "Independientemente del sistema operativo, el proceso debe completarse **reiniciando la terminal** o el sistema completo para garantizar que todas las variables de entorno se configuren correctamente.\n", "\n", "#### Verificación de la instalación\n", "\n", "Una vez completada la instalación, abrimos la **línea de comandos** correspondiente a nuestro sistema: Símbolo del sistema o PowerShell en Windows, Terminal en macOS, o cualquier emulador de terminal en Linux. Ejecutamos el comando `python --version` para verificar que el intérprete responde correctamente y muestra la versión instalada.\n", "\n", "En sistemas Unix (Linux y macOS), es posible que necesitemos utilizar `python3 --version` si coexisten múltiples versiones del intérprete. La respuesta esperada debería ser similar a \"Python 3.11.5\" o la versión específica que hayamos instalado.\n", "\n", "Adicionalmente, podemos **acceder al intérprete interactivo** escribiendo simplemente `python` en la terminal, lo que nos mostrará el prompt característico `>>>` donde podríamos ejecutar comandos Python directamente. Para salir del intérprete interactivo, utilizamos `exit()` o la combinación de teclas Ctrl+D (Linux/macOS) o Ctrl+Z seguido de Enter (Windows).\n", "\n", "#### Selección y configuración del entorno de desarrollo\n", "\n", "La elección del editor o IDE (Integrated Development Environment) influye significativamente en nuestra productividad como programadores. Cada herramienta presenta ventajas específicas según nuestro nivel de experiencia y los requisitos del proyecto:\n", "\n", "<tabla>\n", "| **IDE/Editor** | **Curva de aprendizaje** | **Depuración integrada** | **Plugins/extensiones** | **Consumo de recursos** |\n", "|:---------------|:-------------------------|:-------------------------|:------------------------|:------------------------|\n", "| **Thonny** | <PERSON>y baja | Básica pero clara | Limitados | Muy bajo |\n", "| **VS Code** | Moderada | Avanzada | Extenso ecosistema | Moderado |\n", "| **PyCharm Community** | Alta | Profesional | Integrados nativamente | Alto |\n", "| **IDLE** | Baja | Básica | Ninguno | Muy bajo |\n", "</tabla>\n", "\n", "*Tabla 3. Comparativa de entornos de desarrollo Python según criterios de selección práctica para estudiantes.*\n", "\n", "Para estudiantes que se inician en programación, **Thonny** ofrece una interfaz simplificada que visualiza claramente la ejecución paso a paso del código. **VS Code** proporciona un equilibrio excelente entre potencia y simplicidad, especialmente con la extensión oficial de Python instalada. **PyCharm Community Edition** brinda herramientas profesionales de desarrollo, aunque puede resultar abrumador inicialmente.\n", "\n", "#### Creación y gestión de entornos virtuales\n", "\n", "Los **entornos virtuales** constituyen una herramienta fundamental para aislar las dependencias de diferentes proyectos. Imaginemos que un proyecto requiere la versión 2.0 de una librería, mientras que otro necesita la versión 3.0; los entornos virtuales previenen conflictos manteniendo instalaciones separadas.\n", "\n", "Para crear un entorno virtual, navegamos hasta nuestro directorio de proyecto y ejecutamos `python -m venv venv`. Este comando crea un directorio llamado \"venv\" que contiene una instalación independiente de Python. La **activación** varía según el sistema operativo: `venv\\Scripts\\activate` en Windows o `source venv/bin/activate` en Linux/macOS. Una vez activado, el prompt del terminal mostrará \"(venv)\" indicando que trabajamos dentro del entorno aislado.\n", "\n", "Para **desactivar** el entorno virtual, simplemente escribimos `deactivate` en cualquier momento. Esta práctica se convertirá en rutina cuando trabajemos en múltiples proyectos simultáneamente.\n", "\n", "#### Introducción a Jupyter Notebook\n", "\n", "**Jupyter Notebook** merece mención especial como entorno de desarrollo, especialmente valioso para **experimentación rápida**, **análisis exploratorio de datos** y **documentación interactiva** de procesos. A diferencia de los editores tradicionales que trabajan con archivos de texto plano, <PERSON><PERSON><PERSON> combina código, resultados de ejecución y texto explicativo en un documento único.\n", "\n", "Para instalar Jupyter en nuestro entorno virtual, ejecutamos `pip install notebook` seguido de `jupyter notebook` para lanzar la interfaz web. Aunque no profundizaremos en Jupyter durante las primeras semanas, su disponibilidad nos permitirá experimentar cuando deseemos probar fragmentos de código rápidamente.\n", "\n", "#### Verificación práctica del entorno\n", "\n", "Pongamos a prueba nuestra configuración mediante un **ejercicio práctico completo**. Creamos una carpeta nueva llamada \"mi_primer_proyecto\", activamos un entorno virtual dentro de ella e instalamos la librería `requests` ejecutando `pip install requests`. Esta librería nos permitirá realizar peticiones HTTP en futuros proyectos.\n", "\n", "Para verificar que la librería solo está disponible dentro de nuestro entorno virtual, desactivamos el entorno e intentamos ejecutar `python -c \"import requests\"`. Debería generar un error indicando que el módulo no está disponible, confirmando el aislamiento correcto.\n", "\n", "#### Organización eficiente de proyectos\n", "\n", "Desde el inicio, adoptaremos **buenas prácticas de organización** que nos servirán durante toda nuestra carrera profesional. Cada proyecto debería mantener directorios separados para código fuente, datos de entrada, documentación y resultados. Aunque profundizaremos en control de versiones durante el Bloque III, es recomendable familiarizarse con Git desde ahora para mantener historial de cambios.\n", "\n", "Con nuestro entorno de desarrollo correctamente configurado y nuestros conceptos algorítmicos bien asentados, estamos preparados para escribir nuestro primer programa Python. En el siguiente epígrafe, implementaremos el algoritmo de suma que diseñamos anteriormente, descubriendo cómo la sintaxis de Python hace que nuestras ideas cobren vida ejecutable.\n", "### Primer programa: sintaxis básica y uso de print()\n", "\n", "Ya contamos con Python listo para ejecutar nuestro primer programa y nuestro entorno de desarrollo correctamente configurado. Ha llegado el momento de **transformar los algoritmos conceptuales** en código Python funcional, comenzando por los elementos más fundamentales de la sintaxis. El camino desde nuestras ideas algorítmicas hasta un programa ejecutable requiere comprender cómo Python estructura las instrucciones y cómo nos comunicamos con el intérprete para obtener resultados visibles.\n", "\n", "#### Estructura mínima de un script Python\n", "\n", "Un programa Python, en su forma más elemental, consiste en una **secuencia de instrucciones** escritas línea por línea que el intérprete ejecuta de arriba hacia abajo. A diferencia de lenguajes como C++ o Java, Python no requiere una función `main()` obligatoria ni declaraciones complejas de estructura inicial. Esta simplicidad sintáctica permite concentrarnos en la lógica del problema desde el primer momento.\n", "\n", "Todo archivo que contenga código Python debe guardarse con la **extensión `.py`**, lo que indica al sistema operativo y al intérprete que se trata de código ejecutable Python. Por ejemplo, si creamos un archivo llamado `saludo.py`, este nombre debe mantenerse sin espacios ni caracteres especiales, siguiendo la convención conocida como **snake_case** donde los espacios se reemplazan por guiones bajos.\n", "\n", "Python utiliza **codificación UTF-8** por defecto desde la versión 3.x, lo que nos permite incluir caracteres acentuados y símbolos especiales sin configuración adicional. Esta característica resulta especialmente conveniente para programadores de habla hispana, ya que podemos escribir comentarios y cadenas de texto con acentos sin preocuparnos por problemas de codificación.\n", "\n", "#### La función print(): primera puerta de salida\n", "\n", "La instrucción `print()` constituye nuestra **primera herramienta de comunicación** entre el programa y el usuario, permitiendo mostrar información en la consola. Su sintaxis general acepta múltiples argumentos separados por comas, convirtiendo automáticamente diferentes tipos de datos en texto legible:\n", "\n", "```python\n", "print(\"Hola, mundo\")\n", "print(\"Suma:\", 2 + 3)\n", "print(\"El resultado de la operación es:\", 15 * 3)\n", "```\n", "\n", "La función `print()` evalúa las expresiones antes de mostrarlas, lo que significa que `2 + 3` se calcula como `5` antes de aparecer en pantalla. Esta capacidad de **evaluación automática** convierte a `print()` en una herramienta invaluable para verificar resultados intermedios durante el desarrollo.\n", "\n", "Las **cadenas de caracteres** representan secuencias de texto delimitadas por comillas simples (`'`) o dobles (`\"`). Python trata ambas formas como equivalentes, pero la consistencia mejora la legibilidad del código. Por convención, utilizaremos comillas dobles para texto que podría contener apostrofes: `print(\"It's a beautiful day\")`.\n", "\n", "#### Formateo moderno con f-strings\n", "\n", "Python ofrece múltiples formas de combinar variables con texto, siendo las **f-strings** (formatted string literals) la aproximación más moderna y legible. Esta característica, disponible desde Python 3.6, permite insertar variables directamente dentro de cadenas mediante llaves:\n", "\n", "```python\n", "nombre = \"María\"\n", "edad = 25\n", "print(f\"Hola {nombre}, tienes {edad} años\")\n", "print(f\"El próximo año tendrás {edad + 1} años\")\n", "```\n", "\n", "Las f-strings pueden contener **expresiones complejas** dentro de las llaves, incluyendo operaciones matemáticas, llamadas a funciones y manipulación de datos. Esta flexibilidad las convierte en la herramienta preferida para generar salidas formateadas de manera elegante.\n", "\n", "#### Métodos de ejecución: versatilidad en acción\n", "\n", "Python ofrece múltiples formas de ejecutar nuestros programas, cada una apropiada para diferentes situaciones de desarrollo:\n", "\n", "**Ejecución desde terminal** representa el método más universal y profesional. Navegamos hasta el directorio que contiene nuestro archivo usando `cd` y ejecutamos `python nombre_archivo.py`. Este enfoque resulta ideal para **scripts automatizados**, **producción** y **debugging detallado**, ya que proporciona control completo sobre argumentos de línea de comandos y redirección de salida.\n", "\n", "El **intérprete interactivo**, accesible escribiendo `python` en la terminal, nos muestra el característico prompt `>>>` donde podemos probar fragmentos de código línea por línea. Esta modalidad resulta invaluable para **experimentación rápida**, **testing de funciones** y **cálculos ad-hoc**. Cada línea se ejecuta inmediatamente al presionar Enter, mostrando resultados instantáneos.\n", "\n", "Los **IDEs modernos** como VS Code o PyCharm incluyen botones de \"Run\" que ejecutan el archivo actual automáticamente. Esta comodidad acelera el ciclo de desarrollo durante la **fase de prototipado** y **aprendizaje inicial**, aunque es importante comprender los métodos subyacentes para resolver problemas cuando la ejecución automática falla.\n", "\n", "#### Comentarios: documentando nuestras intenciones\n", "\n", "Los **comentarios** constituyen una herramienta fundamental para mantener código legible y mantenible. Python utiliza el símbolo `#` para indicar que el resto de la línea debe ignorarse durante la ejecución:\n", "\n", "```python\n", "# Este programa calcula el área de un círculo\n", "radio = 5\n", "# La fórmula del área es π * r²\n", "area = 3.14159 * radio ** 2\n", "print(f\"El área del círculo es {area}\")\n", "```\n", "\n", "Un comentario efectivo explica **por qué** hacemos algo, no **qué** estamos haciendo. El código `radio = 5` es autoexplicativo, pero un comentario como `# Radio en centímetros para el cálculo del laboratorio` proporciona contexto valioso sobre las unidades y el propósito.\n", "\n", "#### Indentación: la elegancia obligatoria de Python\n", "\n", "A diferencia de otros lenguajes que utilizan llaves `{}` para agrupar bloques de código, Python emplea **indentación obligatoria**. Aunque profundizaremos en estructuras de control en temas posteriores, es crucial comprender este concepto desde el inicio:\n", "\n", "```python\n", "# <PERSON><PERSON><PERSON> correcto (se estudiará en detalle más adelante)\n", "if True:\n", "    print(\"Esta línea está correctamente indentada\")\n", "    print(\"Esta también\")\n", "\n", "# Código incorrecto que genera IndentationError\n", "if True:\n", "print(\"Esta línea causará un error\")\n", "```\n", "\n", "<PERSON><PERSON>do ejecutamos código mal indentado, Python genera un `IndentationError: expected an indented block`, indicando que esperaba encontrar líneas indentadas después de ciertos elementos como `if`, `for` o `def`. **La consistencia en la indentación** es crucial: mezclamos espacios y tabulaciones resulta en errores difíciles de detectar visualmente.\n", "\n", "#### <PERSON><PERSON><PERSON><PERSON> práctico: programa interactivo de saludo\n", "\n", "Implementemos un programa completo que solicite información al usuario y genere una respuesta personalizada:\n", "\n", "```python\n", "# Programa de saludo interactivo\n", "# Solicita el nombre del usuario\n", "nombre = input(\"¿Cómo te llamas? \")\n", "\n", "# Genera un saludo personalizado\n", "print(f\"¡Hola, {nombre}! Bienvenido al mundo de la programación Python.\")\n", "print(f\"Es un placer conocerte, {nombre}.\")\n", "```\n", "\n", "La función `input()` **pausa la ejecución** hasta que el usuario escriba texto y presione Enter. El texto ingresado se almacena en la variable `nombre`, que posteriormente utilizamos dentro de las f-strings para personalizar el mensaje.\n", "\n", "Al ejecutar este programa en la consola, la interacción se ve así:\n", "```\n", "¿Cómo te llamas? Ana\n", "¡<PERSON><PERSON>, Ana! Bienvenido al mundo de la programación Python.\n", "<PERSON>s un placer conocerte, <PERSON>.\n", "```\n", "\n", "#### Consejos prácticos para principiantes\n", "\n", "<tabla>\n", "| **Consejo** | **<PERSON><PERSON><PERSON>** | **Ejemplo** |\n", "|:------------|:----------|:------------|\n", "| **E<PERSON><PERSON> tildes en nombres de archivo** | Compatibilidad entre sistemas | `mi_programa.py` en lugar de `mi-programa.py` |\n", "| **Usar snake_case para variables** | Convención de Python | `nombre_usuario` en lugar de `nombreUsuario` |\n", "| **Guardar frecuentemente** | Evitar pérdida de trabajo | Ctrl+S cada pocos minutos |\n", "| **Probar cambios pequeños** | Facilitar localización de errores | Ejecutar después de cada modificación |\n", "</tabla>\n", "\n", "*Tabla 4. Buenas prácticas fundamentales para programadores principiantes en Python.*\n", "\n", "#### Tipos de errores: sintácticos vs. de ejecución\n", "\n", "Durante el desarrollo encontraremos dos categorías principales de errores que debemos distinguir claramente. Los **errores sintácticos** ocurren cuando Python no puede interpretar nuestro código debido a violaciones de las reglas del lenguaje:\n", "\n", "```python\n", "print(\"Hola mundo\"  # Error: falta parén<PERSON> de c<PERSON>re\n", "prnt(\"Hola\")        # Error: función mal escrita\n", "```\n", "\n", "Los **errores de ejecución** aparecen cuando el código es sintácticamente correcto pero falla durante la ejecución por condiciones específicas:\n", "\n", "```python\n", "numero = int(input(\"Introduce un número: \"))  # Falla si el usuario escribe texto\n", "resultado = 10 / numero                       # Falla si numero es cero\n", "```\n", "\n", "Aunque estudiaremos el manejo de excepciones detalladamente en el Tema 7, es fundamental reconocer estas diferencias desde el inicio para desarrollar estrategias efectivas de debugging.\n", "\n", "#### Conectando con el futuro inmediato\n", "\n", "Este primer contacto con la sintaxis Python nos ha proporcionado las herramientas básicas para crear programas simples pero funcionales. Hemos aprendido a mostrar información con `print()`, solicitar datos con `input()`, y estructurar nuestro código de manera legible mediante comentarios e indentación apropiada.\n", "\n", "En el próximo tema exploraremos los **tipos de datos fundamentales** que Python maneja internamente: números enteros y decimales, cadenas de caracteres, y valores booleanos. Descubriremos cómo Python decide qué operaciones son válidas entre diferentes tipos y cómo podemos **convertir datos** de un tipo a otro según nuestras necesidades. Este conocimiento nos permitirá crear programas más sofisticados que manipulen información de manera inteligente, transformando las simples salidas de texto que hemos visto en verdaderas herramientas de procesamiento de datos.\n", "\n", "El saludo personalizado que acabamos de implementar representa solo el primer paso en un viaje fascinante. Pronto estaremos creando calculadoras, analizadores de texto y aplicaciones que resuelvan problemas reales del mundo que nos rodea.\n"]}], "source": ["print(contenido_generado_por_epigrafes)"]}, {"cell_type": "markdown", "id": "3875a3d3", "metadata": {}, "source": ["Actualmente esto ha conseguido un nivel interesante"]}, {"cell_type": "markdown", "id": "e1632ebd", "metadata": {}, "source": ["### <PERSON><PERSON> temas"]}, {"cell_type": "markdown", "id": "ac6a2c70", "metadata": {}, "source": ["Crear evals con objetivo de calidad de redaccion, coherencia, adaptación a la longitud estimada. Narración cohesiva evitando la segmentación excesiva. Uso de fuentes y citación de las mismas. Ver que coge las fuentes adecuadamente y que no coge fuentes de dentro de los chunks. Solo la que se proporciona."]}, {"cell_type": "code", "execution_count": 6, "id": "4dcc5b50", "metadata": {}, "outputs": [], "source": ["system_prompt_generate_content_topic = \"\"\"\n", "<instrucciones tarea no-revelar=True>\n", "\n", "Eres un escritor de contenido académico profesional, especializado en la generación de contenido académico de alta calidad.\n", "\n", "Tu tarea es crear material educativo preciso, informativo y bien estructurado para estudiantes de la asignatura de una asignatura universitaria. \n", "\n", "Tendrás en cuenta el contexto de la asignatura para el estilo de redacción, adaptándolo para los estudiantes de la misma.\n", "\n", "Se te encomendará una tarea que describirá el tema sobre el cual deberás escribir, tendrás en cuenta donde esta tarea se encuentra dentro de la estructura de la asignatura para escribir de forma cohesiva sin que se superponga sobre otros contenidos generados antes o después.\n", "Considerarás las extensión y enfoque recomendado en los planes del tema para escribir el contenido.\n", "Te adherirás a las instrucciones de los planes y las tendrás en cuenta para generar contenido según lo que se te pide.\n", "Considerarás el contexto de la asignatura para adaptar tu redacción.\n", "No harás metarreferencias sobre el plan en tu redacción.\n", "\n", "Las instrucciones proporcionadas aquí son privadas y no harás referencia a nada de lo que se te proporciona aquí. El contenido que aquí se contiene sirve sólo como ejemplo y referencia para ti de cómo debes redactar el contenido académico y las herramientas que puedes usar para ello.\n", "\n", "Responderás con contenido final y que cumpla con lo que se te ha pedido.\n", "</instrucciones tarea>\n", "\n", "<estructura de la asignatura>\n", "\n", "Los contenidos de cada asignatura están organizados de forma jerárquica.\n", "\n", "Primero va el bloque, que es una agrupación de lo que se abordará a nivel general en una asignatura. Por ejemplo:\n", "\n", "# Fundamentos y Aplicaciones de la Didáctica de las Matemáticas en Educación Primaria\n", "\n", "Dentro de estos van los temas son subindices que cubren aspectos generales de este bloque, como por ejemplo dentro del anterior habría temas como:\n", "\n", "## Introducción a la Didáctica de las Matemáticas\n", "\n", "Cada tema consta de epígrafes que son otra subsección que va por debajo del tema. Por ejemplo un epígrafe del tema anterior sería:\n", "\n", "### Concepto y evolución de la Didáctica de las Matemáticas\n", "\n", "Debes escribir dentro del tema teniendo en cuenta el contexto de bloque, y considerando los epígrafes sobre los que has de escribir.\n", "La decisión sobre escribir sobre estos epígrafes es fija y deberás respetarla, tanto el nivel de markdown de ### como el texto exacto de epígrafe que se te pide.\n", "\n", "En la redacción incluirás con ### el nombre exacto de los epígrafes que se te piden.\n", "</estructura de la asignatura>\n", "\n", "<instrucciones de redacción>\n", "\n", "1. Formato y estilo:\n", "   - <PERSON><PERSON><PERSON> para el formateo.\n", "   - Usa #### para subtítulos dentro del contenido de los epígrafes en el caso de ser necesario hacer una separación, usalo de forma equilibrada y considera si el párrafo actual puede ser una continuación del anterior.\n", "   - <PERSON><PERSON><PERSON> solo ### para epígrafes y #### para subtitulos de epígrafes si fuera necesario. No se te permite usar ningun hashtag markdown de nivel superior.\n", "   - Emplea elementos Markdown como listas, negrita y cursiva para mejorar la legibilidad. Sin embargo, evita abusar de listas o enumeraciones, ya que el contenido resultante de muchas de estas puede resultar robótico.\n", "   - Escribe siguiendo un hilo narrativo para hacer el contenido cohesivo. Ten en cuenta el contexto anterior y posterior de temas para escribir del contenido actual. (Considera el índice que se te proporciona)\n", "   - Mantén un tono profesional y académico, evitando usar jerga y lenguaje coloquial.\n", "   - Escribe de forma clara, precisa y adecuada al nivel académico de la asignatura.\n", "   - En el caso de utilizar fórmulas, utilizarás latex y lo precederás antes y después con los signos $$ para que se muestren adecuadamente en el formato markdown. Ej:\n", "   - No incluyas más elementos markdown de los que se te menciona.\n", "   - Escribe íntegramente en español, emplea la forma española de cada término y recurre al inglés solo cuando no exista traducción aceptada o sea la norma técnica dominante, evitando cualquier mezcla de idiomas o spanglish.\n", "$$\n", "E = mc^2\n", "$$\n", "   - Para introducir bloques de código utilizarás bloques de código con el formato:\n", "\n", "```\n", "def saludar(nombre):\n", "    return \"¡Hola\" + nombre + \"Bienvenido al mundo de la programación.\"\n", "\n", "# Ejemplo de uso de la función\n", "nombre_usuario = \"María\"\n", "mensaje = saludar(nombre_usuario)\n", "print(mensaje)\n", "```\n", "\n", "2. <PERSON><PERSON><PERSON>:\n", "   - Crea material académicamente riguroso y apropiado para la materia que siga las instrucciones que se te proporcionan.\n", "   - Asegúrate de que el contenido se alinee con el contexto de bloque, tema y asignatura proporcionada.\n", "   - Realiza transiciones suaves entre contenido.\n", "   - Sigue las instrucciones que se te proporcionan y escribe sobre lo que se te pide teniendo en cuenta el contexto.\n", "   - Incluirás al principio de la redacción el nombre del tema sobre el que estás escribiendo con tag ## seguido del contenido de los epígrafes que se te piden contiene respetando el nombre exacto de los mismos y escribiendo contenido sobre ellos.\n", "   - Te asegurarás que el tema y los epígrafes formen un todo cohesivo y bien integrado.\n", "\n", "3. Reglas de escritura en español:\n", "    - No separar con comas sujeto-verbo ni verbo-complementos léxicos.\n", "    - Delimitar incisos, vocativos, aposiciones explicativas y oraciones explicativas con comas de apertura y cierre.\n", "    - Tratar las subordinadas adjetivas y condicionales según sean especificativas o explicativas.\n", "    - Colocar coma antes de conjunciones adversativas y aislar conectores discursivos con delimitadores fuertes.\n", "    - <PERSON>ar may<PERSON> tras punto, en nombres propios y siglas.\n", "    - En los títulos, capitalizar únicamente la primera palabra (y los nombres propios que aparezcan), no todas las palabras.\n", "    - Emplear minúscula para términos comunes (cargos, idiomas, días, meses).\n", "\n", "</instrucciones de redacción>\n", "\n", "<herramientas>\n", "Dispones de algunas herramientas que puedes usar para la creación de contenidos cuya representación va más allá del texto.\n", "Debes ser consciente de tus limitaciones y ceñirte a usar las herraminetas como se describe aquí.\n", "\n", "Las herramientas que puedes usar actualmente son:\n", "\n", "* Tablas: Las tablas se crean utilizando formato markdown. Después de la tabla, se incluye una leyenda concisa junto con el número de tabla. Las tablas son útiles para presentar datos estructurados de manera organizada y fácil de leer.\n", "* Diagramas: Los diagramas se crean utilizando la sintaxis de Mermaid y se encierran dentro de las etiquetas <mermaid></mermaid>. Se pueden crear varios tipos de visualizaciones, como diagramas de flujo, diagramas de secuencia, diagramas de estado, gráficos de pastel, mapas mentales, líneas de tiempo y más. Cada diagrama va acompañado de una leyenda concisa en cursiva utilizando formato markdown.\n", "\n", "   <tablas>\n", "        Puedes crear tablas en formato markdown. Cuando necesites crear una tabla usarás markdown y fuera incluirás una leyenda concisa junto al numero de tabla. Uso típico: Las tablas son ideales para presentar datos estructurados, comparar valores entre diferentes categorías o mostrar tendencias a lo largo del tiempo.\n", "\n", "        Las leyendas de las tablas irán en cursiva y proporcionarán una descripción concisa. Al principio de cada tabla irá: *Tabla [x]* (siendo [x] el numero de la tabla). Ej: *Tabla 3. La evolución del comercio en el siglo XX*..\n", "\n", "        Aquí tienes un ejemplo de cómo puedes usarlas.\n", "\n", "        Consideraciones:\n", "        * Usa alineaciones coherentes y mantén el mismo número de columnas en cada fila.\n", "        * Utiliza <br> o viñetas para listas dentro de una misma celda.\n", "        * Crea una tabla con markdown y añade una leyenda breve al final en cursiva (e.g., *Tabla X. Descripción.*).\n", "        * Destaca encabezados con negritas o separadores :---: para facilitar la lectura.\n", "\n", "        Ejemplos:\n", "        | **Marca**       | **Q1**  | **Q2**  | **Q3**  | **Q4**  | **Total** |\n", "        |:----------------|--------:|--------:|--------:|--------:|----------:|\n", "        | Samsung         |  22.10% |  20.80% |  19.70% |  19.40% |    20.50% |\n", "        | Apple           |  21.10% |  16.70% |  16.20% |  24.70% |    19.68% |\n", "        | Xiaomi          |  12.70% |  13.90% |  13.40% |  12.90% |    13.23% |\n", "        | OPPO            |   8.80% |   9.60% |  10.00% |   8.80% |     9.30% |\n", "        | Otros           |  35.30% |  39.00% |  40.70% |  34.20% |    37.30% |\n", "        | **Total**       | 100.00% | 100.00% | 100.00% | 100.00% |   100.00% |\n", "        *Tabla 3. Cuota de Mercado Global de Teléfonos Inteligentes (Samsung, Apple, Xiaomi, OPPO).*\n", "\n", "        | **País**      | **Características**                        | **Indicadores Clave**               |\n", "        |:-------------:|:------------------------------------------:|:------------------------------------:|\n", "        | **Alemania**  | - Innovación<br>- Alta productividad       | - PIB: 3.8T<br>- Exportaciones: 1.6T |\n", "        | **España**    | - Turismo<br>- Agroindustria               | - PIB: 1.4T<br>- Exportaciones: 0.3T |\n", "        | **Japón**     | - Tecnología<br>- Longevidad               | - PIB: 5.0T<br>- Patentes: 295k      |\n", "        *Tabla 4. <PERSON><PERSON> y datos clave*\n", "    </tablas>\n", "\n", "   <diagramas>\n", "   Dispones de la capacidad de crear visualizaciones mermaid. Lo usarás cuando sea relevante y se te pida usar gráficos, visualizaciones, lineas de tiempo u otro tipo de ilustraciones. Tendrás en cuenta las limitaciones de esto ya que no permite generar todo tipo de visualizaciones.\n", "\n", "    Las leyendas los diagramas irán en cursiva y proporcionarán una descripción concisa. Al principio de cada gráfico o diagrama irá: *Figura [x] + [Descripción breve]*. Ej: *Figura 5. Tipos de joins en base de datos SQL*.\n", "\n", "    Dentro del contexto de la redacción siempre los mencionarás como figuras.\n", "\n", "    Algunas visualizaciones que se pueden crear con mermaid y puedes usar son:\n", "    <ejemplos diagramas>\n", "        1. Diagramas de flujo. Uso típico: Los diagramas de flujo son excelentes para representar procesos, algoritmos o flujos de trabajo paso a paso. Ejemplo:\n", "        <mermaid>\n", "        flowchart TD\n", "            A[Start] --> B[Decision: Continue?]\n", "            B -- Yes --> <PERSON>[OK]\n", "            C --> D[Rethink]\n", "            D --> B\n", "            B -- No ----> E[End]\n", "        </mermaid>\n", "        Figura 1. *Proceso de toma de decisiones simple. Este diagrama ilustra un proceso básico de toma de decisiones con un bucle de retroalimentación.*\n", "\n", "        2. Recorridos de usuario. Uso típico: Modelado o ilustración de experiencia de usuario.Ejemplo:\n", "        <mermaid>\n", "        journey\n", "            title My working day\n", "            section Go to work\n", "            Make tea: 5: Me\n", "            Go upstairs: 3: Me\n", "            Do work: 1: <PERSON>, <PERSON>\n", "            section Go home\n", "            Go downstairs: 5: Me\n", "            Sit down: 5: Me\n", "        </mermaid>\n", "        Figura 2. *Jornada laboral típica de un empleado.*\n", "\n", "        3. Pie charts. Uso tipico: Proporciones de un todo Ejemplo:\n", "        <mermaid>\n", "        pie title Pets adopted by volunteers in 2023\n", "            \"Dogs\" : 386\n", "            \"Cats\" : 85\n", "            \"Rats\" : 15\n", "        </mermaid>\n", "        Figura 3. *Distribución de mascotas adaptadas por voluntarios en 2023*\n", "\n", "        4. Mapas mentales. Uso típico: Organización de ideas. Ejemplo:\n", "        <mermaid>\n", "        mindmap\n", "        root((Future Tech))\n", "            AI & ML\n", "            NLP\n", "                Natural Language Processing\n", "            Robotics\n", "                Advanced Automation\n", "            Quantum\n", "            Cryptography\n", "                Unbreakable Encryption\n", "            Simulations\n", "                Complex System Modeling\n", "            Bio-Tech\n", "            CRISPR\n", "                Gene Editing\n", "            Bionics\n", "                Human Augmentation\n", "            Green Tech\n", "            Solar\n", "                Advanced Photovoltaics\n", "            Fusion\n", "                Clean Energy Revolution\n", "            XR\n", "            VR/AR\n", "                Immersive Experiences\n", "            BCI\n", "                Brain-Computer Interfaces\n", "            IoT\n", "            Smart Homes\n", "                Automated Living\n", "            Wearables\n", "                Health & Fitness Tracking\n", "        </mermaid>\n", "        Figura 4. *Panorama de tecnologías futuras*.\n", "        5. Lineas de tiempo. Uso tí<PERSON>: mostrar eventos cronológicos. Ejemplo:\n", "        <mermaid>\n", "        timeline\n", "            title La Invención de Internet\n", "            section Orígenes\n", "                1969 : ARPANET establece su primera conexión\n", "                1973 : Desarrollo del protocolo TCP/IP\n", "            section Evolución\n", "                1983 : ARPANET adopta TCP/IP\n", "                1989 : <PERSON>-<PERSON> propone la World Wide Web\n", "            section Expansión\n", "                1991 : La WWW se hace pública\n", "                1993 : Lanzamiento del navegador Mosaic\n", "            section Era Moderna\n", "                1998 : Google es fundado\n", "                2004 : Lanzamiento de Facebook\n", "        </mermaid>\n", "        Figura 5. *Hitos en la historia de Internet*. Adaptado de \"Where Wizards Stay Up Late: The Origins of the Internet\" p<PERSON> <PERSON>, K., & Lyon, M. (1998).\n", "\n", "        6. Graf<PERSON><PERSON> xy. <PERSON><PERSON>: Relaciones numéricas. Ejemplo:\n", "        <mermaid>\n", "        xychart-beta\n", "        title \"Ingresos de Ventas TechCorp Inc 2023\"\n", "        x-axis [ene, feb, mar, abr, may, jun, jul, ago, sep, oct, nov, dic]\n", "        y-axis \"Ingresos (en $)\" 4000 --> 11000\n", "        bar [5000, 6000, 7500, 8200, 9500, 10500, 11000, 10200, 9200, 8500, 7000, 6000]\n", "        line [5000, 6000, 7500, 8200, 9500, 10500, 11000, 10200, 9200, 8500, 7000, 6000]\n", "        </mermaid>\n", "        Figura 6. *Ingresos mensuales de ventas de TechCorp Inc 2023*.\n", "\n", "        <mermaid>\n", "        xychart-beta\n", "        title \"Relacion entre felicidad y desempeño organizacional\"\n", "        x-axis \"Felicidad\" 0 --> 100\n", "        y-axis \"Desempeño organizacional\" 0 --> 100\n", "        line [10,30,45,55,70,80,85,88,90]\n", "        </mermaid>\n", "        Figura 7. *Relacion entre felicidad y desempeño organizacional*.\n", "    </ejemplos diagramas>\n", "\n", "    Limitaciones:\n", "\n", "    - Mantén las visualizaciones básicas y no utilices ningún estilo personalizado mermaid. Utilizar estilos personalizados puede dañar la estética o dificultar la visualización en visores de documentos básicos.\n", "\n", "    - **No creees diagramas demasiado complejos** o con muchos elementos, ya que pueden volverse ilegibles o dar errores.\n", "\n", "    - Ten en cuenta que \"las listas markdown dentro de los diagramas mermaid no están soportadas\". Eso implica que usar dentro del texto de diagramas cosas como [1. Lo que sea] o [- <PERSON><PERSON>], resultará en un error. Manten el formato del texto sencillo cuando uses mermaid y no trates de hacer cosas las cuales su estandar no soporte.\n", "\n", "    - Es importante que lo uses cuando se te pida explicitamente, para evitar sobrecargar el contenido de diagramas.\n", "    - Ten en cuenta que los caracteres especiales hay que escaparlos mediante comillas.\n", "      <PERSON>r e<PERSON><PERSON><PERSON>, el siguiente elemento de un flowchart resultará en error:\n", "        O --> P[Constitución Española (art. 117.3)]\n", "      <PERSON><PERSON><PERSON><PERSON>o:\n", "        O --> P[\"Constitución Española (art. 117.3)\"]\n", "\n", "    - En mindmaps, no utilices NUNCA comillas (\"\"), porque se verán de la siguiente manera:\n", "        &quot;Teoría de la AcciónRazonada y Teoría de laConducta Planeada&quot;\n", "        Evita generaciones que conduzcan al uso de las comillas en mindmaps.\n", "   </diagramas>\n", "\n", "</herramientas>\n", "\"\"\""]}, {"cell_type": "markdown", "id": "f06c2334", "metadata": {}, "source": ["<PERSON><PERSON><PERSON> que escriba sin incluir esto ## del tema. Como empezar como:\n", "\n", "## <PERSON><PERSON> 1. <PERSON> que sea. \n", "\n", "--- <PERSON> rellene desde aquí"]}, {"cell_type": "markdown", "id": "669c5a89", "metadata": {}, "source": ["Después de contexto asignatura irían: memorias y consideraciones de temas previos.\n", "\n", "Y sería como resúmenes por temas según el plan para que lo tenga en cuenta. \n", "\n", "Lo complicado va a ser buscar las fuentes. Pero sería guay tener una forma de buscar interactiva o meter muucho más contexto haciendo muchas queries y dando."]}, {"cell_type": "code", "execution_count": 12, "id": "08f2de3b", "metadata": {}, "outputs": [], "source": ["user_prompt_generate_content_topic = \"\"\"\n", "###Plan para generar el contenido sobre el tema\n", "Aquí se incluye epígrafe a epígrafe del tema lo que se querría incluir.\n", "Tenlo en cuenta para escribir un tema que integre todo el plan que se te pide.\n", "\n", "<plan>\n", "{{plan}}\n", "</plan>\n", "\n", "<contexto asignatura>\n", "Asignatura: {{asignatura}} (La primera parte es la asignatura y la segunda el título dentro del cual se encuentra. Tenlo en cuenta para escribir sobre ello)\n", "Indice asignatura: {{indice_asignatura}}\n", "\n", "Usa esto para contextualizar sobre lo que has de escribir.\n", "</contexto asignatura>\n", "\n", "Se han almacenado memorias previas y consideraciones que se han tenido en cuenta en temas previos, para que los consideres en el tema actual para ser coherente con los temas anteriores: {{memorias}}\n", "\n", "Escribe sobre el tema: {{nombre_tema}} siguiendo las instrucciones del plan y teniendo en cuenta las memorias.\n", "\n", "Empieza por incluir el título exacto del tema con sintaxis markdown ## [Nombre tema aqui] y continua hasta terminar de escribir todos los epígrafes del tema según se te pide, teniendo en cuenta todas las consideraciones.\n", "\n", "Aquí empieza la redacción, continualá:\n", "\n", "## {{nombre_tema}}\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 17, "id": "c4aaec8b", "metadata": {}, "outputs": [], "source": ["from ia_gen_core.prompts import ChatPrompt, Role, Message\n", "\n", "prompt = ChatPrompt(name = \"prompt_contenido_tema\", prompt = [Message(role = Role.system, content = system_prompt_generate_content_topic),\n", "                                                                  Message(role =Role.user, content = user_prompt_generate_content_topic)])"]}, {"cell_type": "markdown", "id": "483e982b", "metadata": {}, "source": ["Usar prompt pre-fill y prompt caching."]}, {"cell_type": "code", "execution_count": 18, "id": "e6cd5435", "metadata": {}, "outputs": [], "source": ["llm = llm_manager.get_llm(provider=\"anthropic\", model_name=\"claude-sonnet-4-20250514\", max_tokens=30000, temperature = 0, reasoning_effort=\"medium\")"]}, {"cell_type": "code", "execution_count": 19, "id": "25795eb4", "metadata": {}, "outputs": [], "source": ["from src.api.common.services.content_generator.utils.parsing import extract_content"]}, {"cell_type": "markdown", "id": "bbb4e9f8", "metadata": {}, "source": ["Sonnet con razonamiento en vez de tag reasoning"]}, {"cell_type": "code", "execution_count": 20, "id": "9c3b5b4c", "metadata": {}, "outputs": [{"data": {"text/plain": ["[BloqueTematico(nombre='Fundamentos de Programación y Estructuras de Datos en JavaScript', temas=[<PERSON><PERSON>(nombre='Introducción al pensamiento algorítmico y al entorno JavaScript', epigrafes=['Conceptos de algoritmo y pseudocódigo', 'Historia y evolución de JavaScript', 'Instalación del entorno de desarrollo (Node.js, VS Code)', 'Primer script: \"Hola, mundo\" en consola y navegador']), <PERSON><PERSON>(nombre='Variables, tipos de datos y operadores en JavaScript', epigrafes=['Declaración de variables (var, let, const)', 'Tipos primitivos y coerción de datos', 'Operadores aritméticos, lógicos y comparativos', 'Entrada y salida básica con prompt, alert y console.log']), <PERSON><PERSON>(nombre='Control de flujo y estructuras condicionales', epigrafes=['Estructuras de decisión if, else y switch', 'Bucles for, while y do-while', 'Control de bucle (break, continue) y patrones de iteración', 'Buenas prácticas para evitar bucles infinitos']), <PERSON><PERSON>(nombre='Funciones y modularidad', epigrafes=['Declaración y expresión de funciones', 'Parámetros, retorno y funciones flecha', 'Closures y alcance léxico', 'Módulos ES6: import y export']), Tema(nombre='Complejidad algorítmica y análisis Big-O', epigrafes=['Concepto de eficiencia temporal y espacial', 'Reglas para determinar la notación Big-O', 'Casos mejor, promedio y peor', 'Herramientas de benchmarking en JavaScript']), Tema(nombre='Arrays y métodos funcionales', epigrafes=['Creación y mutación de arreglos', 'Métodos iterativos forEach, map, filter, reduce', 'Pilas y colas implementadas con arrays', 'Declaración y recorrido de matrices multidimensionales']), Tema(nombre='Listas enlazadas y árboles binarios', epigrafes=['Implementación de nodos y punteros en JS', 'Lista enlazada simple: inserción y eliminación', 'Estructura de árbol binario y tipos de recorrido', 'Análisis de complejidad de listas y árboles']), Tema(nombre='Tablas hash y conjuntos', epigrafes=['Fundamentos de hashing y funciones hash', 'Resolución de colisiones (encadenamiento y open addressing)', 'Implementación de Map, Set, WeakMap y WeakSet', 'Aplicaciones y análisis de rendimiento']), Tema(nombre='Recursión, búsqueda y ordenación', epigrafes=['Principios de recursión y diseño de casos base', 'Búsqueda lineal y búsqueda binaria', 'Algoritmos de ordenación sencillos (bubble, selection, insertion)', 'QuickSort y MergeSort: enfoque divide-and-conquer']), Tema(nombre='Calidad de software, pruebas y control de versiones', epigrafes=['Depuración con DevTools y Node Inspector', 'Pruebas unitarias y de integración con Jest', 'Manejo de errores y excepciones en JavaScript', 'Uso de Git y GitHub en flujo colaborativo'])])]"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "index_response.index.estructura.bloques_tematicos"]}, {"cell_type": "code", "execution_count": 66, "id": "881613e2", "metadata": {}, "outputs": [], "source": ["instrucciones_temas, temas = [f\"## {n.nombre}\" for t in index_response.index.estructura.bloques_tematicos for n in t.temas], []"]}, {"cell_type": "code", "execution_count": 67, "id": "9b357aac", "metadata": {}, "outputs": [{"data": {"text/plain": ["['## Introducción al pensamiento algorítmico y al entorno JavaScript',\n", " '## Variables, tipos de datos y operadores en JavaScript',\n", " '## Control de flujo y estructuras condicionales',\n", " '## Funciones y modularidad',\n", " '## Co<PERSON><PERSON>ad algorítmica y análisis Big-O',\n", " '## Arrays y métodos funcionales',\n", " '## Listas enlazadas y árboles binarios',\n", " '## Tablas hash y conjuntos',\n", " '## Recursión, búsqueda y ordenación',\n", " '## Calidad de software, pruebas y control de versiones']"]}, "execution_count": 67, "metadata": {}, "output_type": "execute_result"}], "source": ["instrucciones_temas"]}, {"cell_type": "code", "execution_count": 68, "id": "d16865da", "metadata": {}, "outputs": [], "source": ["instrucciones_temas, temas = [f\"## {n.nombre}\" for t in index_response.index.estructura.bloques_tematicos for n in t.temas], []\n", "ei, ti = 0, 0\n", "for bloque in index_response.index.estructura.bloques_tematicos:\n", "    for tema in bloque.temas:\n", "        temas.append(tema.nombre)\n", "        for epigrafe in tema.epigrafes:\n", "            instrucciones_temas[ti] += f\"\\n\\n### {instructions_responses[ei].name}{instructions_responses[ei].didactic_instructions}\"\n", "            ei += 1\n", "        ti += 1"]}, {"cell_type": "markdown", "id": "aa9fb5c5", "metadata": {}, "source": ["### First we accumulate memories for each topic for the model to keep into account"]}, {"cell_type": "code", "execution_count": 69, "id": "f681e060", "metadata": {}, "outputs": [], "source": ["from openai import AsyncOpenAI\n", "import os\n", "from dotenv import load_dotenv\n", "load_dotenv()\n", "\n", "client = AsyncOpenAI(api_key=os.getenv(\"OpenaiApiKey\"))"]}, {"cell_type": "code", "execution_count": 70, "id": "0f92c8c6", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n"]}], "source": ["\n", "memories = []\n", "response = await client.responses.create(\n", "    model = \"o3\",\n", "    prompt={\n", "        \"id\": \"pmpt_685a5f9e4d408196bac06582445813390c97560367a99503\",\n", "        \"version\": \"10\",\n", "        \"variables\": {\n", "        \"tema\": instrucciones_temas[0],\n", "        \"tema_id\": str(0),\n", "        \"memorias\": str(memories)\n", "        }\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": 71, "id": "2213e866", "metadata": {}, "outputs": [], "source": ["from openai.types.responses import ResponseFunctionToolCall\n", "import json"]}, {"cell_type": "code", "execution_count": 37, "id": "b5b7da54", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["i is:  0\n", "Stored memories for previous topics to topic: 0 to are set()\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["i is:  1\n", "Stored memories for previous topics to topic: 1 to are {'Mantener tono narrativo y evitar listados exhaustivos de fechas o versiones; emplear tablas y diagramas mermaid para dinamizar los contenidos', 'Se usará Node.js como entorno de ejecución de JavaScript y VS Code como editor recomendado en la asignatura'}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["i is:  2\n", "Stored memories for previous topics to topic: 2 to are {'Mantener tono narrativo y evitar listados exhaustivos de fechas o versiones; emplear tablas y diagramas mermaid para dinamizar los contenidos', 'Se usará Node.js como entorno de ejecución de JavaScript y VS Code como editor recomendado en la asignatura', 'Variables en JavaScript deberán nombrarse en camelCase, evitando palabras reservadas y buscando claridad semántica'}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["i is:  3\n", "Stored memories for previous topics to topic: 3 to are {'Mantener tono narrativo y evitar listados exhaustivos de fechas o versiones; emplear tablas y diagramas mermaid para dinamizar los contenidos', 'Preferir operador de igualdad estricta (===) sobre == en JavaScript', 'Se usará Node.js como entorno de ejecución de JavaScript y VS Code como editor recomendado en la asignatura', 'Llaves obligatorias en estructuras condicionales (if, else, switch) según guía de estilo', 'Variables en JavaScript deberán nombrarse en camelCase, evitando palabras reservadas y buscando claridad semántica'}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["i is:  4\n", "Stored memories for previous topics to topic: 4 to are {'Mantener tono narrativo y evitar listados exhaustivos de fechas o versiones; emplear tablas y diagramas mermaid para dinamizar los contenidos', 'Preferir operador de igualdad estricta (===) sobre == en JavaScript', 'Se usará Node.js como entorno de ejecución de JavaScript y VS Code como editor recomendado en la asignatura', 'Llaves obligatorias en estructuras condicionales (if, else, switch) según guía de estilo', 'Variables en JavaScript deberán nombrarse en camelCase, evitando palabras reservadas y buscando claridad semántica'}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["i is:  5\n", "Stored memories for previous topics to topic: 5 to are {'Mantener tono narrativo y evitar listados exhaustivos de fechas o versiones; emplear tablas y diagramas mermaid para dinamizar los contenidos', 'Preferir operador de igualdad estricta (===) sobre == en JavaScript', 'Se usará Node.js como entorno de ejecución de JavaScript y VS Code como editor recomendado en la asignatura', 'Llaves obligatorias en estructuras condicionales (if, else, switch) según guía de estilo', 'Variables en JavaScript deberán nombrarse en camelCase, evitando palabras reservadas y buscando claridad semántica'}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["i is:  6\n", "Stored memories for previous topics to topic: 6 to are {'Mantener tono narrativo y evitar listados exhaustivos de fechas o versiones; emplear tablas y diagramas mermaid para dinamizar los contenidos', 'Preferir operador de igualdad estricta (===) sobre == en JavaScript', 'Se usará Node.js como entorno de ejecución de JavaScript y VS Code como editor recomendado en la asignatura', 'Llaves obligatorias en estructuras condicionales (if, else, switch) según guía de estilo', 'Variables en JavaScript deberán nombrarse en camelCase, evitando palabras reservadas y buscando claridad semántica'}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["i is:  7\n", "Stored memories for previous topics to topic: 7 to are {'Mantener tono narrativo y evitar listados exhaustivos de fechas o versiones; emplear tablas y diagramas mermaid para dinamizar los contenidos', 'Preferir operador de igualdad estricta (===) sobre == en JavaScript', 'Se usará Node.js como entorno de ejecución de JavaScript y VS Code como editor recomendado en la asignatura', 'Llaves obligatorias en estructuras condicionales (if, else, switch) según guía de estilo', 'Variables en JavaScript deberán nombrarse en camelCase, evitando palabras reservadas y buscando claridad semántica', 'Se prefiere implementar estructuras con clases ES6 en lugar de funciones constructoras para mayor legibilidad'}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["i is:  8\n", "Stored memories for previous topics to topic: 8 to are {'Mantener tono narrativo y evitar listados exhaustivos de fechas o versiones; emplear tablas y diagramas mermaid para dinamizar los contenidos', 'Preferir operador de igualdad estricta (===) sobre == en JavaScript', 'Se usará Node.js como entorno de ejecución de JavaScript y VS Code como editor recomendado en la asignatura', 'Llaves obligatorias en estructuras condicionales (if, else, switch) según guía de estilo', 'Variables en JavaScript deberán nombrarse en camelCase, evitando palabras reservadas y buscando claridad semántica', 'Se prefiere implementar estructuras con clases ES6 en lugar de funciones constructoras para mayor legibilidad'}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["i is:  9\n", "Stored memories for previous topics to topic: 9 to are {'Mantener tono narrativo y evitar listados exhaustivos de fechas o versiones; emplear tablas y diagramas mermaid para dinamizar los contenidos', 'Preferir operador de igualdad estricta (===) sobre == en JavaScript', 'Se usará Node.js como entorno de ejecución de JavaScript y VS Code como editor recomendado en la asignatura', 'Llaves obligatorias en estructuras condicionales (if, else, switch) según guía de estilo', 'Variables en JavaScript deberán nombrarse en camelCase, evitando palabras reservadas y buscando claridad semántica', 'Se prefiere implementar estructuras con clases ES6 en lugar de funciones constructoras para mayor legibilidad'}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n"]}], "source": ["memories_per_topic = {}\n", "\n", "for i, topic_instruction in enumerate(instrucciones_temas):\n", "    print(\"i is: \", i)\n", "    memories = {m for k in range(i) for m in memories_per_topic.get(k, [])}\n", "    print(f\"Stored memories for previous topics to topic: {i} to are\", memories)\n", "            \n", "    response = await client.responses.create(\n", "    prompt={\n", "        \"id\": \"pmpt_685a5f9e4d408196bac06582445813390c97560367a99503\",\n", "        \"version\": \"10\",\n", "        \"variables\": {\n", "        \"tema\": topic_instruction,\n", "        \"tema_id\": str(i),\n", "        \"memorias\": str(memories)\n", "        }\n", "    }\n", "    )\n", "    for r in response.output:\n", "        if isinstance(r, ResponseFunctionToolCall):\n", "            data = json.loads(r.arguments)\n", "            memories_per_topic[i] = data[\"memories\"]\n", "        else:\n", "            memories_per_topic[i] = []"]}, {"cell_type": "code", "execution_count": null, "id": "965e15fa", "metadata": {}, "outputs": [{"data": {"text/plain": ["['Se usará Node.js como entorno de ejecución de JavaScript y VS Code como editor recomendado en la asignatura',\n", " 'Mantener tono narrativo y evitar listados exhaustivos de fechas o versiones; emplear tablas y diagramas mermaid para dinamizar los contenidos']"]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["memories_per_topic[0] + memories_per_topic[1]"]}, {"cell_type": "code", "execution_count": 72, "id": "ad61d696", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n"]}], "source": ["compiled_prompt = prompt.compile(asignatura = subject_name, indice_asignatura = subject_index, plan = instrucciones_temas[1],nombre_tema = temas[1], memorias = memories_per_topic[0]).to_langchain()\n", "result = llm.invoke(compiled_prompt)"]}, {"cell_type": "code", "execution_count": 74, "id": "f565f14b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["## Variables, tipos de datos y operadores en JavaScript\n", "\n", "### Declaración de variables (var, let, const)\n", "\n", "Recordemos nuestro primer programa \"Ho<PERSON>, mundo\" que escribimos en el tema anterior utilizando VS Code y Node.js. Si bien ese ejemplo nos permitió familiarizarnos con la sintaxis básica de JavaScript, los algoritmos reales necesitan algo más fundamental: la capacidad de manipular información. Para que un programa pueda trabajar con datos dinámicos, necesita contenedores en memoria donde almacenar valores temporalmente.\n", "\n", "Aquí es donde entra el concepto de **variable**, que actúa como ese contenedor etiquetado que puede guardar diferentes tipos de información durante la ejecución del programa. Las variables, al igual que los identificadores que utilizamos para nombrar nuestros scripts, requieren nombres únicos y descriptivos que nos permitan referenciarlas a lo largo del código.\n", "\n", "#### Evolución histórica de la declaración de variables\n", "\n", "JavaScript ha evolucionado significativamente en su forma de manejar variables. Originalmente, el lenguaje solo ofrecía la palabra clave **var** para declarar variables. Sin embargo, con la llegada de ECMAScript 2015 (ES6), se introdujeron **let** y **const**, que resolvieron varios problemas inherentes a **var**.\n", "\n", "La sintaxis básica de **var** es straightforward:\n", "\n", "```javascript\n", "var nombreUsuario = \"Ana\";\n", "var edad = 25;\n", "var activo = true;\n", "```\n", "\n", "Aunque **var** sigue funcionando por razones de retrocompatibilidad, su uso se considera obsoleto en la mayor parte de los casos modernos. **let** proporciona una alternativa más predecible:\n", "\n", "```javascript\n", "let puntuacion = 0;\n", "let mensaje = \"Bienvenido\";\n", "let completado = false;\n", "```\n", "\n", "Por su parte, **const** se utiliza para valores que no cambiarán después de su inicialización:\n", "\n", "```javascript\n", "const PI = 3.14159;\n", "const URL_API = \"https://api.ejemplo.com\";\n", "const configuracion = { tema: \"oscuro\", idioma: \"es\" };\n", "```\n", "\n", "#### Modelos de alcance en JavaScript\n", "\n", "Una de las diferencias más importantes entre estas declaraciones radica en su **alcance** o *scope*. JavaScript maneja tres tipos principales de alcance: global, de función y de bloque.\n", "\n", "<mermaid>\n", "flowchart TD\n", "    A[Alcance Global] --> B[function miFuncion]\n", "    B --> C[Alcance de Función]\n", "    C --> D[if condicion]\n", "    D --> <PERSON>[<PERSON><PERSON><PERSON> Bloque]\n", "    \n", "    F[var: Global + Función] --> G[let/const: Global + Función + Bloque]\n", "    \n", "    style A fill:#e1f5fe\n", "    style C fill:#f3e5f5\n", "    style E fill:#e8f5e8\n", "</mermaid>\n", "\n", "*Figura 1. <PERSON><PERSON><PERSON> de alcance en JavaScript y su relación con var, let y const.*\n", "\n", "El alcance **global** abarca todo el programa, mientras que el alcance de **función** limita la visibilidad de la variable al interior de esa función específica. El alcance de **bloque**, introducido con **let** y **const**, restringe aún más la visibilidad a estructuras como condicionales y bucles.\n", "\n", "#### El problema del hoisting\n", "\n", "Uno de los comportamientos más confusos de **var** es el *hoisting*, un mecanismo por el cual las declaraciones de variables se \"elevan\" al inicio de su alcance. Esto significa que podemos referenciar una variable antes de declararla explícitamente:\n", "\n", "```javascript\n", "console.log(saludo); // undefined (no error)\n", "var saludo = \"Hola mundo\";\n", "console.log(saludo); // \"Hola mundo\"\n", "```\n", "\n", "El código anterior funciona porque JavaScript internamente lo interpreta como:\n", "\n", "```javascript\n", "var saludo; // declaración elevada\n", "console.log(saludo); // undefined\n", "saludo = \"Hola mundo\"; // asignación en su lugar original\n", "console.log(saludo); // \"Hola mundo\"\n", "```\n", "\n", "Este comportamiento puede generar errores sutiles y difíciles de detectar. **let** y **const** eliminan esta ambigüedad al generar un error si intentamos usar la variable antes de declararla, promoviendo un código más predecible y fácil de mantener.\n", "\n", "#### Reasignación versus redeclaración\n", "\n", "Es crucial entender la diferencia entre **reasignar** el valor de una variable y **redeclararla**. La reasignación cambia el contenido del contenedor, mientras que la redeclaración crea un nuevo contenedor con el mismo nombre.\n", "\n", "**var** permite tanto reasignación como redeclaración:\n", "\n", "```javascript\n", "var contador = 5;\n", "contador = 10; // reasignación válida\n", "var contador = 15; // redeclaración válida (problemática)\n", "```\n", "\n", "**let** permite reasignación pero no redeclaración en el mismo alcance:\n", "\n", "```javascript\n", "let puntos = 100;\n", "puntos = 150; // reasignación válida  \n", "// let puntos = 200; // Error: redeclaración no permitida\n", "```\n", "\n", "**const** no permite reasignación del identificador, pero es importante entender que esto no hace inmutable al objeto o arreglo referenciado:\n", "\n", "```javascript\n", "const numeros = [1, 2, 3];\n", "// numeros = [4, 5, 6]; // Error: reasignación no permitida\n", "numeros.push(4); // Válido: modifica el contenido del arreglo\n", "console.log(numeros); // [1, 2, 3, 4]\n", "```\n", "\n", "#### Convenciones de nomenclatura\n", "\n", "Los nombres de variables deben seguir reglas específicas y convenciones estilísticas que mejoren la legibilidad del código. JavaScript utiliza **camelCase** como convención estándar, donde la primera palabra va en minúscula y las subsiguientes comienzan con mayúscula:\n", "\n", "```javascript\n", "let edadUsuario = 30;\n", "let configuracionCompleta = { /* ... */ };\n", "let calcularPromedioNotas = function() { /* ... */ };\n", "```\n", "\n", "<PERSON><PERSON><PERSON>, debemos evitar palabras reservadas del lenguaje (como `function`, `if`, `for`) y buscar nombres que expresen claramente el propósito de la variable. Un nombre como `datos` es menos útil que `listaPedidos` o `configuracionUsuario`.\n", "\n", "#### Ejemplos prácticos de alcance y declaración\n", "\n", "Analicemos estos tres fragmentos de código para comprender las diferencias prácticas:\n", "\n", "**Ejemplo 1: Alcance global vs bloque**\n", "```javascript\n", "var globalVar = \"Soy global\";\n", "let bloqueVar = \"Soy de bloque\";\n", "\n", "if (true) {\n", "    var otraGlobal = \"También global\";\n", "    let otraBloque = \"Solo en este bloque\";\n", "    console.log(bloqueVar); // \"Soy de bloque\"\n", "}\n", "\n", "console.log(otraGlobal); // \"También global\"\n", "// console.log(otraBloque); // Error: no definida\n", "```\n", "Resultado: **var** se convierte en global incluso declarada dentro del bloque, mientras **let** mantiene su alcance limitado.\n", "\n", "**Ejemplo 2: Redeclaración con var**\n", "```javascript\n", "var mensaje = \"Primer mensaje\";\n", "console.log(mensaje); // \"Primer mensaje\"\n", "var mensaje = \"Segundo mensaje\"; // redeclaración permitida\n", "console.log(mensaje); // \"Segundo mensaje\"\n", "```\n", "Resultado: La redeclaración sobrescribe silenciosamente el valor anterior, lo que puede causar errores no detectados.\n", "\n", "**Ejemplo 3: Reasignación de const**\n", "```javascript\n", "const configuracion = { tema: \"claro\" };\n", "configuracion.tema = \"oscuro\"; // modificación válida\n", "console.log(configuracion.tema); // \"oscuro\"\n", "// configuracion = {}; // Error: reasignación no permitida\n", "```\n", "Resultado: **const** protege la referencia, no el contenido del objeto referenciado.\n", "\n", "#### Buenas prácticas para la declaración de variables\n", "\n", "Para escribir código mantenible y libre de errores, seguiremos estas prácticas esenciales:\n", "\n", "1. **Pre<PERSON>ir `const` por defecto**, usar `let` solo cuando necesitemos reasignar el valor\n", "2. **Evitar `var` completamente** en código nuevo, excepto casos muy específicos de compatibilidad\n", "3. **Declarar variables lo más cerca posible** de donde se usen para minimizar su alcance\n", "4. **Usar nombres descriptivos** que expresen claramente el propósito de la variable\n", "5. **Inicializar variables en el momento de la declaración** siempre que sea posible\n", "\n", "Estas prácticas nos preparan para trabajar con los diferentes tipos de datos que pueden almacenar nuestras variables, tema que abordaremos a continuación para comprender qué tipos de información podemos manipular en JavaScript.\n", "\n", "### Tipos primitivos y coerción de datos\n", "\n", "Ahora que comprendemos cómo crear contenedores para nuestros datos mediante variables, necesitamos explorar qué tipos de información pueden almacenar. A diferencia del pseudocódigo que pudimos usar en ejercicios previos, donde especificábamos explícitamente si una variable era numérica o textual, JavaScript utiliza **tipado dinámico**. Esto significa que el tipo de dato se determina automáticamente según el valor asignado y puede cambiar durante la ejecución del programa.\n", "\n", "Esta flexibilidad hace que JavaScript sea más accesible para principiantes, pero también requiere que comprendamos cómo el lenguaje maneja internamente estas conversiones automáticas de tipos.\n", "\n", "#### Los siete tipos primitivos de JavaScript\n", "\n", "JavaScript reconoce oficialmente siete tipos de datos primitivos, cada uno con características específicas que determinan cómo se comportan en operaciones y comparaciones.\n", "\n", "**Number**\n", "El tipo `number` representa tanto números enteros como decimales en un único tipo. JavaScript usa el estándar IEEE 754 de doble precisión para almacenar todos los números:\n", "\n", "```javascript\n", "let entero = 42;\n", "let decimal = 3.14159;\n", "let negativo = -17;\n", "let cientifico = 2.5e6; // 2,500,000\n", "console.log(typeof entero); // \"number\"\n", "```\n", "\n", "**String**\n", "Las cadenas de texto se pueden declarar con comillas simples, dobles o backticks (template literals):\n", "\n", "```javascript\n", "let saludo = \"Hola mundo\";\n", "let nombre = 'JavaScript';\n", "let mensaje = `Bienvenido a ${nombre}`; // interpolación\n", "console.log(typeof saludo); // \"string\"\n", "```\n", "\n", "**Boolean**\n", "Representa valores de verdad, fundamentales para la lógica del programa:\n", "\n", "```javascript\n", "let activo = true;\n", "let completado = false;\n", "console.log(typeof activo); // \"boolean\"\n", "```\n", "\n", "**Undefined**\n", "Indica que una variable ha sido declarada pero no inicializada, o que una función no retorna valor explícitamente:\n", "\n", "```javascript\n", "let sinInicializar;\n", "console.log(sinInicializar); // undefined\n", "console.log(typeof sinInicializar); // \"undefined\"\n", "```\n", "\n", "**Null**\n", "Representa la ausencia intencional de valor, utilizado cuando queremos indicar explícitamente que una variable no contiene datos:\n", "\n", "```javascript\n", "let usuario = null; // intencionalmente vacío\n", "console.log(typeof usuario); // \"object\" (peculiaridad histórica)\n", "```\n", "\n", "**Symbol**\n", "Introducido en ES6, crea identificadores únicos, útiles para propiedades de objetos que no deben colisionar:\n", "\n", "```javascript\n", "let simbolo = Symbol('descripcion');\n", "let otroSimbolo = Symbol('descripcion');\n", "console.log(simbolo === otroSimbolo); // false (únicos)\n", "console.log(typeof simbolo); // \"symbol\"\n", "```\n", "\n", "**BigInt**\n", "Permite trabajar con números enteros de precisión arbitraria, más allá del límite de `number`:\n", "\n", "```javascript\n", "let numeroGrande = 123456789012345678901234567890n;\n", "let otroBigInt = BigInt(\"987654321098765432109876543210\");\n", "console.log(typeof numeroGrande); // \"bigint\"\n", "```\n", "\n", "#### Diferencias entre undefined y null\n", "\n", "Aunque ambos representan \"ausencia de valor\", tienen significados semánticos distintos. **undefined** surge naturalmente cuando JavaScript no encuentra un valor asignado:\n", "\n", "```javascript\n", "function saludar() {\n", "    console.log(\"Hola\");\n", "    // sin return explícito\n", "}\n", "\n", "let resultado = saludar(); // función ejecutada\n", "console.log(resultado); // undefined\n", "```\n", "\n", "**null**, por el contrario, es un valor que asignamos intencionalmente para indicar que una variable existe pero está vacía:\n", "\n", "```javascript\n", "let datosUsuario = null; // inicialización intencional\n", "// más tarde...\n", "datosUsuario = { nombre: \"Ana\", edad: 25 };\n", "```\n", "\n", "#### Coerción implícita: cuando JavaScript convierte automáticamente\n", "\n", "JavaScript realiza conversiones automáticas de tipos cuando los operadores esperan un tipo específico pero reciben otro. Esta **coerción implícita** puede ser útil pero también sorprendente.\n", "\n", "En operaciones de suma, si uno de los operandos es string, JavaScript convierte todo a string y concatena:\n", "\n", "```javascript\n", "console.log(\"El resultado es: \" + 42); // \"El resultado es: 42\"\n", "console.log(5 + \"3\"); // \"53\" (concatenación)\n", "console.log(\"5\" + 3); // \"53\" (concatenación)\n", "```\n", "\n", "En operaciones aritméticas diferentes de suma, JavaScript intenta convertir a números:\n", "\n", "```javascript\n", "console.log(\"10\" - 2); // 8 (conversión a número)\n", "console.log(\"10\" * \"3\"); // 30 (ambos convertidos)\n", "console.log(\"10\" / \"2\"); // 5 (división numérica)\n", "```\n", "\n", "En contextos booleanos (como condicionales), JavaScript convierte valores usando reglas específicas:\n", "\n", "```javascript\n", "if (\"texto\") console.log(\"Truthy\"); // se ejecuta\n", "if (0) console.log(\"No se ejecuta\"); // 0 es falsy\n", "if ([]) console.log(\"Array vacío es truthy\"); // se ejecuta\n", "```\n", "\n", "#### Coerción explícita: controlando las conversiones\n", "\n", "Para evitar sorpresas, podemos forzar conversiones usando funciones específicas:\n", "\n", "```javascript\n", "// Conversión a número\n", "let numero1 = Number(\"42\"); // 42\n", "let numero2 = parseInt(\"42px\"); // 42 (ignora caracteres finales)\n", "let numero3 = parseFloat(\"3.14abc\"); // 3.14\n", "let numero4 = +\"123\"; // 123 (operador unario)\n", "\n", "// Conversión a string\n", "let texto1 = String(123); // \"123\"\n", "let texto2 = (123).toString(); // \"123\"\n", "\n", "// Conversión a boolean\n", "let bool1 = Boolean(1); // true\n", "let bool2 = Boolean(\"\"); // false\n", "let bool3 = !!\"texto\"; // true (doble negación)\n", "```\n", "\n", "#### Tabla de conversiones frecuentes\n", "\n", "| Expresión | Resultado | Tipo resultado |\n", "|:----------|----------:|:--------------:|\n", "| `\"5\" + 2` | `\"52\"` | string |\n", "| `\"5\" - 2` | `3` | number |\n", "| `\"5\" * 2` | `10` | number |\n", "| `\"five\" * 2` | `NaN` | number |\n", "| `\"\" + false` | `\"false\"` | string |\n", "| `!!\"0\"` | `true` | boolean |\n", "| `!!\"\"` | `false` | boolean |\n", "| `Number(true)` | `1` | number |\n", "\n", "*Tabla 1. Conversiones de tipo más frecuentes en JavaScript.*\n", "\n", "#### <PERSON><PERSON><PERSON><PERSON> g<PERSON>: prediciendo conversiones\n", "\n", "Analicemos estas cinco líneas de código y prediguemos sus resultados antes de conocer la respuesta:\n", "\n", "```javascript\n", "console.log(\"3\" + 2 + 1);\n", "console.log(3 + 2 + \"1\");  \n", "console.log(\"3\" - 2 + 1);\n", "console.log(!\"\" + 1);\n", "console.log(+\"42\" + 7);\n", "```\n", "\n", "**Resultados y explicación:**\n", "1. `\"31\"` - La cadena \"3\" convierte todo a concatenación: \"3\" + \"2\" + \"1\"\n", "2. `\"51\"` - Se evalúa de izquierda a derecha: (3 + 2) + \"1\" = 5 + \"1\" = \"51\"\n", "3. `2` - La resta fuerza conversión numérica: 3 - 2 + 1 = 2\n", "4. `2` - La negación de \"\" da true, true + 1 = 2\n", "5. `49` - El operador unario + convierte \"42\" a 42, lue<PERSON> 42 + 7 = 49\n", "\n", "#### Igualdad suelta versus igualdad estricta\n", "\n", "JavaScript ofrece dos formas de comparar valores: igualdad suelta (`==`) que permite coerción, e igualdad estricta (`===`) que compara valor y tipo sin conversiones.\n", "\n", "**Comparación con coerción (==):**\n", "```javascript\n", "console.log(5 == \"5\"); // true (coerción de string a number)\n", "console.log(true == 1); // true (coerción de boolean a number)\n", "console.log(null == undefined); // true (caso especial)\n", "```\n", "\n", "**Comparación estricta (===):**\n", "```javascript\n", "console.log(5 === \"5\"); // false (diferentes tipos)\n", "console.log(true === 1); // false (diferentes tipos)\n", "console.log(null === undefined); // false (diferentes tipos)\n", "```\n", "\n", "La recomendación general es usar siempre `===` para evitar comportamientos inesperados por coerción implícita, reservando `==` solo para casos muy específicos donde necesitemos la coerción intencionalmente.\n", "\n", "Ahora que comprendemos los valores y cómo se convierten entre diferentes tipos, necesitamos herramientas para operarlos y combinarlos. Los operadores nos proporcionan estas herramientas esenciales para manipular datos y construir expresiones complejas.\n", "\n", "### Operadores aritméticos, lógicos y comparativos\n", "\n", "Los algoritmos manipulan datos a través de **operadores**, herramientas fundamentales que nos permiten realizar cálculos, comparaciones y evaluaciones lógicas. Recordando la tabla de conversiones que acabamos de estudiar, es crucial entender qué tipo de resultado produce cada operador, especialmente cuando combinamos diferentes tipos de datos.\n", "\n", "Los operadores transforman valores mediante expresiones que siguen reglas de precedencia similares a las matemáticas, pero con algunas particularidades propias de la programación que debemos dominar.\n", "\n", "#### Operadores aritméticos básicos\n", "\n", "JavaScript proporciona los operadores aritméticos fundamentales que reconocemos de las matemáticas, con algunas extensiones modernas:\n", "\n", "```javascript\n", "// Operadores básicos\n", "let suma = 15 + 3;        // 18\n", "let resta = 15 - 3;       // 12\n", "let multiplicacion = 15 * 3;  // 45\n", "let division = 15 / 3;    // 5\n", "let modulo = 15 % 4;      // 3 (resto de la división)\n", "\n", "// Exponenciación (ES2016)\n", "let potencia = 2 ** 3;    // 8 (equivale a Math.pow(2, 3))\n", "let raizCuadrada = 16 ** 0.5; // 4 (equivale a Math.sqrt(16))\n", "```\n", "\n", "El operador módulo (`%`) es especialmente útil para determinar si un número es par o impar, o para crear ciclos que se repiten cada cierto intervalo:\n", "\n", "```javascript\n", "console.log(10 % 2); // 0 (par)\n", "console.log(11 % 2); // 1 (impar)\n", "console.log(25 % 7); // 4 (resto)\n", "```\n", "\n", "#### Operadores de asignación compuesta\n", "\n", "Para evitar repetir el nombre de la variable en operaciones frecuentes, JavaScript ofrece operadores de asignación compuesta:\n", "\n", "```javascript\n", "let contador = 10;\n", "\n", "// Formas equivalentes de incrementar\n", "contador = contador + 5;  // forma explícita\n", "contador += 5;            // forma compuesta (más concisa)\n", "\n", "// Otros operadores compuestos\n", "contador -= 3;  // contador = contador - 3\n", "contador *= 2;  // contador = contador * 2\n", "contador /= 4;  // contador = contador / 4\n", "contador %= 3;  // contador = contador % 3\n", "\n", "// Incremento y decremento unitario\n", "contador++;     // contador = contador + 1 (postfijo)\n", "++contador;     // contador = contador + 1 (prefijo)\n", "contador--;     // contador = contador - 1 (postfijo)\n", "--contador;     // contador = contador - 1 (prefijo)\n", "```\n", "\n", "La diferencia entre prefijo y postfijo importa cuando usamos el resultado en una expresión:\n", "\n", "```javascript\n", "let a = 5;\n", "let b = ++a; // a se incrementa primero, luego se asigna: a=6, b=6\n", "let c = a++; // a se asigna primero, luego se incrementa: c=6, a=7\n", "```\n", "\n", "#### Operadores comparativos\n", "\n", "Los operadores comparativos evalúan la relación entre dos valores y siempre devuelven un resultado booleano, fundamental para las estructuras condicionales que estudiaremos en el próximo tema:\n", "\n", "```javascript\n", "let x = 10, y = 5;\n", "\n", "console.log(x > y);   // true (mayor que)\n", "console.log(x < y);   // false (menor que)\n", "console.log(x >= 10); // true (mayor o igual)\n", "console.log(y <= 5);  // true (menor o igual)\n", "console.log(x == \"10\");  // true (igualdad con coerción)\n", "console.log(x === \"10\"); // false (igualdad estricta)\n", "console.log(x != y);     // true (desigualdad con coerción)\n", "console.log(x !== \"10\"); // true (desigualdad estricta)\n", "```\n", "\n", "Como vimos anteriormente, la diferencia entre `==`/`!=` y `===`/`!==` radica en si permiten coerción de tipos o requieren coincidencia exacta de valor y tipo.\n", "\n", "#### Operadores lógicos y evaluación de corto circuito\n", "\n", "Los operadores lógicos trabajan con valores booleanos pero tienen comportamientos especiales que los hacen muy útiles más allá de la lógica simple:\n", "\n", "```javascript\n", "// AND lógico (&&)\n", "console.log(true && true);   // true\n", "console.log(true && false);  // false\n", "console.log(false && true);  // false\n", "\n", "// OR lógico (||)\n", "console.log(true || false);  // true\n", "console.log(false || true);  // true\n", "console.log(false || false); // false\n", "\n", "// NOT lógico (!)\n", "console.log(!true);   // false\n", "console.log(!false);  // true\n", "console.log(!!0);     // false (doble negación para conversión)\n", "```\n", "\n", "El concepto de **evaluación de corto circuito** es crucial: JavaScript no evalúa la segunda expresión si ya puede determinar el resultado con la primera.\n", "\n", "Con `&&`, si la primera expresión es falsy, se devuelve sin evaluar la segunda:\n", "```javascript\n", "let usuario = null;\n", "usuario && console.log(\"Usuario válido\"); // no se ejecuta console.log\n", "```\n", "\n", "Con `||`, si la primera expresión es truthy, se devuelve sin evaluar la segunda:\n", "```javascript\n", "let configuracion = obtenerConfiguracion() || { tema: \"predeterminado\" };\n", "let puerto = process.env.PORT || 3000; // valor por defecto común\n", "```\n", "\n", "#### El operador ternario: decisiones compactas\n", "\n", "El operador ternario (`? :`) ofrece una forma concisa de expresar decisiones simples:\n", "\n", "```javascript\n", "// Sintaxis: condicion ? valorSiTrue : valorSiFalse\n", "let edad = 17;\n", "let categoria = edad >= 18 ? \"adulto\" : \"menor\";\n", "\n", "// Equivalente con if-else (más verboso)\n", "let categoria2;\n", "if (edad >= 18) {\n", "    categoria2 = \"adulto\";\n", "} else {\n", "    categoria2 = \"menor\";\n", "}\n", "```\n", "\n", "El operador ternario es especialmente útil para asignaciones condicionales y para JSX en React, aunque no debemos abusar de él cuando la lógica se vuelve compleja.\n", "\n", "#### Tabla de precedencia de operadores\n", "\n", "| Precedencia | Operador | Descripción | Asociatividad |\n", "|:-----------:|:--------:|:------------|:-------------:|\n", "| 1 | `()` | Agrupación | Izquierda a derecha |\n", "| 2 | `++`, `--` | Incremento/Decremento | Derecha a izquierda |\n", "| 3 | `**` | Exponenciación | **Derecha a izquierda** |\n", "| 4 | `*`, `/`, `%` | **Multiplicación, División, Módulo** | Izquierda a derecha |\n", "| 5 | `+`, `-` | **Suma, Resta** | Iz<PERSON><PERSON>a a derecha |\n", "| 6 | `<`, `<=`, `>`, `>=` | **Comparación relacional** | Izquierda a derecha |\n", "| 7 | `==`, `===`, `!=`, `!==` | **Igualdad** | Izquierda a derecha |\n", "| 8 | `&&` | **AND lógico** | Izquierda a derecha |\n", "| 9 | `\\|\\|` | **OR lógico** | Izquierda a derecha |\n", "| 10 | `? :` | Ternario | Derecha a izquierda |\n", "\n", "*Tabla 2. Precedencia de operadores más comunes (1 = mayor precedencia).*\n", "\n", "#### E<PERSON><PERSON><PERSON> integrador: evaluación paso a paso\n", "\n", "Analicemos esta expresión compleja siguiendo la precedencia de operadores:\n", "\n", "```javascript\n", "let resultado = 3 + 4 * 2 > 10 && \"valor\" || 0;\n", "```\n", "\n", "**Resolución paso a paso:**\n", "1. `4 * 2` = `8` (precedencia 4)\n", "2. `3 + 8` = `11` (precedencia 5)\n", "3. `11 > 10` = `true` (precedencia 6)\n", "4. `true && \"valor\"` = `\"valor\"` (precedencia 8, evaluación de corto circuito)\n", "5. `\"valor\" || 0` = `\"valor\"` (precedencia 9, evaluación de corto circuito)\n", "\n", "El resultado final es `\"valor\"` porque las cadenas no vacías son truthy y el operador `||` devuelve el primer valor truthy que encuentra.\n", "\n", "Esta expresión, aunque académicamente útil para entender precedencia, sería mejor escrita con paréntesis explícitos en código real:\n", "\n", "```javascript\n", "let resultado = ((3 + (4 * 2)) > 10) && \"valor\" || 0;\n", "```\n", "\n", "Ahora que dominamos la manipulación de datos a través de operadores, necesitamos formas de interactuar con el usuario y observar los resultados de nuestros cálculos en tiempo real. Para esto, exploraremos las funciones básicas de entrada y salida que nos permiten crear programas verdaderamente interactivos.\n", "\n", "### Entrada y salida básica con prompt, alert y console.log\n", "\n", "Hasta este punto hemos trabajado con fragmentos de código aislados que manipulan valores estáticos. Para construir programas verdaderamente útiles, necesitamos establecer comunicación bidireccional: recibir datos del usuario y mostrar resultados de forma dinámica. Las funciones de entrada y salida nos proporcionan estas capacidades esenciales.\n", "\n", "JavaScript, al estar diseñado originalmente para navegadores web, heredó un conjunto de funciones modales que permiten interacción básica con el usuario. Además, los entornos de desarrollo modernos nos ofrecen herramientas más sofisticadas para depurar y monitorear nuestros programas.\n", "\n", "#### Funciones modales del navegador\n", "\n", "Las funciones modales interrumpen temporalmente la ejecución del programa para interactuar con el usuario. Aunque son útiles para prototipado y aprendizaje, en aplicaciones profesionales se suelen reemplazar por interfaces más elegantes.\n", "\n", "**alert()** muestra un mensaje al usuario:\n", "```javascript\n", "alert(\"¡Bienvenido a JavaScript!\");\n", "alert(\"El resultado del cálculo es: \" + (5 * 3));\n", "```\n", "\n", "**prompt()** solicita información al usuario y devuelve una cadena (string):\n", "```javascript\n", "let nombre = prompt(\"¿Cuál es tu nombre?\");\n", "let edad = prompt(\"¿Cuántos años tienes?\");\n", "console.log(\"Hola \" + nombre + \", tienes \" + edad + \" años\");\n", "```\n", "\n", "**confirm()** presenta una pregunta con opciones \"Aceptar\" y \"Cancelar\", devolviendo un booleano:\n", "```javascript\n", "let continuar = confirm(\"¿Deseas continuar con la operación?\");\n", "if (continuar) {\n", "    console.log(\"El usuario decidió continuar\");\n", "} else {\n", "    console.log(\"El usuario canceló la operación\");\n", "}\n", "```\n", "\n", "Es importante recordar que **prompt()** siempre devuelve una cadena, incluso cuando el usuario introduce números. <PERSON><PERSON> tanto, debemos convertir explícitamente cuando necesitemos valores numéricos:\n", "\n", "```javascript\n", "let numeroTexto = prompt(\"Introduce un número:\");\n", "let numero = Number(numeroTexto); // o parseInt(), parseFloat()\n", "```\n", "\n", "#### Herramientas de consola para desarrollo\n", "\n", "La consola del navegador y Node.js proporcionan funciones más flexibles y profesionales para mostrar información durante el desarrollo:\n", "\n", "**console.log()** es la función más utilizada para mostrar valores:\n", "```javascript\n", "console.log(\"Mensaje simple\");\n", "console.log(\"El valor de x es:\", x);\n", "console.log(`Usando template literals: ${x + y}`);\n", "```\n", "\n", "**console.error()** muestra mensajes de error con formato distintivo:\n", "```javascript\n", "console.error(\"Algo salió mal en el cálculo\");\n", "console.error(\"Error:\", error.message);\n", "```\n", "\n", "**console.table()** presenta datos estructurados en formato tabular:\n", "```javascript\n", "let estudiantes = [\n", "    { nombre: \"<PERSON>\", edad: 20, nota: 8.5 },\n", "    { nombre: \"<PERSON>\", edad: 22, nota: 7.2 },\n", "    { nombre: \"<PERSON>\", edad: 21, nota: 9.1 }\n", "];\n", "console.table(estudiantes);\n", "```\n", "\n", "#### Consideraciones sobre Node.js\n", "\n", "Cuando trabajamos en Node.js, que es nuestro entorno principal en esta asignatura según establecimos en la configuración inicial, debemos tener en cuenta que **prompt()** no existe nativamente. Para entrada de usuario en Node.js, tendríamos que usar el módulo **readline**, pero esto excede el nivel de complejidad apropiado para esta introducción.\n", "\n", "<PERSON><PERSON> tanto, en nuestros ejercicios con Node.js nos enfocaremos en mostrar resultados con **console.log()** y simular entrada de datos mediante variables predefinidas o generación de valores aleatorios.\n", "\n", "#### <PERSON>je<PERSON><PERSON> práctico: calculadora básica\n", "\n", "Construyamos una calculadora mínima que integre los conceptos aprendidos sobre variables, tipos de datos, operadores y entrada/salida:\n", "\n", "```javascript\n", "// Simulación de entrada de usuario (en navegador usaríamos prompt)\n", "let numero1Texto = \"15\";    // prompt(\"Introduce el primer número:\");\n", "let numero2Texto = \"4\";     // prompt(\"Introduce el segundo número:\");\n", "let operacion = \"division\"; // prompt(\"¿Qué operación? (suma, resta, multiplicacion, division)\");\n", "\n", "// Conversión explícita a números\n", "let num1 = Number(numero1Texto);\n", "let num2 = Number(numero2Texto);\n", "let resultado;\n", "\n", "// Determinación de la operación usando operador ternario anidado\n", "resultado = operacion === \"suma\" ? num1 + num2 :\n", "           operacion === \"resta\" ? num1 - num2 :\n", "           operacion === \"multiplicacion\" ? num1 * num2 :\n", "           operacion === \"division\" ? num1 / num2 :\n", "           \"Operación no válida\";\n", "\n", "// Salida usando template literals y console.log\n", "console.log(`Operación: ${num1} ${operacion} ${num2}`);\n", "console.log(`Resultado: ${resultado}`);\n", "\n", "// También mostraríamos con alert en el navegador\n", "// alert(`El resultado de ${num1} ${operacion} ${num2} es: ${resultado}`);\n", "```\n", "\n", "**Explicación paso a paso:**\n", "1. **Simulamos entrada**: Asignamos valores directamente (en navegador usaríamos `prompt()`)\n", "2. **Conversión de tipos**: Usamos `Number()` para convertir strings a números\n", "3. **Operación condicional**: Empleamos operador ternario anidado para seleccionar la operación\n", "4. **Salida formateada**: Utilizamos template literals para crear mensajes informativos\n", "5. **Múltiples canales de salida**: Mostramos en consola y opcionalmente con `alert()`\n", "\n", "Este ejemplo demuestra la integración de conversión de tipos, operadores comparativos y de asignación, junto con las funciones de entrada y salida básicas.\n", "\n", "#### Buenas prácticas para salida en consola\n", "\n", "Para mantener un código profesional y facilitar la depuración, sigamos estas prácticas:\n", "\n", "1. **Usar etiquetas descriptivas**: `console.log(\"Resultado del cálculo:\", resultado)` es más claro que `console.log(resultado)`\n", "2. **Evitar logs redundantes**: No saturar la consola con información innecesaria en producción\n", "3. **Formatear datos complejos**: Usar `console.table()` para arrays y objetos, `JSON.stringify()` para estructuras anidadas\n", "4. **Diferenciar tipos de mensaje**: Usar `console.error()` para errores, `console.warn()` para advertencias\n", "5. **Limpiar logs temporales**: Remover `console.log()` de depuración antes de finalizar el código\n", "\n", "La capacidad de recibir datos externos y mostrar resultados nos permite crear programas verdaderamente interactivos. Sin embargo, al trabajar con información dinámica del usuario, se hace indispensable poder decidir qué acciones tomar según el valor recibido. Esta necesidad de **tomar decisiones programáticas** nos lleva naturalmente hacia las estructuras condicionales, que exploraremos en el próximo tema para construir algoritmos que respondan inteligentemente a diferentes situaciones.\n"]}], "source": ["print(result.content[1]['text'])"]}, {"cell_type": "markdown", "id": "a53260a2", "metadata": {}, "source": ["*Notas. Siguientes pasos*\n", "\n", "- [ ] Buscar fuentes para el tema de lo almacenado."]}, {"cell_type": "code", "execution_count": 147, "id": "bab7dded", "metadata": {}, "outputs": [{"data": {"text/plain": ["'## Tema 1. Introducción a la programación y al lenguaje Python\\n\\n### Presentación de la asignatura y herramientas de trabajo**Extensión orientativa: 850–1 000 palabras**\\n\\n- Comienza con un breve párrafo que sitúe la disciplina de la programación dentro del ámbito de la informática y motive su estudio (ej. automatizar tareas, resolver problemas y crear productos digitales). Aclara que el lenguaje elegido es Python por su sintaxis sencilla y su ecosistema.\\n- Enumera los objetivos generales que el lector alcanzará al terminar la asignatura (pensar algorítmicamente, dominar las estructuras básicas, escribir programas robustos y colaborar en proyectos). Relaciónalos con los bloques temáticos del programa para que el alumnado visualice el recorrido completo sin profundizar todavía en los contenidos concretos que se abordarán más adelante.\\n- Describe las competencias prácticas que se desarrollarán: comprensión de problemas, diseño de algoritmos, traducción a código, prueba y depuración, documentación y trabajo colaborativo.\\n- Presenta las herramientas que se utilizarán durante el semestre, diferenciándolas por categoría en una tabla (lenguaje, interprete, editores/IDEs, terminal/comandos, sistema de control de versiones):\\n  | Categoría                | Ejemplos propuestos | Propósito principal |\\n  |--------------------------|---------------------|---------------------|\\n  | Intérprete Python 3.x    | CPython, Anaconda   | Ejecutar código     |\\n  | Editor/IDE              | VS Code, PyCharm CE | Escribir y depurar  |\\n  | Terminal                | bash, PowerShell    | Ejecutar comandos   |\\n  | Entornos virtuales      | venv, conda         | Aislar dependencias |\\n  | Control de versiones    | Git + GitHub/GitLab | Trabajo en equipo   |\\n- Añade un apartado sobre requisitos mínimos de hardware/software y recomendaciones de buenas prácticas (guardar código en la nube, uso responsable del copy-paste, hábito de comentar). Evita todavía detallar la instalación de Python: eso se cubrirá en el epígrafe siguiente.\\n- Introduce la dinámica de trabajo: sesiones teóricas, laboratorios, entregas y foros de dudas. Incluye un micro-ejemplo narrativo para mostrar cómo un estudiante pasará de la idea a la solución: “imagina que quieres clasificar tus gastos mensuales…”. No muestres código en este punto.\\n- Conecta con el tema siguiente explicando que, antes de escribir un programa, hay que entender qué es un algoritmo y cómo representarlo gráficamente.\\n- Cierra con una invitación a la exploración y al aprendizaje activo, evitando conclusiones definitivas y dejando claro que los detalles técnicos empiezan en el siguiente epígrafe.\\n\\n\\n### Concepto de algoritmo y diagramas de flujo**Extensión orientativa: 900–1 100 palabras**\\n\\n- Abre con una breve transición desde la motivación anterior: ya sabemos qué perseguimos, ahora descubriremos el concepto que sustenta cualquier programa.\\n- Define “algoritmo” con lenguaje accesible y formula canónica: secuencia finita, ordenada y no ambigua de pasos que transforma entradas en salidas. Incluye un ejemplo cotidiano (preparar un café) para ilustrar las tres propiedades clave: finitud, determinismo y efectividad.\\n- Introduce la noción de pseudocódigo como puente entre lenguaje natural y código ejecutable. Explica cuándo conviene usarlo y destaca su flexibilidad sintáctica.\\n- Presenta los diagramas de flujo como alternativa visual. Describe los símbolos básicos (inicio/fin, proceso, decisión, entrada/salida, conector) e indica la semántica de las flechas. Resume estos elementos en una tabla de dos columnas (símbolo y significado).\\n- Propón un micro-ejercicio guiado que el lector no resolverá todavía por completo (se resolverá más tarde con código). Ejemplo sugerido: algoritmo que calcule la suma de los números del 1 al N. Incluye:  \\n  1. Pseudocódigo de alto nivel.  \\n  2. Diagrama mermaid sencillo con los símbolos clave (inicio, entrada N, inicializar suma, bucle, decisión, salida).\\n- Explora las ventajas y limitaciones de cada representación; por ejemplo, la comunicación con no programadores (diagramas) frente a la precisión técnica (pseudocódigo). Mantén el análisis a nivel introductorio para no adelantar detalles de bucles que se verán formalmente en un tema posterior.\\n- Enlaza con lo que vendrá: para convertir los algoritmos anteriores en un programa real necesitaremos instalar un intérprete de Python y un entorno de desarrollo adecuado.\\n- Evita enumerar todavía comandos concretos; reserva esa descripción para el siguiente epígrafe. Cierra resaltando la importancia de planificar antes de codificar y dejar prefigurada la metáfora “cocina-receta-plato” sin concluir definitivamente.\\n\\n\\n### Instalación de Python y configuración del entorno de desarrollo**Extensión orientativa: 750–950 palabras**\\n\\n- Introduce la finalidad práctica: disponer de las herramientas para transformar los algoritmos diseñados en programas ejecutables. Conecta explícitamente con el diagrama de flujo del ejemplo anterior.\\n- Explica brevemente la diferencia entre CPython, Anaconda y otras distribuciones para que el estudiante comprenda que Python es un intérprete y un ecosistema. Recomienda la versión estable 3.x y justifica la elección.\\n- Estructura la sección en tres subsecciones principales, sin encabezados internos explícitos (se integrarán narrativamente):\\n  1. Descarga e instalación multiplataforma.  \\n     • Describe los pasos genéricos para Windows, macOS y Linux; usa verbos de acción (descargar, ejecutar, verificar) y destaca la casilla “Add Python to PATH” en Windows.  \\n  2. Verificación en terminal.  \\n     • Muestra cómo abrir la línea de comandos y ejecutar `python --version`. Incluye una breve mención a `python3` en sistemas *nix.  \\n  3. Configuración mínima de un entorno de trabajo.  \\n     • Presenta los editores/IDEs propuestos (VS Code, PyCharm Community, Thonny).  \\n     • Explica la creación de un entorno virtual con `python -m venv venv` y su activación/desactivación básica.  \\n     • Añade una nota sobre Jupyter Notebook: cuándo resulta útil y cómo se lanza con `pip install notebook` + `jupyter notebook`.\\n- Incluye una tabla comparativa pequeña con criterios de selección de IDE (curva de aprendizaje, depuración integrada, plugins, consumo de recursos). Esto dará al redactor margen para expandir el texto sin excederse.\\n- Sugiere un micro-chequeo práctico: crear carpeta de proyecto, activar entorno virtual e instalar la librería `requests` con `pip`, verificando que sólo está disponible dentro del entorno. Este ejercicio anticipa la gestión de paquetes que se estudiará en un bloque posterior sin explicar todavía los detalles de `pip`.\\n- Describe brevemente buenas prácticas de organización de archivos (separar código, datos y documentación) y uso de control de versiones, pero pospone la explicación detallada para el bloque III.\\n- Conecta con el epígrafe siguiente señalando que, con el intérprete listo, es momento de escribir el primer programa y estudiar la sintaxis básica.\\n\\n\\n### Primer programa: sintaxis básica y uso de print()**Extensión orientativa: 950–1 200 palabras**\\n\\n- Abre con una frase que retome la instalación anterior: “Ya contamos con Python listo para ejecutar nuestro primer programa…”. Emplea una narrativa paso a paso: crear un archivo, escribir instrucciones, ejecutarlo.\\n- Introduce la estructura mínima de un script: líneas de código secuenciales. Menciona la importancia de la extensión `.py` y la codificación UTF-8 por defecto.\\n- Presenta la instrucción `print()` como primera puerta de salida de datos. Explora su sintaxis general y argumentos:  \\n  ```python\\n  print(\"Hola, mundo\")\\n  print(\"Suma:\", 2 + 3)\\n  print(f\"La variable x vale {x}\")\\n  ```\\n  Explica brevemente el concepto de cadena de caracteres y uso de comillas simples/dobles.\\n- Incorpora una sección sobre la ejecución:  \\n  • Desde terminal: `python primer_programa.py`.  \\n  • Desde el intérprete interactivo (`>>>`).  \\n  • Con botón de “Run” en un IDE.  \\n  Señala cuándo es útil cada método.\\n- Introduce los conceptos de comentario y legibilidad: \\n  ```python\\n  # Esto es un comentario explicativo\\n  ```\\n  Enlaza con la idea de que una buena práctica es documentar la intención, no sólo el qué.\\n- Explica la relevancia de la indentación obligatoria en Python para bloques de código, sin profundizar en `if`/`for`. Incluye un ejemplo erróneo para que el redactor muestre el mensaje `IndentationError` y su corrección. \\n- Propón un ejercicio sencillo dirigido: solicitar al usuario su nombre con `input()`, almacenarlo en una variable y saludarlo con `print(f\"Hola, {nombre}!\")`. Deja indicaciones para que el modelo describa la ejecución en la consola.\\n- Añade un cuadro de consejos rápidos: evitar tildes en nombres de archivo, usar snake_case para variables, guardar con frecuencia.\\n- Introduce brevemente la noción de error sintáctico vs. error de ejecución, señalando que se estudiarán con detalle en un tema posterior sobre manejo de excepciones.\\n- Cierra conectando con el siguiente tema del curso (tipos de datos y operaciones) y subrayando que comprenderemos en profundidad lo que significan las expresiones evaluadas dentro de `print()`. Evita conclusiones globales; invita al lector a experimentar con variaciones del ejemplo.\\n'"]}, "execution_count": 147, "metadata": {}, "output_type": "execute_result"}], "source": ["instrucciones_temas[0]"]}, {"cell_type": "code", "execution_count": null, "id": "32a40ff0", "metadata": {}, "outputs": [], "source": ["#Make memories accumulating each of the things throughout topics.\n", "instruction_memories = \"\""]}, {"cell_type": "code", "execution_count": 161, "id": "f08d9237", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["## Te<PERSON> 1. Introducción a la programación y al lenguaje Python\n", "\n", "### Presentación de la asignatura y herramientas de trabajo\n", "\n", "La programación constituye una de las disciplinas fundamentales dentro del ámbito de la informática moderna, representando la capacidad de crear soluciones sistemáticas y automatizadas para una amplia gama de problemas. En un mundo cada vez más digitalizado, la habilidad de programar trasciende las fronteras de la informática pura, convirtiéndose en una competencia transversal que permite automatizar tareas repetitivas, resolver problemas complejos de manera eficiente y crear productos digitales innovadores. Desde la automatización de procesos empresariales hasta el desarrollo de aplicaciones móviles, pasando por el análisis de grandes volúmenes de datos, la programación se ha establecido como una herramienta indispensable para profesionales de múltiples disciplinas.\n", "\n", "Para este primer acercamiento al mundo de la programación, hemos seleccionado Python como lenguaje de aprendizaje por razones pedagógicas y prácticas bien fundamentadas. Python destaca por su sintaxis clara y legible, que se asemeja al lenguaje natural, lo que facilita la comprensión de conceptos algorítmicos sin la barrera adicional de una sintaxis compleja. Además, cuenta con un ecosistema extenso y maduro de librerías y herramientas que abarcan desde el desarrollo web hasta la inteligencia artificial, lo que garantiza que los conocimientos adquiridos tendrán aplicación práctica inmediata.\n", "\n", "Al finalizar esta asignatura, el estudiante habrá desarrollado cuatro competencias fundamentales que constituyen la base de cualquier programador competente. En primer lugar, desarrollará la capacidad de **pensar algorítmicamente**, descomponiendo problemas complejos en secuencias de pasos lógicos y ordenados. Esta habilidad trasciende la programación y resulta valiosa en cualquier contexto que requiera resolución sistemática de problemas. En segundo lugar, dominará las **estructuras básicas de programación**, incluyendo variables, tipos de datos, estructuras de control y funciones, que constituyen los bloques de construcción fundamentales de cualquier programa. La tercera competencia se centra en la capacidad de **escribir programas robustos y mantenibles**, aplicando principios de buenas prácticas, manejo de errores y documentación adecuada. Finalmente, desarrollará habilidades de **colaboración en proyectos de desarrollo**, utilizando herramientas de control de versiones y metodologías de trabajo en equipo.\n", "\n", "Estas competencias se articulan a través de los tres bloques temáticos que estructuran el programa de la asignatura. El **Bloque I** establece los fundamentos teóricos y prácticos, cubriendo desde conceptos algorítmicos básicos hasta las estructuras de control que rigen el flujo de ejecución de los programas. El **Bloque II** se adentra en las estructuras de datos y la programación modular, elementos esenciales para crear programas más complejos y organizados. El **Bloque III** completa la formación con aspectos avanzados como persistencia de datos, uso de librerías externas y metodologías de trabajo colaborativo, preparando al estudiante para enfrentar proyectos reales de desarrollo.\n", "\n", "Las competencias prácticas que se desarrollarán durante el curso incluyen la **comprensión profunda de problemas**, analizando requisitos y restricciones antes de iniciar el desarrollo. El **diseño de algoritmos** eficientes y elegantes constituye otra competencia clave, seguida de la **traducción efectiva de algoritmos a código** Python funcional. La **prueba y depuración** sistemática de programas, junto con la **documentación clara y completa** del código, completan las habilidades técnicas. Finalmente, el **trabajo colaborativo en equipo** se abordará mediante proyectos compartidos y uso de herramientas de control de versiones.\n", "\n", "<tabla>\n", "| **Categoría** | **Ejemplos propuestos** | **<PERSON><PERSON><PERSON>ito principal** |\n", "|:-------------:|:----------------------:|:----------------------:|\n", "| **Intérprete Python 3.x** | CPython, Anaconda | Ejecutar código |\n", "| **Editor/IDE** | VS Code, PyCharm CE | Escribir y depurar |\n", "| **Terminal** | bash, PowerShell | Ejecutar comandos |\n", "| **Entornos virtuales** | venv, conda | Aislar dependen<PERSON>s |\n", "| **Control de versiones** | Git + GitHub/GitLab | Trabajo en equipo |\n", "</tabla>\n", "*Tabla 1. Herramientas de desarrollo por categoría y función principal.*\n", "\n", "Para un aprovechamiento óptimo de la asignatura, es importante considerar algunos requisitos mínimos de hardware y software. Un ordenador con al menos 4GB de memoria RAM y sistema operativo actualizado (Windows 10+, macOS 10.14+, o distribución Linux reciente) será suficiente para ejecutar todas las herramientas propuestas. Se recomienda encarecidamente mantener copias de seguridad del código en servicios de almacenamiento en la nube, establecer el hábito de comentar el código de manera descriptiva y hacer un uso responsable del copy-paste, priorizando siempre la comprensión sobre la rapidez en la obtención de resultados.\n", "\n", "La metodología de trabajo combinará **sesiones teóricas** donde se presentarán conceptos fundamentales con **laboratorios prácticos** que permitirán aplicar inmediatamente los conocimientos adquiridos. Las **entregas periódicas** de ejercicios y proyectos proporcionarán retroalimentación continua sobre el progreso del aprendizaje, mientras que los **foros de dudas** facilitarán la resolución colaborativa de problemas y el intercambio de experiencias entre estudiantes.\n", "\n", "Para ilustrar este proceso de aprendizaje, consideremos un ejemplo práctico: imagina que quieres crear un programa para clasificar tus gastos mensuales. Inicialmente, identificarás las categorías necesarias (alimentación, transporte, entretenimiento), luego diseñarás el algoritmo que permita asignar cada gasto a su categoría correspondiente, posteriormente traducirás esta lógica a código Python funcional, y finalmente probarás el programa con datos reales para verificar su correcto funcionamiento. Este tipo de proyectos prácticos conecta directamente los conceptos teóricos con aplicaciones concretas y útiles.\n", "\n", "Antes de adentrarnos en la escritura de programas, resulta fundamental comprender qué es un algoritmo y cómo podemos representarlo gráficamente para facilitar su comprensión y comunicación. Esta comprensión conceptual previa nos permitirá abordar la programación desde una perspectiva más sólida y estructurada, estableciendo las bases para un aprendizaje más efectivo y duradero.\n", "\n", "### Concepto de algoritmo y diagramas de flujo\n", "\n", "Habiendo establecido las bases y objetivos de nuestro recorrido formativo, es momento de adentrarnos en el concepto fundamental que sustenta cualquier programa: el algoritmo. Comprender qué es un algoritmo y cómo representarlo adecuadamente constituye el primer paso esencial para convertirse en un programador competente.\n", "\n", "Un **algoritmo** puede definirse como una secuencia finita, ordenada y no ambigua de pasos que transforma un conjunto de entradas en un conjunto de salidas deseadas. Esta definición formal engloba tres propiedades esenciales que distinguen a un algoritmo de una simple lista de instrucciones. La **finitud** garantiza que el algoritmo termine en un número determinado de pasos, evitando bucles infinitos o procesos que nunca concluden. El **determinismo** asegura que cada paso esté definido de manera precisa, sin ambigüedades que puedan llevar a interpretaciones diferentes. La **efectividad** requiere que cada paso sea ejecutable con los recursos disponibles y en un tiempo razonable.\n", "\n", "Para ilustrar estos conceptos, consideremos el algoritmo cotidiano de preparar una taza de café. Las entradas serían agua, café molido y azúcar (opcional), mientras que la salida sería una taza de café lista para consumir. Los pasos serían: 1) Llenar la cafetera con agua, 2) Agregar café molido al filtro, 3) Encender la cafetera, 4) Esperar a que termine la preparación, 5) Servir en una taza, 6) Agregar az<PERSON>car si se desea. Este ejemplo cumple con la finitud (termina cuando el café está listo), el determinismo (cada paso está claramente definido) y la efectividad (todos los pasos son realizables con los recursos disponibles).\n", "\n", "El **pseudocódigo** representa una herramienta fundamental como puente entre el lenguaje natural y el código ejecutable. Se trata de una descripción de alto nivel que utiliza una sintaxis simplificada para expresar la lógica de un algoritmo sin preocuparse por los detalles sintácticos específicos de un lenguaje de programación. Su flexibilidad sintáctica permite concentrarse en la lógica del problema, facilitando la comunicación entre desarrolladores y la planificación previa a la implementación. Resulta especialmente útil en las fases iniciales de desarrollo, cuando es necesario validar la lógica antes de invertir tiempo en la codificación detallada.\n", "\n", "Los **diagramas de flujo** ofrecen una alternativa visual para representar algoritmos, utilizando símbolos geométricos estandarizados conectados por flechas que indican el flujo de ejecución. Esta representación visual facilita la comprensión de la lógica algorítmica, especialmente para algoritmos complejos con múltiples decisiones y caminos de ejecución.\n", "\n", "<tabla>\n", "| **Símbolo** | **Significado** |\n", "|:----------:|:---------------:|\n", "| **Óvalo** | Inicio/Fin del algoritmo |\n", "| **Rectángulo** | Proceso o instrucción |\n", "| **Rombo** | Decisión o condición |\n", "| **Paralelogramo** | Entrada/Salida de datos |\n", "| **<PERSON><PERSON><PERSON><PERSON> pe<PERSON>** | Conector o continuación |\n", "</tabla>\n", "*Tabla 2. Símbolos básicos utilizados en diagramas de flujo.*\n", "\n", "Para consolidar estos conceptos, analicemos un ejemplo práctico que retomaremos posteriormente con código: el algoritmo para calcular la suma de los números del 1 al N. En pseudocódigo, este algoritmo se expresaría como:\n", "\n", "```\n", "INICIO\n", "  Solicitar valor de N\n", "  Inicializar suma = 0\n", "  <PERSON>ici<PERSON>zar contador = 1\n", "  MIENTRAS contador <= N HACER\n", "    suma = suma + contador\n", "    contador = contador + 1\n", "  FIN MIENTRAS\n", "  Mostrar suma\n", "FIN\n", "```\n", "\n", "<mermaid>\n", "flowchart TD\n", "    A[Inicio] --> B[Solicitar N]\n", "    B --> C[suma = 0<br>contador = 1]\n", "    C --> D{contador <= N?}\n", "    D -->|Sí| E[suma = suma + contador<br>contador = contador + 1]\n", "    E --> D\n", "    D -->|No| F[Mostrar suma]\n", "    F --> G[Fin]\n", "</mermaid>\n", "*Figura 1. Diagrama de flujo para calcular la suma de números del 1 al N.*\n", "\n", "Cada representación ofrece ventajas específicas según el contexto de uso. Los **diagramas de flujo** destacan en la comunicación con personas no técnicas, la detección visual de errores lógicos y la comprensión rápida de algoritmos complejos. Su naturaleza gráfica facilita la identificación de caminos de ejecución y puntos de decisión críticos. Por su parte, el **pseudocódigo** ofrece mayor precisión técnica, facilita la transición posterior a código ejecutable y permite expresar algoritmos complejos de manera más compacta. Su flexibilidad sintáctica lo convierte en una herramienta ideal para la planificación y documentación técnica.\n", "\n", "Las limitaciones también deben considerarse en la selección de la representación apropiada. Los diagramas de flujo pueden volverse excesivamente complejos para algoritmos extensos, mientras que el pseudocódigo puede resultar menos intuitivo para personas sin experiencia en programación. La elección entre ambas representaciones debe basarse en el público objetivo, la complejidad del algoritmo y el propósito específico de la documentación.\n", "\n", "La importancia de planificar antes de codificar no puede subestimarse en el proceso de desarrollo de software. Como un chef que no comenzaría a cocinar sin conocer la receta, un programador experimentado dedica tiempo significativo a la conceptualización y diseño algorítmico antes de escribir la primera línea de código. Esta práctica reduce significativamente los errores, facilita el mantenimiento posterior y mejora la eficiencia del desarrollo.\n", "\n", "Para materializar estos algoritmos conceptuales en programas ejecutables, necesitaremos disponer de un intérprete de Python correctamente instalado y configurado, junto con un entorno de desarrollo que facilite la escritura, prueba y depuración del código. La transición de la representación abstracta a la implementación concreta constituye el siguiente paso natural en nuestro proceso de aprendizaje.\n", "\n", "### Instalación de Python y configuración del entorno de desarrollo\n", "\n", "Tras haber diseñado nuestros algoritmos conceptualmente, llega el momento de adquirir las herramientas prácticas necesarias para transformar esas ideas en programas ejecutables. Retomando el diagrama de flujo para calcular la suma de números del 1 al N, ahora necesitamos un intérprete que pueda ejecutar las instrucciones correspondientes y un entorno que facilite la escritura y depuración del código.\n", "\n", "Python, como lenguaje interpretado, requiere de un intérprete específico para ejecutar el código fuente. **CPython** representa la implementación de referencia, desarrollada en lenguaje C y mantenida por la Python Software Foundation. Es la versión más ampliamente utilizada y compatible con la mayoría de librerías externas. **Anaconda** constituye una distribución especializada que incluye Python junto con más de 1500 paquetes científicos preinstalados, además de herramientas de gestión de entornos y paquetes. Esta distribución resulta especialmente útil para aplicaciones de ciencia de datos y análisis numérico. Independientemente de la distribución elegida, es fundamental trabajar con **Python 3.x**, ya que Python 2.x alcanzó su fin de vida útil en enero de 2020 y ya no recibe actualizaciones.\n", "\n", "El proceso de instalación requiere atención a varios aspectos críticos según el sistema operativo utilizado. En **Windows**, tras descargar el instalador oficial desde python.org, es esencial marcar la casilla \"Add Python to PATH\" durante la instalación, lo que permite ejecutar Python desde cualquier ubicación en la línea de comandos. Para **macOS**, aunque el sistema incluye una versión de Python, se recomienda instalar la versión más reciente mediante el instalador oficial o utilizando Homebrew. En **Linux**, la mayoría de distribuciones incluyen Python preinstalado, pero puede ser necesario instalar `python3-pip` y `python3-venv` para funcionalidades completas.\n", "\n", "La verificación de la instalación se realiza mediante la línea de comandos, abriendo el terminal correspondiente al sistema operativo. En Windows, esto se logra mediante Command Prompt o PowerShell; en macOS y Linux, a través de la aplicación Terminal. El comando `python --version` debe mostrar la versión instalada, mientras que en algunos sistemas basados en Unix puede ser necesario utilizar `python3 --version` para distinguir entre Python 2 y Python 3.\n", "\n", "La configuración de un entorno de desarrollo adecuado impacta significativamente en la productividad y experiencia de aprendizaje. **Visual Studio Code** destaca como editor ligero y extensible, con excelente soporte para Python mediante extensiones oficiales. Ofrece funcionalidades avanzadas como autocompletado inteligente, depuración integrada y control de versiones. **PyCharm Community Edition** proporciona un IDE completo y gratuito, especialmente diseñado para Python, con herramientas sofisticadas de análisis de código y refactorización. **Thonny** representa una opción ideal para principiantes, con una interfaz simplificada que visualiza claramente la ejecución paso a paso del código.\n", "\n", "<tabla>\n", "| **Criterio** | **VS Code** | **PyCharm CE** | **Thonny** |\n", "|:------------:|:-----------:|:--------------:|:----------:|\n", "| **Curva de aprendizaje** | Moderada | Empinada | Suave |\n", "| **Depuración integrada** | Excelente | Excelente | Básica |\n", "| **Extensibilidad** | Muy alta | Alta | Limitada |\n", "| **Consumo de recursos** | Bajo | Alto | Muy bajo |\n", "</tabla>\n", "*Tabla 3. Comparativa de entornos de desarrollo recomendados.*\n", "\n", "Los **entornos virtuales** constituyen una herramienta fundamental para el desarrollo profesional en Python. Permiten crear espacios aislados donde instalar paquetes específicos sin afectar la instalación global del sistema. La creación de un entorno virtual se realiza mediante `python -m venv nombre_entorno`, generalmente utilizando `venv` como nombre convencional. Su activación varía según el sistema operativo: `venv\\Scripts\\activate` en Windows, y `source venv/bin/activate` en macOS y Linux. Una vez activado, el prompt de la terminal mostrará el nombre del entorno entre paréntesis, indicando que los comandos pip instalarán paquetes únicamente en ese espacio aislado.\n", "\n", "**Jupyter Notebook** merece mención especial como herramienta interactiva que combina código, texto explicativo y visualizaciones en un mismo documento. Resulta especialmente útil para experimentación, análisis de datos y creación de prototipos. Su instalación se realiza mediante `pip install notebook`, y su ejecución a través del comando `jupyter notebook`, que abre una interfaz web en el navegador.\n", "\n", "Para verificar el correcto funcionamiento del entorno, se sugiere realizar un chequeo práctico integral. <PERSON><PERSON>, crear una carpeta de proyecto denominada `mi_primer_proyecto`. <PERSON><PERSON><PERSON>, dentro de esta carpeta, crear y activar un entorno virtual. Tercero, instalar la librería `requests` mediante `pip install requests`. Cuarto, verificar que la librería esté disponible ejecutando `python -c \"import requests; print(requests.__version__)\"` dentro del entorno activado. Finalmente, desactivar el entorno e intentar la misma importación, confirmando que la librería no está disponible globalmente.\n", "\n", "Las **buenas prácticas de organización** incluyen separar claramente el código fuente, los datos de entrada y la documentación en carpetas diferenciadas. Una estructura típica incluiría carpetas `src/` para código, `data/` para archivos de datos, `docs/` para documentación y `tests/` para pruebas unitarias. El uso de control de versiones, aunque se estudiará en detalle posteriormente, debe iniciarse desde los primeros proyectos para establecer hábitos de trabajo profesionales.\n", "\n", "Con nuestro intérprete Python correctamente instalado y configurado, junto con un entorno de desarrollo preparado para la escritura y ejecución de código, hemos establecido las bases técnicas necesarias para dar vida a nuestros algoritmos. Es momento de escribir nuestro primer programa y explorar la sintaxis básica que nos permitirá comunicarnos efectivamente con el intérprete.\n", "\n", "### Primer programa: sintaxis básica y uso de print()\n", "\n", "Ya contamos con Python listo para ejecutar nuestro primer programa, marcando así el inicio de nuestra comunicación directa con el intérprete. Este momento representa la transición desde la conceptualización teórica hacia la implementación práctica, donde nuestros algoritmos cobran vida mediante instrucciones ejecutables.\n", "\n", "Un programa Python consiste fundamentalmente en una secuencia de instrucciones que el intérprete ejecuta línea por línea, de arriba hacia abajo. Para crear nuestro primer script, abrimos nuestro editor preferido y creamos un archivo con extensión `.py`, por ejemplo `primer_programa.py`. La extensión es crucial ya que identifica el archivo como código Python, permitiendo que el sistema operativo y los editores apliquen el tratamiento adecuado. Python utiliza por defecto la codificación UTF-8, lo que permite incluir caracteres especiales y acentos sin configuración adicional.\n", "\n", "La instrucción `print()` constituye nuestra primera puerta de comunicación con el usuario, permitiendo mostrar información en la consola. Su sintaxis básica es sorprendentemente flexible y potente:\n", "\n", "```python\n", "print(\"Hola, mundo\")\n", "print(\"Suma:\", 2 + 3)\n", "print(f\"El resultado es {10 * 4}\")\n", "```\n", "\n", "El primer ejemplo muestra el uso básico con una **cadena de caracteres** o string, delimitada por comillas dobles. Python acepta tanto comillas simples (`'`) como dobles (`\"`), pero es importante mantener consistencia dentro de cada cadena. El segundo ejemplo demuestra cómo `print()` puede combinar texto y expresiones numéricas separadas por comas, insertando automáticamente espacios entre los elementos. El tercer ejemplo introduce las **f-strings** (cadenas formateadas), una característica moderna que permite insertar expresiones directamente dentro de las cadenas precediendo la cadena con `f` y colocando las expresiones entre llaves `{}`.\n", "\n", "La ejecución del programa puede realizarse de tres maneras principales, cada una apropiada para diferentes contextos. Desde la **terminal o línea de comandos**, navegamos hasta la carpeta que contiene nuestro archivo y ejecutamos `python primer_programa.py`. Este método es ideal para scripts finalizados y automatización de tareas. El **intérprete interactivo** se inicia escribiendo `python` en la terminal, mostrando el prompt `>>>` donde podemos escribir instrucciones individuales que se ejecutan inmediatamente al presionar Enter. Esta modalidad resulta perfecta para experimentación y pruebas rápidas. Finalmente, los **IDEs modernos** proporcionan botones de \"Run\" o \"Ejecutar\" que simplifican el proceso, siendo la opción preferida durante el desarrollo activo.\n", "\n", "Los **comentarios** representan un elemento fundamental para la legibilidad y mantenibilidad del código. Se crean mediante el símbolo `#`, haciendo que el intérprete ignore todo el texto que sigue en esa línea:\n", "\n", "```python\n", "# Este es un comentario explicativo\n", "print(\"Hola, mundo\")  # Comentario al final de la línea\n", "```\n", "\n", "Los comentarios efectivos explican el **por qué** del código, no meramente el **qué** hace. Un buen comentario anticipa dudas futuras y facilita la comprensión del código a otros desarrolladores, incluido nuestro yo futuro.\n", "\n", "Python se distingue por hacer obligatoria la **indentación** para definir bloques de código, una característica que inicialmente puede parecer restrictiva pero que fuerza la escritura de código legible y bien estructurado. Aunque exploraremos esta característica en detalle con las estructuras de control, es importante comprender desde el inicio que Python utiliza espacios o tabulaciones (preferiblemente 4 espacios) para indicar jerarquía:\n", "\n", "```python\n", "# Ejemplo de indentación incorrecta (genera IndentationError)\n", "print(\"Primera línea\")\n", "    print(\"Segunda línea\")  # Error: indentación inesperada\n", "```\n", "\n", "La corrección de este error requiere alinear ambas instrucciones al mismo nivel, ya que en este contexto no existe una estructura que justifique la indentación adicional.\n", "\n", "Desarrollemos ahora un ejercicio práctico que combina los conceptos presentados. Nuestro objetivo es crear un programa que solicite el nombre del usuario y lo salude personalmente:\n", "\n", "```python\n", "# Programa para saludar al usuario\n", "nombre = input(\"¿Cuál es tu nombre? \")\n", "print(f\"¡Hola, {nombre}! Bienvenido a Python.\")\n", "```\n", "\n", "Al ejecutar este programa, aparecerá en la consola el mensaje \"¿Cuál es tu nombre? \" y el programa esperará la entrada del usuario. Después de que el usuario escriba su nombre y presione Enter, el programa mostrará un saludo personalizado. La función `input()` siempre devuelve una cadena de texto, independientemente de lo que escriba el usuario.\n", "\n", "Para maximizar la efectividad de nuestro trabajo, es recomendable seguir algunas **prácticas inmediatas**. Evitar tildes y espacios en nombres de archivo previene problemas de compatibilidad entre diferentes sistemas. Adoptar la convención `snake_case` para nombres de variables (usando guiones bajos para separar palabras) mejora la legibilidad y sigue las convenciones de Python. Guardar el trabajo con frecuencia evita pérdidas de código y facilita la experimentación sin temor.\n", "\n", "Es importante distinguir entre diferentes tipos de errores que pueden surgir durante el desarrollo. Los **errores sintácticos** ocurren cuando el código no sigue las reglas del lenguaje Python, como paréntesis desbalanceados o palabras clave mal escritas. Estos errores se detectan antes de la ejecución y Python proporciona mensajes descriptivos sobre su ubicación. Los **errores de ejecución** o excepciones ocurren cuando el código es sintácticamente correcto pero encuentra problemas durante la ejecución, como dividir por cero o usar una variable no definida. El manejo detallado de estos errores se abordará en el tema dedicado a excepciones.\n", "\n", "Este primer contacto con la programación en Python nos ha proporcionado las herramientas básicas para crear programas funcionales y comunicarnos con el intérprete. La función `print()` actúa como nuestra ventana de salida, permitiendo observar los resultados de nuestros cálculos y operaciones. En el próximo tema, profundizaremos en los tipos de datos y operaciones fundamentales que dan significado a las expresiones que evaluamos dentro de nuestras instrucciones `print()`, expandiendo significativamente nuestras capacidades de procesamiento y manipulación de información. La experimentación continua con variaciones de estos ejemplos básicos consolidará los conceptos presentados y preparará el terreno para estructuras más complejas.\n"]}], "source": ["print(result.content[1][\"text\"])"]}, {"cell_type": "code", "execution_count": 68, "id": "99a57315", "metadata": {}, "outputs": [], "source": ["tema_result = result"]}, {"cell_type": "code", "execution_count": 72, "id": "1eaf8948", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["## Te<PERSON> 1. Introducción a la programación y al lenguaje Python\n", "\n", "### Presentación de la asignatura y herramientas de trabajo\n", "\n", "La programación constituye una disciplina fundamental dentro del ámbito de la informática, representando el puente entre la conceptualización de soluciones y su implementación práctica en sistemas digitales. Esta capacidad de traducir ideas y procesos lógicos en instrucciones que las máquinas pueden ejecutar nos permite automatizar tareas repetitivas, resolver problemas complejos de manera eficiente y crear productos digitales que impactan en múltiples sectores de la sociedad. En este contexto, hemos seleccionado Python como lenguaje de aprendizaje debido a su sintaxis clara y legible, que facilita la comprensión de conceptos fundamentales, así como por su extenso ecosistema de librerías que lo convierten en una herramienta versátil para diversos campos de aplicación.\n", "\n", "Al finalizar esta asignatura, el estudiante habrá desarrollado una serie de competencias esenciales que le permitirán abordar con confianza los desafíos de la programación moderna. Entre los objetivos principales se encuentra el desarrollo del pensamiento algorítmico, una habilidad cognitiva que trasciende el ámbito informático y permite descomponer problemas complejos en pasos manejables y lógicos. Asimismo, se alcanzará el dominio de las estructuras básicas de programación, incluyendo variables, tipos de datos, estructuras de control y funciones, elementos que constituyen los cimientos sobre los cuales se construyen programas más sofisticados.\n", "\n", "La capacidad de escribir programas robustos y mantenibles representa otro pilar fundamental de la formación. Esto implica no solo la correcta implementación de la lógica de negocio, sino también la adopción de buenas prácticas de programación, el manejo adecuado de errores y excepciones, y la documentación clara del código. Finalmente, se fomentará el trabajo colaborativo en proyectos de desarrollo, una competencia indispensable en el entorno profesional actual, donde los equipos multidisciplinarios son la norma.\n", "\n", "Estos objetivos se articulan coherentemente con la estructura de la asignatura, organizada en tres bloques temáticos progresivos. El **Bloque I** establece los fundamentos de la programación y del lenguaje Python, introduciendo conceptos básicos como tipos de datos, operaciones y estructuras de control de flujo. El **Bloque II** profundiza en las estructuras de datos y la programación modular, abordando listas, diccionarios, funciones y manejo de excepciones. Finalmente, el **Bloque III** se centra en aspectos avanzados como la persistencia de datos, el uso de librerías externas y el desarrollo colaborativo con control de versiones.\n", "\n", "Las competencias prácticas que se desarrollarán a lo largo del curso incluyen, en primer lugar, la **comprensión y análisis de problemas**, habilidad que implica identificar los requisitos, restricciones y objetivos de una situación dada antes de proceder a su resolución. El **diseño de algoritmos** constituye la siguiente fase, donde se aprende a estructurar la solución de manera lógica y eficiente, considerando diferentes alternativas y evaluando sus ventajas y desventajas.\n", "\n", "La **traducción de algoritmos a código ejecutable** representa el corazón de la programación práctica, requiriendo no solo conocimiento sintáctico del lenguaje, sino también comprensión de las mejores prácticas de implementación. Las actividades de **prueba y depuración** son igualmente cruciales, desarrollando la capacidad de identificar y corregir errores, así como de validar que el programa funciona correctamente bajo diferentes condiciones. La **documentación del código** se presenta como una competencia transversal que facilita el mantenimiento y la colaboración, mientras que el **trabajo colaborativo** introduce metodologías y herramientas para el desarrollo en equipo.\n", "\n", "<tabla>\n", "| **Categoría** | **Ejemplos propuestos** | **<PERSON><PERSON><PERSON>ito principal** |\n", "|:--------------|:------------------------|:------------------------|\n", "| **Intérprete Python 3.x** | CPython, Anaconda | Ejecutar código |\n", "| **Editor/IDE** | VS Code, PyCharm CE | Escribir y depurar |\n", "| **Terminal** | bash, PowerShell | Ejecutar comandos |\n", "| **Entornos virtuales** | venv, conda | Aislar dependen<PERSON>s |\n", "| **Control de versiones** | Git + GitHub/GitLab | Trabajo en equipo |\n", "</tabla>\n", "\n", "*Tabla 1. Herramientas principales para el desarrollo en Python y sus funciones específicas.*\n", "\n", "En cuanto a los requisitos técnicos, se recomienda disponer de un ordenador con al menos 4 GB de RAM y 10 GB de espacio libre en disco duro, aunque estas especificaciones son modestas y la mayoría de equipos actuales las superan ampliamente. El sistema operativo puede ser Windows, macOS o cualquier distribución de Linux, ya que Python es multiplataforma. Es fundamental establecer desde el inicio buenas prácticas de trabajo, como mantener copias de seguridad del código en servicios de almacenamiento en la nube, utilizar el copy-paste de manera responsable priorizando la comprensión sobre la velocidad, y desarrollar el hábito de comentar el código de manera clara y concisa.\n", "\n", "La dinámica de trabajo combina sesiones teóricas, donde se presentan los conceptos fundamentales, con laboratorios prácticos que permiten aplicar inmediatamente los conocimientos adquiridos. Las entregas periódicas de ejercicios y proyectos proporcionan oportunidades de evaluación continua, mientras que los foros de dudas facilitan la resolución de problemas y el intercambio de conocimientos entre estudiantes.\n", "\n", "Para ilustrar el proceso de desarrollo, imaginemos que queremos crear un programa para clasificar nuestros gastos mensuales por categorías. Inicialmente, identificaríamos los tipos de gastos (alimentación, transporte, ocio, etc.) y estableceríamos criterios de clasificación. A continuación, diseñaríamos un algoritmo que lea la información de los gastos, los analice según nuestros criterios y genere un resumen organizado. Posteriormente, traduciremos este algoritmo a código Python, utilizando estructuras de datos apropiadas como diccionarios para almacenar las categorías y listas para los gastos individuales. Finalmente, probaríamos el programa con datos reales, depuraríamos posibles errores y documentaríamos su funcionamiento para futuras referencias o colaboradores.\n", "\n", "Este enfoque sistemático, que progresa desde la conceptualización hasta la implementación, será el hilo conductor de toda la asignatura. Antes de adentrarnos en los aspectos técnicos de Python, es fundamental comprender qué constituye un algoritmo y cómo podemos representarlo de manera visual y comprensible, aspectos que exploraremos en el siguiente apartado.\n", "\n", "La programación es una disciplina que recompensa la curiosidad y el aprendizaje activo. Te invitamos a experimentar con los conceptos presentados, a plantear preguntas y a explorar más allá de los ejemplos proporcionados. Cada error es una oportunidad de aprendizaje, y cada programa exitoso un paso hacia el dominio de esta poderosa herramienta de resolución de problemas.\n", "\n", "### Concepto de algoritmo y diagramas de flujo\n", "\n", "Habiendo establecido nuestros objetivos y herramientas de trabajo, es momento de adentrarnos en el concepto fundamental que sustenta cualquier programa informático: el algoritmo. Comprender qué es un algoritmo y cómo representarlo de manera clara constituye el primer paso esencial hacia el desarrollo de software efectivo.\n", "\n", "Un **algoritmo** puede definirse como una secuencia finita, ordenada y no ambigua de pasos que transforma un conjunto de entradas (datos de entrada) en un conjunto de salidas (resultados) para resolver un problema específico. Esta definición, aunque téc<PERSON>, cobra vida cuando la aplicamos a actividades cotidianas. Consideremos el proceso de preparar una taza de café: comenzamos con ingredientes específicos (agua, café molido, azúcar opcional), seguimos una serie de pasos ordenados (calentar el agua, añadir el café, mezclar, endulzar si se desea), y obtenemos un resultado predecible (una taza de café lista para consumir).\n", "\n", "Este ejemplo ilustra las tres propiedades fundamentales de todo algoritmo. La **finitud** garantiza que el proceso terminará en un número determinado de pasos; no podemos quedarnos indefinidamente preparando café. El **determinismo** asegura que cada paso está claramente definido sin ambigüedades; \"calentar el agua\" implica acciones específicas que cualquier persona puede interpretar y ejecutar de manera similar. La **efectividad** establece que cada operación debe ser realizable con los recursos disponibles; no podemos requerir herramientas inexistentes o procedimientos imposibles de ejecutar.\n", "\n", "En el contexto de la programación, necesitamos herramientas que nos permitan expresar algoritmos de manera precisa antes de traducirlos a código ejecutable. El **pseudocódigo** representa una de estas herramientas, funcionando como un puente entre el lenguaje natural y el código de programación. Se caracteriza por utilizar un lenguaje estructurado similar al de los lenguajes de programación, pero sin restringirse a la sintaxis específica de ninguno en particular.\n", "\n", "El pseudocódigo resulta especialmente útil durante las fases de diseño y planificación, cuando necesitamos concentrarnos en la lógica del algoritmo sin preocuparnos por detalles sintácticos específicos. Su flexibilidad permite expresar ideas de manera clara y comprensible, facilitando la comunicación entre programadores y la detección temprana de errores lógicos. Además, sirve como documentación valiosa del proceso de pensamiento que condujo a la solución final.\n", "\n", "Como alternativa al pseudocódigo, los **diagramas de flujo** ofrecen una representación visual de algoritmos que resulta especialmente efectiva para comprender la estructura lógica y el flujo de control de un proceso. Estos diagramas utilizan símbolos geométricos estandarizados, cada uno con un significado específico, conectados mediante flechas que indican la dirección del flujo de ejecución.\n", "\n", "<tabla>\n", "| **Símbolo** | **Significado** |\n", "|:------------|:----------------|\n", "| Óvalo | Inicio o fin del algoritmo |\n", "| Rectángulo | Proceso o acción a realizar |\n", "| Rombo | Decisión o condición |\n", "| Paralelogramo | Entrada o salida de datos |\n", "| <PERSON><PERSON><PERSON><PERSON> pe<PERSON> | Conector o punto de unión |\n", "</tabla>\n", "\n", "*Tabla 2. Símbolos básicos utilizados en diagramas de flujo y sus significados específicos.*\n", "\n", "Las flechas en los diagramas de flujo indican la secuencia de ejecución, guiando al lector a través del algoritmo de manera intuitiva. En las decisiones (rombos), las flechas se etiquetan típicamente con \"Sí/No\" o \"Verdadero/Falso\" para indicar qué camino seguir según el resultado de la evaluación.\n", "\n", "Para consolidar estos conceptos, consideremos un algoritmo que calcule la suma de los números enteros del 1 al N, donde N es un valor proporcionado por el usuario. Este problema, aparentemente simple, ilustra elementos fundamentales como la entrada de datos, el procesamiento iterativo y la salida de resultados.\n", "\n", "El pseudocódigo de alto nivel podría expresarse así:\n", "\n", "```\n", "1. Solicitar al usuario el valor de N\n", "2. <PERSON><PERSON><PERSON><PERSON> suma = 0\n", "3. <PERSON><PERSON><PERSON><PERSON> = 1\n", "4. <PERSON><PERSON><PERSON> contador <= N:\n", "   a. Agregar contador a suma\n", "   b. <PERSON>rementar contador en 1\n", "5. Mostrar el resultado de suma\n", "```\n", "\n", "<mermaid>\n", "flowchart TD\n", "    A[Inicio] --> B[Solicitar N al usuario]\n", "    B --> C[suma = 0, contador = 1]\n", "    C --> D{contador <= N?}\n", "    D -->|Sí| E[suma = suma + contador]\n", "    E --> F[contador = contador + 1]\n", "    F --> D\n", "    D -->|No| G[Mostrar suma]\n", "    G --> H[Fin]\n", "</mermaid>\n", "\n", "*Figura 1. Diagrama de flujo para el cálculo de la suma de números del 1 al N.*\n", "\n", "Cada representación (pseudocódigo y diagrama de flujo) ofrece ventajas específicas según el contexto y la audiencia. Los diagramas de flujo destacan en la comunicación con personas no familiarizadas con la programación, ya que su naturaleza visual facilita la comprensión del flujo lógico general. Son particularmente útiles en reuniones de equipo, documentación de procesos de negocio y fases iniciales de diseño donde se requiere una visión global del algoritmo.\n", "\n", "<PERSON>r otro lado, el pseudocódigo proporciona mayor precisión técnica y se aproxima más al código final, lo que lo convierte en una herramienta valiosa durante las fases de implementación. Permite expresar detalles algorítmicos complejos de manera más compacta y resulta más eficiente para programadores experimentados que necesitan documentar la lógica específica de sus soluciones.\n", "\n", "Sin embargo, ambas representaciones también presentan limitaciones. Los diagramas de flujo pueden volverse complejos y difíciles de mantener cuando representan algoritmos extensos, mientras que el pseudocódigo puede resultar intimidante para audiencias no técnicas. La elección entre una u otra herramienta, o su uso combinado, dependerá del propósito específico, la audiencia objetivo y la complejidad del problema a resolver.\n", "\n", "Es importante destacar que, aunque hemos introducido aquí la estructura de bucle en nuestro ejemplo, su estudio formal y detallado se abordará en profundidad durante el Tema 3, dedicado a las estructuras de control de flujo. Por el momento, es suficiente comprender que los bucles permiten repetir operaciones de manera controlada, un concepto que resulta fundamental en la mayoría de algoritmos útiles.\n", "\n", "Para convertir estos algoritmos conceptuales en programas ejecutables, necesitaremos establecer un entorno de desarrollo adecuado. Esto implica instalar un intérprete de Python en nuestro sistema y configurar las herramientas que nos permitirán escribir, probar y depurar nuestro código de manera eficiente. Este proceso técnico, que puede parecer intimidante inicialmente, se simplifica considerablemente cuando se aborda de manera sistemática y ordenada.\n", "\n", "La importancia de planificar antes de codificar no puede subestimarse. Al igual que un arquitecto no comenzaría la construcción de un edificio sin planos detallados, un programador experimentado invierte tiempo en diseñar algoritmos claros antes de escribir la primera línea de código. Esta metodología no solo previene errores costosos, sino que también facilita el mantenimiento y la colaboración en proyectos de desarrollo.\n", "\n", "### Instalación de Python y configuración del entorno de desarrollo\n", "\n", "Con una comprensión sólida de los algoritmos y sus representaciones, estamos preparados para dar el paso crucial hacia la implementación práctica. La finalidad de esta sección es proporcionarte las herramientas necesarias para transformar los algoritmos que hemos diseñado conceptualmente en programas ejecutables. Recordemos el ejemplo del cálculo de la suma de números del 1 al N: ahora tendremos la capacidad técnica de convertir ese diagrama de flujo en un programa real que funcione en nuestro ordenador.\n", "\n", "Python, a diferencia de otros lenguajes de programación, no es simplemente un intérprete único, sino un ecosistema completo con múltiples distribuciones disponibles. **CPython** representa la implementación oficial y más ampliamente utilizada, desarrollada en lenguaje C y mantenida por la Python Software Foundation. Esta versión proporciona el intérprete estándar y sirve como referencia para otras implementaciones.\n", "\n", "**Anaconda**, por su parte, constituye una distribución especializada que incluye Python junto con una extensa colección de librerías científicas y herramientas de gestión de paquetes. Resulta especialmente popular en entornos de ciencia de datos, análisis estadístico y aprendizaje automático, aunque puede resultar excesiva para principiantes debido a su tamaño y complejidad inicial.\n", "\n", "Para nuestros propósitos académicos, recomendamos la instalación de **Python 3.x** en su versión estable más reciente (actualmente 3.11 o 3.12). Esta elección se fundamenta en que Python 2.x ha llegado al final de su vida útil y ya no recibe actualizaciones de seguridad, mientras que Python 3.x representa el presente y futuro del lenguaje, con mejoras continuas en rendimiento, seguridad y funcionalidades.\n", "\n", "#### Descarga e instalación multiplataforma\n", "\n", "El proceso de instalación varía ligeramente según el sistema operativo, pero mantiene una estructura similar en todas las plataformas. Para **Windows**, accede al sitio web oficial python.org y descarga el instalador correspondiente a tu arquitectura (32 o 64 bits, aunque prácticamente todos los sistemas actuales utilizan 64 bits). Ejecuta el archivo descargado como administrador y, crucialmente, asegúrate de marcar la casilla \"Add Python to PATH\" durante la instalación. Esta opción permite ejecutar Python desde cualquier ubicación en la línea de comandos, evitando configuraciones manuales posteriores.\n", "\n", "En **macOS**, puedes utilizar el instalador oficial del sitio web o, alternativamente, gestores de paquetes como Homebrew (`brew install python3`). Los sistemas Mac modernos incluyen una versión de Python preinstalada, pero suele ser una versión anterior o Python 2.x, por lo que la instalación independiente resulta recomendable.\n", "\n", "Para **distribuciones Linux**, la mayoría incluyen Python 3 preinstalado. Sin embargo, puedes actualizarlo o instalar versiones específicas utilizando el gestor de paquetes de tu distribución: `apt install python3` en Ubuntu/Debian, `yum install python3` en CentOS/RHEL, o `pacman -S python` en Arch Linux.\n", "\n", "#### Verificación en terminal\n", "\n", "Una vez completada la instalación, es fundamental verificar que Python está correctamente disponible en el sistema. Abre la línea de comandos: en Windows, busca \"cmd\" o \"PowerShell\" en el menú de inicio; en macOS, utiliza \"Terminal\" desde las aplicaciones; en Linux, abre tu emulador de terminal preferido.\n", "\n", "Ejecuta el comando `python --version` o `python -V`. Si la instalación fue exitosa, verás información similar a \"Python 3.11.2\". En sistemas Unix (macOS y Linux), es posible que necesites utilizar `python3 --version` para distinguir entre versiones de Python 2 y 3 que puedan coexistir en el sistema.\n", "\n", "Si encuentras errores como \"comando no encontrado\" o \"no se reconoce como comando interno\", verifica que Python se añadió correctamente al PATH del sistema durante la instalación.\n", "\n", "#### Configuración mínima de un entorno de trabajo\n", "\n", "La selección del entorno de desarrollo influye significativamente en la productividad y experiencia de programación. Entre las opciones disponibles, **Visual Studio Code** destaca por su equilibrio entre funcionalidad y simplicidad, ofreciendo excelente soporte para Python mediante extensiones, depuración integrada y un ecosistema rico de plugins. **PyCharm Community Edition** proporciona un entorno más completo y especializado, con herramientas avanzadas de refactorización y análisis de código, aunque con una curva de aprendizaje más pronunciada. **Thonny** se diseñó específicamente para principiantes, con una interfaz simplificada y herramientas educativas integradas.\n", "\n", "<tabla>\n", "| **IDE/Editor** | **Curva de aprendizaje** | **Depuración integrada** | **Plugins/Extensiones** | **Consumo de recursos** |\n", "|:---------------|:-------------------------|:-------------------------|:------------------------|:------------------------|\n", "| **VS Code** | Moderada | Excelente | Muy amplio | Medio |\n", "| **PyCharm CE** | Alta | Excepcional | Integrado | Alto |\n", "| **Thonny** | Muy baja | Básica | Limitado | Bajo |\n", "</tabla>\n", "\n", "*Tabla 3. Comparación de criterios de selección para entornos de desarrollo Python.*\n", "\n", "Los **entornos virtuales** representan una práctica esencial en el desarrollo Python, permitiendo aislar las dependencias de diferentes proyectos. Crea un entorno virtual navegando a tu directorio de proyecto en la terminal y ejecutando `python -m venv venv`. Este comando crea una carpeta llamada \"venv\" que contiene una instalación independiente de Python y pip.\n", "\n", "Para activar el entorno virtual, utiliza `venv\\Scripts\\activate` en Windows o `source venv/bin/activate` en macOS/Linux. Cuando está activo, observarás que el prompt de la terminal se modifica para mostrar \"(venv)\" al inicio. Para desactivarlo, simplemente ejecuta `deactivate`.\n", "\n", "**Jupyter Notebook** merece una mención especial como herramienta complementaria, especialmente útil para experimentación, análisis de datos y documentación interactiva. Instálalo con `pip install notebook` dentro de tu entorno virtual activado, y ejecútalo con `jupyter notebook`. Esta herramienta abre una interfaz web que permite combinar código, visualizaciones y texto explicativo en un mismo documento.\n", "\n", "#### Micro-chequeo práctico\n", "\n", "Para verificar que todo funciona correctamente, realiza el siguiente ejercicio: crea una carpeta llamada \"mi_primer_proyecto\" en tu escritorio, navega hasta ella en la terminal y activa un entorno virtual nuevo. Una vez activado, instala la librería `requests` ejecutando `pip install requests`. Verifica la instalación ejecutando `pip list` - de<PERSON><PERSON> ver `requests` en la lista de paquetes instalados.\n", "\n", "Ahora, desactiva el entorno virtual y vuelve a ejecutar `pip list`. <PERSON> `requests` no aparece (o aparece una versión diferente), la configuración es correcta: has logrado aislar las dependencias del proyecto del sistema global.\n", "\n", "Las buenas prácticas de organización incluyen mantener una estructura de directorios clara, separando el código fuente, los datos de entrada y la documentación en carpetas específicas. Evita espacios y caracteres especiales en nombres de archivos y carpetas, optando por el estilo snake_case (palabras_separadas_por_guiones_bajos) que es convencional en Python.\n", "\n", "Aunque el control de versiones con Git será objeto de estudio detallado en el Bloque III, es recomendable inicializar repositorios desde el comienzo de cada proyecto. Esto no solo proporciona un historial de cambios, sino que también facilita la colaboración y el respaldo del código.\n", "\n", "Con el intérprete correctamente instalado y configurado, junto con un entorno de desarrollo preparado, estamos listos para escribir nuestro primer programa Python. En el siguiente apartado, exploraremos la sintaxis básica del lenguaje y crearemos programas simples pero funcionales que nos permitirán verificar que nuestra configuración funciona correctamente y comenzar a familiarizarnos con el paradigma de programación en Python.\n", "\n", "### Primer programa: sintaxis básica y uso de print()\n", "\n", "Ya contamos con Python listo para ejecutar nuestro primer programa, marcando el momento en que la teoría se encuentra con la práctica. La creación de un programa sigue un proceso sistemático: primero creamos un archivo con las instrucciones que deseamos ejecutar, luego escribimos estas instrucciones utilizando la sintaxis del lenguaje, y finalmente ejecutamos el archivo para observar los resultados.\n", "\n", "Un script de Python posee una estructura mínima caracterizada por la simplicidad: consiste en una secuencia de líneas de código que el intérprete ejecuta de arriba hacia abajo, una tras otra. Para que el sistema reconozca nuestro archivo como un programa Python, debe llevar la extensión `.py`, por ejemplo: `mi_programa.py` o `calculadora.py`. Python utiliza por defecto la codificación UTF-8, lo que nos permite usar caracteres especiales y acentos sin configuración adicional, una ventaja significativa para programadores que trabajan en idiomas distintos al inglés.\n", "\n", "La instrucción `print()` constituye nuestra primera puerta de comunicación con el usuario, permitiendo mostrar información en la pantalla. Su sintaxis básica es intuitiva y versátil:\n", "\n", "```python\n", "print(\"Hola, mundo\")\n", "print(\"Suma:\", 2 + 3)\n", "print(f\"La variable x vale {x}\")\n", "```\n", "\n", "En el primer ejemplo, `print(\"Hola, mundo\")` muestra literalmente el texto contenido entre comillas. Las **cadenas de caracteres** (strings) pueden delimitarse con comillas simples (`'texto'`) o dobles (`\"texto\"`), siendo funcionalmente equivalentes. La elección entre una u otra suele depender del contenido: si el texto incluye apostrofes, resulta más cómodo usar comillas dobles, y viceversa.\n", "\n", "El segundo ejemplo demuestra que `print()` puede mostrar el resultado de operaciones matemáticas. La expresión `2 + 3` se evalúa antes de mostrarse, por lo que el resultado será `Suma: 5`. Esta capacidad de evaluar expresiones dentro de `print()` resulta fundamental para mostrar resultados de cálculos y operaciones.\n", "\n", "El tercer ejemplo introduce las **f-strings** (cadenas formateadas), una característica moderna de Python que permite insertar variables y expresiones directamente dentro del texto. El prefijo `f` antes de las comillas indica que la cadena puede contener expresiones entre llaves `{}` que serán evaluadas y sustituidas por sus valores.\n", "\n", "#### Métodos de ejecución\n", "\n", "Existen tres formas principales de ejecutar código Python, cada una útil en diferentes situaciones. **Desde terminal**, navega hasta la carpeta que contiene tu archivo y ejecuta `python primer_programa.py`. Este método resulta ideal para programas completos y scripts automatizados, proporcionando un entorno similar al que encontrarás en servidores de producción.\n", "\n", "El **intérprete interactivo** se activa escribiendo `python` en la terminal, lo que abre un prompt `>>>` donde puedes escribir código línea por línea. Cada línea se ejecuta inmediatamente, mostrando el resultado al presionar Enter. Este modo es excelente para experimentar con código, probar funciones rápidamente o realizar cálculos puntuales.\n", "\n", "Los **entornos de desarrollo integrados** (IDEs) proporcionan la experiencia más cómoda, con botones de \"Run\" o \"Ejecutar\" que lanzan el programa con un simple clic. Además, ofrecen características avanzadas como depuración visual, autocompletado y detección de errores en tiempo real.\n", "\n", "#### Comentarios y legibilidad del código\n", "\n", "Los comentarios representan una herramienta fundamental para la documentación y mantenimiento del código. En Python, las líneas que comienzan con `#` son ignoradas por el intérprete, permitiendo incluir explicaciones y notas:\n", "\n", "```python\n", "# Esto es un comentario explicativo\n", "print(\"Este código sí se ejecuta\")\n", "# El siguiente print muestra la suma de dos números\n", "print(\"Resultado:\", 15 + 27)\n", "```\n", "\n", "Una buena práctica consiste en documentar la **intención** del código, no solo lo que hace. En lugar de escribir `# Suma 2 + 3`, es más útil comentar `# Calcula el precio total incluyendo impuestos`. Los comentarios deben mantenerse actualizados con el código; comentarios obsoletos pueden generar más confusión que beneficio.\n", "\n", "#### Indentación obligatoria\n", "\n", "Python se distingue de otros lenguajes por utilizar la indentación (espacios o tabulaciones al inicio de línea) para definir bloques de código, en lugar de llaves o palabras clave específicas. Aunque inicialmente no escribiremos estructuras complejas, es importante comprender este concepto fundamental:\n", "\n", "```python\n", "# Código correctamente indentado\n", "if True:\n", "    print(\"Esta línea está indentada\")\n", "    print(\"Esta también\")\n", "print(\"Esta línea vuelve al nivel base\")\n", "```\n", "\n", "Un error común produce `IndentationError`:\n", "\n", "```python\n", "# <PERSON><PERSON><PERSON>o - produci<PERSON><PERSON> error\n", "if True:\n", "print(\"Esta línea debería estar indentada\")  # Error!\n", "```\n", "\n", "La corrección requiere añadir espacios al inicio de la línea:\n", "\n", "```python\n", "# <PERSON><PERSON><PERSON> corregido\n", "if True:\n", "    print(\"Esta línea está correctamente indentada\")\n", "```\n", "\n", "#### <PERSON><PERSON><PERSON><PERSON> dirigido: interacción con el usuario\n", "\n", "Vamos a crear un programa que solicite información al usuario y le responda de manera personalizada. La función `input()` permite leer texto desde el teclado:\n", "\n", "```python\n", "# Solicitar el nombre del usuario\n", "nombre = input(\"¿Cómo te llamas? \")\n", "\n", "# Almacenar el nombre en una variable y saludar\n", "print(f\"¡Hola, {nombre}! Bienvenido a Python.\")\n", "\n", "# Solicitar la edad\n", "edad = input(\"¿Cuántos años tienes? \")\n", "\n", "# Mostrar un mensaje personalizado\n", "print(f\"Genial, {nombre}. Con {edad} años estás en el momento perfecto para aprender programación.\")\n", "```\n", "\n", "Al ejecutar este programa en la consola, verás algo similar a:\n", "\n", "```\n", "¿Cómo te llamas? Ana\n", "¡<PERSON><PERSON>, Ana! Bienvenido a Python.\n", "¿Cuántos años tienes? 22\n", "<PERSON><PERSON>, <PERSON>. Con 22 años estás en el momento perfecto para aprender programación.\n", "```\n", "\n", "#### Consejos rápidos para principiantes\n", "\n", "Desarrolla desde el inicio hábitos que te beneficiarán a largo plazo. **Evita tildes y espacios** en nombres de archivos Python; utiliza nombres descriptivos como `calculadora_basica.py` en lugar de `cálculo 1.py`. Adopta la convención **snake_case** para nombres de variables: `nombre_usuario` en lugar de `nombreUsuario` o `NombreUsuario`.\n", "\n", "**Guarda tu trabajo con frecuencia** - una combinación de teclas como Ctrl+S debería convertirse en un reflejo automático. Muchos editores modernos guardan automáticamente, pero no dependas completamente de esta funcionalidad.\n", "\n", "**Experimenta con variaciones** de los ejemplos proporcionados. Cambia los textos, prueba diferentes nombres de variables, añade más líneas de `print()`. La programación se aprende mejor mediante la práctica activa que mediante la lectura pasiva.\n", "\n", "#### Tipos de errores\n", "\n", "Durante el desarrollo, encontrarás dos categorías principales de errores. Los **errores sintácticos** ocurren cuando el código no respeta las reglas del lenguaje:\n", "\n", "```python\n", "print(\"Hola mundo\"  # Falta paréntesis de cierre - SyntaxError\n", "```\n", "\n", "Los **errores de ejecución** aparecen cuando el código es sintácticamente correcto pero encuentra problemas durante la ejecución:\n", "\n", "```python\n", "nombre = input(\"Tu nombre: \")\n", "print(f\"Tu nombre tiene {len(nombre)} caracteres\")\n", "# Si el usuario no introduce nada, len() funciona correctamente\n", "# Pero si intentáramos dividir por cero, obtendríamos ZeroDivisionError\n", "```\n", "\n", "El manejo detallado de errores y excepciones será objeto de estudio en el Tema 7, donde aprenderemos técnicas avanzadas para crear programas robustos que gestionen situaciones imprevistas de manera elegante.\n", "\n", "Con estos fundamentos de sintaxis básica y el uso de `print()`, hemos dado los primeros pasos en el mundo de la programación Python. En el siguiente tema profundizaremos en los tipos de datos y operaciones básicas, explorando cómo Python maneja números, textos y valores lógicos. Comprenderemos en detalle qué significan las expresiones que hemos estado evaluando dentro de `print()`, y cómo podemos manipular diferentes tipos de información de manera efectiva.\n", "\n", "La programación es un proceso iterativo de aprendizaje. Te animamos a experimentar con las variaciones de estos ejemplos básicos, a cometer errores y a aprender de ellos. Cada programa que escribas, por simple que parezca, representa un paso hacia el dominio de esta poderosa herramienta de resolución de problemas.\n"]}], "source": ["print(tema_result.content[1][\"text\"])"]}], "metadata": {"kernelspec": {"display_name": "ia_gestorcontenidosiagen_be (3.12.9)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}