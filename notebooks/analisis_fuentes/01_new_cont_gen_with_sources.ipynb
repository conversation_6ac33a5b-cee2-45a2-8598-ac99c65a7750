{"cells": [{"cell_type": "markdown", "id": "eaa6a4fc", "metadata": {}, "source": ["## Code set up"]}, {"cell_type": "code", "execution_count": 10, "id": "f1f5ce31", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-10 12:22:27,532 - logger - INFO - Initial user 'Unir demo' already exists.\n", "2025-07-10 12:22:29,115 - logger - INFO - Created prompts successfully\n"]}], "source": ["import sys, os\n", "from pathlib import Path\n", "from dotenv import load_dotenv\n", "load_dotenv()\n", "os.environ[\"DbHost\"] = \"localhost\"\n", "os.environ[\"DbPort\"] = \"5436\"\n", "parent_dir = Path.cwd().parent.parent\n", "sys.path.append(str(parent_dir))\n", "from src.api.common.dependency_container import DependencyContainer\n", "DependencyContainer.initialize(observability = False)\n", "from src.api.common.services.structs import ModelInfo\n", "index_repository = DependencyContainer.get_index_repository()\n", "\n", "from openai import AsyncOpenAI\n", "import os\n", "from dotenv import load_dotenv\n", "client = AsyncOpenAI(api_key=os.getenv(\"OpenaiApiKey\"))"]}, {"cell_type": "markdown", "id": "906ae222", "metadata": {}, "source": ["### Utilities"]}, {"cell_type": "code", "execution_count": 2, "id": "76de5e8b", "metadata": {}, "outputs": [], "source": ["from IPython.display import display, Markdown\n", "from src.api.common.services.structs import Asignatura\n", "\n", "def display_index_markdown(subject_index: Asignatura) -> str:\n", "    \"\"\"\n", "    Convierte un objeto de índice de asignatura en formato Markdown y lo muestra en un notebook.\n", "    \n", "    Args:\n", "        subject_index: Objeto GenerateIndexResponse con la estructura de la asignatura\n", "        \n", "    Returns:\n", "        str: El contenido en formato Markdown\n", "    \"\"\"\n", "    asignatura = subject_index.index\n", "    md_content = []\n", "    md_content.append(f\"# {asignatura.nombre}\\n\")\n", "    md_content.append(\"## Competencias\\n\")\n", "    for i, comp in enumerate(subject_index.competencies, 1):\n", "        md_content.append(f\"{i}. {comp.descripcion}\")\n", "    md_content.append(\"\")\n", "    md_content.append(\"## Estructura Temática\\n\")\n", "    for bloque_num, bloque in enumerate(asignatura.estructura.bloques_tematicos, 1):\n", "        md_content.append(f\"### Bloque {bloque_num}: {bloque.nombre}\\n\")\n", "        for tema_num, tema in enumerate(bloque.temas, 1):\n", "            md_content.append(f\"#### {bloque_num}.{tema_num}. {tema.nombre}\\n\")\n", "            for epigrafe_num, epigrafe in enumerate(tema.epigrafes, 1):\n", "                md_content.append(f\"##### {bloque_num}.{tema_num}.{epigrafe_num} {epigrafe}\")\n", "            md_content.append(\"\")\n", "    \n", "    markdown_text = \"\\n\".join(md_content)\n", "    \n", "    display(Markdown(markdown_text))"]}, {"cell_type": "markdown", "id": "bb40f515", "metadata": {}, "source": ["## Previous steps"]}, {"cell_type": "markdown", "id": "c9bec8ea", "metadata": {}, "source": ["### Visualizing index and competencies"]}, {"cell_type": "code", "execution_count": 3, "id": "375a3450", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-10 12:17:47,520 - logger - INFO - Index is order_id=1 version=1 created_by='ia_gen_user' created_at=datetime.datetime(2025, 7, 10, 9, 17, 56, 70556) id=1 status=<IndiceStatus.CONTENT_GENERATION: 'CONTENT_GENERATION'> is_displayed=False updated_by='ia_gen_user' updated_at=datetime.datetime(2025, 7, 10, 9, 24, 39, 364154)\n", "2025-07-10 12:17:47,521 - logger - INFO - Entered in async session\n", "2025-07-10 12:17:47,530 - logger - INFO - Indice inside get subject index is, order_id=1 version=1 created_by='ia_gen_user' created_at=datetime.datetime(2025, 7, 10, 9, 17, 56, 70556) id=1 status=<IndiceStatus.CONTENT_GENERATION: 'CONTENT_GENERATION'> is_displayed=False updated_by='ia_gen_user' updated_at=datetime.datetime(2025, 7, 10, 9, 24, 39, 364154)\n", "2025-07-10 12:17:47,540 - logger - INFO - <PERSON><PERSON><PERSON> inside get subject index is, [<PERSON><PERSON><PERSON>(id=1, position=1, created_by='ia_gen_user', updated_at=datetime.datetime(2025, 7, 10, 9, 18, 13, 441158), name='Fundamentos Económicos para la Ingeniería Informática', indice_id=1, updated_by=None, created_at=datetime.datetime(2025, 7, 10, 9, 18, 13, 440974)), <PERSON><PERSON><PERSON>(id=2, position=2, created_by='ia_gen_user', updated_at=datetime.datetime(2025, 7, 10, 9, 18, 13, 459020), name='Gestión Económica de Proyectos de Ingeniería Informática', indice_id=1, updated_by=None, created_at=datetime.datetime(2025, 7, 10, 9, 18, 13, 458887))]\n", "2025-07-10 12:17:47,540 - logger - INFO - Asignatura is, nombre='Economia - Grado en Ingenieria Informatica' estructura=Estructura(bloques_tematicos=[BloqueTematico(nombre='Fundamentos Económicos para la Ingeniería Informática', temas=[Tema(nombre='Principios Microeconómicos aplicados al Desarrollo de Software', epigrafes=['Demanda, oferta y elasticidad de productos de software', 'Costes de producción y economías de escala en servicios digitales', 'Estructuras de mercado y competencia en el sector TIC']), Tema(nombre='Macroeconomía y Políticas Públicas en el Sector TIC', epigrafes=['Indicadores macroeconómicos y ciclos tecnológicos', 'Políticas públicas, regulación y fiscalidad en la economía digital', 'Impacto de la transformación digital en el crecimiento económico y el empleo'])]), BloqueTematico(nombre='Gestión Económica de Proyectos de Ingeniería Informática', temas=[Tema(nombre='Evaluación Financiera y Gestión de Riesgos en Proyectos de Software', epigrafes=['Presupuestación y control de costes en el ciclo de vida del software', 'Métodos ROI, TIR y análisis coste-beneficio para inversiones TI', 'Gestión de riesgos económicos y sostenibilidad de proyectos tecnológicos'])])])\n", "2025-07-10 12:17:47,541 - logger - INFO - Asignatura is: nombre='Economia - Grado en Ingenieria Informatica' estructura=Estructura(bloques_tematicos=[BloqueTematico(nombre='Fundamentos Económicos para la Ingeniería Informática', temas=[Tema(nombre='Principios Microeconómicos aplicados al Desarrollo de Software', epigrafes=['Demanda, oferta y elasticidad de productos de software', 'Costes de producción y economías de escala en servicios digitales', 'Estructuras de mercado y competencia en el sector TIC']), Tema(nombre='Macroeconomía y Políticas Públicas en el Sector TIC', epigrafes=['Indicadores macroeconómicos y ciclos tecnológicos', 'Políticas públicas, regulación y fiscalidad en la economía digital', 'Impacto de la transformación digital en el crecimiento económico y el empleo'])]), BloqueTematico(nombre='Gestión Económica de Proyectos de Ingeniería Informática', temas=[Tema(nombre='Evaluación Financiera y Gestión de Riesgos en Proyectos de Software', epigrafes=['Presupuestación y control de costes en el ciclo de vida del software', 'Métodos ROI, TIR y análisis coste-beneficio para inversiones TI', 'Gestión de riesgos económicos y sostenibilidad de proyectos tecnológicos'])])])\n", "2025-07-10 12:17:47,544 - logger - INFO - Competencies are: competencias=[Competencia(descripcion='Capacidad para analizar costes y beneficios de proyectos de software aplicando técnicas de evaluación económica.'), Competencia(descripcion='Conocimiento de los principios microeconómicos y macroeconómicos relevantes para el sector de las tecnologías de la información.'), Competencia(descripcion='Habilidad para elaborar, gestionar y controlar presupuestos en proyectos de ingeniería informática.'), Competencia(descripcion='Dominio de métodos de cálculo de retorno de la inversión (ROI) y Tasa Interna de Retorno (TIR) para inversiones tecnológicas.'), Competencia(descripcion='Comprensión del impacto económico de la transformación digital y de los modelos de negocio basados en software.'), Competencia(descripcion='Capacidad para integrar criterios de sostenibilidad económica y eficiencia de recursos en el ciclo de vida de productos informáticos.'), Competencia(descripcion='Habilidad para interpretar información financiera básica y estados contables de empresas tecnológicas para apoyar la toma de decisiones.'), Competencia(descripcion='Conocimiento de políticas públicas y regulaciones económicas que afectan al sector TIC y su influencia en la estrategia empresarial.'), Competencia(descripcion='Capacidad para aplicar técnicas de gestión de riesgos económicos y de mercado en proyectos de ingeniería informática.'), Competencia(descripcion='Aptitud para comunicar análisis económicos y recomendaciones de negocio a equipos multidisciplinares utilizando herramientas cuantitativas.')]\n"]}, {"data": {"text/markdown": ["# Economia - Grado en Ingenieria Informatica\n", "\n", "## Competencias\n", "\n", "1. Capacidad para analizar costes y beneficios de proyectos de software aplicando técnicas de evaluación económica.\n", "2. Conocimiento de los principios microeconómicos y macroeconómicos relevantes para el sector de las tecnologías de la información.\n", "3. Habilidad para elaborar, gestionar y controlar presupuestos en proyectos de ingeniería informática.\n", "4. Dominio de métodos de cálculo de retorno de la inversión (ROI) y Tasa Interna de Retorno (TIR) para inversiones tecnológicas.\n", "5. Comprensión del impacto económico de la transformación digital y de los modelos de negocio basados en software.\n", "6. Capacidad para integrar criterios de sostenibilidad económica y eficiencia de recursos en el ciclo de vida de productos informáticos.\n", "7. Habilidad para interpretar información financiera básica y estados contables de empresas tecnológicas para apoyar la toma de decisiones.\n", "8. Conocimiento de políticas públicas y regulaciones económicas que afectan al sector TIC y su influencia en la estrategia empresarial.\n", "9. Capacidad para aplicar técnicas de gestión de riesgos económicos y de mercado en proyectos de ingeniería informática.\n", "10. Aptitud para comunicar análisis económicos y recomendaciones de negocio a equipos multidisciplinares utilizando herramientas cuantitativas.\n", "\n", "## Estructura Temática\n", "\n", "### Bloque 1: Fundamentos Económicos para la Ingeniería Informática\n", "\n", "#### 1.1. Principios Microeconómicos aplicados al Desarrollo de Software\n", "\n", "##### 1.1.1 <PERSON><PERSON><PERSON>, oferta y elasticidad de productos de software\n", "##### 1.1.2 Costes de producción y economías de escala en servicios digitales\n", "##### 1.1.3 Estructuras de mercado y competencia en el sector TIC\n", "\n", "#### 1.2. Macroeconomía y Políticas Públicas en el Sector TIC\n", "\n", "##### 1.2.1 Indicadores macroeconómicos y ciclos tecnológicos\n", "##### 1.2.2 Políticas públicas, regulación y fiscalidad en la economía digital\n", "##### 1.2.3 Impacto de la transformación digital en el crecimiento económico y el empleo\n", "\n", "### Bloque 2: Gestión Económica de Proyectos de Ingeniería Informática\n", "\n", "#### 2.1. Evaluación Financiera y Gestión de Riesgos en Proyectos de Software\n", "\n", "##### 2.1.1 Presupuestación y control de costes en el ciclo de vida del software\n", "##### 2.1.2 Métodos ROI, TIR y análisis coste-beneficio para inversiones TI\n", "##### 2.1.3 Gestión de riesgos económicos y sostenibilidad de proyectos tecnológicos\n"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["INDEX_ID = 1\n", "ORDER_ID = 1\n", "from src.api.workflows.indexes import GetIndexRequest\n", "request = GetIndexRequest(id = INDEX_ID)\n", "index_response = await DependencyContainer.get_index_workflow().execute(request)\n", "display_index_markdown(index_response)"]}, {"cell_type": "markdown", "id": "4380127d", "metadata": {}, "source": ["### Getting instructions and context"]}, {"cell_type": "markdown", "id": "ea493205", "metadata": {}, "source": ["We get info from instructions and topics so we can build the context for the model."]}, {"cell_type": "code", "execution_count": 4, "id": "945d107f", "metadata": {}, "outputs": [], "source": ["CONTENT_PLAN_VERSION = 1"]}, {"cell_type": "code", "execution_count": 5, "id": "6e6573d0", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-10 12:18:29,415 - logger - INFO - Entered in async session\n", "2025-07-10 12:18:29,421 - logger - INFO - Indice inside get subject index is, order_id=1 version=1 created_by='ia_gen_user' created_at=datetime.datetime(2025, 7, 10, 9, 17, 56, 70556) id=1 status=<IndiceStatus.CONTENT_GENERATION: 'CONTENT_GENERATION'> is_displayed=False updated_by='ia_gen_user' updated_at=datetime.datetime(2025, 7, 10, 9, 24, 39, 364154)\n", "2025-07-10 12:18:29,424 - logger - INFO - <PERSON><PERSON><PERSON> inside get subject index is, [<PERSON><PERSON><PERSON>(id=1, position=1, created_by='ia_gen_user', updated_at=datetime.datetime(2025, 7, 10, 9, 18, 13, 441158), name='Fundamentos Económicos para la Ingeniería Informática', indice_id=1, updated_by=None, created_at=datetime.datetime(2025, 7, 10, 9, 18, 13, 440974)), <PERSON><PERSON><PERSON>(id=2, position=2, created_by='ia_gen_user', updated_at=datetime.datetime(2025, 7, 10, 9, 18, 13, 459020), name='Gestión Económica de Proyectos de Ingeniería Informática', indice_id=1, updated_by=None, created_at=datetime.datetime(2025, 7, 10, 9, 18, 13, 458887))]\n", "2025-07-10 12:18:29,425 - logger - INFO - Asignatura is, nombre='Economia - Grado en Ingenieria Informatica' estructura=Estructura(bloques_tematicos=[BloqueTematico(nombre='Fundamentos Económicos para la Ingeniería Informática', temas=[Tema(nombre='Principios Microeconómicos aplicados al Desarrollo de Software', epigrafes=['Demanda, oferta y elasticidad de productos de software', 'Costes de producción y economías de escala en servicios digitales', 'Estructuras de mercado y competencia en el sector TIC']), Tema(nombre='Macroeconomía y Políticas Públicas en el Sector TIC', epigrafes=['Indicadores macroeconómicos y ciclos tecnológicos', 'Políticas públicas, regulación y fiscalidad en la economía digital', 'Impacto de la transformación digital en el crecimiento económico y el empleo'])]), BloqueTematico(nombre='Gestión Económica de Proyectos de Ingeniería Informática', temas=[Tema(nombre='Evaluación Financiera y Gestión de Riesgos en Proyectos de Software', epigrafes=['Presupuestación y control de costes en el ciclo de vida del software', 'Métodos ROI, TIR y análisis coste-beneficio para inversiones TI', 'Gestión de riesgos económicos y sostenibilidad de proyectos tecnológicos'])])])\n"]}], "source": ["ti_result, ti_list = await index_repository.get_instructions_per_topic(INDEX_ID)\n", "subject_index = await index_repository.get_subject_index(INDEX_ID)"]}, {"cell_type": "markdown", "id": "0b6cfcd1", "metadata": {}, "source": ["### Creating memories of the instructions"]}, {"cell_type": "code", "execution_count": 6, "id": "4056f89c", "metadata": {}, "outputs": [], "source": ["from openai.types.responses import ResponseFunctionToolCall\n", "import json\n", "from typing import Iterable"]}, {"cell_type": "code", "execution_count": 7, "id": "febe25d4", "metadata": {}, "outputs": [], "source": ["async def create_memories_for_topics(topics_info: list) -> dict[int, list]:\n", "    \"\"\"\n", "    Devuelve un dict donde la clave es el índice del tema (posicion - 1) y el valor\n", "    son las memorias de TODOS los temas previos a ese índice.\n", "    \"\"\"\n", "    accumulated: dict[int, list] = {}\n", "    previous: set = set()\n", "\n", "    for i, tema in enumerate(topics_info):\n", "        accumulated[i] = list(previous)\n", "\n", "        response = await client.responses.create(\n", "            reasoning={},\n", "            prompt={\n", "                \"id\": \"pmpt_685a5f9e4d408196bac06582445813390c97560367a99503\",\n", "                \"version\": \"12\",\n", "                \"variables\": {\n", "                    \"tema\": tema,\n", "                    \"memorias\": str(previous),\n", "                    \"tema_id\": str(i),\n", "                },\n", "            },\n", "        )\n", "\n", "        new_memories: Iterable = []\n", "        for r in response.output:\n", "            if isinstance(r, ResponseFunctionToolCall):\n", "                new_memories = json.loads(r.arguments).get(\"memories\", [])\n", "                break\n", "\n", "        previous.update(new_memories)\n", "\n", "    return accumulated"]}, {"cell_type": "markdown", "id": "cce7a811", "metadata": {}, "source": ["Quick eval\n", "\n", "#### o3 (version 11) reasoning effort medio\n", "* **Time**: 2m 54s\n", "* **All memories**: \n", "```\n", "{'Cada epígrafe debe ajustarse a rangos de extensión específicos (≈800-1300 palabras)',\n", " 'Diagramas de flujo en Mermaid simples y lineales, con menos de 10 nodos',\n", " 'Los ejemplos de código se escribirán en JavaScript moderno usando clases ES6',\n", " 'Los rangos de extensión concretos por epígrafe prevalecen sobre el rango genérico, pudiendo llegar hasta 1 400 palabras',\n", " 'Pseudocódigo con palabras reservadas en mayúsculas, sangrado consistente y comentarios con //'}\n", " ```\n", "* **Quality**:\n", "\n", "#### gpt 4.1 (version 12)\n", "* **Time**: 1m 25s\n", "* **All memories**:\n", "```\n", "{'Comandos estándar de comprobación de entorno: node -v, npm -v',\n", " 'Convención para pseudocódigo: palabras reservadas en mayúsculas, sangrías y comentarios con //',\n", " 'Est<PERSON><PERSON> m<PERSON> ES6+ (let/const, arrow functions, template literals, etc.)',\n", " 'JavaScript será el lenguaje base a lo largo de la asignatura',\n", " 'Los ejemplos y ejercicios deberán incorporar pseudocódigo y diagramas mermaid cuando corresponda',\n", " 'Node.js se adopta como entorno de ejecución principal ´uera del navegador',\n", " 'Se recomienda configurar el editor con ESLint, <PERSON><PERSON><PERSON> y JavaScript ES6 snippets como estándar mínimo de extensiones',\n", " 'VS Code se establece como editor de referencia para los ejercicios`}\n", " ```\n", "* **Quality**: \n", "\n", "#### gpt 4.1-mini (version 13)\n", "* **Time**: 57s\n", "* **All memories**: I dont include it, there were so many\n", "* **Quality**: bad, creates too much fields\n", "\n", "#### gpt o4-mini (version 14)\n", "* **Time**: 1m 47s\n", "* **All memories**:\n", "```\n", "{'Diagramas Mermaid deben contener menos de diez nodos',\n", " 'Ejemplos de código deben incluir comentarios en línea que vinculen la lógica con la teoría explicada',\n", " 'Extensión esperada por epígrafe: Recursión 1 000–1 200 palabras; Búsqueda 900–1 100; Algoritmos sencillos 1 100–1 300; Divide-and-conquer 1 200–1 400',\n", " 'Reglas sintácticas de pseudocódigo: palabras reservadas en mayúsculas, sangrías y comentarios con //',\n", " 'Uso de camelCase para identificadores, preferir const sobre let, evitar var salvo motivos concretos',\n", " 'Uso de operador de igualdad estricta (===) para comparaciones de igualdad'}\n", "```\n", "* **Quality**: \n", "\n", "\n", "Equilibrio calidad latencia gpt-4.1 parece muy buena opcion, si se observaran memorias innecesarias se podría ir a o3"]}, {"cell_type": "code", "execution_count": null, "id": "b7d039b3", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n"]}], "source": ["acc_mem_topics = await create_memories_for_topics(ti_list)"]}, {"cell_type": "code", "execution_count": null, "id": "2370cba7", "metadata": {}, "outputs": [{"data": {"text/plain": ["{0: [],\n", " 1: ['El pseudocódigo utilizará mayúsculas para palabras reservadas, sangrías y comentarios con // como estándar de sintaxis en toda la asignatura.',\n", "  'Se requerirá la instalación de extensiones mínimas en VS Code: ESLint, Prettier y JavaScript ES6 snippets, documentando siempre su propósito y ruta de instalación en tablas cuando aplique.',\n", "  'JavaScript será el lenguaje de programación principal y Node.js el entorno de ejecución fuera del navegador durante la asignatura.',\n", "  'El ejercicio de ejecución y prueba de scripts se realizará siempre en consola mediante node, y en navegador, cuando proceda, usando DevTools para visualizar la consola.',\n", "  'Todos los diagramas de flujo solicitados serán en formato mermaid y deberán mantenerse simples (menos de diez nodos para los ejemplos integrales, lineales y esquemáticos cuando se indique).',\n", "  'VS Code será el editor recomendado y estándar para ejercicios y desarrollo en local a lo largo del curso.'],\n", " 2: ['El pseudocódigo utilizará mayúsculas para palabras reservadas, sangrías y comentarios con // como estándar de sintaxis en toda la asignatura.',\n", "  'Se requerirá la instalación de extensiones mínimas en VS Code: ESLint, Prettier y JavaScript ES6 snippets, documentando siempre su propósito y ruta de instalación en tablas cuando aplique.',\n", "  'JavaScript será el lenguaje de programación principal y Node.js el entorno de ejecución fuera del navegador durante la asignatura.',\n", "  'El ejercicio de ejecución y prueba de scripts se realizará siempre en consola mediante node, y en navegador, cuando proceda, usando DevTools para visualizar la consola.',\n", "  'Todos los diagramas de flujo solicitados serán en formato mermaid y deberán mantenerse simples (menos de diez nodos para los ejemplos integrales, lineales y esquemáticos cuando se indique).',\n", "  'VS Code será el editor recomendado y estándar para ejercicios y desarrollo en local a lo largo del curso.'],\n", " 3: ['El pseudocódigo utilizará mayúsculas para palabras reservadas, sangrías y comentarios con // como estándar de sintaxis en toda la asignatura.',\n", "  'Se requerirá la instalación de extensiones mínimas en VS Code: ESLint, Prettier y JavaScript ES6 snippets, documentando siempre su propósito y ruta de instalación en tablas cuando aplique.',\n", "  'JavaScript será el lenguaje de programación principal y Node.js el entorno de ejecución fuera del navegador durante la asignatura.',\n", "  'El ejercicio de ejecución y prueba de scripts se realizará siempre en consola mediante node, y en navegador, cuando proceda, usando DevTools para visualizar la consola.',\n", "  'Todos los diagramas de flujo solicitados serán en formato mermaid y deberán mantenerse simples (menos de diez nodos para los ejemplos integrales, lineales y esquemáticos cuando se indique).',\n", "  'VS Code será el editor recomendado y estándar para ejercicios y desarrollo en local a lo largo del curso.'],\n", " 4: ['El pseudocódigo utilizará mayúsculas para palabras reservadas, sangrías y comentarios con // como estándar de sintaxis en toda la asignatura.',\n", "  'Se requerirá la instalación de extensiones mínimas en VS Code: ESLint, Prettier y JavaScript ES6 snippets, documentando siempre su propósito y ruta de instalación en tablas cuando aplique.',\n", "  'JavaScript será el lenguaje de programación principal y Node.js el entorno de ejecución fuera del navegador durante la asignatura.',\n", "  'El ejercicio de ejecución y prueba de scripts se realizará siempre en consola mediante node, y en navegador, cuando proceda, usando DevTools para visualizar la consola.',\n", "  'Todos los diagramas de flujo solicitados serán en formato mermaid y deberán mantenerse simples (menos de diez nodos para los ejemplos integrales, lineales y esquemáticos cuando se indique).',\n", "  'VS Code será el editor recomendado y estándar para ejercicios y desarrollo en local a lo largo del curso.'],\n", " 5: ['El pseudocódigo utilizará mayúsculas para palabras reservadas, sangrías y comentarios con // como estándar de sintaxis en toda la asignatura.',\n", "  'Se requerirá la instalación de extensiones mínimas en VS Code: ESLint, Prettier y JavaScript ES6 snippets, documentando siempre su propósito y ruta de instalación en tablas cuando aplique.',\n", "  'JavaScript será el lenguaje de programación principal y Node.js el entorno de ejecución fuera del navegador durante la asignatura.',\n", "  'El ejercicio de ejecución y prueba de scripts se realizará siempre en consola mediante node, y en navegador, cuando proceda, usando DevTools para visualizar la consola.',\n", "  'Todos los diagramas de flujo solicitados serán en formato mermaid y deberán mantenerse simples (menos de diez nodos para los ejemplos integrales, lineales y esquemáticos cuando se indique).',\n", "  'VS Code será el editor recomendado y estándar para ejercicios y desarrollo en local a lo largo del curso.'],\n", " 6: ['El pseudocódigo utilizará mayúsculas para palabras reservadas, sangrías y comentarios con // como estándar de sintaxis en toda la asignatura.',\n", "  'Se requerirá la instalación de extensiones mínimas en VS Code: ESLint, Prettier y JavaScript ES6 snippets, documentando siempre su propósito y ruta de instalación en tablas cuando aplique.',\n", "  'JavaScript será el lenguaje de programación principal y Node.js el entorno de ejecución fuera del navegador durante la asignatura.',\n", "  'El ejercicio de ejecución y prueba de scripts se realizará siempre en consola mediante node, y en navegador, cuando proceda, usando DevTools para visualizar la consola.',\n", "  'Todos los diagramas de flujo solicitados serán en formato mermaid y deberán mantenerse simples (menos de diez nodos para los ejemplos integrales, lineales y esquemáticos cuando se indique).',\n", "  'VS Code será el editor recomendado y estándar para ejercicios y desarrollo en local a lo largo del curso.'],\n", " 7: ['El pseudocódigo utilizará mayúsculas para palabras reservadas, sangrías y comentarios con // como estándar de sintaxis en toda la asignatura.',\n", "  'Se requerirá la instalación de extensiones mínimas en VS Code: ESLint, Prettier y JavaScript ES6 snippets, documentando siempre su propósito y ruta de instalación en tablas cuando aplique.',\n", "  'JavaScript será el lenguaje de programación principal y Node.js el entorno de ejecución fuera del navegador durante la asignatura.',\n", "  'El ejercicio de ejecución y prueba de scripts se realizará siempre en consola mediante node, y en navegador, cuando proceda, usando DevTools para visualizar la consola.',\n", "  'Todos los diagramas de flujo solicitados serán en formato mermaid y deberán mantenerse simples (menos de diez nodos para los ejemplos integrales, lineales y esquemáticos cuando se indique).',\n", "  'VS Code será el editor recomendado y estándar para ejercicios y desarrollo en local a lo largo del curso.'],\n", " 8: ['El pseudocódigo utilizará mayúsculas para palabras reservadas, sangrías y comentarios con // como estándar de sintaxis en toda la asignatura.',\n", "  'Se requerirá la instalación de extensiones mínimas en VS Code: ESLint, Prettier y JavaScript ES6 snippets, documentando siempre su propósito y ruta de instalación en tablas cuando aplique.',\n", "  'JavaScript será el lenguaje de programación principal y Node.js el entorno de ejecución fuera del navegador durante la asignatura.',\n", "  'El ejercicio de ejecución y prueba de scripts se realizará siempre en consola mediante node, y en navegador, cuando proceda, usando DevTools para visualizar la consola.',\n", "  'Todos los diagramas de flujo solicitados serán en formato mermaid y deberán mantenerse simples (menos de diez nodos para los ejemplos integrales, lineales y esquemáticos cuando se indique).',\n", "  'VS Code será el editor recomendado y estándar para ejercicios y desarrollo en local a lo largo del curso.'],\n", " 9: ['El pseudocódigo utilizará mayúsculas para palabras reservadas, sangrías y comentarios con // como estándar de sintaxis en toda la asignatura.',\n", "  'Se requerirá la instalación de extensiones mínimas en VS Code: ESLint, Prettier y JavaScript ES6 snippets, documentando siempre su propósito y ruta de instalación en tablas cuando aplique.',\n", "  'JavaScript será el lenguaje de programación principal y Node.js el entorno de ejecución fuera del navegador durante la asignatura.',\n", "  'El ejercicio de ejecución y prueba de scripts se realizará siempre en consola mediante node, y en navegador, cuando proceda, usando DevTools para visualizar la consola.',\n", "  'Todos los diagramas de flujo solicitados serán en formato mermaid y deberán mantenerse simples (menos de diez nodos para los ejemplos integrales, lineales y esquemáticos cuando se indique).',\n", "  'VS Code será el editor recomendado y estándar para ejercicios y desarrollo en local a lo largo del curso.']}"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["acc_mem_topics"]}, {"cell_type": "markdown", "id": "3e62a78b", "metadata": {}, "source": ["Then we would store this by topic in the db and by version, and we will override the version 1 each time for now. We skip for now, but with that memories we already have all the context for generating each topic."]}, {"cell_type": "markdown", "id": "e6fe5196", "metadata": {}, "source": ["### Searching sources with Search Agent\n", "\n", "Note: Would be good if from the search_references we could specify topic, model to use and so on.\n", "\n", "We have searched sources for index_id 48. But put an invalid gen ai api so it doesn´t trigger the current content generation. We will use the sources from there."]}, {"cell_type": "markdown", "id": "13ab9978", "metadata": {}, "source": ["#### Analyzing search Agent urls per topic."]}, {"cell_type": "markdown", "id": "ea88f81d", "metadata": {}, "source": ["Create functions to analyze this, cluster and create statistics on what was found\n", "\n", "Identified from searches for now: it founds a lot of paywalled pages."]}, {"cell_type": "code", "execution_count": 8, "id": "8e07bf01", "metadata": {}, "outputs": [], "source": ["from src.domain.models import Doc, EpigrafeDocumento, Tema, Epigrafe\n", "from sqlmodel import select\n", "from src.api.common.dependencies.get_session import get_session\n", "\n", "\n", "\n", "def get_docs_per_topic(topic_id: int, session = None):\n", "       if not session:\n", "              session = next(get_session())\n", "       stm = (select(Doc.name,\n", "                     Doc.summary,\n", "                     Doc.document_url,\n", "                     Doc.token_count)\n", "              .distinct()\n", "              .join(EpigrafeDocumento, Doc.id == EpigrafeDocumento.id_documento)\n", "              .join(Epigrafe, EpigrafeDocumento.id_epigrafe == Epigrafe.id)\n", "              .join(Te<PERSON>, Epigrafe.id_tema == Tema.id)\n", "              .where(Tema.id == topic_id))\n", "       result = session.exec(stm).all()\n", "       return result\n"]}, {"cell_type": "markdown", "id": "34ffc6d5", "metadata": {}, "source": ["#### Document Analysis and Clustering for Index "]}, {"cell_type": "code", "execution_count": 12, "id": "f73c9b7a", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[2mResolved \u001b[1m229 packages\u001b[0m \u001b[2min 3.36s\u001b[0m\u001b[0m\n", "\u001b[1m\u001b[33mwarning\u001b[39m\u001b[0m\u001b[1m:\u001b[0m \u001b[1m`grpcio==1.72.0` is yanked (reason: \"Missing cp312 macosx universal2 wheel\")\u001b[0m\n", "\u001b[1m\u001b[33mwarning\u001b[39m\u001b[0m\u001b[1m:\u001b[0m \u001b[1m`thinc==8.3.5` is yanked (reason: \"Incorrect blis dependency\")\u001b[0m\n", "\u001b[1m\u001b[33mwarning\u001b[39m\u001b[0m\u001b[1m:\u001b[0m \u001b[1m`spacy==3.8.5` is yanked (reason: \"Incorrect python3.13 support specified\")\u001b[0m\n", "\u001b[1m\u001b[33mwarning\u001b[39m\u001b[0m\u001b[1m:\u001b[0m \u001b[1m`grpcio==1.72.0` is yanked (reason: \"Missing cp312 macosx universal2 wheel\")\u001b[0m\n", "\u001b[1m\u001b[33mwarning\u001b[39m\u001b[0m\u001b[1m:\u001b[0m \u001b[1m`thinc==8.3.5` is yanked (reason: \"Incorrect blis dependency\")\u001b[0m\n", "\u001b[1m\u001b[33mwarning\u001b[39m\u001b[0m\u001b[1m:\u001b[0m \u001b[1m`spacy==3.8.5` is yanked (reason: \"Incorrect python3.13 support specified\")\u001b[0m\n", "\u001b[36m\u001b[1mDownloading\u001b[0m\u001b[39m pydantic-core \u001b[2m(1.9MiB)\u001b[0m\n", "\u001b[36m\u001b[1mDownloading\u001b[0m\u001b[39m scipy \u001b[2m(36.7MiB)\u001b[0m\n", "\u001b[36m\u001b[1mDownloading\u001b[0m\u001b[39m langchain-community \u001b[2m(2.4MiB)\u001b[0m\n", "\u001b[36m\u001b[1mDownloading\u001b[0m\u001b[39m scikit-learn \u001b[2m(10.2MiB)\u001b[0m\n", "\u001b[36m\u001b[1mDownloading\u001b[0m\u001b[39m fonttools \u001b[2m(2.1MiB)\u001b[0m\n", "\u001b[36m\u001b[1mDownloading\u001b[0m\u001b[39m numpy \u001b[2m(12.1MiB)\u001b[0m\n", "\u001b[36m\u001b[1mDownloading\u001b[0m\u001b[39m pandas \u001b[2m(10.5MiB)\u001b[0m\n", "\u001b[36m\u001b[1mDownloading\u001b[0m\u001b[39m tokenizers \u001b[2m(2.3MiB)\u001b[0m\n", "\u001b[36m\u001b[1mDownloading\u001b[0m\u001b[39m pygments \u001b[2m(1.2MiB)\u001b[0m\n", "\u001b[36m\u001b[1mDownloading\u001b[0m\u001b[39m cryptography \u001b[2m(3.1MiB)\u001b[0m\n", "\u001b[36m\u001b[1mDownloading\u001b[0m\u001b[39m matplotlib \u001b[2m(7.7MiB)\u001b[0m\n", "\u001b[36m\u001b[1mDownloading\u001b[0m\u001b[39m ruff \u001b[2m(10.9MiB)\u001b[0m\n", "\u001b[36m\u001b[1mDownloading\u001b[0m\u001b[39m grpcio \u001b[2m(4.1MiB)\u001b[0m\n", "\u001b[36m\u001b[1mDownloading\u001b[0m\u001b[39m mypy \u001b[2m(8.9MiB)\u001b[0m\n", " \u001b[32m\u001b[1mDownloading\u001b[0m\u001b[39m pydantic-core\n", " \u001b[32m\u001b[1mDownloading\u001b[0m\u001b[39m pygments\n", " \u001b[32m\u001b[1mDownloading\u001b[0m\u001b[39m tokenizers\n", " \u001b[32m\u001b[1mDownloading\u001b[0m\u001b[39m fonttools\n", " \u001b[32m\u001b[1mDownloading\u001b[0m\u001b[39m cryptography\n", " \u001b[32m\u001b[1mDownloading\u001b[0m\u001b[39m grpcio\n", " \u001b[32m\u001b[1mDownloading\u001b[0m\u001b[39m matplotlib\n", " \u001b[32m\u001b[1mDownloading\u001b[0m\u001b[39m langchain-community\n", " \u001b[32m\u001b[1mDownloading\u001b[0m\u001b[39m ruff\n", " \u001b[32m\u001b[1mDownloading\u001b[0m\u001b[39m numpy\n", " \u001b[32m\u001b[1mDownloading\u001b[0m\u001b[39m scikit-learn\n", " \u001b[32m\u001b[1mDownloading\u001b[0m\u001b[39m pandas\n", " \u001b[32m\u001b[1mDownloading\u001b[0m\u001b[39m mypy\n", " \u001b[32m\u001b[1mDownloading\u001b[0m\u001b[39m scipy\n", "\u001b[2mPrepared \u001b[1m108 packages\u001b[0m \u001b[2min 13.72s\u001b[0m\u001b[0m\n", "\u001b[2mUninstalled \u001b[1m94 packages\u001b[0m \u001b[2min 4.61s\u001b[0m\u001b[0m\n", "\u001b[2mInstalled \u001b[1m108 packages\u001b[0m \u001b[2min 3.37s\u001b[0m\u001b[0m\n", "\u001b[2mBytecode compiled \u001b[1m16227 files\u001b[0m \u001b[2min 12.10s\u001b[0m\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1maiohttp\u001b[0m\u001b[2m==3.12.13\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1maiohttp\u001b[0m\u001b[2m==3.11.18\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1manthropic\u001b[0m\u001b[2m==0.56.0\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1manthropic\u001b[0m\u001b[2m==0.49.0\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1masgiref\u001b[0m\u001b[2m==3.9.0\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1masgiref\u001b[0m\u001b[2m==3.8.1\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mazure-core\u001b[0m\u001b[2m==1.35.0\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mazure-core\u001b[0m\u001b[2m==1.33.0\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mazure-identity\u001b[0m\u001b[2m==1.23.0\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mazure-identity\u001b[0m\u001b[2m==1.21.0\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mazure-keyvault-secrets\u001b[0m\u001b[2m==4.10.0\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mazure-keyvault-secrets\u001b[0m\u001b[2m==4.9.0\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mazure-monitor-opentelemetry\u001b[0m\u001b[2m==1.6.10\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mazure-monitor-opentelemetry\u001b[0m\u001b[2m==1.6.7\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mazure-monitor-opentelemetry-exporter\u001b[0m\u001b[2m==1.0.0b39\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mazure-monitor-opentelemetry-exporter\u001b[0m\u001b[2m==1.0.0b36\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mcertifi\u001b[0m\u001b[2m==2025.6.15\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mcertifi\u001b[0m\u001b[2m==2025.1.31\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mcharset-normalizer\u001b[0m\u001b[2m==3.4.2\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mcharset-normalizer\u001b[0m\u001b[2m==3.4.1\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mclick\u001b[0m\u001b[2m==8.2.1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mclick\u001b[0m\u001b[2m==8.1.8\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mcohere\u001b[0m\u001b[2m==5.15.0\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mcohere\u001b[0m\u001b[2m==5.14.2\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mcontourpy\u001b[0m\u001b[2m==1.3.2\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mcoverage\u001b[0m\u001b[2m==7.9.2\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mcoverage\u001b[0m\u001b[2m==7.8.0\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mcryptography\u001b[0m\u001b[2m==45.0.5\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mcryptography\u001b[0m\u001b[2m==44.0.3\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mcycler\u001b[0m\u001b[2m==0.12.1\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1m<PERSON><PERSON><PERSON>\u001b[0m\u001b[2m==1.11.1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1m<PERSON><PERSON><PERSON>\u001b[0m\u001b[2m==1.10.0\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mfonttools\u001b[0m\u001b[2m==4.58.5\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mf<PERSON>zenlist\u001b[0m\u001b[2m==1.7.0\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mfrozenlist\u001b[0m\u001b[2m==1.6.0\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mfsspec\u001b[0m\u001b[2m==2025.5.1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mfsspec\u001b[0m\u001b[2m==2025.3.2\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1m<PERSON>let\u001b[0m\u001b[2m==3.2.3\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mgreenlet\u001b[0m\u001b[2m==3.2.2\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mgrpcio\u001b[0m\u001b[2m==1.73.1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mgrpcio\u001b[0m\u001b[2m==1.72.0\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mh11\u001b[0m\u001b[2m==0.16.0\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mh11\u001b[0m\u001b[2m==0.14.0\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mhttpcore\u001b[0m\u001b[2m==1.0.9\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mhttpcore\u001b[0m\u001b[2m==1.0.8\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mhuggingface-hub\u001b[0m\u001b[2m==0.33.2\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mhuggingface-hub\u001b[0m\u001b[2m==0.30.2\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mipython\u001b[0m\u001b[2m==9.4.0\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mipython\u001b[0m\u001b[2m==9.2.0\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mjiter\u001b[0m\u001b[2m==0.10.0\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mjiter\u001b[0m\u001b[2m==0.9.0\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mjoblib\u001b[0m\u001b[2m==1.5.1\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mjupyter-core\u001b[0m\u001b[2m==5.8.1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mjupyter-core\u001b[0m\u001b[2m==5.7.2\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mkiwi<PERSON>ver\u001b[0m\u001b[2m==1.4.8\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mlangchain\u001b[0m\u001b[2m==0.3.26\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mlangchain\u001b[0m\u001b[2m==0.3.24\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mlangchain-anthropic\u001b[0m\u001b[2m==0.3.16\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mlangchain-anthropic\u001b[0m\u001b[2m==0.3.11\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mlangchain-cohere\u001b[0m\u001b[2m==0.4.4\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mlangchain-cohere\u001b[0m\u001b[2m==0.4.3\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mlangchain-community\u001b[0m\u001b[2m==0.3.27\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mlangchain-community\u001b[0m\u001b[2m==0.3.20\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mlangchain-core\u001b[0m\u001b[2m==0.3.67\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mlangchain-core\u001b[0m\u001b[2m==0.3.56\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mlangchain-openai\u001b[0m\u001b[2m==0.3.27\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mlangchain-openai\u001b[0m\u001b[2m==0.3.14\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mlangsmith\u001b[0m\u001b[2m==0.4.4\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mlangsmith\u001b[0m\u001b[2m==0.3.31\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mmarkdown\u001b[0m\u001b[2m==3.8.2\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mmarkdown\u001b[0m\u001b[2m==3.8\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mmatplotlib\u001b[0m\u001b[2m==3.10.3\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mmcp\u001b[0m\u001b[2m==1.10.1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mmcp\u001b[0m\u001b[2m==1.8.1\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mmsal\u001b[0m\u001b[2m==1.32.3\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mmsal\u001b[0m\u001b[2m==1.32.0\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mmultidict\u001b[0m\u001b[2m==6.6.3\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mmultidict\u001b[0m\u001b[2m==6.4.3\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mmypy\u001b[0m\u001b[2m==1.16.1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mmypy\u001b[0m\u001b[2m==1.15.0\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mnumpy\u001b[0m\u001b[2m==2.3.1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mnumpy\u001b[0m\u001b[2m==2.2.5\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1<PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[2m==3.3.1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mo<PERSON><PERSON><PERSON>\u001b[0m\u001b[2m==3.2.2\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mo<PERSON>ai\u001b[0m\u001b[2m==1.93.0\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mo<PERSON>ai\u001b[0m\u001b[2m==1.77.0\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mopenai-agents\u001b[0m\u001b[2m==0.1.0\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mopenai-agents\u001b[0m\u001b[2m==0.0.14\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mopeninference-instrumentation\u001b[0m\u001b[2m==0.1.34\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mopeninference-instrumentation\u001b[0m\u001b[2m==0.1.27\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mopeninference-instrumentation-langchain\u001b[0m\u001b[2m==0.1.45\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mopeninference-instrumentation-langchain\u001b[0m\u001b[2m==0.1.41\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mopeninference-instrumentation-openai\u001b[0m\u001b[2m==0.1.30\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mopeninference-instrumentation-openai\u001b[0m\u001b[2m==0.1.24\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mopeninference-instrumentation-openai-agents\u001b[0m\u001b[2m==1.0.0\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mopeninference-instrumentation-openai-agents\u001b[0m\u001b[2m==0.1.14\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mopeninference-semantic-conventions\u001b[0m\u001b[2m==0.1.21\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mopeninference-semantic-conventions\u001b[0m\u001b[2m==0.1.17\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mopentelemetry-api\u001b[0m\u001b[2m==1.31.1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mopentelemetry-api\u001b[0m\u001b[2m==1.32.1\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mopentelemetry-exporter-otlp\u001b[0m\u001b[2m==1.31.1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mopentelemetry-exporter-otlp\u001b[0m\u001b[2m==1.32.1\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mopentelemetry-exporter-otlp-proto-common\u001b[0m\u001b[2m==1.31.1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mopentelemetry-exporter-otlp-proto-common\u001b[0m\u001b[2m==1.32.1\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mopentelemetry-exporter-otlp-proto-grpc\u001b[0m\u001b[2m==1.31.1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mopentelemetry-exporter-otlp-proto-grpc\u001b[0m\u001b[2m==1.32.1\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mopentelemetry-exporter-otlp-proto-http\u001b[0m\u001b[2m==1.31.1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mopentelemetry-exporter-otlp-proto-http\u001b[0m\u001b[2m==1.32.1\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mopentelemetry-instrumentation\u001b[0m\u001b[2m==0.52b1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mopentelemetry-instrumentation\u001b[0m\u001b[2m==0.53b1\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mopentelemetry-instrumentation-asgi\u001b[0m\u001b[2m==0.52b1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mopentelemetry-instrumentation-asgi\u001b[0m\u001b[2m==0.53b1\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mopentelemetry-instrumentation-dbapi\u001b[0m\u001b[2m==0.52b1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mopentelemetry-instrumentation-dbapi\u001b[0m\u001b[2m==0.53b1\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mopentelemetry-instrumentation-django\u001b[0m\u001b[2m==0.52b1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mopentelemetry-instrumentation-django\u001b[0m\u001b[2m==0.53b1\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mopentelemetry-instrumentation-fastapi\u001b[0m\u001b[2m==0.52b1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mopentelemetry-instrumentation-fastapi\u001b[0m\u001b[2m==0.53b1\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mopentelemetry-instrumentation-flask\u001b[0m\u001b[2m==0.52b1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mopentelemetry-instrumentation-flask\u001b[0m\u001b[2m==0.53b1\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mopentelemetry-instrumentation-psycopg2\u001b[0m\u001b[2m==0.52b1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mopentelemetry-instrumentation-psycopg2\u001b[0m\u001b[2m==0.53b1\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mopentelemetry-instrumentation-requests\u001b[0m\u001b[2m==0.52b1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mopentelemetry-instrumentation-requests\u001b[0m\u001b[2m==0.53b1\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mopentelemetry-instrumentation-urllib\u001b[0m\u001b[2m==0.52b1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mopentelemetry-instrumentation-urllib\u001b[0m\u001b[2m==0.53b1\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mopentelemetry-instrumentation-urllib3\u001b[0m\u001b[2m==0.52b1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mopentelemetry-instrumentation-urllib3\u001b[0m\u001b[2m==0.53b1\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mopentelemetry-instrumentation-wsgi\u001b[0m\u001b[2m==0.52b1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mopentelemetry-instrumentation-wsgi\u001b[0m\u001b[2m==0.53b1\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mopentelemetry-proto\u001b[0m\u001b[2m==1.31.1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mopentelemetry-proto\u001b[0m\u001b[2m==1.32.1\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mopentelemetry-sdk\u001b[0m\u001b[2m==1.31.1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mopentelemetry-sdk\u001b[0m\u001b[2m==1.32.1\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mopentelemetry-semantic-conventions\u001b[0m\u001b[2m==0.52b1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mopentelemetry-semantic-conventions\u001b[0m\u001b[2m==0.53b1\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mopentelemetry-util-http\u001b[0m\u001b[2m==0.52b1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mopentelemetry-util-http\u001b[0m\u001b[2m==0.53b1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mpandas\u001b[0m\u001b[2m==2.3.1\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mpluggy\u001b[0m\u001b[2m==1.6.0\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mpluggy\u001b[0m\u001b[2m==1.5.0\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[2m==0.36.0\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[2m==0.33.1\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mpropcache\u001b[0m\u001b[2m==0.3.2\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mpropcache\u001b[0m\u001b[2m==0.3.1\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mprotob<PERSON>\u001b[0m\u001b[2m==5.29.5\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mprotob<PERSON>\u001b[0m\u001b[2m==5.29.4\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mpsutil\u001b[0m\u001b[2m==7.0.0\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mpsutil\u001b[0m\u001b[2m==6.1.1\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mpydantic\u001b[0m\u001b[2m==2.11.7\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mpydantic\u001b[0m\u001b[2m==2.11.3\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mpydantic-core\u001b[0m\u001b[2m==2.33.2\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mpydantic-core\u001b[0m\u001b[2m==2.33.1\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mpydantic-settings\u001b[0m\u001b[2m==2.10.1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mpydantic-settings\u001b[0m\u001b[2m==2.9.1\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mpygments\u001b[0m\u001b[2m==2.19.2\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mpygments\u001b[0m\u001b[2m==2.19.1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mpyparsing\u001b[0m\u001b[2m==3.2.3\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mpytest\u001b[0m\u001b[2m==8.4.1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mpytest\u001b[0m\u001b[2m==8.3.5\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mpytest-asyncio\u001b[0m\u001b[2m==1.0.0\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mpytest-asyncio\u001b[0m\u001b[2m==0.26.0\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mpytest-cov\u001b[0m\u001b[2m==6.2.1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mpytest-cov\u001b[0m\u001b[2m==6.1.1\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mpython-dotenv\u001b[0m\u001b[2m==1.1.1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mpython-dotenv\u001b[0m\u001b[2m==1.1.0\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mpytz\u001b[0m\u001b[2m==2025.2\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mpyzmq\u001b[0m\u001b[2m==27.0.0\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mpyzmq\u001b[0m\u001b[2m==26.4.0\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mrequests\u001b[0m\u001b[2m==2.32.4\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mrequests\u001b[0m\u001b[2m==2.32.3\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mruff\u001b[0m\u001b[2m==0.12.1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mruff\u001b[0m\u001b[2m==0.11.3\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mscikit-learn\u001b[0m\u001b[2m==1.7.0\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mscipy\u001b[0m\u001b[2m==1.16.0\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1ms<PERSON>born\u001b[0m\u001b[2m==0.13.2\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1msse-starlette\u001b[0m\u001b[2m==2.3.6\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1msse-starlette\u001b[0m\u001b[2m==2.3.5\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mtavily-python\u001b[0m\u001b[2m==0.7.9\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mtavily-python\u001b[0m\u001b[2m==0.5.4\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mthreadpoolctl\u001b[0m\u001b[2m==3.6.0\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mtok<PERSON><PERSON>\u001b[0m\u001b[2m==0.21.2\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mtok<PERSON><PERSON>\u001b[0m\u001b[2m==0.21.1\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mtornado\u001b[0m\u001b[2m==6.5.1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mtornado\u001b[0m\u001b[2m==6.4.2\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mtypes-pyyaml\u001b[0m\u001b[2m==6.0.12.20250516\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mtypes-pyyaml\u001b[0m\u001b[2m==6.0.12.20250402\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mtypes-requests\u001b[0m\u001b[2m==2.32.4.20250611\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mtypes-requests\u001b[0m\u001b[2m==2.32.0.20250328\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mtyping-extensions\u001b[0m\u001b[2m==4.14.0\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mtyping-extensions\u001b[0m\u001b[2m==4.13.2\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mtyping-inspection\u001b[0m\u001b[2m==0.4.1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mtyping-inspection\u001b[0m\u001b[2m==0.4.0\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mtzdata\u001b[0m\u001b[2m==2025.2\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1murllib3\u001b[0m\u001b[2m==2.5.0\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1murllib3\u001b[0m\u001b[2m==2.4.0\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mu<PERSON>orn\u001b[0m\u001b[2m==0.35.0\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mu<PERSON>orn\u001b[0m\u001b[2m==0.34.2\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1myarl\u001b[0m\u001b[2m==1.20.1\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1myarl\u001b[0m\u001b[2m==1.20.0\u001b[0m\n", " \u001b[31m-\u001b[39m \u001b[1mzipp\u001b[0m\u001b[2m==3.23.0\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mzipp\u001b[0m\u001b[2m==3.21.0\u001b[0m\n"]}], "source": ["os.environ[\"UV_INDEX_UNIR_PASSWORD\"]=\"1H5fAE9MmkovSuEiaf6mLVZtceAnE3jUVWRCNSy0wNHUJ1iziwEuJQQJ99BFACAAAAAEP09zAAASAZDOj0sx\"\n", "!uv add --dev scikit-learn seaborn matplot<PERSON>b pandas"]}, {"cell_type": "code", "execution_count": 13, "id": "f2428fff", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "from sklearn.cluster import KMeans\n", "from sklearn.decomposition import PCA\n", "from sklearn.metrics import silhouette_score\n", "from collections import Counter\n", "from urllib.parse import urlparse\n", "from typing import List, Dict, Tuple, Any\n", "\n", "def analyze_documents_per_topic(topic_id: int, topic_name: str = None, session=None) -> Dict[str, Any]:\n", "    \"\"\"\n", "    Comprehensive document analysis for a specific topic.\n", "    \n", "    Args:\n", "        topic_id: ID of the topic to analyze\n", "        topic_name: Optional name of the topic for display\n", "        session: Database session (optional)\n", "    \n", "    Returns:\n", "        Dictionary with analysis results including clustering, statistics, and visualizations\n", "    \"\"\"\n", "    docs = get_docs_per_topic(topic_id, session)\n", "    \n", "    if not docs:\n", "        print(f\"No documents found for topic {topic_id}\")\n", "        return {}\n", "    \n", "    df = pd.DataFrame([{\n", "        'name': d.name,\n", "        'summary': d.summary,\n", "        'url': d.document_url,\n", "        'token_count': d.token_count\n", "    } for d in docs])\n", "    \n", "    print(f\"=== ANALYSIS FOR TOPIC: {topic_name or f'ID {topic_id}'} ===\")\n", "    print(f\"📊 Total documents: {len(df)}\")\n", "    \n", "    cluster_results = perform_document_clustering(df)\n", "    create_topic_visualizations(df, cluster_results, topic_name or f\"Topic {topic_id}\")\n", "    print_analysis_summary(df, cluster_results)\n", "    \n", "    return {\n", "        'topic_id': topic_id,\n", "        'topic_name': topic_name,\n", "        'dataframe': df,\n", "        'cluster_results': cluster_results,\n", "        'total_docs': len(df),\n", "        'total_tokens': df['token_count'].sum() if 'token_count' in df.columns else 0\n", "    }, docs\n", "\n", "def perform_document_clustering(df: pd.DataFrame) -> Dict[str, Any]:\n", "    \"\"\"\n", "    Perform TF-IDF clustering on document summaries.\n", "    \n", "    Returns:\n", "        Dictionary with clustering results\n", "    \"\"\"\n", "    summaries = df['summary'].dropna().tolist()\n", "    \n", "    if len(summaries) < 2:\n", "        return {'status': 'insufficient_data', 'message': 'Not enough summaries for clustering'}\n", "    \n", "    vectorizer = TfidfVectorizer(\n", "        max_features=1000,\n", "        stop_words='english',\n", "        ngram_range=(1, 2),\n", "        min_df=2,\n", "        max_df=0.8\n", "    )\n", "    \n", "    try:\n", "        X = vectorizer.fit_transform(summaries)\n", "        max_clusters = min(8, len(summaries) - 1)\n", "        silhouette_scores = []\n", "        K_range = range(2, max_clusters + 1)\n", "        \n", "        for k in K_range:\n", "            kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)\n", "            cluster_labels = kmeans.fit_predict(X)\n", "            silhouette_avg = silhouette_score(X, cluster_labels)\n", "            silhouette_scores.append(silhouette_avg)\n", "        \n", "        optimal_k = K_range[np.argmax(silhouette_scores)]\n", "        kmeans = KMeans(n_clusters=optimal_k, random_state=42, n_init=10)\n", "        cluster_labels = kmeans.fit_predict(X)\n", "        \n", "        cluster_descriptions = create_cluster_descriptions(\n", "            vectorizer, kmeans, optimal_k, df[df['summary'].notna()].copy()\n", "        )\n", "        \n", "        return {\n", "            'status': 'success',\n", "            'vectorizer': vectorizer,\n", "            'X': X,\n", "            'kmeans': kmeans,\n", "            'cluster_labels': cluster_labels,\n", "            'optimal_k': optimal_k,\n", "            'silhouette_scores': silhouette_scores,\n", "            'K_range': K_range,\n", "            'cluster_descriptions': cluster_descriptions,\n", "            'df_with_clusters': df[df['summary'].notna()].copy().assign(cluster=cluster_labels)\n", "        }\n", "        \n", "    except Exception as e:\n", "        return {'status': 'error', 'error': str(e)}\n", "\n", "def create_cluster_descriptions(vectorizer, kmeans, optimal_k: int, df_subset: pd.DataFrame) -> Dict[int, str]:\n", "    \"\"\"Create descriptions for each cluster based on actual top terms.\"\"\"\n", "    feature_names = vectorizer.get_feature_names_out()\n", "    centroids = kmeans.cluster_centers_\n", "    descriptions = {}\n", "    \n", "    for cluster_id in range(optimal_k):\n", "        top_indices = centroids[cluster_id].argsort()[-3:][::-1]\n", "        top_terms = [feature_names[i] for i in top_indices]\n", "        descriptions[cluster_id] = f\"{', '.join(top_terms)}\"\n", "    \n", "    return descriptions\n", "\n", "def create_topic_visualizations(df: pd.DataFrame, cluster_results: Dict, topic_name: str):\n", "    \"\"\"Create comprehensive visualizations for topic analysis.\"\"\"\n", "    \n", "    if cluster_results.get('status') != 'success':\n", "        print(f\"⚠️ Clustering failed: {cluster_results.get('message', 'Unknown error')}\")\n", "        create_basic_visualizations(df, topic_name)\n", "        return\n", "    \n", "    X = cluster_results['X']\n", "    cluster_labels = cluster_results['cluster_labels']\n", "    kmeans = cluster_results['kmeans']\n", "    optimal_k = cluster_results['optimal_k']\n", "    cluster_descriptions = cluster_results['cluster_descriptions']\n", "    df_with_clusters = cluster_results['df_with_clusters']\n", "    \n", "    fig = plt.figure(figsize=(20, 15))\n", "    gs = fig.add_gridspec(3, 3, height_ratios=[2, 1.5, 1], width_ratios=[1.5, 1, 1])\n", "    \n", "    ax1 = fig.add_subplot(gs[0, :2])\n", "    create_enhanced_pca_plot(ax1, X, cluster_labels, kmeans, optimal_k, cluster_descriptions)\n", "    \n", "    ax2 = fig.add_subplot(gs[0, 2])\n", "    create_cluster_summary_table(ax2, df_with_clusters, cluster_results, optimal_k)\n", "    \n", "    ax3 = fig.add_subplot(gs[1, 0])\n", "    create_token_distribution(ax3, df)\n", "    \n", "    ax4 = fig.add_subplot(gs[1, 1])\n", "    create_domain_analysis(ax4, df)\n", "    \n", "    ax5 = fig.add_subplot(gs[1, 2])\n", "    create_silhouette_plot(ax5, cluster_results)\n", "    \n", "    ax6 = fig.add_subplot(gs[2, 0])\n", "    create_cluster_size_plot(ax6, df_with_clusters, cluster_descriptions)\n", "    \n", "    ax7 = fig.add_subplot(gs[2, 1:])\n", "    create_summary_stats_table(ax7, df, cluster_results)\n", "    \n", "    plt.suptitle(f'📊 Document Analysis Dashboard: {topic_name}', fontsize=18, fontweight='bold', y=0.98)\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "def create_enhanced_pca_plot(ax, X, cluster_labels, kmeans, optimal_k, cluster_descriptions):\n", "    \"\"\"Create enhanced PCA plot with meaningful labels.\"\"\"\n", "    pca = PCA(n_components=2, random_state=42)\n", "    X_pca = pca.fit_transform(X.toarray())\n", "    \n", "    colors = plt.cm.Set3(np.linspace(0, 1, optimal_k))\n", "    markers = ['o', 's', '^', 'D', 'v', '<', '>', 'p', '*', 'h']\n", "    \n", "    for cluster_id in range(optimal_k):\n", "        mask = cluster_labels == cluster_id\n", "        ax.scatter(X_pca[mask, 0], X_pca[mask, 1], \n", "                  c=[colors[cluster_id]], \n", "                  marker=markers[cluster_id % len(markers)],\n", "                  alpha=0.7, s=60, \n", "                  label=f\"C{cluster_id}: {cluster_descriptions[cluster_id]}\")\n", "    \n", "    centroids_pca = pca.transform(kmeans.cluster_centers_)\n", "    for cluster_id in range(optimal_k):\n", "        ax.scatter(centroids_pca[cluster_id, 0], centroids_pca[cluster_id, 1], \n", "                  c='red', marker='X', s=200, linewidths=2, edgecolors='black')\n", "        ax.annotate(f'C{cluster_id}', \n", "                   (centroids_pca[cluster_id, 0], centroids_pca[cluster_id, 1]),\n", "                   xytext=(5, 5), textcoords='offset points', fontsize=10, fontweight='bold',\n", "                   bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.8))\n", "    \n", "    ax.set_title('📍 Document Clusters (PCA)', fontweight='bold')\n", "    ax.set_xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.1%} variance)')\n", "    ax.set_ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.1%} variance)')\n", "    ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=9)\n", "    ax.grid(True, alpha=0.3)\n", "\n", "def create_basic_visualizations(df: pd.DataFrame, topic_name: str):\n", "    \"\"\"Create basic visualizations when clustering fails.\"\"\"\n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "    \n", "    if 'token_count' in df.columns and df['token_count'].notna().any():\n", "        axes[0, 0].hist(df['token_count'].dropna(), bins=15, alpha=0.7, color='skyblue')\n", "        axes[0, 0].set_title('Token Distribution')\n", "        axes[0, 0].set_xlabel('Tokens')\n", "    \n", "    if df['summary'].notna().any():\n", "        axes[0, 1].hist(df['summary'].dropna().str.len(), bins=15, alpha=0.7, color='lightgreen')\n", "        axes[0, 1].set_title('Summary Lengths')\n", "    \n", "    if df['url'].notna().any():\n", "        domains = [urlparse(str(url)).netloc for url in df['url'].dropna()]\n", "        domain_counts = Counter(domains).most_common(5)\n", "        if domain_counts:\n", "            names, counts = zip(*domain_counts)\n", "            axes[1, 0].barh(range(len(names)), counts)\n", "            axes[1, 0].set_yticks(range(len(names)))\n", "            axes[1, 0].set_yticklabels(names)\n", "            axes[1, 0].set_title('Top Domains')\n", "    \n", "    axes[1, 1].text(0.5, 0.5, f'📊 {len(df)} documents\\n🔍 Clustering unavailable', \n", "                   ha='center', va='center', transform=axes[1, 1].transAxes, fontsize=14)\n", "    axes[1, 1].set_title('Summary')\n", "    \n", "    plt.suptitle(f'📊 Basic Analysis: {topic_name}', fontsize=16, fontweight='bold')\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "def create_token_distribution(ax, df):\n", "    \"\"\"Create token distribution plot with statistics.\"\"\"\n", "    if 'token_count' in df.columns and df['token_count'].notna().any():\n", "        token_data = df['token_count'].dropna()\n", "        ax.hist(token_data, bins=15, alpha=0.7, color='lightcoral', edgecolor='black')\n", "        ax.axvline(token_data.median(), color='red', linestyle='--', label=f'Median: {token_data.median():,.0f}')\n", "        ax.axvline(token_data.mean(), color='orange', linestyle='--', label=f'Mean: {token_data.mean():,.0f}')\n", "        ax.set_yscale('log')\n", "        ax.set_title('📊 Token Distribution (Log Scale)')\n", "        ax.set_xlabel('Token Count')\n", "        ax.legend()\n", "        ax.grid(True, alpha=0.3)\n", "\n", "def create_domain_analysis(ax, df):\n", "    \"\"\"Create domain analysis chart.\"\"\"\n", "    if df['url'].notna().any():\n", "        domains = [urlparse(str(url)).netloc for url in df['url'].dropna() if urlparse(str(url)).netloc]\n", "        if domains:\n", "            domain_counts = Counter(domains).most_common(10)\n", "            names, counts = zip(*domain_counts)\n", "            bars = ax.barh(range(len(names)), counts, color='mediumpurple', alpha=0.7)\n", "            ax.set_yticks(range(len(names)))\n", "            ax.set_yticklabels([n[:25] + '...' if len(n) > 25 else n for n in names])\n", "            ax.set_title('🌐 Top Source Domains')\n", "            for i, (bar, count) in enumerate(zip(bars, counts)):\n", "                ax.text(bar.get_width() + 0.1, bar.get_y() + bar.get_height()/2, \n", "                       str(count), va='center', fontweight='bold')\n", "\n", "def create_silhouette_plot(ax, cluster_results):\n", "    \"\"\"Create silhouette score plot.\"\"\"\n", "    if cluster_results.get('silhouette_scores'):\n", "        K_range = cluster_results['K_range']\n", "        scores = cluster_results['silhouette_scores']\n", "        optimal_k = cluster_results['optimal_k']\n", "        \n", "        ax.plot(K_range, scores, 'bo-', linewidth=2, markersize=6)\n", "        ax.axvline(optimal_k, color='red', linestyle='--', \n", "                  label=f'Optimal K = {optimal_k}')\n", "        ax.set_title('🎯 Clustering Quality')\n", "        ax.set_xlabel('Number of Clusters')\n", "        ax.set_ylabel('Silhouette Score')\n", "        ax.legend()\n", "        ax.grid(True, alpha=0.3)\n", "\n", "def create_cluster_size_plot(ax, df_with_clusters, cluster_descriptions):\n", "    \"\"\"Create cluster size distribution plot.\"\"\"\n", "    if 'cluster' in df_with_clusters.columns:\n", "        cluster_counts = df_with_clusters['cluster'].value_counts().sort_index()\n", "        labels = [f\"C{i}: {cluster_descriptions.get(i, f'Cluster {i}')}\" for i in cluster_counts.index]\n", "        \n", "        wedges, texts, autotexts = ax.pie(cluster_counts.values, labels=None, autopct='%1.0f',\n", "                                         colors=plt.cm.Set3(np.linspace(0, 1, len(cluster_counts))))\n", "        ax.set_title('📈 Cluster Distribution')\n", "        \n", "        ax.legend(wedges, [f\"{label} ({count})\" for label, count in zip(labels, cluster_counts.values)],\n", "                 loc=\"center left\", bbox_to_anchor=(1, 0, 0.5, 1), fontsize=8)\n", "\n", "def create_cluster_summary_table(ax, df_with_clusters, cluster_results, optimal_k):\n", "    \"\"\"Create cluster summary as table.\"\"\"\n", "    ax.axis('tight')\n", "    ax.axis('off')\n", "    \n", "    if 'cluster' in df_with_clusters.columns:\n", "        vectorizer = cluster_results['vectorizer']\n", "        kmeans = cluster_results['kmeans']\n", "        cluster_descriptions = cluster_results['cluster_descriptions']\n", "        \n", "        table_data = []\n", "        feature_names = vectorizer.get_feature_names_out()\n", "        centroids = kmeans.cluster_centers_\n", "        \n", "        for cluster_id in range(optimal_k):\n", "            cluster_docs = df_with_clusters[df_with_clusters['cluster'] == cluster_id]\n", "            top_indices = centroids[cluster_id].argsort()[-3:][::-1]\n", "            top_terms = [feature_names[i] for i in top_indices]\n", "            \n", "            table_data.append([\n", "                f\"C{cluster_id}\",\n", "                cluster_descriptions.get(cluster_id, ''),\n", "                len(cluster_docs),\n", "                ', '.join(top_terms[:2])\n", "            ])\n", "        \n", "        table = ax.table(cellText=table_data,\n", "                        colLabels=['ID', 'Description', 'Docs', 'Top Terms'],\n", "                        cellLoc='left',\n", "                        loc='center')\n", "        table.auto_set_font_size(False)\n", "        table.set_fontsize(9)\n", "        table.scale(1, 1.5)\n", "        ax.set_title('📋 Cluster Summary', fontweight='bold', pad=20)\n", "\n", "def create_summary_stats_table(ax, df, cluster_results):\n", "    \"\"\"Create summary statistics table.\"\"\"\n", "    ax.axis('tight')\n", "    ax.axis('off')\n", "    \n", "    stats_data = [\n", "        ['Total Documents', len(df)],\n", "        ['With Summaries', df['summary'].notna().sum()],\n", "        ['With URLs', df['url'].notna().sum()],\n", "    ]\n", "    \n", "    if 'token_count' in df.columns and df['token_count'].notna().any():\n", "        token_data = df['token_count'].dropna()\n", "        stats_data.extend([\n", "            ['Total Tokens', f\"{token_data.sum():,}\"],\n", "            ['Avg <PERSON>', f\"{token_data.mean():,.0f}\"],\n", "            ['Token Range', f\"{token_data.min():,} - {token_data.max():,}\"]\n", "        ])\n", "    \n", "    if cluster_results.get('status') == 'success':\n", "        stats_data.extend([\n", "            ['Clusters Found', cluster_results['optimal_k']],\n", "            ['Silhouette Score', f\"{max(cluster_results['silhouette_scores']):.3f}\"]\n", "        ])\n", "    \n", "    table = ax.table(cellText=stats_data,\n", "                    colLabels=['Metric', 'Value'],\n", "                    cellLoc='left',\n", "                    loc='center')\n", "    table.auto_set_font_size(False)\n", "    table.set_fontsize(10)\n", "    table.scale(1, 1.2)\n", "    ax.set_title('📈 Summary Statistics', fontweight='bold', pad=20)\n", "\n", "def print_analysis_summary(df: pd.DataFrame, cluster_results: Dict):\n", "    \"\"\"Print detailed analysis summary.\"\"\"\n", "    print(f\"\\n{'='*60}\")\n", "    print(\"📊 DOCUMENT ANALYSIS SUMMARY\")\n", "    print(f\"{'='*60}\")\n", "    \n", "    if cluster_results.get('status') == 'success':\n", "        df_with_clusters = cluster_results['df_with_clusters']\n", "        cluster_descriptions = cluster_results['cluster_descriptions']\n", "        \n", "        print(f\"🎯 Clustering Results:\")\n", "        print(f\"   • Optimal clusters: {cluster_results['optimal_k']}\")\n", "        print(f\"   • Silhouette score: {max(cluster_results['silhouette_scores']):.3f}\")\n", "        \n", "        print(f\"\\n📋 Cluster Breakdown:\")\n", "        for cluster_id in range(cluster_results['optimal_k']):\n", "            cluster_docs = df_with_clusters[df_with_clusters['cluster'] == cluster_id]\n", "            desc = cluster_descriptions.get(cluster_id, f'Cluster {cluster_id}')\n", "            print(f\"   • C{cluster_id} ({desc}): {len(cluster_docs)} documents\")\n", "    \n", "    if 'token_count' in df.columns and df['token_count'].notna().any():\n", "        token_data = df['token_count'].dropna()\n", "        print(f\"\\n📊 Token Statistics:\")\n", "        print(f\"   • Total tokens: {token_data.sum():,}\")\n", "        print(f\"   • Average: {token_data.mean():,.0f}\")\n", "        print(f\"   • Range: {token_data.min():,} - {token_data.max():,}\")\n", "    \n", "    print(f\"\\n✅ Analysis completed successfully!\")\n", "\n", "def analyze_multiple_topics(topic_configs: List[Tuple[int, str]]) -> Dict[int, Dict]:\n", "    \"\"\"\n", "    Analyze multiple topics at once.\n", "    \n", "    Args:\n", "        topic_configs: List of tuples (topic_id, topic_name)\n", "    \n", "    Returns:\n", "        Dictionary mapping topic_id to analysis results\n", "    \"\"\"\n", "    results = {}\n", "    \n", "    for topic_id, topic_name in topic_configs:\n", "        print(f\"\\n{'='*80}\")\n", "        print(f\"🔍 ANALYZING TOPIC: {topic_name} (ID: {topic_id})\")\n", "        print(f\"{'='*80}\")\n", "        \n", "        results[topic_id] = analyze_documents_per_topic(topic_id, topic_name)\n", "        \n", "    return results"]}, {"cell_type": "code", "execution_count": 18, "id": "9022b12a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== ANALYSIS FOR TOPIC: Evaluación Financiera y Gestión de Riesgos en Proyectos de Software ===\n", "📊 Total documents: 38\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_55508\\1311764639.py:162: UserWarning: Glyph 128205 (\\N{ROUND PUSHPIN}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_55508\\1311764639.py:162: UserWarning: Glyph 128203 (\\N{CLIPBOARD}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_55508\\1311764639.py:162: UserWarning: Glyph 128202 (\\N{BAR CHART}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_55508\\1311764639.py:162: UserWarning: Glyph 127760 (\\N{GLOBE WITH MERIDIANS}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_55508\\1311764639.py:162: UserWarning: Glyph 127919 (\\N{DIRECT HIT}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_55508\\1311764639.py:162: UserWarning: Glyph 128200 (\\N{CHART WITH UPWARDS TREND}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "c:\\Users\\<USER>\\proyectos\\ia-gestorcontenidosiagen-be\\.venv\\Lib\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 128205 (\\N{ROUND PUSHPIN}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "c:\\Users\\<USER>\\proyectos\\ia-gestorcontenidosiagen-be\\.venv\\Lib\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 128203 (\\N{CLIPBOARD}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "c:\\Users\\<USER>\\proyectos\\ia-gestorcontenidosiagen-be\\.venv\\Lib\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 128202 (\\N{BAR CHART}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "c:\\Users\\<USER>\\proyectos\\ia-gestorcontenidosiagen-be\\.venv\\Lib\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 127760 (\\N{GLOBE WITH MERIDIANS}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "c:\\Users\\<USER>\\proyectos\\ia-gestorcontenidosiagen-be\\.venv\\Lib\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 127919 (\\N{DIRECT HIT}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "c:\\Users\\<USER>\\proyectos\\ia-gestorcontenidosiagen-be\\.venv\\Lib\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 128200 (\\N{CHART WITH UPWARDS TREND}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAB8YAAAXDCAYAAACiVVTKAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjMsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvZiW1igAAAAlwSFlzAAAPYQAAD2EBqD+naQABAABJREFUeJzs3QmcVfP/x/HPTKv2fSekvYRoQbInkpCQrRDZ/WwhZM1OyL4VocjegiyV7EJSKFRKpU373tz/4/3l3P+ZM3eduTNzZ+b1fDzuY+bee+65Zz/nns/38/lmhEKhkAEAAAAAAAAAAAAAUExlFvYEAAAAAAAAAAAAAACQnwiMAwAAAAAAAAAAAACKNQLjAAAAAAAAAAAAAIBijcA4AAAAAAAAAAAAAKBYIzAOAAAAAAAAAAAAACjWCIwDAAAAAAAAAAAAAIo1AuMAAAAAAAAAAAAAgGKNwDgAAAAAAAAAAAAAoFgjMA4AAAAAAAAAAAAAKNZKF/YEAAAAAEBJNHz4cFuxYoX7v3PnztatW7fCniQAyGb79u1233332ebNm93z7t27W8eOHQt7sgAAAAAgVzJCoVAodx8FAAAAAOTGI488Ypdeeqn7v0uXLjZhwgSrVKlSYU8WAGRz5ZVX2gMPPOD+P+mkk+zll1+20qXJsQAAAABQNFFKHQCAImzEiBGWkZGR7ZFODj744GzT1q9fv8KepCJt8uTJOdb3/Pnzi912ky60vfqXkbZnxKbtMbhtabstjvJyfPvqq6/sqquucv8fdNBBNnHixGIRFE/VMaokCS4vHZ+BVNp1112zbWM333xzwp998803w0HxPn36FJmgeHE9F2nd+edJ6xYAiuP5p7j7559/bPDgwbbXXntZ5cqVLTMzk9+cAFCA0v8XDYC08M4779jixYsTHr5BgwbWs2fPpL4jUmCmVKlSVqZMGatYsaLVqFHDGjdubG3btrVjjjnGDjvssKTGj6Il+KOpV69e7kdDqixfvtwaNmxo27Zty/Z6vXr1bOHChUXiph8S245uueWWHK/ruFKuXDmrWrWqW+dNmzZ1ZYxPPfVUq127dqFMK0o23bA/5JBDkvrMgw8+aJdffnm+TRPyx8qVK13W5datW11QXJnius5Jh0DSbrvtltRnLrvsMhs2bFi+TROQHz755BMbN26cffHFF/bnn3/aqlWrXLlw7Ye6NmzWrJm7JjjqqKPc7450PF/4g7zVqlXLl3PBb7/9Fm7wc/LJJ9tLL73kfpshOVqGI0eOjPr7t0KFClazZk1r2bKlHXnkkXb22We7dQqkkgJtU6ZMifp+2bJl3XbXvHlzO/TQQ61///7u3guK1nEb8YPRzz33nLv2njVrlnuu+z6616iHroPbtWvn7jtpP9C9gvyajv32289+//33Qr8vBgAlFXf9ASREmQKxfkgFde3aNenAeCQ7duxwD/VppxvJc+fOtQ8//NAFA/Sj7aGHHqI/zmIqGMxUa+NU/gDQzb1gUFyWLl1q7733nvXo0SNl34X0o3Wvx/r16+2vv/6y6dOn2+jRo+2aa66xs846y+6///5ikb0JIL2oF6vTTz/dNcDStdL48ePTIigOlARTp061iy66yH766aeI769evdo9dLNcmdK6JlCQ8tlnn7V0ouCK/zpZwatUB1g2bdpkvXv3trVr1xIUz+dzwoYNG9xDjTTef/99Gzp0qL399tt2wAEHFPbkoQRRY71ly5a5x6effmp333233XPPPXbJJZcU9qQVCwVx3Eb8daDKJ0qQCG77GzdutEWLFtmPP/7ojr/y7rvv5ts9oUcffTShoHhB3BcDgJKKwDiAhOmmiAJH8eiGry4q89uvv/5q3bt3t1tvvdVuuOGGfP8+FC+xSpXqPQLjqaFjhhq2eNI92LxlyxZ76qmnXEMgtSTffffdC3uSABSj45uyA1U2vbjq1KmTzZs3L9trjRo1KrTpKQqCy6tWrVqFNi3F2b333mvXXnutZWVlJfW5v//+24q6adOmuYx4TyLZyDvttJP98MMP+TxliFVVRA3Cgw2nFEjzd9tBhSvkF13fXHrppa6aQd++fQt7coA8UYPUY4891jWKTwfqUslP9xxeeOEFV3lTvxXKly9faNMGACUFV9EA0lbHjh3dTWdljKvE4ffff+/K0H3++efZWtnfeOONVqdOHTvvvPMKdXpRdOhG34wZM6K+r9bB2uZUTgt5ozLl6eaVV15xwRsvO2fSpEkuG0xZUf6GN+qy4csvv8y3EmpAImWqY2WTcIwqXOl4fEu1E0880e67776o71epUiXbc93Io8/b5LC88p/Kpir7O2ifffZxGeH77ruvO54qY0zXBd98843rRirWtWJRQuOU9KOMXK0XBWm0vWn7XLFiRfj9JUuWuOzxE044Idvn1KiBMutIdcMs3W9RdypqQKTtzk99IBMYR1Gnbn/8QXE1/tK2re4Z1fhDDUG0D6iKnPaBYOA61YIB+gMPPJAqIQBQ0EIAkICuXbuGTj755ISGPe2009zwydIhyf+INo4HHnggx7AVK1YM/f333xGH37p1a+jFF18MnXDCCaFddtkltNNOO4XKly8fatSoUejYY48NPfPMM6HNmzfHnb4ZM2aELrvsstC+++4bqlmzZqhMmTKhWrVqhdq0aRMaMGBAaPz48dmGHzJkSLZpbNy4cY5xzps3L8e8fPLJJwmN55133gkdeuihoapVq4aqVKkS6tKlS+itt94Kf2779u2hhx9+OLTXXnuFKlSoEKpWrVro8MMPD02aNCnuvH722Wdunlq1auXGrXmtV69e6KijjnLLS8s0Ek17cH40j4sXLw5deumlod133z1Urlw5t/y07L/88su4yyPaI9LyTJTWo39cWqf169fP9tojjzwS9fN5nU/PunXrQs8991zo4osvdutvjz32CNWoUSNUunRpt9ybNWvm9rs33ngjlJWVFXEczz//fI5p8bzwwgvZXq9cuXJo/fr1EcejafAP27lz52zvf/PNN6HzzjvPbe8aT6lSpULVq1cPNW3aNHTEEUeErr/++tCbb74Z2rZtW7bPaT/2j/ess87K8d2rVq0K3XHHHW4Z1KlTJ1S2bFm3n2p/3W+//dy2+PTTT4cWLlwYSlZw/4m0j8mff/7p5i047FVXXZVjWO1DGq/WbevWrd2+ofWt44q2o8MOOyx09913h5YvXx51ujZs2BB66KGH3D7ZoEED93k9GjZsGNp7773dcho+fHjo119/TWjb03dpWrUNJbLtRdtu5s6dGzr77LPd8dGbHj3Xd8SSl+OstplRo0aFrrjiCndM03avY6v2g0qVKoV22223UK9evUIjR44MbdmyJeI4oi2XOXPmhPr37x/aeeed3XEseNzQd+s4uc8++4SPkwcffHDotddec+9rPcQ7L8XaB5MRaR60nSVCy1/7jv+zmq9I/vjjjxzfM2XKlPD7Ot5cd911oW7duoVatGjhxqtlp+Wj5di9e/fQo48+6o5fkSRyXktkuUZbp8Hv0n7Ur18/dxzX+tXxSduOjk/aly644ILQ119/HXcZalsYPXq0O+Y2adLEjUf7gLbhAw880K2L33//Penjm+e7774LXXjhhaE999zTbWeaRu2nHTp0CF177bWh+fPnR/2s5iu4Xegc/8QTT7hjtc4XWj8632t57NixI5QbkdZdrHmKJJH1Fm39f/DBB6EePXq4/V/nAZ1LdVz4559/In7XTz/9FLr33ntDffv2deta26euB73rs44dO4auvPLK0OzZs6NOb7R1OGbMGHc80nak7aBly5ahW2+9NbRp06aY869pffDBB91+ouOnjoOaJs3LMcccExo2bFho7dq12T4TXF46pkQzceLE0Omnn+6O9To+etvo8ccfH3r11VeTvlbQuV3bvM5dOq/798VUnOui0XRqHvzTc8stt0QcVtf3mjb/sO+//37C37Vs2TK3jwTnX+szHu23jz32WNT3tT3o+kTLSOvBW9+6NtK586uvvoo5/g8//NCtz+bNm7v16R0XdOzV9nLzzTeH3nvvvaj7TqyHfzuKdAyJZvLkyW7aNQ3eb4DatWuHDjrooNBtt93mlmc0kaZBy0jbiY5PWjaaTx23dN7Pq1mzZrn939s2db1wySWXhJYuXZrQucij46n2+ZNOOim06667uuOp1qX+P/XUU92xKS8irbfgcVHn7OAwWm5Bify+9O87Wmdad1qHWpc6/+h4ec0118S8rtY5UetPx2Tvus77Pdi2bdvQKaec4n6Tf//991GPhfoO77pU1xK6lps2bVqBbpO5vd5ORl62n2jrU9ctOjZreet8qOOLfg8tWrQo19MZPN9Ful7VdbbOV8Hh9Psg3jncO2/qt2y0dZqbdanh/d93yCGHRJ3H4G85Xc8GrVmzxp2njzzyyPDy1TWfznMXXXRR6Oeff467LDdu3OiO/dqmtX/o2KZ1rvWnbe2uu+5yx6G8HLc9f/31l1uWBxxwgLu20TLTstN9Gm0Tn3/+eUrPM8lauXKl+w2qa2ftW3Xr1g316dMnNH369KT39dzeg0qE7in4p0PzHYuu+WMdI3P72ze4PGJtC4luN95xQ/MUb1/RtbL3vqY3+Nta24p/HOeee25a/GbP6zkNACIhMA6gyAXGRRdoweEHDx4c8aapbmbGu5jURZxuDkaiQGLwB1mkR7t27QosMK4Lv2jTMXToUHcTQBeMkd7PyMhwF6uR6IeiftTHm1f9WIl0AyHSha5+FES6IamHfoj6b24WRGBcP2J0Ie0fl26I/O9//8v2Wvv27aOOI6/z6dHNpETnV8G64M30eEE5/SDRj1P/e/oRH6Qgin50BufHf7NO200i07lkyZKkAkfajoKNEqI97rzzzlB+Bca9H8BaV/5h9SM3uNy1rycyvbrpEOm7dNMnkeOSHueff37cbU83ooLbdLxtL9J2o5siurETaTz6sesPnqbyOKubp4nuB7oZqxtEQZGWi7Z1rb9oxw0d23XDINp36cbMmWeeWSQC46KbFP7PdurUKeJwt99+e7bhFMDxU2OrRNaFluXMmTMLNTCuG5uJTKuOX1o+sYJfukEYbzz6vmQD4woKaVuKN27dVIoUBIl0E00NsBSsjzauZIPZhR0YV6MoBbOizY9uVkdqiBFs5BZr2SqAE0lwHSrYc+KJJ0Ydl24EKggSySuvvJLQ/hMMJCVyY1wN73QdEG/c2i6C5+Fox6kRI0bkCDj798W8nuviUSMB/3h0YzlSow41xElkuGhuvPHGHNOsG7959cUXXyR0g3vgwIERb+ar0UYiy1eBhoIIjKuRYs+ePeOOV9cJ0X5HBIdVY4dY1wfJnOOC1BgzeM3mPXSjXu8HX4+0nf7yyy+uwVK8+Vbjk0jX4akKjE+YMCHHMJEaZSQaGH/22WddgDbetuW/3veft2KdY/wPNaIL+u2339x+Gmn4zMxMt+8XxDaZl+vtROV1+4m0PnUtoOUUaTwK7uc2OJ5IYFx69+6dYzh/8DXSNVyk6xz/Os3Luvzhhx9yXNMpiBapoUysgL68++677pwVaxq07GM1nFKgWesh3rzoGBRpecV6BM//Ov9pP433OTXAiHSdlJvzTDK0HhQIjnbtpflJZF/P6z2oROj3jn9cWja5lZffvvkZGJ86dWq213UO8F9/RLrO//TTT7NNX/DY/fLLLxf6b/a8ntMAIBoC4wCKZGBcP7iDwysTIZgVFy1YFOmhm5nBrCK1ilRr4kQ+X1CB8XgBSt3kjBYU9x5qFa0fIH66aI70gznaQy3tdaM23oVuvOnVcvFuMhdEYDx4o0wtTZXtpFbNwe+IFPRJxXzmJjAe7UZuvKDcTTfdlO09ZSfGmx/dlPB+XOsGu5ZRotOYbGBcmSiJjju/A+ORWknr4a/EkEywQA+1Yl6xYkXM7Py8BsbjrZ9I216k7Sbej01lgAT3+VQcZ5P5ka2HbtYGRVouuiETaVl4ErlRFVwm6RwY102a4Oe1foIUZAw2pspNYFwPZRUFAz7pGBj3HpFulvz4448Jz3OygXFlxUa6wRzroeoZQcGbaIk0VNKN26ISGE9kfm644YZcB8a9R6SKOcF1mMi0RNqOFGROdDqSDYyvXr064eCOHgrSBKvDRDpORTpG5iYwHu1cF4+uQ3U96h+PgoPx1pGua5Kh3wfB6U0kIzAWNaaJ1pAs0kMNbP10ozzRzxZEYFxZbcpGTGZ/ipTxHRwm3v6kAFRughxaf8p0izXuSNc0wXPRggULcjQMjfXQb8JoDWPyGhiPVBEtUiZoIr8vn3zyyaTWpSpM+d13330JfzYYGNe2pOBVvM8Fjz/5sU3m5Xo7EanYfpL9nZ+Xhj2JBMZ13RK8TgweM4Pbc6RziX+dpmJdBu9tKEkgKLgs9Rk/nV+CjcFiPSIFx3Udkejv4rwGxtVwP5llpqxq//aV2/NMotSAJl6jUi3vYGA/uK+n4h5UItQIM3j+Uba3lrl+myba4C6vv33zMzCuZRk89/mrxwWrCQZ/B0b6HeBVPijM3+x5PacBQDSZBV67HQBSoEmTJjn6y/vxxx8tKysr/PySSy6x5cuXZxtG/ZCrTzf1Gxzst3XNmjV2wQUXZHvtiSeesA8++CBHn6IPP/yw66da/RBPmDDBjVf9FBUE3XeqVKmS6xN59uzZ9sgjj2R7X32ETZ061U3n2LFj7aeffrIrr7wy2zDr1q1zfSf6PfroozZlypTw8zJlytiQIUPs66+/dt/z0ksv2S677BJ+/6+//rJBgwYlNL3ql0z953322WfWtWvXbO8vWLAg3G+81qn6OvP6O/NTn2fee3pMmzbNcmPEiBHZnnfv3t1q1arl+pls2bJlzGFTNZ+ejIwMa9eunevf6q233nKf0zalbVnr59hjj802/Msvv+yWezK0TZctWzb8XOtT4/cbM2ZMtud9+vRx25homrdt25atL1RN688//+wen3zyidsf1A+t95lk+Lc5GTp0qH3//fc2d+5ctyy13V100UVuny8IBx98cI7XND1+derUsTPPPNNefPFF++ijj9zy1HrTNnnzzTdbuXLlwsOuXr3ann766ZjzrGOR5lXz/N1339lrr73m9tm2bdu6bSQerZ9kt71INm3a5Pq41Hi0Xnv27Jnt/VWrVtltt92W7bVUHGeladOm9r///c8ds3T8+uWXX9yxS328nXXWWdmG1XLWuOPZvn277bfffvbuu++69aPlrumVmTNn2siRI7MNX7t2bXvhhRfc+tS+Vr9+fdffbGG65ZZb3DYQ6RHsY7R169bWsWPHbK9pPvw0b7NmzQo/L1WqVI7lq+P8wIEDbfTo0TZ58mQ3vM4B2ia0jjIz///nwx9//GGvv/66FRYd2w499FB74IEHbPz48e745u1HWpdaJsHzSPC4rf6FtV36aZw6Bmu7UR/DOt+q779kadlom/bTNOkYqnWhbVDbnZ/Ou7/99lvM8Wq6dUx8++233bas7SQouO5zS9MYbRvUQ8e4vNL8VKxY0V2HeNcbwb7LI81PhQoV7JhjjnGfe++991y/kFr/3377rT355JM5rhNj9ZXunxYd40eNGuW2fY1b10OxpmXp0qXuPOWnZXP++ee7486cOXPcMevuu++23Xff3ZKlbULnW0/lypXdNq/tXMdJzWv16tXD72vb0nclcow88sgj7cMPP3THXF3vnnrqqSk718WjddyvX79srz311FPZnqufZZ1P/Mu1f//+CX+HfhcEr3kaNmxoLVq0yDGs+haN9vD3Bapt5JxzzrENGzaEX2vevLnbbrU+tP1dd9112c7f6uP8448/Dj/Xec5P16A632n5arvTutDx6uijj7by5ctn24Z1DXzZZZflmCf/NbIevXv3Tng56beErh/8unTp4vYrHQP1vvZRv4svvjju/q9l1b59e5s0aZL73RTcT7R+gtegidB1s/qD9Tv99NPdtqnrHa2fRM7fl156qdt/PTrvaxvUPGt6tQ7827nWS/DaIbcWLVrkti1tM9o+br/99hznoc6dOyc93sWLF+e49jrqqKNs4sSJbj/Xeb1Xr17Z3te10T///BP1OlXXmFquOr5q2egcdsMNN7hrDv81gTzzzDPuOO6n+dA1hI5jDz30kFumOv7k9zaZ6uvtgth+tM/o2kzbg7YNXYs0aNAg2zC6rvD/NksF3T/4/fffbcCAAdmuE71zQbNmzaJ+1luXWh7ql1mf17Lt0KFDytZl8Jin3+jBZfDqq69me67jgP83jp5rPj3aft988023XX7xxRd27rnnZvu8rq10/vbomKPzT/B7dazVutWwOv5rfvT7Pi/HbR0frrrqqmzD67pf53stM13z6tjqp+Wp83VezzOJ0jlfxxQ/TZOONfrtfM8997htecuWLTHHk1/3oIKCxz2df9544w23Tlu1auWuSQ455BD3WzfWdXhef/vqPKV1HfzNpnspwW0h2ftiWnbB3yv+a6jgNhF8Lfi+lkvdunUL9Td7Ks5pABBV1JA5AKRxxrio78jgZ7x+qdSCO5HW1WodHhzO36IzWHJJfU1G62s32AdmfmWM66ESdH7qHzc4TLC/cZUVjNV3svqF8r+vfjuDlIEWbAXsn+9ILUDVj6C/z0uto+AwahEdFKnVbF7pu4OtvL2+hEWZev73lAUQ7DM71fMZi9ZbMJNRfeAmm62qfdL/vvq59X9HsG9ir/8/UZnuYEnQWP2tJdvHuL9slkrRR+uPSnJTwjLZjHH13xoc3r+8EhHMUFELfr9g5p+/JXa8eU7Vthdpu1F5Zj+NM5gxqHXktahP1XE2EcE+A9V3X7zlolJw6lYikkjdUaj8nF+kKhIFnTEe66FjQ1CwRb2yfvwGDRqU7X31LZisYJWH4DGhIDPG4/n2229zjMO/v6kvw+D7KqMdrZ/m4Hk+3vFNZbeD+4/KiQbLMQenIZgJFcwuUZZLcB/SuvQPoz7Xk5VM1RbvEVwmuckY10P9pfvpGiQ4TDALOp6xY8dm+7wyfIMZQZGylIJ9barPUf/7up7yC1476KE+bSNRNk/wuBTrekeZdsHsH/91i0dZ7P5hlM3k344jHad0HZ2b7NdEz3WJlmH1Z0gqa8ifCRbsd1l9tyYj0vkwUuUcibWd+9eJyo7639N1ZaTSxsEKNDq2eFQqOdY5zS/R8suxxMsYD/YprPKvwWog6iogXqnvSN2wBCsJBLNRVVUjGVoewaxPHWuD1MdrrHOR+iINvq9+nYNUrSLRrpaiSSZjVA8dz4OVvRJd9+p/NVjONnjc034f3CYeeeSR8Pvdu3ePmnEYb/sM/j7XeS94nghu//m1TeblejueVG0/kX6nXHvttdmG0TE/OIyqBCUrmazcWNVyIm3PwfsKqV6XkbbZV199NVv1n+B25z/Xqi/o4Dky0m+EYBcC/nLbL730Uo5pVFdskej8G9yHkzluK1s9XiUgVXcL3t/xn9/yep6JR98VrBwTnOf7778/7r6eintQidD11P7775/QNq/rbPV3HbzuTOVv30S6Y/Ikc19M69k/rLow8Kifee+6RZXgvH3FO0ecc8452T6r67xkpfo3eyrOaQAQDRnjAIqsf68Rs/Nae0dqDamWnEHK5gnyPqvWqGpRHhyHMmYjCWbv5aczzjgj2/Pddtst23NlDvXo0SP8XK11g1lK/laUmle1Eve7+uqrc2SGHX744dmGUavreJmoyg7xt8JXdlzNmjWjTkt+UotjfytvrTN/VvZpp52WbVqVBaDW14nI7XyqFfGwYcNc5rrWo7KulX2hcZUuXTpHJqNakCdLLfiDy8HLpFEGx7Jly7JlPR1wwAHZWpf750uZEN26dbNrr73WZbho/XvjUtUETXMy/K3d165d67I2LrzwQrdM1BLYnyGvLLnCPK74qaW+Mt2UEaD9TS20vf1k+PDhMddZsIX/vvvu67IU1PpbmSHKwk1mnlO1jylz1i9SZp7WkZe5mIrjrD8LQ5kHavGtluiabx23vGWqlujJ7gfKAlJGaSTKaPHbY489XPaKn7b9vfbaK+73aDv4r3ui8KOwnHLKKdnmWRkhyiwRTZeywKNl0/iP6xpO41KmQNWqVd1+7a2LcePG5fmYlEo6dyl7UOtP2Vo6DnnTqn0ryD+9Ov4FKUsrWuZYMud5LcdgZRNlf/gze6VTp0625557Znst0r4VzCQMVjgJZsAWpUwJnfeCmcORMnojzZOy8q644gq3HJXVpqwnb/0HM2aV4Rtvuej4o3NcrGkJjiO4HSm7L5gZ69G5ItpxKRJlnwUzX0866aQc12fBTDdlM/mzzCO5/vrr3XE2lryc6xKh5a3MH3/W0PPPPx9+HswmDp6nciM3maF+wSxUXVeqOkFwnajqQLT9OngdcNNNN9lxxx1nN954o8v403rfunVrgVz7aL35rztE5/5gpQRVEwoev+Idq3QeCV6P5PVYpcoQ/qzPaNtFpPNbrPUoynANrsdgJrcyIf0VBFJNWaLKmgtWzUhUcL5UVcR/PeX9vlA1oUS3T1XmUPWIO++801VCUZaed63j3z61LygT20/H4eC5M3i8yq9tMtXX2wW1/ShbOjfnw1TT9Ov8Gsx0DtJ60e/C/FyX2ob1+9DPX6UkeK4IXg8H15fOkcpSD66v4HWbfxqC53rNg6qnRKJx5XYfjjS9qhJ02GGH5bh2UjWHaNcM+XmeibavB+c53nE4P+9BBal6g6rfqKJLvPlVNrmuRfxVdFL92ze/6DeCn7ZpHa91v0fZ2N6x8KCDDgr/tte1dKRpDI6rMH6zp+KcBgDRJHf3GgDSSLCktIKJNWrUCJfcCYpUijlSSUvvs5HGEfyBURgUpPDm0xO8kFS5qeDNzmCpd38Ju2TLcwfLXMYS6cd8rGnJT8HS6Lq57C9x17hxY1d+yl9ySp/xNzJI5XwqOKfAfLAcVyy5uRmnGzUKGnilrBRsV7k53ewOlp0L3lxUwFDlqFQu3fuhqNJr/i4GdGNA5btV1iz4wzWeu+66y4444ghX4k5Uhs5ftk70o0s/rFVGy7++8kOkfcFf6lg3G0444QRXLiy360w3JtTgYsWKFeEfjCrVHCyvpwYwutGkfb4g9rFgA5tor6nBiMpBp+I4KypXp0BU8MZZXveDvffeO+p7f//9d9z59F73bhYUBt2MDJaP8wTLl4puSOnGlMqI+8s+K6ilMpb+mwYKIgaPbToWqZGOAg+Jys8AQTxqqKMAZDLnEP/0BrdhnUsj7U+5sXLlyvANR0+0LiG0j/jLPUfatwrrvKrSjrFKkOflxq9HDQ6Dx/ZI3dME50k3d++4446kGqNo/QcDdX5qHBYUnJZgUC64vtSgJl7AOVF5vT5T45bcHCNTca5L5jinhnD+Usy6ca1599/4VtDk+OOPT2rcWtc6Vvq7WsrLMs3L53XO1zasG7gKcChAoe4QRMcKBer83RypkYcaDajhT6RGPqmS6Llcy1HXyf6AXGEcq4Ln72SuX1KxHrUtaRpy031QInS8VcNsBaBzcxzJ7Xz5f89pn3zllVfCASudz/wlmr196+STT3bHYXXf5XW5EywzHWlb0u9YBcujleJP1TaZ6uvtgth+9LqmKdnzYSqpoY9KSisQrd+P8ey8885Rz6upPL6ozLvKm3uBX3UDojLS2tdjlVFP1X4RnB79Pk3FNVAkwe+K1g1L8HVv+9Iyyc/zjPb14DYY6ZirfUrn7mgNOfLzHlQkml9126auINTFh4Kuuj+iIH+kku9qpKQGBN6ySdVv3/ykht1a7l5yhdaVStL7y96rMbFKpKtbDC+IrEad/qQg7ZPBLuYK4zd7KvZdAIiGjHEARZIu2oIXSbrxHylQkC6CN1LF+6GejEgZa8H5LsjsdS+gGU2kH8qpumGcDAW3vMxJj1q7Blsk+4Pi3g8i/aCIJ9n51I0jtY5PJiguuc1GDWaNa971g1Z9a3l0s1YZIUHqD1B9sKkPskg3cjQvujmhvkqT7W9Y2ekKCOnmi26IRNvfdcNKDRnyW6QMUv+PNWV+JBMoiLTOdCNFrZ3Vn3e0Pvt0fFOjAbXUjnfzK132sdzSNpfMD+xE94Ngn4zJfj4d6FiuoGGkh7+/vViNW3RzW/Orv366ERzM2NHN8GSC4qlalrk5P+omj44byd4cLirrPl32eR3zo22DeqTiuis386PGWToeJ7s+4w1f1I+nyVyfxTpGpuJclyhdO/gDqAp06JpCfdT6x6mqPsn2g6rtM1iRQcGxYEUob/r1iNSfZypo3P5+sXX9pYw0BaAiNfrTsLppresk9X9bFOXH/pQOx/B4+1YitJ0pUKUMYjUu9VMgK5hpXJDzVKtWLRcoUl+7+n0dqcqCguWPPfaYa3wbq6/5vFZoyItUX28XxPZT0Ocgfz/FWhYKYi1cuNA1sEwkKB7vXJJKCrCefvrp2Y4Fakilfch/TG/Tpk24f/N02NcLU36dZ4r6cVgNYdVo4IEHHnAN8BREVgO9SJXCks1ML2w6XgTPKbq/5b/HpcC4lzHuvR/MsNayCFZxKIzf7CV13wVQMMgYB1Ak3X333TleU9Au1sWVWr0HX490YadSrNHGoYBBosG5YEnpYClMCWbGFpZI8/rkk0+6m5XxxMq8SifBbPFE6aaVMi6DZe3ySj+y/vzzz2yvKTtL2Y9qqV+2bFn32n777ZerBhRByiJVeUavtbK+X1ngurHl34e8rI8glcvSQy3RlXWq/UllWkeOHBkOpOmHj27kKcMwGcpKf/TRR91DjRB0c0OPyZMnu3Lt3g8qNVJQ4wbdpMsPmqexY8fmyNLQzQRPMLio4KRanuvGu1eWTcenJ554IuZ3aTlrOD3WrVvnjgX6ft2U0HLwMm50Y1KlbPXjPb/pplhw2UYKEHjbSCqOs9qWgjdi1DpdZfTU2t4LgihLMNnM7Vg3EtVK3isnJ9ECIfkVIMlPutGh7AUv00s3ONXgQ0GmWNk0OtYFG7Zoux4yZIjLolXJSVEFiWA59WSl4vyo+fEH1BUAU9UK7Su6qa/1r2UQq4pFcFvVdCijIhVZ4zo36jjuzxoPlouMto94+weiCx6LdaxQJpkypJSNqGCMMoGCJdrzg7YjNdTw6Filc2UqGg1EOs7qnBArE9x/nMvtMTJV57pEaF3pGst/naVqEMHym7kto67KGMHzh6ZdAZVUrBNlZulcncj69o6jouG1feqhgJzONzpGqAyp5t8L9OgYcs8997gGivkh2rk8yLv+K+xjVaTtWstu//33z/FaMvOt7VDrMZHGxcGM3txS4zQFH3ROVSUe/+8CbaPa5pWNmwzNl78bBVVl0vYUTzBopkxYZVXqoQCDtkdlC+qaX90oqPyud42h3wJqWKdjr+bJnzU+f/78HN+la/1YwfRUbpP5db2dDttPKkTrIi4Zsc4lqT6+qKG3f3tW0DdYnSfSuSI4HeqOZsKECZbMvAXHof1B+0F+ZI0H9+NErx91XvEfI/PrPKNrXF3L+xuTRDrmaj+PVfY/Xe5B6finzHntD8GuitQNjycVv30Lghr6+CsDKPDtZYzrWKWGENpu1fhVjWH0fnD6gmXUC+s3e6rOaQAQSfqmVgJAFA8++GCOMmi60aSb9R5/C0j/RXYir3mf1Y9WtTQPZtkGg5me4A/84A9j3QQIlv9TS/t0oEBssOSTWg/rRmi0LDG1tNXNkfzq+zCYyZiXVp+66aHgdkEH1WOJVBZKN2n1I0RZDVrGCoinIijuLc8LLrgg22vB/uAi9QOmQJG/n3P9wFapNAWbtM8FbyzH69M0Xlkx3VTr2LGjywjQuIOZXsmOP1G6safAf/Dmihoq+Lfx4HpTmWtlsalvdK0zHTeC/VfHm2eNX900qIKAjm/+Rj75Oc9BweOqGiT4+3oV/Yj2goapOM5G2g/Uel/LQN+jZaofy/4gdipoGwve3ApWi9ANzkR+2Ov4EKw8UZgi9Q2v7dhfnUJZQMEbPzrWBLd/9Z2oBju6Ya91ocwBZebkVfD8qJtK/u/WMdvfd2QkwW1HGUIK3GndqmGApjfetPobvfhLr0bLboh1Iz9I26265ggG84M3CFXC0V9GPdq+hdjrXzdRlRWoY6nOUVr/33zzTYFMS3A70jH+8ccfjzistu1IDUGiUeO4YHc5yiaNlcWvY4DOG5HK7yYqFee6ZJx11lnZyhjrGtQ/flVuiVVqMxZdqwSr3eh853URk6xgaVFdI2laY60TdUGifd87P+hY4r+uVYDB629djRgVoIh1HeA1nkzFNXKk3wA69wdLYqtUcfD4VRjHKu3jwZvoakQZ75omKJhRp+O+MgZjrUcFSPQ7MPgbJa+0fapbiOA6Db6Wm+1TgQyty2jzpMC7fs/5gwjaXv3nQR1LdD2u6wFNU/Aaw9s+tVxUxtdPDe78gSWJ1yglVdtkfl5vp9P2k85SfXzR9ai/r22VS1ZlM/+xURWR4u0XChJq+4i2rlTFTEFj/3QGz/W6blVVhUi0PXiNR/zTluhxO7h9KdCqSip+Cmi+9NJL2V7T9u1dM+T1PBOLtuHgOVmNy4PzHOnYXFj3oNSPtX6zRSqZ7gkeq8Tf0CAVv30L4r5YMKitbcernKjfS/o9p/Oo16BMvxGDDSKD4yis3+ypOKcBQDQExgGkLZV2Uitz/RBQ3z5qGaiLtyuuuCLHsPfff3+2i1ZdTB9zzDHZhtEPh4EDB7p+Vr/++ms3nuBFq36E+LNw/MF2L7itgIJamOtGtlqdq38itZIPfl8woCf6Ia7v18WafrTp/3ShwImfftyrRaZKcKlVsX5AKoNXPz51AayLzkceeSTfpsffr7Oobzvd5NY2oUcyAQplAARLlmvZ+8vH+R+jRo3KNqzWl5ZBfs6f6Ka+gnEq+6eb6sFtKq/OP//8bD8S/D/2lVERvEEk2v/Uglg3w5QhosxT/XDWzQL9r35A/ZLtc1GZGbrxr2CUbvbrR5uCZApo6eZbcLmnok9H3fDTNqQMP/U9+L///c/daAl+l37kKVMm1npT8E7j0I9A7TPqdyteME7ZDjrOqIW1biJonSswq/WuY0vwxkd+9WMZpPWrxhI63irTUg0Fgt0P9O3bN3xDOhXH2Uj7gYKxChZq/Wi/142oVJdD85di9KjSgfZ9rYfRo0fn6H+7MOg45x3zIj2WLVsW8XPKDPEHDvz9ykVrBKObJMFMbp1bddzX53Uu0LrIa/+8kc6PCiwp6KZtT9UsevbsGTHLzC+47Wh7GTZsmPurbe/66693j1g6d+7sbiIGg9faj5XBp+OctgdtF2oMlGwjqWBjJGWrqXyhjnU63qhUqebVT+vgvPPOS+p7SqLg+v/4449d1qK2VR1/dBzS8bQgKDvNnwnsXT+q1L8a3Oj4rusXXT8psJxMtSCds4P7q66HVQVG2W7a3vXQtahucuv4qpvMY8aMydM8peJclwyd5/xZfspC8wfmIh2zElWnTh13LAvStbtuuGq/ViMorScdg+ItO2VaBauraNoVaNC5U8cN/UbQzX1dI+n6Qsca//l02rRp7tpKxz0tWy+bS9uGlu+dd94Z8zoguH50LtC5Vp/3zg95OVbpWljBJ5XT17xoXzr33HNzNHDSNUFBUwO94HFT+79+V+n8oZvm6otYVYZi0e+YY489NttruubTZ3Udpm1dx2ntZ7feequ7VlVAQd+VH0499dSIAcRg5YR4FLT2N4pR8EzbuQLBasCh7VPbuc5r+l2gwJSOJ/6Alvo5VwMjHcfUsFjHL31u1qxZ7rosGEDxb5/BLplUmcrblrSNq0GKKtEUxDaZn9fb6bb9pLNUH1+C3YP5f8/q2KCqQUH6Heuviqbzi37DaN3omkHbt85pCtDrd6H2RTW48ydEKBM2mO2vfeWUU05x61zbls4lOqarlHtwXSdz3NZ+HOw6RFULNW5tvzpPaL/2V34TXXek6jyT7G8p/WbRetW1gs539957b477BIV5D0rHLy1XXRPofKFlovOFlofWm463kSpD+gOzqbrHmN/3xXSs0Xz6tzWvypZ+h3j8//u7DlQg3v9epGkoqN/sqTinAUBUIQBIQNeuXUMnn3xyQsOedtppbvhk6ZCU7CMzMzN02223RRzf77//HqpVq1bC46patWpo1qxZ2caxdevW0GGHHZbQ59u1a5fts9u3bw/tsssuMT+TkZGR47VPPvkk23iGDBmS7f3GjRvnmNezzjor2zCRlr9e8w+jz/ht2bIldNBBByW1/IPfo2kPDjNv3rwc06J58A+jeQw68cQTY353pM9Ec9xxx2X7bMuWLWMOv2nTplDFihWzfebKK69M6Xxu3LgxVLt27ZjzWKlSpVDlypVjzvfzzz+f43OxBLcV73HNNddEHH7mzJlJbRPB7Sredte+ffuEx61lsWbNmlAygvtPoo8WLVq4Y0jQxRdfHPez9evXj7nPxtu2/Y/SpUuH5syZk/J9LNJ2o++KNS3VqlULLVq0KOXH2TZt2sT8TKlSpXLsK8HtKNHl4nf66acntPzjHVuT3QejiTQP8R46tkXTvXv3iJ/RsW3t2rURP9OjR4+kt+/gMtFyj3de++eff3IcYxM5P/rX6TfffJP0tEaalh9++CHHcTba48EHH0zq+JaVlZXU/q7H7bffnqtzZiLXCvFEWnfBeYonkX0xkWuWeOMZO3ZsrtZ/cFrircNE9/Fnn3024XX8/fffZ/ts8H19n9+qVavcOSmZ7Sg4H8kep1JxrkuWzie6rg9+T/ny5d0yyCv9Xoh0XEnkEVwn3377bdxjWKxxvPvuu0l9NrjPJ3JtlswxZPPmzaH9998/qWkaNWpUjmUcb7kluv/Ho+uIcuXKxZy+SNc0weO/jgd169bN07pIRKRr70jXKI8//niO4S666KKkj/WRxhPv4Z8e/eZJ5rMff/xxtm2pVatWMYfXfl62bNl83ybzcr2diFRsP4msz0SuqxIRPN/pkRvJ7sOpOr54duzYEdp9990jfm7ChAlRP6fjrn5TJDMdweX8/vvvx/295D3efPPNPB23H3744aSmtVu3bu7+k39+87JtxqN7Gc2aNYs73uAyD35PKu5BJULLJ5nv0KNv3745xpOK376JXn/m5b5Ynz59Ig77yiuvhIeZMmVKxGG0v0ZSWL/Z83pOA4BoyBgHUGSp1aVaGgczOj1q6auM1kT6ClXZHbX0DbbkVGtJZXwEW78nQtl6yuqJVi5N5Z9S0Tdjqqi8l7IrTj755IQ/k2y/d8lQ9nQqSs0pUzzYh1i8TFC10PaXafNaA/v70cortXxVmcdo86j3lbWq0uKppAypSHLbd6efMqjUOjw/aHmoJXJ+9OMWXPfKbFDL42DmjqgP21gtvnWsULZIKugYosyaYJcO+UUtrYNl/jzKhlTL/WCmRCqOsyqzF60cnpaBqifkpZV9NBpvrLJ2yphP5niYbqLt08qGiLa8tb3F6gPvxhtvTKjfv3iUCaTvilZ2Xlk9kTI8/fbdd1/Xp3g0yuoIlpiMdtzSNqwuLFJN86f9KpgJFYkyxe+66y4bPHhwyqejOFL2l7K0Yq3XgsoY9/a3/DpHqZqDMs+CZS1jbXfKmMmLgjzX+c8nka7PlKWnZZBX+r2g68Fkz6mqVqV+oP1UaULnM2XVJpr5HynbKhFa78FjnbLBglnTeaHpU5WMYAZsJCpjq+oMykIsLNo2dXyPdg2tzMd43XF41yXK8g+u32h0TZKf/cQqMy44fv1WULnoZCh7UZ8LVrKIRhm2ue16QRmh/hLT2pbUt22034hahsqm9Gczep8rrG0yt9fb6bb9pKtUr0t163XxxRfneF3nPVUziUbnF93XiZRRHomulYNd/+gaWJXo/NnniUr2uK2KDarmlUhJaF0PqSpCrL6akz3PxKNjhiogRep327sWUSWbeNcjBXUPKtl7KtpeI51HUvHbtyDui0W7ZvRngquyQaTtK9pnC+s3e0Ge0wCULATGAaQ1XVDrYlkXsvqxqlKmKm+li1GVQ9LzeD9AVKJLNyt1Y00X0Qp86QJQF/H6gaQLXpWG1k32aDdX9ANN5bVUukt9p3klZ2vWrOm+QzcH1bdpkH6cqYyVLqw1D5oXXSCrzJVKDqUiwJBKuqGrYKzKQGkadWNZPwh1oasL0T322MP9oFPw0yuZlF90oT516lQXnFKZ/Nz+0NONs2A/ZomUSA6WyVL5bZUGSyVtFyo/pXJPumGqHzwKPKo0mQKzqS6lLuoPLNjvrZ43b9484vD60afSawrYaN17pbm0/esHh/pg0+v6oaSSXsne+FXJUn1WZVJVZlDBLI1Xy0I/alR+VIEilSRU2fVU0fRrm9by1g1u3VDQTTGVrHzsscei/vDSfqx1phLNCqRpn9Y+omWo/UHHinh9TOt7tF1qH1PXDLqxruOMpknHFk2PjnMqlRcsP5ifdENCZSa1/en4qHnTX92o1XE0Un/MqTjOar3re9XHrIbXutc+r3Fp20t18MWjZf7RRx+58tvaL7Td6RiobU79X6ohQLC0eFESrZxkrEYw2hZ1rtMNR+3b3n7o3QhUyclU0XSoXKIaIWm5a3vRcUg35nR+SaQ/YR2XVPpcDRx0o0bbna4VVDZVZRETDVppn1O5RpWMVcMBfU7HAO0DuqGnm0jq7iE3gShNk7Z/dcmhfV6ltNWXsrYtHU+0/WueVQow2ZuSJZmOs1pfaoDo9cOtdabjkfr81HE6FcHUZOjYqZKWatSh6z8FQLRda9q0TemcruNNkyZNkh63xqXjlUqm65jcsmVLt9/o2kh/9VzbrsqLqkzt7bffnqd5ScW5LjeCJXLzWkY9SH2rqiyrbsLr/KqAlnf9pX1V/+s8pXWpxqvqTkblUSMFvnT+1nlNy0INNXTM1Lr29m0dVzTtOt///fff2a7p9PtFN8tVhlTbitafjrVanxqHrre1PnV803CRbvCq5K8aMOiYEuyHPje0vyigqUYY2sZ0PNZxVfOjadNxUOcAbV+5aTCcaur+ROcrlSDXNYO2UV1/aJnr+inYL2k0mk9dc+ucr9LNWva6PtC60Davrj+0Pei6QH0Sq1RrftHxQtd/wa7F7r777lydY3U80nlS51kF8jR+LSf9r/Wpkr86D2u+/F2SKditvsH1vobTMvEfb7RMtBx0jIj0+1fHOJVS1rlY/+s79dtB13Xan7Q8g/1/BwPlqdgmC+J6O522n3SW6uOLtu9g6W91I6SgeSz6TaLjugLOOibrd6CO/ToH6Piv7USlvbU+ddyOdOzXNbG6/VIDD10X6rijY7TGo9+xOr6rTLl+T+T1uK1p0XfpGlSNtHTvSctMy07nDTW81D6l7g2CyyMV55l4dJ9AvwF1rFDAWPu6vkPLWffrdAxIl3tQumbU/UNdJ6nhhfZ/rXMdF/U9ujbXetG2pWstba/R1lEq7jHm932xSMFtHQP9jdw1zbqGTuSzhfmbPa/nNACIJkNp41HfBYD/6OaCLjh0wRqPfvwpuKS+gACkH900VDDao/91kwIAAKCwLFy40AUWPGpMqiBGfgThARQOVVJRH79+CljlR6Yhii8FSxU0Fp0j1Md3pGpfAAAAkZAxDgBACfLFF19ka+CiFvxFuVw0AAAo+jZt2pSjaoKyKwmKA0WLSt4qA1uN5LVfe9avX28vvPBCjjLYHTt2JCiOhCm3S9UAvKC4l8VNUBwAACSj6NaHBAAACVE5OPVNpRtSKgvnpxJnqSjBCQAAkJuqVAsWLHDXJ/4gmkqcqqQqgKJl3bp1risCPVTaWl0LqIHLypUrLSsrK9uwKgmt4YB41HBKJf5XrFhha9asCb+ubUulxgEAAJJBxjiApPoC1g+PeA/15QUgfaxdu9a1qg8GxdUv71VXXVVo0wUAAEo29Rmphz8orj5Un3/+edfvKICiS4FwBTKXL1+eIyjerFkz17dxpH6cgSD9jtXvWX9QXK699lrX/zYAAEAyyBgHkBBllarv8ETVr18/X6cHQO5UrFjRmjRp4vr2UynDMmXKFPYkAQCAEk6Naxs0aGAdOnSwG264wfbZZ5/CniQAudC7d2/3d8qUKfbzzz+7oLiCmZUqVbJ69epZ+/bt7bjjjrNevXpZ2bJlC3tyUQTVrFnTld+//PLL7YQTTijsyQEAAEVQRkgdtAAAAAAAAAAAAABIiX79+tnIkSPd/5988onrSghA4aKUOgAAAAAAAAAAAJCARYsW2TXXXGPt2rVzXQCpQmPLli3trLPOso8++qjQpuvmm292j2HDhllhmjBhgh1xxBFWo0YNVyWmTp06blmpocB7771XqNMGUEodAAAAAAAAAAAAxdasWbNs7733jtqdx9atW11XIOqCMJbXX3/dBcA3bNiQ7fVffvnFPd5++21bvXq1FYZbbrnF/W3cuLHrdqIwKENeAXA/da+ix48//milS5e2o446qlCmDRAC4wAAAAAAAAAAACi21Ktwhw4dbNq0aRHf79Spkxsmli+++MJOPfVU27Ztm3uu8V100UW288472+LFi23cuHE2adIkK87UIEAZ8tEMHjzY/c3MzHT/d+nSxX3mt99+s/fff9+9jv+3ceNGq1ChQmFPRonCFggAAAAAAAAAAADEcOWVV4aD4p07d3ZB9jPPPNMOOeQQO+200+yVV16xTz/9NOY45s+fbxkZGe4R7HN81113Db/n9+STT9q+++5rlSpVsnLlylnDhg3t8MMPt3vuuce9r/Lp/s8sWLAgPB6N06Npf+CBB6x9+/YuuK1Hx44dbdSoUTmm0//5mTNnutLo+v5jjjkm6rz9/fff9tdff7n/99prL7v11lvd53r16mVXXXWVazTgL/Oe7LKYPHly+DVlpb/22muuhL0CywrAazqzsrLc92oZ6fXu3bu75RFt3HqvR48eblko0/6xxx4Lf9d+++1n5cuXt2bNmtmrr76abRw//fSTW+etWrVyJePLlCnjSsZr+UydOjXbsCNGjAh/n9bVE088Yc2bN3efefnll8Pvde3aNdvnVq1a5TLs9V7btm2jLnckh4xxAAAAAAAAAAAAIIqFCxe6jHHPnXfe6QKbQQrUptKLL75oAwcOzPaastP1UOl29XWeCAXFFSQO9oH+9ddf2xlnnOGCynfffXeOz6ksvAL/K1eujPsdCpwriKvMe5VNv/fee+3444+3PfbYIzxMqrKjFXx+4YUXwln+aqRw5JFH2rHHHmtPP/10eDj1aa4AdrRKAYcddpj9/vvv7v8///zTVQDQulYDApXXl7lz57pKAeonXQFtLzCuoLafysWrf3V954cffuiWW6T1+ccff4SfK/CtgPiUKVNcowpNwy677OLeGz9+vO3YscP937dv3zwvM/yLjHEAAAAAAAAAAAAgihkzZoT/L1WqlO2///4F8r3qs9wLoCrTWIHtl156yWWv77bbbu69s88+O1umer169dxzPcaOHetee+ihh8JBcZWNf/PNN917XqBX2edfffVVju9fs2aNm9+nnnrKlUI/99xzo06rsq41btm+fbsL2jdt2tRlUp988skuYJwq8+bNc1njCh572dRLly51QfHrrrvOzV/dunXd65999pnrYz4SzZuGveyyy8Kv3XXXXS5b/N1337UTTjjBvaZM9GeeeSY8jJbb/fffb2+99ZZ9/PHHbtk+/vjjLqNfw6rhRCQKinfr1s19TlnorVu3tnPOOce9pyC/qg543nnnnfD/p5xySh6XGDxkjAMAAAAAAAAAAABRKEDsqVWrVsRs8fzgfU/ZsmVd5rVKqlepUiVbBrEyjL0sY1Fw9sADD8w2Hn+59CuuuMLNgyib+qabbgoPo9LqQXpdJdETocD0cccdF87C9jKpFQTWQ9+tgHJeqV93BarVZ/ns2bPt6quvdq+rpPrQoUPd/8rafvTRR93/6uNcQeig4cOHu3nT8lLjAc/IkSOtSZMmrpHBG2+8ER6HZ88993RZ63fccYfL3F+/fn22Puq//fbbiNOtcu3qi14NHTxt2rSxSy65xG1javQwaNAgl62uhgiixgZeIwjkHRnjAAAAAAAAAAAAQBRVq1YN/79ixYpwX+P5rX///q48+caNG12/4poOBYVPP/30qMHXSObMmRP+v0+fPi6ArIcXFJeff/45x+fUx3aiQXFR8Fll1FVmXFni9evXz/b+gw8+6ALJeaV+0hUUF/Xx7VHDAY8X/PdKwkfSoUOHHOOoXr26C4rHGocC/Jdffrl98803tm7dumxB8Vjfd9RRR2UListOO+3kSrWLStrroT7ONV7x3kNqEBgHAAAAAAAAAAAAolD/0h71+/zll1/majwKcvvH46eAe5D6zVYp8AEDBtjee+/t+uhetGiRyyxW39T+/qrzasOGDTleUxn0ZGkaFcwdPXq06wtd0++VNVcA+YcffsjVsojWUMELkIuy6SMJBq6D40lmHMrmVml5UZBbpdc/+eQTV7reC6RH+z5vOQR55dS9DH2vjLpKvauBAVKHwDgAAAAAAAAAAAAQhbK0O3fuHH6ufqwjZY1HyrqOFtBVn9ieadOmRQxMK8Cq71Ug9rvvvnNZxF4pcmWR+/vt9gLN6uM6qFmzZuH/FUzXeIMPrw9yP3/wOh59b6R+xNUfu79Pdi8InuyySBcrV660zZs3hxtMqPT5wQcfbLvvvrutWrUq5mejLU9luqs8u6ifcfVvLoccckjUYDpyhz7GAQAAAAAAAAAAgBgUkFaWtgLiyoJWKfKLLrrIGjVqZEuWLHHBzEmTJsXMdq5WrZrVrFnTBVfVZ/XAgQOtefPmdt9990Uc/tJLL3XjVjlzBeeVoazMZM+WLVuylQBXYFZZ2sooV3/WCqo2bdrU9SU+Y8YMN1yPHj3smmuuCU+3Spu//fbbduWVV1q/fv1yvXwUGO/evbvrM/ukk05yGe4VK1Z0Jd8nTJgQHm6//fbL1bJIF1qmKjGv4LjKnqvRgl677bbbIjZKSJSyxi+77DJbuHBh+DXKqKcegXEAAAAAAAAAAAAgBmVuK5v3rLPOchnNX331lXv4+bOgoznvvPPszjvvdP8/+eST7q/64lagONg39aZNm+z11193jyD1TX3ccceFnyu7WMMpI1t9kIumdcSIES7g+v7777us8NmzZ+cpAB7PTz/95B6R6Hv92evJLIt0obLrCmI/+uijrqz6+eef715XAwSVnl+2bFmuxqt1pgYLXmOHsmXL2gknnJDSaQel1AEAAAAAAAAAAIC4TjzxRJdhffXVV1vbtm2tUqVKLkC9xx57WN++fW3s2LFxx3HTTTe5gLCCv8qoVnBbGeiRgurK9FZwW5nUel99Tiv42qtXL5c5rvLdnuHDh1ufPn2sdu3aOcajIKvKnD/88MPWoUMHq1y5sst63m233eyYY46xZ5991o4//vg8LRtlsyszXEF4lQZXgLtMmTLuuzp27Oim75lnnsn1skgnymq//PLL3TxqG+jZs6drdKBtIbdq1Kjh1qtH2fdaLkitjFC0HuABAAAAAAAAAACAIk4ZzCrVrf6rI+nUqZONGjXKBbiBwvLCCy+4hhAyZswY19ABqUUpdQAAAAAAAAAAAAAoBBs3bnR9rT///PPuuTLFjz322MKerGKJwDgAAAAAAAAAAACKtS+//DJqaer169cX+PQAnlatWtmCBQvCz1WqPy9l2REdgXEAAAAAAAAAAAAUW23atLHt27cX9mQAManP8rPPPtuuvfbawp6UYos+xgEAAAAAAAAAAAAAxVpmYU8AAAAASqabb77ZMjIy3GPEiBGFPTlpR+1X27Zt65bPgAEDLJ2p3Ffp0qXdtL722muFPTkAAAAAAABADgTGAQBAkXTwwQeHg6p6lC1b1mrXrm377LOPXXTRRfbTTz8V9iQWWatXr3ZBaz1yE7BetWqV3XrrrdahQwerXr266xOpadOmdtJJJ9lbb73lAr6FYdiwYeH5KgrGjBkT3o4vv/zy8OtaJ/5t33tUqlTJbf/33Xefbdu2Lcf4tNxff/1169mzpyvNVa5cOWvQoIF16dLF7r//flu+fHmOzyxevNhKlSoV/g71xbZly5YcwzVu3NhOOOEE9/8tt9xiWVlZKV4aAAAAAAAAQN7QxzgAAChws2bNsr333tsFsyPZunWr/fzzz9akSZOEx6lA4IoVK9zj+++/t8cff9xuvPFGF6RD8oFxb7l17drV+vXrl/BnP/30UzvxxBNzBFl/++039xg7dqz9888/LsBaGIFxZTZLUQiOK8AtnTp1statW8cdfsOGDW7b1+P99993j8zMf9vBrl271jVM+OCDD7J9ZsmSJe4xbdo0FwD3B+C94Lw/yL1mzRqbOHGi9erVK8f3n3vuuS5bXPv3hAkTrEePHrmedwAAAAAAACDVyBgHAAAFTpmryiZev359xIeyXpPJKr7++uttypQp9vLLL4cDdvq8spYVDEXB+P333+3YY48NB8WbN29uTz75pH388cf26quvusBp+fLlrThTcDoVZs6cadOnT3f/q6FBNHvttZdrjKBlrP3A8+GHH9obb7wRft63b99wUFzr4Oqrr7b33nvPPe644w7bfffdI47/lVdeyfHa6NGjIw57yCGHuAoBQml8AAAAAAAApBsC4wAAoMhTme6DDjrITj31VHvzzTftyiuvDL930003uQxoP2UtK4inrGWVk1ZQ8OKLL3aZs5HKgl933XXWqlUrq1ChglWpUsUF7ocPHx4eZtdddw2XmvZTprX3+uTJk8Ove6/pcz/++KObdo27RYsWbtq8aVSWsKavXbt2LvAZpAD0FVdc4eZfwykoecwxx9iXX36ZbTh9t/edmiZlEu+3334uQLrLLrvYww8/nG2ad9ttt/BzNTjwPqvy9bEoQ18ZxaJl+vXXX9t5553nlrWylZ9++mmXTax5jcW/fKKVz58/f374dZUHP/DAA61q1aquCkG9evXc80GDBrkGEl7pcS9b3P8d/nWmYZ9//nk74IAD3HpWCXgt+4ceeihHaXD/Ov/zzz9d8Frf36ZNG/f+ypUrbeDAga7EuKapcuXK1qxZM7eNapnGo+3Yc+SRR0YdTt+pedUyVoDbv44UMBcFxMePHx9+XVnd99xzj3Xr1s09FFBXhQY1agg2dPjmm2/c/1omO++8s/v/3XffjdgAoEyZMq7CgOj7VPkBAAAAAAAASBcExgEAQLGjMtle5uq6dets3Lhx4fcULFWQVsFiBXEVvJs3b549+uijLuCt/z0LFy50Jd/vuusuFzjctGmTG59KVXsB7LzQ9x966KEugKlx//rrr9anTx8XYNY0zp49202fgufKhFcJco+CsZreBx980JUo13BqAKAS1gq0v/POOxG/U/N99NFH27fffuv6itY8XnbZZS7DOC80LvUf7m+QoOBykALm0Uro54aCzFpmn332mSsXrpL6f//9t3uu4O+OHTsSHpcaBZx99tn2+eefu/W8efNmt+xVXlwZ19EoKK3sbH2/V+lA06Rsea0nTZMqIcydO9dlW0+aNCnutGj6RY0X1CgjUQqUe7zAtMqhexQ4j1TiXOsk2HWBPzNc8+Nlrm/cuDHq9qVtUrTsvvvuu4SnGwAAAAAAAMhvBMYBAECxU6lSpXDmrvzwww/u71dffeWCpV7AUX04K8CnwKYsXbrULrzwwvDn9L8Cm6LM6qeeesqVntY4vOzZvFAgW9nemoZTTjnFvabA6u23327HHXecC+grG1gUqFWpeP+0LVq0yP1/5plnuulSv+qadwViFeCNlNWrrGllBivr1/tOURBXBg8e7DKKg6W69XjkkUeizouCvgrue7p06WIFQfPhZXMPHTrUPvroIxfQveGGG1xAWRndagig6VcmucebJy+rWg0dXnjhhXAJeJUQ17jVv7cXXPYHmP0UiH/ggQdcZrayr7WuPvnkE/eeGlZo/apf7ieeeMIFlytWrBh3vtQQQ5RxXrp06bjDb9++3VUC0Hbgadu2rfs7Y8aMXK0Xr4y6lqGmu3fv3nHLqe+xxx7h/9WwAwAAAAAAAEgX8e+yAQAAFEH169cP/++V9/YHli+66KJwyfXOnTtbo0aNXNazgosqny7KvpZSpUq5gGPLli3dc5WfThUFYxUc1/R6wUaVGn/xxRdd+W0Fm6dNm+ZeV2a4aPq8aVOwd8CAAe5/NQY44ogjXBlulfLWNAf7p65Tp44L8Kr0usqpe9/pjVvTopLYwVLd8XjL2NOgQQMrCP5p1bSr9HnNmjXt5JNPtttuuy08z3ponj3BeRo1alS2bUPbg5xzzjnh0vQaRuMNUta+tw5E60zBZDVyqFWrlgsWa9oU4D7//PMTmq8VK1a4v17lg2i8UvdBashxxhln5Fg3ia6Xn376yZW9FzUOaNiwofusHosXL3bblhp2qDsCP//0evMAAAAAAAAApAMyxgEAQLH0119/5SgvPWfOnPBrHTt2DP+v4KVKfIuCmQoS6+FlIus9LyieSgoqKmAqNWrUCL+ujGUFxb1p83h9pWvavJLdynJXFrD38PdN7WUd+ynI6QWIFUAOjju3/CW8RcHTgnDaaaeF50fl57W86tatayeccEJS5eH928all14aXp7+gHek5SnBvrnVN7n6EheVTff6p1f2uErMBxsRxOKt50RlZma6UukKmHvbkH/dJLpevGxx8TLFvcxxr0y7ysfndXoBAAAAAACAgkJgHAAAFDvq61kZr/5y4PFEyrpNlP+z/j6t42XM+gOWCmh6IvXNnZugY6RS6v6MXn+J7rwGNBXgV0A42Ed2XgT7B4+0PJUlP336dBfMVmMHLdNly5a5BgLK7Fd/4akSaXmKAvFBzz//vCtP37NnT9d3t+ZFJf2VxR4p6zzIaxDh71c+Eq/UvaoKqE9vNXBQCfhdd901PIyy6JNdL/6y8aqsoG1cD385/Ujl1P3T62/UAQAAAAAAABQ2AuMAAKDY8Wflqs/tY445xv3frFmz8DBff/11+H+VHf/999/d/wr+qfS1Hl6w+o8//rBffvkloQC3MrhF/UynIjgciabNC8Yr6Kr+pRXY9j+U0Xvrrbfmavz+IL2XNR+PsrZ79eoVfn7LLbe4ZRCkZalpi8Vbnlov6i9d5s+fH3EdaF5bt25tDz30kCt5rsCw+gv3pv2tt95KaL7824b6Bw8uTz28bSSRRhVqdHDeeefZ22+/7TL8FTDef//93XvqizxakN3jVShQn/Bav9F4pe4POOAAl5HuZYn7+QPxH3/8sevvPEjrxJs/7RvR5tVP41IjBD+vJL8oUx4AAAAAAABIF/QxDgAAiry5c+fa1KlTXZlolYB+5513sgVovSxplbd++OGH3f/Dhw93/SUr03nYsGGuf3FRlrFX1rx79+42fvx4l+2r/2+44QbbeeedXd/Lys5VP+BeoHrGjBnu/zPPPNOVm9Z7eS1PHo2mT9OjfsYVwFRWsvrCVlBUgdTvv//elbn+4osvsmUOJ8qfVT5z5kwXXFb2r/qt1iMaZUNrmtQoQdPVoUMHu+KKK9zyUZBb/bern+4lS5ZY2bJlo45HwysLXH119+3b1w466CB77LHHcmSQyz333GOTJ092jR80bRUrVnTf4/HWqzdf8+bNc/8r87l9+/YusNy2bVtXkl1BbFHf3IMHD3bbxvLly932pe1Ay3zIkCEJLUM1WNB2oGxtbWcKIHvfrSC7pkvTGo0C3Qqgazhtb/6s72QdeeSRbvloHkTTpQz7Qw891E2LtuVnnnnGLrnkErv88suzlVFXUD3YH7u2h48++sitj9dee831ye7Rtifly5e3ffbZJ9fTDAAAAAAAAKRcCAAAoIDNnDkzdMABB0R9v2PHjqG5c+fGHEfXrl1V+zvqIyMjI3TjjTfm+Nw111wT9TP16tUL/fHHH+FhFyxYEGrUqFHEYfX9nvfffz/H+6VLlw7tscce4eeffPJJeHjvtcaNG4dfmzdvXsRx63Pe62eddVZC0+Y9NM5Y44g2LdK+ffsc4xsyZEgonqlTp4Zq164dc7r++ecfN6zG5732/PPPh8fx5JNP5vhMpUqVss2vN2+33XZb1O/JzMwMTZs2LTzeK6+8MuZ6PPPMM2NOt3/+tby81yMpVapU1PF069YtoX3EG/6+++7L9p6WVaTpj2XNmjWhI488Mub8Pfjgg6EdO3aEGjRoEH5N0xH01ltvhd8/8MADw69v3bo1VL16dfd67969E5ouAAAAAAAAoKBQSh0AABQLKl2tTGpl1p5//vkuczVSKfG7777bXn31Vevatavry7tMmTIuq1pZr8qc3W233cLDKgNZ47nmmmusRYsWLgtWpdnVr3Pv3r2zZeQq67xRo0aupLgypZW1rKzf/OJN29VXXx2eNmWM639lrStrXtntuaWs4aOOOipb9ngiunTpYj///LPL1N93331dRraWiZarSq2//vrr2UrPR3LuuefaddddZ3Xq1HH9liuzWf1oKws76Oijj3brW32Na1pLlSrltgOtk+A6ULa3ypsrgztS+fORI0faCy+84LYNTaOy2rWcDzvsMFdp4MILL0x4OQwdOtRVH/C2CT2aN2/u1peyrOPR/Gj5ibL/80rb+nvvvee+u0ePHlavXj237WsZd+rUye0Xypr3Ki+I1pmmI+iII45w25uou4CFCxeGS9B7fYz369cvz9MMAAAAAAAApFKGouMpHSMAAEAcP/30kw0cONCmTZsW8X0F6lRyWyW1gZJqzJgxdsopp7j/VU493fvs7tOnjwu8q8/3H3/8MVuf7gAAAAAAAEBh424VAAAAkKaBZi9j+8EHH7R0pr7tvcz2m2++maA4AAAAAAAA0k7pwp4AAABQMn355ZdWrVq1iO+tX7++wKcHSDcq9z5z5kwrCho3bmzbt28v7MkAAAAAAAAAoqKUOgAAAAAAAAAAAACgWKPGIQAAAAAAAAAAAACgWCMwDgAAAAAAAAAAAAAo1giMAwAAAAAAAAAAAACKtdKFPQHpLisryxYvXmyVK1e2jIyMwp4cAAAAAAAAlFChUMjWrVtnDRo0sMxM8l0AAACAZBAYj0NB8Z133rmwJwMAAAAAAABwFi5caI0aNSrsyQAAAACKFALjcShT3PvBUaVKFSuJGfPLly+32rVr0xIZSDPsn0B6Yx8F0hf7J5De2EeB6NauXesSOLz7VQAAAACKcWD80UcftXvvvdeWLl1q7dq1s0ceecQ6dOgQdfjVq1fb4MGD7Y033rBVq1ZZ48aNbdiwYXb00Ucn9H1e+XQFxUtqYHzz5s1u3rkhAaQX9k8gvbGPAumL/RNIb+yjQHx09wcAAAAU88D4mDFj7IorrrAnnnjCOnbs6ALc3bp1s19//dXq1KmTY/itW7faEUcc4d4bO3asNWzY0BYsWGDVqlUrlOkHAAAAAAAAAAAAABS8IhUYf+CBB2zAgAHWv39/91wB8vHjx9tzzz1n1157bY7h9bqyxD///HMrU6aMe23XXXct8OkGAAAAAAAAAAAAABSeIlOTTNnf06dPt8MPPzz8mkqq6fkXX3wR8TPvvPOOde7c2S666CKrW7eutWnTxoYOHWo7duwowCkHAAAAAAAAAAAAABSmIpMxvmLFChfQVoDbT89/+eWXiJ/5448/7OOPP7bTTjvNJkyYYL/99ptdeOGFtm3bNhsyZEjEz2zZssU9PGvXrg33caZHSaN5DoVCJXLegXTH/gmkN/ZRIH2xfwLpjX0UiI794v/pOLF9+3YSYACUCKqIW6pUqcKeDAAo8opMYDy3PxbUv/hTTz3lThrt27e3v/76y+69996ogfE777zTbrnllhyvL1++3DZv3mwljZbhmjVr3I8NZegDSB/sn0B6Yx8F0hf7J5De2EeB6NatW1fYk5A2lSWXLFliGzduLOxJAYACkZGRYY0aNbJKlSoV9qQAQJFWZALjtWrVcsHtv//+O9vrel6vXr2In6lfv36OllQtW7a0pUuXugvosmXL5vjMddddZ1dccUW2jPGdd97ZateubVWqVLGSeENCJ13NPzckgPTC/gmkN/ZRIH2xfwLpjX0UiK58+fJW0ukYMW/ePHe/r0GDBu7+no4ZAFBcqbGgEvcWLVpkTZs2JXMcAEpCYFwXucr4/uijj6xXr17hC2E9v/jiiyN+5oADDrCXX37ZDef9mJ4zZ44LmEcKiku5cuXcI0ifL6k/yPXjoiTPP5DO2D+B9MY+CqQv9k8gvbGPApGxT/ybLa57fUpkqVChQmFPDgAUCDUYnD9/vusmlsA4AORekbqaVib3008/bSNHjrSff/7ZLrjgAtuwYYP179/fvX/mmWe6jG+P3l+1apVddtllLiA+fvx4Gzp0qF100UWFOBcAAAAAAAAA8oJGAgBKEipjAEAJyxiXk08+2ZUMuemmm1w59L322svee+89q1u3rnv/zz//zHZRrJaj77//vv3vf/+zPffc0xo2bOiC5IMGDSrEuQAAAAAAAAAAAAAAFKQiFRgXlU2PVjp98uTJOV7r3LmzffnllwUwZQAAAAAAAADS3Zqtm23JxvW2LSvLymRmWv0Klaxq2ZLdf3v37t3t2GOPtQsvvLBAvk9VPWfOnGmvvPJKgXxfSaCksVatWtlff/1lVatWzdO4+vXrZ9WqVbNhw4ZZYfrhhx9s7733dn1sAwCQCtQcAgAAAAAAAFDsrd+21SYvmW/j/pxrXyxbZNNXLnF/9XzKkvnu/VSaNm2aCzhXr17dBRnbtWtn99xzj+snXRYvXmxHH320VaxY0XbZZRfXhWRBUNDz8ssvz/baxIkT8y0oPmLECFf50+/6668nKJ4g9SutMtqrV6+OOZy2ofXr1+c5KA4AQHFGYBwAAAAAAABAsaag90eL59kf61ZbqYwMq1a2XPih57+vW+3eT1VwfNy4cS4o3q1bN5s7d64Lao4ZM8Zmz55tS5YsccOceuqpVq9ePVu2bJm99tprdvXVV9uUKVNS8v3IH8pc3rFjh6Wbbdu2FfYkAABQJBAYBwAAAAAAAFCsfbtisa3csskFwsuXLu0ycEV/9Vyv6/3pK/4NWuc1eHrppZfaoEGDXGZ2rVq13OstWrRw2dONGze233//3WWU33nnnS5jvGPHjnbaaafZc889l/D3KDtYXU4qU7hOnTp25pln2po1a9x7W7ZssbPPPtt9tzKI27RpY9988409/PDD9tJLL9ljjz1mlSpVstatW7vhDz744HDZbHVXqQx3DdOwYUOX8a73fvnlFzedVapUsV69etmGDRvC03L66adbgwYN3Hvt27e3Tz75xL3+/fff28CBA13ZdH2fHir5ffPNN7txeH777TfXiKBGjRrWpEmTbCW8vYzz2267zc1n3bp1kyrxre/q0aOHnXPOOW76mjZtam+++Wb4/Q8++MD23Xdft5zq16/vMuc3bdoUfn/XXXd166lTp05WoUIF17hh1KhRbplWrlzZLf8bb7wxW7lvbVfDhw93pc21fs844wz7559/7OSTT3bToPLgWp6JrMsOHTq4v40aNXLLT+vPW0ePP/64+8z++++fI7M8KyvLrW9td5pOzfd7772X8HJbu3atW0f6zj333NNtr55Y86+/2vbV6EPz2qxZM9dQxDN69Gg3Pk3/fvvtZ59//nn4PU17nz593Hua7qlTpyY8vQAAJILAOAAAAAAAAIBi3af4XxvWWYVSpS3zv4B4kF7X+4s2rnXD54UyxOfNm+cywqP58ccfXRBWQV6Pgr963aNg7l133RV1HAp8r1q1yn1G36esYQVXZeTIkTZjxgwXcFaw8Y033nCBSgXsFYBX8FfB2FmzZkUc97p161ygVeNVNvtVV13lHmPHjrWFCxe68T755JPh4Q877DD7+eefbeXKlXbKKadY79693TgUAH7iiSesbdu27vv0UCDVb/v27W5eVWpe5eUVtFbJ+Zdffjk8jKZTQWn1n63Me2XXq3FBohQQVoBZy+uBBx5w68b7/E477eTK2Ou9zz77zAX1NYyfgvNappr+5s2bW82aNd0yVfD4nXfesaeeeirb9Mrbb7/tgsnaHhR879q1q11yySXue7Sur7nmmoTW5ddff+3+Llq0yH2/1p+3jrSOFWCPVGlAgXk1IFAgXdP50UcfuUYZidL8qDGBth9tLz179gwH3WPN/6RJk9z/3333nXv/ww8/dMFxmTBhgtuOtDw1v9ddd53r217bjWj71Hdo2/v444/thRdeSHh6AQBIBIFxAAAAAAAAAMXWko3rbWvWDitXqlTM4fT+1h073PB5sXz5cvdX2dbRKMCprFg/PVew06Ms22uvvTbqd7z++uv26KOPus8pK/nWW291QWOV+i5Tpowbl4LVyuBVYHLnnXdOaj5uueUWK1u2rB1++OEuk1sBTI1DmdXqG12BT0///v3d6/peBa2VrewP8sfy1VdfufLyt99+u5UvX95lEysorOCpR5nvV155pRu/stuVxf3DDz8kPC+a//PPP99Kly7t5uOQQw4J93HepUsXF8AvVaqU7b777m44ZWT7XXDBBS4grmG0TFQmX+NUhraC3Aq0Bz+jALCWmzLpFRRXdv6BBx7opuGkk04KL7946zIaLWM1nFCDAT2ClE2ubHll8Gs61SChZcuWCS+zQw891C0rTa+y/tWIw8v8jjX/WkebN292jRkU4Nf3eoFxzaO2j3322ccyMzPthBNOcJnhCphrXjXP2g60HLTcNCwAAKlEYBwAAABFTmj7dsuaM9v9BQAAAGLZlpXlAnhe+fRovGE0fF54pdOV3RyNylN7pbI9eq7S1IlQRq0Co7vttpsLInplqRVsXLp0qSvd3a9fPxfQ1PTo/xUrViQ8D5oOZVJ7FHj1Z7fruYL7oukYPHiwK9Wt0tmaFs1Lot+nTGgFQRVw9ihArdc9/u8WBY/9jQjiCWZK67m3flRiXsF/fYem//rrr88x7cEs9/fff9+VL/dK1SsrPviZ4PKKtvzirctY6yjYuMJvwYIFbp3kVqxlFmv+1ehAjSpUXl3vn3jiiS4L3ptXLV9vPvVQAweNV5/funVrtu9NJsMdAIBEEBgHAABAkROaM8uypk5yfwEAAIBYymRmuqxpfx/QkXjDaPi8UHasMprVl3I0yopW2fBly5aFX1OAUCXHE6HMbQVONQ6VnvYeytRVprqyfBWAVKltZY2rX28FK0WfSyWVzdZj/PjxLiCu6VCw1Fve8b5PfWdrPpRd7FEAVa+nioLEfloeXka/sp0VzP3jjz9c6e+hQ4fm2Fb886DgrTKdlVmugK7mWQ0Q4m1fuV2X0ZZfvOWqoLJK3qd6mSUy/yq9/uWXX7rPlCtXzpVI9+b1/vvvzzaf6qtelREURFe2uf979XkAAFKJwDgAAACKlNC2bRaa8a3Zmn/c39D2/7+BBgAAAATVr1DJymaWsi0xylKL3i9bqpQ1qJBY1nY0yjp/5JFHXJlr/fX6T54zZ47rs1mBvyZNmtgBBxzggtcbN250/UirL2i9nwj1F96rVy9XctzL1FV2sfrnFvXPrEC7+u9WdrVKlCtYLspcVhA4t4HcIAWTle2twKaCpioD7s/m1vepVPqmTZsifl59f2uYm266ybZs2WI//fSTW25nnXVWQt+vILqWuf5Go2WvfsS1PBTA1/I5+eSTw9PvlTBXIwKVII9F06igtfrZVtBXpeCD/YsnI966rF27tguCJ9OnuihwrcYQ2g60rhVk1vyJyp7Hq6CgZaRlpWWmZad1eMwxx8Sdf2Xgf/75525bUNUBLVdv27vooovs3nvvtenTp7tp0ravPshVHUBl6vv06eO2AwXM1VBAwwIAkEoExgEAAFCkhObOttCqFWY167i/oTmzC3uSAAAAkMaqli1vDStWto07tltWlGCwXtf7jSpUsSply+X5O3v06GETJ050gUUFwRV47d27t+tPuX79+m4Y9XGtjFsFPlVu+p577nF9UXvUj7Oyl6NRH9xe2W2VAFdf2Qo4yt9//+0yofW+SnQrg3vIkCHuvXPPPdd9r/q/VuZ6XimArf6zlaGsEugKhvqzvdVXdadOnVy2saYnmAWsLGH1Xa1pV5C4Z8+edsUVV1jfvn0T+n6NT98dq0/3o446ymUwa54vu+wyGzVqVLjM+JNPPmn33XefK2+vzOdTTjkl5vephLn6yj7vvPPccr/jjjvCQfbcirUutTy17rQ9aJhEg/DK0lbf6Ao2a5pVLt5b9vqrUuixaPkrIK7vfPjhh+3tt9+26tWrx51/NTRQxrgC51qfCnA/9NBD7j31Wa4GIwMGDHDj0rap91RKXtQgQutB61PbjboEAAAglTJCqWoaWEzpRK4LR5WE0Ym+pNFFiUo61alTJ+VllgDkDfsnkN7YR/MvWzxr7AsWWr3KMqrVCP/NPOlMyyhdprAnD0UE+yeQ3thHgehK+n0qUaaq+itWQE1Z0Ilav22rfbR4nq3csskqlCpt5UqVchmzujWqTHEFxWuW28kOa7CbVSrz/31dI/0pK1oBWGVIR3LzzTe7rOm33nqrwKctXfXv3981AOjWrVthTwry+dgHAMju3xomAAAAQFHKFq9a7d8XKlUJZ41ntGpX2JMHAACANKVgt4Le01cssUUb19rqrVvCgXGVT29Subq1r1WfoHgR5GXCI3HPP/98YU8CAACFgsA4AAAAilbf4pmZllHq38vYjNKlLZSZ+W9f481akTUOAACAqBT07lq/sa3ZutmWbFxv27KyrExmputTPBXl0wEAAJDeCIwDAACgaGaLe8gaBwAAQJJ9juuBkkGl1AEAAITOugAAAFAks8U9yho3L2t8+7ZCm0YAAAAAAAAA6YvAOAAAAIpOtnjlKpEH8GWNAwAAAAAAAEAQgXEAAAAU2WxxD1njAAAAAAAAAGKhj3EAAACktdC8uRZavcps+/Z/s8ajycpyw4Xm/WYZTVsW5CQCAAAAAAAASHMExgEAAJDWMurUs8wOB5qFEhnYLKN23QKYKgAAAAAAAABFCYFxAAAApLWMajUso33nwp4MAAAAFGmbzWxHAsOVMrPyVtRlZGTY999/b3vttZeVRLvuuqsNGzbMevXqVSjff95559nYsWOtbNmy9vXXX1urVq3sr7/+sqpVqxbK9AAAgH/RxzgAAAAAAACAYh4U/87Mpifw+O6/4fNu2rRp1r17d6tevbpVq1bN2rVrZ/fcc49t3bo1HDxt3ry5ZWZmuiBuSTZ58mS3jIoDrXcFxefNm2dLly61XXbZxdavXx8Oivfr188uv/zywp5MAABKJALjAAAAAAAAAIoxZYpv/+9WaOkYj8z/hkskszy2cePGuaB4t27dbO7cubZ69WobM2aMzZ4925YsWeKGUaD8sccesw4dOqRgHpEuFBBXMJzscAAA0g+BcQAAAAAAAAAlQCKB8bwLhUJ26aWX2qBBg1xmcK1atdzrLVq0sBEjRljjxo3d84suusgOO+wwK18+d6XblY18+umnW/369V229UEHHWSbNm0Kvz9lyhSXka73Tj75ZFuzZk34PX2uQYMGVqVKFWvfvr198sknSX33pEmTrGPHjm7c+v4777wz/N6oUaOsZcuW7r0DDzzQvvtOWfj/eumll6xp06ZWuXJla9iwod122222cuVK14hA01epUiX3+PTTT12A+fDDD3cB5ho1atgBBxxgGzduTHgaZ82aZfvss4+bRzVQWLx4cfi9ZcuW2WmnneamXctB62nLli3ZstefeeYZ23nnna1mzZp2zTXXZBv3hx9+6Bo0aLjWrVvbO++8415/+OGHbcCAATZz5kw3H8oOnz9/vittr8YRel/LQA0i9L4+G225AACA1CMwDgAAAAAAAAApogxxBXVPPfXUPI1nzz33tJdffjnie1lZWXbsscda6dKlXRb6ihUrbOjQoa4su+fFF190AW8FZv/5559s5bsVkP/5559dUPqUU06x3r1727p16xKaLvVdftxxx7lg8fLly+2XX36xQw45xL03depUu+CCC+zJJ59072m8Rx11lAt6b9iwwQWKn332WfddClzrPQWeJ06c6ALgKjmuR5cuXWzw4MG2xx57uHn7+++/7d5773XzmygFtrX81ICgXr16rjGA13ChZ8+e7rXff//dBbFnzJhht99+e/izmj4tV61LlUZ/9NFHXcBcfvzxRzvppJPsrrvuslWrVrl5PeOMM+zXX391DSKeeOIJa9u2rZsPNYTw0/sKyF944YXufS2DaMsFAACkHoFxAAAAAAAAAEgRBYRFmb95oQBs3759I773zTffuMD2448/7vowV8BY2dnlypULD6PAtbKhldWsDGQFiRVQl/79+7tAdJkyZezqq692r+v7EvHUU0+5YPqJJ57oPq/xdOrUKRyMVwBa2et6T8F4Td/48ePd+3pN07127Vo3Xfvtt1/U79GwKjuvwL7+33///a1s2bIJLz8F6JWlX6FCBde3uxoJLFq0yL799lsX8FagXe8pMH/99ddna4Sg4LkC5crmV/a7vnv6dPVBby4QrkD2oYce6hoiaLn36NHDXn311YSnLdK8JrpcAABA7hEYBwAAAAAAAIAU8Uqn//XXX/n2HQsWLHCB95122inqMF7Jdu//rVu3uqC9guDKxlbpbpUZVyBWGd3KzE70u/XZSBR43nXXXbO9tttuu7nXK1asaO+++669/fbbrkS5AsqxSrgrcK15VDl1jfPmm28OB/YT4Z//unXrukYDWicKtKusucqza971UGa7stI9Wi4Kmns07V5GvT6vrHDvs3ponvyl2pOR7HIBAAC5R2AcAAAAAAAAAFKkWbNmLpA7evTofPsOBX0V5N28eXPMALbnzz//dNnWtWvXdpnReiiLWwFxBYmV9a0s6US/+7fffov4XqNGjVzg2E/P9bpXwn3ChAkuCK9y5L169XLBbn8JeE+dOnVcX9yaDwWOFYx+8803E5rG4PyrT3H1Ia5Au4LPGrfm23toOai0eSL0+csuuyzb5/VZZe8nItK8RlsuAAAgtQiMAwAAAAAAAECKZGRk2COPPOL6oNZf9eMtc+bMsXPOOSccsFUGtwLbCoBu377d/a+/iVCp7ebNm7u+qhWY1efUF7aCv/6Ma2Ux6/2bbrrJlT9XUFbluhUkV2a7puHWW2/N0b+45sHrUztowIAB9sorr7ggtb5XQeUvv/zSvacy6i+99JJ99tln7j1v/o8++miXka3P6LtU+l1Z2V6f4cro1usKYHtUmlwBfQXslZVdqlSp8PDKHj/44INjLiOVPFe/35s2bbJBgwa58u4K0GvZKbh9ww03uO/U+LVO1M95Is4//3x7/vnnXVb3jh073DL/4osvXCn0RGhe//jjj3BDhFjLBQAApBaBcQAAAAAAAABIIfU5rUCrsrKbNGkSLtetPq/r16/vhjnyyCNdKfRPP/3U9fOt/9Wvtad169YuyByJAtzKot64caMLkCvIrUCvP8tYQepDDjnEZXhXrlzZHnroIff6WWed5cat13fffXf3vV5GtygYreHbtm0b8bv32Wcfe/311+2OO+5w5cjVB/eUKVPce127dnXBcDUAUN/dyprXctD8a9o0DQpKK0P90UcftbFjx7p50TzoM61atXLDKsivPr3Vt3elSpWsc+fO7v2ePXuGp/GAAw6IuQ7OPvtsO/XUU10gWtn13rJUgH3cuHHuNU27puWYY46JmgUftPfee7uGAVreysBXFvqNN96YrVFCLOeee677bi27PffcM+ZyAQAAqZURSrRGTgmlFpS6IFHLR7XWK2l0YaaWmiovxMUYkF7YP4H0xj4KpC/2TyC9sY8C0ZX0+1SirOp58+a5fqvLly+f4Kc2mNn0/3KEYh1Xsv57tFfPz1ZSjRw50n755Re78847LV0paK+MdgXfgZIgd8c+AEAQNVkAAAAAAAAAFGOl/rsNqjLl8fptLv3f8CWXMsrT3cyZMwt7EgAAQBFEYBwAAAAAAABAMabsyn3MbEcCwyooTjYmAABAcURgHAAAAAAAAEAxR7AbAACgpKOzLgAAAAAAAAAAAABAsUZgHAAAAAAAAAAAAABQrBEYBwAAAAAAAAAAAAAUawTGAQAAAAAAAAAAAADFGoFxAAAAAAAAAAAAAECxRmAcAAAAAAAAAIqB1q1b27hx4+IOt+uuu9pbb72Vsu8dOnSonXrqqXGHu/nmm61Xr16Wat27d7fHHnss6vv6Tn13fgl+/7vvvuuWcaVKldxyjjd9eTFw4EAbNGhQvowbAIDipnRhTwAAAAAAAAAAFKTQ9u0W+mOOZezezDJK588t0mnTptkdd9xhX375pYVCIWvcuLGddtppdvnll9v8+fNdMPOLL76wzZs3u4D2PffcYwcccECevnPWrFlWGK6//norTBMnTiyw7+rXr59Vq1bNhg0bFvX7//e//9ltt91mZ5xxhnueqsYACu7/8MMP2Ro1PPHEEykZNwAAJQEZ4wAAAAncNMuaM9v9BQAAAFD0hebMsqypk9zf/KCsbWUJd+vWzebOnWurV6+2MWPG2OzZs23JkiXuud6fOXOmrVy50gVbjz76aFuxYkWuvm+7Av2hkBWGbdu2Fcr3prN58+ZZ27ZtC3syAABAAIFxAACAQr5pBgAAAKDghLZts9CMb83W/OP+hranNrCrAPWll17qMsKVHV6rVi33eosWLWzEiBEuc7xDhw523nnnWe3ata1UqVI2YMAA9/fHH39M+HsyMjJs+PDh1qZNG6tYsaKtX78+W4l0BWcPP/xwq1q1qtWoUcNlo2/cuDHHeP7++2/bZ5997JprrkkouK4g/jnnnGN9+vSxKlWquIxlf4l0jUPzXq9ePfd+s2bNopZ3Hzx4sLVr1841Fli1apUdf/zxVr16dZeR3b59e1uwYEFCy+Lggw/OlsH9+uuv2x577OHmXctWDQf8vvvuOzvkkEPcctFwTz/9dPg9zcuxxx5rF198sZuOXXbZxTVqkIcfftheeuklVxZdZdKV6e//fjVy0OtZWVm2//77u/+3bNmSY/qmT59uhx56qPt+bQOXXHKJe13r8LjjjrM6deq4aT/ooINsxowZ7j2tV5Ws17LUePXw1oe2M8+3337r1rWmvVWrVvbKK68kNG8AAJQEBMYBAAAK8aYZAAAAgIIVmjvbQqtWmNWs4/6G5sxO6fiVIa6gdCJ9bnuUOb5u3ToXyPTsueee9vLLL8f8nN7/4IMPbO3atS44Hgw6K+irLHQFv++9914rHSgb/9tvv9mBBx7oSn6rlLuC7YlQsFXBcWW+66/fpEmT3HQp+Kzp+vDDD11w3E+Ban3us88+s6lTp1r9+vXtvvvuc6//9ddfLsD87LPPWuXKlS1Zc+bMsb59+9qDDz7oxqMA+3vvvRd+f+nSpXbEEUfYBRdcYMuXL3cB5yFDhthHH30UHub99993QWl9/vbbb7dzzz3XrR81eFA5/AsvvNAFsYOl62vWrOlel88//9z9X65cuWzDaP4UFO/du7ctXrzYBf/VyEAUUNe0a/vROtt7773de2psoIYHKlnfo0cPN17ve/y0Po466ig75ZRT3Lw9/vjjrmGAlnO8eQMAoCSgj3EAAIAkb5pltGpnJZlubikr5M8//7StW7emdNzKllAJybz2rQgAAADEbPiamWkZ5cpZaNOGfxvANmtlGaXLpOQ7FJCUhg0bJjS8gpkKZCroqSxrTyLZ48rybtCgQcT3ypQp4zKx1Z9506ZNXQaznzKLlTmsgLiCsck48sgjXZl4qVChQo7vVb/pChrr+l5ZyX7KWldmuALGCtJ6gWN9TsFaNSxQFvlee+1luaEM6MMOO8xlRsvAgQPtoYceCr//4osvusCwF4xWxn3//v1dMF+fE2XQe++r0YCCywq4K8ieV6NGjXLjUXDd06VLF/dXGfYnn3xy+PVbbrnFZakrgJ7I9jR+/PhsGehdu3Z163bkyJHh31j5OW8AAKQ7AuMAAACFeNOsKAbFdQNHWQ777ruvK/2XKjt27HCZEVdccYXdddddrrQhAAAAkC8NX6tW+/eFSlVS3gDWK52ua+YmTZrEHHbNmjUuwKysbZW5TlYw6OynDHGNU+XUlQmukts33XSTZWb+W0T0mWeesebNm4eDpKn6Xl3HK6B744032s8//+y+X9ngu+22m3v/hx9+cL8rFJj3Z1NfffXVLqCu6dFyUYBYvwt22mmnpKZNQWSVq/fzP1dDgQkTJrhS4v7fIl5wWvwNFLTsNA2pyqpWhrgaKkSyadMmu/LKK930qbS8t66U9Z9IYHzRokWunL7f7rvv7rLyC2LeAABIdwTGAQAACvGmWVGjTHHd4Hvqqaei3szJC92QUqbMdddd527elC1bNuXfAQAAgJIpW8PXUv/eFs0oXdpCmZkpbQCrsuEKTo4ePdqVM48XFFc/1brOTrSMuZ8XOI1E/VSrL2yvVLvKh7dt29ZOPPFE95r6vH7++eftpJNOsldffdVlbKfie0WNafXQPKpkuUqQv/vuu+49Za4rm1vTozLrXj/d6jP77rvvdg81mNUwmn4FipOhDPovvvgi22uqdtWpUyf3/8477+wy1rV+ciPevMejIL3K30dy//33u/7Hp02bZo0aNXLVBNTnutf3e7zv1mcU+PfTc70OAADoYxwAACCpm2Z6XpL7Gld2w3777ZcvQXEpVaqUywxR34LK9AAAAABS3vC1cpXsb/gawKaCAtyPPPKIy3bWX5UHF5WrVr/auqZWxrT6glYQXZnbuQmKx6NgtwLCCqoqO1rX2v4+xsuXL29vv/22bdmyxQXLvW6SJk+enKfp+eabb1z/2hqfspHV93mwb3MthzvvvNOVLvdKxo8bN84tI/WzrZLiCtR7nxsxYkSOTOholHGu/sJVVly/K55++mk3Xo/Kh3/88cf2+uuv27Zt29xDWeya7kTUrVvX/vjjj3CwOlnqo/zrr792jSG07FVa/tNPP3XvabvQelEwXH2Iq9Fw8Lu1/Wi+IlG3VMuWLXMNCjSMxvvSSy/ZmWeematpBQCguCEwDgAAUIg3zYoa3dzSjZr85I0/1f2XAwAAoOSK1PDVkx8NYHv06GETJ050wVmVU1dgunfv3taiRQurX7++vfnmm/bll1+64KyCwMqW1kNBTI8yqf3Pk6XMY2Vna7ydO3d2weiePXvmuPbWtCjIqyxqBWoVTA/2R56b7pdq1qzpynarwau/j2+PSrur3LsyxxWY/u2331xjgcqVK1urVq3cNCvbXDRNXh/Z8ag8vPoRV5a6puGrr75y4/WoJLn6Nn/yySfdulCw+aKLLnLTnYhzzz3XVdFSt1J77rmnJUvZ2wrcq09zfbcC/mPHjnXvqVspNWDQ6+r7XMvAT9n92l7Uj7i/FLxHAXVtd+rHXPN+3nnn2eOPP+5K9QMAALOMUG6btpUQuiCqWrWqK/uji46SRi001cpQpZfyWiYIQGqxfwL5e9Msa+wLFlq9yjKq5exD23s986Qzo5ZaLK776IABA9zNo1tvvTXb6+oLUFkJY8aMcf0IKjtBN3zUv+CgQYNs1qxZ9uyzz7obXn///be7rmrXrp0rLdm1a9ds45o9e7bLaNCNImXQAKlWXPdPoLhgHwWiK+n3qbzrTpXZVn/VyTTYzJo9w7I+ec91kxQMjEtIGbhrV1vmIUdZZgntNsnTv39/O+WUU1yZ93ShzPLhw4dby5YtC3tSgCJ17AMAZEcf4wAAAPH6Fg+ir/Fs/vnnH3ej6vvvv3fPleGhrBhldagvcmU5qByjyjQqWL7HHnu4QPmkSZNcCUOV9wtmQgAAAAAFkS3uyY++xosq9TuebpRhDQAAkFc0vQYAAMjFTbOS3te438UXXxwOil999dW2atUqmzlzpstkmjJliitl2KVLF1fCcOHChe49lWuUHTt22OjRowt5DgAAAFCchebNdVWfbPv2fxu4Rnm491evstC83wp7kgEAAJAPyBgHAACIcdMsqqys8E2zjKYlt5yfgt+vvvqq+1+l0e+++27LyMgIv3/QQQe5v8GMcAXKPeXKlSuw6QUAAEDJk1GnnmV2ONAskQ4lM8wyatctgKkCAABAQSMwDgAA4MNNs+TMmTPH9SfuBbv9QfFY1B+5FxRXf+IAAABAfsmoVsMy2tN1DwAAQElHYBwAAMCHm2bJCYX+vwVBokHxW2+91YYMGWJlypSxF154wdq0aZOPUwgAAAAAAAAA9DEOAACA/0yfPt0GDBhgEyZMiDnc5s2bbf78+bZhwwbXf3hp9bluZtOmTcsWKA/atm2b9e/f3wXFK1WqZG+//bb16dMn5fMBAAAAAAAAAEEExgEAAGBTp051/YE/88wz1qNHD3vuueciDjd37lxbtWqVrVixwn799VfLzMwMB7e///57u/7668Ol1eXDDz+0zz//3PVF3r17dxsxYoQ1bNjQPv30U/ccAAAAAAAAAAoCgXEAAIASTkFxBak3btzonivr+9xzz80RHNdzBbk9WVlZLlA+dOhQ22uvvdxrd911l9WsWdPatWtnNWrUsCOOOML1Q37NNdfYRx99FO5XfODAgdapUyf3uPDCCwt0fkuC0PbtljVntvsLAACAnEIbV1jWnHfcXwAAAJQMBMYBAABKsGBQvPF/rweD4/qr516p9LK+4PjKlSvtgw8+sPvuu8/2228/95qyyatXr+4+o0z0LVu2hL/zjz/+sK+++ir8mD17dgHPdfEXmjPLsqZOcn8BAACQnYLhodmvmC2a5v4W1eB4RkaG/fDDDykbX69evezmm29O2fgAAADSDYFxAACAEioYFD/azH4xs/8FguP9+vXLFhSvZGZtzKzqf8MpEL5w4UI777zz7Ouvv7Z169a5fsh///13e/rpp2333Xd3JdT1+UiPyZMnF9ISKJ5C27ZZaMa3Zmv+cX9D27cV9iQBAACkX1B8/RKzCnXd3/wKjk+bNs1db6vBaLVq1VxVpXvuuce2bt3qGo4efPDBVqdOHatSpYq1aNHCnnrqKSsuFGBXoL2wpct0AACA9EBgHAAAoASaPn16jqD462ZW3szuDwTHR44cGQ6Ktzazyv9dRDYJBMdVVn3Dhg2FNEfwhObOttCqFWY167i/oTlk5AMAAOQIiu9U26xUmX//5kNwfNy4ce56u1u3bu46efXq1TZmzBhXLWnJkiVWunRpe+SRR2zx4sW2du1ae+ONN+zGG2+0Tz/9NGXTAAAAgOwIjAMAAJRATzzxRLby6V5QXDICwXHPFWa2rwLk/wXJveC4v6z68uXL8zxtXhAeecgWz8y0jHLl3F+yxgEAACIExTNL/fuG/qY4OK7r2UsvvdQGDRpkl19+udWqVcu9rqxwVVJq3LixlSpVytq2besC5F5ZdD1+++23pL5rypQp1rx5c5eRfvLJJ9uaNWvc66rKpNdilUp//fXXbY899rCqVavagAEDbPv27dmGHzt2bLb3e/Toke3z3333nR1yyCFWo0YNN5yqRclbb71lQ4cOdY0DKlWq5B6J0O+Jhx9+2C2nypUrW9OmTe29995z723bts2uu+4622WXXax27dpuXr3fHlreWtb16tVz2ffNmjVz353b6dD4vOnQMlRm/88//+zee/DBB+3QQw/NNrwaPGhY0fLRcjr//PPdctttt93cutC0aBmpesDgwYMTmg4AAJB6BMYBAABKoOOPP97deJMFZqZbM/5wtBccv83Mdv3v731mVmfHDvvjzz9tx44dbvi/zGyr73O60ZNX3s1A3fBCLrPFK1f594VKVcgaBwAAJV7UoLgnxcFxZYjPmzfPTj311LjDKohavnx5a9WqldWtW9ddp3v23HNPe/nll2N+/sUXX7RPPvnE5s+fb//8848LxCdizpw51rdvXxfoXblypbVv3z4chPbeP+OMM2z48OHu/Q4dOtj7778ffn/p0qV2xBFH2AUXXOAC1Ar8DhkyxD766CMXgL/++uvdvK1fv949EqHvGjZsmL300ksui17jUiMCufPOO12AW+XptWz1W+a0005z702aNMktJwXq9bkPP/zQBcdzOx2PP/64Pfvss/buu+/aihUr7IQTTrBjjz3WlcDXMtM0qCsp/zrQsvJ88MEHrlLAqlWr3Ounn366vf322zZjxgz77LPP7P7773fTCgAACt6/TRIBAABQohx99NH2zDPPhPsOf+C/1xX8/jdc/u/fG/57eLqb2ZVff22DH3vMuh55pG1QVvJ/GjZs6MpC6pEbXjn2u+++2/bff/8cGS5IIlu81H+ZR6VLW8jLGm/WyjJKlynsyQQAAEivoHiU4Li1OtUyKvyb6Z0sL5NZ18fxKNirRqcKtir7e6eddgq/9+OPP8b9/DXXXGMNGjRw/99222120EEHuaBuPMpyPuyww1zAVwYOHGgPPfRQjvePOuoo91wZ4wpa+4PB+q4+ffq4523atLH+/fu7ALU+lxsKSCvjWkF6UXa4//tuv/328GsPPPCAW74qRV+mTBnbvHmzzZo1yzWu9X8uNx599FGXaa6MdVH2v36jfPXVV9alSxc7/PDDXfD+2muvtWXLlrnA/GOPPRb+vKZfwXQ55ZRT3HrRsBUrVnQNINTgQYHxffbZJ0/TCQAAkkdgHAAAoIQ6++yz3d9YwfGgLmY2dO1aO/u11+zpceMsM/PfAkQ1a9ZMuDRhPAqK33fffeGMdiSZLV410KDAlzWe0apdYU0eAABA+gbFUxwc90qn//XXX9akiTofik1l1bt27Wqvvvqq3XvvvXbDDf6mqbF5GdXe/8pqTqR7IwWU/Z8Njkvv77zzztne9weclaE+YcKEbI1ZFeBX4Di3FixYEA5GBy1atMh23VW1rP6lxgDlypVzr6uc+y233OL6aFfJcwWu9XtCZcxzQ/OmLG+tF4+Wq75LzjzzzHCw+5VXXnG/X/zLRpn/ngoVKkR8LdHsdQAAkFoExgEAAEqwSMFxFUOPdSvuSzNbuGmTmR5mLnPjxBNPzPO0KBCuDA+VYyconvdscQ9Z4wAAoKQKLfrcbO1Cswp14wfFPRqufE33OX0+o1nPpL9XZbwVxB09enRS/UmrH21VUEo2mNyxY0f3/59//mlly5Z119Qq9b1p0yZ3je9dW6uy01577RUOLH/xxRfZxqXPd+rUKfy+MqSD73vfpaC5yr5rHiPxGtAmQ4F5davUuXPnHO81atTIBay971cp9y1btrjX5cILL3QP9bGu8u7K8lYp9NxMh+ZN2fFetnzQcccd5/oQnz59ustk1/cCAICigT7GAQAASjgFx5X14IlXePEZ3/9nnXWWu9nXokWLPD+aN29uNWrUICieir7Fg+hrHAAAlEAZjfY3q7Kz2eaVZlk7EvuQhtPwVXb+9/O5+d6MDHvkkUfsrrvucn/VR7fXb/c555zjgtk//PCDK8Gt4PX27dtt/Pjxrjy3+qZOhjLMld29evVqu+mmm1zpbgWDFZxXiXGVNlcmtzKbv//++/DnVAJdfXjre/X9Tz/9tJs+//vqq1v9Zev95557Ltv76jv7448/ttdff90F9PXQPH3zzTfhDGnNpz7rmTx5csxrfQWblfmt8Sigr0C8MsBFGdwqb66Av7Ktr7jiCpcZrgC+vvPzzz93Wd0qRa+S5aVLl871dFx00UVuWf7666/uufotVx/h69atc8/1Hb1793a/g2bPnm0nnXRSUusMAAAUHgLjAAAAJZxucr3wwgvh5+fEGf5c3//6nD6P9MwW92eNm5c1vn1bgU8jAABAYVAZ9IxWp5pVqm+2aXn84Lje13CV6rvP5baPcenRo4dNnDjRBZ5VTl0lxxVMVYPQ+vXru0Dt9ddf7wK36pZI/6vf7L59+4bH0bp1axcsj0UBY5USV7Z15cqVw/2EV6lSxQW7Ve5b4//ss8+yBd3VKFXZzsqs1vvKDvdnSOv9kSNHuuxrva/s8kMPPdSVLxf17/3+++/bk08+6eZH86GAsoLIomCxpkHZ6165dQW6VXY8Gk2Lvk9Bec2LAt/6jFx33XVu+pVNrmx8BeJHjRrl3tN3Kmtb01mvXj3XUMBbDrmZjosvvtj69evn+gnXZ1u2bOkaGPipYbHmv1evXm5aAQBA0ZARUvM7RKULq6pVq7oyPLoQKmmysrJs2bJlVqdOnVyVHgKQf9g/gfRWVPZRBbW9MupyRZw+xkVDXmlmD/73XNkWzzzzTLgsOwpW1pzZlvXReDNlwcTa1rKyzEqXtszDe1hm05ZWkhWV/RMoqdhHgehK+n0q2bx5s82bN8/1H12+fPnU9TWewqB4caVguTKpTzvttFx9vn///i6jPdms+FRLl+kA8vvYBwDIiT7GAQAASqjcBMXtv/fv/+9/Bcf1eY1HCI4XvIw69Syzw4H/tliIO7BZRu26BTBVAAAA6cMFuVudGj04TlA8IvXRffDBB7t+y4cPH+76KI/W73Yinn/+eUsH6TIdAACg4BEYBwAAKIEmTJgQNyiud+74r09xhb0H+96PFhxX6cKjjz66wOenJMuoVsMy2ncu7MkAAAAomsFxguJRqVT4WWed5cqWK1v8nXfeceXKAQAAiipqkgEAAJRAb775Zjgo3vi/AHhGhHLpN5rZgv/+XvXf6x4NP/S/z7vPhEJuvAAAAECR6HN8xzaC4jEoS3zVqlW2bt06+/bbb132OAAAQFFGYBwAAKAEGjhwoFWoUMH9r8D3ieqzLEof4p4HAsHxzf99Tp8XjU/jBQAAAIpEcHzj3wTFAQAAShAC4wAAACVQ+/btbeLEieHg+ARfcNwfFM/IyHDlE/XXHxz3guL6nGg8Gp/GCwAAABSJ4HijAwmKAwAAlCAExgEAAEqogw46KEdwvEUgKP7MM8/YiBEj3F9/cLxFhKC4xgcAAAAUBQqGZzbrSVAcAACgBCEwDgAAUIIFg+MLAkHxs88+2z3XX39w3F8+naA4AAAAAAAAgHRHYBwAAKCECwbHg0FxTzA4TlAcAAAAAAAAQFFRurAnAAAAAIVPwe2pU6faE088Yccff7wdffTREYdTcLxevXr25ptv2sCBA+lTHAAAAAAAAECRQGAcAAAAjoLcTz/9dNzhFDSPFjgHAAAA0s3GbStt+aafzSyUwNAZVnunllahTE0rboYOHWozZ860V155pbAnBQAAoFBQSh0AAAAAAABAsbVu22Kbv3ay/bHmQ5u39pOoD72v4dZtW5KS7502bZp1797dqlevbtWqVbN27drZPffcY1u3bs023E8//WRly5a1Xr16WX66/vrrCz0ovuuuu9pbb71l6SCdpgUAABQMAuMAAAAAAAAAiq1a5ZvbTqVrWkZGaatQulbUh97XcBo+r8aNG+eC4t26dbO5c+fa6tWrbcyYMTZ79mxbsuT/A+9ZWVk2YMAAO+CAA/L0fdu3b7dQKJGMeAAAgJKLwDgAAAAAAACAYqtUZllrVLmjK6WeFdoecZh/Xw+54UpllsnT9ylAfemll9qgQYPs8ssvt1q1arnXW7RoYSNGjLDGjRuHh3344YetZcuW1rVr16S/JyMjw4YPH25t2rSxihUr2vr16+3bb791QXZlqLdq1SpbhvjNN9+cVFb60qVL7fTTT7f69eu78R100EG2adMm995vv/3mgv41atSwJk2a2LBhw8Kfmzdvnh1++OFWtWpV976mZ+PGjXbSSSfZn3/+aaeeeqpVqlTJBg4c6JaVllO9evWsSpUq1qxZM9eoIBGan2OPPdYuvvhiN3277LKLa3zg0bi1fLXc9f7BBx9sP/+skvoWcVoAAEDxR2AcAAAAAAAAQLFWd6c2VqF0bdu8Y03E9/W63q+7U9s8f5cyxBUcVtA1lgULFthDDz1k9957b8T399xzT3v55ZdjjkPvf/DBB7Z27Vrbtm2bHXXUUXbKKafY8uXL7fHHH3fZ6J999lnS86BMdgWdS5cu7bLcV6xY4fooz8zMdNnpPXr0cKXhFy9ebG+++aYrEe9N6+DBg22PPfZwn/n777/d/Gk8r732mgteK1ivIP4TTzxhkyZNcp/77rvv3Dx8+OGHLjieqPfff98F7FeuXGm33367nXvuubZu3Tr3nub/2WeftXfffddNywknnODmSaXsI00LAAAo/giMAwAAAAAAACixWeOpzBYXBaWlYcOGMYc7//zz7dZbb7WaNWtGfP/HH3+0vn37xhzHNddcYw0aNLBy5crZxIkTrXbt2nbJJZdYmTJlXBa6Pj9y5Mik5+Gbb75x2dUKLquPdAW2DzzwQPc9X331lSsHr0B0+fLlXQBfWdvKhhd9t96fP3+++3///fd3fahHovc3b95ss2bNcoF9BauTCYzvs88+1qdPHytVqpSdccYZLug9Z84c996jjz7qlm/Tpk3d9CuLXxnvmn4AAFAyERgHAAAoAkLbt1vWnNnuLwAAAIDUZY2nMltcvNLpf/31V9RhRo0a5TKvFczNCwWSPYsWLbJdd9012/u77767ez1ZymZXYH+nnXbK8Z7Gp2C8P9jt/x5liOuzKqeu6VHJc2WgR3LIIYfYLbfcYjfeeKNbbieeeKLLtk+USrD7S8trer2McQXmVQpeZdS9xz///JOr5QEAAIoHAuMAAABFQGjOLMuaOsn9BQAAAJCarPFUZ4uLMp4VEB49enTUYVQyXJnLCgbroVLkyvj2B3oTodLmnkaNGrlgsJ+e6/VkqR90BfaVzR2k8amEujK8I31PnTp17LHHHnPBdZUxV5lylVsPTq/nwgsvtC+//NL1+a2MdGV2p8LOO+/sSqavXr06/FBf516J+0jTAgAAijfO/gAAAGkutG2bhWZ8a7bmH/c3tP3/b0ABAAAAyH3WeKqzxb3M5UceecTuuusu91f9X4tKfJ9zzjkuYPzggw+6UuU//PCDewwcONBlT0+fPj3X33v00UfbsmXLXFBa2eiffvqpvfTSS3bmmWdGHL5fv37uEcl+++1nzZs3d0FrBZQ1vmnTptmWLVusQ4cOVrduXbvpppvc859++snN51lnneU+++qrr7ogdygUclnaKnOuUuaiz/3+++/ZSrZ//vnnrgS6sr0rVqwYHlbBdi3LYLA/URdddJGbxl9//dU9Vx/mb7/9djijPDgtAACg+CMwDgAAkOZCc2dbaNUKs5p13N/QnNmFPUkAAABAkc8a3561OeXZ4p4ePXq4DPDx48dbkyZNXIC4d+/e1qJFC6tfv77rt1sZ1t6jSpUqrr9uf7/krVu3doHtRGmc+k6VaVe/5eedd57rI1x9g0ei4PUBBxwQ8T1lUyvbWxnWCpArq/2GG25wJdHVL/i4ceNcEF8Z7j179rQrrrgi3B+6Xle/4pUqVbLOnTu7xgAaRq6//nobPny4Wx4KuitYrb+aXo1LmegPPfRQePqUuR6vr/Zo1O+5Av8nnHCCW74tW7a0l19+Ofx+cFoAAEDxlxFS0z1EpYuzqlWr2po1a9wFVEmji121NFUJJMoLAemF/RMoGfuossWzxr5godWrLKNajfDfzJPOtIzSqb15B5QUnEOB9MY+CkRX0u9TiUp7qw/q3XbbzQWSc2NH1labvuxZW7N1gVUt29ja1zk35YHxdKdM7z333NNleyvQnY7U97iC5eeff35hTwpQLI59AACzf+vSAAAAIL2zxatW+/eFSlXCWeMZrdoV9uQBAAAARTZrfOuadfmSLV4UqC9vr8R4uhoyZEhhTwIAAChmCIwDAACke9/imZmWUerfy7aM0qUtlJn5b1/jzVqRNQ4AAADkQt0Kba1URhmrtVOLwp4UAAAAFBBqkgEAAKR7tnjlQJlMX9Y4AAAAgOQpKO4FxwEAAFAyEBgHAAAoItniHmWNm5c1vn1boU0jAAAAAAAAABQVBMYBAACKUra4h6xxAAAAAAAAAEgYgXEAAIAilC3uIWscAAAAAAAAABIX+U4rAAAACk1o3lwLrV5ltn37v1nj0WRlueFC836zjKYtC3ISAQAAAAAAAKBIITAOAACQZjLq1LPMDgeahRIZ2Cyjdt0CmCoAAAAAAAAAKLoopQ4AAJBmMqrVsMz2nS1z3wQe7Tu74QEAAADkTVZWlq1fv962baOrokg+/fRTa9SoUcxhWrdubePGjUtofJUqVbKZM2dacTFw4EAbNGhQnpaf56WXXrL999/fioLJkydbtWrV8jyeZLaddFqvfiNGjLC99tor6vs333yz9erVK4VTBwBIFoFxAAAAAAAAACU6IL5u3TpbsmSJrVixwjZs2JCS8U6bNs26d+9u1atXd4HDdu3a2T333GNbt2517++666620047uQCxHqkILuanLl262KJFi8LPDz74YBs2bFi2YWbNmmU9evRIaHxqhNC2bdtiEzB84okn7O6773b/z58/3zIyMmz16tVRl18sp512mn3++efh59pW3nrrLSvOktl2Cmu9AgCKviIXGH/00UfdhUD58uWtY8eO9vXXXyf0udGjR7uLkaJ+gQUAAAAAAAAg9QFxZYqHQon0ZxSfMl8VFO/WrZvNnTvXBUjHjBljs2fPdt/neeWVV1yAWA9/EBVA4du+fXthTwIAoCQHxnXxeMUVV9iQIUPsu+++c60sdXG5bNmymJ9TC72rrrrKtcoDAAAAAAAAUHJFCoiXKVPGPVJBwfVLL73UlV++/PLLrVatWu71Fi1auFLLjRs3Tsn3PPDAA7bLLrtY5cqVXSLRM888E37vww8/tA4dOrgsdJWofuedd8Lv9evXzwYMGGCnnHKK+2zz5s1dOWx/Ge+mTZu69xo2bGi33XZbjpLZV155pSsNrnlUtrsaAfgzm7VMNd9Tp07NNs2tWrVyjQFESUw//PCDG37o0KGuMYGXPT9jxgz3/Wow4Pnrr7+sXLlytnjx4rjLZtWqVXb88ceHs/Xbt29vCxYscO9p2m666SZr0qSJ1axZ03r27JltnJouZQm3adPGqlSp4t5fs2aNe2/Lli129tlnu3mrWrWqG+abb74JL1etb9GyF5VO1/xomfqX35tvvum+3++rr75y72/evDlbSe6TTjrJ/vzzTzv11FPduFTa+3//+5/7Pr+77rorvB6CPvjgA9t3333dNNevX98uvPBC27RpU/h9rTdVM+jUqZNb7l27drWFCxeG37/mmmvcdqv3tA5fe+21iN/z9ttv2+67756tgcmXX35pNWrUcPM1b948O/zww9106LUDDjjANm7cmCMrPtZwsTz44IN26KGH5ogpaN+T77//3g488EA3ztq1a7tlunLlymxVEDSvRx55pFWsWNEmTpyYbb3K6aefbg0aNHDbhrarTz75JMd0XH/99W7b0v752GOPRZ1exTVUHUDrROPU92gbAwDknyIVGNfFni7a+vfv707AukCpUKGCPffcc1E/s2PHDndyueWWW9xJGQAAAAAAAEDJEysgrmBoqihDXIE9Bd3iOf/8812QtXPnzjZhwoRs76mstIKdkcyZM8duuOEGF/DUPCmo6gVjf/zxRxdM1WcVIH7yySftjDPOsF9//TVbsFABVmWp6z0vyKoy8vr/2WefdeNVeeujjjoqx/fff//9LglJJaYVvFYA0U/LVIH3F198Mfzat99+64LbwYqeeq5AoubXy55XQpQC9mPHjg0P98ILL7hgqQKI8dx3330u21ffp8Cn5kdBXRk8eLB99tlnrtS9toVmzZq5afV79dVX7eOPP3YBaZU/V8BVRo4c6YL2v/32m1t2b7zxhtWrVy/H93tVTvVZzY/uT/sdc8wx7vOaDo+WldabKqX6KQitAKtXXUD3xM855xx7/fXXszUcUDBdQftIVLL/6aefdtuDvlPBXN1r9xs1apT7juXLl7ug8I033hh+T+tDDQA0zWpUoG1G23iQ5ksB7ClTpoRfe/75592+oPnSst9jjz3c/vf333/bvffea6VLl84xnkSHC+rbt69br/6gvparplcyMzPdfqFx/vTTT277uPbaa7ONQ8vx9ttvd8tW21vQYYcdZj///LPbrrTd9O7d2+0rHo1XxxNtW9rPNP5gAxFR4wE1utD28/vvv9vMmTPdtqXvBgDkn/hnkzShvnemT59u1113Xfg1nch0cvriiy+ifu7WW2+1OnXquIsFtWKMRy2y/K2y1q5dG75w1qOk0TzrJF0S5x1Id+yfQHpjHwXSF/snkN7YR4Ho2C9yv9wU8NV9Pt1jVNAq1cFwPwUWRdnWsShgp4zTUqVKuSDniSee6AJo++23n3tfGdTR6DM6VipwrUzeunXruocoEK7gtpc5qwxZBZ0V7PWCnUcffbTLjhUlIel1BfoUvNSyUeBPGcvKYPamJ1lnnnmmy7x95JFH3Hg1vwoiKkibCN3PVZDSC9orKJ1o0FDzoPlRIwUFdb3say0zZfAqOKwsXdE4FQhWMHXnnXd2rylrWPeURetFWc/eeBUE1fJRN58KqudG2bJl7eSTT3bLRNnQaqShIKoC7YlQproSx9RwQMtH98e13SnQGom/kqqSx9QgY/z48S4A7VEW+W677eb+VyDf3yjDH9hXMFjvqQ90b3iPgtdnnXWWW2/avpQlrvlSBQNv+SlgrAqvqkqw//77R5zeRIcL0j6geIEy9BWQVkb2pEmTwlnb2hb8w6o67dVXX50juO41Mom0rWp/8eizqnagxihaj6Jt6eabb3bzoAYvWnZq1HHQQQdlG48aimj71HJUnEMJgGogogYrXpUGAEAJDoyrdZiyv70LPI+e//LLLxE/o9Zhag2okjyJuvPOO112eZAuLHQiL4k/HFQqSBeNOkEDSB/sn0B6Yx8F0hf7J5De2EeB6PxZiUicMlj/+ecfF3xUQDK/jy1e6XRlowbLZUcLVioYpzLSCpAnEojWeBUoHj58uAvUqQS2SmErAKxgorKdlanrUfa0Sj97/FnOCuR525fKP7/77rsuI1zB4bZt27og3SGHHJL0clBwUd+jMu4nnHCCy0aOVoI7EmUZq3tMZSYvXbrU3R+OFvgNUsBS93L79OnjzikKQiuYqyxgNZJQkNLfMELbhT8wHlw+3r6nzGMFbBW81PCaHmWne+s82YYDaqDw0EMP2Xvvvecy2tWIIVHKDvcaDuivArAqNR+Jsr2VcKasZJVQ1/agjHy/aPMsyphXqX5lwGu5aTlqfUSbLpVt17apbUnZ7nouyvxW0FjBa41H064M9OA+mehw0ZartlkFxrXNKaiuaRBl+qsbAC0PzYOuOYJdKHjDRqLh1YhEjUyUda7pUYMb/7JQRQP/ONVwxZ9B79F+qgx8lXX36NpHMRAAQP4pMoHxZOnErQsVlYhJ5sJEFwhqKebRiU0XROpzxH/xWFLoZK+LD80/NySA9ML+CaQ39lEgfbF/AumNfRSILlhiGYlRJqYCTv6McWW25lfGuLKI1V/y6NGjs2XkxpPsMU9BXz0U6PTKWyvwqXuZl112WdQy7PGoVLQeakigTFuVOlfDgtxMr6ZJWdFaB3oEs2ZjjUvZ6uonXA0AFIxW4FcB7ESoL26VeddDgfVjjz3WzYv65tZ0qPS81+90MrTdKKtXDwVGFbxXkpWy4uPNT5AaM+i+tSoDKICrvqujbZORxqfvVpB39uzZLis7Ul/X/mHVgEJ9gCvoPWzYMBdMT4SSzxSkVmOLvffe202LGmD4+xH3U8BdmdnKZtd8+TOslYXvZW9rWz3iiCNc4wtl5fslOlwkxx13nMuIV/VZbXvKhPeoQYP2T21T2r7UGCXYV3usdffyyy+7x/vvv+8y2bW+1I+9f1mov3qvmwZROf5I1SO0n2o+tW0DAApOkQmM6yJBJYJ0weGn55H6cVG/HGp1pYueYLkpXcCoT51ILTbVqi5SyzqdEEvqD3Kd4Evy/APpjP0TSG/so0D6Yv8E0hv7KBAZ+0Tul5uycRUQDJZUz48AucanQKmCkUq0UTa4MrHVL7gCtQpiaxjdu1Q5bk3fm2++6YKWsYKbfrq3qYCbMowVLFYg2OuDWUFB9QverVs3F4hWdvB3333nAoEtW7aMOV7da1VpZ2Xqapya/mh9O6uSp+7BxguMq6tLZe3HCvxqXAsWLHDT6v8+lVNXYFWZtcGsWzU+UMA2GNgUBZsVAFU/1ZoHBSk1Xi1rBUcVUFZf3QpOquS6Sn0rqzweBYeV4atS5tqe1Fgl0vLxGndp+ahcfqzlo21FgfpYDRkiLWvNlwLF2r5U0lxB62i0zWv9a5pVBv7xxx9PuKS9Pqv78pon3V9XQF39aMei9aaqA8rQVt/lHmVaq0GAlrumR+ONtPxiDad1PnnyZPeIRPOlkv1qlKJGA+q33T8vOhZo2SnjX5npydDntb8pVqFjiPbnYCUPHWOUsX7DDTfY999/78q6KwAfpMoQmj8NN2jQILe/aZ/WNHfv3j2p6QIAJK7IXE3rhKOLiI8++ij8mk7Eeq6+OoLU4k+tyVRG3XuotI3K/uh/rywOAAAAAAAAgJITIFff0gpsKViqzE49omW/5pb69J44caLrx1nJOQruKVine5b6fpVxvvTSS13AXAFHleP2goEeBcfUf3EkCsqppLMCphqHArZeBrACpMrUVcBN41a2qobdsmVL3OnW/VaV9ta906pVq9qjjz7qMn8jNcq4/PLLXUBZ86b5jURlqVXKWtOnEtfRKHipYKWmV+PzqJ9qBUXVL7a/f2jNi8pX+5eXnwKyahyg9a2+uHX/+IILLgh3pann6oNd7+ue8wcffGCJ8LLENY0KRmsZDRkyJGJwVq9rHWpYZRlHC4yrX3mtMwXxo1GGukqTa1z+DGgFoGfMmJEtKzsS9TuvbUzBVzUMUD/hidJy1LarjG2VCVe/9l5/2tGokoEaOmj+tU49yuLW9qDp0DrQ9Ecqjx9rOAWP432/tjVldavagdax54EHHnCNJrStKbM8kQx0P/Wf3rp1a1ceXduk1nOjRo2yDaNGE2rgof1cy+2OO+6I2BWBtmtNi7pcUIMVbUvHHHOM23YBAPknI5Tqq758pJIwOvnoRK4+alTyRReM6mNcF4E64elCTxc3kaj1oFoXRmqhFasVmE5K6oumpJZSX7ZsmSvrQqtkIL2wfwLpjX0USF/sn0B6Yx8Foivp96lE/UarNLaCknktLa/jTTCDXLdKVRrZH5xF4VIAW32UX3zxxeHXlD2ujG81ACjJFCRWSW+V71YDiXSiBiFqZBGt0URuKUCvbPF0m9+idOwDgJKsyJRSF5WzWb58uSs3tHTpUteXyXvvveeC4t6FAD+aAQAAAAAAAOSmxLqyx5E+vvjiC/v2229dqXm/rl27ukdJtmPHDlfKW9nZ6RYkHj16tJu+/CgJriqxAACUiMC4qGWgv3WgX7R+RTxeOSEAAAAAAAAACAbI1Rd2uXLlCnuS8F8J7y+//NJlHatSAv6fModVslvZwxMmTLB0orLgq1atspEjR7py4QAApJMiFxgHAABIJ6Ht2y30xxzL2L2ZZZTm0goAAAAoygFy9WmM9KBKoYhMAXFVOUhHP//8c2FPAgAAUVF3HAAAIA9Cc2ZZ1tRJ7i8AAAAAAAAAID0RGAcAAMil0LZtFprxrdmaf9zf0Hb6IwQAAAAAAACAdERgHAAAIJdCc2dbaNUKs5p13N/QnNmFPUkAAAAAAAAAgAgIjAMAAOQlWzwz0zLKlXN/yRoHAAAAAAAAgPREYBwAACAv2eKVq/z7QqUqZI0DAAAAAAAAQJoiMA4AAJCXbPFSpd1rGaVLkzUOAAAAAPlo2LBhdvDBB8ccpnXr1jZu3LgCm6aiZvXq1ZaRkWHz58+POszQoUPt1FNPLdDpAgCgIBAYBwAAyGu2uIescQAAACDtbNmx2dZsXWVZoawC/d5p06ZZ9+7drXr16latWjVr166d3XPPPbZ161b3figUsjvvvNN23XVXq1ixojVr1sy++uqrAp3G4mjWrFnWo0cPKwluvvlm69WrV8rHe/3119srr7yS8vECAFDYCIwDAADkMVvcQ9Y4AAAAkH7Wblttizb8aX+s/dX+2bKiQALkylhWULxbt242d+5cl6U7ZswYmz17ti1ZssQNM3jwYBs/frx9+OGHtn79eps0aZLtsssu+T5txdX27dtdY4P8sm1byfiNl5/zqfWzY8eOfBs/AADxEBgHAABIRbZ4MGt87s8FPWkAAAAAIgkpIJdlW3ZsscUbF+Z7gFzBv0svvdQGDRpkl19+udWqVcu93qJFCxsxYoQ1btzYVq1aZQ888IA999xztscee7jS1nq9fv36CX/P0qVL7fTTT3efUUb6QQcdZJs2bXLv/fbbby4oX6NGDWvSpIkrQe6nIHzHjh3d5/R5Za57Ro0aZS1btnTvHXjggfbdd9+F31MZ82uuucYOO+wwl+XeqVMn++uvv1zmcu3ata1Ro0b25ptvZguyXnfddS7gr/dPPvlkW758eVLZ3/qOypUr2yGHHGKLFy/O9r6W2/Dhw61NmzZuetTAQBn4b731lvtuLfupU6dm+0yrVq3C2dDLli2z0047zS2DBg0auPW1ZcsW997kyZPdMnj88cfd9O+///7uvbPPPtuNt2rVqu57v/nmm4TmJSsryx5++GG3HWh+mjZtau+9917c5aTtSdtSvXr1rEqVKq6ygBpeaB5V8lz/V6pUyT0i0TRfcMEFblvYbbfdbOzYsdne79evn51zzjnWp08fN/4nnngiWyb6ZZdd5ubZ7+6773bblzd93nxpeWkb+fnn//89rPWh7UvrsUKFCq5xyEsvveTmX8uhYcOGdttttyW0DAEAyCsC4wAAACnIFs+RNf7jdAvt2F7g0wgAAAAgpwzLsHKlylmZjLL5HiBXhvi8efNi9tH85ZdfWrly5VyAVgFZBQ8V/PTKrMuee+5pL7/8ctQg67HHHmulS5d2gcYVK1a4IGlmZqbLnFYpcZVuVyBZgWqVcPfG9f3339txxx3nAtwKvv7yyy8u6CwKIiuI+uSTT7r3evfubUcddZStWbMm/N2aZgVCFdxXYLNr164u6KpM+FtuucUGDBgQzjpWQFSBW5WV1zJRIFuB6ERoPnr27OmC8CtXrnTz98wzz+QYTvP1wQcf2Nq1a11w3FOmTBk75ZRT7MUXXwy/9u2337pAvoK+Cuhq/Ao4//777zZz5kybMWOG3X777eHh161b517TMpoyZYqNHDnSPVfDA1UBeOONN9znE6EAvhooKCisaf3oo49cY4h4y0mNGDSPaqCgz6nCgILjmgeVPNe6VoMAPSK544477IsvvrCffvrJrXtNc5DWqYLjmif99TvjjDPs9ddfDze6EC3TM8880/2vhgPPPvusvfvuu247POGEE9y26d+W1SBEy07TqOC/gvH6jJavGj9oGwMAoCBEvqMLAACAHELz5lpo9Srdofk3azyarCw3XOjvJWb1GxTkJAIAAACIITMj0wXIFQz3AuQrNy+3muVrW9WyNdz7eeVl+ioTNhoFlRXkVBB9zpw57rkCnMr6vfHGG90wP/74Y9TPK0tZWbkKZO+0007uNWV3y2effeaC1Arwli1b1gXYL774Yhec7Nu3rz311FMuYHziiSe64ZX5rGxeL+CpLHRln4syqBX4VMl3fVb0fuvWrd3/xx9/vMv2VYa8qDHAueeeawsWLHCZ8BqfpsMrEa8seS0XBezVICAWBXMVaFX2soLcnTt3dpnU/mxkUYA/2rgUvD3yyCPtkUcesfLly7vpUbBfy0zLUMv/888/dw0KlM2sQPPAgQPDGcxqgHDXXXe590TToWCupkEZ9wpQJ0rLUfPSvn1799xfNj/WctJ3bt682QWQlU2ebLl9BeIVePeW0ZAhQ2zixInZhtEy8jLAvXn17Lvvvq4SwNtvv+22GwXX//zzT7fu5dFHH3WNFpQBLtoWlFH+1VdfWZcuXdxramzRvHlz978ac2ietAz32msvl2W+3377JTVPiE7rRvsNckcVFtRoCbnD8ssbll/eqJpLIudIAuMAAAAJyqhTzzI7HOhKMcYXsowq1fJ/ogAAAACkVYDcK52uzGSVMY/EK3utDGuvDLZKVitT2wuMx6LAswKnXlDcb9GiRS4IqqC4Z/fdd3cl0r3PegHLSJ9VKWw/ld/W6566deuG/1cQNfhcvOxlfU7Z8B5Nl276e9MYixc8VxDVowzrYGA81k3wDh06uIzud955x2UyKzP6tddec+/Nnz/fZUgr2z1aH9jKiFfg1p89rUYHCp4vXLjQZZzfd9994XUei5a7FzwOirWclM2v7UTbheb98MMPd9+p9ZIILUcvM138/3viBRI03y+88IILjOuvGlV461rLUY0lSpUqFR5e2eL+bcY/fmX1K7v8/vvvd40a2rZt6xoieFULkLeguLpB2LhxY2FPSpGl7dh/DEByWH55w/LLG52XdJ6Md04jMA4AAJCgjGo1LKN958QGzsqyjGXL8nuSAAAAAKQgQL4jtMM2bF9v2zdts4yMTKtW9v+DpclSFrGCnKNHj7bBgwdHHEZlzvNCwU0F3pVJrExoP2X3KhiqcuZeUFnBS73ufValwCPRMBrWz//ZZHnjU3a11y+6MuISGZ+Cw8H5UOAvSNne8YK6ysjWDXM9vGz4nXfe2erUqeMC3dEEx61sZ2WV6/H333+7DHkFrZWRHo+33JX5nuxyuvDCC91DJe2Vfa2sbAWX4827txwVlPfGnZtlqLLuyjTXNqfGBf4S/1qOKhEfqxx6cPwqj6+H1u1jjz3mysL/888/Cc0PolOmuILiagSjADmSM2HCBNcAheWXOyy/vGH55Y0C4mqkpeMggXEAAAAAAAAAiEAZ49uytqoTcqtYupLLGK9SJm+Vn9Q/tAKlCppWqVLFlSCvWbOmK5muEtM33XSTy/ZV5u+tt97qSmwrc1mfOemkkxL6DpWeVmlqBUtVdlsZ5+q3XK8rS1pZ3Poele5WuXCNW/2Mi/oAV9l1lW5XX9AbNmxwN5RVTl03lfWa/iqQqmlT/95HH310rpaFxqMy2/vvv79Vr17drrjiCjffXra4+poWlXkP0vQom1vZxDfccIMr4T1mzBiXYZwMBca1nBUw1PRo/XjLUEFdjVv9u2sZKmisPtu7d+8ecVwff/yxm6Y2bdq4zGc1SlCwXCZPnuyynpV1Hsn555/vguiafjWMUMa5lr0CILGWk0q+K4CskuaqEKDv9fr71npW0Fv9sXvTEaTtUOXgtc7VMEDLIllaTvq8+h9XJQJ/dvdFF10U3qa1TaqLgE8++cQOPfRQl3EfpAYFKl+v+dMy1z4SbdqRO9qm9tlnn8KejCLHq0bB8ssdll/esPwKDk2wAAAAAAAAAJQo/5ZQ32zbQlutXKny1qDCzrZ7leZWvVytlPQzrqCz+nFW39wqp65y3OrbukWLFla/fv1w38/KAFZwU0Fa9fGs0tIe9eOtYSJRZq0yhhXsVTBSpbwV4FWf2MquHjdunE2fPt2VEVe5bwVavT7CdcP99ddftzvuuMMFeXUTfsqUKe69rl27uiC6AqAK5ivrXfPhLyeejOuuu87Nl7KklUWvAK9X0l0UiD7ggAMiflbzoRLo77//vpvOa6+91s4+++ykp0GZYwo4K6itPsf9JWu1nJQFrWWgvtaPOeaYqNn04mWJa3koEKzPKJPamxd9TzTK8la2d58+fVzAWIFhL3s71nJSoFkNILQ+tD6VRf/QQw+599SQQoFl9T0ebR1pu1BQXcF89emt7Ozc0LLTuvA3LhD1X68GDipVr2nRsvRnlAdpG9X0K9iu5ac+yseOHUu2OACgQGSEojVhQ/jCQydoXaTqxF7S6EJl2bJlrqwQFydAemH/BNIb+yiQvtg/gfTGPgpEV9LvU4lKh8+bN88FJYMlxKNZvmmpLdu0xAXA/Rni5TLLp6RPceSOSoXvueee9tNPP2XrR7yo6t+/v+uDWwFuoDCOfd999521b9/eNYoh4zR5aoikhh8sv9xh+eUNyy9vkjn+UaMEAAAAAAAAQLEWspDLEHcB8VIExNNBuXLl7Ndff7Xi4vnnny/sSQAAAHEQGAcAAAAAAABQfGWo3+9MK5dZjoA4AABACUZgHAAAAAAAAECxVaVMNSubWc4ql6lKQBwAAKAE40oQAAAAAAAAQLGl0ulVy1YnKA4AKPJat25t48aNy/XnBw4caIMGDUrpNAFFCVeDAAAAAAAAAAAASImDDz7Yhg0bZvPnz7eMjAyrVKmSValSxWrVqmWHHHKIjRgxwkKhkBWl+SlXrpxVrlzZqlatam3atLErr7zSli9fXuDTMmvWLOvRo0dCw958883Wq1evbK898cQTdvfdd1tRlk7ro6BpX/IepUqVcsvBe969e/eUNb6o9N84y5QpY2XLlg0/13tFHYFxAAAAAAAAAAAA5ItFixbZ2rVrbeHChXbVVVfZLbfcYueff74VJQomr1u3zlavXm2vvvqq/fXXX9a+fXv7+++/C+T7t2/fXqQaExT39VFY1q9fH3506dLFLQfv+cSJE1PW+GL9f+M87bTT7MILLww/13uJ2rZtm6UjAuMAAAAAAAAAAADIVzvttJMdc8wx9tJLL9kzzzxjs2fPtqJGGfCtWrWyUaNGuSz4+++/373+3XffuWz4GjVq2B577GFPP/10+DN6r1OnTuGs+WOPPTb83tKlS+3000+3+vXrW7Vq1eyggw6yTZs2hb9r+PDhLiO6YsWKLjC566672ltvveXeV+b9XnvtZddff73VrFnTdtllF3vsscfcexpm6NChruy6l+0r/fr1s8svvzz8/d9++60dcMAB7rs1X6+88kq2jHNN68UXX+ze1/jHjBljRWF9fPDBB7b33nu7jPJ99tnHPvzww/BnsrKy7OGHH7YWLVq4rPOmTZvae++9596bNGmS7bnnnu71unXr2gUXXGBFRax51no/++yzXQUBbQuax2nTpiX9Hb///rvbJmrXrm2NGze222+/3S1P//Y4ZMgQq1evnp1yyiluG1KFAzWE0XTttttuNnnyZLd9aj+pXr26DR48ODz+efPm2eGHH+6G1b6kbXPjxo2WSgTGAQAAAAAAAAAAUCD2339/a9CggU2ZMsWKqtKlS7sgo+ZBwe0jjjjCBVFVzltBPwUHP/roIzesAssKJiq7WZnNV199tXtdAUW9rnGpkcCKFStcMDsz8/9Ddy+//LILeCrjXsHxoJ9++skFh5csWeKC1tdee61NnTrVTZsC5gpKetm+QZqeo446ygUwNd2PP/64DRgwwD777LPwMO+//74L1q9cudIFQc8991yXqZ3O6+O3336z4447zm688UY33VoOPXv2dEFXUWMDlfpXAw0tV60nBXnlrLPOcutH8/jHH3/YGWecYUVBvHn2tqVzzjnHrXdlget9/Z8oBagPO+ww99B2/Omnn9ro0aPt+eefz7Y9al38+eef9uKLL7rXtP1269bNVq1a5ZanGoK8/fbbNmPGDLetqTGDGo+IguQKmGtfUPb/vffe68aXSgTGAQAAAAAAAABJU1BBAa5UUAaZMhIT/S5/1mQqp6M4UYDt1FNPLezJACJq2LChC5QVh3lQAFDB4z59+rh+n5Xh3b9/fxeIFPXTvGDBAlu8eLHrE1rDyjfffGM///yzC0grc1YBwAMPPNAN47nmmmtcIwK95g+YexQsV1au+oHu3LmzK339wgsvJDT948ePd5m/l1xyiZvGrl27Wt++fW3kyJHhYZR57M2Xgppbt261OXPmWDqvDzUQUD/kJ5xwglumvXv3dsvVy4bX8tYyU+l1NSpQJnzLli3de1oOCjKroYCWbVE5t8SbZzn00EPDDTEGDhzoMuJVUSBR2l60narigLY3LbfLLrssvJ2LMr0V3Nb7FSpUcK9pOWu6tA2pEYaC6mrAoeWrbH9lr3uBcS1/NfKYP3+++1/LX+NKJQLjAAAAAAAAAEqWrA1mW777928+UYnS7t27u5vICvi2a9fO7rnnHhdUUJaVV9rWeyjgcemll1phUoDghx9+SHh4BWA+//zzfJ2mRL4r+J4/aF5UlmV+UMagPygCpBMFx1QquTjMg4J4EyZMcMd676FS3QrwyXPPPWebN292AUKV71bGsihYrmCuSsxHo+BjLAqaK4DoUeazpivRvt91vPTbfffd3eselcT2H9c0remYMe5fH/HmS8td5dMjefPNN13Wc/PmzV1ZcvVfXhQksi69rPjcbCui7VzLxr+dX3nlla5igkfbc7ABhwLwHi9YHnzNq2igDHGNQ+XUNT9qwOCVak8VAuMAAAAAAAAASo7tf5pteMVs08R//+p5iikDS0FxlQ6dO3euK1WqbC6VylWgpEuXLuHStnqoz04vkwrpb9u2bUX+O7Zv326hUKhAvxPwfPHFFy57WhnKRZX2IZWDVpbuzjvvbMcff7w71nsPBY8VLJcmTZq4LG4FENW3+lVXXWXTp08PByYVNI8mUpa4n5ajf99VCWsFFhP5bKNGjVyw00/P9XpRXh/x5kvLXVnhkShD/vXXX3elvFWWXBn0Kumd7hJZl2oQ4OffVhKh7VyNO/zbuUrRz5o1KzxMvG0unjp16thjjz3mpvXdd9+1J554wjVWSCUC4wAAAAAAAACKv9AOs81fmG14zWzHcrPM6v/+1XO9rvdT8TWhkMv8HjRokCs3WqtWLfe6sgRHjBiRI2NLVLZW2WvJlGx94IEHXCZh5cqVXVaVgi2i7Cr1s+qnrC6VKheVK+3UqZNVqVLFTZvKqkqHDh3cX02DMthVhlvUF6gyEjW8boh/8skn4fFqfvbaa6/wc02HsuI1fk2Xgl4LFy7MVhJY86/3VD71tddeyzFfjzzyiNWvX99lKaqPXi94G/wuP/97J510krvZrxLimg+Vi/3f//5n/fr1y/aZu+66yzVeSIQ+q35ZVU5Yy0E36hWIuummm1zAq2bNmq6vVgWooi3LSKXitZ60vsR7XyV+tV71WW++brvtNhcsUIad+sVNVHBbULanMlVV5lklbL2+idU/rPp0LYrBMBQtCgBPnDjRHVfUV7WOA0XRL7/84vqiXrNmjV1xxRWuxPjHH3/sAqo6NuihihEqlS4Kiiu4qv1N+7mCh2oMtd9++7nMZPX3rCCjgruqNrJly5aEp2XDhg3uGKFqJF999ZXrWkJVNETHDAUYNd5Ijj76aFu2bJkLRGoYVTPR588880wryuvj5JNPdsdUBco1X2+88Ybrd91rfHb++efbLbfc4taRzjE6Z6ikvZahyuL/888/bh15x+xU93GdH+LNs2gbHT9+vHv/6aefdg31jjnmmIS/Q/3VazvW9qJ9eceOHfbrr7+Gry9SQRn6Wh9aL1r+2k/oYxwAAAAAAAAAkpG12mzj62abFdTNMMusaZZR5t+/snnyv+9ruDxShvi8efOS6ttZZXYVeA3egFbwNhL173rDDTfYBx984LISFQzxgrHxXHzxxS4YriCMMhWvvvpq9/rXX3/t/qokubLYVYZbDjvsMBcwWLlypbvBrn5LY5XRHTVqlCvf7fXPqow7j8rJK1Ck71ZQWcEkLSuPxqvAvTLodaNdyyXRvnI9CrYrsKxp0HwoiK1lq4CVV6pVFHQ+++yzEx6vxqfxaNr1V32ofvbZZy6IpeBCs2bNwgGIaMsyHs3/jBkzXJDn/9i7E3ip5v+P45+73277vog2raTNVpQlW0n0y1JEVBSyEyKypaLwC78oKYqy/PiT+FEkKVuFkIr2fV9vd+ne+T/e3+6Mube7rzN3Xs/HY5o7c86cOXPumWnueZ/P5/v111+7+1SJpzaz+l2p64B+X9o++aWxYLXfqMpPvx/56KOP7Keffkr3uwAKk0660Ekluh41apT7/HrllVcsmOhkJ53UozGUNV6yTt7R+0bhs6pu//e//7nXpBN7dN+tt97q3mcye/Zs9/mnE2UuvfRS1y5aJ70ofFVVbHx8vAvIdbKStk1eWkfrRBcFnXpefT4/9dRTds455/hOFNJ21zjiGU/MEQ31oRMV9LmtE3xuuukmd3KOxqYO5t+HTvRRMKyTq9Ra/fHHH3dVx2otLjp57eabb3YnO2kZatutMNb7GanH636Nva7b2jaBLqfXLKp+nzBhgq/Vv0J07QO5pf1X+/KcOXPciXDaLlqmfyv1glInBe9JZe3bt3f/3+rEs8IU+Kc5AAAAAAAAAEB+pew2OzjdLHWXWXgls7Dof6aFhZmFVTDzJJklrzRL2WlWtpdZRO4PFGekQFhy255UFXqrVq06qkJP7dizogoqVVMpNFUFtoIA//E6s6OxaFVBqOpmhVSdOnXKdv4bbrjB97NCWVU///rrr3bGGWdkOr8qHxs0aOB+VtWif7jvrWIUhciapvDYO7/CIIVmCoJVYa8QX9V7qgQsCAVHqkx97733XPW32jjr95SXg+0XXHCBa40vGmNXFXMKxhVGyZNPPumCZlXIq91sfuj1a5t4x2AVBWUaw1XUIlhhhKocVameH6raVwcA/5a3ClIyC82A/PKvIM3Ysj8Y5aYiVuNR66STzGR3go/ej9OnT890WmbbLmO7bNHnsrfLhz8FpN6TbPxPCvKnk6r0OZwZb0cLfzo5KBh+H+oIklVXEH326XPV+9nqTycKBIuM2yG71yw6SWLSpEl5eo6M+4v+79GJZpnR/68Zu7Nk3If0f1jG/dr/deg7gC5FiYpxAAAAAAAAAKVXWISZ55BZWNn0oXi6eaKPTHfzRRTo6byt01XhmxuvvfaaC2hV0ZdbOjCt9utqi61AXKGtwtLc0EFxtUBVW3SFz1pGdkGtKqPV5l0H1BWeqlWtxl7Niqr2vBQU+1eXP/fcc3bCCSe4Cj8tS628/ZcVGxvrWoZ7ecffLQyqDvce4Ne1QvqYmJhcP15V6F5aZ7Uv1kkFeh266HVHR0enax2fV6pQzBhQZzzhIeM2zSv/15HdfQAAlEYE4wAAAAAAAABKr/AKZhF1j4Te2dF0zaf5C0AttVURlVUFoD+12VXrb421m1dqAavxvjXep1r0qi25qP2o2vJ6KcD1tvP1huqqXlTrU41Lfu+997rWpaLxb/2phawuGpNUgbgqBRVq56cCVC3HVTmm59b4rVqWKrn9l6XAXuPdeqm1bW4r7/15K6H9qbW92uz+8ccfriW5fyV8Xpep9rGq6lYLe70O7+XQoUO+ceIzbkv9XjTd//WqBXtO613YMnuO4nheAAACAf/jAQAAAAAAACjdoo5X8m3myWLcVne/xyyqcYGfSoHouHHjXEtsXWtsbu+44BorU23M/cetVsiqiu+8WL58uX3xxRcuaFWVskLXyMgjo2a2bdvWtQrXONUKmjW+tX9Iq2BaYbruU3WyQlG1ZvdWJ/uPX61AXctXFXxSUpIbszS/1cpalp5HlfGqRFfluirG/WldHnzwQfe69BpfeumldO3Xcyvj6xBVvPfs2dONh6rW7Wp77N/GNWOQnR2t56BBg1wbXm+FuH7PCtyzWgedMKE29jrRICUlxf3ulyxZYgWhlspa78xaKwMo/dS2OrfdQhDa1Cnl+eefL+nVCAgE4wAAAAAAAABKt8gGZmExZp6EzKfr/rBYs6gjY10XVLdu3dw4paq0VoW2AujLL7/ctS73jkntbaOuyuXMKnY1TmhmY8aKQuphw4a58FXB+pdffulrE37uuefawIEDXeXy8ccfby1btnQtur1mz57tKswVpl966aX2zDPPWOvWrd20J554wm6//XarXLmyC/Y1trdan6ulecOGDd3Y2hqXPD8uuugitw20PhpTV+OjZxynXOupddFzqU25xl3Pz/jiOhlALeK13TXmuZdOTPjll1+OqhZXZbq30ju3nn76aWvfvr3b3lpvtab3H18447ZUMD9hwgR74IEH3O9M45N7xyzPL623fjf5qaoHACAUhXny0/cmhOhMRrUHUqsgfXkJNTp7U+2LNLYPLXWAwML7EwhsvEeBwMX7EwhsvEeBrIX6cSpRBfTq1atdxa/Go86TA2+ZHV5rFnFkDPB0UnaYRdY3K9e70NYVgUdBssZL37RpkwunvRSU9+rVq8BBdXF77LHH3NjmOhECpVtuPvsWL17sTtDQ0AjqHIG8mTZtmvXp04ftl09sv4Jh+xVMXj7/jvTXAQAAAAAAAIDSLKrJkWBcIXim0wveRh2BS+3LR40a5cZm9w/F5fXXX7dg9Oijj5b0KgAAEFQIxgEAAAAAAACUflEt1Jci7ZJReNp0lEaqtD3xxBNdte2sWbNKenUAAEAJIRgHAAAAAAAAUPqFx5nFnFrSa4ESoED84MGDJb0aAACghDFYFwAAAAAAAAAAAACgVKNiHAAAAAAAAAAAoBTQcAHLli0r6dUIOt9++627ZvvlD9uvYNh+BR8yJbcIxgEAAAAAAAAEldTUzMYJB4DSyePx5DhPYmKiRURE2LBhw4plnUqj8PBwtl8BsP0Khu1XMPr80+dgTgjGAQAAAAAAAASF6Ohod+B406ZNVr16dXc7LCyspFcLAIo0FN++fbv7rIuKispyvpiYGEtJSbGpU6da8+bNi3UdSwNV6iqUZPvlD9uvYNh+BaMq+z59+rjPwZwQjAMAAAAAAAAICgrFGzRoYJs3b3bhOACEAoXidevWdRWROVGo1rZt22JZr9LE276a7Zc/bL+CYfsVH4JxAAAAAAAAAEFDVeLHHXecHT582FVHAkBpp0rx3ITiAIDsEYwDAAAAAAAACCrelsLZtRUGAAAA/IWnuwUAAAAAAAAAAAAAQClDMA4AAAAAAAAAAAAAKNUIxgEAAAAAAAAAAAAApRrBOAAAABxP/A5LXfGRuwYAAAAAAEDR6dKli7388svF9nwjRoyw3r17F9vzIbB1CdH9j2AcAAAALgz3/PG22Yb57ppwHAAAAACA0mv+/PkuGKtcubJVqlTJWrVqZaNHj7akpCTbtGmTde3a1cqWLWvHHXecTZgwoaRXN+hdf/31duedd6a779NPP7VbbrmlSJ5v8uTJ1rp163T3DR061N5++20rSS+++KKdfPLJFhMTY5dddpnv/m3bttk111xjdevWtQoVKlibNm3so48+KtF1LU3Y//5BMA4AABDifKH4gc1mcTXdNeE4AAAAAACl08yZM10ofuGFF9rKlSttz549NmPGDPvjjz9s8+bNrqqzVq1aLqx899137b777rOvv/66pFcbpUCdOnXs4YcfthtvvDHd/QcOHHBh+Hfffef2x8cff9zth9ongcJEMA4AABDC0oXiZaqbRUQduSYcBwAAAACg1PF4PHb77bfb/fff7ypIq1Wr5u5v1qyZq/I8fPiwqyZ/+umnXcX4aaed5ip5J02aVNKrHhAU4A4ePNhV0teoUcOuu+4627t3r5uWmJho/fr1c9u0YsWKduKJJ9qPP/5o//73v23atGmubXW5cuXshBNOcPOfffbZ9vzzz7uf586d6yr3Nc8xxxzjKvk17c8//3S/A1VRq8L64MGDvnXp06ePC5o1rV27dvbVV1+5+5csWWKDBg2ypUuXuufTZd26dTZ8+PB0Vdp//fWXOzmiSpUq1qhRI9+6+Ff8PvHEE+511qxZM930/PrXv/7l1sG733k1bNjQ7r33XlcxHh4ebpdccok1bdrUBeX4B/tf7ui5unXrZv3793fr17hxY/vggw/cNIJxAACAEHVUKB4ecWSCrgnHAQAAAAAodVQhvnr16izH+v3111+tdu3aLojyUkCl+2EueNy1a5fbHtqOycnJLqiUKVOm2C+//OICP1U9//e//3WV9zoRQScXqG21gs3ff/8902Xv37/f1qxZ45arSn0Fxbq89957tn79erfcV155xTd/586dbdmyZbZz507r1auXXX755W4ZqrweP368tWzZ0j2fLgpS/ekECAWHaqGv1vkKDdVK/6233vLNo/WMi4uzjRs3uo4C6hzw999/W3FQtwK9tpNOOqlYni9YsP/9nett9dlnn9mpp57qttfYsWPdZ54eTzAOAAAQgrIMxb0IxwEAAAAAKHW2b9/urlUVmhmFWKoc9afbCrxCnbbd+++/by+99JLbJqqoV8tvhXYpKSkWFRXltpPCQlXmN2nSxI499tg8Pcdjjz1m0dHRdt5557lKWlVOaxmqANa474sXL/bNe8MNN7j79bwKDVNTU3N9AsP333/v2uY/+eSTFhsb6wJoBayq1PVS5fE999zjlq/q4vr169vPP/9sRU3j3CtovfLKK9145DiC/a9+nvY/vf6BAwdaZGSkex3nnHOOG+OcYBwAACDE5BiKexGOAwAAAABQqnhbWKsKMzNqe+xtzeyl2+XLl7dQp2pahX8NGjRwwaQup5xyimv9vWXLFrv22mvt+uuvd22ktZ31844duT+Wom1cpkwZ321Vy/pX7uu2TlwQrcdDDz3kWkSrVbTWRb+n3D7fhg0bXBtshaD+7cx1v5f/c4uC2KI+QUKhuCqP9VonTJhQpM8VbNj/yuZp/6tXr95Rt/W5RzAOAAAQQnIdinsRjgMAAAAAUGqoilKVl9OnT890uio31dpYray9VKWptsihTpWzCiG1fdSq2ntJSEhwFfiqTB06dKhrZ62qXY2rrApc0eMKk1pO6/LJJ5+4QFLroepdVQrn5vk0lrdeh1px+wevur+kKBS/4oor3LUqo/1DU7D/5dXatWvT3db20HYiGAcAAAghng0LzPatN4utmnMo7qX5NP++9UceDwAAAAAAglJYWJiNGzfORo4c6a41PrCsWLHC+vfv78K1M844wwVs8fHx9sMPP9i0adPctFCn8Zovu+wy1/LZWxmrSl2NjyxffvmlO4lA4yerulUtorU9vdWvq1at8gWHBbVv3z4XHKsyWEGyWmr7V9Pq+dSq+tChQ5k+XmMva55HHnnEEhMT7bfffnP7Q9++fXP1/AoxtS/pOi+0bRTk6lpVx/pZ66+AVK3TDx48aB9++KHFxMTkabmhgP0vb/ufPtPUdUDbQwG+ts9VV11FMA4AABBKwup2MKtwrFnCTrPUlNw9SPNp/grHHnk8AAAAAAAIWt26dbNPP/3UhUWNGjVybZDVvrpZs2ZWu3ZtNw6vWg5Xr17devbsaaNHj7azzjqrpFc7IGgMZG8La7WQ7tixoy1atMhN27p1q/Xu3dtNV7trVdA++uijbtqAAQPcNtW4zarKLygFiCeccIJrD60W1GqB7V9te+6559rpp5/uKmS1PqqW9adxm2fOnOnWXYFr9+7d7e6777arr746V8+v5em5sxqrPisaU1rr+tRTT9nHH3/sfr7gggtswYIF9n//93/27bffurBVLf11GTFiRJ6WX9qx/+V+/7vooovsu+++c6/5jjvusKlTp7rW72Gewjo9oJTSWQ/aedQKQDtZqNEZO2qZUqNGjUJvtQCgYHh/AoEtkN+jeWqnrlD80HazcrUtrEVvC4s7MhYZEMwC+f0JgPcokJ1QP04FAMja4sWLrV27di5oatu2bUmvTtBRVXyfPn3YfrmkFt0KNAcOHOhus/0Khu1XsP0v4+ff8OHDXfW8ug9kdKSGHgAAACHDhdsteuccjhOKAwAAAAAAIANvJTIQbPsfp14DAACEIIXcCrsVervwO2NbdUJxAAAAAAAAAKUIFeMAAAAhKsvKcUJxAAAAAAAAAEFIrdSzQsU4AABACDuqcjwlmVAcAAAAAAAAQKlDMA4AABDi0oXj8VsJxQEAAAAAAACUOrRSBwAAwD9t1TcssLC6HQjFAQAAAAAAAJQqBOMAAAD4p3K8SfeSXg0AAAAAAAAAKHS0UgcAAAAAAAAAAAAAlGoE4wAAAAAAAAAAAACAUo1W6gAAAAAAAAAAAKXArFmzbNmyZSW9GkHn22+/dddsv/xh+xUM269gVq9enet5CcYBAAAAAAAAAACCWGJiokVERNiwYcNKelWCVnh4ONuvANh+BcP2Kxh9/ulzMCcE4wAAAAAAAAAAAEEsJibGUlJSbOrUqda8efOSXp2go0pdhZJsv/xh+xUM269gVGXfp08f9zmYE4JxAAAAAAAAAACAUkChWtu2bUt6NYKOt3012y9/2H4Fw/YrPuHF+FwAAAAAAAAAAAAAABS7fFWMq0f7999/b2vXrrX4+HirXr26tWnTxho0aFD4awgAAAAAAAAAAAAAQHEF499++6298MIL9vHHH1tycrJVrFjRypQpY7t27XJhecOGDe2mm26yQYMGWfny5QuyXgAAAAAAAAAAAAAAFG8r9e7du9tVV11l9evXt88//9z2799vO3futA0bNriq8ZUrV9rDDz9sc+bMsSZNmtgXX3xROGsIAAAAAAAAAAAAAEBxVIxffPHF9v7771tUVFSm01Utrkvfvn3tjz/+sM2bNxdkvQAAAAAAAAAAAAAAKN6K8YEDB2YZimfUokUL69y5c0HWCwAAAAAAAAAAAAgK6rj84YcflvRqIESx/xVyMJ7Rnj17bOLEifbggw+6McZl8eLFtnHjxvwuEgAAAAAAAAAAAEVs/vz51qVLF6tcubJVqlTJWrVqZaNHj7akpCS76aabrGnTphYeHm7PP/98Sa8qisiaNWssLCzM5X3F5frrr7fo6GgrV66c77Jw4cJie36E9v6X72D8119/deOIjxo1yp599lnfSv/3v/91QTkAAAAAAAAAAAACz8yZM10ofuGFF9rKlStdxjNjxgzfMLkKyV9++WU79dRTS3pVUQrdcsstduDAAd+lffv2Jb1KCCH5Csbvvvtud1aHPjBjY2N993ft2tXmzZtXmOsHAABQrA4fPmxPPfWUde/e3c4666x8X8455xz717/+5a69911yySU2fPhwd/Y1AAAAAABAcfN4PHb77bfb/fffb3feeadVq1bN3d+sWTObPHmy1atXz2699VY3XK5//oMj9u3bZ4MHD3bbqUKFCnbKKafY+vXrfdN///13a9u2rZumEw82bdrkmzZkyBD3uPLly7shid999908PfeiRYvs3HPPtSpVqlj16tXttttu8037/PPPrU2bNlaxYkX3/LNnz/ZN++KLL+ykk05yz1uzZk27+eab3f3eEx/q1q3rKrenTZvmOkT36NHD10mgXbt2tnbt2gJtMxQe9r+Ci8zPg3788Ud75ZVXjrr/mGOOsS1bthTCagEAAJRMKD506FB3ot+VV15pNWrUcC198vuHZkpKikVERLhl6PbOnTvdGdg6E1vtydQ6CgAAAAAAoLio4HH16tXWu3fvkl6VoKSi0fj4eNf+u1atWvbLL79YmTJlfNM1BPGnn35qxx13nAsA+/TpY19++aWbpkr8e++916pWrepCyWuvvdZOPvlka9CgQY7Pq2GMFUo+/fTTNmvWLEtNTXVBpfz111926aWXumBRhR4aZ1rXCkm17L59+7oO0Hq+gwcPunWWH374wU3fsGGDCyFFx8V0fEzPFxMTY0uXLnWBZmF644033KV27drWr18/u+uuu1zbfuSM/a+EgnGtjM5KyGjFihXuLAEAAIBg9PPPP7sviyNHjrTzzjuvQMtSEK4vcpGRkenCdZ3pqDOyf/rpJ+vQoUMhrDUAAAAAAEDubN++3VfoiLzZunWrffDBB66CtU6dOu4+Vcn6Uxip6ntRUYTCSwV/qoq95pprfPP16tXLHX9asGBBroLJqVOnumNKakPu1bFjR3etIoyzzz7bdS6Uyy+/3F599VV7++23XdAYFRXlwkv97pXhZXc8SvOqsEMnUChIbd26tRUmdSt45plnXNWxinBVmKJQXOE4ssf+VzjydQqGkv7HH3/ckpOT3W0d7F23bp1rvdGzZ89CWjUAAIDipXG0RG3Pi4q+/KmK3L+VEQAAAAAAQHHwtk5XRSbyRoGkCkdVjZsVtar2Uttoze/d1s8995ydcMIJrt20KmR/++0327FjR66fu3HjxplOU/BZv379dPc1bNjQ3S8KU/VcTZs2dUHqO++8k+Xz3HfffS7wVGCtUPWOO+6wQ4cOWWFRm22Fozo2dvrpp9sDDzzgglXkjP2vBIPxMWPG2IEDB1x7Ua2QDh4ff/zxrpxdY3ICAAAEI7U+956dWFR0FqyqyL3PBQAAAAAAUFyaNGniQqzp06eX9KoEHYWOiYmJ6cZ0zsh/PORt27a5+VWdP3/+fBs+fLhrIb579243zN6JJ57oOg7m9rlVdZsZVQOvWbMm3X26rfu9YfT777/vQtBhw4bZ1Vdf7aqPM2tfrrGe1fZ6+fLlrl33nDlz7OWXX7aiQgv13GP/Kxz52uN0NoEGS585c6b9+9//dgO9q6f8119/bWXLli3UFQQAAAgECQkJNnbsWDvttNOsQoUKFhcX5/6YHDhwoK1atcpmz57tzmjUWa8aO1xnZaod+//93/+V9KoDAAAAAAD4OgCPGzfOtVHWtdoWe4fK7d+/vwvWkpKS3HEQjSOsYeL0s65DnY71aCzlQYMGua6D2j5LlizxbUN55ZVXXKinolJ1We7UqZMLCDU8saqkddxIj5s0aZKrovWnExYmT56c6XOrDbbGZB4/frwLOzXO9DfffOOmXXXVVTZ37lx3DEq/p//+9782b9481y5bv8s333zThaEKIr1jOatoQ+ui+/7++2/f8yj3076gddTxLxWPaF7RumWsDM4rVQtrWyiQ1TCD2g/pRJ07ob7/FZYCnYpxxhlnuH7yQ4YMKfA4nAAAAIFKX97UAv2ee+5xXwKlUaNG7uxGjZmjL3v6MqmL2vyoLdH+/fvd/fpyr/F6AAAAAAAAAkG3bt3s008/tU8++cQd31BYpXGBNTZx7dq17YILLrAyZcq44EutjfXzk08+WdKrHRCmTJlixx57rJ188sluuymk9G/13K9fP+vdu7cLMdXCetq0ae7+iy66yG3jli1buvGhf//9d5exeSlsVEWt2otnRuGmqmffeustt2yFmO+9956bpo7OCiMfffRRN3a3hkJW+2q1sxY9xtv1+bbbbnO3q1at6n6vekyXLl3ca9H9qgrWumreFi1aWPv27d241aIhlf3XOT9efPFF1wpcy1fYqoxRx9uQO6G8/xWWME9u6+T93H777e5F6DrjDq2Vfv7556200FkUqpDfu3evOzsh1OisDLVbUNt8WloAgYX3J1D4PvzwQ/eHns5Y9acv6vpyJvqDcMSIEb6zFRV+6+xFtQXSuD1eqiA///zz3c/PPvtsui/5+uKp71E6oxJA8eP/UCCw8R4Fshbqx6kAAFlbvHixtWvXzhYtWuSOUSBvFCD26dOnxLafOjKrGvftt9+2QNW5c2eXAzZv3jzgtl+wK+nt93UQ7H+F9fmXr/pz9YL/6KOPjrpflVRqe1CagnEAABDadNBRbZ6kVatWbpwbtR3zUksiL7UbU9itsyzVtkh0QF/fkQAAAAAAAIDMnHXWWe4SyFQxjNLprCDY/wpLvoJx9avX2akZ6UxVldoDAACUFhrXxjuOlsYQ9w/FM1Lrou+//953u2zZsm7MHrX9CRSe+B3m2bDAwup2sLC4aiW9OgAAAAAAAABQLPLVk0xt1D/77LOj7teYFN6e8QAAAIFErXRuvPFGmzVrVrbzqdp7zZo1dvDgQXfbf9SZ7EJx0Vhcml8nET799NNuGQMHDnTtfAImFP/jbbMN8921bgMAAAAAAABAKMhXMH733XfbkCFD3KDo6juvyyOPPGIPPPCA3XXXXYW/lgAAAAWgccDV8nzixInWrVs3V8WdGY0LvmfPHtcBR63Q9+/fb02bNvWNJz5//vx0QXlWqlSpYvfff79VrlzZLU9jjAdMKH5gs1lcTXdNOA4AAAAAAAAgVOQrGO/Xr5+NGTPGXnvtNTvnnHPcZerUqfaf//zHVWIBAAAEUijepUsXi4+Pd7cVbA8YMOCocFy3X3rpJd/t1NRUW7lypRsj/Morr3T3LVmyxIYOHeprre4N0xcsWOBC9127dvnu130KxcVbfR4QoXiZ6mYRUUeuCccBAAAAAAAAhIh8BeNy880324YNG2zr1q22b98+W7VqlV133XWFu3YAAACFGIrXS7s/Yziua932is4Qjo8YMcJat27t7hs5cqRVrVrVWrVq5SrDzz//fDcO+ZNPPmk1atSwxo0bW4sWLdx45N7q8pL8jnRUKB4ecWSCrgnHAQAAAAAAAISIfAfjXtWrV7dy5coVztoAAAAUUSje1cz+NLO7MoTj119/vbv2hthxZnaimVX0C8c1Zvjnn3/uWqKfcsop7j61WlerdD1Wbdp79eplzZs3t23btrmgXOH5BRdcYJ988on17NkzsEJxL8JxAAAAAAAAACEiX8G4qsSvvfZaq1OnjhtzMyIiIt2lKKnFaf369S02NtZOO+00++GHH7Kcd8KECa5aSwetdTnvvPOynR8AUHwUwKWu+IggDkVi0aJFR4Xi75tZrJmNyRCOT5kyxReKdzez8mlfkBplCMfXr19vgwYNct8lNPZ4QkKC/f333+77RsOGDV0l+dKlS23v3r2u1boC8pkzZ7r1CMhQ3ItwHAAAAAAAAEAIiMzPg1RZtW7dOhs2bJjVrl3bwsLCrDjMmDHD7r77bhs/frwLxZ9//nm78MILXcWWWpdmNHfuXOvdu7d16NDBBemjRo1ylVu///67HXPMMcWyzgCAbAK7fevNs2+tWYveFhZXraRXC6WIviv4t0/3huISlhaOy3N+j7nbzM40s6fSbnvD8d/MLCktHN++fbuVLVvWAl2uQ/EswnHekwAAAAAAAABKm3wF4/Pnz7dvvvnGN9ZmcRk7dqzdeOONdsMNN/gOeqs9qcYFfeCBB46af9q0aeluT5w40d5//32bM2cO46EDQCAEdnE1CeJQJHr06GGvvfaaqwRfa2YPmdmzaaG4fzhexcxeM7P+afN8nDZdFd8RkZG2MS0U91IHmoJSwJ6SkmLh4QUe0SZLng0L3Ikn7j2WUyjupfliqx45YWXDAgtrovp5AAAAAAAQTGbNmmXLli0r6dUIOt9++627ZvvlD9uvYNh+BbN69eqiDcaPPfZYX8vR4pKUlOTaoj744IO++3RAWe3RFy5cmKtlqHIsOTnZqlTRYXAAQEBUsVKliiLQtWtXd0Kcd+zwsWn3ZwzHH067eNXSP4mJ9t3vv1u9Vq1sq980DeVSsaK3uXr+LV682AXv6rpTVMLqdjjSjSG3FeOSmmKWsNOswrHu8QAAAAAAIHgkJia6oW7V6Rf5o8yJ7Zd/bL+CYfsVjD7/9DlYJMG4WpirQvuVV15xB4mLw44dO1x1Vc2aNdPdr9t//vlnrpZx//33u3HRFaZnRRvNf8Pt27fPV92lS6jRa1agEIqvHQh0wfb+9MTvNM+y6f8EdWERZjrHStex1c32bzb7/W0La97LwuKqlvTqohTQ0C9y0003uffK82n3j/YLxzM6yeOx03btsltGjLBLevVyFeIaMkZDtmzYsCHP66AAPDLyyNctrcPOnTvtjTfesDZt2rhLkb1/Y6uYp1mv9O+57MJxheKHtpuVq21hzXodeXyQfLYAofB/KBBqeI8CWeN9AQDISkxMjMtQpk6das2bNy/p1Qk6qtRVKMn2yx+2X8Gw/QpGVfZ9+vRxn4NFEoxfddVVrvq6UaNGFhcXZ1FRUemm79q1ywLNyJEjbfr06W7ccY03npWnn37aHnvssaPu15iiCQkJFop/cO3du9cdlCjKlq8ASvf705O4zzzr5pklHDKLrmeWnMn6hseZ7dlrtuT/LOy4ThYWU6EkVhWlsHL89ddft3Hjxrnb35jZi/ouk81jGqam2oebN7uKc73PFI7r+05+6PH+789y5cpZy5YtbciQIbZ//353KUqeGheaJ2Ge2f5dZtFxOvU0s5U0S9prFtvQwmp0srADKWYHthXpegElLZj+DwVCEe9RIGtF/f0RABD8FKq1bdu2pFcj6HjbV7P98oftVzBsv+KT74rx4latWjVXBr91q39TU3O3a9VyjU+z9Oyzz7pgfPbs2XbSSSdlO69atd99993pKsbVOr569epWoUKFkDwgoUo5vX4OSACBJVjen65SfP3/zA5vNiuvqtVs2pnERJkdWmW27RCV4yg011xzjX355ZeuUlt2mtlt2cz/kpltSfv52muvdWOV5/c9qhPrMr5H9b4tPjXMU7VyWuX4WrOYDJXj3krxSrUtrPmlvOcQMoLl/1AgVPEeBbKWXbEHAAAAgCIIxvv27WvFLTo62tq1a2dz5syxyy67zPfHsm4PHjw4y8eNHj3annrqKfvf//5nJ598co7PozL7zErt9cd4qP5BrgMSofz6gUAW6O9PjSluf063sIObzeJyMc5xRMSR+TS/HseY4ygEkyZNsilTpriqM7lB/69nM38/M/OO5qMwvVOnTtavn+7N3/tTJ/aV6Hu0XHXznNDbPH+8nb6tuhtTfLtZ+dq81xCSAv3/UCDU8R4FMsd7AgAAAMi/An+bVntxVVX7X4qKKrknTJjgDm6rrcDNN99sBw8etBtu0CFus+uuu85VfHuNGjXK9eTXAXGNhb5lyxZ3OXDgQJGtIwDgH54NC8z2rTeLrZpzKO6l+TT/vvVHHg8UgL4DDBgwwBeKqyfMQzk8RtPvSvtZj9PjtZxgptBb4bfGEHcV4inJ/4wpTigOAAAAAAAAIATkq2JcYfT9999v77zzju3cqYak6aWkpFhR0Njmakn6yCOPuIC7devW9tlnn1nNmjXd9HXr1qU7c/Y///mPJSUl2eWXX55uOY8++qgNHz68SNYRAPCPsLodzLNvbfoq1Zy4KtadZhWOdY8HCjMUf1b7ZQ6P0/QxaT8/5xeOS34qxwOFC79bpFWO64QVvccIxQEAAAAAAACEiHwF40OGDLGvvvrKBc8ae/Oll16yjRs32iuvvOLG8i5KapueVev0uXPnpru9Zs2aIl0XAEAegrjchOPe8Y6pYkUBzZo1K8dQXFOeMrOJZjYgrVI8LIdwvFatWta1a1cL+vfkhgXuxBPeYwAAAAAAAABCRb5aqX/88cf28ssvW8+ePS0yMtI6duxoDz/8sI0YMcKmTZtW+GsJACg9LZwVfmeGUByF6IMPPvCF4vXSAvCMofg9aWOJr027vjftfi/NPyLt8e4xHo9bbrDTeyu8SXfeYwAAAAAAAABCSr6C8V27dlnDhg3dzxUqVHC35cwzz7R58+YV7hoCAEp/OE4ojkI2aNAgi4uLcz8r+O5pZgkZQnFVgvsbmyEcT0h7nB4vWp6WCwAAAAAAAHh16dLFFZPm5Oyzz7bnn3++0J5XhaodOuQ8FOXkyZPd0MSFTcfJNOwySlao7n/FGowrFF+9erX7uVmzZm6scW8leaVKlfK9MgCAEAzHCcVRBNq1a2effvqpLxyf5ReO+4fiYWFh1rdvX3ftH457Q3E9TrQcLU/LBQAAAAAACHbz5893gVrlypVdrtOqVSsbPXq0rVixwnr06OGGk9P9Z5xxhn377bclvboBTceMbrnllmJ/3muuucYWLFhgJWX8+PE2atSoPD3m+uuvt+joaCtXrpzvsnDhwiJbx1AQqvtfsQbjN9xwg/3yyy/u5wceeMCNMR4bG2t33XWX3XffffleGQBAiIXjKcmE4igynTp1Oiocb5YhFJ84caI7a1HX/uF4s0xCcS0PAAAAAAAg2M2cOdOF4hdeeKGtXLnS9uzZYzNmzLA//vjDNm/e7KYtXbrUdu7c6YLMrl272o4dO0p6tQOOht1LScli2MgilpycbMFKIe6BAwd8l/bt25f0KgUl9r9iDMYVgN9+++3u5/POO8/+/PNPe+utt2zJkiV2xx135HNVAAAhF47HbyUUR7GG42szhOL9+vVzt3XtH477t08nFAcAAAAAAKUpTFO+oxbYd955p1WrVs3XHVjFA2eddZbddNNNVr16dYuIiLAbb7zRXf/6668lveoBoX79+vb000/b6aef7o4b6WQC/xbVGnpYFffeSnx1H1y71nuk6R8KhHVigqpucxMyDh8+3Lp162Y333yzValSxRWtZmxRPXbsWDvuuOOsfPnybj11rCurSm91hla2l5iY6I6LaT+oWLGinXjiifbjjz/malvopAntQyg+7H8FF1kIy7B69eq5CwAAueFC8Ba9zbNhgYXV7UAojmIJx3W2c3x8/FGhuJf39oABA9wfiYTiAAAAAACgtFGFuIbK7d27d67mV+X4/v37rUWLFkW+bsFCgeBHH31kxx9//FEVu88++6wdPnzYNm7caDExMW77KSj0t337dleF37FjRxszZoyvUCMnn332mTumNW7cOEtKSvINcyxqgf/www/b4sWL3UkOW7dudZeMHn30Ufvvf/9r33zzjR1zzDH26quvug7Rf/31lwsmtX+UKVMmz9vk4MGDuZ73jTfecJfatWu743Eqxg0Pz1cdb0hi/yumYPzf//63O0tILdP1c3a81eQAAGRbOd6ke0mvBkKEwu158+a5MxJ11qS+/GVGX8Y1htYHH3xggwYNYkxxAAAAAABQqigUE4VSOVGL9V69etnQoUPd8RIcoarZpk2bup9VTe8vKirKtaBXwKdx2/0ramXVqlVu3HYVZgwZMiRPz6tqWlVpS2Rk+nhP66FCj99//90VstasWdNdvBSgKuNbtmyZO0amimLv+urEB91/2mmnWZMmTSw/1FE6N5QfPvPMM67qWJXBV155pQvFFY4jd9j/iikYf+6551xJvYJx/ZwVnVlAMA4AAAKNQu4JEybkOJ9C86yCcwAAAAAAgGDmbZ2uitJGjRplOd/evXtdq+UzzzzTtVHGP9QuOiv33XefJSQkuMBX2/Cqq66ykSNH+qpgVWWrFtcKNwvzefW7nDJlir344ot2ww03uFbbo0eP9gWj69evd2GpKo29oaRce+21blx5FYhonu7du7uqY+9+klu7d+/O1Xxt27b1/ax1VEtuVY8TjOce+1/B5Lo3gVprVK1a1fdzVhedbQAAAAAAAAAAAIDAoopMjf87ffr0HEPxE044wXXfy22r5VCRXdvvcuXK2ahRo2z58uW2cOFCmzNnjr388su+6arSbd++vdu++/btK7TnFYWhX331lWthrWphhY5e+p2rQ+LVV19tc+fO9d2vyl91BFA7a1Xtrlu3zh577DHLK/+wMy9ooZ537H8Fk+c9ToOwK/nXCgIAAAAAAAAAACA4KOTWGMGqItW12i57xwju37+/rV271i666CIXoGs8YULxvJk5c6bblqmpqVahQgXXKtq/7bTCxddee82N2X7BBRe4kxBkzZo1blvrOj8UhH7xxRd26NAhi46OdgFpxnbXXbp0sWnTptnll1/uAlP58ssv7eeff3bjUpctW9Z1jfY+TgFmbn//GVt2Z0UVywpk1Xb7p59+cvthz5498/x6Ufr3v4AJxrURVYYPAAAAAAAAAACA4NKtWzf79NNP7ZNPPnGFkGqtrLCqWbNmLgz97rvv7P3333fBmgIuXRRoIWd//fWXO7GgfPnyLnxUdW7GttUKJzXcn8Lk8847z7UhV6WsxmbOzdjvmUlKSrJhw4a5cZ3V/VmB4+TJk4+aT5XC6hagFtuff/65q+7t3bu32wcaNGhgFStWtEcffdTNq3Xq0KFDrp5f+0huqNW2WnJr+2j45ltuucXuueeePL5ahML+V1TCPDotI49GjBjhzjjQ2UJFndyXNJ25ol+EzprQfwKhRmeVbNu2zWrUqEFLCyDA8P4EAhvvUSBw8f4EAhvvUSBroX6cCgCQtcWLF1u7du1s0aJF6cZxRu4o+O/Tp0+JbT+1j65Vq5YNHDiw2J87KxorulevXi7MDPTtF+xKevs9FoD7X1F9/uUr1f7xxx9dmbvS/JYtW7rydn///e9/87NYAAAABEggoTZGGzdudK2MCovCDZ05qnGGypQpU2jLBQAAAAAACGZFXSWbH6+//npJrwJCeP8rKvkKxlXSTs9/AACA0hmKP/74425MoqKiYFzjmMXFxRXZcwAAAAAAAABAgYNxzhIBAAAonRRYz5o1y4YPH24XXHCBRUdHF9qyU1JSXCX63XffbUOGDHHjSgEAAAAAAABAcWCwLgAAAPh8//331q1bN3cpzFBcIiIi3Hg/gwYNsp9++slVpwMAAAAAAABAwFaMy3vvvWfvvPOOrVu3zpKSko4a5BwAAADBZ+/evVa9evUifQ4tX2OXHzx40MqXL1+kzwUAAAAAAAAA+a4Y//e//2033HCD1axZ05YsWWKnnnqqVa1a1VatWmVdunRhywIAAASxsLCwo+5LSEiwsWPH2mmnnWYVKlRw44M3adLEBg4c6L4Dzp071z0us8vs2bNzXD4AAAAAAAAABFzF+Msvv2yvvvqq9e7d2yZPnuzGiGzYsKE98sgjtmvXrsJfSwAAAJSY3bt3W+fOnd0JkaIq70aNGrnOQfpO2L59e6tfv76bpvbrbdq0Sff4ihUrlsh6AwAAAAAAAECBgnEdBO3QoYP7uUyZMrZ//37387XXXmunn366vfjii/lZLAAAQNDSeNnx8fEWExNjUVFRVpoMHjzYF4rfd999NmLECIuMPPI1ct68ee71JiYmutu1a9e27777rkTXFwAAAAAAAAAKpZV6rVq1fJXhxx13nO/g5+rVq83j8eRnkQAAAEEbiOskwc2bN9uOHTvcuNmlbczxd955x/3cqlUrGzVqlC8Ul06dOrmKca9NmzZZpUqV3EUnTL733nslst4AAAAAAAAAUOCK8XPPPdc++ugj1yZTY43fdddd7qDnTz/9ZP/617/ys0gAAICgC8QVgu/bt8+SkpLcuNml8QTBFStW2OHDh93PHTt2zHF88Bo1aljVqlVt+fLl9v3339sVV1zhhuG5+eabi2mNAQAAAAAIXbNmzbJly5aV9GoEnW+//dZds/3yh+1XMGy/glHhdpEG4xpLUgeD5dZbb3UHPxcsWGDdu3e3gQMH5meRAAAAQRuIq5W4rnW7tPEP+7MLxU844QT766+/3Njj3qF3Tj31VNu6dauNGTOGYBwAAAAAgCKkIc4iIiJs2LBhJb0qQSs8PJztVwBsv4Jh+xWMPv+8Qz0WejCuX44uXr169XIXAACAUAzEg8WiRYts/Pjx1qNHD+vatWuW8x06dMjWrFlj1atXt6ZNm7rW6aoanz9/vgvKM3vNmlcXLw23c+aZZ9r777/vQnIAAAAAAFB0YmJiLCUlxaZOnWrNmzcv6dUJOqrUVSjJ9ssftl/BsP0KRlX2ffr0cZ+DRRKMH3/88e4Jrr76amvSpEl+FgEAABAUSkMgLvPmzbMuXbpYfHy8vfbaazZx4kTr16/fUfNprPRt27a58dJ37dpljRs3tiuvvNLeeustW7JkiQ0dOtSeeOIJ3zjjs2fPtri4OFctrhD9tNNOc/dv2LDBBelSv379Yn61AAAAAACEJoVqbdu2LenVCDre9tVsv/xh+xUM26/4/FP2nQdqn/7JJ5+4X9App5xiL7zwgm3ZsqXw1w4AAKCEKUjevXu3JSQkuDA42ENxUdX3gAEDbNKkSenm022NKe5/UsDKlSttxIgR1rp1a3ffyJEj3TA6rVq1sipVqtj555/vHvPll1/a6aef7qrGNU2Butqoy0MPPVSsrxcAAAAAAAAACiUYv+uuu+zHH390ZzCoDedLL71kxx57rF1wwQX2xhtv5GeRAAAAAUnV0JUrV7bY2FjXTjw5OTnduNvBForXS7s/Yziua932vrZov3B8586d9vnnn9uzzz7rTorUfcuXL3fbRY/p1KmTXXvttXbFFVdYuXLlXFBesWJFO++88+yLL76wvn37lshrBwAAAAAAAIACtVL3Uhv1xx57zF2+++47u/nmm+2GG26w6667riCLBQAACBjh4eFWvnx5K1u27FEt1VVBHsjV4xlDcY0q/r6ZDTWz5/zCcc2nkxu9oXh5MzvRzP42s71p4fj69evtpptusnvuuSfT52rYsKF17ty5WF8fAAAAAAAAABRLMC4//PCDG3NyxowZ7kCxKoUAAABKm2ALyBctWpRpKB5rZmPS5vGG41OmTPE97lgzq5zWVqhRhnBcbdU1jri2AQAAAAAAAACU+lbqao/56KOPuorxM844w7VUHzVqlBtHcvr06YW/lgAAAAEWkNeuXduqVavmxhxXe3VdAsn48ePTtU/3huISlhaO35XhMXenheHeiN8bjvu3Vd++fXuxvQYAAAAAAAAAKNFgvFmzZvbZZ5/Zrbfeahs2bLD//e9/rn26xpQEAAAI1YA8kKrGe/To4VuftWb2kKrD/aZ7w/EnzKx+2vWzZlY2NdX2HTzo5tH8G80sye9xGle8oPbu3eu2n8ZtBwAAAAAACBb169e3Dz/8sNCWd+edd9r1119faMtD6cb+V0LB+PLly+3777+3O+64w2rWrFkIqwEAAFA6AvJAaTPetWtXmzhxoi8cH2tm92YSjj9sZqvTrnW7RUKCzfzsM/tl5UrbYGZbM3z5rlixYoHWa9OmTfb6669b48aN3ckEAAAAAACg+M2fP98NwaYT4CtVqmStWrWy0aNHW2Jiop199tlWo0YNq1ChgiuUfPXVV0t6dZELkydPttatW1uwOHTokB1//PFu/0Pwmxwg+19O65GvMcZ1IBMAAADpA/JA657Tr18/dz1gwAA3lrjCcUurDM+qtv1+VYmvWmVX3nefla9Vy6KjjzRS1x/KcXFxBVqfw4cP25YtW9yJlWPHetcGAAAAAAAUp5kzZ1rv3r3tiSeesDfffNOd6P/nn3/ayJEj3d/t48aNs+bNm1tkZKT98ccfds4557jbHTt2LOlVRynyyCOPWL169WzHjh0lvSoIIfmqGAcAAEBwUDiesXL8qWzmL2NmdVJSbMXGjbZo0SJbuHChHXfccXbeeedZhw4dCnTRGedq0aQzzek6hHRSD5olLj5yDQAAAAAoMjpx/vbbb7f777/f/Y2uUFxUGa5KSwWVLVu2dKG46HiCLn/99VcJr3ng+P33361t27auov7CCy903fFkzZo1blvt2bMny1bV8+bNc9tXxRX/+te/bP/+/emW7Z2u7oSa3r9//3SP//vvv+2SSy6x6tWru9/Vk08+aampqbZkyRIbNGiQLV261C1bl3Xr1uXq9bz99tuuY4Bej5ap/cC7r4wZM8YaNWpkVapUsYsuushWrVrle5yKHnTMSOuq15kXOuakIZu1HyJv2P+O3v+6devm7tMJPjmuhwfZ2rt3rzqOuutQlJKS4tm8ebO7BhBYeH8CgS3Q3qN9+/Z132l0qa+vgNlc6qXNp4seBxSp5LUez74JHs/uJ49c63aIvT8BpMd7FMhaqB+nAgBkbdGiRe7/CF1nZ/ny5W6+v/76K9v5Lr74Yk9MTIyb96STTvLs3r3bU5pNnTo1V9uvXr16nvr163uWLVvmOXjwoOe6667znHPOOW7a6tWr3TL8t9Udd9zhO7aya9cuT8WKFT3jx4/3JCcnez766CNPdHT0UdNfe+01N/2TTz5xvwPvdD2fnv+5557zJCYmetauXes54YQTPBMnTnTTX3/9dU+rVq3y9Lq1DlWqVPHMmTPHff/eunWrZ/HixW7alClTPHXq1PH8+uuvnkOHDnnuvvtuT4sWLdy6aT8qU6aM2w7y0ksv5Wr7iR7fpk0bz9dff+356quv3GsOdex/cwq0/33++ee+7ZfTelAxDgAAUMpNmjTJ3njjDd/t/jnMP8DvZz1OjwcKnSfFLGGh2cF3zVK2m4VXPnKt27pf0wEAAAAAhWr79u3u+phjjsmx3frBgwdt7ty51rNnTytTRj3mIDfffLOrsNeQcxqX/auvvrINGzbk+Dht0zp16tjAgQNdRb4qb88999x00+vWreu6/2l6165drXPnzr7pn3zyiRvqTlXAGvpO1bJ33HGHvfXWW/l+LS+//LJbhtZDwwRqbPk2bdq4aWqzr+4CqiCOjY21ESNG2Pr16+2HH36wiIgIV9Gr6mWNFV6xYsVcP+czzzzjnqNTp075Xu9Qxv73w1H7X9WqVXP9nIUSjOvDcd++fYWxKAAAgJDiid9hqSs+ctdFQaG2d4xxudvMHsrhMZp+l3f9PB73eMJxFKrUPWbx75slfHVkxPvwqmZhUUeuJWHukemaDwAAAABQaLyt0zdu3JjjvAqfzjrrLNu6dasLM3GE2j17aai4mJiYXG1Ptbz2f2zGZWn6sccem266wkcvtcr+7bffrFKlSr7LPffc48aFz6+1a9da48aNM52msLV+/fq+23qdClZ1v9pbT5kyxV588UW3DTQ+fW6oJf/48ePZnwqA/W/DUfvfLbfcUjzB+B9//GEnn3yy69+uswSU2mtcAAAAAORMYbjnj7fNNsx314UdjmcWij97JIbMlqaPKQXhuMY4OnDggCUnJ5f0qsBfym6zA2+bJa80C69oFl5eg9Ydmabr8ApHLpqu+TQ/AAAAAKBQNGnSxIVN06dPz/Vj9Hf1ypUri3S9gonCPK9t27ZZYmKiq8DXeMYSHx/vm75582bfzwr1/B8r/uMfa7oqYrOartCyXbt2bgxp70VFq6qaFVXc5pWC0azGj1f1sMJQr6SkJBee6n658sorXbWyTpzwD1CzM3/+fDe/9kOdpHHppZe616Cfv//++zyvfyhi/6t71P6n/ckrp/UoUDCucvvBgwe7A447d+50A7Ffd911BVkkAABAaIXiBzabxdV014UZjs+aNSvHUFxTnjSz+mnXR+bMPhzXcoMhEN+/f7/78r9jxw7X3QgBJCzCzHPILKysWVh0FvNEH5nu5oso7jUEAAAAgFIrLCzMxo0b5yp8da1sR1asWGH9+/e3r7/+2r744gvXnvjw4cOuffK0adPswgsvLOlVDxivvPKKLV++3G2j+++/37UEV1incFcBsSpZdWxCoZ3/cZSLL77YVfZOmDDBt22//PLLdNMVTE6ePNlN/+yzz9JN79atmwsB1X46ISHBUlJS3Hqo3b2oclbHQrReXgoW9Tv3Dxgz5nwvvPCC+71rnRW0LlmyxE3r06ePq8hVkazC14cfftgFsKeeeqp7Xu9+orbaanWdGwozFYT+/PPP7jJx4kRXfKufvS20kT32v1OP2v/UVt4rs/XIdzCuMzf8y/E1FkX37t3dE6pkXv3mtVEAAACQy1C8THWziKgj14UYjn/wwQe+UFxNkZ7KJBS/x8yG6UzTtOt7MwnHR6Q93j3G43HLDZZAXGe0e7cBAoiqwSPqHgm9s6Ppmk/zAwAAAAAKjQKuTz/91AVjakmsfOfyyy934xZrLPGhQ4e6cEnj9urnsWPH2tVXX13Sqx0wNAZz79693TZSZqYTB7zUbe/11193Y24rwOzVq5dvWpUqVez//u//XBCoba5Q+Jprrkk3/cMPP7Rnn33WTX/11VftiiuucC2kRRXBs2fPtjlz5riqf/1+9HvxtrLWOM2nn366Cw/1eFX76qKq3KzGlL/sssvc7/fWW29163zKKafY0qVL3TQVwt52221uf6lVq5b98ssv9vHHH7vxp1W9O2zYMN9+ovAyN5QnKsT1XqpXr+6CU/2sgBM5Y/+LPGr/+/HHH33LzGw9/IV58nC08N1333WJvFZQK/PSSy/ZU0895caY0IFHnTmgfvKap7RQGwD9Mvbu3WsVKoTeQTnvGRoa8D4/bRAAFB3en0BwvkePCsXD/aphU1PMDm03K1fbwlr0trC4I+N+5ZknxRYtfMM6nT/Q4uOPtBHvambvm1msXyj+XCYP9a8sTzCznqo+9/vjZd68ea5tUqBta1WF63ubvhjrDyp9Sda1bmvIH30RRgBJXGx26LO0scUz+T/Mk2qWutOsTBezmMI/Y5z/Q4HAxnsUyFqoH6cCAGRt8eLF7u91DXfbtm3bkl6doKNwURWqgbb9VKmviuCHHnooX49/7LHHXKioytxQ3H7BIlC334VBsv/l5fMvT39h6syAH374wZ35obT9jDPOsM8//9xdd+zY0f1cmkJxAACAwpRtKC66XdDK8dQ9ZvHvW7sWm+3Td2+0uLgjZ9vOSgu5EzKE4gqP+/bt665lbFrleGahuM4mD6RQPLMK8aioKHfxvh4EqMgGZmExZh7taZnQ/WGxZlENinvNAAAAAAAoMcrZdIxDraw1DrwKUjWMcX49+uijRR5KovT4PAT2v8i8PkBnpY4fP97mz5/vDqKef/759sQTT6Tr3w4AAIA8huJZhOOWl8rxlN1mB6ebpe4yC69knTpWt0/fv9269Py3xccnuZC7WVrrdFF4rLZJasGksz+9Y5KPTasuX5shFNc8gVohThgeZCIqm0XUNjusvSyTvyM88WaR9d1+DAAAAABAqFDFq9pbx8fHW4MGDeztt9+25s2bl/RqIUQsCoH9L889yXbt2uU2TMuWLd212ja1adMm3QDuAAAAyEcoXtDK8bCII+Myh5U1CztSKd7pjCYuHPdWjqcLxV8d50Jx0bVCcm+4HKihuOjL+e7duy0hIcG1TCcUD1JRTY5cp+w4+uKmNy7R1QMAAAAAoLg9+OCDtn37dlcQ8Ntvv7mx34Hi8mAI7H95Csbfeustq1u3rl188cVusHQdJFUZvAZrHz16tF155ZW2devWoltbAACAIOTZsMBs33qz2Ko5h+Jemk/z71t/5PG5ekwFs4i6R8JxPxnDcReKvzjQ+g24Nd18GcPxQAzFveulccNjY2Ndaye1UFelO4JMVAuzMp3NypyTyaXzkekAAAAAAABASbRS15kCkyZNsl69erlqcR087d69uzVr1szmzp1rEyZMsPbt29uqVasKa/0AAACCXljdDubZtzb3FeOSmmKWsNOswrHu8bkWdbzZ4b/MPKlmYeHpwvF5n95n41+baz0ubmRdL70p04fr+12tWrXsgw8+sEGDBgXUmOJe4eHhVr58eStbtuxRLdVVQU71eJAIjzOLObWk1wIAAAAAAAAhIk/B+IEDB6xp06bu50aNGrk2lv5uvPFGu/TSSwt3DQEAAIKcGyO8Re/ct1NXKH5ou1m52haWlzHGJbKBWViMmSdBT5xuUrs29WzCv69Q72qzqAZZLqJr167uEugIyAEAAAAAAAAUSSv1vn37ujbqV199tZ166ql27bXXHjVPjRo18rJIAACAkKBwWyG3wm4Xeiv8LuxQXCIqm0XU1sDmmU/X/ZoeXslKC29AXrt2batWrZobc1zt1XUBAAAAAAAAgDxXjI8dO9bOOecc+/PPP+3666+3Cy64gK0IAABQWJXjBQ3FvaKamB1ea5ayI4vpja00yqyCnHAcAAAAAAAAQJ6DcbnkkkvcBQAAAIUYjhdWKC5RLZSyp10yCk+bXnr5B+Qa+icmJqakVwkAAAAAAABAsAXjhw4dskWLFlmVKlWsRYv0B1UTEhLsnXfeseuuu64w1xEAAKB0h+OxVc0SdhZOKC7hcWYxp1qoU0Berly5kl4NAAAAAACKzaxZs2zZsmUlvRpB59tvv3XXbL/8YfsVDNuvYFavXp3recM8Ho8ntzOvWLHCtU9ft26dhYWF2ZlnnmnTp0934znK1q1brU6dOpaSksWYmUFILTgrVqxoe/futQoVKlioSU1NtW3btrmx43VwGUDg4P0JBP971BO/40g4vm+9WYVjCycUB5Aj/g8FAhvvUSBroX6cCgCQtYULF1rHjh1LVT5T3PTdU99FkT9sv4Jh+xVMRESEffPNN9a+ffvCqxi///777cQTT7SffvrJ9uzZY3feeaedccYZNnfuXDvuuOMKuMoAAAAhWjm+YYGF1e1AKOd7Fd0AAQAASURBVA4AAAAAAPJFw4gpFJ86dao1b968pFcn6KhSd9iwYWy/fGL7FQzbr2BUZd+nT59cDaeYp2B8wYIFNnv2bKtWrZq7fPzxx3bLLbe4s5C++uorN44jAAAAck9heFiT7iW9GgAAAAAAoBRQqNa2bduSXo2g421fzfbLH7ZfwbD9ik94XscXj4z8J0tXO/X//Oc/dskll9hZZ53lWq0DAAAAAAAAAAAAABBI8lQx3qxZM9dGPWMZ/4svvuiuu3en2gkAAAAAAAAAAAAAEMQV4z169LC3334702kKx3v37m0ej6ew1g0AAAAAAAAAAAAAgOINxh988EE3AHxWXn75ZUtNTS34WgEAAAAAAAAAAAAAUBLBOAAAAAAAAAAAAAAAIR2Mq2L88ccfL8xFAgAAAAAAAAAAAEFn2rRp1qFDh5JeDYQo9r8iDsbff/99mzx5cmEuEgAAAAAAAAAAAIVo/vz51qVLF6tcubJVqlTJWrVqZaNHj7akpCTfPL/99ptFR0fbZZddVqLrGsyuueYaW7BgQYmuw9lnn23PP/+8BZpDhw7Z8ccf7/Y/FA32vyIOxufMmWOrVq0qzEUCAAAAAAAAAACgkMycOdOF4hdeeKGtXLnS9uzZYzNmzLA//vjDNm/e7OZJTU21G2+80c4444ySXt2A5fF4LCUlpaRXI2g98sgjVq9evZJejaDF/pc/jDEOAAAAAAAAAAAQImHa7bffbvfff7/deeedVq1aNXd/s2bNXEdgb1D573//25o3b25nnXVWCa9xYKlfv749/fTTdvrpp1tcXJw7meCvv/5yJxlUqVLFGjVqlK46Vtu0devWuV7+vn37bPDgwe73UKFCBTvllFNs/fr1btrWrVvtyiuvtOrVq9txxx1nDz30kB0+fNhN27Vrl/Xo0cPXAaBdu3a2du1au+eee+ybb75xv+9y5cq5EyJk7Nixbhnly5d3r2nixIm5Wj/v63niiSesRo0aVrNmzXxVAy9atMg+++wzt17IvVDf/wpDZF5mTkxMtPDwcIuKinK3//77b5s0aZKtW7fObaT+/ftbgwYNCmXFAAAAAAAAAAAAUHhUIb569Wrr3bt3lvMo0HrhhRfsp59+snHjxhXr+gUDhY0fffSRawOuit2TTjrJunfvbv/3f/9nK1assIsuusiFxldffXWel3399ddbfHy8LVy40GrVqmW//PKLlSlTxk3T8nSffn87d+60rl27WtmyZW3o0KH27LPPupBy48aNFhMTY0uXLnWh45gxY1wIrXb4OhFCtI4PP/ywLV682J0QocBTl9z6/fff7dprr3XP9e2339r5559vo0aNyvXjtZ7qRvDSSy+5zgTIm1Df/4q1YlxnHGjDinb2E044wbXcSE5OtlmzZtmJJ57oNhYAAAAAAAAAAAACy/bt2931Mccck+U8AwcOtMcff9yqVq1ajGsWPG6++WZr2rSpRURE2I8//ujazz/55JMWGxvrQkpV3Cq8zCuFgx988IG9+uqrVqdOHVeo2qZNG1fVr8Dxyy+/dJW2qrxVsaoqdr3Po4JWhZU68UHrpSphVRBnRtPVOUABt8b5VtW31ju3tD6qBNZzavxoVfzqZIrceuaZZ9zr6tSpU64fg3+E+v5XrBXjS5YssVatWrmf9YJvueUWtxG8hg0bZvfdd5/Nnz+/wCsGAAAAIDTo7GH9XbFp06ZCHR9Lf2zVrl3bnZV86qmnFtpyAQAAACBYeVunK+hS2+WMpk6d6io/VRGMzKkFtNeGDRtciBgdHe27r2HDhm475pXCZVXb+i/f/3kUfCpE9H8e3S/K5hISElyr671799pVV11lI0eO9FX7+tPvfcqUKfbiiy/aDTfc4Npyjx49Otctt/3XQVQ1rOfODbX9Hj9+vMsbkT+hvv8VazCug1TeA1V//vmna6WRscQ+P2MJAAAAAAjdUFzj2+ls5759+6b7Y66gkpKS3Em7Csb1dwrhOAAAAIBQ16RJE1fhO336dFcAmdHs2bPt+++/9wXoaqusXEgtlLds2VICaxx4VEnrVbduXXeStzore4chXrNmjbs/r1SFqyGNNabzsccem26alqfgUVW93nDS/3lUxat25rqo1fUll1xiL7/8sqvs9l9fLwWYuqhi95FHHnEnQqj9dVHT3+h6DdoPRdtt//79bn/75JNP7LTTTivydQh27H/FGIxrh/z4449dz3cl+uot760gl59//jnL0ngAAAAAyEh/KDVu3Nj+85//uDOTC5vGwBo0aJBbPsE4AAAAgFAXFhbmxg3XGOMVKlRwfzOpZbrG/VWo9dhjj7m2zF7q7vXHH3/Ya6+9VqLrHaj0d6aCQoV7w4cPd62ktX1VAZsZzTN37lx3yUjLufTSS93fsBMnTnS3lcOpglet78855xy79957XcW12lY/9dRT7gRz0bDHCps17rR+rwpJIyOPRIBazt9//+17nuXLl9u6devszDPPdCenK9T0zuvdR7766ivXJr2wKQw977zzfLc1PPOAAQNcvqhxsZE3pXH/C6gxxvVhqBeqDacPTSX9ap/+1ltv2aOPPup23ltvvbXo1hYAAABAqaKxsHQCblGE4qI/stSWS2dQAwAAAADMunXrZp9++qmr0FURZKVKlezyyy93RZEajkpVoN6LQi61UM5uTPJQpgBQoaC6oamqvnv37nb33Xe7Ew4yo0DwjDPOyHJ5ajGtat2TTz7Z/V4UUqqqVpTF6WdV9moZF198sQ0ZMsTXovyiiy6y8uXLW4sWLax9+/ZuLGpRFzV1AtDy9LtXdzVlewosdVKExo72jhWt9dMyWrZsWQRbyywuLi7d/lW9enUXxOvnwuwgFypK2/5XHMI8GuE8D3T2hjaqWmn4Uw979ZC/4447rDTZt2+fVaxY0fXE138AoSY1NdW2bdvmztTJrN0BgJLD+xMIbLxHgdy58MIL3Rnj/fv3L7LneP31123atGnuDzHh/QkENt6jQNZC/TgVACBrixcvtnbt2rmArG3btiW9OkFHfzP26dOnyLefAmdV6yoQDEQKRjWU8tNPPx2Q26+0Yv8rvs+/PNemK+VXOL59+3ZbtWqV+4NVZxBpTAoAAAAAKAwau0pt1mfMmGHLli2zw4cPuzPI1brr/vvvt3PPPdfWrl2b6WPPOuusTNuCAQAAAABQkoprHOX88rbGRum0NMD3v+KQ76btam+gCwAAAAAUpt27d1vnzp1tyZIl7rZacam9n1p+vfrqq+5k3TZt2rg2YV46YffHH390P+vEXQAAAAAAACBfwfj06dOtV69euZp3/fr1OfapBwB/8fHxlpKSUmjLi4iIcOOVAACA4DN48GBfKK7hmkaMGGGRkUf+dJk3b54bQ+uDDz5I95j33nvPrrjiCvfzbbfdVgJrDQAAAAAAgFIRjP/nP/+xxx57zG644Qa75JJLrHnz5umma2yjb7/91qZOnWpffPGFvfbaa0WxvgACTGJKgiWkxFv5qEoWHpb38f9mzZplY8aMcZ8hhU3jrqnV6gUXXFDoywYAAEVD3wneeecd93OrVq1s1KhRFhYW5pveqVOnTB/37LPPuusOHTq4CwAAAAAAAJCvYPzrr7+2jz76yMaNG2cPPviglS1b1mrWrGmxsbGu1eGWLVusWrVqdv3119tvv/3mpgEo/fYl77Fth7ZYTPhWqxpb3SpGV8l1QP7ZZ5/Z8OHD3RihZ599tkVHRxfaeiUlJdmXX35pDz/8sIWHh9t5551XaMsGAABFZ8WKFW48cenYsWO6UDwr33zzjX3//ffu53vvvbfI1xEAAAAAAAClfIzx7t27u8uOHTts/vz5tnbtWjt06JALxDXGny4KoACEEI+Zx5NqiSmJtil+ve1M2J7rgFwdJk4//XTXHrUoPjtUKa5WrNOmTSMYBwCgBCxatMjGjx9vPXr0sK5du2Y7pMqaNWusevXq5vF4fPfnJhT3rxZv3LixXXrppYWw5gAAAAAAAAjpYNxLQfhll11W+GsDICiFWZjFRMRYah4D8m3bttk555xTZCfUaLknnXSS63YBAACKl8YC79Kliwu9NczSxIkTrV+/fkfNt3XrVvedQCff7tq1y+rUqePGE1fVuE7GVVCeXUC+fPly+/jjj93P99xzDyfqAgAAAAAAIFMcNQJQaBSAKyCPCov2BeSr9i233Yk7XGieUWpqqkVERBx1f0JCgo0dO9ZOO+00q1ChgsXFxVmTJk1s4MCBtmrVKjePDpY/88wz1rJlSzekg8YTb9eunX3yySfplqUD63oeAABQMqG4KNweMGCATZo0Kd18uv3XX3/5buv/bIXkqjCXJUuW2NChQ32t1WX27Nm2YMEC3+0xY8a45avavG/fvsXw6gAAAAAAABCMCMYBlHhA7m/37t3WoUMHV/H1ww8/uPsaNWrkqsleffVVd6BdB7979uxpQ4YMsd9++83q1q1rDRo0sNWrV7sD6AAAIHBC8Xpp92cMx3Wt217RfuH4zTff7Lq+yMiRI61q1arWqlUrq1Klip1//vluHHJRiP7mm2+6nzV8ik6WAwAAAAAAAAqtlToA5CUgT/Gk2MHDB+zwoWQLCwu3StFVsnyMDmp7w+377rvPjT+uqm/vgfaoqCibMWOGa49etmxZ+/zzz12Q7j3gfvDgwWJ6dQAAIKdQXKOKv29mQ83sOb9wXPO98cYbvvHEK5jZiWb2t5ntNbPy5cu7scm/+uor+/DDD23ZsmWuZfoxxxzjTo7r1KmTe9yLL77oOs2UKVPGbrnllhJ97QAAAAAQCGbNmuX+hkLefPvtt+6a7Zc/bL+CYfsVjIomc4tgHECRUXV4cmqSBiG3spHl3JjjFaIqZTn/3r177Z133nE/qyps1KhR6cYU9R4EHz16tLtu2LChPfTQQ/bTTz9ZjRo1rE+fPu42AAAofosWLco0FFcN95i0ebzh+JQpU3yPq2NmldNaWTXyC8d1MtxFF11kd9xxhzsZLjOPP/64uwAAAABAqEtMTHTDVg4bNqykVyVohYeHs/0KgO1XMGy/gtHnnz4Hc0IwDqBIA/GYiFgXiFeMruIqyLOjtqjeMUQ7duyYLhT3p4oxWbp0qRuDXNVjuk8Hxnfu3OmqxwAAQPFShbd/+3RvKC5hGcJxr7vN7Le06eYXjuu+pLS26tu3b88yGAcAAAAAHBETE2MpKSk2depUa968eUmvTtBRpa5CSbZf/rD9CobtVzCqslfhpD4HCz0Y37x5s82ZM8eN73feeedZdLR3NEBzLYzHjBljjzzySN7XGkDIBuJe3naqklUoLt7wXGcA/fLLL1a/fn3r37+/G6tU45A/99xzrsoMAAAUnx49ethrr73m/j9fa2bq4fKsX+jtDcc1oMprZtY/bZ6LPR47nJLi5tE3gY1pobhX5cqqJy8YfXfQmdcAAAAAUNopVGvbtm1Jr0bQ8bavZvvlD9uvYNh+xSdPR4d+/PFHa9Gihd166612+eWX2wknnGC///67b/qBAwfsscceK4r1BBDAPOaxxJQES/YkuUC8TtyxtnvlARsy+EH77NPPsn2sKsvWrFnjTqxp2rSpbzzx+fPnpwvK/alCXKpXr+5CcTn11FPddXJysm3cqEPqAACgOHXt2tUmTpzoO7ltrJndmxZ2e2nKwxr7Ke1at2slJdmPv/1mSYcP2wYz2+o3v/6fr1ixYoFDcf0dU7t27QItBwAAAAAAAMEtTxXjQ4cOdZUgOuClEOv++++3s846y7744gtr06ZN0a0lgMAVpurucIsJj/FViM//Zr5vjFFVjukzo1+/fkc9VK1Rt27dajt27LBdu3ZZ48aN7corr7S33nrLlixZ4j5znnjiCV9YPnv2bIuLi3PdKubOnesev3btWqtXr54bZ1zUapUD3wAAlAzv//cDBgxwJ7gpHLcMleMZDTh82O6ZO9f6R0Zai1NO8XV9qVGjhm3YoKg8/5KSkuzrr7+2X3/91Z555pkCLQsAAAAAAAAhFIwvWrTIXnrpJdeGsHz58vbyyy/bcccdZ507d7b//e9/7mcAoaVCVCWLDo+x8lEVXcv0efPm+UJx0UFxHRwX/3Bcbc9XrVrlu60xRFeuXGkjRoywP/74w37++WcbOXKk+5xRtdj69ett9+7d9vrrr7uuFRMmTHCheKtWrVwQ/ueff7rl6ISd3IwjAQAAii8cr5xWIZ6ZDmZ24v799uasWRb5+eeu4rxWrVoFrhT3DrtSp04dF4p37NixwMsDAAAAAABA8MrzGOMJCQnpbj/wwAOumvOCCy5wQReA0KLW6bpIxlC8npkbYzRjOK7PCt1W9bdEp40lqnB8586d9vnnn9sbb7xhM2bMcGNrLF++3LVP79mzp3Xq1MkqVapk33zzjQvBdVKOQnONu3HXXXdZnz59SnBrAAAA7//3+l4wZcoUd/u1bIJx+cLMEhMT3aVv3742efLkYltXAAAAAAAAhIY8BeMnnniiLViwwE466aR09997770u0Ordu3dhrx+AIJExFO9qZu9rCAYze84vHNd8Cr2944erFuxEM/vbzPamheMKum+66Sa75557sny+Y4891rVcBwAAgUcnwen/e6/+Ocyv0+eGpf2sx+lEuMyGYQEAAAAAAADyKzwvM1933XU2f/78TKcNGTLEHnvsMdqpAyFIwyxkFoqrjnyMmd2VNp/CcFWOeUPx2mmtVfVB1CgtJPdvq37w4MESekUAACC/vJ1hvP/f321mD+XwmIcyfF/Q4+lGBQAAAAAIdeqcWrdu3WznOeGEE2zmzJm5Wl65cuVs6dKlhbR2KO2+KQX73/Dhw+2yyy7LXzCuA1RTp07NcrraGq9evbpgawgg6IwfPz5d+3RvKC5hGcJxr7vT5tV08wvH1VbdG45v37692F4DAAAomlD8Wb//77OS8fsC4TgAAAAAFC0VQarYqXLlym7oylatWtno0aMtKSnJ6tevb2XKlHEhli6ajpLRsWNH27Bhg+/22Wefbc8//3y6eX7//Xfr1q1brpZ34MABa9myZaaBYXHQEGo33nijNWjQwMqXL2/NmjXjb/8A1rGU7X95DsY1vvhHH31k+/fvP2ravn373DTt1ABCS48ePSws7Mgh77VpVV9HDoenP9j9hJnVT7t+Nm0sh6TkZDeP5t+YNta4l76UFZS+yEVG5mnUCAAAkA+zZs3KMRTXlCfTvg88mcX3hYzhuJYLAAAAACg8qu5UKH7hhRe6zp179uyxGTNm2B9//GGbN29287z99tsuxNJF04HCcPjwYatdu7bNnj3b5YqTJ092Q6p+/vnnJb1qCBF5CsZfeeUVe+GFF9xZHBlVqFDB/v3vf9uECRMKc/0AFKLElATbm7TLUj2phbrcrl272sSJE33h+FgzuzeTg90Pm9nqtGvdrpOYaN/89JMdTEgwnXO01W9+nZVYsaK3uXr+qBW7znysU6dOgZYDAABy9sEHH/hCcXWFeSqTUPyetLHE16ZdZ/Z9YUTa491jPB63XAAAAABA4dDfWbfffrvrAHznnXdatWrV3P2q3FVIWa+e9y8yZGbs2LFuSGHlZDqGrePiXgp7Tz31VFdhr/bSKib1uv76612ldK9evdxjmzZtanPnzvVNnzZtmjVu3NhNO+aYY+yJJ1ReZm4eb8W+AmS1ttbvTpX8OrlBtB4ffvihJScnu9/nvHnz0q1zixYt3IkOomP4P//8s5t/xIgR7iQJb2eAX375xT2/Tobw2rhxo8XExNimTZsKZfuVLVvWHn/8cWvUqJFbl9NPP93OOeecLIdxRnrsf9k77bTTLDw83OLi4qxdu3a2du1aX4fiRx55xO13eSqj1IYZNkyHsDKnD1Ht0IMHD87LYgEUk33Je2zboS0WE77VqsZWt4rRVSw8LE/nx2SpX79+7tpbKaZw3HJon3rX4cM2eMEC6z18uDVp3dqioqLc/VWrVnUfhAWtFF+wYIFt2bLFXn755QItCwAA5GzQoEH21ltvueFV9GdHT7/hVbyh+HMZHpPx+0JC2uOO/Nli7g8ZLRcAAAAAUDhUIa4hcXv37p3tfAMHDnTHehWWKRdScVSoW7FihT388MO2ePFidyLB1q1b3UV+/fVXu+KKK+z999937aZ1bPriiy+2H374wYWQoqp8hZXK2p5++mkXVq5Zs8YVeOnnOXPmWKdOnVyFvn5PGY0ZM8YWLVrk2k8rj8tIx9cVfL755ptuOfLTTz+5cDFjy2rdHjp0qC+k9NK6vvfee2595I033rDzzjuvyIrP1Kla2+jqq68ukuWXJux/uQvGFep3797dbrnlFl+h97Jly9zrVLCfp2BcG0LjTGTlpJNOynRjAQgQHp0RmGqJKYm2KX697UzYXqgBeWbheOW0CvHMaCSJlgcP2n+++srmLFzozhbSB59aqRRURESEO7NJZwHpjCQAAFC0dCbup59+6s4YVjg+yy8cH+oXiuv/++uuu879ceN/Mt1TafPP8gvFtTwtFwAAAABQOLZv3+6udew0Kwq29LeYjrEqaOvZs6erAj3llFMslGl76O9YjamsyvqaNWu6i7fjssK8c889190+88wz3bjL77zzjq/gVCcXKLSUG264wd2/c+dOi42NdaGiwrvWrVu7Ct38bmv9vX3BBRfYuHHj3HL1u7z88svdmPG50b9/f9c5wBtMTpkyxZ58UoOhFT7vEGo6+eJf//pXkTxHacL+lzMNBV6lShXr06ePLVy40C1P20wnAylwV0gfmdfe//rQVJl+ZjRN8wAIXGEWZjERMa6delEE5ArH9SVJH1jyWjbBuMxMa2OhA+h9+/Z1H3oAACA46YzgjOF4M78KcIXiavOl7wua1/9kuvczVIprOd4zjAEAAAAAhcPbOl0BkdoKZ6Zjx46+n1XJq4pOBeShHoxre+m494svvuiCRbUBHz16tAsTVXn75Zdf2uuvv+6bX3mZhiH2qlWrVrqW4t4gTx1UP/74Y1eRO2TIEGvZsqWrelWL8bxSK209jyqDFTarhfW7776b68erk8C9997rgkR1Y92xY4ervi1sOhagit7ly5e7FuBqf43ssf/lrE2bNnbllVe61ut6XYcOHXLHp1JSUtwxJm2TPO1p6kmvHTQrn3/+uZsHQOBTAK6APCos2heQr9q33HYn7ijQGOSTJk1yFWBe/XOYf4Dfz3qcHg8AAII/HFe4bVmE4qJr3db9/vMRigMAAABA0WnSpIkbE3j69Om5fgyh5T8Uun311VeuhbU6LF977bXu/mOPPdbuuOMO14bae9FYyf/5z39ytdzOnTvbrFmzXBColthqNa2Csvz8LrROqtT97LPP3N/YWf19ndmyVC3co0cPF8CqiO2aa66x6OhoK+xQ/NZbb7Xvv//e5YoVK1Ys1OWXZux/2bv99tvdyRaXXnqp7d271w2zq3VQtb32N7WLz9OnmQ5e6SwBDYaekc4meOqpp3wHugCEXkCuUNtb+SV3m9lDOTxG0+/K0DqFcBwAgNIVjmcMxb0yhuOE4gAAAABQtPT3l9ocjxw50l2rlbJ3/GK1HV67dq3rCJqYmGjJycmuFfP//d//HTVGcChS4PbFF1+4KlSFdeXKlbPIyEjfmOyq1lVoqepUbT+1clZ76pwo5Pzggw9c9a6Wpypf73IzUuvsv//+O8dgUoHzc88951pKe//mzmxZ+n1n7ATtbWetMakz/h2vkyoK2vV18ODB9u2337ptWbmyBmNFbrD/WY77n9ZNr1ufWeqOodeh59fj7rnnHlfNnqdg/KabbnIffipb15i9Su11ad68ubv/kksucfMUpZdeesm9APWm1yDqGjg+OyrR1yD0ml/l/zrjAUDWAXlkWJQdPHzAth7aZPuS9xQoFH/WtW7PnqaPIRwHAKDUUbitgyn6f10n1mZ1Aq3u13TNp/kJxQEAAACgaGnsYZ2U/Mknn7j2zKrS1DjAylJUZamqS7Uhrl69uj377LMuHFfb5lCXlJTkxmVWoKfto9bV3pBOLZzVNvrhhx92201juGteBZQ5UWXuCy+84Kp+VT2tHEzjIWdWUXvnnXe6zs76nen3mBkNh9yhQwe3fhrzOSuqDFYIqvXV8rw0DrUqbBs2bOiqkr30WlRRXJB9QUGoqngV8mqcbIW7ugwaNCjfywwV7H+JOe5/V111lZ1xxhkuDD///PPt5ptvdvcr027fvr27L8zjTbHyQB+C06ZNs7/++suFWGq9oXEmVMJflHR2gDbi+PHjXSj+/PPPu+Bbb6AaNWocNf+CBQvcgbWnn37a/YLeeustGzVqlC1evNhOPPHEXD3nvn373I6g/wz8e/GHCr0htm3b5rYv7VKC3/ZDW2zboc0WExF71DRVhyenJrmkOiY8Nk9jjuuEE73HsgvFNeUpM5uY1j79oUym32Nmz6Xd1geXDpJ37dq1wK+7tOL9CQQ23qNA4OL9CQQ23qNA1kL9OBUAIGvKPdq1a2eLFi2ytm3blvTqBB1lXqpuZfsdce6557oxolXd7fX111+7fE4BbEZsv4Jh+xVs/8vL51/mtfA5UABe1CF4ZsaOHWs33nijG1RetAF0RpMqSx944IGj5tcZDhdddJHdd9997rbawKvNgAam12MBZAjEI/IWiHupzYY3FK+XFoBnF3oPM7PdGcJzXY8ws/+mjTGq5Wm5BOMAAAAAAAAAABQPtaL+6aef3PF5f2eddZa7AMG8/0Xm9aztZ555xj766CNXsq/B2B999FErU6aMFTU9n5L+Bx980Hefzhw/77zz3EbKjO6/+27Vrv7jwgsvtA8//DDL51Epvn9rAZ2J633tmQ00X9rpNSugDMXXXhp5Uj3u96nrVPMLxMOOBOIVoisfCcQ9RwLz3NL4FdOnT7f4+Hhbb2aXq7OEmcWmheI6NeUFvWf9HvN82vXotFA8QSfdmLnHh6eNMarlsu9ljfcnENh4jwKBi/cnENh4jwJZ430BAACKkopNv/vuO1d4qi41QGnb//IUjD/11FM2fPhwF0YrDNeKqb1ZcYwFrL7xGjBevfP96faff/6Z6WO2bNmS6fy6Pytqu/7YY48ddf/27dstIUHRXej9waX2XDooQQu74Lc3cY8dTDpk8XbIpdFRYdFWPrqixUWWs6TEFNthO/K13Lp167qzd/Te0Ykl29IqxHUay5tmNt/M2vm1wNDYEvKNmY00s2v13jNzj9N8MTEx7qQbLVefMfmh0D8pJdHKRJbNU/V7MOH9CQQ23qNA4OL9CQQ23qNA1vbv31/SqwAAAEqxzz77rKRXASHss2LY//IUjL/xxhv28ssvuypO0QDrF198sU2cOLHU/LGqinT/KnNVjGvAeQ3+HopjN+mAhMZ61usvLb/jUBae4LHkhASLCYtJXyFeCBR4R0VFubHGVTm+yMz+l9YWXbQfvfrqq3b99dfb5MmT7aabbnIHujTfa37zqVJcY4t37NixQOuzI2Gr7U3YaalhyYX+WgMF708gsPEeBQIX708gsPEeBbIWG6vebAAAAACKPBhft25duvF+VTmuP1Y3bdrkKjuLUrVq1SwiIsK2bt2a7n7drlWrVqaP0f15md/SKlV1yUh/jIfqH+T6HYfy6y9NKsZUtpjIWCsfVbFIQmKN7/DJJ59Yly5dXDi+2m8fmjBhgvXr18/d9l4PGDDAheOr/UJxPb5Tp04FXhc9pxq5J3mSbHPCBtuVtCNf46cHOt6fQGDjPQoELt6fQGDjPQpkjvcEAAAAkH95+jZ9+PDho85MVYVocnKyFbXo6Ghr166dzZkzJ91Z5Lrdvn37TB+j+/3nly+++CLL+YHSLiYi1ioWceW0Qu1PP/3UhdzeA1rqKuENw710W/cfCbCPhOJ6XGGE4l5hFmYxETGuZXxiSqJtil9vq/Ytt92JO/I0hjoAAAAAAAAAAABCqGJclZ1qg+xfUa1xtwcNGmRly5b13fff//7XioJanPft29dOPvlkO/XUU+3555+3gwcP2g033OCmX3fddXbMMce4ccLljjvucBWsY8aMcS3fp0+fbj/99JNr5wyg6Cjcnjdvno0fP9569OiRrtNExnBcHRw0Prk+R3TyS1HQiQAKyBWGewPynQnb01WQ701KsM3xByw5NdWiwsOtdlw5qxhNizoAAAAAAAAAAICQC8YVSmfUp08fKy5XXXWVbd++3R555BHbsmWLtW7d2g3EXrNmTV+rd/+WUh06dLC33nrLHn74YRs6dKg1btzYPvzwQzvxxBOLbZ2BUKWQW+3Tc6LQPKvgvDgC8q2HttqOhHBbdyDZEtPGMtRJQNHhEVa3bHlrV62OlYuKLpb1AwAAAAAAAAAAQAAE46+//rqVtMGDB7tLZubOnXvUfVdccYW7AEDGgDwpNdn2Je2zVE+YlY+KsTKeWF8wnpiSYn/v32N7khKtc50GhOMAAAAAAAAAAAChEowDQGmgivHk1CQ7mJJsBw6HWVJKGUuxaN9457qOjYy06IgI25l4yBbt2Gxn1a5X0qsNAAAAAAAAANmaNWuWLVu2rKRXI+h8++237prtlz9sv4Jh+xXM6tWrcz0vwTiAkAvELcwsPDzatuz32KHDURYTGZXp/OFhYRYXEWkb4ve5McgZcxwAAAAAAABAIEpMTLSIiAgbNmxYSa9K0NJQvWy//GP7FQzbr2D0+afPwZwQjAMIqUA8JiLWqsZWty3xqbYzcZNVis7+YzAmIsK1U98cf4BgHAAAAAAAAEBAiomJsZSUFJs6dao1b968pFcn6KhSV6Ek2y9/2H4Fw/YrGFXZ9+nTx30O5oRgHECp5TGNFZ6QLhCvGF3FjTG+4eA21zLd2z49K955klNTi229AQAAAAAAACA/FKq1bdu2pFcj6HjbV7P98oftVzBsv+JDMA6gdApTqB1uMeEx6QJxr6jwcPN4PO6SXTjunUfzAwAAAAAAAAAAIDgRjAMolSpEVbLo8BgrH1UxXSDuVTuunEWHR1hiSorFRmb9Uajp0RERVieufBGvMQAAAAAAAAAAAIoKJZAASiW1Tq8YXTnTUFw0XvgxZctbfMphS/V4Mp1H92t63bgKViE657EpAAAAAAAAAAAAEJioGAcQsk6uVsf2JiXazsRDFhcRaTEREa6tulqnq1JcoXjVmDLWrlrtkl5VAAAAAAAAAAAAFAAV4wBCVrmoaOtcp4E1Kl/ZUsxje5ISfRfd1v2arvkAAAAAAAAAAAAQvAjGAYQ0hd5n1a5n3Y5tbO1r1LV2VWu760uObeLuJxQHAAAAAAAAAISqDz/80OrXr5/tPKNHjy629UFo+TAX+99tt92W6+URjANA2pjjzSpVs5ZVarhrxhQHAAAAAAAAUFrNnz/funTpYpUrV7ZKlSpZq1atXLiZlJTkhpp8+umnXRhVtmxZa9KkiX3//fclvcoIYEOGDMn1vAoxjz32WKtQoYIdc8wxduedd7r9DsivcePG5XpegnEAAAAAAAAAAIAQMXPmTBeKX3jhhbZy5Urbs2ePzZgxw/744w/bvHmzPfTQQ/bJJ5/Y7Nmz7cCBA/bFF1/YcccdV9KrjQCkkyhSUlLy9JhbbrnF/vzzT9u3b5/98ssv7kLFOYpr/yMYBwAAAAAAAAAACJEg6fbbb7f777/fVepWq1bN3d+sWTObPHmylS9f3saOHWuTJk2y448/3sLCwqxevXpWu3btkl71gKAwd/DgwW6bqOL5lFNOsfXr17tpW7dutSuvvNKqV6/uTiTQCQaHDx/2PXbRokV27rnnWpUqVdw8/u2fP//8c2vTpo1VrFjR2rZt605K8Lr++uutf//+dvnll1u5cuXshBNOsN9++81eeeUVq1u3rlvWyy+/nO53PGbMGGvUqJF7rosuushWrVqV69e4YcMGu+CCC9zra9eunTthwp86CaijwOmnn25xcXFu+lNPPZXr5Tdv3tx1IvCua3h4uDtBAzlj/7NM97+bbrrJN13dL9544410j9GJQHqMEIwDAAAAAAAAAACEAAWQq1evtt69e2c6/bvvvrOYmBh7++23rU6dOi6EUohOq+t/QsK//vrLFi5c6CrtX331VStTpoybdvXVV1tUVJTbvt98840bG9lbCb1x40YXSipc3LRpk61du9aFmKLlXXrppTZs2DDbuXOnDR061Lp37+6W4/Xuu+/aXXfd5Z5TYajm//vvv13gOH36dDdNwai8+eab7uQGPb+eS0HmJZdcki4kzY5eh06E2LJli02bNs0mTJhw1Dw6iWLKlCmuo0DTpk3zvB1HjhzpQtYaNWq4ivG8jBEdytj/ct7/rr32WrcOXlrOnDlzrE+fPu42wTiAUmVvUoL9uWeHLd21zV3rNgAAAAAAAADAbPv27e5aYztnZteuXa4qVQH6ihUrbN68efbpp5/aqFGjLNQp+Pvggw9cGKmTBlTprCpbVd0rePzyyy9dIKjAVxW9qthVgCdTp0511a9qIx4bG+sqXTt27OimqY392Wefbf/6178sMjLShZdnnnmmOznB6+KLL7YzzjjDTVeguWbNGnvssccsOjraOnfu7Cp9ly5d6uZVKKiuAC1btnTPNWLECFdV/MMPP+T4GjWfQtVnnnnGraM6CQwaNOio+W6++WYXSEZERLh1yKsHHnjAhZqq9tXya9WqledlhBr2v9ztf9dcc419/fXXbpuIXodeq8a11+cfwTiAUuFAcpLN3bzGZq5baQu3bbBFOze7a93+evMaNx0AAAAAAAAAQpm3dbo3NMpIoZoo9NLPasl8xx132Mcff2yhTlW2qqbPbLx1tX9WCFizZk3ffQ0bNnT3ex/buHHjTJereVSZ78//seK/XAWGannvrRT23qegObPlaZ0VpPovLyuq8NXrUCW3l0LWjAprzHm1VVfra1VCI3vsf7nb/1Rtrup4VZuL2qpfd9117mcF+gTjAIKeQu85m1bbqv17LCIszCpFx/guuv33/j1uOuE4AAAAAAAAgFDWpEkTF1qp/XFmFFIicwroEhMTfWM6+9NYywkJCb520t4QTvd7H6uW1ZnRPJrXn/9j8yrj8tQGX4FjbpanAFOvY9u2bb771q1bd9R8qlYuLMnJyYwxngvsf7nf/7zt1DUWujpf9OzZ092vqnGCcQBB76cdm2xn4iEXhMdGRlpYWJi7X9e6rfs1fdGOzSW9qgAAAAAAAABQYnTMdNy4cW6MZ11rTGFReNS/f38XOJ133nn2+OOPW3x8vAu0NJ/GFA51qprVdlBr582bN1tqaqotWbLEbUO1pj/nnHPs3nvvtYMHD7ow76mnnrK+ffv62jurlfT48eNduKltq5bRctVVV9ncuXPt//7v/9w4zP/9739dC/tevXrlaz01lvKLL77o2pTruR5++GG3fqeeeqqbPnz4cNc6OzMKDtUyW63ODx06ZMuXL7dXXnnFCouqil9//XU3VrXH43Htt5988km78MILC+05Siv2v9zr0aOHq5LX9tDP3k4YatlPMA4gqGkM8Y0H91tcRKSFpwXiGel+Td8Qv48xxwEAAAAAAACEtG7durlxwz/55BNr1KiRVapUyY0rrPF81YZYLYj37t3rgrhTTjnFhZZDhgwp6dUOCFOmTHHh3cknn+y2m0JKBXjy1ltvuZ9VnatwT+Mye7ebqmXnzJnj5tF2VdX+e++956Ydf/zxLox89NFHrUqVKu6kBI0lrXbW+aG20bfddpv7PSsI/OWXX1wrfI0PLQpNtX5Z0TqqKlntrK+++mrr16+fFeaJGVq+9ju141bQq+30/PPPF9pzlGbsf7mj1u6qEv/f//7na6PuFebRKRnI0r59+9yg8fpPoEKFChZqdMaJWhZoByzM1hhAYflzzw43lriqwr2V4pnRR92epERrX6OuNat0ZBydYMf7EwhsvEeBwMX7EwhsvEeBrIX6cSoAQNYWL15s7dq1s0WLFlnbtm1LenWCjk4GUKVrKGy/li1bugrhqlWrFtoyQ2n7FYVQ2n4ti2D/y8vn35F4HgCCVHJqqgvEswvFxTuP5gcAAAAAAAAAIBSpfTkQqvsfp14DCGpR4eGuGjyn5hfeeTQ/AAAAAAAAAAAAQgsJEYCgVjuunEWHR1hiSkq282l6dESE1YkrX2zrBgAAAAAAAAAAgMBAMA4gqFWMjrVjypa3+JTDlppF1bju1/S6cRWsQnRMsa8jAAAAAAAAAAAAShZjjAMhbm9Sgm2OP+DG3labcVVgK2wOJidXq2N7kxJtZ+Ihi4uItJiICDeeuFqnq1JcoXjVmDLWrlrtkl5VAAAAAAAAAAAAlACCcSBEHUhOsp92bLKNB/dbUmqKL0hWW/K6Zctbu2p1rFxUtAUDrWfnOg1s0Y7NtiF+n+1JSvzn9UREWKPylV0oHiyvBwAAAAAAAAAAAIWLYBwI0VB8zqbVvgrrStEx6Sqs/96/x4XLCpuDJUzWep5Vu95RFfAaU5z26QAAAAAAAAAAAKGNYBwIQaoUVyiuQDw8LMx3v8Lx2MhIV2Wt6arAVtgcTNQGPthawQMAAAAAAAAAAKBohRfx8gEEGFVUq326KsX9Q3F/ul/T1ZZc8wMAAAAAAAAAAADBjIpxIMSozbjGFFe1eHZiIiJcO3XNTwU2AAAAAAAAAAS+WbNm2bJly0p6NYLOt99+667ZfvnD9isYtl/BrF69OtfzEowDIUZjb6tlui7Z8c6j+QEAAICsqcNQSi7mizAzTrgEAAAAikJiYqJFRETYsGHDSnpVglZ4eDjbrwDYfgXD9isYff7pczAnBONAiIkKDzePx+Mu2YXj3nk0PwAAAJB1KL7YzA7nYl79+dmWcBwAAAAoAjExMZaSkmJTp0615s2bl/TqBB1V6iqUZPvlD9uvYNh+BaMq+z59+rjPwZwQjAMhpnZcOYsOj7DElBSLjcz6I0DToyMirE5c+WJdPwAAAASTlLRQXCdTZndCZWrafLmpLAcAAACQXwrV2rbVCanIC2/7arZf/rD9CobtV3woBQVCjMYLP6ZseYtPOWypHk+m8+h+Ta8bV8Eq5DAWOQAAAHDkT8vIbC786QkAAAAAAEoWFeNACDq5Wh3bm5RoOxMPWVxEpMVERLi26mqdrkpxheJVY8pYu2q1S3pVAQAAAAAAAAAAgALjtH0gBJWLirbOdRpYo/KVLcU8ticp0XfRbd2v6ZoPAAAAAAAAAAAACHZUjAMhSqH3WbXr2d6kBNscf8CSU1MtKjzcjSlO+3QAAAAAAAAAAACUJgTjQIjTmOO6AAAAAAAAAAAAAKUVrdQBAAAAAAAAAACAIDBt2jTr0KFDoSxr7ty5VqlSpVw/V/369e3DDz8s9PVA8JgW5PsfwTgAAAAAAAAAAEAImT9/vnXp0sUqV67sgqlWrVrZ6NGj7euvv7Zy5cqlu4SHh9vtt99e0qtcaoWFhdnPP/+c6/mvueYaW7BgQZGuU26eK7/rcdttt9mxxx5rFSpUsGOOOcbuvPNOS0pKKoS1Rajvf1999VWOyyQYBwAAAAAUUKqZHc7moukAAAAAAsHMmTNdKH7hhRfaypUrbc+ePTZjxgz7448/XEXmgQMHfJe///7bIiIirFevXiW92iglbrnlFvvzzz9t37599ssvv7iLTsoAigPBOAAAQW5vUoL9uWeHLd21zV3rNgAAxSPCzCJzGYxHps0PAAAAoKR4PB5X/X3//fe7St1q1aq5+5s1a2aTJ0+2evXqpZt/ypQp1rhxY1pmpxk7dqwdd9xxVr58eXcSwcSJE939w4cPt8suuyzdvKrEV6toWbx4sZ1++umuSlrb/JJLLnH3n3rqqe5a21fV+SNGjHC3+/TpY3Xq1HHzt2vXLl0lrH5PrVu39t3WeihY1vK1XmeddZatX7/eN33IkCHu96ppLVq0sHffffeo1zVu3DirXbu21apVyx599FG3n2T2XP78p73wwgu53obNmze3smXLup/1POpIoBM0kDP2v3/4T9NzyEMPPeRex6BBg+yuu+6y66+/3u8RZiNHjiQYBwAgWB1ITrK5m9fYzHUrbeG2DbZo52Z3rdtfb17jpgMAULRizaytmbXLxaVt2vwAAAAASooCyNWrV1vv3r1zNf+kSZOsf//+Rb5ewWDFihX28MMP2+eff2779++377//3hcs5mTw4MEujFR1/saNG+2+++5z9//www/uWi2hVaE/dOhQd7tz5862bNky27lzp6vWv/zyy91zZmXq1Kn29ttv2/bt213oPGzYMN80tcn/8ccf3XM/8sgjdu2117p9wEvLVXCq7gAKUvU7f+ONN/K0be644448za+AUgFmjRo1XMW42qsje+x/WfN2HHjqqafc6xg/frz73Hr//ffdbf8wnWAcAIAgpNB7zqbVtmr/HosIC7NK0TG+i27/vX+Pm044DgAoegq7y+biQigOAAAAlDQFV6KxnXPyzTff2KpVq+y6664rhjULfGopr0rW33//3Q4dOmQ1a9a0k046KVePjYqKsrVr19qmTZssJibGOnXqlO38N9xwg1WsWNE9TiFmamqq/frrr9m2J2/QoIHFxsa6sZcXLVrkm6bbCqC9LfHVHcB/bGYte9SoURYXF+emKUR98803rSg98MADLrBU+35V96pSGNlj/8ubE0880VWov/fee+72woUL3ecfwTgAAEHopx2bbGfiIReEx0ZGWlhYmLtf17qt+zV90Y7NJb2qAAAAAAAACBDe1umqGs3Ja6+9Zt27d7fq1asXw5oFvkaNGrnW8i+++KILJS+44AL7+eefc/VYVcEmJCS4ttQK/7SMrCgoVEtotbBXK2u1xN67d6/t2LEjy8f4B8uq2PWv7n3uuefshBNOcEGnlvXbb7+lW5bCTAWXXmp7nZv9ozCorboqijO2vMbR2P/yrl+/fq5KXHStkJ5gHACAIKMxxDce3G9xEZEWnhaIZ6T7NX1D/D7GHAcAAAAAAIDTpEkTNybw9OnTs51v3759bizgAQMGFNu6BYMrr7zSjbe8detWF+iqLbSoLXh8fLxvvoMHD7pt6B9qqj30li1b3LjQ9957r6+q1lvw4vXWW2+5yyeffOICSbWgVqjoHXc5L+bPn+/Gn9Zz79692y1LlbT+y1Jgum3bNt/tdevW5aqjQGFJTk5mjPFcYv/LGw0Z8dNPP7nOBDNmzHCV8ATjAAAEmc3xBywpNcViIiKynU/Tk1JS3PwAAAAAAACAQrBx48a5MZ51rTGEveMXa0xetVsWjRdctWpVV5WKI5YvX25ffPGFa2MdHR3twsjIyEg3rW3btq5V859//umCPo3V7B84KhhUmKn7VDUbHh7uWkuLqn81vrKXAk0tX9X9SUlJ9vjjj2c7vnN2tCw9j6r+VQmsymFV7PrTujz44IPudek1vvTSS66ytiioffrrr7/uAlKFo0uXLrUnn3zSLrzwwiJ5vtKE/S9nGzZsSHdbFe89e/a0q6++2rV6b9OmDcE4AADBJjk11X2JyXg2X0beeTQ/AAAINur4cjAXFzrDAAAAIG+6detmn376qasIVSWpgrLLL7/ctViuXbu2r426q64MJ0byUkg4bNgwFyTqpIEvv/zS16b53HPPtYEDB1qHDh3s+OOPt5YtW1r58uV9j509e7ar8FWYeemll9ozzzxjrVu3dtOeeOIJu/32261y5cruhIW+ffu61tNqKd2wYUMrU6aM1a1bN1/rfNFFF7nfrdanTp06bnzqM844I908Wk+ti55LY09rTHmtQ1HQsUpVI2u/0/NqW1x88cX2/PPPF8nzlSbsfzl755133OeZxjz30gk/v/zyi/s8kzBPfmrfQ4jOZlCLALUL0JkFoUZncKiFgfr78x8gEFh4f4auP/fssIXbNrhxxLMLx/Vf/J6kRGtfo641q3Rk/CgUH96jQODi/YnAp7B7sZkdzsW8qhBoq5HZrLTgPQpkLdSPUwEAsrZ48WI3frDaI6t6FHkzbdo069OnD9svn9h+BcP2K7rPP7Vm13jpmzZtcicUHKmxBwAAQaN2XDmLDo+wxJQUi01rl5MZTY+OiLA6cf+cHQgAAIJBSloorlA4u2A4NW0+zQ8AAAAAALxSUlJs1KhRbmx2heJCMA4AQJCpGB1rx5Qtb6v273HBd3gmVeOpHo/Fpxy2RuUrW4XomBJZTwAAUFDhOfzZrlCcIVMAAAAAAPC3evVqO/HEE93Y4rNmzfLdTzAOAEAQOrlaHdublGg7Ew9ZXESkxUREuLbqap+uSnGF4lVjyli7akfGhQIAAAAAAAAAIBQoED948OBR9zNYFwAAQahcVLR1rtPAVYSn2JGxxL0X3db9mq75AAAAAAAAAAAIdVSMAwBC2t6kBNscf8CSU1MtKjzcjd+tVuXBQKH3WbXrHfUaNKY47dMBAAAAAAAAAPgHwTgAICQdSE6yn3Zsso0H91tSaoqvDXl0eITVLVve2lWrEzTV1grygyXMBwAAAAAAAACgJBCMAwBCMhSfs2m1b3zuStEx6cbn/nv/HteSnFbkAAAAAAAAAACUDgTjAILS77//bh9++KFt3brVhZmFJTo62urVq2cDBgywuLi4QlsuAosqxRWKKxAPDwvz3a9wPDYy0qIjItz0RTs2u1blAAAAAAAAAAAguBGMAwg6v/76qw0ePNgqVapkTZo0sYiIiEJb9qFDh+zdd991z/Hvf/+bcLwU0njcap+uSnH/UNyf7tf0DfH73Py0KQdKn/jknbb90DIzy83JVWFWvUxzi4uqWgxrBgD+Us3scA7TAQAAAABAbhCMAwg6Q4YMscaNG9u4ceOKJLj+7bff7JZbbrEJEybYHXfcUejLR8naHH/AjSmuavHsxEREuHbqmp9gHCh99idvsjX75lqqJ9nCwrI+wcrjSbHwsCiLjaxEMA6gGEWk/bl+OBfht+YrvBNFAQAAENyWLdNJ4Mir1atXu2u2X/6w/QqG7VcwedluBOMAgsrBgwdtx44ddueddxZZNfeJJ55obdq0sTVr1hTJ8lGyklNTXct0XbLjnUfzAyh9qsU2tTKRVS3+8A6Li8w68I4/vNPNp/kBoPjopLy2ZpaSi3kVinMSHwAAQKirVq2aO17ap0+fkl6VoKXOpGy//GP7FQzbr2D0+afPwZwQjAMIKomJie66qFucly1b1vbs2VOkz4GSERUe7sal1yW7cNw7j+YHUPpEhEdb3fKn2YrdMy3Vc9jCw47+Wqz71Wpd80WER5XIegIIZaEXdu/evds2b95sSUlJtmvXLtuyZYuFF/C7mB5fq1Ytq1q1ao4nRgIAAASz4447zlVNqqgI+T/2HBOTfZdJZI3tVzBsv4JRKK7PwZwQjAMoNRISEuzll1+2GTNmuC+Bhw8ftrp169o555xj999/vzVs2NAWLVpkDz30kC1YsMBNb9u2rQ0fPtzOO++8kl59FJPaceUsOjzCElNSLDYy6/8GNT06IsLqxJUv1vUDUHxqljnRNuz/Psuq8YSUvRYXWd1qlmlZIusHAKHk1VdfdRevlJQUVzFRWC699FL3d0BBg3YAAIBAplAoN8EQAIQqgnEApaa6pHPnzrZkyRJ3u3z58taoUSNbt26dO8DWvn17O3DggHXq1Mni4+Pd2UMVKlSwb7/91i666CKbNWuWXXDBBSX9MlAMNF74MWXL26r9e1zwHZ5J5VCqx2PxKYetUfnKViGHscgBlM6qcarFAaD4vPHGG+47e79+/ezcc8+1qKgodxJrZGRkgau8tZyffvrJnnvuOVd9MWTIkEJbbwAAAABAcCEYB1AqDB482BeK33fffTZixAh3IE3mzZvnDq49/PDDLhSvX7++/frrr1amTBk788wz7fvvv7d7773X3YfQcHK1OrY3KdF2Jh6yuIhIi4mIcAdd1TpdleIKxavGlLF21WqX9KoCKKGqcarFgVIo9aBZ8nKzqKZm4WVLem3g57PPPnMnqd5yyy3utr6TFVYwLk2aNHGt2d9//333twIt1QEAAAAgNNFDDEDQ27t3r73zzjvu51atWtmoUaN8obioSvyUU06x2bNnu9s66KaKcs3TvXt3d9/SpUtt06ZNJfQKUNzKRUVb5zoNXEV4inlsT1Ki76Lbul/TNR+A0KgaV3X4kSpxqsWBUunwOrODb5sd+vTItW4jYGgcTHV7KkoaVmn//v1u/HIAAAAAQGgiGAcQ9FasWOEqSqRjx46ZVoDoYNuhQ4fczzVq1PDdX7NmTd/ParuO0KHQ+6za9azbsY2tfY261q5qbXd9ybFN3P2E4kBoVY2rOlxV4kK1OFCKeFLMEhaaHXzXLGW7WXjlI9e6rfs1HQEhs+/wCQkJNnbsWDvttNPcMEhxcXGu+nvgwIG2atUq27x5s1111VXWoEED93hdevXqlevlAwAAAABCC63UAQQ9tVrM7wEv/8cidMcc1yUY7U1KsM3xByw5NdWiwsOtdly5oH0tQKCMNX44NYFqcaC0SN1jduhzs+S/zMJizcKr6svikWvPfrOEuWYp683KXGAWXqmk1xYZ7N692y666CLfcEnq+KSqcp3MqvHI27dvb61bt3adoxSMx8bGuiAdAAAAAICsEIwjZBEoBZ5FixbZ+PHjrUePHta1a9cs59M44aoOOXjwoJUtW9aaNm3q2qKranz+/Pku7M4YkFerVs2NKa6q8W3btvnu9//5uOOOK6JXBhSuA8lJ9tOOTbbx4H5LSk3xjY8eHR5hdcuWt3bV6lDxDuRzrPG9SWutYnQ9qsWBYJey2+zgdLPUXUdC7zC//xf1PTGsgpknySx5pVnKTrOyvcwiKpfkGiODO+64wxeKa1zwESNG+IZLmjdvnkVFRbm/A9QZqmrVqla/fn1bu3ZtCa81AAAAACCQ0UodIRkozd28xmauW2kLt22wRTs3u2vd/nrzGjcdxU8HtzQW+MSJE61bt242adKkTOebOnWqC8U1rvjy5cvdOIEVK1a0K6+80k3XwbOhQ4f6WquLxhb/4YcfrHPnzu72559/7h6neT766CN3X8uWLa1OnTrF8lqBgtBn1JxNq23V/j0WERZmlaJjfBfd/nv/HjedzzIgf1XjZSKrUC0OlAZhEWaeQ2ZhZdOH4unmiT4y3c0XUdxriGzou/57773nfm7VqpWNGjXKF4qL/m5QxbhOfFUoDgAAAABAbhCMI6QQKAVuKN6lSxdXCS6qfB0wYMBR4bhuq3LE2/48NTXVVq5c6ULucePGuVaKMnLkSHeATAfRqlSpYueff74bh/zJJ590B8/WrFljDRs2dFUl33//vUVERNjo0aNL4JUDeadK8Z2Jh9znVmxkpK87gq51W/dr+qIdm0t6VYGgUzOupTWu1MVdAwhy4RXMIuoeCb2zo+maT/MjYOi7u/dE144dOzI+OAAAAACgUBCMI6QQKAV+KF4v7f6M4biuddvLW8fnDcfVSnHhwoX27LPP2imnnOLuV0V55cqV3eNUVaKg/Ouvv3ZBucYf3Llzp3Xo0MFmzZrlxi8EgmEICLVPj4uItPAsDhDrfk3fEL/PzQ8g9yLColwormsApUDU8fpWaeZJzXy6u99jFtW4uNcMOfCeCCuE4gAAAACAwsIY4wgZ+QmUGHO8eENxjSr+vpkNNbPn/MJxzffGG2/4DpBVMrMGZlZRv1e/cLxx48Z2zz33uEtWFJqrlToQjDbHH3BjiusknuzERETYnqRENz+fYwCAkBXZwCwsxsyTYBYWd/R0d3+sWZS+WaKoLFq0yMaPH289evSwrl31jT9z+ptAnZ2qV6/uxg5X63RVjc+fP9/9HUBADgAAAAAoKCrGEXKBkgKj7Gh6UkqKmx9Fe4Ass1BcEd4YM7srbT4dBJsyZYovFL/ZzKqlfXg1SgvHzS8cP3jwYAm9IqDoJaemuoPCOR0Y9s6j+QEACFkRlc0iapt5jnzfPIru1/RwnXaJoqATXNW5aeLEidatW7ejhkry2rZtm7vs2LHDdX3SUEeXX365m7ZkyRIbOnSor7W6zJ492xYsWFBsrwMAAAAAUDoQjCNkECgFFlWN+LdP94biEpYhHPe628yeSPs51ePxhePR3vtSU2379u2Fsn5aFhBoosLD3Uki/u1FM+OdR/MDABDSopocuU7ZcfTFTaeNenF1h8o4VJKXbv/111++294TXp9++mlr3bq1u2/kyJFWtWpVNzRSlSpV3NBIGod848aNdvzxx7uLfpZPPvnEdx8AAAAAAP5opY6QDJSyC8cJlIqHWim+9tprbluvNbOHzOzZtFDcPxyvYmavmVn/tHkUV0empNjqjRvtrHbtTIe/kvyWqzHFC0oH49auXetaswOBpHZcOYsOj7DElBSLjcz6v3BNj46IsDpx5Yt1/QAACDhRLdK+QWZ20mN42nQUdSiuE2HX+oXj0q9fPxeK63Zc3JFW99Fp3+31fTwhIcENgaQhlWbMmGHLli1z1eTHHHOM9ezZ01WiJycn299//53uuQ8cOOAuAAAAAABkRDCOkEGgFFg0vqBaKupAmA6QjU27P2M4/nDaxUuN8C84cMDGT55sSeXKWfXjj7fwtJMY6tatazt37nSX/Dp06JC99dZb7gDbLbfcUoBXCBQ+jRd+TNnytmr/Hvc5FZ7JST7qphCfctgala9sFXIYixwAgFIvPM4s5tSSXouQDsW9QyYNNbPn/MJxzafQ29sJp4KZnWhmirn3ps23fv16u+mmm+yee+7J8vly6qQDAAAAAIAXwThCBoFS4FGViGQXjmfmETP73/r1dv+IERYdHe06AKilYtmyZQtlvRS0P/XUU9axY8dCWR5QmE6uVsf2JiXazsRDFhcRaTEREe49oPeQTuzRZ1jVmDLWrlrtkl5VAAAQYhYtWpRpKB6b1g3K/MLxKVOm+B5XR52f0sZ6a5QWjh/2a6vetGnTQvuuX/QS1Ls/F/NF+A0mBQAAAAAoDgTjCCkESsERjlfOUCWe0Ugz+0E/7FUtidnw4cNda/bCEBMT49ozlitXrlCWBxS2clHR1rlOA1u0Y7NtiN9ne5ISfZ9jOulHJ/boM0zzAQAAFKfx48ena5/uDcX9h0ryhuNed5vZUr8TYxWONzSzVWm3FY5v3749SIJxheKL02L93ByOaUs4DgAAAADFiGAcIYVAKXDDcbVS9FaNvJZDMD7R7+e+ffvao48+WuTrCAQSfUadVbue7U1KsM3xByw5NdWiwsPdEBB0uwAAACVFJ6u+9tpr7u8rjSn+UCZDJSkcr5L2nb9/2jyXeDx2KEGhspkao2/KEC1XrqxTZwtGgb06Q0VmM6xWwaWkrbni/SPDPWUuNW2+******************************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********************************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", "text/plain": ["<Figure size 2000x1500 with 7 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "📊 DOCUMENT ANALYSIS SUMMARY\n", "============================================================\n", "🎯 Clustering Results:\n", "   • Optimal clusters: 8\n", "   • Silhouette score: 0.068\n", "\n", "📋 Cluster Breakdown:\n", "   • C0 (estimation, parametric, based): 12 documents\n", "   • C1 (cba, costs, benefits): 5 documents\n", "   • C2 (risk, risks, identification): 5 documents\n", "   • C3 (budget, cost, evm): 4 documents\n", "   • C4 (roi, costs, net): 4 documents\n", "   • C5 (sensitivity, sensitivity analysis, variable): 2 documents\n", "   • C6 (cocomo, drivers, cost drivers): 3 documents\n", "   • C7 (sustainability, return, irr): 3 documents\n", "\n", "📊 Token Statistics:\n", "   • Total tokens: 218,002\n", "   • Average: 5,737\n", "   • Range: 633 - 18,360\n", "\n", "✅ Analysis completed successfully!\n"]}], "source": ["temas = await index_repository.get_index_elements(1, structure_type=\"tema\")\n", "result, docs = analyze_documents_per_topic(temas[2].id, temas[2].name)"]}, {"cell_type": "code", "execution_count": 19, "id": "2fcc016a", "metadata": {}, "outputs": [{"data": {"text/plain": ["[('Analogous Estimating: How to Use Past Projects to Estimate Cost and Duration', '- Explains analogous estimating by leveraging data from similar past projects for initial cost and duration estimates.\\n- Highlights speed and ease o ... (268 characters truncated) ... nships and unit costs.\\n- Describes process: identify comparable projects, compare key variables, and adjust estimates for current project specifics.', 'https://monday.com/blog/project-management/analogous-estimating/', 2034),\n", " ('A Structured Approach to Project Estimation', '- Many organizations face challenges in estimating project effort, often resulting in projects being off-track before starting.\\n- Traditional estima ... (684 characters truncated) ...  of the estimation approach is promoted through organizational standards and training to ensure consistent application and improved project outcomes.', 'https://www.pmi.org/learning/library/project-estimation-reduce-hectic-deloitte-6061', 4128),\n", " ('Bottom-up Estimating', '- Bottom-up estimating is a project management technique that estimates costs, duration, or resource needs at a granular level, typically work packag ... (989 characters truncated) ... es.\\n- Bottom-up estimating requires a detailed work breakdown structure and can also be used for assessing change requests in cost-benefit analyses.', 'https://project-management.info/bottom-up-estimating-definition-example-pros-cons/', 2152),\n", " ('Calculating ROI in Software Development', '- ROI evaluates software investments’ profitability relative to costs.\\n- Key components: initial investment, operational costs, and revenue generate ... (269 characters truncated) ... sizes tracking both short-term and long-term ROI metrics.\\n- Tools like DailyBot enhance productivity and transparency, boosting ROI in remote teams.', 'https://www.dailybot.com/insights/how-to-calculate-roi-in-software-development', 2135),\n", " ('COCOMO II - Constructive Cost Model', '- Online tool implementing COCOMO II model to calculate effort, schedule, and cost based on software size and cost drivers.\\n- Supports size metrics  ... (342 characters truncated) ...  Monte Carlo simulation and automatic calculations.\\n- Guides effort distribution across inception, elaboration, construction, and transition phases.', 'http://softwarecost.org/tools/COCOMO/', 633),\n", " ('Constructive Cost Model (COCOMO)', '- Introduces COCOMO model with three project types: Organic, Semi-detached, and Embedded.\\n- Details Basic, Intermediate, and Detailed COCOMO variant ... (315 characters truncated) ... ncorporating phase-wise adjustments.\\n- Highlights COCOMO’s use in cost estimation, resource management, planning, risk management, and benchmarking.', 'https://www.geeksforgeeks.org/software-engineering-cocomo-model/', 4001),\n", " ('Cost-Benefit Analysis for Project Business Cases', '- Defines CBA as evaluating project options using indicators: NPV, BCR, Payback Period, ROI, IRR.\\n- Outlines a seven-step CBA process: scope definit ... (258 characters truncated) ... d recommends scenario analysis and stakeholder communication.\\n- Links CBA with business case and benefits management plan for project authorization.', 'https://project-management.info/cost-benefit-analysis-business-cases/', 5446),\n", " ('Cost-Benefit Analysis for ROI: Step-by-Step Guide', '- Presents CBA steps: goals setting, listing costs/benefits, monetization, metric calculation, review for decision-making.\\n- Defines ROI metrics: NP ... (188 characters truncated) ... ty costs and using Excel for calculations.\\n- Warns against common CBA mistakes: hidden costs, unrealistic assumptions, ignoring intangible benefits.', 'https://ceohangout.com/cost-benefit-analysis-for-roi-step-by-step-guide/', 2586),\n", " ('Cost Benefit Analysis: What Is It and How to Do It | The Workstream', \"- Cost-benefit analysis (CBA) is a data-driven method to evaluate a project's financial benefits and costs to determine its feasibility and profitabi ... (784 characters truncated) ...  and making decisions.\\n- Confluence software can facilitate CBA by enabling collaboration, real-time updates, and centralized project documentation.\", 'https://www.atlassian.com/work-management/strategic-planning/cost-benefit-analysis', 13234),\n", " ('Cost Estimation Models in Software Engineering', '- Mathematical (parametric) models estimate costs via algorithms or equations.\\n- Empirical techniques use historical data and expert assumptions.\\n- ... (282 characters truncated) ... curacy vs. effort, data requirements, and method suitability.\\n- Useful primer on analogies, parametric, bottom-up, and advanced ML-based approaches.', 'https://www.geeksforgeeks.org/cost-estimation-models-in-software-engineering/', 5994),\n", " ('Cost estimation of a software product using COCOMO II.2000 model – a case study', '- Applies COCOMO II.2000 model to ten embedded systems projects in automotive software services.\\n- Collects actual effort and logical source lines o ... (197 characters truncated) ... erforms experience-based or price-to-win strategies.\\n- Reinforces importance of calibrated parametric models for competitive software industry bids.', 'https://www.sciencedirect.com/science/article/abs/pii/S0263786304001024', 5511),\n", " ('Financial Viability Metrics in Open Source Projects: A Deep Dive into Economic Growth', '- Open-source projects use funding models like corporate sponsorships, crowdfunding, and dual licensing.\\n- Emerging trends include blockchain integr ... (370 characters truncated) ... community/commercial interests, digital asset volatility.\\n- Future: hybrid funding models, global standardization, enhanced incentives and security.', 'https://dev.to/rachellovestowrite/financial-viability-metrics-in-open-source-projects-a-deep-dive-into-economic-growth-5bea', 4336),\n", " ('How Software Cost Estimation Works and Why We Need Cost Estimates', '- Explains size metrics (e.g., SLOC) and cost drivers (product, process, personnel, environment) in estimation.\\n- Describes generalizable models: CO ... (157 characters truncated) ... dels and iterative updates to improve accuracy.\\n- Advocates uncertainty and risk analysis to manage changes in scope and maintain estimate validity.', 'https://insights.sei.cmu.edu/blog/software-cost-estimation-explained/', 3535),\n", " ('How to Calculate Project ROI: Formulas, Analysis, and Tips', '- Defines Project ROI as (net profit / cost of investment) × 100% and distinguishes anticipated vs actual ROI.\\n- Describes cost-benefit ratio and pa ... (188 characters truncated) ... result collection, stakeholder reporting.\\n- Discusses challenges: isolating project effects, selecting suitable projects, making accurate estimates.', 'https://productive.io/blog/how-to-calculate-roi-for-a-project/', 2699),\n", " ('How to Calculate Return On Investment For Software Development', '- Defines ROI in software development as net profit divided by total investment costs.\\n- Highlights ROI’s role in management decisions and project p ... (263 characters truncated) ... mplementation, maintenance, and training costs.\\n- Emphasizes holistic approach considering all development and maintenance factors for accurate ROI.', 'https://ardas-it.com/how-to-calculate-roi-for-software-development', 1908),\n", " ('How to Calculate Software ROI?', '- Defines ROI = (Net Benefit ÷ Cost of Investment) × 100.\\n- Net benefits include profits and cost savings; costs cover purchase, implementation, tra ... (327 characters truncated) ... justments, and intangible benefit recognition.\\n- Best practices: negotiate pricing, consider open-source, thorough training, continuous improvement.', 'https://ideamaker.agency/software-roi/', 3960),\n", " ('Key Performance Indicators for Program Cost Control and Efficiency', '- Program budgeting aligns financial resources with objectives, scope, governance, and prioritization.\\n- Cost control tracks spending and performanc ... (390 characters truncated) ... agement, governance, risk management, continuous improvement.\\n- Periodic review and adjustment of KPIs ensures ongoing efficiency and effectiveness.', 'https://www.linkedin.com/advice/0/what-key-performance-indicators-program-cost', 5882),\n", " ('Methodology for Risk Analysis of Projects', '- Proposes a cyclical risk management methodology: identification, evaluation, response, monitoring.\\n- Details identification techniques: brainstorm ... (228 characters truncated) ... , mitigation, acceptance, with ongoing reassessment.\\n- Emphasizes risk management planning with assigned responsibilities and continuous monitoring.', 'https://pdfs.semanticscholar.org/463a/06e62328c540d37146d866571666c7c6822c.pdf', 3256),\n", " ('Project Cost Management', '- Covers cost estimation, budgeting, cost control, monitoring, and reporting to deliver projects within budget.\\n- Lists estimation techniques: top-d ... (384 characters truncated) ... tinuous monitoring.\\n- Recommends best practices: early planning, clear roles, training, and disciplined monitoring to address estimation challenges.', 'https://www.6sigma.us/project-management/project-cost-management/', 2971),\n", " ('Project Cost Variance Analysis: Key Metrics and Indicators', '- Project cost variance (PCV) is the difference between actual and planned project costs, indicating if a project is over or under budget.\\n- Key met ... (727 characters truncated) ...  Effective project cost variance management supports communication with stakeholders and informs better project delivery and cost control strategies.', 'https://www.linkedin.com/advice/0/what-key-metrics-indicators-project-cost-variance', 8717),\n", " ('Proven Financial Models for Sustainable Projects', '- Integrates environmental and social impacts into financial planning for sustainable projects.\\n- Key models include project finance, green bonds, a ... (354 characters truncated) ... it analysis, public–private partnerships, risk diversification.\\n- Future trends: green finance innovations and rising investor sustainability focus.', 'https://bottombillioncorp.com/financial-models-sustainable-projects/', 1596),\n", " ('Quantitative Risk Analysis: A Data-Driven Approach to Managing Uncertainty', '- Describes Quantitative Risk Analysis (QRA) to assign probabilities and impacts to identified risks.\\n- Outlines QRA steps: risk identification, qua ... (291 characters truncated) ... egration with Excel for industry-agnostic QRA applications.\\n- Recommends combining qualitative and quantitative analyses for robust risk management.', 'https://lumivero.com/resources/quantitative-risk-analysis-101/', 1804),\n", " ('Return on Investment (ROI) vs. Internal Rate of Return (IRR): An Overview', '- Return on investment (ROI) measures total growth of an investment over a set period, while internal rate of return (IRR) measures the annual growth ... (594 characters truncated) ... ROI and IRR values are roughly the same over one year but differ over longer periods.\\n- An average ROI of 7% is considered a good investment return.', 'https://www.investopedia.com/articles/investing/111715/return-investment-roi-vs-internal-rate-return-irr.asp', 4500),\n", " ('Risk Management in Software Development: An Expert Guide', '- Details ScienceSoft’s risk management approach for software projects spanning identification to mitigation.\\n- Lists risk factors: operational, tec ... (423 characters truncated) ... ies, and proactive mitigation.\\n- Includes client testimonials underscoring the value of early risk detection and clear reporting in project success.', 'https://www.scnsoft.com/software-development/about/how-we-work/risk-management', 9473),\n", " ('Risk Matrix and Why It Is Important', '- Defines a risk matrix: grid mapping probability vs impact to prioritize risks.\\n- Distinguishes risks (potential events) from issues (occurred even ... (286 characters truncated) ...  mitigation plans.\\n- Discusses benefits (proactive management, standardization, real-time views) and limitations (subjectivity, oversimplification).', 'https://www.saviom.com/blog/risk-matrix-and-why-it-is-important/', 5380),\n", " ('Software Cost Estimation', '- Forecasts effort, duration, and financial cost for development, deployment, and maintenance.\\n- Outlines components: project size, complexity, tech ... (156 characters truncated) ... evelopment, justification/documentation.\\n- Lists techniques: expert judgment, analogous, top-down, bottom-up, COCOMO model, function point analysis.', 'https://www.geeksforgeeks.org/software-cost-estimation/', 6041),\n", " ('Software Cost Estimation Approaches: A Survey', '- Surveys five fundamental software cost estimation approaches: COCOMO, Feed-Forward Neural Network with PCA, Putnam/SLIM, Function Point Analysis, a ... (405 characters truncated) ... ds and comparing results for realistic estimates.\\n- Highlights the importance of automatable, validated, and empirically supported estimation tools.', 'https://pdfs.semanticscholar.org/2002/aecfd96088e85eda58afb6b639cb26be6620.pdf', 11847),\n", " ('Software Cost Estimation: Key Models, Factors, and Tools for Accurate Project Budgeting', '- Describes popular estimation models: COCOMO, Function Point Analysis, PERT, Use Case Points, Wideband Delphi, Parametric Estimation, Monte Carlo Si ... (348 characters truncated) ... atica, CostOS, QuickBooks.\\n- Highlights the purpose of cost estimation in planning expenditures, predicting risks, and ensuring successful delivery.', 'https://keenethics.com/blog/software-cost-estimation', 5628),\n", " ('Software Cost Estimation Models in Software Planning', '- Presents static single-variable (size-based) and multivariable cost models for software estimation.\\n- Covers analogy-based estimation, three-point ... (102 characters truncated) ... r effort, documentation, and duration estimation.\\n- Emphasizes the importance of pre-development cost forecasting to guide budgeting and scheduling.', 'https://www.geeksforgeeks.org/software-cost-estimation-models-in-software-planning/', 5787),\n", " ('Software Cost Estimation Techniques: An Overview of Algorithmic, Non-Algorithmic and Learning-Oriented Approaches', '- Categorizes estimation into algorithmic (COCOMO I & II, FPA, Putnam), non-algorithmic (Expert Judgment, Top-Down, Bottom-Up), and learning-oriented ... (389 characters truncated) ... l to avoid overestimation losses or underestimation failures.\\n- Suggests future research on time-series models (e.g., LSTM) for scheduling accuracy.', 'https://www.scirp.org/journal/paperinformation?paperid=93452', 13557),\n", " ('Software Size Estimation: A survey', \"- Software size prediction is essential for estimating project cost, effort, schedules, and duration.\\n- Main software size estimation methods includ ... (610 characters truncated) ... he software lifecycle.\\n- No single method provides fully accurate size estimation; combining multiple approaches is recommended for better accuracy.\", 'https://pdfs.semanticscholar.org/f14e/7be730b081a047ef465c037f510197c8b8c6.pdf', 6127),\n", " ('Sustainability-Oriented Financial Resource Allocation in a Project Portfolio through Multi-Criteria Decision-Making', '- Integrates a composite sustainability index into Markowitz’s mean-variance risk–return framework.\\n- Uses MCDM methods to optimize expected return, ... (343 characters truncated) ... nability criteria are applied and suggests further research.\\n- Model adaptable to other sectors by substituting an appropriate sustainability index.', 'https://pdfs.semanticscholar.org/d442/3787886067ebda59725a03cf2ce2479f0a81.pdf', 15683),\n", " ('The Ultimate Guide to Parametric Estimating in Project Management', '- Parametric estimating is a statistical technique that calculates project time, cost, and resources using historical and statistical data.\\n- It dif ... (743 characters truncated) ... oftware supports parametric estimating by providing centralized data, analytics, and collaboration tools to improve project estimation and execution.', 'https://www.wrike.com/blog/guide-to-parametric-estimating-in-project-management/', 18360),\n", " ('Tracking Project Progress with Earned Value Management Metrics - A Real Case', '- Project management involves planning, organizing, motivating, and controlling resources to meet goals within time and budget constraints.\\n- Earned ... (805 characters truncated) ... - The decision model simplifies interpretation of EVM data and is more suitable for medium to large structured projects rather than small agile ones.', 'https://pdfs.semanticscholar.org/150b/288566e1284e03bb2e60ffc1da0a9f0b0e23.pdf', 6453),\n", " ('Using a Risk Matrix (Template Included)', '- A risk matrix is a visual tool used by project managers to assess the potential impact and probability of risks on a project.\\n- The risk matrix he ... (745 characters truncated) ... k analysis complements the risk matrix by providing a structured approach to identify, rate, and prioritize risks visually represented in the matrix.', 'https://www.projectmanager.com/blog/risk-assessment-matrix-for-qualitative-analysis', 11541),\n", " ('What Is a Cost-Benefit Analysis?', '- Defines cost-benefit analysis (CBA) to compare present value of benefits against project costs.\\n- Explains identifying direct, indirect, intangibl ... (317 characters truncated) ... ality, project size, duration, external economic factors.\\n- Mentions software tools for real-time tracking of costs, benefits, risks, and resources.', 'https://www.projectmanager.com/blog/cost-benefit-analysis-for-projects-a-step-by-step-guide', 4438),\n", " ('What is Sensitivity Analysis?', '- Explains sensitivity analysis: measures how input variable changes impact model output.\\n- Describes its role in Monte Carlo simulations and visual ... (155 characters truncated) ... thod for correlating input-output variables.\\n- Provides example: lower-cost, higher-variance materials can have strong impact on total project cost.', 'https://intaver.com/sensitivity-analysis-for-project-management/', 737),\n", " ('What Is Sensitivity Analysis?', \"- Sensitivity analysis shows how different values of an independent variable affect a dependent variable under a given set of assumptions.\\n- It is u ... (561 characters truncated) ... bserving effects on outcomes.\\n- Sensitivity analysis differs from scenario analysis, which examines specific scenarios rather than variable changes.\", 'https://www.investopedia.com/terms/s/sensitivityanalysis.asp', 3932)]"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["docs"]}, {"cell_type": "markdown", "id": "4f9ad7bc", "metadata": {}, "source": ["Dominios paywalled o bloqueo de acceso encontrados:\n", "* medium\n", "* springer\n", "* sciencedirect (da info pero no todo accesible)\n", "* routledge. (También no hay info, es una tienda de libros)\n", "* sagepub journals.\n", "* amazon\n", "* scribd (alguno se lo puede leer pero en general aquí suele haber cosas paywalled, o la gente puede subir libros pirateado)\n", "\n", "\n", "Categorias a excluir:\n", "* Paywalled\n", "* Tiendas de libros\n", "* Docs cortos o que no expliquen la info. Igual se puede incluso excluir por cantidad de tokens.\n", "* Enlaces de papers que no contienen la info, en vez de pdf. Ej: https://www.academia.edu/59857968/The_Shanghai_Cooperation_Organization_and_Eurasian_geopolitics_new_directions_perspectives_and_challenges"]}, {"cell_type": "markdown", "id": "5109b803", "metadata": {}, "source": ["### Cont gen process"]}, {"cell_type": "markdown", "id": "a4fc966d", "metadata": {}, "source": ["Here iterate on the instructions and create memories.\n", "\n", "Think about a way of doing this with celery so each task is handled differently. May be good to start using it and set it up to work similar as our tasks. Do some tweaks so it can be scaled with no problem\n", "\n", "Note. Would be great to split into paragraphs, detect if paragraphs have tables or diagrams or even other elements. (Ask <PERSON> for help)"]}, {"cell_type": "code", "execution_count": null, "id": "db60f4c4", "metadata": {}, "outputs": [], "source": ["llm_manager = DependencyContainer.get_llm_manager()"]}, {"cell_type": "code", "execution_count": null, "id": "8839f5b1", "metadata": {}, "outputs": [], "source": ["from cont_gen_prompts import prompt_contenido_tema"]}, {"cell_type": "code", "execution_count": null, "id": "5efb4e82", "metadata": {}, "outputs": [{"data": {"text/plain": ["('Introducción al pensamiento algorítmico y al entorno JavaScript',\n", " [{'epigraph_name': 'Conceptos de algoritmo y pseudocódigo',\n", "   'epigraph_id': 390,\n", "   'epigraph_position': 1,\n", "   'didactic_instructions': '#### Introducción\\nComienza estableciendo la relación entre la resolución de problemas cotidianos y la formulación de un algoritmo, usando un ejemplo sencillo (p. ej., \"preparar una taza de té\"). Destaca que la programación parte de la capacidad humana para describir procesos paso a paso y que esos pasos, una vez formalizados, se convierten en una solución «computable».\\n\\n#### Desarrollo de conceptos clave\\n1. Presenta una definición rigurosa pero accesible de algoritmo; seguidamente enumera sus propiedades (finito, definido, efectivo).\\n2. Diferencia algoritmo, programa y lenguaje de programación, subrayando que el algoritmo es independiente de la sintaxis concreta.\\n3. Explica brevemente la utilidad del pseudocódigo como puente entre la idea y la implementación. Fija unas reglas sintácticas mínimas (uso de mayúsculas para palabras reservadas, sangrías, comentarios con `//`).\\n4. Introduce las tres estructuras básicas (secuencia, selección y repetición) y expón un micro-ejemplo para cada una en pseudocódigo.\\n5. Incluye un ejemplo integral: «calcular el máximo de tres números». Proporciona la descripción en lenguaje natural, el pseudocódigo y un diagrama de flujo mermaid sencillo que contenga inicio, comparaciones y fin. Indica al redactor que mantenga el diagrama en menos de diez nodos.\\n\\n#### Ejercicio propuesto (1 de 2 en todo el tema)\\nPlantea al lector redactar el pseudocódigo para \"contar el número de vocales en una palabra\" y, opcionalmente, bosquejar el diagrama de flujo. Sugiere comprobar la claridad y exhaustividad de los pasos.\\n\\n#### Conexión con epígrafes adyacentes\\nCierra la sección señalando que, aunque los algoritmos pueden escribirse en pseudocódigo, necesitan un lenguaje real para ejecutarse; adelanta que JavaScript será el vehículo elegido y que su historia se explora a continuación.\\n\\n#### Extensión esperada\\nIndica que el contenido resultante debe oscilar entre 1 100 y 1 300 palabras, incorporando un bloque de pseudocódigo y un diagrama mermaid.'},\n", "  {'epigraph_name': 'Historia y evolución de JavaScript',\n", "   'epigraph_id': 391,\n", "   'epigraph_position': 2,\n", "   'didactic_instructions': '#### Introducción\\nContextualiza la aparición de JavaScript a mediados de los años 90 como respuesta a la necesidad de dotar de dinamismo al navegador. Enlaza con la idea previa: las computadoras necesitaban un lenguaje sencillo para plasmar algoritmos directamente en la web.\\n\\n#### Desarrollo de conceptos clave\\n1. Traza una narrativa cronológica que cubra: creación por <PERSON> (1995), estandarización ECMAScript (1997), Ajax (2005), ES6/ES2015 (2015) y la irrupción de Node.js (2009) hasta la actualidad.\\n2. Explica brevemente la diferencia entre JavaScript, ECMAScript y los motores de ejecución (V8, SpiderMonkey). Destaca cómo cada avance amplió la capacidad de expresar algoritmos complejos.\\n3. Incluye una tabla con tres columnas (Versión, Año, Novedades relevantes) para ES3, ES5, ES6, ES7+, seña<PERSON>o caracterís<PERSON>s que serán ú<PERSON> en el curso (p. ej., `let/const`, arrow functions, `Map`, `Set`, `async/await`).\\n4. Aclara la expansión de JavaScript más allá del navegador: introducción de Node.js y su impacto en la programación de servidores, desarrollo de herramientas (npm, frameworks) y la convergencia \"full-stack\".\\n5. Ofrece un pequeño diagrama mermaid de línea temporal (opcional) para ilustrar cinco hitos esenciales, indicando al redactor mantenerlo simple y lineal.\\n\\n#### Conexión con epígrafes adyacentes\\nConcluye señalando que, para beneficiarse de toda esta evolución, el estudiante instalará en la siguiente sección el entorno de ejecución (Node.js) y un editor moderno (VS Code).\\n\\n#### Extensión esperada\\nSugiere entre 950 y 1 150 palabras, con una tabla y, opcionalmente, un diagrama mermaid.'},\n", "  {'epigraph_name': 'Instalación del entorno de desarrollo (Node.js, VS Code)',\n", "   'epigraph_id': 392,\n", "   'epigraph_position': 3,\n", "   'didactic_instructions': '#### Introducción\\nMotiva la necesidad de un entorno local que permita ejecutar los algoritmos del curso sin depender del navegador. Resalta la dualidad ejecución-servidor (Node.js) y edición (VS Code) como estándar de facto.\\n\\n#### Desarrollo de conceptos clave\\n1. Describe brevemente qué es Node.js y cómo actúa como motor en línea de comandos; diferencia entre LTS y versión actual.\\n2. Proporciona una guía paso a paso para instalar Node.js en Windows, macOS y Linux (descarga, asistente o gestor de paquetes). Incluye comandos de verificación `node -v` y `npm -v` en bloques de código.\\n3. Introduce el concepto de npm y enumsiona su papel como gestor de dependencias, pero pospone el uso avanzado para temas posteriores.\\n4. Explica la instalación de VS Code: descarga, configuración inicial y localización del terminal integrado.\\n5. Recomienda extensiones mínimas (ESLint, <PERSON><PERSON><PERSON>, JavaScript ES6 snippets) y muestra cómo habilitarlas en una tabla (Nombre, Propósito, Comando/Ruta de instalación).\\n6. Enseña a crear una carpeta de proyecto, abrirla en VS Code, generar `index.js` y ejecutar `node index.js` desde el terminal.\\n\\n#### Ejercicio propuesto (2 / 2 en todo el tema)\\nPide al estudiante crear la carpeta `intro-js`, añadir `index.js` con `console.log(\\'Entorno listo\\');`, ejecutarlo y hacer una captura de la versión de Node (se menciona que la captura no se incluirá en la redacción; basta con comprobar la salida en el terminal).\\n\\n#### Conexión con epígrafes adyacentes\\nSeñala que el entorno ya listo permitirá ejecutar el primer programa real del curso en la siguiente sección, enlazando con la inminente introducción al \"Hola, mundo\".\\n\\n#### Extensión esperada\\nRecomienda entre 850 y 1 050 palabras e incorporar una tabla de extensiones y varios bloques de comandos.'},\n", "  {'epigraph_name': 'Primer script: \"Hola, mundo\" en consola y navegador',\n", "   'epigraph_id': 393,\n", "   'epigraph_position': 4,\n", "   'didactic_instructions': \"#### Introducción\\nPresenta la primera oportunidad de transformar el pseudocódigo en un programa real. Destaca la gratificación inmediata de ver un resultado tangible y cómo esto afianza el vínculo entre teoría y práctica.\\n\\n#### Desarrollo de conceptos clave\\n1. Muestra el código mínimo en Node.js:\\n```javascript\\nconsole.log('Hola, mundo');\\n```\\n   • Explica la función `console.log`, su salida en la terminal y cómo ejecutarla con `node`.\\n2. Replica la experiencia en el navegador con un archivo HTML elemental que incluya un `<script>` interno y, en un segundo paso, un script externo. Indica cómo abrir DevTools y visualizar la consola.\\n3. Contrasta la ejecución en consola con la instrucción `alert('Hola, mundo');`, comentando brevemente la naturaleza modal de `alert` y su utilidad limitada.\\n4. Introduce la noción de interacción mínima mediante `prompt` para recoger el nombre del usuario y saludar usando template literals.\\n```javascript\\nconst nombre = prompt('¿Cómo te llamas?');\\nconsole.log(`¡Hola, ${nombre}!`);\\n```\\n5. Explica someramente la diferencia de alcance entre código que corre en Node y en el navegador (objetos `window`, `global`) sin profundizar todavía.\\n\\n#### Ejercicio propuesto\\nPide al estudiante modificar el script para que muestre el momento actual (con `Date`) y salude indicando la hora. Deja abierta la preparación para el próximo tema sobre variables y tipos de datos.\\n\\n#### Conexión con epígrafes adyacentes\\nFinaliza subrayando que, para enriquecer este simple programa, se necesitará entender cómo se declaran y utilizan variables y operadores, tema que se abordará a continuación.\\n\\n#### Extensión esperada\\nEntre 800 y 1 000 palabras, con dos listados de código y sin necesidad de tablas ni diagramas.\"}])"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["ti_result[0].topic_name, ti_result[0].epigraphs"]}, {"cell_type": "markdown", "id": "795e3078", "metadata": {}, "source": ["Vamos a probar búsqueda de queries sobre fuentes que tenemos con esta info.\n", "\n", "Y sacar suficientes queries y docs reelevantes para buscar sobre ello. \n", "\n", "Vamos a probar dos opciones:\n", "\n", "* 1. Modelo crea queries de búsqueda y acaba dando info reelevante. Aquí duda si dejar coger un doc entero -> Más determinista y fácil de evaluar. Da info sobre qué queries se han hecho (ayudará a darle contexto sobre en qué epigrafes puede usar)\n", "* 2. Mientras genera el modelo puede buscar entre medio, darle tool, menos determinista pero admite más potencial de mejora.\n", "\n", "\n", "Otras cosas que se pueden probar: de<PERSON><PERSON> leerse más info, darle más top k del rerank."]}, {"cell_type": "code", "execution_count": null, "id": "c9f477eb", "metadata": {}, "outputs": [], "source": ["from openai import AsyncOpenAI"]}, {"cell_type": "code", "execution_count": null, "id": "70fe07f9", "metadata": {}, "outputs": [], "source": ["client = AsyncOpenAI(api_key=os.getenv(\"OpenaiApiKey\"))"]}, {"cell_type": "markdown", "id": "a2s2xek59h9", "metadata": {}, "source": ["#### Option 1: Multiple Query Search Approach\n", "\n", "This approach allows the model to generate multiple search queries and retrieve relevant documents for each query before generating content."]}, {"cell_type": "code", "execution_count": null, "id": "vhcumta4opt", "metadata": {}, "outputs": [], "source": ["import asyncio\n", "from src.api.workflows.document_ingestion.retrievers import PGVectorRetriever\n", "from src.api.workflows.document_ingestion.reranker import Reranker\n", "from src.api.common.services.content_generator.utils.formatting import format_docs\n", "from pydantic import BaseModel\n", "\n", "class Query(BaseModel):\n", "    query: str\n", "    \n", "class SearchQueries(BaseModel):\n", "    queries: list[Query]\n", "\n", "async def generate_search_queries(\n", "    topic_info: dict, \n", "    epigraph_info: dict,\n", "    model: str = \"gpt-4.1\"\n", ") -> list[str]:\n", "    \n", "    system_prompt = \"\"\"\n", "    Tu tarea es generar queries de búsqueda para generar contenido sobre un contexto que se te proporcione.\n", "    Ya se ha encontrado información reelevante para esta información.\n", "    Lo que debes hacer es generar queries específicas para encontrar pasajes granulares sobre las fuentes que permitan asistir en la generación de contenido.\n", "    \"\"\"\n", "    \n", "    prompt = f\"\"\"\n", "    Tema: {topic_info['topic_name']}\n", "    Epígrafe: {epigraph_info['epigraph_name']}\n", "\n", "    Instrucciones didácticas:\n", "    {epigraph_info.get('didactic_instructions', 'N/A')}\n", "    \"\"\"\n", "    \n", "    response = await client.responses.parse(\n", "        instructions=system_prompt,\n", "        text_format=SearchQueries,\n", "        model=model,\n", "        input =[\n", "            {\"role\": \"user\", \"content\": prompt}\n", "        ]\n", "    )\n", "    \n", "    return response.output_parsed"]}, {"cell_type": "code", "execution_count": null, "id": "e7a7cdf8", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}