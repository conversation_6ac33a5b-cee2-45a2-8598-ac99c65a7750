{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "from pathlib import Path\n", "\n", "parent_dir =Path.cwd().parent.parent\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv()\n", "sys.path.append(str(parent_dir))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Podemos tener un ai_process que perfectamente sea. \n", "\n", "TOPIC_GENERATION. Que se meta en los metadatos de los otros como proceso padre o task padre. \n", "\n", "Eso es una manera de adaptar lo que hay. "]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from src.domain.models import AIProcess, AIProcessStatus, AIProcessType"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/Library/CloudStorage/OneDrive-UNIR/Code/cont-gen-poc/venv/lib/python3.12/site-packages/pydantic/_internal/_config.py:334: UserWarning: Valid config keys have changed in V2:\n", "* 'allow_population_by_field_name' has been renamed to 'populate_by_name'\n", "* 'smart_union' has been removed\n", "  warnings.warn(message, UserWarning)\n", "/Users/<USER>/Library/CloudStorage/OneDrive-UNIR/Code/cont-gen-poc/venv/lib/python3.12/site-packages/pydantic/_internal/_fields.py:200: UserWarning: Field name \"schema\" in \"GenerateCompetenciesFromIndexInput\" shadows an attribute in parent \"GenerateCompetenciesInput\"\n", "  warnings.warn(\n", "/Users/<USER>/Library/CloudStorage/OneDrive-UNIR/Code/cont-gen-poc/venv/lib/python3.12/site-packages/pydantic/_internal/_fields.py:160: UserWarning: Field \"model_info\" has conflict with protected namespace \"model_\".\n", "\n", "You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.\n", "  warnings.warn(\n"]}], "source": ["from sqlmodel import Session\n", "from src.api.common.services import AITracer"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from src.api.common.dependency_container import DependencyContainer\n", "\n", "DependencyContainer._initialize_database_engines()\n", "db_engine = DependencyContainer.get_database_engine()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["tracer = AITracer(db_engine=db_engine)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Lets imagine we want to have a big process that is generating everything for the Topic"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["with Session(db_engine) as session:\n", "    parent_process = tracer.start_process(session, AIProcessType.GENERATE_CONTENT_FOR_TOPIC, tema_id=1, indice_id=1)#For example"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["150"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["parent_process.id"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["with Session(db_engine) as session:\n", "    execution = tracer.log_execution(session,parent_process, {\"parametro\": \"hola\"}, \"Output modelo\", {\"id_indice\": 1}, status=AIProcessStatus.COMPLETED)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["<AIProcessStatus.IN_PROGRESS: 'IN_PROGRESS'>"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["parent_process.status"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now this parent process is in progress and we can start related ones and link them with the parent. \n", "\n", "For example: \n", "* Search process\n", "* Content plan process\n", "* Content Generation process\n", "\n", "When the status of all the dependent ones is correct we can change the status as SUCESSS. This can be created in the endpoint and passed around in the queue so the dependent processes reference this parent. "]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["with Session(db_engine) as session:\n", "    child_process_1 = tracer.trace_process(session, AIProcessType.SEARCH_SOURCES,\n", "                                           {\"data\": \"Test\"},\n", "                                           \"Sucess\",\n", "                                           {\"type\": \"TEST\"},\n", "                                            indice_id=1,\n", "                                            tema_id=1,\n", "                                            parent_id=parent_process.id)\n", "    child_process_2 = tracer.trace_process(session, AIProcessType.CONTENT_PLAN_WITH_INSTRUCTIONS,\n", "                                           {\"data\": \"Test 2\"},\n", "                                           \"Sucessful 2\",\n", "                                           {\"type\": \"TEST\"},\n", "                                            indice_id=1,\n", "                                            tema_id=1,\n", "                                            process_status=AIProcessStatus.FAILED,\n", "                                            parent_id=parent_process.id)\n", "\n", "    child_process_3 = tracer.trace_process(session, AIProcessType.CONTENT_GENERATION_WITH_SOURCES,\n", "                                           {\"data\": \"Test 3\"},\n", "                                           \"Sucessful 3\",\n", "                                           {\"type\": \"TEST\"},\n", "                                            indice_id=1,\n", "                                            tema_id=1,\n", "                                            parent_id=parent_process.id)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["with Session(db_engine) as session:\n", "    parent_process = session.get(AIProcess, parent_process.id)\n", "    children = parent_process.children"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Not correctly, "]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["[<AIProcessStatus.COMPLETED: 'COMPLETED'>,\n", " <AIProcessStatus.FAILED: 'FAILED'>,\n", " <AIProcessStatus.COMPLETED: 'COMPLETED'>]"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["[children.status for children in children]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can take a look at the children status to see if everything is completed sucesfully"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}