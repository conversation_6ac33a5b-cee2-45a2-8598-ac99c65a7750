{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-08-07 09:27:12,523 - logger - INFO - Initial user 'Unir demo' already exists.\n", "2025-08-07 09:27:13,062 - logger - INFO - Created prompts successfully\n"]}], "source": ["import sys\n", "from pathlib import Path\n", "\n", "parent_dir =Path.cwd().parent.parent\n", "sys.path.append(str(parent_dir))\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv()\n", "import os\n", "os.environ[\"DbHost\"] = \"localhost\"\n", "os.environ[\"DbPort\"] = \"5436\"\n", "from src.api.common.dependencies.get_session import get_session\n", "from src.api.common.dependency_container import DependencyContainer\n", "\n", "DependencyContainer.initialize(observability= False)\n", "session = next(get_session())"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-08-07 09:27:13,741 - logger - INFO - Entered in async session\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-08-07 09:27:13,800 - logger - INFO - Indice inside get subject index is, status=<IndiceStatus.CONTENT_GENERATION: 'CONTENT_GENERATION'> is_displayed=False created_at=datetime.datetime(2025, 8, 6, 13, 52, 30, 317986) id=2 order_id=2 version=1 created_by='ia_gen_user' updated_by='ia_gen_user' updated_at=datetime.datetime(2025, 8, 6, 16, 3, 29, 231653)\n", "2025-08-07 09:27:13,808 - logger - INFO - Bloques inside get subject index is, [<PERSON><PERSON><PERSON>(id=2, position=1, created_by='ia_gen_user', updated_by=None, created_at=datetime.datetime(2025, 8, 6, 13, 54, 1, 123642), updated_at=datetime.datetime(2025, 8, 6, 13, 54, 1, 123819), name='Panorama General de la Historia Universal', indice_id=2)]\n", "2025-08-07 09:27:13,809 - logger - INFO - Asignatura is, nombre='Introduccion a la historia - Curso de Historia General' estructura=Estructura(bloques_tematicos=[BloqueTematico(nombre='Panorama General de la Historia Universal', temas=[Tema(nombre='Fundamentos de la disciplina histórica', epigrafes=['Definición de historia y objeto de estudio', 'Tipos de fuentes: primarias y secundarias', 'Métodos de datación y ordenamiento cronológico', 'Introducción al análisis crítico y anacronismo']), <PERSON><PERSON>(nombre='La Prehistoria y las primeras civilizaciones', epigrafes=['Evolución humana y culturas paleolíticas', 'Revolución neolítica y sedentarización', 'Surgimiento de Mesopotamia y Egipto', 'Sistemas de escritura y primeras organizaciones políticas']), <PERSON><PERSON>(nombre='Grecia y Roma: cuna de la Antigüedad clásica', epigrafes=['Polis griegas y democracia ateniense', 'Helenismo y expansión cultural', 'República y Imperio romano', 'Aportaciones políticas, jurídicas y culturales']), Tema(nombre='La Edad Media: Europa, Islam y civilizaciones asiáticas', epigrafes=['Feudalismo y sociedad estamental europea', 'Expansión del Islam y califatos', 'Imperio bizantino y relaciones este-oeste', 'Cultura, arte y universidades medievales']), Tema(nombre='Renacimiento, Reforma y descubrimientos geográficos', epigrafes=['Humanismo y renacimiento artístico', 'Reforma protestante y Contrarreforma', 'Grandes exploraciones y choque de mundos', 'Cambios económicos: mercantilismo y capitalismo temprano']), Tema(nombre='Ilustración y revoluciones atlánticas', epigrafes=['Pensamiento ilustrado y razón crítica', 'Revolución estadounidense', 'Revolución francesa y era napoleónica', 'Independencias latinoamericanas']), Tema(nombre='Revolución industrial y siglo XIX', epigrafes=['Innovaciones tecnológicas y transformación productiva', 'Cuestión social y movimientos obreros', 'Nacionalismos y unificación de Italia y Alemania', 'Imperialismo y colonización global']), Tema(nombre='El siglo XX y el mundo contemporáneo', epigrafes=['Guerras mundiales y cambios geopolíticos', 'Guerra Fría y descolonización', 'Globalización y avances tecnológicos', 'Retos del siglo XXI: medioambiente y memoria histórica'])])])\n"]}], "source": ["index_repository = DependencyContainer().get_index_repository()\n", "content_generator = DependencyContainer().get_content_generator()\n", "indice_id= 2\n", "version = 1\n", "asignatura = await index_repository.get_subject_index(indice_id=indice_id)\n", "temas = await index_repository.get_index_elements(indice_id, structure_type=\"tema\")\n", "epigrafes = await index_repository.get_epigrafes_with_context( indice_id=indice_id, tema_id=temas[0].id, plan_version=version)\n", "epigrafe_ids = [e.id for e in epigrafes]"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["[<PERSON><PERSON>(name='Fundamentos de la disciplina histórica', position=1, memories=[{'version': 1, 'memories': [], 'created_at': '2025-08-06T16:17:48.170684'}], created_at=datetime.datetime(2025, 8, 6, 13, 54, 1, 129387), id=8, status=<TopicStatus.CONTENT_GENERATION: 'CONTENT_GENERATION'>, id_bloque=2, created_by='ia_gen_user', updated_by='ia_gen_user', updated_at=datetime.datetime(2025, 8, 6, 16, 17, 48, 171272)),\n", " <PERSON><PERSON>(name='La Prehistoria y las primeras civilizaciones', position=2, memories=[{'version': 1, 'memories': [], 'created_at': '2025-08-06T16:17:48.175082'}], created_at=datetime.datetime(2025, 8, 6, 13, 54, 1, 141255), id=9, status=<TopicStatus.CONTENT_GENERATION: 'CONTENT_GENERATION'>, id_bloque=2, created_by='ia_gen_user', updated_by='ia_gen_user', updated_at=datetime.datetime(2025, 8, 6, 16, 24, 12, 829514)),\n", " <PERSON><PERSON>(name='Grecia y Roma: cuna de la Antigüedad clásica', position=3, memories=[{'version': 1, 'memories': ['Estructura estándar de cada tema: Propós<PERSON> y encuadre; Desarrollo narrativo; Recursos diná<PERSON>os; Ejemplo práctico; Cierre; Extensión sugerida', 'Se adopta Mermaid para diagramas de línea de tiempo y de flujo'], 'created_at': '2025-08-06T16:17:48.178391'}], created_at=datetime.datetime(2025, 8, 6, 13, 54, 1, 145643), id=10, status=<TopicStatus.CONTENT_GENERATION: 'CONTENT_GENERATION'>, id_bloque=2, created_by='ia_gen_user', updated_by='ia_gen_user', updated_at=datetime.datetime(2025, 8, 6, 16, 31, 49, 628841)),\n", " <PERSON><PERSON>(name='La Edad Media: Europa, Islam y civilizaciones asiáticas', position=4, memories=[{'version': 1, 'memories': ['Estructura estándar de cada tema: Propósito y encuadre; Desarrollo narrativo; Recursos dinámicos; Ejemplo práctico; Cierre; Extensión sugerida', 'Se adopta Mermaid para diagramas de línea de tiempo y de flujo', 'Se adopta Mermaid para todos los diagramas (línea de tiempo, flujo, jer<PERSON>r<PERSON><PERSON>)'], 'created_at': '2025-08-06T16:17:48.181765'}], created_at=datetime.datetime(2025, 8, 6, 13, 54, 1, 147989), id=11, status=<TopicStatus.CONTENT_GENERATION: 'CONTENT_GENERATION'>, id_bloque=2, created_by='ia_gen_user', updated_by='ia_gen_user', updated_at=datetime.datetime(2025, 8, 6, 16, 38, 38, 482289)),\n", " <PERSON><PERSON>(name='<PERSON><PERSON><PERSON><PERSON><PERSON>, Reforma y descubrimientos geográficos', position=5, memories=[{'version': 1, 'memories': ['Estructura estándar de cada tema: Propósito y encuadre; Desarrollo narrativo; Recursos diná<PERSON>os; Ejemplo prá<PERSON>o; Cierre; Extensión sugerida', 'Se adopta Mermaid para diagramas de línea de tiempo y de flujo', 'Se adopta Mermaid para todos los diagramas (línea de tiempo, flujo, jerárquico)'], 'created_at': '2025-08-06T16:17:48.185232'}], created_at=datetime.datetime(2025, 8, 6, 13, 54, 1, 151076), id=12, status=<TopicStatus.CONTENT_GENERATION: 'CONTENT_GENERATION'>, id_bloque=2, created_by='ia_gen_user', updated_by='ia_gen_user', updated_at=datetime.datetime(2025, 8, 6, 16, 45, 17, 493311)),\n", " <PERSON><PERSON>(name='Ilustración y revoluciones atlánticas', position=6, memories=[{'version': 1, 'memories': ['Estructura estándar de cada tema: Propósito y encuadre; Desarrollo narrativo; Recursos dinámicos; Ejemplo práctico; Cierre; Extensión sugerida', 'Se adopta Mermaid para diagramas de línea de tiempo y de flujo', 'Se adopta Mermaid para todos los diagramas (línea de tiempo, flujo, jer<PERSON>r<PERSON>co)', 'Recursos dinámicos (tablas, diagramas y ejercicios) no cuentan en el recuento de palabras principal', 'Limitar a dos ejercicios prácticos por tema'], 'created_at': '2025-08-06T16:17:48.187958'}], created_at=datetime.datetime(2025, 8, 6, 13, 54, 1, 155729), id=13, status=<TopicStatus.CONTENT_GENERATION: 'CONTENT_GENERATION'>, id_bloque=2, created_by='ia_gen_user', updated_by='ia_gen_user', updated_at=datetime.datetime(2025, 8, 6, 16, 50, 41, 904676)),\n", " <PERSON><PERSON>(name='Revolución industrial y siglo XIX', position=7, memories=[{'version': 1, 'memories': ['Estructura estándar de cada tema: Propósito y encuadre; Desarrollo narrativo; Recursos dinámicos; Ejemplo práctico; Cierre; Extensión sugerida', \"Extensión orientativa de 'Pensamiento ilustrado y razón crítica': ≈1 500 palabras\", \"Extensión orientativa de 'Revolución estadounidense': ≈1 300 palabras\", 'Se adopta Mermaid para diagramas de línea de tiempo y de flujo', \"Extensión orientativa de 'Revolución francesa y era napoleónica': ≈1 800 palabras\", \"Extensión orientativa de 'Independencias latinoamericanas': ≈1 600 palabras\", 'Se adopta Mermaid para todos los diagramas (línea de tiempo, flujo, jerárquico)', 'Recursos dinámicos (tablas, diagramas y ejercicios) no cuentan en el recuento de palabras principal', 'Limitar a dos ejercicios prácticos por tema'], 'created_at': '2025-08-06T16:17:48.189756'}], created_at=datetime.datetime(2025, 8, 6, 13, 54, 1, 158189), id=14, status=<TopicStatus.CONTENT_GENERATION: 'CONTENT_GENERATION'>, id_bloque=2, created_by='ia_gen_user', updated_by='ia_gen_user', updated_at=datetime.datetime(2025, 8, 6, 16, 57, 32, 287881)),\n", " <PERSON><PERSON>(name='El siglo XX y el mundo contemporáneo', position=8, memories=[{'version': 1, 'memories': ['Estructura estándar de cada tema: Propósito y encuadre; Desarrollo narrativo; Recursos dinámicos; Ejemplo práctico; Cierre; Extensión sugerida', \"Extensión orientativa de 'Pensamiento ilustrado y razón crítica': ≈1 500 palabras\", \"Extensión orientativa de 'Revolución estadounidense': ≈1 300 palabras\", 'Se adopta Mermaid para diagramas de línea de tiempo y de flujo', \"Extensión orientativa de 'Revolución francesa y era napoleónica': ≈1 800 palabras\", \"Extensión orientativa de 'Independencias latinoamericanas': ≈1 600 palabras\", 'Se adopta Mermaid para todos los diagramas (línea de tiempo, flujo, jerárquico)', 'Recursos dinámicos (tablas, diagramas y ejercicios) no cuentan en el recuento de palabras principal', 'Limitar a dos ejercicios prácticos por tema'], 'created_at': '2025-08-06T16:17:48.190702'}], created_at=datetime.datetime(2025, 8, 6, 13, 54, 1, 160846), id=15, status=<TopicStatus.CONTENT_GENERATION: 'CONTENT_GENERATION'>, id_bloque=2, created_by='ia_gen_user', updated_by='ia_gen_user', updated_at=datetime.datetime(2025, 8, 6, 17, 4, 28, 825873))]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["temas"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["8"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["len(temas)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Cambiar para meterle la numeracion en bloque, tema epigrafe. "]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["from src.api.workflows.topics import GetContentMarkdownQueryParams, GetContentMarkdownWorkflow\n", "\n", "md_workflow = DependencyContainer.get_content_markdown_workflow()\n", "\n", "params = GetContentMarkdownQueryParams(plan_version=1, tema_id=1)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["content = md_workflow.execute(params).markdown_content"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["from IPython.display import Markdown, display"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["Asignatura(nombre='Introduccion a la historia - Curso de Historia General', estructura=Estructura(bloques_tematicos=[BloqueTematico(nombre='Panorama General de la Historia Universal', temas=[Tema(nombre='Fundamentos de la disciplina histórica', epigrafes=['Definición de historia y objeto de estudio', 'Tipos de fuentes: primarias y secundarias', 'Métodos de datación y ordenamiento cronológico', 'Introducción al análisis crítico y anacronismo']), Tema(nombre='La Prehistoria y las primeras civilizaciones', epigrafes=['Evolución humana y culturas paleolíticas', 'Revolución neolítica y sedentarización', 'Surgimiento de Mesopotamia y Egipto', 'Sistemas de escritura y primeras organizaciones políticas']), <PERSON><PERSON>(nombre='Grecia y Roma: cuna de la Antigüedad clásica', epigrafes=['Polis griegas y democracia ateniense', 'Helenismo y expansión cultural', 'República y Imperio romano', 'Aportaciones políticas, jurídicas y culturales']), Tema(nombre='La Edad Media: Europa, Islam y civilizaciones asiáticas', epigrafes=['Feudalismo y sociedad estamental europea', 'Expansión del Islam y califatos', 'Imperio bizantino y relaciones este-oeste', 'Cultura, arte y universidades medievales']), Tema(nombre='Renacimiento, Reforma y descubrimientos geográficos', epigrafes=['Humanismo y renacimiento artístico', 'Reforma protestante y Contrarreforma', 'Grandes exploraciones y choque de mundos', 'Cambios económicos: mercantilismo y capitalismo temprano']), Tema(nombre='Ilustración y revoluciones atlánticas', epigrafes=['Pensamiento ilustrado y razón crítica', 'Revolución estadounidense', 'Revolución francesa y era napoleónica', 'Independencias latinoamericanas']), Tema(nombre='Revolución industrial y siglo XIX', epigrafes=['Innovaciones tecnológicas y transformación productiva', 'Cuestión social y movimientos obreros', 'Nacionalismos y unificación de Italia y Alemania', 'Imperialismo y colonización global']), Tema(nombre='El siglo XX y el mundo contemporáneo', epigrafes=['Guerras mundiales y cambios geopolíticos', 'Guerra Fría y descolonización', 'Globalización y avances tecnológicos', 'Retos del siglo XXI: medioambiente y memoria histórica'])])]))"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["asignatura"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["from pathlib import Path"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["from src.api.workflows.topics import GetContentMarkdownQueryParams\n", "\n", "markdown_workflow = DependencyContainer.get_content_markdown_workflow()"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["folder_path = Path(f'data/{asignatura.nombre}_{version}')\n", "folder_path.mkdir(exist_ok = True)\n", "for i, tema in enumerate(temas):\n", "    try:\n", "        params = GetContentMarkdownQueryParams(plan_version=1, tema_id=tema.id, citations_at_end=True)\n", "        content = markdown_workflow.execute(params).markdown_content\n", "        #content, _= content_generator.get_content_markdown(session, tema_id=tema.id, content_plan_version=version, citations_at_end=True)\n", "        file_path = folder_path / f'Tema_{i + 1}.md'\n", "        file_path.write_text(content)\n", "    except Exception as e:\n", "        print(f'An exception ocurred saving topic number {i}: {e}')"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["data/Introduccion a la historia - Curso de Historia General_1/Tema_6.md\n", "data/Introduccion a la historia - Curso de Historia General_1/Tema_2.md\n", "data/Introduccion a la historia - Curso de Historia General_1/.DS_Store\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[WARNING] data/Introduccion a la historia - Curso de Historia General_1/.DS_Store is not UTF-8 encoded: falling back to latin1.\n", "pandoc: .docxd.docxa.docxt.docxa.docx/.docxI.docxn.docxt.docxr.docxo.docxd.docxu.docxc.docxc.docxi.docxo.docxn.docx .docxa.docx .docxl.docxa.docx .docxh.docxi.docxs.docxt.docxo.docxr.docxi.docxa.docx .docx-.docx .docxC.docxu.docxr.docxs.docxo.docx .docxd.docxe.docx .docxH.docxi.docxs.docxt.docxo.docxr.docxi.docxa.docx .docxG.docxe.docxn.docxe.docxr.docxa.docxl.docx_.docx1.docx/.docx..docxD.docxS.docx_.docxS.docxt.docxo.docxr.docxe.docx: withBinaryFile: does not exist (No such file or directory)\n", "HasCallStack backtrace:\n", "  collectBacktraces, called at libraries/ghc-internal/src/GHC/Internal/Exception.hs:92:13 in ghc-internal:GHC.Internal.Exception\n", "  toExceptionWithBacktrace, called at libraries/ghc-internal/src/GHC/Internal/IO.hs:260:11 in ghc-internal:GHC.Internal.IO\n", "  throwIO, called at libraries/ghc-internal/src/GHC/Internal/IO/Exception.hs:315:19 in ghc-internal:GHC.Internal.IO.Exception\n", "  ioException, called at libraries/ghc-internal/src/GHC/Internal/IO/Exception.hs:319:20 in ghc-internal:GHC.Internal.IO.Exception\n", "\n", "\n"]}, {"name": "stdout", "output_type": "stream", "text": ["data/Introduccion a la historia - Curso de Historia General_1/Tema_3.md\n", "data/Introduccion a la historia - Curso de Historia General_1/Tema_7.md\n", "data/Introduccion a la historia - Curso de Historia General_1/Tema_8.md\n", "data/Introduccion a la historia - Curso de Historia General_1/Tema_4.md\n", "data/Introduccion a la historia - Curso de Historia General_1/Tema_5.md\n", "data/Introduccion a la historia - Curso de Historia General_1/Tema_1.md\n"]}], "source": ["import subprocess\n", "for file_path in folder_path.iterdir():\n", "    subprocess.run(['pandoc', f'{file_path}','-o', f'{str(file_path).replace(file_path.suffix, '.docx')}' ,'--from', 'markdown+yaml_metadata_block+raw_html', '--top-level-division=chapter'])\n", "    print(file_path)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "#command\n", "pandoc md-file.md \\\n", "-o out-docx-file.docx \\\n", "--from markdown+yaml_metadata_block+raw_html \\\n", "--top-level-division=chapter"]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "ia_gestorcontenidosiagen_be (3.12.9)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}