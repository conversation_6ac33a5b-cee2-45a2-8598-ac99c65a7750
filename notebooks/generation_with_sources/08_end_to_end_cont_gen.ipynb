{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["Script to test out generation of individual parts.\n", "\n", "Figure out how to do individual tests without running the whole queue with current setup. "]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/pydantic/_internal/_fields.py:172: UserWarning: Field name \"schema\" in \"GenerateCompetenciesFromIndexInput\" shadows an attribute in parent \"GenerateCompetenciesInput\"\n", "  warnings.warn(\n", "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/pydantic/_internal/_fields.py:132: UserWarning: Field \"model_info\" in RegenerateCompetenciesRequest has conflict with protected namespace \"model_\".\n", "\n", "You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.\n", "  warnings.warn(\n", "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/pydantic/_internal/_fields.py:132: UserWarning: Field \"model_info\" in GenerateCompetenciesRequest has conflict with protected namespace \"model_\".\n", "\n", "You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.\n", "  warnings.warn(\n", "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/pydantic/_internal/_config.py:341: UserWarning: Valid config keys have changed in V2:\n", "* 'allow_population_by_field_name' has been renamed to 'populate_by_name'\n", "* 'smart_union' has been removed\n", "  warnings.warn(message, UserWarning)\n", "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/sentence_transformers/cross_encoder/CrossEncoder.py:11: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from tqdm.autonotebook import tqdm, trange\n", "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/pydantic/_internal/_fields.py:132: UserWarning: Field \"model_info\" in GenerateIndexRequest has conflict with protected namespace \"model_\".\n", "\n", "You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.\n", "  warnings.warn(\n", "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/pydantic/_internal/_fields.py:132: UserWarning: Field \"model_info\" in RegenerateIndexSchemaRequest has conflict with protected namespace \"model_\".\n", "\n", "You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.\n", "  warnings.warn(\n", "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/pydantic/_internal/_fields.py:132: UserWarning: Field \"model_info\" in RegenerateBlockSchemaRequest has conflict with protected namespace \"model_\".\n", "\n", "You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.\n", "  warnings.warn(\n", "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/pydantic/_internal/_fields.py:132: UserWarning: Field \"model_info\" in RegenerateTopicSchemaRequest has conflict with protected namespace \"model_\".\n", "\n", "You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.\n", "  warnings.warn(\n", "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/pydantic/_internal/_fields.py:132: UserWarning: Field \"model_info\" in GenerateSubjectCompetenciesRequest has conflict with protected namespace \"model_\".\n", "\n", "You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.\n", "  warnings.warn(\n", "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/pydantic/_internal/_fields.py:132: UserWarning: Field \"model_info\" in GenerateAllDidacticInstructionsRequest has conflict with protected namespace \"model_\".\n", "\n", "You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.\n", "  warnings.warn(\n", "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/pydantic/_internal/_fields.py:132: UserWarning: Field \"model_info\" in GenerateDidacticInstructionsRequest has conflict with protected namespace \"model_\".\n", "\n", "You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.\n", "  warnings.warn(\n", "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/pydantic/_internal/_fields.py:132: UserWarning: Field \"model_info\" in RegenerateDidacticInstructionsRequest has conflict with protected namespace \"model_\".\n", "\n", "You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.\n", "  warnings.warn(\n", "INFO:azure.identity._credentials.environment:No environment configuration found.\n", "INFO:azure.identity._credentials.managed_identity:ManagedIdentityCredential will use IMDS\n", "2025-01-13 11:20:36,615 - logger - INFO - Initial user 'Unir demo' already exists.\n", "2025-01-13 11:20:36,804 - logger - INFO - Include document router is True\n", "2025-01-13 11:20:36,804 - logger - INFO - Entered inside include document router and search engine\n", "2025-01-13 11:20:36,862 - logger - INFO - Prompt number > 5 found in the db, not creating them, if you added some new, run the prompt creation script for your specfic prompt, or add it to the db.\n", "WARNING:opentelemetry.util.re:Header format invalid! Header values in environment variables must be URL encoded per the OpenTelemetry Protocol Exporter specification: Authorization=Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJBcGlLZXk6MiJ9.V6VpZ07Sa8sSOIfNoQk4d_DSbDcgovIKOGrGqXn3YJA\n", "WARNING:opentelemetry.trace:Overriding of current TracerProvider is not allowed\n", "WARNING:opentelemetry.metrics._internal:Overriding of current MeterProvider is not allowed\n", "WARNING:opentelemetry.instrumentation.instrumentor:Attempting to instrument while already instrumented\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[1mLogfire\u001b[0m project URL: \u001b]8;id=597974;https://logfire.pydantic.dev/antoni0z/contenidos\u001b\\\u001b[4;36mhttps://logfire.pydantic.dev/antoni0z/contenidos\u001b[0m\u001b]8;;\u001b\\\n", "INFO:azure.identity._credentials.chained:DefaultAzureCredential acquired a token from AzureCliCredential\n", "INFO:azure.identity._credentials.chained:DefaultAzureCredential acquired a token from AzureCliCredential\n", "INFO:azure.identity._internal.decorators:AzureCliCredential.get_token succeeded\n", "INFO:azure.identity._credentials.default:DefaultAzureCredential acquired a token from AzureCliCredential\n"]}], "source": ["import os\n", "import sys\n", "from pathlib import Path\n", "\n", "parent_dir =Path.cwd().parent.parent\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv()\n", "sys.path.append(str(parent_dir))\n", "import logfire\n", "from src.api.common.dependency_container import DependencyContainer\n", "\n", "DependencyContainer.initialize(observability=False)\n", "logfire.configure(environment=\"local\")\n", "logfire.instrument_openai()\n", "logfire.instrument_anthropic()\n", "logfire.instrument_requests()\n", "logfire.instrument_pydantic()"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["\n", "import requests\n", "\n", "environ = os.getenv(\"GLOBAL__ENVIRONMENT\")\n", "headers = {\"x-api-key\": os.getenv(\"FASTAPI_API_KEY\")}\n", "base_url = \"http://0.0.0.0:8000\" if environ == \"Local\" else \"https://webapp-contenidos-dev-sc-001.azurewebsites.net/\""]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["tracing_global = {\n", "    \"base_url\": base_url\n", "    }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Create title"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["titles_url = base_url + \"/api/v1/titles\"\n", "title_response = requests.post(titles_url, headers = headers, json = {\n", "    \"name\":\"Matemática aplicada\",\n", "    \"type\":\"MASTER\",\n", "    \"description\": \"Master en matemática aplicada\"\n", "})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Create Subject"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["subjects_url = base_url + \"/api/v1/subjects\"\n", "subject_response = requests.post(subjects_url, headers = headers, json = {\n", "    \"name\": \"Matematicas para machine learning I\",\n", "    \"description\": \"Matematicas para machine learning asume prerequisitos en bachillerato de ciencias\",\n", "    \"title_id\": title_response.json()['id'],\n", "    \"year_number\": 1,\n", "    \"term_number\": 1,\n", "    \"credits\": 60\n", "})"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["tracing_global[\"subject_id\"] = subject_response.json()[\"id\"]\n", "tracing_global[\"order_id\"] = subject_response.json()[\"order_id\"]\n", "tracing_global[\"title_id\"] = title_response.json()['id']"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Competencies"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generate Competencies first time"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["from src.api.workflows.competencies.generate.generate_competencies_workflow import (\n", "    GenerateCompetenciesRequest,\n", ")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["definicion_competencia = \"Habilidad adquirida en el contexto académico de una asignatura, que es aplicable y útil en el ámbito laboral\""]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["10:21:04.396 generate_competencies\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/langchain_core/utils/utils.py:235: UserWarning: WARNING! extra_headers is not default parameter.\n", "                extra_headers was transferred to model_kwargs.\n", "                Please confirm that extra_headers is what you intended.\n", "  warnings.warn(\n", "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/langchain_core/utils/utils.py:235: UserWarning: WARNING! extra_headers is not default parameter.\n", "                extra_headers was transferred to model_kwargs.\n", "                Please confirm that extra_headers is what you intended.\n", "  warnings.warn(\n", "2025-01-13 11:21:04,790 - logger - INFO - User input: 253 has been saved\n", "2025-01-13 11:21:05,058 - logger - WARNING - Few shot examples not found\n"]}, {"name": "stdout", "output_type": "stream", "text": ["10:21:05.085   Message with 'claude-3-5-sonnet-20241022' [LLM]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "2025-01-13 11:21:11,612 - logger - INFO - Index number: 33 status changed to: IndiceStatus.NON_CONFIRMED_COMPETENCIES\n"]}], "source": ["with logfire.span(\"generate_competencies\", **tracing_global):\n", "    generate_competencies_request = GenerateCompetenciesRequest(order_id = subject_response.json()['order_id'], definicion_competencia= definicion_competencia, descripcion=subject_response.json()['description'])\n", "    generate_competencies_workflow = DependencyContainer.get_generate_competencies_workflow()\n", "    generate_competencies_response = await generate_competencies_workflow.execute(generate_competencies_request)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Here we can see the competencies created, if we would like to change something we can use regenerate. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Regenerate competencies"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["from src.api.workflows.competencies.regenerate.regenerate_competencies_workflow import (\n", "    RegenerateCompetenciesRequest,\n", ")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'subject_response' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[11], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m order_id \u001b[38;5;241m=\u001b[39m \u001b[43msubject_response\u001b[49m\u001b[38;5;241m.\u001b[39mjson()[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124morder_id\u001b[39m\u001b[38;5;124m'\u001b[39m]\n", "\u001b[0;31mNameError\u001b[0m: name 'subject_response' is not defined"]}], "source": ["order_id = subject_response.json()['order_id']"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["comentario = \"Quiero que pongas las competencias en un lenguaje más formal y que incluyas 3 más para hacer un total de 11. Hazlas centradas en matemáticas, ya que la asignatura es matemáticas y solo se debe cubrir temas sobre ella, sin entrar en machine learning que irá en otra asignatura específica de machine learning. En esta se introdcen los prerequisitos matemáticos para machine learning.\""]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["10:12:05.701 regenerate_competencies\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/langchain_core/utils/utils.py:235: UserWarning: WARNING! extra_headers is not default parameter.\n", "                extra_headers was transferred to model_kwargs.\n", "                Please confirm that extra_headers is what you intended.\n", "  warnings.warn(\n", "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/langchain_core/utils/utils.py:235: UserWarning: WARNING! extra_headers is not default parameter.\n", "                extra_headers was transferred to model_kwargs.\n", "                Please confirm that extra_headers is what you intended.\n", "  warnings.warn(\n", "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/langchain_core/utils/utils.py:235: UserWarning: WARNING! extra_headers is not default parameter.\n", "                extra_headers was transferred to model_kwargs.\n", "                Please confirm that extra_headers is what you intended.\n", "  warnings.warn(\n", "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/langchain_core/utils/utils.py:235: UserWarning: WARNING! extra_headers is not default parameter.\n", "                extra_headers was transferred to model_kwargs.\n", "                Please confirm that extra_headers is what you intended.\n", "  warnings.warn(\n", "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/langchain_core/utils/utils.py:235: UserWarning: WARNING! extra_headers is not default parameter.\n", "                extra_headers was transferred to model_kwargs.\n", "                Please confirm that extra_headers is what you intended.\n", "  warnings.warn(\n"]}, {"ename": "NameError", "evalue": "name 'order_id' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[13], line 3\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m logfire\u001b[38;5;241m.\u001b[39mspan(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mregenerate_competencies\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mtracing_global):\n\u001b[1;32m      2\u001b[0m     regenerate_competencies_workflow \u001b[38;5;241m=\u001b[39m DependencyContainer\u001b[38;5;241m.\u001b[39mget_regenerate_competencies_workflow()\n\u001b[0;32m----> 3\u001b[0m     regenerate_request \u001b[38;5;241m=\u001b[39m RegenerateCompetenciesRequest(order_id \u001b[38;5;241m=\u001b[39m \u001b[43morder_id\u001b[49m, comentario\u001b[38;5;241m=\u001b[39mcomentario)\n\u001b[1;32m      4\u001b[0m     regenerated_competencies \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mawait\u001b[39;00m regenerate_competencies_workflow\u001b[38;5;241m.\u001b[39mexecute(regenerate_request)\n", "\u001b[0;31mNameError\u001b[0m: name 'order_id' is not defined"]}], "source": ["with logfire.span(\"regenerate_competencies\", **tracing_global):\n", "    regenerate_competencies_workflow = DependencyContainer.get_regenerate_competencies_workflow()\n", "    regenerate_request = RegenerateCompetenciesRequest(order_id = order_id, comentario=comentario)\n", "    regenerated_competencies = await regenerate_competencies_workflow.execute(regenerate_request)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Before"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["[Compete<PERSON>ie(id=1, description='Capacidad para aplicar conceptos de álgebra lineal en el análisis y procesamiento de datos multidimensionales'),\n", " Competencie(id=2, description='Habilidad para implementar y optimizar algoritmos de machine learning utilizando cálculo diferencial y técnicas de optimización'),\n", " Competencie(id=3, description='Competencia en el uso de métodos estadísticos para el análisis y modelado de datos'),\n", " Competencie(id=4, description='Capacidad para interpretar y aplicar conceptos de probabilidad en el contexto de machine learning'),\n", " Competencie(id=5, description='Habilidad para traducir problemas de machine learning a su formulación matemática correspondiente'),\n", " Competencie(id=6, description='Competencia en el uso de técnicas de reducción dimensional y transformación de datos'),\n", " Competencie(id=7, description='Capacidad para evaluar y optimizar el rendimiento de modelos matemáticos aplicados a machine learning'),\n", " Competencie(id=8, description='Habilidad para implementar y adaptar funciones de coste y optimización en algoritmos de aprendizaje')]"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["generate_competencies_response.competencies"]}, {"cell_type": "markdown", "metadata": {}, "source": ["After edit"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["[Compete<PERSON>ie(description='Dominio de los fundamentos del álgebra lineal y su aplicación en el análisis de espacios vectoriales multidimensionales'),\n", " Competencie(description='Capacidad para aplicar conceptos avanzados de cálculo diferencial y técnicas de optimización matemática'),\n", " Compete<PERSON>ie(description='Dominio de métodos estadísticos fundamentales para el análisis cuantitativo y modelización matemática'),\n", " Compete<PERSON>ie(description='Comprensión y aplicación de la teoría de probabilidad y sus fundamentos matemáticos'),\n", " Competencie(description='Capacidad para formular y resolver problemas mediante modelos matemáticos'),\n", " Compete<PERSON>ie(description='Dominio de técnicas matemáticas para la reducción dimensional y transformaciones lineales'),\n", " Competencie(description='Capacidad para analizar y optimizar funciones matemáticas multivariables'),\n", " Competencie(description='Competencia en el desarrollo y aplicación de funciones de optimización matemática'),\n", " Compete<PERSON><PERSON>(description='Dominio de la teoría matricial y sus aplicaciones en transformaciones lineales'),\n", " Compete<PERSON><PERSON>(description='Capacidad para aplicar conceptos de análisis matemático en espacios métricos'),\n", " Competencie(description='Comprensión y aplicación de métodos numéricos para la resolución de problemas matemáticos complejos')]"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["regenerated_competencies.competencies"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/langchain_core/utils/utils.py:235: UserWarning: WARNING! extra_headers is not default parameter.\n", "                extra_headers was transferred to model_kwargs.\n", "                Please confirm that extra_headers is what you intended.\n", "  warnings.warn(\n", "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/langchain_core/utils/utils.py:235: UserWarning: WARNING! extra_headers is not default parameter.\n", "                extra_headers was transferred to model_kwargs.\n", "                Please confirm that extra_headers is what you intended.\n", "  warnings.warn(\n", "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/langchain_core/utils/utils.py:235: UserWarning: WARNING! extra_headers is not default parameter.\n", "                extra_headers was transferred to model_kwargs.\n", "                Please confirm that extra_headers is what you intended.\n", "  warnings.warn(\n", "2025-01-07 16:47:02,546 - logger - INFO - Executing SaveCompetenciesWorkflow with request: order_id=1 competencies=[Competencie(description='Dominio de los fundamentos del álgebra lineal y su aplicación en el análisis de espacios vectoriales multidimensionales'), Competencie(description='Capacidad para aplicar conceptos avanzados de cálculo diferencial y técnicas de optimización matemática'), Competencie(description='Dominio de métodos estadísticos fundamentales para el análisis cuantitativo y modelización matemática'), Competencie(description='Comprensión y aplicación de la teoría de probabilidad y sus fundamentos matemáticos'), Competencie(description='Capacidad para formular y resolver problemas mediante modelos matemáticos'), Competencie(description='Dominio de técnicas matemáticas para la reducción dimensional y transformaciones lineales'), Competencie(description='Capacidad para analizar y optimizar funciones matemáticas multivariables'), Compete<PERSON><PERSON>(description='Competencia en el desarrollo y aplicación de funciones de optimización matemática'), Competencie(description='Dominio de la teoría matricial y sus aplicaciones en transformaciones lineales'), Competencie(description='Capacidad para aplicar conceptos de análisis matemático en espacios métricos'), Competencie(description='Comprensión y aplicación de métodos numéricos para la resolución de problemas matemáticos complejos')]\n", "2025-01-07 16:47:02,553 - logger - INFO - Fetched Indice ID 1 with current status IndiceStatus.NON_CONFIRMED_COMPETENCIES\n", "2025-01-07 16:47:02,565 - logger.ContentGenerator - INFO - Indice at store indice after get or create indice is id=2 order_id=1 created_at=datetime.datetime(2025, 1, 7, 16, 47, 2, 561787) status=<IndiceStatus.NOT_STARTED: 'NOT_STARTED'> version=2 is_displayed=False updated_at=datetime.datetime(2025, 1, 7, 16, 47, 2, 561797)\n", "2025-01-07 16:47:02,587 - logger.ContentGenerator - INFO - Indice at end of store indice is id=2 order_id=1 created_at=datetime.datetime(2025, 1, 7, 16, 47, 2, 561787) status=<IndiceStatus.NOT_STARTED: 'NOT_STARTED'> version=2 is_displayed=False updated_at=datetime.datetime(2025, 1, 7, 16, 47, 2, 561797)\n", "2025-01-07 16:47:02,594 - logger.ContentGenerator - INFO - Index number: 2 status changed to: IndiceStatus.NON_CONFIRMED_COMPETENCIES\n", "2025-01-07 16:47:02,594 - logger - INFO - Successfully saved regenerated competencies with Indice ID: 2\n"]}], "source": ["from src.api.workflows.competencies.save.save_competencies_schemas import (\n", "    SaveCompetenciesRequest,\n", ")\n", "\n", "save_competencies_workflow = DependencyContainer.get_save_competencies_workflow()\n", "save_competencies_response = await save_competencies_workflow.execute(SaveCompetenciesRequest(order_id = order_id, competencies = regenerated_competencies.competencies))"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Index\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generate Index"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/langchain_core/utils/utils.py:235: UserWarning: WARNING! extra_headers is not default parameter.\n", "                extra_headers was transferred to model_kwargs.\n", "                Please confirm that extra_headers is what you intended.\n", "  warnings.warn(\n", "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/langchain_core/utils/utils.py:235: UserWarning: WARNING! extra_headers is not default parameter.\n", "                extra_headers was transferred to model_kwargs.\n", "                Please confirm that extra_headers is what you intended.\n", "  warnings.warn(\n", "2025-01-07 16:47:02,669 - logger - INFO - Competences are competencias=[Competencia(descripcion='Dominio de los fundamentos del álgebra lineal y su aplicación en el análisis de espacios vectoriales multidimensionales'), Competencia(descripcion='Capacidad para aplicar conceptos avanzados de cálculo diferencial y técnicas de optimización matemática'), Competencia(descripcion='Dominio de métodos estadísticos fundamentales para el análisis cuantitativo y modelización matemática'), Competencia(descripcion='Comprensión y aplicación de la teoría de probabilidad y sus fundamentos matemáticos'), Competencia(descripcion='Capacidad para formular y resolver problemas mediante modelos matemáticos'), Competencia(descripcion='Dominio de técnicas matemáticas para la reducción dimensional y transformaciones lineales'), Competencia(descripcion='Capacidad para analizar y optimizar funciones matemáticas multivariables'), Competencia(descripcion='Competencia en el desarrollo y aplicación de funciones de optimización matemática'), Competencia(descripcion='Dominio de la teoría matricial y sus aplicaciones en transformaciones lineales'), Competencia(descripcion='Capacidad para aplicar conceptos de análisis matemático en espacios métricos'), Competencia(descripcion='Comprensión y aplicación de métodos numéricos para la resolución de problemas matemáticos complejos')]\n", "2025-01-07 16:47:02,677 - logger - WARNING - Few shot examples not found\n"]}, {"name": "stdout", "output_type": "stream", "text": ["15:47:02.651 generate_index\n", "15:47:02.681   Message with 'claude-3-5-sonnet-20241022' [LLM]\n", "15:47:02.681   Message with 'claude-3-5-sonnet-20241022' [LLM]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "2025-01-07 16:47:18,007 - logger - WARNING - Few shot examples not found\n"]}, {"name": "stdout", "output_type": "stream", "text": ["15:47:18.018   Message with 'claude-3-5-sonnet-20241022' [LLM]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "2025-01-07 16:47:34,584 - logger - INFO - Schema has been generated\n", "2025-01-07 16:47:34,585 - logger.ContentGenerator - INFO - Indice at store indice after get or create indice is id=2 order_id=1 created_at=datetime.datetime(2025, 1, 7, 16, 47, 2, 561787) status=<IndiceStatus.NON_CONFIRMED_COMPETENCIES: 'NON_CONFIRMED_COMPETENCIES'> version=2 is_displayed=False updated_at=datetime.datetime(2025, 1, 7, 15, 47, 2, 592743)\n", "2025-01-07 16:47:34,617 - logger.ContentGenerator - INFO - Indice at end of store indice is id=2 order_id=1 created_at=datetime.datetime(2025, 1, 7, 16, 47, 2, 561787) status=<IndiceStatus.NON_CONFIRMED_COMPETENCIES: 'NON_CONFIRMED_COMPETENCIES'> version=2 is_displayed=False updated_at=datetime.datetime(2025, 1, 7, 15, 47, 2, 592743)\n", "2025-01-07 16:47:34,617 - logger - INFO - Index has been stored succesfully\n", "2025-01-07 16:47:34,618 - logger.ContentGenerator - INFO - Index number: 2 status changed to: IndiceStatus.INDICE\n"]}], "source": ["from src.api.workflows.indexes.generate.generate_index_schemas import (\n", "    GenerateIndexRequest,\n", ")\n", "\n", "generate_index_workflow = DependencyContainer.get_generate_index_workflow()\n", "with logfire.span(\"generate_index\", **tracing_global):\n", "    request = GenerateIndexRequest(order_id = order_id, numero_bloques = 2, numero_temas = 9, numero_epigrafes = 5, descripcion = subject_response.json()['description'])\n", "    index_response = await generate_index_workflow.execute(request)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["[BloqueTematico(nombre='FUNDAMENTOS DE ÁLGEBRA LINEAL Y ANÁLISIS MATEMÁTICO', temas=[Tema(nombre='Espacios Vectoriales y Estructuras Algebraicas', epigrafes=['Fundamentos de espacios vectoriales y subespacios', 'Bases, dimensiones y coordenadas', 'Productos internos y normas', 'Ortogonalidad y bases ortonormales', 'Aplicaciones en machine learning']), <PERSON><PERSON>(nombre='Matrices y Transformaciones Lineales', epigrafes=['Operaciones matriciales avanzadas', 'Determinantes y sus propiedades', 'Transformaciones lineales y representación matricial', 'Autovalores, autovectores y diagonalización', 'Descomposición en valores singulares (SVD)']), <PERSON><PERSON>(nombre='Cálculo Diferencial Multivariable', epigrafes=['Funciones multivariables y límites', 'Derivadas parciales y gradientes', 'Matriz jaco<PERSON> y hessiana', 'Optimización de funciones multivariables', 'Aplicaciones en redes neuronales']), <PERSON><PERSON>(nombre='Técnicas de Optimización', epigrafes=['Métodos de descenso del gradiente', 'Optimización con y sin restricciones', 'Multiplicadores de Lagrange', 'Métodos numéricos de optimización', 'Algoritmos de optimización en ML'])]),\n", " BloqueTematico(nombre='PROBABILIDAD, ESTADÍSTICA Y MÉTODOS NUMÉRICOS', temas=[Tema(nombre='Fundamentos de Probabilidad', epigrafes=['Teoría de probabilidad y axiomas', 'Variables aleatorias y distribuciones', 'Esperanza, varianza y momentos', 'Distribuciones multivariadas', 'Teoremas límite y aplicaciones']), <PERSON><PERSON>(nombre='Estadística para Machine Learning', epigrafes=['Estimación y máxima verosimilitud', 'Pruebas de hipótesis', 'Regresión y correlación', 'Análisis de varianza', 'Métodos de remuestreo']), <PERSON><PERSON>(nombre='Reducción Dimensional y Análisis Matricial', epigrafes=['Análisis de componentes principales (PCA)', 'Técnicas de factorización matricial', 'Métodos de proyección', 'Selección de características', 'Aplicaciones en procesamiento de datos']), <PERSON><PERSON>(nombre='Métodos Numéricos Avanzados', epigrafes=['Interpolación y aproximación numérica', 'Integración y diferenciación numérica', 'Resolución de sistemas de ecuaciones', 'Métodos iterativos y convergencia', 'Análisis de error y estabilidad']), Tema(nombre='Optimización Estocástica', epigrafes=['Algoritmos estocásticos fundamentales', 'Descenso del gradiente estocástico', 'Optimización online y batch', 'Métodos adaptativos', 'Regularización y generalización'])])]"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["index_response.index.estructura.bloques_tematicos"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Regenerate Index Full"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/langchain_core/utils/utils.py:235: UserWarning: WARNING! extra_headers is not default parameter.\n", "                extra_headers was transferred to model_kwargs.\n", "                Please confirm that extra_headers is what you intended.\n", "  warnings.warn(\n", "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/langchain_core/utils/utils.py:235: UserWarning: WARNING! extra_headers is not default parameter.\n", "                extra_headers was transferred to model_kwargs.\n", "                Please confirm that extra_headers is what you intended.\n", "  warnings.warn(\n", "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/langchain_core/utils/utils.py:235: UserWarning: WARNING! extra_headers is not default parameter.\n", "                extra_headers was transferred to model_kwargs.\n", "                Please confirm that extra_headers is what you intended.\n", "  warnings.warn(\n", "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/langchain_core/utils/utils.py:235: UserWarning: WARNING! extra_headers is not default parameter.\n", "                extra_headers was transferred to model_kwargs.\n", "                Please confirm that extra_headers is what you intended.\n", "  warnings.warn(\n", "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/langchain_core/utils/utils.py:235: UserWarning: WARNING! extra_headers is not default parameter.\n", "                extra_headers was transferred to model_kwargs.\n", "                Please confirm that extra_headers is what you intended.\n", "  warnings.warn(\n", "2025-01-07 16:47:34,779 - logger - INFO - Request regenerate_index_workflow: order_id=1 comentario='Divide el contenido en 3 bloques para que sea más digerible, manteniendo igual el contenido pero organizado en 3 partes' model_info=None\n", "2025-01-07 16:47:34,784 - logger - INFO - Fetched Indice ID 2 with current status IndiceStatus.INDICE\n"]}, {"name": "stdout", "output_type": "stream", "text": ["15:47:34.626 regenerate_index_full\n", "15:47:34.809   Message with 'claude-3-5-sonnet-20241022' [LLM]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "2025-01-07 16:47:52,286 - logger - INFO - Index Schema Instructions result: reasoning='Basándome en el comentario del usuario y el contexto proporcionado, he procedido a reorganizar el contenido en tres bloques temáticos manteniendo todo el contenido original pero distribuyéndolo de una manera más digerible. El proceso de razonamiento ha sido el siguiente:\\n\\n1. **Análisis de la Solicitud:**\\n   - El usuario solicita específicamente dividir el contenido en tres bloques para mejorar la digestibilidad del contenido.\\n   - No se requiere modificar el contenido en sí, solo su organización.\\n\\n2. **Evaluación del Contenido Actual:**\\n   - El índice actual está organizado en dos bloques principales.\\n   - Contiene nueve temas en total que cubren desde fundamentos hasta aplicaciones avanzadas.\\n\\n3. **Estrategia de Reorganización:**\\n   He decidido reorganizar los temas en tres bloques siguiendo una progresión lógica:\\n   - Primer bloque: Fundamentos matemáticos básicos (álgebra lineal y espacios vectoriales)\\n   - Segundo bloque: Cálculo y optimización\\n   - Tercer bloque: Probabilidad, estadística y métodos avanzados\\n\\n4. **Justificación de la Nueva Estructura:**\\n   - La nueva organización mantiene una progresión natural de aprendizaje.\\n   - Cada bloque tiene una carga temática equilibrada.\\n   - Se respeta la interrelación entre temas afines.\\n   - Se mantiene la alineación con las competencias de la asignatura.\\n\\n5. **Beneficios de la Nueva Estructura:**\\n   - Mejor digestibilidad del contenido como solicita el usuario.\\n   - Mayor claridad en la progresión del aprendizaje.\\n   - Agrupación más coherente de temas relacionados.\\n   - Distribución más equilibrada de la carga de contenido.' asignatura=Asignatura(asignatura='Matematicas para machine learning I - Matemática aplicada', estructura=Estructura(bloques_tematicos=[BloqueTematico(nombre='FUNDAMENTOS DE ÁLGEBRA LINEAL', temas=[Tema(nombre='Espacios Vectoriales y Estructuras Algebraicas', epigrafes=['Fundamentos de espacios vectoriales y subespacios', 'Bases, dimensiones y coordenadas', 'Productos internos y normas', 'Ortogonalidad y bases ortonormales', 'Aplicaciones en machine learning']), Tema(nombre='Matrices y Transformaciones Lineales', epigrafes=['Operaciones matriciales avanzadas', 'Determinantes y sus propiedades', 'Transformaciones lineales y representación matricial', 'Autovalores, autovectores y diagonalización', 'Descomposición en valores singulares (SVD)']), Tema(nombre='Reducción Dimensional y Análisis Matricial', epigrafes=['Análisis de componentes principales (PCA)', 'Técnicas de factorización matricial', 'Métodos de proyección', 'Selección de características', 'Aplicaciones en procesamiento de datos'])]), BloqueTematico(nombre='CÁLCULO Y OPTIMIZACIÓN', temas=[Tema(nombre='Cálculo Diferencial Multivariable', epigrafes=['Funciones multivariables y límites', 'Derivadas parciales y gradientes', 'Matriz jacobiana y hessiana', 'Optimización de funciones multivariables', 'Aplicaciones en redes neuronales']), Tema(nombre='Técnicas de Optimización', epigrafes=['Métodos de descenso del gradiente', 'Optimización con y sin restricciones', 'Multiplicadores de Lagrange', 'Métodos numéricos de optimización', 'Algoritmos de optimización en ML']), Tema(nombre='Optimización Estocástica', epigrafes=['Algoritmos estocásticos fundamentales', 'Descenso del gradiente estocástico', 'Optimización online y batch', 'Métodos adaptativos', 'Regularización y generalización'])]), BloqueTematico(nombre='PROBABILIDAD, ESTADÍSTICA Y MÉTODOS AVANZADOS', temas=[Tema(nombre='Fundamentos de Probabilidad', epigrafes=['Teoría de probabilidad y axiomas', 'Variables aleatorias y distribuciones', 'Esperanza, varianza y momentos', 'Distribuciones multivariadas', 'Teoremas límite y aplicaciones']), Tema(nombre='Estadística para Machine Learning', epigrafes=['Estimación y máxima verosimilitud', 'Pruebas de hipótesis', 'Regresión y correlación', 'Análisis de varianza', 'Métodos de remuestreo']), Tema(nombre='Métodos Numéricos Avanzados', epigrafes=['Interpolación y aproximación numérica', 'Integración y diferenciación numérica', 'Resolución de sistemas de ecuaciones', 'Métodos iterativos y convergencia', 'Análisis de error y estabilidad'])])]))\n", "2025-01-07 16:47:52,316 - logger.ContentGenerator - INFO - Index number: 2 status changed to: IndiceStatus.INDICE\n"]}], "source": ["from src.api.workflows.indexes.regenerate.regenerate_index_schemas import (\n", "    RegenerateIndexSchemaRequest,\n", ")\n", "\n", "with logfire.span(\"regenerate_index_full\", **tracing_global):\n", "    request = RegenerateIndexSchemaRequest(order_id = order_id, comentario = \"Divide el contenido en 3 bloques para que sea más digerible, manteniendo igual el contenido pero organizado en 3 partes\")\n", "    regenerate_index_workflow = DependencyContainer.get_regenerate_index_workflow()\n", "    regenerate_index_response = await regenerate_index_workflow.execute(request)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This way we can regenerate the full index, there are also options for regenerating parts"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/langchain_core/utils/utils.py:235: UserWarning: WARNING! extra_headers is not default parameter.\n", "                extra_headers was transferred to model_kwargs.\n", "                Please confirm that extra_headers is what you intended.\n", "  warnings.warn(\n", "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/langchain_core/utils/utils.py:235: UserWarning: WARNING! extra_headers is not default parameter.\n", "                extra_headers was transferred to model_kwargs.\n", "                Please confirm that extra_headers is what you intended.\n", "  warnings.warn(\n", "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/langchain_core/utils/utils.py:235: UserWarning: WARNING! extra_headers is not default parameter.\n", "                extra_headers was transferred to model_kwargs.\n", "                Please confirm that extra_headers is what you intended.\n", "  warnings.warn(\n", "2025-01-07 16:47:52,418 - logger - INFO - Executing SaveIndexWorkflow with request: order_id=1 index=Asignatura(asignatura='Matematicas para machine learning I - Matemática aplicada', estructura=Estructura(bloques_tematicos=[BloqueTematico(nombre='FUNDAMENTOS DE ÁLGEBRA LINEAL', temas=[Tema(nombre='Espacios Vectoriales y Estructuras Algebraicas', epigrafes=['Fundamentos de espacios vectoriales y subespacios', 'Bases, dimensiones y coordenadas', 'Productos internos y normas', 'Ortogonalidad y bases ortonormales', 'Aplicaciones en machine learning']), Tema(nombre='Matrices y Transformaciones Lineales', epigrafes=['Operaciones matriciales avanzadas', 'Determinantes y sus propiedades', 'Transformaciones lineales y representación matricial', 'Autovalores, autovectores y diagonalización', 'Descomposición en valores singulares (SVD)']), <PERSON><PERSON>(nombre='Reducción Dimensional y Análisis Matricial', epigrafes=['Análisis de componentes principales (PCA)', 'Técnicas de factorización matricial', 'Métodos de proyección', 'Selección de características', 'Aplicaciones en procesamiento de datos'])]), BloqueTematico(nombre='CÁLCULO Y OPTIMIZACIÓN', temas=[Tema(nombre='Cálculo Diferencial Multivariable', epigrafes=['Funciones multivariables y límites', 'Derivadas parciales y gradientes', 'Matriz jacobiana y hessiana', 'Optimización de funciones multivariables', 'Aplicaciones en redes neuronales']), Tema(nombre='Técnicas de Optimización', epigrafes=['Métodos de descenso del gradiente', 'Optimización con y sin restricciones', 'Multiplicadores de Lagrange', 'Métodos numéricos de optimización', 'Algoritmos de optimización en ML']), Tema(nombre='Optimización Estocástica', epigrafes=['Algoritmos estocásticos fundamentales', 'Descenso del gradiente estocástico', 'Optimización online y batch', 'Métodos adaptativos', 'Regularización y generalización'])]), BloqueTematico(nombre='PROBABILIDAD, ESTADÍSTICA Y MÉTODOS AVANZADOS', temas=[Tema(nombre='Fundamentos de Probabilidad', epigrafes=['Teoría de probabilidad y axiomas', 'Variables aleatorias y distribuciones', 'Esperanza, varianza y momentos', 'Distribuciones multivariadas', 'Teoremas límite y aplicaciones']), Tema(nombre='Estadística para Machine Learning', epigrafes=['Estimación y máxima verosimilitud', 'Pruebas de hipótesis', 'Regresión y correlación', 'Análisis de varianza', 'Métodos de remuestreo']), Tema(nombre='Métodos Numéricos Avanzados', epigrafes=['Interpolación y aproximación numérica', 'Integración y diferenciación numérica', 'Resolución de sistemas de ecuaciones', 'Métodos iterativos y convergencia', 'Análisis de error y estabilidad'])])]))\n", "2025-01-07 16:47:52,422 - logger - INFO - Fetched Indice ID 2 with current status IndiceStatus.INDICE\n", "2025-01-07 16:47:52,431 - logger.ContentGenerator - INFO - Indice at store indice after get or create indice is id=3 order_id=1 created_at=datetime.datetime(2025, 1, 7, 16, 47, 52, 427973) status=<IndiceStatus.NOT_STARTED: 'NOT_STARTED'> version=3 is_displayed=False updated_at=datetime.datetime(2025, 1, 7, 16, 47, 52, 427974)\n", "2025-01-07 16:47:52,469 - logger.ContentGenerator - INFO - Indice at end of store indice is id=3 order_id=1 created_at=datetime.datetime(2025, 1, 7, 16, 47, 52, 427973) status=<IndiceStatus.NOT_STARTED: 'NOT_STARTED'> version=3 is_displayed=False updated_at=datetime.datetime(2025, 1, 7, 16, 47, 52, 427974)\n", "2025-01-07 16:47:52,474 - logger.ContentGenerator - INFO - Index number: 3 status changed to: IndiceStatus.INDICE\n", "2025-01-07 16:47:52,474 - logger - INFO - Successfully saved regenerated index with ID: 3\n"]}], "source": ["from src.api.workflows.indexes.save.save_index_schemas import SaveIndexRequest\n", "\n", "request = SaveIndexRequest(index = regenerate_index_response.index, order_id = order_id)\n", "save_index_workflow = DependencyContainer.get_save_index_workflow()\n", "save_index_response = await save_index_workflow.execute(request)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Didactic instructions"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["didactic_instructions = \"\"\"\n", "El primer epígrafe de cada tema siempre será un punto de introducción y objetivos en los que explique brevemente la importancia del tema, su relación con el contenido anterior y posterior y los objetivos que va a trabajar.\\n\n", "Los demás epígrafes del tema tendrán:\\nDesglose de Conceptos Clave: declara los puntos teóricos a tratar en la elaboración del epígrafe, descríbelos de forma clara.\\n\n", "Ejemplos Prácticos: Proporciona ejemplos específicos y relevantes.\\nIntegrarás estos de forma concisa en el resultado final, incluyendo una o dos líneas de descripción de cada uno de ellos\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/langchain_core/utils/utils.py:235: UserWarning: WARNING! extra_headers is not default parameter.\n", "                extra_headers was transferred to model_kwargs.\n", "                Please confirm that extra_headers is what you intended.\n", "  warnings.warn(\n", "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/langchain_core/utils/utils.py:235: UserWarning: WARNING! extra_headers is not default parameter.\n", "                extra_headers was transferred to model_kwargs.\n", "                Please confirm that extra_headers is what you intended.\n", "  warnings.warn(\n", "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/langchain_core/utils/utils.py:235: UserWarning: WARNING! extra_headers is not default parameter.\n", "                extra_headers was transferred to model_kwargs.\n", "                Please confirm that extra_headers is what you intended.\n", "  warnings.warn(\n", "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/langchain_core/utils/utils.py:235: UserWarning: WARNING! extra_headers is not default parameter.\n", "                extra_headers was transferred to model_kwargs.\n", "                Please confirm that extra_headers is what you intended.\n", "  warnings.warn(\n", "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/langchain_core/utils/utils.py:235: UserWarning: WARNING! extra_headers is not default parameter.\n", "                extra_headers was transferred to model_kwargs.\n", "                Please confirm that extra_headers is what you intended.\n", "  warnings.warn(\n", "2025-01-07 16:47:52,630 - logger - INFO - Received GenerateAllDidacticInstructionsRequest: order_id=1 didactic_instructions='\\nEl primer epígrafe de cada tema siempre será un punto de introducción y objetivos en los que explique brevemente la importancia del tema, su relación con el contenido anterior y posterior y los objetivos que va a trabajar.\\n\\nLos demás epígrafes del tema tendrán:\\nDesglose de Conceptos Clave: declara los puntos teóricos a tratar en la elaboración del epígrafe, descríbelos de forma clara.\\n\\nEjemplos Prácticos: Proporciona ejemplos específicos y relevantes.\\nIntegrarás estos de forma concisa en el resultado final, incluyendo una o dos líneas de descripción de cada uno de ellos\\n' model_info=None\n", "2025-01-07 16:47:52,635 - logger - INFO - Fetched Indice ID 3 with current status IndiceStatus.INDICE\n", "2025-01-07 16:47:52,638 - logger - INFO - Retrieved 9 Temas for Indice ID 3.\n", "2025-01-07 16:47:52,642 - logger.ContentGenerator - INFO - Index number: 3 status changed to: IndiceStatus.INSTRUCTIONS\n", "2025-01-07 16:47:52,656 - logger - WARNING - Few shot examples not found\n", "2025-01-07 16:47:52,660 - logger - WARNING - Few shot examples not found\n", "2025-01-07 16:47:52,678 - logger - WARNING - Few shot examples not found\n", "2025-01-07 16:47:52,681 - logger - WARNING - Few shot examples not found\n"]}, {"name": "stdout", "output_type": "stream", "text": ["15:47:52.482 generate_all_didactic_instructions\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-01-07 16:47:52,695 - logger - WARNING - Few shot examples not found\n", "2025-01-07 16:47:52,698 - logger - WARNING - Few shot examples not found\n", "2025-01-07 16:47:52,710 - logger - WARNING - Few shot examples not found\n", "2025-01-07 16:47:52,712 - logger - WARNING - Few shot examples not found\n", "2025-01-07 16:47:52,721 - logger - WARNING - Few shot examples not found\n", "2025-01-07 16:47:52,723 - logger - WARNING - Few shot examples not found\n", "2025-01-07 16:47:52,732 - logger - WARNING - Few shot examples not found\n", "2025-01-07 16:47:52,734 - logger - WARNING - Few shot examples not found\n", "2025-01-07 16:47:52,743 - logger - WARNING - Few shot examples not found\n", "2025-01-07 16:47:52,745 - logger - WARNING - Few shot examples not found\n", "2025-01-07 16:47:52,753 - logger - WARNING - Few shot examples not found\n", "2025-01-07 16:47:52,755 - logger - WARNING - Few shot examples not found\n", "2025-01-07 16:47:52,763 - logger - WARNING - Few shot examples not found\n", "2025-01-07 16:47:52,765 - logger - WARNING - Few shot examples not found\n"]}, {"name": "stdout", "output_type": "stream", "text": ["15:47:52.772   Message with 'claude-3-5-sonnet-20241022' [LLM]\n", "15:47:52.772   Message with 'claude-3-5-sonnet-20241022' [LLM]\n", "15:47:52.773   Message with 'claude-3-5-sonnet-20241022' [LLM]\n", "15:47:52.773   Message with 'claude-3-5-sonnet-20241022' [LLM]\n", "15:47:52.773   Message with 'claude-3-5-sonnet-20241022' [LLM]\n", "15:47:52.774   Message with 'claude-3-5-sonnet-20241022' [LLM]\n", "15:47:52.774   Message with 'claude-3-5-sonnet-20241022' [LLM]\n", "15:47:52.774   Message with 'claude-3-5-sonnet-20241022' [LLM]\n", "15:47:52.775   Message with 'claude-3-5-sonnet-20241022' [LLM]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n"]}], "source": ["from src.api.workflows.topics.generate_all_didactic_instructions.generate_all_didactic_instructions_schemas import (\n", "    GenerateAllDidacticInstructionsRequest,\n", ")\n", "\n", "with logfire.span(\"generate_all_didactic_instructions\", **tracing_global):\n", "    generate_all_didactic_instructions_workflow = DependencyContainer.get_generate_all_didactic_instructions_workflow()\n", "    request = GenerateAllDidacticInstructionsRequest(order_id = order_id, didactic_instructions = didactic_instructions)\n", "    all_didactic_instructions = await generate_all_didactic_instructions_workflow.execute(request)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Esto lo ideal sería un objeto que vaya por temas. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Content Plans"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Ideas of improvement\n", "### Potential simplification of the Process\n", "\n", "* Here would be great to try creating the plans directly with o1 or similar. And compare with instructions + plans. It would be maybe a better abstraction to have. \n", "\n", "* ContentPlans + Summary of content plans we show to the user.\n", "\n", "* This way the editing is directly on the ContentPlan\n", "\n", "* Try generating content plans directly\n", "\n", "* We can improve the feedback loop and reduce the data points this way.\n", "\n", "* Make the didactic instructions just a summary of the content plans. \n", "\n", "* We can have less noise and clear examples of what a great end plan is, the summary can be paragraph by paragraph and we could also specify the type of content to the user. For example graph, text, even case study if we decide to include as a type.\n", "\n", "### Flexibility to generate from all the topic and its epigraphs so it creates the outline of everything with more context?\n", "\n", "o1 has much more output tokens we can try this out having context of everything.\n", "\n", "### Arbitrary initial structure to generate the Content?\n", "\n", "Another idea is to have the possibility of generating the content from another more flexible structure not composed of bloque, tema epígrafe but let the ai create the structure with more flexibility. This is out of scope but interesting"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We need a list of generate contentplan input to generate content plans. "]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/langchain_core/utils/utils.py:235: UserWarning: WARNING! extra_headers is not default parameter.\n", "                extra_headers was transferred to model_kwargs.\n", "                Please confirm that extra_headers is what you intended.\n", "  warnings.warn(\n", "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/langchain_core/utils/utils.py:235: UserWarning: WARNING! extra_headers is not default parameter.\n", "                extra_headers was transferred to model_kwargs.\n", "                Please confirm that extra_headers is what you intended.\n", "  warnings.warn(\n"]}], "source": ["content_generator = DependencyContainer.get_content_generator()"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["15:48:08.320 GET /api/v1/topics/3\n"]}], "source": ["topics_json = requests.get(url = base_url + '/api/v1/topics/' + str(save_index_response.indice_id), headers = headers)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["#Getting epigrafes topic 1.\n", "tema_id = topics_json.json()[0]['id']"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We get the epigrafes of Topic 1."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Try with the task"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["from src.background_tasks.content_generation.content_generation_task import (\n", "    ContentQueueItem,\n", ")"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/langchain_core/utils/utils.py:235: UserWarning: WARNING! extra_headers is not default parameter.\n", "                extra_headers was transferred to model_kwargs.\n", "                Please confirm that extra_headers is what you intended.\n", "  warnings.warn(\n", "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/langchain_core/utils/utils.py:235: UserWarning: WARNING! extra_headers is not default parameter.\n", "                extra_headers was transferred to model_kwargs.\n", "                Please confirm that extra_headers is what you intended.\n", "  warnings.warn(\n"]}], "source": ["generate_content_task = DependencyContainer.get_generate_content_task()"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["#Mock item\n", "item = ContentQueueItem(save_index_response.indice_id, tema_id, None, None)"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"text/plain": ["[GenerateContentPlanInput(epigrafe='Fundamentos de espacios vectoriales y subespacios', asignatura='Matematicas para machine learning I - Matemática aplicada', tema='Espacios Vectoriales y Estructuras Algebraicas', bloque='FUNDAMENTOS DE ÁLGEBRA LINEAL', epigrafe_with_context=EpigrafeWithContext(nombre='Fundamentos de espacios vectoriales y subespacios', id=46, nombre_tema='Espacios Vectoriales y Estructuras Algebraicas', nombre_bloque='FUNDAMENTOS DE ÁLGEBRA LINEAL', nombre_asignatura='Matematicas para machine learning I - Matemática aplicada', position=1, id_tema=10, id_bloque=3, index=0, didactic_instructions='```markdown\\n## Introducción y Objetivos\\n\\nDesarrollar una introducción que:\\n- Explique la importancia fundamental de los espacios vectoriales como base matemática del machine learning\\n- Establezca conexiones con aplicaciones prácticas como representación de datos y transformaciones\\n- Liste objetivos específicos:\\n  1. Comprender el concepto de espacio vectorial y sus propiedades\\n  2. Identificar y verificar subespacios vectoriales\\n  3. Aplicar operaciones vectoriales básicas\\n\\n## Contenido Principal\\n- Definir formalmente espacio vectorial y axiomas\\n- Explicar operaciones vectoriales: suma y multiplicación por escalar\\n- Desarrollar el concepto de subespacio con ejemplos geométricos R² y R³\\n- Incluir ejemplos de espacios vectoriales no numéricos (ej: espacio de funciones continuas)\\n```', context=EpigrafeContext(epigrafes_anteriores=[], epigrafes_siguientes=['Bases, dimensiones y coordenadas', 'Productos internos y normas', 'Ortogonalidad y bases ortonormales', 'Aplicaciones en machine learning'], temas_anteriores=[], temas_siguientes=['Matrices y Transformaciones Lineales', 'Reducción Dimensional y Análisis Matricial', 'Cálculo Diferencial Multivariable', 'Técnicas de Optimización', 'Optimización Estocástica', 'Fundamentos de Probabilidad', 'Estadística para Machine Learning', 'Métodos Numéricos Avanzados'])), epigrafe_context=EpigrafeContext(epigrafes_anteriores=[], epigrafes_siguientes=['Bases, dimensiones y coordenadas', 'Productos internos y normas', 'Ortogonalidad y bases ortonormales', 'Aplicaciones en machine learning'], temas_anteriores=[], temas_siguientes=['Matrices y Transformaciones Lineales', 'Reducción Dimensional y Análisis Matricial', 'Cálculo Diferencial Multivariable', 'Técnicas de Optimización', 'Optimización Estocástica', 'Fundamentos de Probabilidad', 'Estadística para Machine Learning', 'Métodos Numéricos Avanzados']), content_plan_schema=None, instrucciones_didacticas='```markdown\\n## Introducción y Objetivos\\n\\nDesarrollar una introducción que:\\n- Explique la importancia fundamental de los espacios vectoriales como base matemática del machine learning\\n- Establezca conexiones con aplicaciones prácticas como representación de datos y transformaciones\\n- Liste objetivos específicos:\\n  1. Comprender el concepto de espacio vectorial y sus propiedades\\n  2. Identificar y verificar subespacios vectoriales\\n  3. Aplicar operaciones vectoriales básicas\\n\\n## Contenido Principal\\n- Definir formalmente espacio vectorial y axiomas\\n- Explicar operaciones vectoriales: suma y multiplicación por escalar\\n- Desarrollar el concepto de subespacio con ejemplos geométricos R² y R³\\n- Incluir ejemplos de espacios vectoriales no numéricos (ej: espacio de funciones continuas)\\n```', few_shot_content_plan_instructions=None, prompt_context=PromptContext(id_asignatura=None, user_input_id=None, id_tema=10, id_indice=3, id_titulo=None, id_epigrafe=46, parent_process_id=None, ai_process=None, plan_item_id=None, plan_id=None, tipo_herramienta=None)),\n", " GenerateContentPlanInput(epigrafe='Bases, dimensiones y coordenadas', asignatura='Matematicas para machine learning I - Matemática aplicada', tema='Espacios Vectoriales y Estructuras Algebraicas', bloque='FUNDAMENTOS DE ÁLGEBRA LINEAL', epigrafe_with_context=EpigrafeWithContext(nombre='Bases, dimensiones y coordenadas', id=47, nombre_tema='Espacios Vectoriales y Estructuras Algebraicas', nombre_bloque='FUNDAMENTOS DE ÁLGEBRA LINEAL', nombre_asignatura='Matematicas para machine learning I - Matemática aplicada', position=2, id_tema=10, id_bloque=3, index=1, didactic_instructions='```markdown\\n## Desglose de Conceptos\\n- Definición formal de base vectorial\\n- Independencia lineal y generadores\\n- Dimensión como invariante fundamental\\n- Cambios de base y matrices de transformación\\n- Coordenadas en diferentes bases\\n\\n## Ejemplos Prácticos\\n1. Cálculo de base para R³ usando eliminación gaussiana\\n2. Transformación de coordenadas entre bases canónica y no canónica\\n3. Determinación de independencia lineal en conjuntos de vectores\\n\\n## Aplicaciones\\n- Representación eficiente de datos en machine learning\\n- Reducción de dimensionalidad básica\\n```', context=EpigrafeContext(epigrafes_anteriores=['Fundamentos de espacios vectoriales y subespacios'], epigrafes_siguientes=['Productos internos y normas', 'Ortogonalidad y bases ortonormales', 'Aplicaciones en machine learning'], temas_anteriores=[], temas_siguientes=['Matrices y Transformaciones Lineales', 'Reducción Dimensional y Análisis Matricial', 'Cálculo Diferencial Multivariable', 'Técnicas de Optimización', 'Optimización Estocástica', 'Fundamentos de Probabilidad', 'Estadística para Machine Learning', 'Métodos Numéricos Avanzados'])), epigrafe_context=EpigrafeContext(epigrafes_anteriores=['Fundamentos de espacios vectoriales y subespacios'], epigrafes_siguientes=['Productos internos y normas', 'Ortogonalidad y bases ortonormales', 'Aplicaciones en machine learning'], temas_anteriores=[], temas_siguientes=['Matrices y Transformaciones Lineales', 'Reducción Dimensional y Análisis Matricial', 'Cálculo Diferencial Multivariable', 'Técnicas de Optimización', 'Optimización Estocástica', 'Fundamentos de Probabilidad', 'Estadística para Machine Learning', 'Métodos Numéricos Avanzados']), content_plan_schema=None, instrucciones_didacticas='```markdown\\n## Desglose de Conceptos\\n- Definición formal de base vectorial\\n- Independencia lineal y generadores\\n- Dimensión como invariante fundamental\\n- Cambios de base y matrices de transformación\\n- Coordenadas en diferentes bases\\n\\n## Ejemplos Prácticos\\n1. Cálculo de base para R³ usando eliminación gaussiana\\n2. Transformación de coordenadas entre bases canónica y no canónica\\n3. Determinación de independencia lineal en conjuntos de vectores\\n\\n## Aplicaciones\\n- Representación eficiente de datos en machine learning\\n- Reducción de dimensionalidad básica\\n```', few_shot_content_plan_instructions=None, prompt_context=PromptContext(id_asignatura=None, user_input_id=None, id_tema=10, id_indice=3, id_titulo=None, id_epigrafe=47, parent_process_id=None, ai_process=None, plan_item_id=None, plan_id=None, tipo_herramienta=None)),\n", " GenerateContentPlanInput(epigrafe='Productos internos y normas', asignatura='Matematicas para machine learning I - Matemática aplicada', tema='Espacios Vectoriales y Estructuras Algebraicas', bloque='FUNDAMENTOS DE ÁLGEBRA LINEAL', epigrafe_with_context=EpigrafeWithContext(nombre='Productos internos y normas', id=48, nombre_tema='Espacios Vectoriales y Estructuras Algebraicas', nombre_bloque='FUNDAMENTOS DE ÁLGEBRA LINEAL', nombre_asignatura='Matematicas para machine learning I - Matemática aplicada', position=3, id_tema=10, id_bloque=3, index=2, didactic_instructions='```markdown\\n## Desglose de Conceptos\\n- Definición y propiedades del producto interno\\n- Tipos de normas vectoriales (L1, L2, infinito)\\n- Desigualdad de Cauchy-Schwarz\\n- Ángulos entre vectores\\n\\n## Ejemplos Prácticos\\n1. Cálculo de distancias usando diferentes normas\\n   - Comparación entre norma L1 y L2 en un conjunto de datos 2D\\n2. Aplicación de producto interno para medir similitud\\n   - Ejemplo con vectores de características en clasificación\\n\\n## Conexiones ML\\n- Métricas de distancia en algoritmos de clustering\\n- Regularización mediante normas\\n```', context=EpigrafeContext(epigrafes_anteriores=['Fundamentos de espacios vectoriales y subespacios', 'Bases, dimensiones y coordenadas'], epigrafes_siguientes=['Ortogonalidad y bases ortonormales', 'Aplicaciones en machine learning'], temas_anteriores=[], temas_siguientes=['Matrices y Transformaciones Lineales', 'Reducción Dimensional y Análisis Matricial', 'Cálculo Diferencial Multivariable', 'Técnicas de Optimización', 'Optimización Estocástica', 'Fundamentos de Probabilidad', 'Estadística para Machine Learning', 'Métodos Numéricos Avanzados'])), epigrafe_context=EpigrafeContext(epigrafes_anteriores=['Fundamentos de espacios vectoriales y subespacios', 'Bases, dimensiones y coordenadas'], epigrafes_siguientes=['Ortogonalidad y bases ortonormales', 'Aplicaciones en machine learning'], temas_anteriores=[], temas_siguientes=['Matrices y Transformaciones Lineales', 'Reducción Dimensional y Análisis Matricial', 'Cálculo Diferencial Multivariable', 'Técnicas de Optimización', 'Optimización Estocástica', 'Fundamentos de Probabilidad', 'Estadística para Machine Learning', 'Métodos Numéricos Avanzados']), content_plan_schema=None, instrucciones_didacticas='```markdown\\n## Desglose de Conceptos\\n- Definición y propiedades del producto interno\\n- Tipos de normas vectoriales (L1, L2, infinito)\\n- Desigualdad de Cauchy-Schwarz\\n- Ángulos entre vectores\\n\\n## Ejemplos Prácticos\\n1. Cálculo de distancias usando diferentes normas\\n   - Comparación entre norma L1 y L2 en un conjunto de datos 2D\\n2. Aplicación de producto interno para medir similitud\\n   - Ejemplo con vectores de características en clasificación\\n\\n## Conexiones ML\\n- Métricas de distancia en algoritmos de clustering\\n- Regularización mediante normas\\n```', few_shot_content_plan_instructions=None, prompt_context=PromptContext(id_asignatura=None, user_input_id=None, id_tema=10, id_indice=3, id_titulo=None, id_epigrafe=48, parent_process_id=None, ai_process=None, plan_item_id=None, plan_id=None, tipo_herramienta=None)),\n", " GenerateContentPlanInput(epigrafe='Ortogonalidad y bases ortonormales', asignatura='Matematicas para machine learning I - Matemática aplicada', tema='Espacios Vectoriales y Estructuras Algebraicas', bloque='FUNDAMENTOS DE ÁLGEBRA LINEAL', epigrafe_with_context=EpigrafeWithContext(nombre='Ortogonalidad y bases ortonormales', id=49, nombre_tema='Espacios Vectoriales y Estructuras Algebraicas', nombre_bloque='FUNDAMENTOS DE ÁLGEBRA LINEAL', nombre_asignatura='Matematicas para machine learning I - Matemática aplicada', position=4, id_tema=10, id_bloque=3, index=3, didactic_instructions='```markdown\\n## Desglose de Conceptos\\n- Vectores ortogonales y ortonormales\\n- Proceso de ortogonalización de Gram-Schmidt\\n- Complemento ortogonal y proyecciones\\n- Bases ortonormales especiales\\n\\n## Ejemplos Prácticos\\n1. Construcción de base ortonormal paso a paso\\n   - Aplicación del proceso Gram-Schmidt a vectores específicos\\n2. Cálculo de proyecciones ortogonales\\n   - Ejemplo de proyección en el contexto de regresión lineal\\n\\n## Visualizaciones\\n- Representaciones geométricas de ortogonalidad\\n- Descomposiciones ortogonales en 2D y 3D\\n```', context=EpigrafeContext(epigrafes_anteriores=['Fundamentos de espacios vectoriales y subespacios', 'Bases, dimensiones y coordenadas', 'Productos internos y normas'], epigrafes_siguientes=['Aplicaciones en machine learning'], temas_anteriores=[], temas_siguientes=['Matrices y Transformaciones Lineales', 'Reducción Dimensional y Análisis Matricial', 'Cálculo Diferencial Multivariable', 'Técnicas de Optimización', 'Optimización Estocástica', 'Fundamentos de Probabilidad', 'Estadística para Machine Learning', 'Métodos Numéricos Avanzados'])), epigrafe_context=EpigrafeContext(epigrafes_anteriores=['Fundamentos de espacios vectoriales y subespacios', 'Bases, dimensiones y coordenadas', 'Productos internos y normas'], epigrafes_siguientes=['Aplicaciones en machine learning'], temas_anteriores=[], temas_siguientes=['Matrices y Transformaciones Lineales', 'Reducción Dimensional y Análisis Matricial', 'Cálculo Diferencial Multivariable', 'Técnicas de Optimización', 'Optimización Estocástica', 'Fundamentos de Probabilidad', 'Estadística para Machine Learning', 'Métodos Numéricos Avanzados']), content_plan_schema=None, instrucciones_didacticas='```markdown\\n## Desglose de Conceptos\\n- Vectores ortogonales y ortonormales\\n- Proceso de ortogonalización de Gram-Schmidt\\n- Complemento ortogonal y proyecciones\\n- Bases ortonormales especiales\\n\\n## Ejemplos Prácticos\\n1. Construcción de base ortonormal paso a paso\\n   - Aplicación del proceso Gram-Schmidt a vectores específicos\\n2. Cálculo de proyecciones ortogonales\\n   - Ejemplo de proyección en el contexto de regresión lineal\\n\\n## Visualizaciones\\n- Representaciones geométricas de ortogonalidad\\n- Descomposiciones ortogonales en 2D y 3D\\n```', few_shot_content_plan_instructions=None, prompt_context=PromptContext(id_asignatura=None, user_input_id=None, id_tema=10, id_indice=3, id_titulo=None, id_epigrafe=49, parent_process_id=None, ai_process=None, plan_item_id=None, plan_id=None, tipo_herramienta=None)),\n", " GenerateContentPlanInput(epigrafe='Aplicaciones en machine learning', asignatura='Matematicas para machine learning I - Matemática aplicada', tema='Espacios Vectoriales y Estructuras Algebraicas', bloque='FUNDAMENTOS DE ÁLGEBRA LINEAL', epigrafe_with_context=EpigrafeWithContext(nombre='Aplicaciones en machine learning', id=50, nombre_tema='Espacios Vectoriales y Estructuras Algebraicas', nombre_bloque='FUNDAMENTOS DE ÁLGEBRA LINEAL', nombre_asignatura='Matematicas para machine learning I - Matemática aplicada', position=5, id_tema=10, id_bloque=3, index=4, didactic_instructions='```markdown\\n## Desglose de Aplicaciones\\n- Regresión lineal desde perspectiva geométrica\\n- PCA y bases ortogonales\\n- SVD y sus aplicaciones en ML\\n- Kernels y productos internos en SVM\\n\\n## Ejemplos Prácticos\\n1. Implementación básica de PCA\\n   - Ejemplo con dataset sintético 2D\\n2. Aplicación de proyecciones en regresión\\n   - Minimización de error cuadrático mediante proyecciones\\n\\n## Conexiones Futuras\\n- Enlace con próximos temas de matrices y transformaciones lineales\\n- Preparación para técnicas de reducción dimensional\\n```', context=EpigrafeContext(epigrafes_anteriores=['Fundamentos de espacios vectoriales y subespacios', 'Bases, dimensiones y coordenadas', 'Productos internos y normas', 'Ortogonalidad y bases ortonormales'], epigrafes_siguientes=[], temas_anteriores=[], temas_siguientes=['Matrices y Transformaciones Lineales', 'Reducción Dimensional y Análisis Matricial', 'Cálculo Diferencial Multivariable', 'Técnicas de Optimización', 'Optimización Estocástica', 'Fundamentos de Probabilidad', 'Estadística para Machine Learning', 'Métodos Numéricos Avanzados'])), epigrafe_context=EpigrafeContext(epigrafes_anteriores=['Fundamentos de espacios vectoriales y subespacios', 'Bases, dimensiones y coordenadas', 'Productos internos y normas', 'Ortogonalidad y bases ortonormales'], epigrafes_siguientes=[], temas_anteriores=[], temas_siguientes=['Matrices y Transformaciones Lineales', 'Reducción Dimensional y Análisis Matricial', 'Cálculo Diferencial Multivariable', 'Técnicas de Optimización', 'Optimización Estocástica', 'Fundamentos de Probabilidad', 'Estadística para Machine Learning', 'Métodos Numéricos Avanzados']), content_plan_schema=None, instrucciones_didacticas='```markdown\\n## Desglose de Aplicaciones\\n- Regresión lineal desde perspectiva geométrica\\n- PCA y bases ortogonales\\n- SVD y sus aplicaciones en ML\\n- Kernels y productos internos en SVM\\n\\n## Ejemplos Prácticos\\n1. Implementación básica de PCA\\n   - Ejemplo con dataset sintético 2D\\n2. Aplicación de proyecciones en regresión\\n   - Minimización de error cuadrático mediante proyecciones\\n\\n## Conexiones Futuras\\n- Enlace con próximos temas de matrices y transformaciones lineales\\n- Preparación para técnicas de reducción dimensional\\n```', few_shot_content_plan_instructions=None, prompt_context=PromptContext(id_asignatura=None, user_input_id=None, id_tema=10, id_indice=3, id_titulo=None, id_epigrafe=50, parent_process_id=None, ai_process=None, plan_item_id=None, plan_id=None, tipo_herramienta=None))]"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["generate_plan_input = generate_content_task._format_content_plan_input(item)\n", "#This is a list of epigraphs\n", "generate_plan_input"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["15:48:08.503 content_plan\n", "15:48:08.545   Message with 'claude-3-5-sonnet-20241022' [LLM]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n"]}], "source": ["#Generating only one content plan\n", "with logfire.span('content_plan'):\n", "    content_plans = await generate_content_task._generate_content_plans([generate_plan_input[0]])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Store now the plan and get the version with saved ids. "]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["content_plans = generate_content_task._store_and_get_content_plans(content_plans, item.indice_id, item.tema_id)"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/plain": ["[Plan(id=1, plan='Motivación e importancia de los espacios vectoriales', descripcion='Redactar una introducción que explique por qué los espacios vectoriales son fundamentales en matemáticas y computación. Incluir ejemplos intuitivos como vectores en el plano y representaciones de datos. Enfatizar cómo las propiedades de los espacios vectoriales permiten operaciones consistentes y predecibles. Longitud aproximada: 300-400 palabras. Mantener un tono accesible pero riguroso.', herramienta=<ContentTypes.TEXTO: 'TEXTO'>, version=1),\n", " Plan(id=2, plan='Definición formal de espacio vectorial', descripcion='Presentar la definición matemática formal de espacio vectorial sobre un campo (típicamente ℝ). Incluir la notación matemática precisa y explicar cada componente de la definición. Mencionar los dos tipos de operaciones fundamentales (suma vectorial y multiplicación por escalar) y su notación. Longitud: 250-300 palabras.', herramienta=<ContentTypes.TEXTO: 'TEXTO'>, version=1),\n", " Plan(id=3, plan='Axiomas de espacios vectoriales', descripcion='Crear una tabla detallada con los 8 axiomas de espacios vectoriales. Columnas: Nombre del axioma, Expresión matemática, Explicación en lenguaje natural, Ejemplo simple. Incluir tanto axiomas de suma vectorial como de multiplicación por escalar. Añadir notas sobre la importancia de cada axioma.', herramienta=<ContentTypes.TABLE: 'TABLA'>, version=1),\n", " Plan(id=4, plan='Visualización geométrica de operaciones vectoriales', descripcion='Crear un diagrama que ilustre geométricamente: 1) Suma de vectores usando la regla del paralelogramo, 2) Multiplicación por escalar mostrando escalado y dirección. Usar vectores en R² para mayor claridad. Incluir leyendas explicativas para cada operación.', herramienta=<ContentTypes.GRAFICA: 'GRAFICA'>, version=1),\n", " Plan(id=5, plan='Ejemplos de espacios vectoriales', descripcion='Desarrollar una tabla comparativa de diferentes espacios vectoriales. Incluir: R², R³, Rⁿ, espacio de polinomios, espacio de funciones continuas, espacio de matrices. Para cada uno: definir elementos, operaciones específicas y un ejemplo concreto de cómo se cumplen los axiomas.', herramienta=<ContentTypes.TABLE: 'TABLA'>, version=1),\n", " Plan(id=6, plan='Definición y propiedades de subespacios vectoriales', descripcion='Presentar la definición formal de subespacio vectorial. Explicar las tres condiciones necesarias y suficientes: no vacío, cerrado bajo suma, cerrado bajo multiplicación por escalar. Incluir la demostración de por qué estas condiciones son suficientes. Longitud: 300-350 palabras.', herramienta=<ContentTypes.TEXTO: 'TEXTO'>, version=1),\n", " Plan(id=7, plan='Visualización de subespacios en R³', descripcion='Crear un diagrama que muestre ejemplos geométricos de subespacios en R³: punto origen, líneas que pasan por el origen, planos que pasan por el origen. Incluir anotaciones que expliquen por qué cada uno es un subespacio válido.', herramienta=<ContentTypes.GRAFICA: 'GRAFICA'>, version=1),\n", " Plan(id=8, plan='Verificación de subespacios', descripcion='Desarrollar una guía paso a paso para verificar si un conjunto es subespacio. Incluir: 1) Verificación del vector cero, 2) Prueba de cerradura bajo suma, 3) Prueba de cerradura bajo multiplicación escalar. Proporcionar un ejemplo completo de verificación. Longitud: 400-450 palabras.', herramienta=<ContentTypes.TEXTO: 'TEXTO'>, version=1),\n", " Plan(id=9, plan='Operaciones con subespacios', descripcion='Explicar las operaciones básicas con subespacios: intersección y suma de subespacios. Demostrar que la intersección siempre es un subespacio. Incluir ejemplos geométricos en R² o R³. No incluir span ni combinaciones lineales (tema posterior). Longitud: 300-350 palabras.', herramienta=<ContentTypes.TEXTO: 'TEXTO'>, version=1)]"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["content_plans[0].output"]}, {"cell_type": "markdown", "metadata": {}, "source": ["For generating one content plan for a single epigraph."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Search Sources"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Search from instructions/plans and create queries with the plans that have been agreed on. \n", "\n", "Identify specific things like case studies etc for certain epígrafes. Maybe store metadata in the docs of the specific stuff. For example if case study to be filtered or something like that to narrow down retrieval."]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/langchain_core/utils/utils.py:235: UserWarning: WARNING! extra_headers is not default parameter.\n", "                extra_headers was transferred to model_kwargs.\n", "                Please confirm that extra_headers is what you intended.\n", "  warnings.warn(\n", "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/langchain_core/utils/utils.py:235: UserWarning: WARNING! extra_headers is not default parameter.\n", "                extra_headers was transferred to model_kwargs.\n", "                Please confirm that extra_headers is what you intended.\n", "  warnings.warn(\n"]}], "source": ["search_references_workflow = DependencyContainer.get_search_references_workflow()"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["epigrafes = content_generator.get_epigrafes_with_context(save_index_response.indice_id, tema_id)\n"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"text/plain": ["'Fundamentos de espacios vectoriales y subespacios'"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["epigrafes[0].nombre"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Observando esto igual se pueden agrupar todas y hacer rerank al final si se gastara mucho del reranker. "]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["15:48:30.814 <PERSON><PERSON>mpletion with 'gpt-4o' [LLM]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://rerank-multilingual-v3.swedencentral.models.ai.azure.com/v1/rerank \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://rerank-multilingual-v3.swedencentral.models.ai.azure.com/v1/rerank \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://rerank-multilingual-v3.swedencentral.models.ai.azure.com/v1/rerank \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://rerank-multilingual-v3.swedencentral.models.ai.azure.com/v1/rerank \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://rerank-multilingual-v3.swedencentral.models.ai.azure.com/v1/rerank \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://rerank-multilingual-v3.swedencentral.models.ai.azure.com/v1/rerank \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://rerank-multilingual-v3.swedencentral.models.ai.azure.com/v1/rerank \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://rerank-multilingual-v3.swedencentral.models.ai.azure.com/v1/rerank \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://rerank-multilingual-v3.swedencentral.models.ai.azure.com/v1/rerank \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://rerank-multilingual-v3.swedencentral.models.ai.azure.com/v1/rerank \"HTTP/1.1 200 OK\"\n"]}], "source": ["#This way we can search for a list of epigraphs\n", "references_epigrafe = await search_references_workflow._search_references([epigrafes[0]])"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"data": {"text/plain": ["[SearchResult(href='https://pdfs.semanticscholar.org/87f3/aa1386f2ac23f290aa7ac0d5967563c4f7fc.pdf', title='The Dual of Generalized Fuzzy Subspaces', body='We cannot provide a description for this page right now', source='BRAVE_SEARCH'),\n", " SearchResult(href='https://pdfs.semanticscholar.org/d770/5adf01fc9791337ed17dd37236129ef3a0f4.pdf', title='Linear Algebraic Structure of Word Senses, with ...', body='We cannot provide a description for this page right now', source='BRAVE_SEARCH'),\n", " SearchResult(href='https://pdfs.semanticscholar.org/0b65/6424392b8c63d834e62c28918a4bcf0374f7.pdf', title='Fundamentals Of Mathematical Statistics - Semantic Scholar', body='We cannot provide a description for this page right now', source='BRAVE_SEARCH'),\n", " SearchResult(href='https://www.semanticscholar.org/paper/Part-I-Machine-learning-and-kernel-vector-spaces/ed61a38a9ec6051a6d79f59de8388f1e550b600a', title='Part I Machine learning and kernel vector spaces | Semantic Scholar', body='Create an AI-powered research feed to stay up to date with new papers like this posted to ArXiv', source='BRAVE_SEARCH'),\n", " SearchResult(href='https://pdfs.semanticscholar.org/5ee9/3e2b235874e41a31f0c4f556bae43969af53.pdf', title='Vector Geometric Algebra in Power Systems - Semantic Scholar', body='We cannot provide a description for this page right now', source='BRAVE_SEARCH'),\n", " SearchResult(href='https://pdfs.semanticscholar.org/7930/f09c283baffdc3978ba69a8c7a06819a4a84.pdf', title='Algebraic model structures', body='We cannot provide a description for this page right now', source='BRAVE_SEARCH'),\n", " SearchResult(href='https://pdfs.semanticscholar.org/6b98/f778b96af9de3b7c0ce34dcb5b84b5bb52de.pdf/1000', title='Semanticscholar', body='We cannot provide a description for this page right now', source='BRAVE_SEARCH'),\n", " SearchResult(href='https://pdfs.semanticscholar.org/0798/9d39e2804c530c20070ba796151d89a373aa.pdf', title='Nonlinear maps preserving certain subspaces', body='We cannot provide a description for this page right now', source='BRAVE_SEARCH')]"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["references_epigrafe"]}, {"cell_type": "markdown", "metadata": {}, "source": ["No easy way to replicate the linking of sources without using the queue. So this may have to be tested independently for now."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Generate Content"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"data": {"text/plain": ["[ContentPlanLinked(output=[Plan(id=1, plan='Motivación e importancia de los espacios vectoriales', descripcion='Redactar una introducción que explique por qué los espacios vectoriales son fundamentales en matemáticas y computación. Incluir ejemplos intuitivos como vectores en el plano y representaciones de datos. Enfatizar cómo las propiedades de los espacios vectoriales permiten operaciones consistentes y predecibles. Longitud aproximada: 300-400 palabras. Mantener un tono accesible pero riguroso.', herramienta=<ContentTypes.TEXTO: 'TEXTO'>, version=1), Plan(id=2, plan='Definición formal de espacio vectorial', descripcion='Presentar la definición matemática formal de espacio vectorial sobre un campo (típicamente ℝ). Incluir la notación matemática precisa y explicar cada componente de la definición. Mencionar los dos tipos de operaciones fundamentales (suma vectorial y multiplicación por escalar) y su notación. Longitud: 250-300 palabras.', herramienta=<ContentTypes.TEXTO: 'TEXTO'>, version=1), Plan(id=3, plan='Axiomas de espacios vectoriales', descripcion='Crear una tabla detallada con los 8 axiomas de espacios vectoriales. Columnas: Nombre del axioma, Expresión matemática, Explicación en lenguaje natural, Ejemplo simple. Incluir tanto axiomas de suma vectorial como de multiplicación por escalar. Añadir notas sobre la importancia de cada axioma.', herramienta=<ContentTypes.TABLE: 'TABLA'>, version=1), Plan(id=4, plan='Visualización geométrica de operaciones vectoriales', descripcion='Crear un diagrama que ilustre geométricamente: 1) Suma de vectores usando la regla del paralelogramo, 2) Multiplicación por escalar mostrando escalado y dirección. Usar vectores en R² para mayor claridad. Incluir leyendas explicativas para cada operación.', herramienta=<ContentTypes.GRAFICA: 'GRAFICA'>, version=1), Plan(id=5, plan='Ejemplos de espacios vectoriales', descripcion='Desarrollar una tabla comparativa de diferentes espacios vectoriales. Incluir: R², R³, Rⁿ, espacio de polinomios, espacio de funciones continuas, espacio de matrices. Para cada uno: definir elementos, operaciones específicas y un ejemplo concreto de cómo se cumplen los axiomas.', herramienta=<ContentTypes.TABLE: 'TABLA'>, version=1), Plan(id=6, plan='Definición y propiedades de subespacios vectoriales', descripcion='Presentar la definición formal de subespacio vectorial. Explicar las tres condiciones necesarias y suficientes: no vacío, cerrado bajo suma, cerrado bajo multiplicación por escalar. Incluir la demostración de por qué estas condiciones son suficientes. Longitud: 300-350 palabras.', herramienta=<ContentTypes.TEXTO: 'TEXTO'>, version=1), Plan(id=7, plan='Visualización de subespacios en R³', descripcion='Crear un diagrama que muestre ejemplos geométricos de subespacios en R³: punto origen, líneas que pasan por el origen, planos que pasan por el origen. Incluir anotaciones que expliquen por qué cada uno es un subespacio válido.', herramienta=<ContentTypes.GRAFICA: 'GRAFICA'>, version=1), Plan(id=8, plan='Verificación de subespacios', descripcion='Desarrollar una guía paso a paso para verificar si un conjunto es subespacio. Incluir: 1) Verificación del vector cero, 2) Prueba de cerradura bajo suma, 3) Prueba de cerradura bajo multiplicación escalar. Proporcionar un ejemplo completo de verificación. Longitud: 400-450 palabras.', herramienta=<ContentTypes.TEXTO: 'TEXTO'>, version=1), Plan(id=9, plan='Operaciones con subespacios', descripcion='Explicar las operaciones básicas con subespacios: intersección y suma de subespacios. Demostrar que la intersección siempre es un subespacio. Incluir ejemplos geométricos en R² o R³. No incluir span ni combinaciones lineales (tema posterior). Longitud: 300-350 palabras.', herramienta=<ContentTypes.TEXTO: 'TEXTO'>, version=1)], id=41, epigrafe_id=46, context=EpigrafeWithContext(nombre='Fundamentos de espacios vectoriales y subespacios', id=46, nombre_tema='Espacios Vectoriales y Estructuras Algebraicas', nombre_bloque='FUNDAMENTOS DE ÁLGEBRA LINEAL', nombre_asignatura='Matematicas para machine learning I - Matemática aplicada', position=1, id_tema=10, id_bloque=3, index=0, didactic_instructions='```markdown\\n## Introducción y Objetivos\\n\\nDesarrollar una introducción que:\\n- Explique la importancia fundamental de los espacios vectoriales como base matemática del machine learning\\n- Establezca conexiones con aplicaciones prácticas como representación de datos y transformaciones\\n- Liste objetivos específicos:\\n  1. Comprender el concepto de espacio vectorial y sus propiedades\\n  2. Identificar y verificar subespacios vectoriales\\n  3. Aplicar operaciones vectoriales básicas\\n\\n## Contenido Principal\\n- Definir formalmente espacio vectorial y axiomas\\n- Explicar operaciones vectoriales: suma y multiplicación por escalar\\n- Desarrollar el concepto de subespacio con ejemplos geométricos R² y R³\\n- Incluir ejemplos de espacios vectoriales no numéricos (ej: espacio de funciones continuas)\\n```', context=EpigrafeContext(epigrafes_anteriores=[], epigrafes_siguientes=['Bases, dimensiones y coordenadas', 'Productos internos y normas', 'Ortogonalidad y bases ortonormales', 'Aplicaciones en machine learning'], temas_anteriores=[], temas_siguientes=['Matrices y Transformaciones Lineales', 'Reducción Dimensional y Análisis Matricial', 'Cálculo Diferencial Multivariable', 'Técnicas de Optimización', 'Optimización Estocástica', 'Fundamentos de Probabilidad', 'Estadística para Machine Learning', 'Métodos Numéricos Avanzados'])), version=1)]"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["content_plans"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["15:48:51.711 generate_content\n", "15:48:51.723   Embedding Creation with 'text-embedding-3-large' [LLM]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "2025-01-07 16:48:53,084 - logger.ContentGenerator - INFO - Input data inside generate content is: plan='Plan: Motivación e importancia de los espacios vectoriales Descripcion: Redactar una introducción que explique por qué los espacios vectoriales son fundamentales en matemáticas y computación. Incluir ejemplos intuitivos como vectores en el plano y representaciones de datos. Enfatizar cómo las propiedades de los espacios vectoriales permiten operaciones consistentes y predecibles. Longitud aproximada: 300-400 palabras. Mantener un tono accesible pero riguroso. Tipo herramienta: ContentTypes.TEXTO' bloque='FUNDAMENTOS DE ÁLGEBRA LINEAL' tema='Espacios Vectoriales y Estructuras Algebraicas' epigrafe='Fundamentos de espacios vectoriales y subespacios' epigrafe_context=\"epigrafes_anteriores=[] epigrafes_siguientes=['Bases, dimensiones y coordenadas', 'Productos internos y normas', 'Ortogonalidad y bases ortonormales', 'Aplicaciones en machine learning'] temas_anteriores=[] temas_siguientes=['Matrices y Transformaciones Lineales', 'Reducción Dimensional y Análisis Matricial', 'Cálculo Diferencial Multivariable', 'Técnicas de Optimización', 'Optimización Estocástica', 'Fundamentos de Probabilidad', 'Estadística para Machine Learning', 'Métodos Numéricos Avanzados']\" contenido_previo='[Vacío]' asignatura='Matematicas para machine learning I - Matemática aplicada' fuentes_verificadas='\\n' prompt_context=PromptContext(id_asignatura=None, user_input_id=None, id_tema=10, id_indice=3, id_titulo=None, id_epigrafe=46, parent_process_id=None, ai_process=None, plan_item_id=1, plan_id=41, tipo_herramienta='TEXTO') docs_info=[{}, defaultdict(<class 'list'>, {})]\n", "2025-01-07 16:48:53,088 - logger - WARNING - Few shot examples not found\n"]}, {"name": "stdout", "output_type": "stream", "text": ["15:48:53.095   Message with 'claude-3-5-sonnet-20241022' [LLM]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "2025-01-07 16:49:04,811 - logger.ContentGenerator - INFO - Fuentes usadas are: []\n", "2025-01-07 16:49:04,812 - logger.ContentGenerator - INFO - Len docs is0 and the list range would be []\n", "2025-01-07 16:49:04,812 - logger.ContentGenerator - INFO - Valid doc sources have been found\n", "2025-01-07 16:49:04,813 - logger.ContentGenerator - INFO - Used docs are []\n"]}, {"name": "stdout", "output_type": "stream", "text": ["15:49:04.826   Embedding Creation with 'text-embedding-3-large' [LLM]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "2025-01-07 16:49:05,650 - logger.ContentGenerator - INFO - Input data inside generate content is: plan='Plan: Definición formal de espacio vectorial Descripcion: Presentar la definición matemática formal de espacio vectorial sobre un campo (típicamente ℝ). Incluir la notación matemática precisa y explicar cada componente de la definición. Mencionar los dos tipos de operaciones fundamentales (suma vectorial y multiplicación por escalar) y su notación. Longitud: 250-300 palabras. Tipo herramienta: ContentTypes.TEXTO' bloque='FUNDAMENTOS DE ÁLGEBRA LINEAL' tema='Espacios Vectoriales y Estructuras Algebraicas' epigrafe='Fundamentos de espacios vectoriales y subespacios' epigrafe_context=\"epigrafes_anteriores=[] epigrafes_siguientes=['Bases, dimensiones y coordenadas', 'Productos internos y normas', 'Ortogonalidad y bases ortonormales', 'Aplicaciones en machine learning'] temas_anteriores=[] temas_siguientes=['Matrices y Transformaciones Lineales', 'Reducción Dimensional y Análisis Matricial', 'Cálculo Diferencial Multivariable', 'Técnicas de Optimización', 'Optimización Estocástica', 'Fundamentos de Probabilidad', 'Estadística para Machine Learning', 'Métodos Numéricos Avanzados']\" contenido_previo='\\n#### ¿Por qué son importantes los espacios vectoriales?\\n\\nLos espacios vectoriales son una de las estructuras matemáticas más fundamentales y versátiles en matemáticas modernas y ciencias de la computación. Para entender su importancia, imaginemos primero algo familiar: el plano cartesiano. Cuando dibujamos una flecha desde el origen hasta cualquier punto del plano, estamos trabajando con un vector bidimensional. Esta representación simple pero poderosa nos permite modelar desde velocidad y fuerza en física hasta precios y cantidades en economía.\\n\\nSin embargo, la verdadera potencia de los espacios vectoriales va mucho más allá del plano o el espacio tridimensional. En el contexto de machine learning y ciencia de datos, los espacios vectoriales nos permiten representar datos de alta dimensionalidad de manera natural. Por ejemplo, podemos representar una imagen como un vector donde cada componente corresponde a la intensidad de un píxel, o un documento de texto como un vector donde cada dimensión representa la frecuencia de una palabra.\\n\\n#### Propiedades que hacen especiales a los espacios vectoriales\\n\\nLo que hace particularmente útiles a los espacios vectoriales son sus propiedades algebraicas bien definidas. Cuando trabajamos con vectores:\\n- Podemos sumarlos y el resultado sigue siendo un vector\\n- Podemos multiplicarlos por escalares manteniendo su estructura\\n- Estas operaciones siguen reglas consistentes y predecibles\\n\\nEstas propiedades aparentemente simples son fundamentales porque:\\n1. Garantizan que nuestras operaciones sean consistentes y bien comportadas\\n2. Permiten desarrollar algoritmos robustos y eficientes\\n3. Facilitan la generalización de conceptos geométricos a dimensiones arbitrarias\\n\\n#### Aplicaciones prácticas\\n\\nEn el contexto de machine learning, los espacios vectoriales son el fundamento para:\\n- Representación de características de datos\\n- Transformaciones lineales y no lineales\\n- Reducción de dimensionalidad\\n- Optimización de funciones\\n- Análisis de patrones y clustering\\n\\nEsta estructura matemática, aparentemente abstracta, es la que nos permite desarrollar algoritmos eficientes y robustos para resolver problemas complejos en el mundo real.\\n' asignatura='Matematicas para machine learning I - Matemática aplicada' fuentes_verificadas='\\n' prompt_context=PromptContext(id_asignatura=None, user_input_id=None, id_tema=10, id_indice=3, id_titulo=None, id_epigrafe=46, parent_process_id=None, ai_process=None, plan_item_id=2, plan_id=41, tipo_herramienta='TEXTO') docs_info=[{}, defaultdict(<class 'list'>, {})]\n", "2025-01-07 16:49:05,657 - logger - WARNING - Few shot examples not found\n"]}, {"name": "stdout", "output_type": "stream", "text": ["15:49:05.662   Message with 'claude-3-5-sonnet-20241022' [LLM]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "2025-01-07 16:49:17,528 - logger.ContentGenerator - INFO - Fuentes usadas are: []\n", "2025-01-07 16:49:17,529 - logger.ContentGenerator - INFO - Len docs is0 and the list range would be []\n", "2025-01-07 16:49:17,529 - logger.ContentGenerator - INFO - Valid doc sources have been found\n", "2025-01-07 16:49:17,530 - logger.ContentGenerator - INFO - Used docs are []\n"]}, {"name": "stdout", "output_type": "stream", "text": ["15:49:17.545   Embedding Creation with 'text-embedding-3-large' [LLM]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "2025-01-07 16:49:18,080 - logger.ContentGenerator - INFO - Input data inside generate content is: plan='Plan: Axiomas de espacios vectoriales Descripcion: Crear una tabla detallada con los 8 axiomas de espacios vectoriales. Columnas: Nombre del axioma, Expresión matemática, Explicación en lenguaje natural, Ejemplo simple. Incluir tanto axiomas de suma vectorial como de multiplicación por escalar. Añadir notas sobre la importancia de cada axioma. Tipo herramienta: ContentTypes.TABLE' bloque='FUNDAMENTOS DE ÁLGEBRA LINEAL' tema='Espacios Vectoriales y Estructuras Algebraicas' epigrafe='Fundamentos de espacios vectoriales y subespacios' epigrafe_context=\"epigrafes_anteriores=[] epigrafes_siguientes=['Bases, dimensiones y coordenadas', 'Productos internos y normas', 'Ortogonalidad y bases ortonormales', 'Aplicaciones en machine learning'] temas_anteriores=[] temas_siguientes=['Matrices y Transformaciones Lineales', 'Reducción Dimensional y Análisis Matricial', 'Cálculo Diferencial Multivariable', 'Técnicas de Optimización', 'Optimización Estocástica', 'Fundamentos de Probabilidad', 'Estadística para Machine Learning', 'Métodos Numéricos Avanzados']\" contenido_previo='\\n#### ¿Por qué son importantes los espacios vectoriales?\\n\\nLos espacios vectoriales son una de las estructuras matemáticas más fundamentales y versátiles en matemáticas modernas y ciencias de la computación. Para entender su importancia, imaginemos primero algo familiar: el plano cartesiano. Cuando dibujamos una flecha desde el origen hasta cualquier punto del plano, estamos trabajando con un vector bidimensional. Esta representación simple pero poderosa nos permite modelar desde velocidad y fuerza en física hasta precios y cantidades en economía.\\n\\nSin embargo, la verdadera potencia de los espacios vectoriales va mucho más allá del plano o el espacio tridimensional. En el contexto de machine learning y ciencia de datos, los espacios vectoriales nos permiten representar datos de alta dimensionalidad de manera natural. Por ejemplo, podemos representar una imagen como un vector donde cada componente corresponde a la intensidad de un píxel, o un documento de texto como un vector donde cada dimensión representa la frecuencia de una palabra.\\n\\n#### Propiedades que hacen especiales a los espacios vectoriales\\n\\nLo que hace particularmente útiles a los espacios vectoriales son sus propiedades algebraicas bien definidas. Cuando trabajamos con vectores:\\n- Podemos sumarlos y el resultado sigue siendo un vector\\n- Podemos multiplicarlos por escalares manteniendo su estructura\\n- Estas operaciones siguen reglas consistentes y predecibles\\n\\nEstas propiedades aparentemente simples son fundamentales porque:\\n1. Garantizan que nuestras operaciones sean consistentes y bien comportadas\\n2. Permiten desarrollar algoritmos robustos y eficientes\\n3. Facilitan la generalización de conceptos geométricos a dimensiones arbitrarias\\n\\n#### Aplicaciones prácticas\\n\\nEn el contexto de machine learning, los espacios vectoriales son el fundamento para:\\n- Representación de características de datos\\n- Transformaciones lineales y no lineales\\n- Reducción de dimensionalidad\\n- Optimización de funciones\\n- Análisis de patrones y clustering\\n\\nEsta estructura matemática, aparentemente abstracta, es la que nos permite desarrollar algoritmos eficientes y robustos para resolver problemas complejos en el mundo real.\\n \\n#### Definición formal de espacio vectorial\\n\\nUn espacio vectorial sobre un campo $\\\\mathbb{F}$ (típicamente $\\\\mathbb{R}$) es una estructura algebraica que consiste en un conjunto $V$ junto con dos operaciones:\\n\\n1. **Suma vectorial**: $+: V \\\\times V \\\\rightarrow V$\\n2. **Multiplicación por escalar**: $\\\\cdot: \\\\mathbb{F} \\\\times V \\\\rightarrow V$\\n\\nPara que $(V,+,\\\\cdot)$ sea un espacio vectorial, estas operaciones deben satisfacer los siguientes axiomas. Para todo $\\\\mathbf{u}, \\\\mathbf{v}, \\\\mathbf{w} \\\\in V$ y $\\\\alpha, \\\\beta \\\\in \\\\mathbb{F}$:\\n\\n**Axiomas de la suma vectorial:**\\n1. Asociatividad: $(\\\\mathbf{u} + \\\\mathbf{v}) + \\\\mathbf{w} = \\\\mathbf{u} + (\\\\mathbf{v} + \\\\mathbf{w})$\\n2. Conmutatividad: $\\\\mathbf{u} + \\\\mathbf{v} = \\\\mathbf{v} + \\\\mathbf{u}$\\n3. Elemento neutro: Existe $\\\\mathbf{0} \\\\in V$ tal que $\\\\mathbf{v} + \\\\mathbf{0} = \\\\mathbf{v}$\\n4. Elemento opuesto: Para cada $\\\\mathbf{v} \\\\in V$, existe $-\\\\mathbf{v} \\\\in V$ tal que $\\\\mathbf{v} + (-\\\\mathbf{v}) = \\\\mathbf{0}$\\n\\n**Axiomas de la multiplicación por escalar:**\\n5. Distributividad respecto a la suma vectorial: $\\\\alpha(\\\\mathbf{u} + \\\\mathbf{v}) = \\\\alpha\\\\mathbf{u} + \\\\alpha\\\\mathbf{v}$\\n6. Distributividad respecto a la suma escalar: $(\\\\alpha + \\\\beta)\\\\mathbf{v} = \\\\alpha\\\\mathbf{v} + \\\\beta\\\\mathbf{v}$\\n7. Asociatividad con escalares: $\\\\alpha(\\\\beta\\\\mathbf{v}) = (\\\\alpha\\\\beta)\\\\mathbf{v}$\\n8. Elemento neutro del campo: $1\\\\mathbf{v} = \\\\mathbf{v}$\\n\\nEsta estructura matemática proporciona el fundamento para el desarrollo de conceptos más avanzados como bases, dimensiones y transformaciones lineales, que serán cruciales en aplicaciones de machine learning.\\n' asignatura='Matematicas para machine learning I - Matemática aplicada' fuentes_verificadas='\\n' prompt_context=PromptContext(id_asignatura=None, user_input_id=None, id_tema=10, id_indice=3, id_titulo=None, id_epigrafe=46, parent_process_id=None, ai_process=None, plan_item_id=3, plan_id=41, tipo_herramienta='TABLA') docs_info=[{}, defaultdict(<class 'list'>, {})]\n", "2025-01-07 16:49:18,088 - logger - WARNING - Few shot examples not found\n"]}, {"name": "stdout", "output_type": "stream", "text": ["15:49:18.098   Message with 'claude-3-5-sonnet-20241022' [LLM]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "2025-01-07 16:49:34,843 - logger.ContentGenerator - INFO - Fuentes usadas are: []\n", "2025-01-07 16:49:34,844 - logger.ContentGenerator - INFO - Len docs is0 and the list range would be []\n", "2025-01-07 16:49:34,845 - logger.ContentGenerator - INFO - Valid doc sources have been found\n", "2025-01-07 16:49:34,845 - logger.ContentGenerator - INFO - Used docs are []\n"]}, {"name": "stdout", "output_type": "stream", "text": ["15:49:34.860   Embedding Creation with 'text-embedding-3-large' [LLM]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "2025-01-07 16:49:35,627 - logger.ContentGenerator - INFO - Input data inside generate content is: plan='Plan: Visualización geométrica de operaciones vectoriales Descripcion: Crear un diagrama que ilustre geométricamente: 1) Suma de vectores usando la regla del paralelogramo, 2) Multiplicación por escalar mostrando escalado y dirección. Usar vectores en R² para mayor claridad. Incluir leyendas explicativas para cada operación. Tipo herramienta: ContentTypes.GRAFICA' bloque='FUNDAMENTOS DE ÁLGEBRA LINEAL' tema='Espacios Vectoriales y Estructuras Algebraicas' epigrafe='Fundamentos de espacios vectoriales y subespacios' epigrafe_context=\"epigrafes_anteriores=[] epigrafes_siguientes=['Bases, dimensiones y coordenadas', 'Productos internos y normas', 'Ortogonalidad y bases ortonormales', 'Aplicaciones en machine learning'] temas_anteriores=[] temas_siguientes=['Matrices y Transformaciones Lineales', 'Reducción Dimensional y Análisis Matricial', 'Cálculo Diferencial Multivariable', 'Técnicas de Optimización', 'Optimización Estocástica', 'Fundamentos de Probabilidad', 'Estadística para Machine Learning', 'Métodos Numéricos Avanzados']\" contenido_previo='\\n#### ¿Por qué son importantes los espacios vectoriales?\\n\\nLos espacios vectoriales son una de las estructuras matemáticas más fundamentales y versátiles en matemáticas modernas y ciencias de la computación. Para entender su importancia, imaginemos primero algo familiar: el plano cartesiano. Cuando dibujamos una flecha desde el origen hasta cualquier punto del plano, estamos trabajando con un vector bidimensional. Esta representación simple pero poderosa nos permite modelar desde velocidad y fuerza en física hasta precios y cantidades en economía.\\n\\nSin embargo, la verdadera potencia de los espacios vectoriales va mucho más allá del plano o el espacio tridimensional. En el contexto de machine learning y ciencia de datos, los espacios vectoriales nos permiten representar datos de alta dimensionalidad de manera natural. Por ejemplo, podemos representar una imagen como un vector donde cada componente corresponde a la intensidad de un píxel, o un documento de texto como un vector donde cada dimensión representa la frecuencia de una palabra.\\n\\n#### Propiedades que hacen especiales a los espacios vectoriales\\n\\nLo que hace particularmente útiles a los espacios vectoriales son sus propiedades algebraicas bien definidas. Cuando trabajamos con vectores:\\n- Podemos sumarlos y el resultado sigue siendo un vector\\n- Podemos multiplicarlos por escalares manteniendo su estructura\\n- Estas operaciones siguen reglas consistentes y predecibles\\n\\nEstas propiedades aparentemente simples son fundamentales porque:\\n1. Garantizan que nuestras operaciones sean consistentes y bien comportadas\\n2. Permiten desarrollar algoritmos robustos y eficientes\\n3. Facilitan la generalización de conceptos geométricos a dimensiones arbitrarias\\n\\n#### Aplicaciones prácticas\\n\\nEn el contexto de machine learning, los espacios vectoriales son el fundamento para:\\n- Representación de características de datos\\n- Transformaciones lineales y no lineales\\n- Reducción de dimensionalidad\\n- Optimización de funciones\\n- Análisis de patrones y clustering\\n\\nEsta estructura matemática, aparentemente abstracta, es la que nos permite desarrollar algoritmos eficientes y robustos para resolver problemas complejos en el mundo real.\\n \\n#### Definición formal de espacio vectorial\\n\\nUn espacio vectorial sobre un campo $\\\\mathbb{F}$ (típicamente $\\\\mathbb{R}$) es una estructura algebraica que consiste en un conjunto $V$ junto con dos operaciones:\\n\\n1. **Suma vectorial**: $+: V \\\\times V \\\\rightarrow V$\\n2. **Multiplicación por escalar**: $\\\\cdot: \\\\mathbb{F} \\\\times V \\\\rightarrow V$\\n\\nPara que $(V,+,\\\\cdot)$ sea un espacio vectorial, estas operaciones deben satisfacer los siguientes axiomas. Para todo $\\\\mathbf{u}, \\\\mathbf{v}, \\\\mathbf{w} \\\\in V$ y $\\\\alpha, \\\\beta \\\\in \\\\mathbb{F}$:\\n\\n**Axiomas de la suma vectorial:**\\n1. Asociatividad: $(\\\\mathbf{u} + \\\\mathbf{v}) + \\\\mathbf{w} = \\\\mathbf{u} + (\\\\mathbf{v} + \\\\mathbf{w})$\\n2. Conmutatividad: $\\\\mathbf{u} + \\\\mathbf{v} = \\\\mathbf{v} + \\\\mathbf{u}$\\n3. Elemento neutro: Existe $\\\\mathbf{0} \\\\in V$ tal que $\\\\mathbf{v} + \\\\mathbf{0} = \\\\mathbf{v}$\\n4. Elemento opuesto: Para cada $\\\\mathbf{v} \\\\in V$, existe $-\\\\mathbf{v} \\\\in V$ tal que $\\\\mathbf{v} + (-\\\\mathbf{v}) = \\\\mathbf{0}$\\n\\n**Axiomas de la multiplicación por escalar:**\\n5. Distributividad respecto a la suma vectorial: $\\\\alpha(\\\\mathbf{u} + \\\\mathbf{v}) = \\\\alpha\\\\mathbf{u} + \\\\alpha\\\\mathbf{v}$\\n6. Distributividad respecto a la suma escalar: $(\\\\alpha + \\\\beta)\\\\mathbf{v} = \\\\alpha\\\\mathbf{v} + \\\\beta\\\\mathbf{v}$\\n7. Asociatividad con escalares: $\\\\alpha(\\\\beta\\\\mathbf{v}) = (\\\\alpha\\\\beta)\\\\mathbf{v}$\\n8. Elemento neutro del campo: $1\\\\mathbf{v} = \\\\mathbf{v}$\\n\\nEsta estructura matemática proporciona el fundamento para el desarrollo de conceptos más avanzados como bases, dimensiones y transformaciones lineales, que serán cruciales en aplicaciones de machine learning.\\n \\n\\n\\n| **Axioma** | **Expresión matemática** | **Explicación** | **Ejemplo en R²** | **Importancia** |\\n|------------|-------------------------|-----------------|-------------------|-----------------|\\n| 1. Asociatividad de la suma | $(\\\\mathbf{u} + \\\\mathbf{v}) + \\\\mathbf{w} = \\\\mathbf{u} + (\\\\mathbf{v} + \\\\mathbf{w})$ | El orden en que sumamos tres o más vectores no afecta al resultado | $(1,2) + ((3,4) + (5,6)) = ((1,2) + (3,4)) + (5,6)$ | Permite manipular sumas de múltiples vectores sin preocuparnos por el orden de agrupación |\\n| 2. Conmutatividad de la suma | $\\\\mathbf{u} + \\\\mathbf{v} = \\\\mathbf{v} + \\\\mathbf{u}$ | El orden de los sumandos no afecta al resultado | $(1,2) + (3,4) = (3,4) + (1,2)$ | Facilita la manipulación algebraica y simplificación de expresiones vectoriales |\\n| 3. Existencia del elemento neutro | $\\\\mathbf{v} + \\\\mathbf{0} = \\\\mathbf{v}$ | Existe un vector nulo que al sumarse a cualquier vector no lo modifica | $(1,2) + (0,0) = (1,2)$ | Define un punto de referencia y permite la resta vectorial |\\n| 4. Existencia del elemento opuesto | $\\\\mathbf{v} + (-\\\\mathbf{v}) = \\\\mathbf{0}$ | Para cada vector existe otro que al sumarse dan el vector nulo | $(1,2) + (-1,-2) = (0,0)$ | Permite definir la resta de vectores y resolver ecuaciones vectoriales |\\n| 5. Distributividad escalar-vectorial | $\\\\alpha(\\\\mathbf{u} + \\\\mathbf{v}) = \\\\alpha\\\\mathbf{u} + \\\\alpha\\\\mathbf{v}$ | La multiplicación por escalar se distribuye sobre la suma de vectores | $2((1,2) + (3,4)) = 2(1,2) + 2(3,4)$ | Fundamental para operaciones lineales y transformaciones |\\n| 6. Distributividad escalar | $(\\\\alpha + \\\\beta)\\\\mathbf{v} = \\\\alpha\\\\mathbf{v} + \\\\beta\\\\mathbf{v}$ | La suma de escalares se distribuye sobre la multiplicación vectorial | $(2+3)(1,2) = 2(1,2) + 3(1,2)$ | Permite factorizar y simplificar expresiones vectoriales |\\n| 7. Asociatividad escalar | $\\\\alpha(\\\\beta\\\\mathbf{v}) = (\\\\alpha\\\\beta)\\\\mathbf{v}$ | El orden de multiplicación de escalares no afecta al resultado | $2(3(1,2)) = (2·3)(1,2)$ | Garantiza consistencia en operaciones con múltiples escalares |\\n| 8. Elemento neutro escalar | $1\\\\mathbf{v} = \\\\mathbf{v}$ | La multiplicación por 1 no modifica el vector | $1(1,2) = (1,2)$ | Define la unidad de escala y completa la estructura algebraica |\\n\\n*Tabla 1. Axiomas de espacios vectoriales con ejemplos y su importancia*\\n\\n#### Nota sobre los axiomas\\n\\nEstos ocho axiomas son fundamentales y necesarios para definir un espacio vectorial. Cada uno juega un papel específico en garantizar que las operaciones vectoriales sean bien comportadas y predecibles, lo cual es esencial para desarrollar teoría más avanzada y aplicaciones prácticas en machine learning. La comprensión profunda de estos axiomas es crucial para el estudio posterior de bases, transformaciones lineales y técnicas de optimización.\\n\\n' asignatura='Matematicas para machine learning I - Matemática aplicada' fuentes_verificadas='\\n' prompt_context=PromptContext(id_asignatura=None, user_input_id=None, id_tema=10, id_indice=3, id_titulo=None, id_epigrafe=46, parent_process_id=None, ai_process=None, plan_item_id=4, plan_id=41, tipo_herramienta='GRAFICA') docs_info=[{}, defaultdict(<class 'list'>, {})]\n", "2025-01-07 16:49:35,632 - logger - WARNING - Few shot examples not found\n"]}, {"name": "stdout", "output_type": "stream", "text": ["15:49:35.639   Message with 'claude-3-5-sonnet-20241022' [LLM]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "2025-01-07 16:49:51,414 - logger.ContentGenerator - INFO - Fuentes usadas are: []\n", "2025-01-07 16:49:51,415 - logger.ContentGenerator - INFO - Len docs is0 and the list range would be []\n", "2025-01-07 16:49:51,415 - logger.ContentGenerator - INFO - Valid doc sources have been found\n", "2025-01-07 16:49:51,415 - logger.ContentGenerator - INFO - Used docs are []\n"]}, {"name": "stdout", "output_type": "stream", "text": ["15:49:51.428   Embedding Creation with 'text-embedding-3-large' [LLM]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "2025-01-07 16:49:52,315 - logger.ContentGenerator - INFO - Input data inside generate content is: plan='Plan: Ejemplos de espacios vectoriales Descripcion: Desarrollar una tabla comparativa de diferentes espacios vectoriales. Incluir: R², R³, Rⁿ, espacio de polinomios, espacio de funciones continuas, espacio de matrices. Para cada uno: definir elementos, operaciones específicas y un ejemplo concreto de cómo se cumplen los axiomas. Tipo herramienta: ContentTypes.TABLE' bloque='FUNDAMENTOS DE ÁLGEBRA LINEAL' tema='Espacios Vectoriales y Estructuras Algebraicas' epigrafe='Fundamentos de espacios vectoriales y subespacios' epigrafe_context=\"epigrafes_anteriores=[] epigrafes_siguientes=['Bases, dimensiones y coordenadas', 'Productos internos y normas', 'Ortogonalidad y bases ortonormales', 'Aplicaciones en machine learning'] temas_anteriores=[] temas_siguientes=['Matrices y Transformaciones Lineales', 'Reducción Dimensional y Análisis Matricial', 'Cálculo Diferencial Multivariable', 'Técnicas de Optimización', 'Optimización Estocástica', 'Fundamentos de Probabilidad', 'Estadística para Machine Learning', 'Métodos Numéricos Avanzados']\" contenido_previo='\\n#### Definición formal de espacio vectorial\\n\\nUn espacio vectorial sobre un campo $\\\\mathbb{F}$ (típicamente $\\\\mathbb{R}$) es una estructura algebraica que consiste en un conjunto $V$ junto con dos operaciones:\\n\\n1. **Suma vectorial**: $+: V \\\\times V \\\\rightarrow V$\\n2. **Multiplicación por escalar**: $\\\\cdot: \\\\mathbb{F} \\\\times V \\\\rightarrow V$\\n\\nPara que $(V,+,\\\\cdot)$ sea un espacio vectorial, estas operaciones deben satisfacer los siguientes axiomas. Para todo $\\\\mathbf{u}, \\\\mathbf{v}, \\\\mathbf{w} \\\\in V$ y $\\\\alpha, \\\\beta \\\\in \\\\mathbb{F}$:\\n\\n**Axiomas de la suma vectorial:**\\n1. Asociatividad: $(\\\\mathbf{u} + \\\\mathbf{v}) + \\\\mathbf{w} = \\\\mathbf{u} + (\\\\mathbf{v} + \\\\mathbf{w})$\\n2. Conmutatividad: $\\\\mathbf{u} + \\\\mathbf{v} = \\\\mathbf{v} + \\\\mathbf{u}$\\n3. Elemento neutro: Existe $\\\\mathbf{0} \\\\in V$ tal que $\\\\mathbf{v} + \\\\mathbf{0} = \\\\mathbf{v}$\\n4. Elemento opuesto: Para cada $\\\\mathbf{v} \\\\in V$, existe $-\\\\mathbf{v} \\\\in V$ tal que $\\\\mathbf{v} + (-\\\\mathbf{v}) = \\\\mathbf{0}$\\n\\n**Axiomas de la multiplicación por escalar:**\\n5. Distributividad respecto a la suma vectorial: $\\\\alpha(\\\\mathbf{u} + \\\\mathbf{v}) = \\\\alpha\\\\mathbf{u} + \\\\alpha\\\\mathbf{v}$\\n6. Distributividad respecto a la suma escalar: $(\\\\alpha + \\\\beta)\\\\mathbf{v} = \\\\alpha\\\\mathbf{v} + \\\\beta\\\\mathbf{v}$\\n7. Asociatividad con escalares: $\\\\alpha(\\\\beta\\\\mathbf{v}) = (\\\\alpha\\\\beta)\\\\mathbf{v}$\\n8. Elemento neutro del campo: $1\\\\mathbf{v} = \\\\mathbf{v}$\\n\\nEsta estructura matemática proporciona el fundamento para el desarrollo de conceptos más avanzados como bases, dimensiones y transformaciones lineales, que serán cruciales en aplicaciones de machine learning.\\n \\n\\n\\n| **Axioma** | **Expresión matemática** | **Explicación** | **Ejemplo en R²** | **Importancia** |\\n|------------|-------------------------|-----------------|-------------------|-----------------|\\n| 1. Asociatividad de la suma | $(\\\\mathbf{u} + \\\\mathbf{v}) + \\\\mathbf{w} = \\\\mathbf{u} + (\\\\mathbf{v} + \\\\mathbf{w})$ | El orden en que sumamos tres o más vectores no afecta al resultado | $(1,2) + ((3,4) + (5,6)) = ((1,2) + (3,4)) + (5,6)$ | Permite manipular sumas de múltiples vectores sin preocuparnos por el orden de agrupación |\\n| 2. Conmutatividad de la suma | $\\\\mathbf{u} + \\\\mathbf{v} = \\\\mathbf{v} + \\\\mathbf{u}$ | El orden de los sumandos no afecta al resultado | $(1,2) + (3,4) = (3,4) + (1,2)$ | Facilita la manipulación algebraica y simplificación de expresiones vectoriales |\\n| 3. Existencia del elemento neutro | $\\\\mathbf{v} + \\\\mathbf{0} = \\\\mathbf{v}$ | Existe un vector nulo que al sumarse a cualquier vector no lo modifica | $(1,2) + (0,0) = (1,2)$ | Define un punto de referencia y permite la resta vectorial |\\n| 4. Existencia del elemento opuesto | $\\\\mathbf{v} + (-\\\\mathbf{v}) = \\\\mathbf{0}$ | Para cada vector existe otro que al sumarse dan el vector nulo | $(1,2) + (-1,-2) = (0,0)$ | Permite definir la resta de vectores y resolver ecuaciones vectoriales |\\n| 5. Distributividad escalar-vectorial | $\\\\alpha(\\\\mathbf{u} + \\\\mathbf{v}) = \\\\alpha\\\\mathbf{u} + \\\\alpha\\\\mathbf{v}$ | La multiplicación por escalar se distribuye sobre la suma de vectores | $2((1,2) + (3,4)) = 2(1,2) + 2(3,4)$ | Fundamental para operaciones lineales y transformaciones |\\n| 6. Distributividad escalar | $(\\\\alpha + \\\\beta)\\\\mathbf{v} = \\\\alpha\\\\mathbf{v} + \\\\beta\\\\mathbf{v}$ | La suma de escalares se distribuye sobre la multiplicación vectorial | $(2+3)(1,2) = 2(1,2) + 3(1,2)$ | Permite factorizar y simplificar expresiones vectoriales |\\n| 7. Asociatividad escalar | $\\\\alpha(\\\\beta\\\\mathbf{v}) = (\\\\alpha\\\\beta)\\\\mathbf{v}$ | El orden de multiplicación de escalares no afecta al resultado | $2(3(1,2)) = (2·3)(1,2)$ | Garantiza consistencia en operaciones con múltiples escalares |\\n| 8. Elemento neutro escalar | $1\\\\mathbf{v} = \\\\mathbf{v}$ | La multiplicación por 1 no modifica el vector | $1(1,2) = (1,2)$ | Define la unidad de escala y completa la estructura algebraica |\\n\\n*Tabla 1. Axiomas de espacios vectoriales con ejemplos y su importancia*\\n\\n#### Nota sobre los axiomas\\n\\nEstos ocho axiomas son fundamentales y necesarios para definir un espacio vectorial. Cada uno juega un papel específico en garantizar que las operaciones vectoriales sean bien comportadas y predecibles, lo cual es esencial para desarrollar teoría más avanzada y aplicaciones prácticas en machine learning. La comprensión profunda de estos axiomas es crucial para el estudio posterior de bases, transformaciones lineales y técnicas de optimización.\\n\\n \\n\\n#### Visualización geométrica de operaciones vectoriales\\n\\nLa comprensión geométrica de las operaciones vectoriales es fundamental para desarrollar una intuición sobre cómo funcionan los espacios vectoriales. Veamos las dos operaciones fundamentales:\\n\\n#### Suma de vectores: Regla del paralelogramo\\n\\n![Figure](https://mermaid.ink/img/eyJjb2RlIjogIlxuZmxvd2NoYXJ0IExSXG4gICAgTygoTykpIC0tPiB8dnxBKChBKSlcbiAgICBPIC0tPiB8d3xCKChCKSlcbiAgICBBIC0tPiB8d3xDKChDKSlcbiAgICBCIC0tPiB8dnxDXG4gICAgc3R5bGUgTyBmaWxsOiNmOWYsc3Ryb2tlOiMzMzMsc3Ryb2tlLXdpZHRoOjJweFxuICAgIHN0eWxlIEEgZmlsbDojYmJmLHN0cm9rZTojMzMzLHN0cm9rZS13aWR0aDoycHhcbiAgICBzdHlsZSBCIGZpbGw6I2JiZixzdHJva2U6IzMzMyxzdHJva2Utd2lkdGg6MnB4XG4gICAgc3R5bGUgQyBmaWxsOiNiZmIsc3Ryb2tlOiMzMzMsc3Ryb2tlLXdpZHRoOjJweFxuIiwgIm1lcm1haWQiOiB7InRoZW1lIjogImRlZmF1bHQifX0)\\n\\n*Diagrama 1. Suma de vectores v + w usando la regla del paralelogramo. El vector resultante (en verde) es la diagonal del paralelogramo formado por los vectores v y w.*\\n\\nLa suma de vectores $\\\\mathbf{v} + \\\\mathbf{w}$ se puede visualizar geométricamente mediante la regla del paralelogramo:\\n1. Dibujamos los vectores $\\\\mathbf{v}$ y $\\\\mathbf{w}$ desde el mismo punto de origen O\\n2. Completamos el paralelogramo trasladando cada vector\\n3. La diagonal del paralelogramo desde el origen representa el vector suma $\\\\mathbf{v} + \\\\mathbf{w}$\\n\\n#### Multiplicación por escalar\\n\\n![Figure](https://mermaid.ink/img/eyJjb2RlIjogIlxuZmxvd2NoYXJ0IExSXG4gICAgTygoTykpIC0tPiB8dnxBKChBKSlcbiAgICBPIC0tPiB8MnZ8QigoQikpXG4gICAgTyAtLT4gfC0wLjV2fEMoKEMpKVxuICAgIHN0eWxlIE8gZmlsbDojZjlmLHN0cm9rZTojMzMzLHN0cm9rZS13aWR0aDoycHhcbiAgICBzdHlsZSBBIGZpbGw6I2JiZixzdHJva2U6IzMzMyxzdHJva2Utd2lkdGg6MnB4XG4gICAgc3R5bGUgQiBmaWxsOiNiZmIsc3Ryb2tlOiMzMzMsc3Ryb2tlLXdpZHRoOjJweFxuICAgIHN0eWxlIEMgZmlsbDojZmJiLHN0cm9rZTojMzMzLHN0cm9rZS13aWR0aDoycHhcbiIsICJtZXJtYWlkIjogeyJ0aGVtZSI6ICJkZWZhdWx0In19)\\n\\n*Diagrama 2. Multiplicación de un vector v por diferentes escalares: v (azul), 2v (verde) y -0.5v (rojo), mostrando el efecto en la magnitud y dirección.*\\n\\nLa multiplicación por escalar $\\\\alpha\\\\mathbf{v}$ tiene dos efectos sobre el vector:\\n1. **Magnitud**: El escalar $|\\\\alpha|$ determina cuánto se \"estira\" o \"encoge\" el vector\\n   - Si $|\\\\alpha| > 1$, el vector se alarga\\n   - Si $0 < |\\\\alpha| < 1$, el vector se acorta\\n   \\n2. **Dirección**: El signo de $\\\\alpha$ determina la dirección\\n   - Si $\\\\alpha > 0$, la dirección se mantiene\\n   - Si $\\\\alpha < 0$, la dirección se invierte\\n   - Si $\\\\alpha = 0$, el vector se convierte en el vector nulo\\n\\nEstas visualizaciones geométricas son fundamentales para desarrollar la intuición necesaria en conceptos más avanzados como transformaciones lineales y espacios vectoriales abstractos.\\n\\n' asignatura='Matematicas para machine learning I - Matemática aplicada' fuentes_verificadas='\\n' prompt_context=PromptContext(id_asignatura=None, user_input_id=None, id_tema=10, id_indice=3, id_titulo=None, id_epigrafe=46, parent_process_id=None, ai_process=None, plan_item_id=5, plan_id=41, tipo_herramienta='TABLA') docs_info=[{}, defaultdict(<class 'list'>, {})]\n", "2025-01-07 16:49:52,323 - logger - WARNING - Few shot examples not found\n"]}, {"name": "stdout", "output_type": "stream", "text": ["15:49:52.330   Message with 'claude-3-5-sonnet-20241022' [LLM]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "2025-01-07 16:50:10,818 - logger.ContentGenerator - INFO - Fuentes usadas are: []\n", "2025-01-07 16:50:10,818 - logger.ContentGenerator - INFO - Len docs is0 and the list range would be []\n", "2025-01-07 16:50:10,819 - logger.ContentGenerator - INFO - Valid doc sources have been found\n", "2025-01-07 16:50:10,819 - logger.ContentGenerator - INFO - Used docs are []\n"]}, {"name": "stdout", "output_type": "stream", "text": ["15:50:10.827   Embedding Creation with 'text-embedding-3-large' [LLM]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "2025-01-07 16:50:11,897 - logger.ContentGenerator - INFO - Input data inside generate content is: plan='Plan: Definición y propiedades de subespacios vectoriales Descripcion: Presentar la definición formal de subespacio vectorial. Explicar las tres condiciones necesarias y suficientes: no vacío, cerrado bajo suma, cerrado bajo multiplicación por escalar. Incluir la demostración de por qué estas condiciones son suficientes. Longitud: 300-350 palabras. Tipo herramienta: ContentTypes.TEXTO' bloque='FUNDAMENTOS DE ÁLGEBRA LINEAL' tema='Espacios Vectoriales y Estructuras Algebraicas' epigrafe='Fundamentos de espacios vectoriales y subespacios' epigrafe_context=\"epigrafes_anteriores=[] epigrafes_siguientes=['Bases, dimensiones y coordenadas', 'Productos internos y normas', 'Ortogonalidad y bases ortonormales', 'Aplicaciones en machine learning'] temas_anteriores=[] temas_siguientes=['Matrices y Transformaciones Lineales', 'Reducción Dimensional y Análisis Matricial', 'Cálculo Diferencial Multivariable', 'Técnicas de Optimización', 'Optimización Estocástica', 'Fundamentos de Probabilidad', 'Estadística para Machine Learning', 'Métodos Numéricos Avanzados']\" contenido_previo='\\n\\n\\n| **Axioma** | **Expresión matemática** | **Explicación** | **Ejemplo en R²** | **Importancia** |\\n|------------|-------------------------|-----------------|-------------------|-----------------|\\n| 1. Asociatividad de la suma | $(\\\\mathbf{u} + \\\\mathbf{v}) + \\\\mathbf{w} = \\\\mathbf{u} + (\\\\mathbf{v} + \\\\mathbf{w})$ | El orden en que sumamos tres o más vectores no afecta al resultado | $(1,2) + ((3,4) + (5,6)) = ((1,2) + (3,4)) + (5,6)$ | Permite manipular sumas de múltiples vectores sin preocuparnos por el orden de agrupación |\\n| 2. Conmutatividad de la suma | $\\\\mathbf{u} + \\\\mathbf{v} = \\\\mathbf{v} + \\\\mathbf{u}$ | El orden de los sumandos no afecta al resultado | $(1,2) + (3,4) = (3,4) + (1,2)$ | Facilita la manipulación algebraica y simplificación de expresiones vectoriales |\\n| 3. Existencia del elemento neutro | $\\\\mathbf{v} + \\\\mathbf{0} = \\\\mathbf{v}$ | Existe un vector nulo que al sumarse a cualquier vector no lo modifica | $(1,2) + (0,0) = (1,2)$ | Define un punto de referencia y permite la resta vectorial |\\n| 4. Existencia del elemento opuesto | $\\\\mathbf{v} + (-\\\\mathbf{v}) = \\\\mathbf{0}$ | Para cada vector existe otro que al sumarse dan el vector nulo | $(1,2) + (-1,-2) = (0,0)$ | Permite definir la resta de vectores y resolver ecuaciones vectoriales |\\n| 5. Distributividad escalar-vectorial | $\\\\alpha(\\\\mathbf{u} + \\\\mathbf{v}) = \\\\alpha\\\\mathbf{u} + \\\\alpha\\\\mathbf{v}$ | La multiplicación por escalar se distribuye sobre la suma de vectores | $2((1,2) + (3,4)) = 2(1,2) + 2(3,4)$ | Fundamental para operaciones lineales y transformaciones |\\n| 6. Distributividad escalar | $(\\\\alpha + \\\\beta)\\\\mathbf{v} = \\\\alpha\\\\mathbf{v} + \\\\beta\\\\mathbf{v}$ | La suma de escalares se distribuye sobre la multiplicación vectorial | $(2+3)(1,2) = 2(1,2) + 3(1,2)$ | Permite factorizar y simplificar expresiones vectoriales |\\n| 7. Asociatividad escalar | $\\\\alpha(\\\\beta\\\\mathbf{v}) = (\\\\alpha\\\\beta)\\\\mathbf{v}$ | El orden de multiplicación de escalares no afecta al resultado | $2(3(1,2)) = (2·3)(1,2)$ | Garantiza consistencia en operaciones con múltiples escalares |\\n| 8. Elemento neutro escalar | $1\\\\mathbf{v} = \\\\mathbf{v}$ | La multiplicación por 1 no modifica el vector | $1(1,2) = (1,2)$ | Define la unidad de escala y completa la estructura algebraica |\\n\\n*Tabla 1. Axiomas de espacios vectoriales con ejemplos y su importancia*\\n\\n#### Nota sobre los axiomas\\n\\nEstos ocho axiomas son fundamentales y necesarios para definir un espacio vectorial. Cada uno juega un papel específico en garantizar que las operaciones vectoriales sean bien comportadas y predecibles, lo cual es esencial para desarrollar teoría más avanzada y aplicaciones prácticas en machine learning. La comprensión profunda de estos axiomas es crucial para el estudio posterior de bases, transformaciones lineales y técnicas de optimización.\\n\\n \\n\\n#### Visualización geométrica de operaciones vectoriales\\n\\nLa comprensión geométrica de las operaciones vectoriales es fundamental para desarrollar una intuición sobre cómo funcionan los espacios vectoriales. Veamos las dos operaciones fundamentales:\\n\\n#### Suma de vectores: Regla del paralelogramo\\n\\n![Figure](https://mermaid.ink/img/eyJjb2RlIjogIlxuZmxvd2NoYXJ0IExSXG4gICAgTygoTykpIC0tPiB8dnxBKChBKSlcbiAgICBPIC0tPiB8d3xCKChCKSlcbiAgICBBIC0tPiB8d3xDKChDKSlcbiAgICBCIC0tPiB8dnxDXG4gICAgc3R5bGUgTyBmaWxsOiNmOWYsc3Ryb2tlOiMzMzMsc3Ryb2tlLXdpZHRoOjJweFxuICAgIHN0eWxlIEEgZmlsbDojYmJmLHN0cm9rZTojMzMzLHN0cm9rZS13aWR0aDoycHhcbiAgICBzdHlsZSBCIGZpbGw6I2JiZixzdHJva2U6IzMzMyxzdHJva2Utd2lkdGg6MnB4XG4gICAgc3R5bGUgQyBmaWxsOiNiZmIsc3Ryb2tlOiMzMzMsc3Ryb2tlLXdpZHRoOjJweFxuIiwgIm1lcm1haWQiOiB7InRoZW1lIjogImRlZmF1bHQifX0)\\n\\n*Diagrama 1. Suma de vectores v + w usando la regla del paralelogramo. El vector resultante (en verde) es la diagonal del paralelogramo formado por los vectores v y w.*\\n\\nLa suma de vectores $\\\\mathbf{v} + \\\\mathbf{w}$ se puede visualizar geométricamente mediante la regla del paralelogramo:\\n1. Dibujamos los vectores $\\\\mathbf{v}$ y $\\\\mathbf{w}$ desde el mismo punto de origen O\\n2. Completamos el paralelogramo trasladando cada vector\\n3. La diagonal del paralelogramo desde el origen representa el vector suma $\\\\mathbf{v} + \\\\mathbf{w}$\\n\\n#### Multiplicación por escalar\\n\\n![Figure](https://mermaid.ink/img/eyJjb2RlIjogIlxuZmxvd2NoYXJ0IExSXG4gICAgTygoTykpIC0tPiB8dnxBKChBKSlcbiAgICBPIC0tPiB8MnZ8QigoQikpXG4gICAgTyAtLT4gfC0wLjV2fEMoKEMpKVxuICAgIHN0eWxlIE8gZmlsbDojZjlmLHN0cm9rZTojMzMzLHN0cm9rZS13aWR0aDoycHhcbiAgICBzdHlsZSBBIGZpbGw6I2JiZixzdHJva2U6IzMzMyxzdHJva2Utd2lkdGg6MnB4XG4gICAgc3R5bGUgQiBmaWxsOiNiZmIsc3Ryb2tlOiMzMzMsc3Ryb2tlLXdpZHRoOjJweFxuICAgIHN0eWxlIEMgZmlsbDojZmJiLHN0cm9rZTojMzMzLHN0cm9rZS13aWR0aDoycHhcbiIsICJtZXJtYWlkIjogeyJ0aGVtZSI6ICJkZWZhdWx0In19)\\n\\n*Diagrama 2. Multiplicación de un vector v por diferentes escalares: v (azul), 2v (verde) y -0.5v (rojo), mostrando el efecto en la magnitud y dirección.*\\n\\nLa multiplicación por escalar $\\\\alpha\\\\mathbf{v}$ tiene dos efectos sobre el vector:\\n1. **Magnitud**: El escalar $|\\\\alpha|$ determina cuánto se \"estira\" o \"encoge\" el vector\\n   - Si $|\\\\alpha| > 1$, el vector se alarga\\n   - Si $0 < |\\\\alpha| < 1$, el vector se acorta\\n   \\n2. **Dirección**: El signo de $\\\\alpha$ determina la dirección\\n   - Si $\\\\alpha > 0$, la dirección se mantiene\\n   - Si $\\\\alpha < 0$, la dirección se invierte\\n   - Si $\\\\alpha = 0$, el vector se convierte en el vector nulo\\n\\nEstas visualizaciones geométricas son fundamentales para desarrollar la intuición necesaria en conceptos más avanzados como transformaciones lineales y espacios vectoriales abstractos.\\n\\n \\n#### Ejemplos de Espacios Vectoriales\\n\\nA continuación, presentamos una comparación detallada de los espacios vectoriales más comunes y relevantes para machine learning, mostrando sus características específicas y cómo se manifiestan las operaciones vectoriales en cada uno.\\n\\n\\n| **Espacio Vectorial** | **Elementos** | **Operaciones** | **Ejemplo de Vector** | **Ejemplo de Axiomas** |\\n|----------------------|---------------|-----------------|---------------------|----------------------|\\n| R² | Pares ordenados (x,y) | Suma: (x₁,y₁)+(x₂,y₂)=(x₁+x₂,y₁+y₂) <br> Escalar: α(x,y)=(αx,αy) | v=(2,3) | Suma: (2,3)+(1,1)=(3,4) <br> Escalar: 2(2,3)=(4,6) |\\n| R³ | Tripletas (x,y,z) | Suma: (x₁,y₁,z₁)+(x₂,y₂,z₂)=(x₁+x₂,y₁+y₂,z₁+z₂) <br> Escalar: α(x,y,z)=(αx,αy,αz) | v=(1,2,3) | Suma: (1,2,3)+(1,1,1)=(2,3,4) <br> Escalar: 3(1,2,3)=(3,6,9) |\\n| Rⁿ | n-tuplas (x₁,...,xₙ) | Suma: Componente a componente <br> Escalar: Multiplicación por cada componente | v=(1,2,...,n) | Suma: Componente a componente <br> Distributividad: α(v+w)=αv+αw |\\n| P_n[x] (Polinomios) | Polinomios de grado ≤n | Suma: Suma de coeficientes del mismo grado <br> Escalar: Multiplicación de todos los coeficientes | p(x)=x²+2x+1 | Suma: (x²+2x)+(x+1)=x²+3x+1 <br> Escalar: 2(x²+2x)=2x²+4x |\\n| C[a,b] (Funciones continuas) | Funciones continuas en [a,b] | Suma: (f+g)(x)=f(x)+g(x) <br> Escalar: (αf)(x)=αf(x) | f(x)=sin(x) | Suma: sin(x)+cos(x) <br> Escalar: 2sin(x) |\\n| M_m×n (Matrices) | Matrices de m×n | Suma: Suma elemento a elemento <br> Escalar: Multiplicación de cada elemento | A=[[1,2],[3,4]] | Suma: Elemento a elemento <br> Escalar: α[[1,2],[3,4]]=[[α,2α],[3α,4α]] |\\n\\n*Tabla 2. Comparación de espacios vectoriales comunes y sus propiedades fundamentales*\\n\\n#### Notas importantes sobre los espacios vectoriales presentados:\\n\\n1. **Dimensionalidad**: R², R³ y Rⁿ tienen dimensión finita igual a 2, 3 y n respectivamente. Son los espacios más intuitivos y frecuentemente utilizados en aplicaciones básicas de machine learning.\\n\\n2. **Espacios de polinomios**: P_n[x] es particularmente útil en aproximación de funciones y regresión polinomial. La dimensión de P_n[x] es n+1, correspondiente a los coeficientes del polinomio.\\n\\n3. **Espacios de funciones**: C[a,b] es un espacio de dimensión infinita, fundamental en análisis funcional y en la teoría de redes neuronales, donde las funciones de activación son elementos de este espacio.\\n\\n4. **Espacios matriciales**: M_m×n es esencial en machine learning para representar transformaciones lineales, datos tabulares y parámetros de modelos. Su dimensión es m×n.\\n\\nEstos espacios vectoriales proporcionan el fundamento matemático para muchas aplicaciones en machine learning, desde la representación de datos hasta la optimización de modelos.\\n\\n' asignatura='Matematicas para machine learning I - Matemática aplicada' fuentes_verificadas='\\n' prompt_context=PromptContext(id_asignatura=None, user_input_id=None, id_tema=10, id_indice=3, id_titulo=None, id_epigrafe=46, parent_process_id=None, ai_process=None, plan_item_id=6, plan_id=41, tipo_herramienta='TEXTO') docs_info=[{}, defaultdict(<class 'list'>, {})]\n", "2025-01-07 16:50:11,901 - logger - WARNING - Few shot examples not found\n"]}, {"name": "stdout", "output_type": "stream", "text": ["15:50:11.904   Message with 'claude-3-5-sonnet-20241022' [LLM]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "2025-01-07 16:50:23,721 - logger.ContentGenerator - INFO - Fuentes usadas are: []\n", "2025-01-07 16:50:23,722 - logger.ContentGenerator - INFO - Len docs is0 and the list range would be []\n", "2025-01-07 16:50:23,722 - logger.ContentGenerator - INFO - Valid doc sources have been found\n", "2025-01-07 16:50:23,723 - logger.ContentGenerator - INFO - Used docs are []\n"]}, {"name": "stdout", "output_type": "stream", "text": ["15:50:23.734   Embedding Creation with 'text-embedding-3-large' [LLM]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "2025-01-07 16:50:24,084 - logger.ContentGenerator - INFO - Input data inside generate content is: plan='Plan: Visualización de subespacios en R³ Descripcion: Crear un diagrama que muestre ejemplos geométricos de subespacios en R³: punto origen, líneas que pasan por el origen, planos que pasan por el origen. Incluir anotaciones que expliquen por qué cada uno es un subespacio válido. Tipo herramienta: ContentTypes.GRAFICA' bloque='FUNDAMENTOS DE ÁLGEBRA LINEAL' tema='Espacios Vectoriales y Estructuras Algebraicas' epigrafe='Fundamentos de espacios vectoriales y subespacios' epigrafe_context=\"epigrafes_anteriores=[] epigrafes_siguientes=['Bases, dimensiones y coordenadas', 'Productos internos y normas', 'Ortogonalidad y bases ortonormales', 'Aplicaciones en machine learning'] temas_anteriores=[] temas_siguientes=['Matrices y Transformaciones Lineales', 'Reducción Dimensional y Análisis Matricial', 'Cálculo Diferencial Multivariable', 'Técnicas de Optimización', 'Optimización Estocástica', 'Fundamentos de Probabilidad', 'Estadística para Machine Learning', 'Métodos Numéricos Avanzados']\" contenido_previo='\\n\\n#### Visualización geométrica de operaciones vectoriales\\n\\nLa comprensión geométrica de las operaciones vectoriales es fundamental para desarrollar una intuición sobre cómo funcionan los espacios vectoriales. Veamos las dos operaciones fundamentales:\\n\\n#### Suma de vectores: Regla del paralelogramo\\n\\n![Figure](https://mermaid.ink/img/eyJjb2RlIjogIlxuZmxvd2NoYXJ0IExSXG4gICAgTygoTykpIC0tPiB8dnxBKChBKSlcbiAgICBPIC0tPiB8d3xCKChCKSlcbiAgICBBIC0tPiB8d3xDKChDKSlcbiAgICBCIC0tPiB8dnxDXG4gICAgc3R5bGUgTyBmaWxsOiNmOWYsc3Ryb2tlOiMzMzMsc3Ryb2tlLXdpZHRoOjJweFxuICAgIHN0eWxlIEEgZmlsbDojYmJmLHN0cm9rZTojMzMzLHN0cm9rZS13aWR0aDoycHhcbiAgICBzdHlsZSBCIGZpbGw6I2JiZixzdHJva2U6IzMzMyxzdHJva2Utd2lkdGg6MnB4XG4gICAgc3R5bGUgQyBmaWxsOiNiZmIsc3Ryb2tlOiMzMzMsc3Ryb2tlLXdpZHRoOjJweFxuIiwgIm1lcm1haWQiOiB7InRoZW1lIjogImRlZmF1bHQifX0)\\n\\n*Diagrama 1. Suma de vectores v + w usando la regla del paralelogramo. El vector resultante (en verde) es la diagonal del paralelogramo formado por los vectores v y w.*\\n\\nLa suma de vectores $\\\\mathbf{v} + \\\\mathbf{w}$ se puede visualizar geométricamente mediante la regla del paralelogramo:\\n1. Dibujamos los vectores $\\\\mathbf{v}$ y $\\\\mathbf{w}$ desde el mismo punto de origen O\\n2. Completamos el paralelogramo trasladando cada vector\\n3. La diagonal del paralelogramo desde el origen representa el vector suma $\\\\mathbf{v} + \\\\mathbf{w}$\\n\\n#### Multiplicación por escalar\\n\\n![Figure](https://mermaid.ink/img/eyJjb2RlIjogIlxuZmxvd2NoYXJ0IExSXG4gICAgTygoTykpIC0tPiB8dnxBKChBKSlcbiAgICBPIC0tPiB8MnZ8QigoQikpXG4gICAgTyAtLT4gfC0wLjV2fEMoKEMpKVxuICAgIHN0eWxlIE8gZmlsbDojZjlmLHN0cm9rZTojMzMzLHN0cm9rZS13aWR0aDoycHhcbiAgICBzdHlsZSBBIGZpbGw6I2JiZixzdHJva2U6IzMzMyxzdHJva2Utd2lkdGg6MnB4XG4gICAgc3R5bGUgQiBmaWxsOiNiZmIsc3Ryb2tlOiMzMzMsc3Ryb2tlLXdpZHRoOjJweFxuICAgIHN0eWxlIEMgZmlsbDojZmJiLHN0cm9rZTojMzMzLHN0cm9rZS13aWR0aDoycHhcbiIsICJtZXJtYWlkIjogeyJ0aGVtZSI6ICJkZWZhdWx0In19)\\n\\n*Diagrama 2. Multiplicación de un vector v por diferentes escalares: v (azul), 2v (verde) y -0.5v (rojo), mostrando el efecto en la magnitud y dirección.*\\n\\nLa multiplicación por escalar $\\\\alpha\\\\mathbf{v}$ tiene dos efectos sobre el vector:\\n1. **Magnitud**: El escalar $|\\\\alpha|$ determina cuánto se \"estira\" o \"encoge\" el vector\\n   - Si $|\\\\alpha| > 1$, el vector se alarga\\n   - Si $0 < |\\\\alpha| < 1$, el vector se acorta\\n   \\n2. **Dirección**: El signo de $\\\\alpha$ determina la dirección\\n   - Si $\\\\alpha > 0$, la dirección se mantiene\\n   - Si $\\\\alpha < 0$, la dirección se invierte\\n   - Si $\\\\alpha = 0$, el vector se convierte en el vector nulo\\n\\nEstas visualizaciones geométricas son fundamentales para desarrollar la intuición necesaria en conceptos más avanzados como transformaciones lineales y espacios vectoriales abstractos.\\n\\n \\n#### Ejemplos de Espacios Vectoriales\\n\\nA continuación, presentamos una comparación detallada de los espacios vectoriales más comunes y relevantes para machine learning, mostrando sus características específicas y cómo se manifiestan las operaciones vectoriales en cada uno.\\n\\n\\n| **Espacio Vectorial** | **Elementos** | **Operaciones** | **Ejemplo de Vector** | **Ejemplo de Axiomas** |\\n|----------------------|---------------|-----------------|---------------------|----------------------|\\n| R² | Pares ordenados (x,y) | Suma: (x₁,y₁)+(x₂,y₂)=(x₁+x₂,y₁+y₂) <br> Escalar: α(x,y)=(αx,αy) | v=(2,3) | Suma: (2,3)+(1,1)=(3,4) <br> Escalar: 2(2,3)=(4,6) |\\n| R³ | Tripletas (x,y,z) | Suma: (x₁,y₁,z₁)+(x₂,y₂,z₂)=(x₁+x₂,y₁+y₂,z₁+z₂) <br> Escalar: α(x,y,z)=(αx,αy,αz) | v=(1,2,3) | Suma: (1,2,3)+(1,1,1)=(2,3,4) <br> Escalar: 3(1,2,3)=(3,6,9) |\\n| Rⁿ | n-tuplas (x₁,...,xₙ) | Suma: Componente a componente <br> Escalar: Multiplicación por cada componente | v=(1,2,...,n) | Suma: Componente a componente <br> Distributividad: α(v+w)=αv+αw |\\n| P_n[x] (Polinomios) | Polinomios de grado ≤n | Suma: Suma de coeficientes del mismo grado <br> Escalar: Multiplicación de todos los coeficientes | p(x)=x²+2x+1 | Suma: (x²+2x)+(x+1)=x²+3x+1 <br> Escalar: 2(x²+2x)=2x²+4x |\\n| C[a,b] (Funciones continuas) | Funciones continuas en [a,b] | Suma: (f+g)(x)=f(x)+g(x) <br> Escalar: (αf)(x)=αf(x) | f(x)=sin(x) | Suma: sin(x)+cos(x) <br> Escalar: 2sin(x) |\\n| M_m×n (Matrices) | Matrices de m×n | Suma: Suma elemento a elemento <br> Escalar: Multiplicación de cada elemento | A=[[1,2],[3,4]] | Suma: Elemento a elemento <br> Escalar: α[[1,2],[3,4]]=[[α,2α],[3α,4α]] |\\n\\n*Tabla 2. Comparación de espacios vectoriales comunes y sus propiedades fundamentales*\\n\\n#### Notas importantes sobre los espacios vectoriales presentados:\\n\\n1. **Dimensionalidad**: R², R³ y Rⁿ tienen dimensión finita igual a 2, 3 y n respectivamente. Son los espacios más intuitivos y frecuentemente utilizados en aplicaciones básicas de machine learning.\\n\\n2. **Espacios de polinomios**: P_n[x] es particularmente útil en aproximación de funciones y regresión polinomial. La dimensión de P_n[x] es n+1, correspondiente a los coeficientes del polinomio.\\n\\n3. **Espacios de funciones**: C[a,b] es un espacio de dimensión infinita, fundamental en análisis funcional y en la teoría de redes neuronales, donde las funciones de activación son elementos de este espacio.\\n\\n4. **Espacios matriciales**: M_m×n es esencial en machine learning para representar transformaciones lineales, datos tabulares y parámetros de modelos. Su dimensión es m×n.\\n\\nEstos espacios vectoriales proporcionan el fundamento matemático para muchas aplicaciones en machine learning, desde la representación de datos hasta la optimización de modelos.\\n\\n \\n#### Definición de Subespacio Vectorial\\n\\nUn subespacio vectorial es un subconjunto $W$ de un espacio vectorial $V$ que mantiene la estructura algebraica del espacio vectorial original. Formalmente, para que $W$ sea un subespacio vectorial de $V$, debe cumplir tres condiciones fundamentales:\\n\\n1. **No vacío**: El vector nulo $\\\\mathbf{0}$ debe pertenecer a $W$\\n2. **Cerrado bajo suma**: Si $\\\\mathbf{u}, \\\\mathbf{v} \\\\in W$, entonces $\\\\mathbf{u} + \\\\mathbf{v} \\\\in W$\\n3. **Cerrado bajo multiplicación escalar**: Si $\\\\mathbf{v} \\\\in W$ y $\\\\alpha \\\\in \\\\mathbb{R}$, entonces $\\\\alpha\\\\mathbf{v} \\\\in W$\\n\\n#### Demostración de Suficiencia\\n\\nPara demostrar que estas condiciones son suficientes, debemos verificar que $W$ cumple todos los axiomas de un espacio vectorial:\\n\\n1. La asociatividad y conmutatividad de la suma se heredan de $V$, ya que las operaciones son las mismas.\\n\\n2. El vector nulo está en $W$ por la primera condición, actuando como elemento neutro.\\n\\n3. Para cualquier $\\\\mathbf{v} \\\\in W$, su opuesto $-\\\\mathbf{v}$ existe en $W$ porque:\\n   - Por la condición 3, si $\\\\mathbf{v} \\\\in W$, entonces $(-1)\\\\mathbf{v} \\\\in W$\\n   - Y $(-1)\\\\mathbf{v} = -\\\\mathbf{v}$\\n\\n4. Las propiedades de distributividad y asociatividad escalar se heredan automáticamente de $V$.\\n\\n#### Consecuencia Importante\\n\\nUna propiedad fundamental que se deriva de estas condiciones es que cualquier combinación lineal de vectores en $W$ también pertenece a $W$. Es decir, si $\\\\mathbf{v}_1, \\\\mathbf{v}_2, ..., \\\\mathbf{v}_n \\\\in W$ y $\\\\alpha_1, \\\\alpha_2, ..., \\\\alpha_n \\\\in \\\\mathbb{R}$, entonces:\\n\\n$$\\n\\\\alpha_1\\\\mathbf{v}_1 + \\\\alpha_2\\\\mathbf{v}_2 + ... + \\\\alpha_n\\\\mathbf{v}_n \\\\in W\\n$$\\n\\nEsta propiedad es fundamental para el estudio posterior de bases y dimensiones de subespacios vectoriales.\\n' asignatura='Matematicas para machine learning I - Matemática aplicada' fuentes_verificadas='\\n' prompt_context=PromptContext(id_asignatura=None, user_input_id=None, id_tema=10, id_indice=3, id_titulo=None, id_epigrafe=46, parent_process_id=None, ai_process=None, plan_item_id=7, plan_id=41, tipo_herramienta='GRAFICA') docs_info=[{}, defaultdict(<class 'list'>, {})]\n", "2025-01-07 16:50:24,090 - logger - WARNING - Few shot examples not found\n"]}, {"name": "stdout", "output_type": "stream", "text": ["15:50:24.096   Message with 'claude-3-5-sonnet-20241022' [LLM]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "2025-01-07 16:50:41,364 - logger.ContentGenerator - INFO - Fuentes usadas are: []\n", "2025-01-07 16:50:41,364 - logger.ContentGenerator - INFO - Len docs is0 and the list range would be []\n", "2025-01-07 16:50:41,365 - logger.ContentGenerator - INFO - Valid doc sources have been found\n", "2025-01-07 16:50:41,366 - logger.ContentGenerator - INFO - Used docs are []\n"]}, {"name": "stdout", "output_type": "stream", "text": ["15:50:41.378   Embedding Creation with 'text-embedding-3-large' [LLM]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "2025-01-07 16:50:41,915 - logger.ContentGenerator - INFO - Input data inside generate content is: plan='Plan: Verificación de subespacios Descripcion: Desarrollar una guía paso a paso para verificar si un conjunto es subespacio. Incluir: 1) Verificación del vector cero, 2) Prueba de cerradura bajo suma, 3) Prueba de cerradura bajo multiplicación escalar. Proporcionar un ejemplo completo de verificación. Longitud: 400-450 palabras. Tipo herramienta: ContentTypes.TEXTO' bloque='FUNDAMENTOS DE ÁLGEBRA LINEAL' tema='Espacios Vectoriales y Estructuras Algebraicas' epigrafe='Fundamentos de espacios vectoriales y subespacios' epigrafe_context=\"epigrafes_anteriores=[] epigrafes_siguientes=['Bases, dimensiones y coordenadas', 'Productos internos y normas', 'Ortogonalidad y bases ortonormales', 'Aplicaciones en machine learning'] temas_anteriores=[] temas_siguientes=['Matrices y Transformaciones Lineales', 'Reducción Dimensional y Análisis Matricial', 'Cálculo Diferencial Multivariable', 'Técnicas de Optimización', 'Optimización Estocástica', 'Fundamentos de Probabilidad', 'Estadística para Machine Learning', 'Métodos Numéricos Avanzados']\" contenido_previo='\\n#### Ejemplos de Espacios Vectoriales\\n\\nA continuación, presentamos una comparación detallada de los espacios vectoriales más comunes y relevantes para machine learning, mostrando sus características específicas y cómo se manifiestan las operaciones vectoriales en cada uno.\\n\\n\\n| **Espacio Vectorial** | **Elementos** | **Operaciones** | **Ejemplo de Vector** | **Ejemplo de Axiomas** |\\n|----------------------|---------------|-----------------|---------------------|----------------------|\\n| R² | Pares ordenados (x,y) | Suma: (x₁,y₁)+(x₂,y₂)=(x₁+x₂,y₁+y₂) <br> Escalar: α(x,y)=(αx,αy) | v=(2,3) | Suma: (2,3)+(1,1)=(3,4) <br> Escalar: 2(2,3)=(4,6) |\\n| R³ | Tripletas (x,y,z) | Suma: (x₁,y₁,z₁)+(x₂,y₂,z₂)=(x₁+x₂,y₁+y₂,z₁+z₂) <br> Escalar: α(x,y,z)=(αx,αy,αz) | v=(1,2,3) | Suma: (1,2,3)+(1,1,1)=(2,3,4) <br> Escalar: 3(1,2,3)=(3,6,9) |\\n| Rⁿ | n-tuplas (x₁,...,xₙ) | Suma: Componente a componente <br> Escalar: Multiplicación por cada componente | v=(1,2,...,n) | Suma: Componente a componente <br> Distributividad: α(v+w)=αv+αw |\\n| P_n[x] (Polinomios) | Polinomios de grado ≤n | Suma: Suma de coeficientes del mismo grado <br> Escalar: Multiplicación de todos los coeficientes | p(x)=x²+2x+1 | Suma: (x²+2x)+(x+1)=x²+3x+1 <br> Escalar: 2(x²+2x)=2x²+4x |\\n| C[a,b] (Funciones continuas) | Funciones continuas en [a,b] | Suma: (f+g)(x)=f(x)+g(x) <br> Escalar: (αf)(x)=αf(x) | f(x)=sin(x) | Suma: sin(x)+cos(x) <br> Escalar: 2sin(x) |\\n| M_m×n (Matrices) | Matrices de m×n | Suma: Suma elemento a elemento <br> Escalar: Multiplicación de cada elemento | A=[[1,2],[3,4]] | Suma: Elemento a elemento <br> Escalar: α[[1,2],[3,4]]=[[α,2α],[3α,4α]] |\\n\\n*Tabla 2. Comparación de espacios vectoriales comunes y sus propiedades fundamentales*\\n\\n#### Notas importantes sobre los espacios vectoriales presentados:\\n\\n1. **Dimensionalidad**: R², R³ y Rⁿ tienen dimensión finita igual a 2, 3 y n respectivamente. Son los espacios más intuitivos y frecuentemente utilizados en aplicaciones básicas de machine learning.\\n\\n2. **Espacios de polinomios**: P_n[x] es particularmente útil en aproximación de funciones y regresión polinomial. La dimensión de P_n[x] es n+1, correspondiente a los coeficientes del polinomio.\\n\\n3. **Espacios de funciones**: C[a,b] es un espacio de dimensión infinita, fundamental en análisis funcional y en la teoría de redes neuronales, donde las funciones de activación son elementos de este espacio.\\n\\n4. **Espacios matriciales**: M_m×n es esencial en machine learning para representar transformaciones lineales, datos tabulares y parámetros de modelos. Su dimensión es m×n.\\n\\nEstos espacios vectoriales proporcionan el fundamento matemático para muchas aplicaciones en machine learning, desde la representación de datos hasta la optimización de modelos.\\n\\n \\n#### Definición de Subespacio Vectorial\\n\\nUn subespacio vectorial es un subconjunto $W$ de un espacio vectorial $V$ que mantiene la estructura algebraica del espacio vectorial original. Formalmente, para que $W$ sea un subespacio vectorial de $V$, debe cumplir tres condiciones fundamentales:\\n\\n1. **No vacío**: El vector nulo $\\\\mathbf{0}$ debe pertenecer a $W$\\n2. **Cerrado bajo suma**: Si $\\\\mathbf{u}, \\\\mathbf{v} \\\\in W$, entonces $\\\\mathbf{u} + \\\\mathbf{v} \\\\in W$\\n3. **Cerrado bajo multiplicación escalar**: Si $\\\\mathbf{v} \\\\in W$ y $\\\\alpha \\\\in \\\\mathbb{R}$, entonces $\\\\alpha\\\\mathbf{v} \\\\in W$\\n\\n#### Demostración de Suficiencia\\n\\nPara demostrar que estas condiciones son suficientes, debemos verificar que $W$ cumple todos los axiomas de un espacio vectorial:\\n\\n1. La asociatividad y conmutatividad de la suma se heredan de $V$, ya que las operaciones son las mismas.\\n\\n2. El vector nulo está en $W$ por la primera condición, actuando como elemento neutro.\\n\\n3. Para cualquier $\\\\mathbf{v} \\\\in W$, su opuesto $-\\\\mathbf{v}$ existe en $W$ porque:\\n   - Por la condición 3, si $\\\\mathbf{v} \\\\in W$, entonces $(-1)\\\\mathbf{v} \\\\in W$\\n   - Y $(-1)\\\\mathbf{v} = -\\\\mathbf{v}$\\n\\n4. Las propiedades de distributividad y asociatividad escalar se heredan automáticamente de $V$.\\n\\n#### Consecuencia Importante\\n\\nUna propiedad fundamental que se deriva de estas condiciones es que cualquier combinación lineal de vectores en $W$ también pertenece a $W$. Es decir, si $\\\\mathbf{v}_1, \\\\mathbf{v}_2, ..., \\\\mathbf{v}_n \\\\in W$ y $\\\\alpha_1, \\\\alpha_2, ..., \\\\alpha_n \\\\in \\\\mathbb{R}$, entonces:\\n\\n$$\\n\\\\alpha_1\\\\mathbf{v}_1 + \\\\alpha_2\\\\mathbf{v}_2 + ... + \\\\alpha_n\\\\mathbf{v}_n \\\\in W\\n$$\\n\\nEsta propiedad es fundamental para el estudio posterior de bases y dimensiones de subespacios vectoriales.\\n \\n\\n#### Visualización de Subespacios en R³\\n\\nLos subespacios vectoriales en R³ proporcionan ejemplos geométricos muy intuitivos de cómo se manifiestan las propiedades algebraicas en el espacio tridimensional. Veamos los tres tipos principales:\\n\\n#### 1. El Punto Origen: Subespacio de Dimensión 0\\n\\n![Figure](https://mermaid.ink/img/eyJjb2RlIjogIlxuZ3JhcGggTFJcbiAgICBPKCgwKSlcbiAgICBzdHlsZSBPIGZpbGw6I2Y5NixzdHJva2U6IzMzMyxzdHJva2Utd2lkdGg6NHB4XG4iLCAibWVybWFpZCI6IHsidGhlbWUiOiAiZGVmYXVsdCJ9fQ)\\n\\n*Diagrama 3. El origen como subespacio vectorial. Este punto representa el vector nulo (0,0,0).*\\n\\nEl punto origen es el subespacio más simple en R³. Es un subespacio válido porque:\\n- Contiene el vector nulo (es el propio punto)\\n- La suma de vectores nulos da el vector nulo\\n- Cualquier escalar multiplicado por el vector nulo da el vector nulo\\n\\n#### 2. Líneas por el Origen: Subespacios de Dimensión 1\\n\\n![Figure](https://mermaid.ink/img/eyJjb2RlIjogIlxuZ3JhcGggTFJcbiAgICBPKChPKSkgLS0-IHx2fEEoKFx1MDBiNykpXG4gICAgTyAtLT4gfC12fEIoKFx1MDBiNykpXG4gICAgc3R5bGUgTyBmaWxsOiNmOTYsc3Ryb2tlOiMzMzMsc3Ryb2tlLXdpZHRoOjRweFxuICAgIHN0eWxlIEEgZmlsbDojNjlmLHN0cm9rZTojMzMzLHN0cm9rZS13aWR0aDoycHhcbiAgICBzdHlsZSBCIGZpbGw6IzY5ZixzdHJva2U6IzMzMyxzdHJva2Utd2lkdGg6MnB4XG4iLCAibWVybWFpZCI6IHsidGhlbWUiOiAiZGVmYXVsdCJ9fQ)\\n\\n*Diagrama 4. Línea que pasa por el origen como subespacio vectorial. La línea se extiende infinitamente en ambas direcciones.*\\n\\nUna línea que pasa por el origen es un subespacio porque:\\n- Contiene el vector nulo (el origen)\\n- La suma de dos vectores en la línea permanece en la línea\\n- Multiplicar cualquier vector de la línea por un escalar produce otro vector en la misma línea\\n\\n#### 3. Planos por el Origen: Subespacios de Dimensión 2\\n\\n![Figure](https://mermaid.ink/img/eyJjb2RlIjogIlxuZ3JhcGggVERcbiAgICBPKChPKSkgLS0-IHx2XHUyMDgxfEEoKFx1MDBiNykpXG4gICAgTyAtLT4gfHZcdTIwODJ8QigoXHUwMGI3KSlcbiAgICBBIC0tPiB8Y29tYmluYWNpb25lc3xDKChcdTAwYjcpKVxuICAgIEIgLS0-IHxsaW5lYWxlc3xDXG4gICAgc3R5bGUgTyBmaWxsOiNmOTYsc3Ryb2tlOiMzMzMsc3Ryb2tlLXdpZHRoOjRweFxuICAgIHN0eWxlIEEgZmlsbDojNjlmLHN0cm9rZTojMzMzLHN0cm9rZS13aWR0aDoycHhcbiAgICBzdHlsZSBCIGZpbGw6IzY5ZixzdHJva2U6IzMzMyxzdHJva2Utd2lkdGg6MnB4XG4gICAgc3R5bGUgQyBmaWxsOiM2OWYsc3Ryb2tlOiMzMzMsc3Ryb2tlLXdpZHRoOjJweFxuIiwgIm1lcm1haWQiOiB7InRoZW1lIjogImRlZmF1bHQifX0)\\n\\n*Diagrama 5. Plano que pasa por el origen como subespacio vectorial. El plano se genera mediante todas las combinaciones lineales posibles de dos vectores linealmente independientes.*\\n\\nUn plano que pasa por el origen es un subespacio porque:\\n- Contiene el vector nulo (el origen)\\n- La suma de dos vectores en el plano resulta en otro vector en el mismo plano\\n- Multiplicar cualquier vector del plano por un escalar mantiene el resultado en el plano\\n\\n#### Propiedades Comunes de los Subespacios\\n\\n![Figure](https://mermaid.ink/img/eyJjb2RlIjogIlxubWluZG1hcFxuICAgIHJvb3QoKFN1YmVzcGFjaW9zIGVuIFJcdTAwYjMpKVxuICAgICAgICBEaW1lbnNpXHUwMGYzbiAwXG4gICAgICAgICAgICBQdW50byBvcmlnZW5cbiAgICAgICAgICAgIFZlY3RvciBudWxvXG4gICAgICAgIERpbWVuc2lcdTAwZjNuIDFcbiAgICAgICAgICAgIExcdTAwZWRuZWFzIHBvciBvcmlnZW5cbiAgICAgICAgICAgIFNwYW4gZGUgdW4gdmVjdG9yXG4gICAgICAgIERpbWVuc2lcdTAwZjNuIDJcbiAgICAgICAgICAgIFBsYW5vcyBwb3Igb3JpZ2VuXG4gICAgICAgICAgICBTcGFuIGRlIGRvcyB2ZWN0b3Jlc1xuICAgICAgICBEaW1lbnNpXHUwMGYzbiAzXG4gICAgICAgICAgICBSXHUwMGIzIGNvbXBsZXRvXG4gICAgICAgICAgICBTcGFuIGRlIHRyZXMgdmVjdG9yZXNcbiIsICJtZXJtYWlkIjogeyJ0aGVtZSI6ICJkZWZhdWx0In19)\\n\\n*Diagrama 6. Jerarquía de subespacios en R³ según su dimensión.*\\n\\nTodos estos subespacios comparten características fundamentales:\\n1. Son cerrados bajo operaciones vectoriales\\n2. Contienen el vector nulo\\n3. Su dimensión es menor o igual a 3 (la dimensión de R³)\\n4. Cada uno es generado por combinaciones lineales de sus vectores base\\n\\n' asignatura='Matematicas para machine learning I - Matemática aplicada' fuentes_verificadas='\\n' prompt_context=PromptContext(id_asignatura=None, user_input_id=None, id_tema=10, id_indice=3, id_titulo=None, id_epigrafe=46, parent_process_id=None, ai_process=None, plan_item_id=8, plan_id=41, tipo_herramienta='TEXTO') docs_info=[{}, defaultdict(<class 'list'>, {})]\n", "2025-01-07 16:50:41,921 - logger - WARNING - Few shot examples not found\n"]}, {"name": "stdout", "output_type": "stream", "text": ["15:50:41.925   Message with 'claude-3-5-sonnet-20241022' [LLM]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "2025-01-07 16:50:55,715 - logger.ContentGenerator - INFO - Fuentes usadas are: []\n", "2025-01-07 16:50:55,716 - logger.ContentGenerator - INFO - Len docs is0 and the list range would be []\n", "2025-01-07 16:50:55,716 - logger.ContentGenerator - INFO - Valid doc sources have been found\n", "2025-01-07 16:50:55,717 - logger.ContentGenerator - INFO - Used docs are []\n"]}, {"name": "stdout", "output_type": "stream", "text": ["15:50:55.732   Embedding Creation with 'text-embedding-3-large' [LLM]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "2025-01-07 16:50:56,231 - logger.ContentGenerator - INFO - Input data inside generate content is: plan='Plan: Operaciones con subespacios Descripcion: Explicar las operaciones básicas con subespacios: intersección y suma de subespacios. Demostrar que la intersección siempre es un subespacio. Incluir ejemplos geométricos en R² o R³. No incluir span ni combinaciones lineales (tema posterior). Longitud: 300-350 palabras. Tipo herramienta: ContentTypes.TEXTO' bloque='FUNDAMENTOS DE ÁLGEBRA LINEAL' tema='Espacios Vectoriales y Estructuras Algebraicas' epigrafe='Fundamentos de espacios vectoriales y subespacios' epigrafe_context=\"epigrafes_anteriores=[] epigrafes_siguientes=['Bases, dimensiones y coordenadas', 'Productos internos y normas', 'Ortogonalidad y bases ortonormales', 'Aplicaciones en machine learning'] temas_anteriores=[] temas_siguientes=['Matrices y Transformaciones Lineales', 'Reducción Dimensional y Análisis Matricial', 'Cálculo Diferencial Multivariable', 'Técnicas de Optimización', 'Optimización Estocástica', 'Fundamentos de Probabilidad', 'Estadística para Machine Learning', 'Métodos Numéricos Avanzados']\" contenido_previo='\\n#### Definición de Subespacio Vectorial\\n\\nUn subespacio vectorial es un subconjunto $W$ de un espacio vectorial $V$ que mantiene la estructura algebraica del espacio vectorial original. Formalmente, para que $W$ sea un subespacio vectorial de $V$, debe cumplir tres condiciones fundamentales:\\n\\n1. **No vacío**: El vector nulo $\\\\mathbf{0}$ debe pertenecer a $W$\\n2. **Cerrado bajo suma**: Si $\\\\mathbf{u}, \\\\mathbf{v} \\\\in W$, entonces $\\\\mathbf{u} + \\\\mathbf{v} \\\\in W$\\n3. **Cerrado bajo multiplicación escalar**: Si $\\\\mathbf{v} \\\\in W$ y $\\\\alpha \\\\in \\\\mathbb{R}$, entonces $\\\\alpha\\\\mathbf{v} \\\\in W$\\n\\n#### Demostración de Suficiencia\\n\\nPara demostrar que estas condiciones son suficientes, debemos verificar que $W$ cumple todos los axiomas de un espacio vectorial:\\n\\n1. La asociatividad y conmutatividad de la suma se heredan de $V$, ya que las operaciones son las mismas.\\n\\n2. El vector nulo está en $W$ por la primera condición, actuando como elemento neutro.\\n\\n3. Para cualquier $\\\\mathbf{v} \\\\in W$, su opuesto $-\\\\mathbf{v}$ existe en $W$ porque:\\n   - Por la condición 3, si $\\\\mathbf{v} \\\\in W$, entonces $(-1)\\\\mathbf{v} \\\\in W$\\n   - Y $(-1)\\\\mathbf{v} = -\\\\mathbf{v}$\\n\\n4. Las propiedades de distributividad y asociatividad escalar se heredan automáticamente de $V$.\\n\\n#### Consecuencia Importante\\n\\nUna propiedad fundamental que se deriva de estas condiciones es que cualquier combinación lineal de vectores en $W$ también pertenece a $W$. Es decir, si $\\\\mathbf{v}_1, \\\\mathbf{v}_2, ..., \\\\mathbf{v}_n \\\\in W$ y $\\\\alpha_1, \\\\alpha_2, ..., \\\\alpha_n \\\\in \\\\mathbb{R}$, entonces:\\n\\n$$\\n\\\\alpha_1\\\\mathbf{v}_1 + \\\\alpha_2\\\\mathbf{v}_2 + ... + \\\\alpha_n\\\\mathbf{v}_n \\\\in W\\n$$\\n\\nEsta propiedad es fundamental para el estudio posterior de bases y dimensiones de subespacios vectoriales.\\n \\n\\n#### Visualización de Subespacios en R³\\n\\nLos subespacios vectoriales en R³ proporcionan ejemplos geométricos muy intuitivos de cómo se manifiestan las propiedades algebraicas en el espacio tridimensional. Veamos los tres tipos principales:\\n\\n#### 1. El Punto Origen: Subespacio de Dimensión 0\\n\\n![Figure](https://mermaid.ink/img/eyJjb2RlIjogIlxuZ3JhcGggTFJcbiAgICBPKCgwKSlcbiAgICBzdHlsZSBPIGZpbGw6I2Y5NixzdHJva2U6IzMzMyxzdHJva2Utd2lkdGg6NHB4XG4iLCAibWVybWFpZCI6IHsidGhlbWUiOiAiZGVmYXVsdCJ9fQ)\\n\\n*Diagrama 3. El origen como subespacio vectorial. Este punto representa el vector nulo (0,0,0).*\\n\\nEl punto origen es el subespacio más simple en R³. Es un subespacio válido porque:\\n- Contiene el vector nulo (es el propio punto)\\n- La suma de vectores nulos da el vector nulo\\n- Cualquier escalar multiplicado por el vector nulo da el vector nulo\\n\\n#### 2. Líneas por el Origen: Subespacios de Dimensión 1\\n\\n![Figure](https://mermaid.ink/img/eyJjb2RlIjogIlxuZ3JhcGggTFJcbiAgICBPKChPKSkgLS0-IHx2fEEoKFx1MDBiNykpXG4gICAgTyAtLT4gfC12fEIoKFx1MDBiNykpXG4gICAgc3R5bGUgTyBmaWxsOiNmOTYsc3Ryb2tlOiMzMzMsc3Ryb2tlLXdpZHRoOjRweFxuICAgIHN0eWxlIEEgZmlsbDojNjlmLHN0cm9rZTojMzMzLHN0cm9rZS13aWR0aDoycHhcbiAgICBzdHlsZSBCIGZpbGw6IzY5ZixzdHJva2U6IzMzMyxzdHJva2Utd2lkdGg6MnB4XG4iLCAibWVybWFpZCI6IHsidGhlbWUiOiAiZGVmYXVsdCJ9fQ)\\n\\n*Diagrama 4. Línea que pasa por el origen como subespacio vectorial. La línea se extiende infinitamente en ambas direcciones.*\\n\\nUna línea que pasa por el origen es un subespacio porque:\\n- Contiene el vector nulo (el origen)\\n- La suma de dos vectores en la línea permanece en la línea\\n- Multiplicar cualquier vector de la línea por un escalar produce otro vector en la misma línea\\n\\n#### 3. Planos por el Origen: Subespacios de Dimensión 2\\n\\n![Figure](https://mermaid.ink/img/eyJjb2RlIjogIlxuZ3JhcGggVERcbiAgICBPKChPKSkgLS0-IHx2XHUyMDgxfEEoKFx1MDBiNykpXG4gICAgTyAtLT4gfHZcdTIwODJ8QigoXHUwMGI3KSlcbiAgICBBIC0tPiB8Y29tYmluYWNpb25lc3xDKChcdTAwYjcpKVxuICAgIEIgLS0-IHxsaW5lYWxlc3xDXG4gICAgc3R5bGUgTyBmaWxsOiNmOTYsc3Ryb2tlOiMzMzMsc3Ryb2tlLXdpZHRoOjRweFxuICAgIHN0eWxlIEEgZmlsbDojNjlmLHN0cm9rZTojMzMzLHN0cm9rZS13aWR0aDoycHhcbiAgICBzdHlsZSBCIGZpbGw6IzY5ZixzdHJva2U6IzMzMyxzdHJva2Utd2lkdGg6MnB4XG4gICAgc3R5bGUgQyBmaWxsOiM2OWYsc3Ryb2tlOiMzMzMsc3Ryb2tlLXdpZHRoOjJweFxuIiwgIm1lcm1haWQiOiB7InRoZW1lIjogImRlZmF1bHQifX0)\\n\\n*Diagrama 5. Plano que pasa por el origen como subespacio vectorial. El plano se genera mediante todas las combinaciones lineales posibles de dos vectores linealmente independientes.*\\n\\nUn plano que pasa por el origen es un subespacio porque:\\n- Contiene el vector nulo (el origen)\\n- La suma de dos vectores en el plano resulta en otro vector en el mismo plano\\n- Multiplicar cualquier vector del plano por un escalar mantiene el resultado en el plano\\n\\n#### Propiedades Comunes de los Subespacios\\n\\n![Figure](https://mermaid.ink/img/eyJjb2RlIjogIlxubWluZG1hcFxuICAgIHJvb3QoKFN1YmVzcGFjaW9zIGVuIFJcdTAwYjMpKVxuICAgICAgICBEaW1lbnNpXHUwMGYzbiAwXG4gICAgICAgICAgICBQdW50byBvcmlnZW5cbiAgICAgICAgICAgIFZlY3RvciBudWxvXG4gICAgICAgIERpbWVuc2lcdTAwZjNuIDFcbiAgICAgICAgICAgIExcdTAwZWRuZWFzIHBvciBvcmlnZW5cbiAgICAgICAgICAgIFNwYW4gZGUgdW4gdmVjdG9yXG4gICAgICAgIERpbWVuc2lcdTAwZjNuIDJcbiAgICAgICAgICAgIFBsYW5vcyBwb3Igb3JpZ2VuXG4gICAgICAgICAgICBTcGFuIGRlIGRvcyB2ZWN0b3Jlc1xuICAgICAgICBEaW1lbnNpXHUwMGYzbiAzXG4gICAgICAgICAgICBSXHUwMGIzIGNvbXBsZXRvXG4gICAgICAgICAgICBTcGFuIGRlIHRyZXMgdmVjdG9yZXNcbiIsICJtZXJtYWlkIjogeyJ0aGVtZSI6ICJkZWZhdWx0In19)\\n\\n*Diagrama 6. Jerarquía de subespacios en R³ según su dimensión.*\\n\\nTodos estos subespacios comparten características fundamentales:\\n1. Son cerrados bajo operaciones vectoriales\\n2. Contienen el vector nulo\\n3. Su dimensión es menor o igual a 3 (la dimensión de R³)\\n4. Cada uno es generado por combinaciones lineales de sus vectores base\\n\\n \\n#### Verificación de Subespacios Vectoriales: Guía Paso a Paso\\n\\nPara determinar si un conjunto es un subespacio vectorial, debemos verificar sistemáticamente tres propiedades fundamentales. A continuación, presentamos el proceso detallado de verificación junto con un ejemplo práctico.\\n\\n#### Pasos de Verificación\\n\\n1. **Verificación del vector cero**\\n   - Comprobar si el vector nulo pertenece al conjunto\\n   - Si el vector nulo no está en el conjunto, podemos concluir inmediatamente que no es un subespacio\\n\\n2. **Verificación de cerradura bajo suma**\\n   - Tomar dos vectores arbitrarios $\\\\mathbf{u}, \\\\mathbf{v}$ del conjunto\\n   - Verificar si su suma $\\\\mathbf{u} + \\\\mathbf{v}$ también pertenece al conjunto\\n   - Esta propiedad debe cumplirse para cualquier par de vectores del conjunto\\n\\n3. **Verificación de cerradura bajo multiplicación escalar**\\n   - Tomar un vector arbitrario $\\\\mathbf{v}$ del conjunto y un escalar $\\\\alpha$\\n   - Verificar si el producto $\\\\alpha\\\\mathbf{v}$ pertenece al conjunto\\n   - Esta propiedad debe cumplirse para cualquier vector y cualquier escalar\\n\\n#### Ejemplo Práctico\\n\\nConsideremos el conjunto $W = \\\\{(x,y) \\\\in \\\\mathbb{R}^2 : x = 2y\\\\}$ (todos los vectores donde la primera coordenada es el doble de la segunda).\\n\\n1. **Verificación del vector cero**:\\n   - El vector $(0,0)$ satisface la condición $0 = 2(0)$\\n   - ✓ El vector cero pertenece a $W$\\n\\n2. **Verificación de cerradura bajo suma**:\\n   - Sean $\\\\mathbf{u} = (2a,a)$ y $\\\\mathbf{v} = (2b,b)$ dos vectores en $W$\\n   - $\\\\mathbf{u} + \\\\mathbf{v} = (2a+2b,a+b) = (2(a+b),a+b)$\\n   - Como $2(a+b)$ es el doble de $(a+b)$\\n   - ✓ La suma pertenece a $W$\\n\\n3. **Verificación de cerradura bajo multiplicación escalar**:\\n   - Sea $\\\\mathbf{v} = (2a,a)$ un vector en $W$ y $\\\\alpha$ un escalar\\n   - $\\\\alpha\\\\mathbf{v} = (\\\\alpha(2a),\\\\alpha a) = (2(\\\\alpha a),\\\\alpha a)$\\n   - Como $2(\\\\alpha a)$ es el doble de $\\\\alpha a$\\n   - ✓ El producto escalar pertenece a $W$\\n\\nConclusión: Como $W$ cumple las tres propiedades, es un subespacio vectorial de $\\\\mathbb{R}^2$. Geométricamente, $W$ representa una línea que pasa por el origen con pendiente $\\\\frac{1}{2}$.\\n' asignatura='Matematicas para machine learning I - Matemática aplicada' fuentes_verificadas='\\n' prompt_context=PromptContext(id_asignatura=None, user_input_id=None, id_tema=10, id_indice=3, id_titulo=None, id_epigrafe=46, parent_process_id=None, ai_process=None, plan_item_id=9, plan_id=41, tipo_herramienta='TEXTO') docs_info=[{}, defaultdict(<class 'list'>, {})]\n", "2025-01-07 16:50:56,237 - logger - WARNING - Few shot examples not found\n"]}, {"name": "stdout", "output_type": "stream", "text": ["15:50:56.242   Message with 'claude-3-5-sonnet-20241022' [LLM]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n", "2025-01-07 16:51:08,617 - logger.ContentGenerator - INFO - Fuentes usadas are: []\n", "2025-01-07 16:51:08,617 - logger.ContentGenerator - INFO - Len docs is0 and the list range would be []\n", "2025-01-07 16:51:08,618 - logger.ContentGenerator - INFO - Valid doc sources have been found\n", "2025-01-07 16:51:08,619 - logger.ContentGenerator - INFO - Used docs are []\n"]}], "source": ["with logfire.span('generate_content'):\n", "    content = await generate_content_task._generate_content(content_plans, item)"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [], "source": ["from IPython.display import Markdown, display"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [], "source": ["plan_outline = ''\n", "for plan in content_plans[0].output:\n", "    plan_outline += f'# {plan.plan}\\n'\n", "    plan_outline += f'## {plan.descripcion}\\n\\n'\n"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["# Motivación e importancia de los espacios vectoriales\n", "## Redactar una introducción que explique por qué los espacios vectoriales son fundamentales en matemáticas y computación. Incluir ejemplos intuitivos como vectores en el plano y representaciones de datos. Enfatizar cómo las propiedades de los espacios vectoriales permiten operaciones consistentes y predecibles. Longitud aproximada: 300-400 palabras. Mantener un tono accesible pero riguroso.\n", "\n", "# Definición formal de espacio vectorial\n", "## Presentar la definición matemática formal de espacio vectorial sobre un campo (típicamente ℝ). Incluir la notación matemática precisa y explicar cada componente de la definición. Mencionar los dos tipos de operaciones fundamentales (suma vectorial y multiplicación por escalar) y su notación. Longitud: 250-300 palabras.\n", "\n", "# Axiomas de espacios vectoriales\n", "## Crear una tabla detallada con los 8 axiomas de espacios vectoriales. Columnas: Nombre del axioma, Expresión matemática, Explicación en lenguaje natural, Ejemplo simple. Incluir tanto axiomas de suma vectorial como de multiplicación por escalar. Añadir notas sobre la importancia de cada axioma.\n", "\n", "# Visualización geométrica de operaciones vectoriales\n", "## Crear un diagrama que ilustre geométricamente: 1) Suma de vectores usando la regla del paralelogramo, 2) Multiplicación por escalar mostrando escalado y dirección. Usar vectores en R² para mayor claridad. Incluir leyendas explicativas para cada operación.\n", "\n", "# Ejemplos de espacios vectoriales\n", "## Desarrollar una tabla comparativa de diferentes espacios vectoriales. Incluir: R², R³, Rⁿ, espacio de polinomios, espacio de funciones continuas, espacio de matrices. Para cada uno: definir elementos, operaciones específicas y un ejemplo concreto de cómo se cumplen los axiomas.\n", "\n", "# Definición y propiedades de subespacios vectoriales\n", "## Presentar la definición formal de subespacio vectorial. Explicar las tres condiciones necesarias y suficientes: no vacío, cerrado bajo suma, cerrado bajo multiplicación por escalar. Incluir la demostración de por qué estas condiciones son suficientes. Longitud: 300-350 palabras.\n", "\n", "# Visualización de subespacios en R³\n", "## Crear un diagrama que muestre ejemplos geométricos de subespacios en R³: punto origen, líneas que pasan por el origen, planos que pasan por el origen. Incluir anotaciones que expliquen por qué cada uno es un subespacio válido.\n", "\n", "# Verificación de subespacios\n", "## Desarrollar una guía paso a paso para verificar si un conjunto es subespacio. Incluir: 1) Verificación del vector cero, 2) Prueba de cerradura bajo suma, 3) Prueba de cerradura bajo multiplicación escalar. Proporcionar un ejemplo completo de verificación. Longitud: 400-450 palabras.\n", "\n", "# Operaciones con subespacios\n", "## Explicar las operaciones básicas con subespacios: intersección y suma de subespacios. Demostrar que la intersección siempre es un subespacio. Incluir ejemplos geométricos en R² o R³. No incluir span ni combinaciones lineales (tema posterior). Longitud: 300-350 palabras.\n", "\n", "\n"]}], "source": ["print(plan_outline)"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"data": {"text/markdown": ["\n", "#### ¿Por qué son importantes los espacios vectoriales?\n", "\n", "Los espacios vectoriales son una de las estructuras matemáticas más fundamentales y versátiles en matemáticas modernas y ciencias de la computación. Para entender su importancia, imaginemos primero algo familiar: el plano cartesiano. Cuando dibujamos una flecha desde el origen hasta cualquier punto del plano, estamos trabajando con un vector bidimensional. Esta representación simple pero poderosa nos permite modelar desde velocidad y fuerza en física hasta precios y cantidades en economía.\n", "\n", "Sin embargo, la verdadera potencia de los espacios vectoriales va mucho más allá del plano o el espacio tridimensional. En el contexto de machine learning y ciencia de datos, los espacios vectoriales nos permiten representar datos de alta dimensionalidad de manera natural. Por ejemp<PERSON>, podemos representar una imagen como un vector donde cada componente corresponde a la intensidad de un píxel, o un documento de texto como un vector donde cada dimensión representa la frecuencia de una palabra.\n", "\n", "#### Propiedades que hacen especiales a los espacios vectoriales\n", "\n", "Lo que hace particularmente útiles a los espacios vectoriales son sus propiedades algebraicas bien definidas. Cuando trabajamos con vectores:\n", "- Podemos sumarlos y el resultado sigue siendo un vector\n", "- Podemos multiplicarlos por escalares manteniendo su estructura\n", "- Estas operaciones siguen reglas consistentes y predecibles\n", "\n", "<PERSON><PERSON><PERSON> propiedades aparentemente simples son fundamentales porque:\n", "1. G<PERSON><PERSON><PERSON> que nuestras operaciones sean consistentes y bien comportadas\n", "2. Permiten desarrollar algoritmos robustos y eficientes\n", "3. Facilitan la generalización de conceptos geométricos a dimensiones arbitrarias\n", "\n", "#### Aplicaciones prácticas\n", "\n", "En el contexto de machine learning, los espacios vectoriales son el fundamento para:\n", "- Representación de características de datos\n", "- Transformaciones lineales y no lineales\n", "- Reducción de dimensionalidad\n", "- Optimización de funciones\n", "- An<PERSON><PERSON><PERSON> de patrones y clustering\n", "\n", "Esta estructura matemática, aparentemente abstracta, es la que nos permite desarrollar algoritmos eficientes y robustos para resolver problemas complejos en el mundo real.\n", "\n", "#### Definición formal de espacio vectorial\n", "\n", "Un espacio vectorial sobre un campo $\\mathbb{F}$ (típicamente $\\mathbb{R}$) es una estructura algebraica que consiste en un conjunto $V$ junto con dos operaciones:\n", "\n", "1. **Suma vectorial**: $+: V \\times V \\rightarrow V$\n", "2. **Multiplicación por escalar**: $\\cdot: \\mathbb{F} \\times V \\rightarrow V$\n", "\n", "Para que $(V,+,\\cdot)$ sea un espacio vectorial, estas operaciones deben satisfacer los siguientes axiomas. Para todo $\\mathbf{u}, \\mathbf{v}, \\mathbf{w} \\in V$ y $\\alpha, \\beta \\in \\mathbb{F}$:\n", "\n", "**Axioma<PERSON> de la suma vectorial:**\n", "1. Asociatividad: $(\\mathbf{u} + \\mathbf{v}) + \\mathbf{w} = \\mathbf{u} + (\\mathbf{v} + \\mathbf{w})$\n", "2. Conmutatividad: $\\mathbf{u} + \\mathbf{v} = \\mathbf{v} + \\mathbf{u}$\n", "3. Elemento neutro: Existe $\\mathbf{0} \\in V$ tal que $\\mathbf{v} + \\mathbf{0} = \\mathbf{v}$\n", "4. Elemento opuesto: Para cada $\\mathbf{v} \\in V$, existe $-\\mathbf{v} \\in V$ tal que $\\mathbf{v} + (-\\mathbf{v}) = \\mathbf{0}$\n", "\n", "**Axiomas de la multiplicación por escalar:**\n", "5. Distributividad respecto a la suma vectorial: $\\alpha(\\mathbf{u} + \\mathbf{v}) = \\alpha\\mathbf{u} + \\alpha\\mathbf{v}$\n", "6. Distributividad respecto a la suma escalar: $(\\alpha + \\beta)\\mathbf{v} = \\alpha\\mathbf{v} + \\beta\\mathbf{v}$\n", "7. Asociatividad con escalares: $\\alpha(\\beta\\mathbf{v}) = (\\alpha\\beta)\\mathbf{v}$\n", "8. Elemento neutro del campo: $1\\mathbf{v} = \\mathbf{v}$\n", "\n", "Esta estructura matemática proporciona el fundamento para el desarrollo de conceptos más avanzados como bases, dimensiones y transformaciones lineales, que serán cruciales en aplicaciones de machine learning.\n", "\n", "\n", "\n", "| **Axioma** | **Expresión matemática** | **Explicación** | **Ejemplo en R²** | **Importancia** |\n", "|------------|-------------------------|-----------------|-------------------|-----------------|\n", "| 1. Asociatividad de la suma | $(\\mathbf{u} + \\mathbf{v}) + \\mathbf{w} = \\mathbf{u} + (\\mathbf{v} + \\mathbf{w})$ | El orden en que sumamos tres o más vectores no afecta al resultado | $(1,2) + ((3,4) + (5,6)) = ((1,2) + (3,4)) + (5,6)$ | Permite manipular sumas de múltiples vectores sin preocuparnos por el orden de agrupación |\n", "| 2. Conmutatividad de la suma | $\\mathbf{u} + \\mathbf{v} = \\mathbf{v} + \\mathbf{u}$ | El orden de los sumandos no afecta al resultado | $(1,2) + (3,4) = (3,4) + (1,2)$ | Facilita la manipulación algebraica y simplificación de expresiones vectoriales |\n", "| 3. Existencia del elemento neutro | $\\mathbf{v} + \\mathbf{0} = \\mathbf{v}$ | Existe un vector nulo que al sumarse a cualquier vector no lo modifica | $(1,2) + (0,0) = (1,2)$ | Define un punto de referencia y permite la resta vectorial |\n", "| 4. Existencia del elemento opuesto | $\\mathbf{v} + (-\\mathbf{v}) = \\mathbf{0}$ | Para cada vector existe otro que al sumarse dan el vector nulo | $(1,2) + (-1,-2) = (0,0)$ | Permite definir la resta de vectores y resolver ecuaciones vectoriales |\n", "| 5. Distributividad escalar-vectorial | $\\alpha(\\mathbf{u} + \\mathbf{v}) = \\alpha\\mathbf{u} + \\alpha\\mathbf{v}$ | La multiplicación por escalar se distribuye sobre la suma de vectores | $2((1,2) + (3,4)) = 2(1,2) + 2(3,4)$ | Fundamental para operaciones lineales y transformaciones |\n", "| 6. Distributividad escalar | $(\\alpha + \\beta)\\mathbf{v} = \\alpha\\mathbf{v} + \\beta\\mathbf{v}$ | La suma de escalares se distribuye sobre la multiplicación vectorial | $(2+3)(1,2) = 2(1,2) + 3(1,2)$ | Permite factorizar y simplificar expresiones vectoriales |\n", "| 7. Asociatividad escalar | $\\alpha(\\beta\\mathbf{v}) = (\\alpha\\beta)\\mathbf{v}$ | El orden de multiplicación de escalares no afecta al resultado | $2(3(1,2)) = (2·3)(1,2)$ | Garantiza consistencia en operaciones con múltiples escalares |\n", "| 8. Elemento neutro escalar | $1\\mathbf{v} = \\mathbf{v}$ | La multiplicación por 1 no modifica el vector | $1(1,2) = (1,2)$ | Define la unidad de escala y completa la estructura algebraica |\n", "\n", "*Tabla 1. Axiomas de espacios vectoriales con ejemplos y su importancia*\n", "\n", "#### Nota sobre los axiomas\n", "\n", "Estos ocho axiomas son fundamentales y necesarios para definir un espacio vectorial. Cada uno juega un papel específico en garantizar que las operaciones vectoriales sean bien comportadas y predecibles, lo cual es esencial para desarrollar teoría más avanzada y aplicaciones prácticas en machine learning. La comprensión profunda de estos axiomas es crucial para el estudio posterior de bases, transformaciones lineales y técnicas de optimización.\n", "\n", "\n", "\n", "#### Visualización geométrica de operaciones vectoriales\n", "\n", "La comprensión geométrica de las operaciones vectoriales es fundamental para desarrollar una intuición sobre cómo funcionan los espacios vectoriales. Veamos las dos operaciones fundamentales:\n", "\n", "#### Suma de vectores: Regla del paralelogramo\n", "\n", "![Figure](https://mermaid.ink/img/eyJjb2RlIjogIlxuZmxvd2NoYXJ0IExSXG4gICAgTygoTykpIC0tPiB8dnxBKChBKSlcbiAgICBPIC0tPiB8d3xCKChCKSlcbiAgICBBIC0tPiB8d3xDKChDKSlcbiAgICBCIC0tPiB8dnxDXG4gICAgc3R5bGUgTyBmaWxsOiNmOWYsc3Ryb2tlOiMzMzMsc3Ryb2tlLXdpZHRoOjJweFxuICAgIHN0eWxlIEEgZmlsbDojYmJmLHN0cm9rZTojMzMzLHN0cm9rZS13aWR0aDoycHhcbiAgICBzdHlsZSBCIGZpbGw6I2JiZixzdHJva2U6IzMzMyxzdHJva2Utd2lkdGg6MnB4XG4gICAgc3R5bGUgQyBmaWxsOiNiZmIsc3Ryb2tlOiMzMzMsc3Ryb2tlLXdpZHRoOjJweFxuIiwgIm1lcm1haWQiOiB7InRoZW1lIjogImRlZmF1bHQifX0)\n", "\n", "*Diagrama 1. Suma de vectores v + w usando la regla del paralelogramo. El vector resultante (en verde) es la diagonal del paralelogramo formado por los vectores v y w.*\n", "\n", "La suma de vectores $\\mathbf{v} + \\mathbf{w}$ se puede visualizar geométricamente mediante la regla del paralelogramo:\n", "1. Dibujamos los vectores $\\mathbf{v}$ y $\\mathbf{w}$ desde el mismo punto de origen O\n", "2. Completamos el paralelogramo trasladando cada vector\n", "3. La diagonal del paralelogramo desde el origen representa el vector suma $\\mathbf{v} + \\mathbf{w}$\n", "\n", "#### Multiplicación por escalar\n", "\n", "![Figure](https://mermaid.ink/img/eyJjb2RlIjogIlxuZmxvd2NoYXJ0IExSXG4gICAgTygoTykpIC0tPiB8dnxBKChBKSlcbiAgICBPIC0tPiB8MnZ8QigoQikpXG4gICAgTyAtLT4gfC0wLjV2fEMoKEMpKVxuICAgIHN0eWxlIE8gZmlsbDojZjlmLHN0cm9rZTojMzMzLHN0cm9rZS13aWR0aDoycHhcbiAgICBzdHlsZSBBIGZpbGw6I2JiZixzdHJva2U6IzMzMyxzdHJva2Utd2lkdGg6MnB4XG4gICAgc3R5bGUgQiBmaWxsOiNiZmIsc3Ryb2tlOiMzMzMsc3Ryb2tlLXdpZHRoOjJweFxuICAgIHN0eWxlIEMgZmlsbDojZmJiLHN0cm9rZTojMzMzLHN0cm9rZS13aWR0aDoycHhcbiIsICJtZXJtYWlkIjogeyJ0aGVtZSI6ICJkZWZhdWx0In19)\n", "\n", "*Diagrama 2. Multiplicación de un vector v por diferentes escalares: v (azul), 2v (verde) y -0.5v (rojo), mostrando el efecto en la magnitud y dirección.*\n", "\n", "La multiplicación por escalar $\\alpha\\mathbf{v}$ tiene dos efectos sobre el vector:\n", "1. **Magnitud**: El escalar $|\\alpha|$ determina cuánto se \"estira\" o \"encoge\" el vector\n", "   - Si $|\\alpha| > 1$, el vector se alarga\n", "   - Si $0 < |\\alpha| < 1$, el vector se acorta\n", "   \n", "2. **Dirección**: El signo de $\\alpha$ determina la dirección\n", "   - Si $\\alpha > 0$, la dirección se mantiene\n", "   - Si $\\alpha < 0$, la dirección se invierte\n", "   - Si $\\alpha = 0$, el vector se convierte en el vector nulo\n", "\n", "Estas visualizaciones geométricas son fundamentales para desarrollar la intuición necesaria en conceptos más avanzados como transformaciones lineales y espacios vectoriales abstractos.\n", "\n", "\n", "#### Ejemplos de Espacios Vectoriales\n", "\n", "A continuación, presentamos una comparación detallada de los espacios vectoriales más comunes y relevantes para machine learning, mostrando sus características específicas y cómo se manifiestan las operaciones vectoriales en cada uno.\n", "\n", "\n", "| **Espacio Vectorial** | **Elementos** | **Operaciones** | **Ejemplo de Vector** | **Ejemplo de Axiomas** |\n", "|----------------------|---------------|-----------------|---------------------|----------------------|\n", "| R² | Pares ordenados (x,y) | Suma: (x₁,y₁)+(x₂,y₂)=(x₁+x₂,y₁+y₂) <br> Escalar: α(x,y)=(αx,αy) | v=(2,3) | Suma: (2,3)+(1,1)=(3,4) <br> Escalar: 2(2,3)=(4,6) |\n", "| R³ | Tripletas (x,y,z) | Suma: (x₁,y₁,z₁)+(x₂,y₂,z₂)=(x₁+x₂,y₁+y₂,z₁+z₂) <br> Escalar: α(x,y,z)=(αx,αy,αz) | v=(1,2,3) | Suma: (1,2,3)+(1,1,1)=(2,3,4) <br> Escalar: 3(1,2,3)=(3,6,9) |\n", "| Rⁿ | n-tuplas (x₁,...,xₙ) | Suma: Componente a componente <br> Escalar: Multiplicación por cada componente | v=(1,2,...,n) | Suma: Componente a componente <br> Distributividad: α(v+w)=αv+αw |\n", "| P_n[x] (Polinomios) | Polinomios de grado ≤n | Suma: Suma de coeficientes del mismo grado <br> Escalar: Multiplicación de todos los coeficientes | p(x)=x²+2x+1 | Suma: (x²+2x)+(x+1)=x²+3x+1 <br> Escalar: 2(x²+2x)=2x²+4x |\n", "| C[a,b] (Funciones continuas) | Funciones continuas en [a,b] | Suma: (f+g)(x)=f(x)+g(x) <br> Escalar: (αf)(x)=αf(x) | f(x)=sin(x) | Suma: sin(x)+cos(x) <br> Escalar: 2sin(x) |\n", "| M_m×n (Matrices) | Matrices de m×n | Suma: Suma elemento a elemento <br> Escalar: Multiplicación de cada elemento | A=[[1,2],[3,4]] | Suma: Elemento a elemento <br> Escalar: α[[1,2],[3,4]]=[[α,2α],[3α,4α]] |\n", "\n", "*Tabla 2. Comparación de espacios vectoriales comunes y sus propiedades fundamentales*\n", "\n", "#### Notas importantes sobre los espacios vectoriales presentados:\n", "\n", "1. **Dimensionalidad**: R², R³ y Rⁿ tienen dimensión finita igual a 2, 3 y n respectivamente. Son los espacios más intuitivos y frecuentemente utilizados en aplicaciones básicas de machine learning.\n", "\n", "2. **Espacios de polinomios**: P_n[x] es particularmente útil en aproximación de funciones y regresión polinomial. La dimensión de P_n[x] es n+1, correspondiente a los coeficientes del polinomio.\n", "\n", "3. **Espacios de funciones**: C[a,b] es un espacio de dimensión infinita, fundamental en análisis funcional y en la teoría de redes neuronales, donde las funciones de activación son elementos de este espacio.\n", "\n", "4. **Espacios matriciales**: M_m×n es esencial en machine learning para representar transformaciones lineales, datos tabulares y parámetros de modelos. Su dimensión es m×n.\n", "\n", "Estos espacios vectoriales proporcionan el fundamento matemático para muchas aplicaciones en machine learning, desde la representación de datos hasta la optimización de modelos.\n", "\n", "\n", "#### Definición de Subespacio Vectorial\n", "\n", "Un subespacio vectorial es un subconjunto $W$ de un espacio vectorial $V$ que mantiene la estructura algebraica del espacio vectorial original. Formalmente, para que $W$ sea un subespacio vectorial de $V$, debe cumplir tres condiciones fundamentales:\n", "\n", "1. **No vacío**: El vector nulo $\\mathbf{0}$ debe pertenecer a $W$\n", "2. **Cerrado bajo suma**: Si $\\mathbf{u}, \\mathbf{v} \\in W$, entonces $\\mathbf{u} + \\mathbf{v} \\in W$\n", "3. **Ce<PERSON>do bajo multiplicación escalar**: Si $\\mathbf{v} \\in W$ y $\\alpha \\in \\mathbb{R}$, entonces $\\alpha\\mathbf{v} \\in W$\n", "\n", "#### Demostración de Suficiencia\n", "\n", "Para demostrar que estas condiciones son suficientes, debemos verificar que $W$ cumple todos los axiomas de un espacio vectorial:\n", "\n", "1. La asociatividad y conmutatividad de la suma se heredan de $V$, ya que las operaciones son las mismas.\n", "\n", "2. El vector nulo está en $W$ por la primera condición, actuando como elemento neutro.\n", "\n", "3. Para cualquier $\\mathbf{v} \\in W$, su opuesto $-\\mathbf{v}$ existe en $W$ porque:\n", "   - Por la condición 3, si $\\mathbf{v} \\in W$, entonces $(-1)\\mathbf{v} \\in W$\n", "   - Y $(-1)\\mathbf{v} = -\\mathbf{v}$\n", "\n", "4. Las propiedades de distributividad y asociatividad escalar se heredan automáticamente de $V$.\n", "\n", "#### Consecuencia Importante\n", "\n", "Una propiedad fundamental que se deriva de estas condiciones es que cualquier combinación lineal de vectores en $W$ también pertenece a $W$. Es decir, si $\\mathbf{v}_1, \\mathbf{v}_2, ..., \\mathbf{v}_n \\in W$ y $\\alpha_1, \\alpha_2, ..., \\alpha_n \\in \\mathbb{R}$, entonces:\n", "\n", "$$\n", "\\alpha_1\\mathbf{v}_1 + \\alpha_2\\mathbf{v}_2 + ... + \\alpha_n\\mathbf{v}_n \\in W\n", "$$\n", "\n", "Esta propiedad es fundamental para el estudio posterior de bases y dimensiones de subespacios vectoriales.\n", "\n", "\n", "#### Visualización de Subespacios en R³\n", "\n", "Los subespacios vectoriales en R³ proporcionan ejemplos geométricos muy intuitivos de cómo se manifiestan las propiedades algebraicas en el espacio tridimensional. Veamos los tres tipos principales:\n", "\n", "#### 1. El Punto Origen: Subespacio de Dimensión 0\n", "\n", "![Figure](https://mermaid.ink/img/eyJjb2RlIjogIlxuZ3JhcGggTFJcbiAgICBPKCgwKSlcbiAgICBzdHlsZSBPIGZpbGw6I2Y5NixzdHJva2U6IzMzMyxzdHJva2Utd2lkdGg6NHB4XG4iLCAibWVybWFpZCI6IHsidGhlbWUiOiAiZGVmYXVsdCJ9fQ)\n", "\n", "*Diagrama 3. El origen como subespacio vectorial. Este punto representa el vector nulo (0,0,0).*\n", "\n", "El punto origen es el subespacio más simple en R³. Es un subespacio válido porque:\n", "- Con<PERSON><PERSON> el vector nulo (es el propio punto)\n", "- La suma de vectores nulos da el vector nulo\n", "- Cual<PERSON>er escalar multiplicado por el vector nulo da el vector nulo\n", "\n", "#### 2. <PERSON><PERSON><PERSON> por el Origen: Subespacios de Dimensión 1\n", "\n", "![Figure](https://mermaid.ink/img/eyJjb2RlIjogIlxuZ3JhcGggTFJcbiAgICBPKChPKSkgLS0-IHx2fEEoKFx1MDBiNykpXG4gICAgTyAtLT4gfC12fEIoKFx1MDBiNykpXG4gICAgc3R5bGUgTyBmaWxsOiNmOTYsc3Ryb2tlOiMzMzMsc3Ryb2tlLXdpZHRoOjRweFxuICAgIHN0eWxlIEEgZmlsbDojNjlmLHN0cm9rZTojMzMzLHN0cm9rZS13aWR0aDoycHhcbiAgICBzdHlsZSBCIGZpbGw6IzY5ZixzdHJva2U6IzMzMyxzdHJva2Utd2lkdGg6MnB4XG4iLCAibWVybWFpZCI6IHsidGhlbWUiOiAiZGVmYXVsdCJ9fQ)\n", "\n", "*Diagrama 4. <PERSON><PERSON><PERSON> que pasa por el origen como subespacio vectorial. La línea se extiende infinitamente en ambas direcciones.*\n", "\n", "Una línea que pasa por el origen es un subespacio porque:\n", "- Contiene el vector nulo (el origen)\n", "- La suma de dos vectores en la línea permanece en la línea\n", "- Multiplicar cualquier vector de la línea por un escalar produce otro vector en la misma línea\n", "\n", "#### 3. Planos por el Origen: Subespacios de Dimensión 2\n", "\n", "![Figure](https://mermaid.ink/img/eyJjb2RlIjogIlxuZ3JhcGggVERcbiAgICBPKChPKSkgLS0-IHx2XHUyMDgxfEEoKFx1MDBiNykpXG4gICAgTyAtLT4gfHZcdTIwODJ8QigoXHUwMGI3KSlcbiAgICBBIC0tPiB8Y29tYmluYWNpb25lc3xDKChcdTAwYjcpKVxuICAgIEIgLS0-IHxsaW5lYWxlc3xDXG4gICAgc3R5bGUgTyBmaWxsOiNmOTYsc3Ryb2tlOiMzMzMsc3Ryb2tlLXdpZHRoOjRweFxuICAgIHN0eWxlIEEgZmlsbDojNjlmLHN0cm9rZTojMzMzLHN0cm9rZS13aWR0aDoycHhcbiAgICBzdHlsZSBCIGZpbGw6IzY5ZixzdHJva2U6IzMzMyxzdHJva2Utd2lkdGg6MnB4XG4gICAgc3R5bGUgQyBmaWxsOiM2OWYsc3Ryb2tlOiMzMzMsc3Ryb2tlLXdpZHRoOjJweFxuIiwgIm1lcm1haWQiOiB7InRoZW1lIjogImRlZmF1bHQifX0)\n", "\n", "*Diagrama 5. Plano que pasa por el origen como subespacio vectorial. El plano se genera mediante todas las combinaciones lineales posibles de dos vectores linealmente independientes.*\n", "\n", "Un plano que pasa por el origen es un subespacio porque:\n", "- Contiene el vector nulo (el origen)\n", "- La suma de dos vectores en el plano resulta en otro vector en el mismo plano\n", "- Multiplicar cualquier vector del plano por un escalar mantiene el resultado en el plano\n", "\n", "#### Propiedades Comunes de los Subespacios\n", "\n", "![Figure](https://mermaid.ink/img/eyJjb2RlIjogIlxubWluZG1hcFxuICAgIHJvb3QoKFN1YmVzcGFjaW9zIGVuIFJcdTAwYjMpKVxuICAgICAgICBEaW1lbnNpXHUwMGYzbiAwXG4gICAgICAgICAgICBQdW50byBvcmlnZW5cbiAgICAgICAgICAgIFZlY3RvciBudWxvXG4gICAgICAgIERpbWVuc2lcdTAwZjNuIDFcbiAgICAgICAgICAgIExcdTAwZWRuZWFzIHBvciBvcmlnZW5cbiAgICAgICAgICAgIFNwYW4gZGUgdW4gdmVjdG9yXG4gICAgICAgIERpbWVuc2lcdTAwZjNuIDJcbiAgICAgICAgICAgIFBsYW5vcyBwb3Igb3JpZ2VuXG4gICAgICAgICAgICBTcGFuIGRlIGRvcyB2ZWN0b3Jlc1xuICAgICAgICBEaW1lbnNpXHUwMGYzbiAzXG4gICAgICAgICAgICBSXHUwMGIzIGNvbXBsZXRvXG4gICAgICAgICAgICBTcGFuIGRlIHRyZXMgdmVjdG9yZXNcbiIsICJtZXJtYWlkIjogeyJ0aGVtZSI6ICJkZWZhdWx0In19)\n", "\n", "*Diagrama 6. Jerarquía de subespacios en R³ según su dimensión.*\n", "\n", "Todos estos subespacios comparten características fundamentales:\n", "1. Son cerrados bajo operaciones vectoriales\n", "2. <PERSON><PERSON><PERSON> el vector nulo\n", "3. Su dimensión es menor o igual a 3 (la dimensión de R³)\n", "4. Cada uno es generado por combinaciones lineales de sus vectores base\n", "\n", "\n", "#### Verificación de Subespacios Vectoriales: Guía Paso a Paso\n", "\n", "Para determinar si un conjunto es un subespacio vectorial, debemos verificar sistemáticamente tres propiedades fundamentales. A continuación, presentamos el proceso detallado de verificación junto con un ejemplo práctico.\n", "\n", "#### Pasos de Verificación\n", "\n", "1. **Verificación del vector cero**\n", "   - Comprobar si el vector nulo pertenece al conjunto\n", "   - Si el vector nulo no está en el conjunto, podemos concluir inmediatamente que no es un subespacio\n", "\n", "2. **Verificación de cerradura bajo suma**\n", "   - Tomar dos vectores arbitrarios $\\mathbf{u}, \\mathbf{v}$ del conjunto\n", "   - Verificar si su suma $\\mathbf{u} + \\mathbf{v}$ también pertenece al conjunto\n", "   - <PERSON><PERSON> propiedad debe cumplirse para cualquier par de vectores del conjunto\n", "\n", "3. **Verificación de cerradura bajo multiplicación escalar**\n", "   - Tomar un vector arbitrario $\\mathbf{v}$ del conjunto y un escalar $\\alpha$\n", "   - Verificar si el producto $\\alpha\\mathbf{v}$ pertenece al conjunto\n", "   - Esta propiedad debe cumplirse para cualquier vector y cualquier escalar\n", "\n", "#### <PERSON>je<PERSON><PERSON> Práctico\n", "\n", "Consideremos el conjunto $W = \\{(x,y) \\in \\mathbb{R}^2 : x = 2y\\}$ (todos los vectores donde la primera coordenada es el doble de la segunda).\n", "\n", "1. **Verificación del vector cero**:\n", "   - El vector $(0,0)$ satisface la condición $0 = 2(0)$\n", "   - ✓ El vector cero pertenece a $W$\n", "\n", "2. **Verificación de cerradura bajo suma**:\n", "   - Sean $\\mathbf{u} = (2a,a)$ y $\\mathbf{v} = (2b,b)$ dos vectores en $W$\n", "   - $\\mathbf{u} + \\mathbf{v} = (2a+2b,a+b) = (2(a+b),a+b)$\n", "   - Como $2(a+b)$ es el doble de $(a+b)$\n", "   - ✓ La suma pertenece a $W$\n", "\n", "3. **Verificación de cerradura bajo multiplicación escalar**:\n", "   - Sea $\\mathbf{v} = (2a,a)$ un vector en $W$ y $\\alpha$ un escalar\n", "   - $\\alpha\\mathbf{v} = (\\alpha(2a),\\alpha a) = (2(\\alpha a),\\alpha a)$\n", "   - Como $2(\\alpha a)$ es el doble de $\\alpha a$\n", "   - ✓ El producto escalar pertenece a $W$\n", "\n", "Conclusión: Como $W$ cumple las tres propiedades, es un subespacio vectorial de $\\mathbb{R}^2$. Geométricamente, $W$ representa una línea que pasa por el origen con pendiente $\\frac{1}{2}$.\n", "\n", "#### Operaciones con Subespacios Vectoriales\n", "\n", "Dados dos subespacios vectoriales de un mismo espacio vectorial, podemos realizar dos operaciones fundamentales: la intersección y la suma.\n", "\n", "#### Intersección de Subespacios\n", "\n", "La intersección de dos subespacios $U$ y $W$, denotada como $U \\cap W$, es el conjunto de todos los vectores que pertenecen simultáneamente a ambos subespacios:\n", "\n", "$$\n", "U \\cap W = \\{\\mathbf{v} : \\mathbf{v} \\in U \\text{ y } \\mathbf{v} \\in W\\}\n", "$$\n", "\n", "La intersección siempre es un subespacio vectorial. Esto se puede demostrar verificando las tres propiedades fundamentales:\n", "\n", "1. El vector nulo está en $U \\cap W$ porque está en ambos subespacios\n", "2. Si $\\mathbf{u}, \\mathbf{v} \\in U \\cap W$, entonces $\\mathbf{u} + \\mathbf{v} \\in U \\cap W$ porque la suma pertenece a ambos subespacios\n", "3. Si $\\mathbf{v} \\in U \\cap W$ y $\\alpha \\in \\mathbb{R}$, entonces $\\alpha\\mathbf{v} \\in U \\cap W$ porque el producto escalar pertenece a ambos subespacios\n", "\n", "#### Su<PERSON> de Subespacios\n", "\n", "La suma de dos subespacios $U$ y $W$, denotada como $U + W$, es el conjunto de todos los vectores que se pueden expresar como suma de un vector de $U$ y un vector de $W$:\n", "\n", "$$\n", "U + W = \\{\\mathbf{u} + \\mathbf{w} : \\mathbf{u} \\in U \\text{ y } \\mathbf{w} \\in W\\}\n", "$$\n", "\n", "#### Ejemplo Geométrico en R²\n", "\n", "Consideremos dos subespacios en R²:\n", "- $U$: la recta $y = x$ que pasa por el origen\n", "- $W$: el eje $x$ (recta $y = 0$)\n", "\n", "En este caso:\n", "- $U \\cap W$ es el punto origen $(0,0)$\n", "- $U + W$ es todo el plano R², ya que cualquier punto del plano se puede expresar como suma de un punto de $U$ y un punto de $W$\n", "\n", "Este ejemplo ilustra que la intersección puede tener dimensión menor que los subespacios originales, mientras que la suma puede tener dimensión mayor que cada uno de ellos por separado.\n"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display(Markdown(''.join(content.content for content in content[0][0])))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Ideas Evaluaciones\n", "\n", "4 partes\n", "\n", "* 1) Competencias + Indice. Se podrían contar como una ya que son dependientes la una de la otra. \n", "\n", "* 2) Búsqueda fuentes -> Influye al contenido aunque este se puede generar sin. Tiene que sumar y añadir a hacer grounding, que lo que se dice sea fundamentado por hechos.\n", "\n", "* 3) Generación de planes -> Outline de contenido a generar basado en el indice y lo que quiere el docente(Instrucciones didácticas).\n", "\n", "* 4) Generación de contenido -> Asegurarse que se adhiere adecuadamente a los planes y que el resultado final es bueno. Feedback loop para cambiar planes, meter ejemplos en planes si es estructural y al contenido si es un tema de redacción.\n", "\n", "Mejorando cualquiera se mejoran las demás.\n", "\n", "* 1, 2, 4 se puede medir lo bien que van viendo el numero de veces que se regeneran o la cantidad de edición que se hace. Esto segundo de momento no tenemos manera de saberlo. Un versionado algo más detallado quizá ayuda.\n", "\n", "* Otra forma de medir satisfacción es pidiendo breves evaluaciones y feedback después de indice + instrucciones al confirmarlo.\n", "\n", "* El contenido generado por ejemplo diagramas también se puede evaluar por numero de fallos en diagramas o tablas, alucinaciones, falta de cohesión y redacción homogenea, duplicación de los temas hablados y detección de temas similares. "]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}