{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/Code/ia_gestorcontenidosiagen_be/venv/lib/python3.12/site-packages/pydantic/_internal/_fields.py:132: UserWarning: Field \"model_info\" in ProcessMetadata has conflict with protected namespace \"model_\".\n", "\n", "You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.\n", "  warnings.warn(\n", "/Users/<USER>/Code/ia_gestorcontenidosiagen_be/venv/lib/python3.12/site-packages/pydantic/_internal/_fields.py:172: UserWarning: Field name \"schema\" in \"GenerateCompetenciesFromIndexInput\" shadows an attribute in parent \"GenerateCompetenciesInput\"\n", "  warnings.warn(\n", "/Users/<USER>/Code/ia_gestorcontenidosiagen_be/venv/lib/python3.12/site-packages/pydantic/_internal/_fields.py:132: UserWarning: Field \"model_info\" in RegenerateCompetenciesRequest has conflict with protected namespace \"model_\".\n", "\n", "You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.\n", "  warnings.warn(\n", "2025-03-06 17:06:57,814 - logger - INFO - Initial user 'Unir demo' already exists.\n", "/Users/<USER>/Code/ia_gestorcontenidosiagen_be/venv/lib/python3.12/site-packages/pydantic/_internal/_config.py:341: UserWarning: Valid config keys have changed in V2:\n", "* 'allow_population_by_field_name' has been renamed to 'populate_by_name'\n", "* 'smart_union' has been removed\n", "  warnings.warn(message, UserWarning)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Error: Script execution failed with return code 2\n", "Command: ['python', 'scripts/create_prompts.py']\n", "Output: None\n", "Error Message: None\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Library/Frameworks/Python.framework/Versions/3.12/Resources/Python.app/Contents/MacOS/Python: can't open file '/Users/<USER>/Code/ia_gestorcontenidosiagen_be/notebooks/generation_with_sources/scripts/create_prompts.py': [Errno 2] No such file or directory\n"]}], "source": ["import sys\n", "from pathlib import Path\n", "\n", "parent_dir =Path.cwd().parent.parent\n", "sys.path.append(str(parent_dir))\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv()\n", "from src.api.common.dependencies.get_session import get_session\n", "from src.api.common.dependency_container import DependencyContainer\n", "\n", "DependencyContainer.initialize(observability= False)\n", "session = next(get_session())"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from IPython.display import Markdown, display"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from src.api.workflows.sections.add_paragraph.add_paragraph_workflow import (\n", "    AddParagraphRequest,\n", ")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/Code/ia_gestorcontenidosiagen_be/venv/lib/python3.12/site-packages/langchain_core/utils/utils.py:235: UserWarning: WARNING! extra_headers is not default parameter.\n", "                extra_headers was transferred to model_kwargs.\n", "                Please confirm that extra_headers is what you intended.\n", "  warnings.warn(\n", "/Users/<USER>/Code/ia_gestorcontenidosiagen_be/venv/lib/python3.12/site-packages/langchain_core/utils/utils.py:235: UserWarning: WARNING! extra_headers is not default parameter.\n", "                extra_headers was transferred to model_kwargs.\n", "                Please confirm that extra_headers is what you intended.\n", "  warnings.warn(\n", "/Users/<USER>/Code/ia_gestorcontenidosiagen_be/venv/lib/python3.12/site-packages/langchain_core/utils/utils.py:235: UserWarning: WARNING! extra_headers is not default parameter.\n", "                extra_headers was transferred to model_kwargs.\n", "                Please confirm that extra_headers is what you intended.\n", "  warnings.warn(\n"]}], "source": ["content_generator = DependencyContainer().get_content_generator()\n", "add_paragraph_workflow = DependencyContainer().get_add_paragraph_workflow()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["_, content_list = content_generator.get_content_markdown(session, content_plan_version=1, tema_id=3, epigrafe_id=9)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["prev_content = content_list[1].contenido\n", "after_content = content_list[2].contenido"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["request = AddParagraphRequest(prev_position= 2, user_comment= \"Haz una conexión entre los temas anterior y posterior que los referencie, introduce el parrafo posterior al final\", tema_id = 3, epigraph_id= 9)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.anthropic.com/v1/messages \"HTTP/1.1 200 OK\"\n"]}], "source": ["response = await add_paragraph_workflow.execute(request)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/markdown": ["\n", "\n", "#### Modelo de etapas emocionales en el proceso de adaptación al diagnóstico oncológico\n", "\n", "El proceso de adaptación psicológica al diagnóstico de cáncer puede conceptualizarse a través de un modelo de etapas emocionales que, si bien no representa una progresión estrictamente lineal, ofrece un marco comprensivo para entender las reacciones habituales que experimentan los pacientes. Este modelo, inspirado en los trabajos sobre el duelo, identifica seis etapas principales que reflejan la evolución emocional durante el proceso adaptativo:\n", "\n", "1. **Shock inicial y crisis**: Caracterizada por una sensación de irrealidad y aturdimiento, esta primera reacción suele manifestarse inmediatamente después de recibir el diagnóstico. El paciente puede experimentar confusión, dificultad para procesar la información médica y una sensación de desconexión con la realidad. Fisiológicamente, pueden presentarse síntomas como taquicardia, sudoración o sensación de ahogo.\n", "\n", "2. **Negación**: En esta etapa, el paciente puede minimizar la gravedad de su condición, restar importancia al diagnóstico o buscar insistentemente segundas opiniones con la esperanza de encontrar un error. La negación funciona como un mecanismo de protección temporal que permite asimilar gradualmente la realidad amenazante.\n", "\n", "3. **Ira**: Cuando la realidad del diagnóstico comienza a asentarse, pueden surgir sentimientos intensos de frustración, rabia e injusticia. El paciente puede preguntarse \"¿por qué a mí?\" y buscar culpables, dirigiendo su ira hacia el personal sanitario, familiares o incluso hacia sí mismo.\n", "\n", "4. **Negociación**: Esta fase implica intentos de establecer compromisos o promesas (con Dios, con el destino o consigo mismo) para cambiar el curso de la enfermedad. El paciente puede prometer cambios en su estilo de vida o comprometerse a valorar más ciertos aspectos de su existencia si consigue superar la enfermedad.\n", "\n", "5. **Depresión**: A medida que el impacto completo de la enfermedad se hace evidente, pueden aparecer sentimientos de profunda tristeza, desesperanza y desamparo. Esta respuesta refleja el duelo anticipatorio por las pérdidas asociadas al cáncer.\n", "\n", "6. **Aceptación**: Representa la reorganización emocional y cognitiva que permite integrar la enfermedad en la narrativa vital. No implica resignación, sino una adaptación activa que posibilita convivir con la realidad del diagnóstico mientras se mantiene la esperanza realista.\n", "\n", "Es fundamental comprender que estas etapas no son universales ni siguen necesariamente una secuencia fija. Algunos pacientes pueden no experimentar todas las etapas, mientras que otros pueden fluctuar entre ellas o experimentarlas simultáneamente. El reconocimiento de este proceso como dinámico y personalizado resulta esencial para proporcionar un apoyo psicooncológico efectivo y adaptado a las necesidades individuales.\n", "\n", "\n", "<span style=\"color: green;\">\n", "\n", "#### La naturaleza dinámica del proceso adaptativo: Conexión entre etapas emocionales y estrategias de afrontamiento\n", "\n", "El modelo de etapas emocionales presentado anteriormente proporciona un marco conceptual valioso para comprender las reacciones psicológicas ante el diagnóstico de c<PERSON><PERSON>. Sin embargo, es fundamental reconocer que estas etapas no representan un proceso lineal y unidireccional, sino un recorrido dinámico, personalizado y frecuentemente recursivo. Los pacientes pueden experimentar simultáneamente elementos de diferentes etapas, regresar a fases anteriores ante nuevos desafíos clínicos, o incluso omitir completamente alguna de ellas dependiendo de sus características individuales y circunstancias específicas.\n", "\n", "Esta naturaleza fluida del proceso adaptativo se refleja claramente en la experiencia clínica, donde observamos que los pacientes no \"superan\" definitivamente una etapa para pasar a la siguiente, sino que desarrollan un repertorio de respuestas emocionales que pueden activarse en diferentes momentos del recorrido oncológico. Por ejemplo, un paciente que ha alcanzado cierto nivel de aceptación puede experimentar episodios de ira o negación ante una recaída o un cambio en el plan terapéutico.\n", "\n", "Comprender esta complejidad resulta esencial para los profesionales de la psicooncología, ya que permite contextualizar las respuestas emocionales cambiantes del paciente sin interpretarlas necesariamente como retrocesos en el proceso adaptativo. Asimismo, esta perspectiva dinámica facilita la identificación de las estrategias de afrontamiento más adecuadas para cada momento y circunstancia particular, reconociendo que diferentes etapas pueden requerir distintos abordajes de intervención.\n", "\n", "Para ilustrar mejor esta naturaleza no lineal y multidimensional del proceso adaptativo, resulta útil recurrir a representaciones visuales que capturen la interconexión entre las diferentes etapas emocionales y las posibles trayectorias que pueden seguir los pacientes. Estas herramientas gráficas no solo facilitan la comprensión conceptual del proceso, sino que también pueden emplearse en la práctica clínica para ayudar a los pacientes a normalizar sus experiencias emocionales y reconocer la legitimidad de sus reacciones cambiantes a lo largo del tiempo.\n", "\n", "</span>\n", "\n", "\n", "#### Representación visual del proceso de adaptación psicológica al cáncer\n", "\n", "El proceso de adaptación psicológica al diagnóstico de cáncer no sigue una trayectoria lineal predecible, sino que constituye un recorrido dinámico y personalizado donde las diferentes etapas emocionales pueden superponerse, repetirse o incluso omitirse según las características individuales del paciente. La siguiente representación gráfica ilustra este proceso adaptativo, destacando la interconexión entre las diferentes etapas y la posibilidad de movimiento bidireccional entre ellas.\n", "\n", "![Figure](https://mermaid.ink/img/eyJjb2RlIjogIlxuZmxvd2NoYXJ0IFREXG4gICAgQVtcIlNIT0NLIElOSUNJQUxcbiAgICBcdTIwMjIgU2Vuc2FjaVx1MDBmM24gZGUgaXJyZWFsaWRhZFxuICAgIFx1MjAyMiBBdHVyZGltaWVudG8geSBjb25mdXNpXHUwMGYzblxuICAgIFx1MjAyMiBEaWZpY3VsdGFkIHBhcmEgcHJvY2VzYXIgaW5mb3JtYWNpXHUwMGYzblxuICAgIFx1MjAyMiBSZXNwdWVzdGFzIGZpc2lvbFx1MDBmM2dpY2FzIGludGVuc2FzXCJdIFxuICAgIFxuICAgIEJbXCJORUdBQ0lcdTAwZDNOXG4gICAgXHUyMDIyIE1pbmltaXphY2lcdTAwZjNuIGRlIGxhIGdyYXZlZGFkXG4gICAgXHUyMDIyIEJcdTAwZmFzcXVlZGEgZGUgc2VndW5kYXMgb3BpbmlvbmVzXG4gICAgXHUyMDIyIEV2aXRhY2lcdTAwZjNuIGRlIGNvbnZlcnNhY2lvbmVzIHNvYnJlIGxhIGVuZmVybWVkYWRcbiAgICBcdTIwMjIgUG9zdGVyZ2FjaVx1MDBmM24gZGUgZGVjaXNpb25lcyBtXHUwMGU5ZGljYXNcIl1cbiAgICBcbiAgICBDW1wiSVJBXG4gICAgXHUyMDIyIFNlbnRpbWllbnRvcyBkZSBpbmp1c3RpY2lhXG4gICAgXHUyMDIyIFByZWd1bnRhcnNlICdcdTAwYmZwb3IgcXVcdTAwZTkgYSBtXHUwMGVkPydcbiAgICBcdTIwMjIgSG9zdGlsaWRhZCBoYWNpYSBwcm9mZXNpb25hbGVzIG8gZmFtaWxpYXJlc1xuICAgIFx1MjAyMiBGcnVzdHJhY2lcdTAwZjNuIHkgcmVzZW50aW1pZW50b1wiXVxuICAgIFxuICAgIERbXCJORUdPQ0lBQ0lcdTAwZDNOXG4gICAgXHUyMDIyIFByb21lc2FzIG8gcGFjdG9zXG4gICAgXHUyMDIyIEJcdTAwZmFzcXVlZGEgZGUgYWx0ZXJuYXRpdmFzXG4gICAgXHUyMDIyIENvbXByb21pc29zIGRlIGNhbWJpbyBwZXJzb25hbFxuICAgIFx1MjAyMiBSZXBsYW50ZWFtaWVudG8gZGUgcHJpb3JpZGFkZXNcIl1cbiAgICBcbiAgICBFW1wiREVQUkVTSVx1MDBkM05cbiAgICBcdTIwMjIgVHJpc3RlemEgcHJvZnVuZGFcbiAgICBcdTIwMjIgRGVzZXNwZXJhbnphXG4gICAgXHUyMDIyIEFpc2xhbWllbnRvIHNvY2lhbFxuICAgIFx1MjAyMiBEdWVsbyBhbnRpY2lwYXRvcmlvIHBvciBwXHUwMGU5cmRpZGFzXCJdXG4gICAgXG4gICAgRltcIkFDRVBUQUNJXHUwMGQzTlxuICAgIFx1MjAyMiBJbnRlZ3JhY2lcdTAwZjNuIGRlIGxhIGVuZmVybWVkYWQgZW4gbGEgbmFycmF0aXZhIHZpdGFsXG4gICAgXHUyMDIyIFBsYW5pZmljYWNpXHUwMGYzbiByZWFsaXN0YSBkZWwgZnV0dXJvXG4gICAgXHUyMDIyIFJlY29uZXhpXHUwMGYzbiBjb24gdmFsb3JlcyBwZXJzb25hbGVzXG4gICAgXHUyMDIyIEJcdTAwZmFzcXVlZGEgYWN0aXZhIGRlIHNpZ25pZmljYWRvXCJdXG4gICAgXG4gICAgQSA8LS0-IEJcbiAgICBCIDwtLT4gQ1xuICAgIEMgPC0tPiBEXG4gICAgRCA8LS0-IEVcbiAgICBFIDwtLT4gRlxuICAgIEYgPC0uLT4gQVxuICAgIEIgPC0uLT4gRFxuICAgIEMgPC0uLT4gRVxuICAgIEIgPC0uLT4gRVxuICAgIEEgPC0uLT4gRVxuICAgIEMgPC0uLT4gRlxuIiwgIm1lcm1haWQiOiB7InRoZW1lIjogImRlZmF1bHQifX0)\n", "\n", "*Figura 1. Modelo no lineal del proceso de adaptación psicológica al diagnóstico de cáncer. Las flechas bidireccionales representan la naturaleza dinámica y fluctuante del proceso adaptativo, donde el paciente puede moverse entre diferentes estados emocionales en distintos momentos de la enfermedad.*\n", "\n", "Es importante destacar que este modelo no implica que todos los pacientes deban atravesar todas las etapas, ni que exista un orden predeterminado o un tiempo específico para cada fase. Algunos individuos pueden experimentar varias etapas simultáneamente o fluctuar entre ellas en respuesta a diferentes acontecimientos durante el curso de la enfermedad (como recibir resultados de pruebas, iniciar nuevos tratamientos o experimentar recaídas).\n", "\n", "Los profesionales de la psicooncología deben considerar este proceso como una guía orientativa que permite contextualizar las reacciones emocionales del paciente, reconociendo siempre la singularidad de cada experiencia individual. La identificación de la etapa predominante en un momento dado puede ayudar a adaptar las intervenciones psicológicas a las necesidades específicas del paciente, facilitando así un acompañamiento más efectivo durante todo el recorrido oncológico.\n", "\n", "El reconocimiento de este carácter no lineal y personalizado del proceso adaptativo constituye un elemento fundamental para el desarrollo de intervenciones psicooncológicas flexibles y centradas en la persona, que respeten los tiempos individuales y promuevan estrategias de afrontamiento acordes a cada momento del proceso.\n", "\n"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["markdown_with_color = f\"\"\"\n", "{prev_content}\n", "\n", "<span style=\"color: green;\">\n", "{response.content}\n", "</span>\n", "\n", "{after_content}\n", "\"\"\"\n", "\n", "display(Markdown(markdown_with_color))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We need to get the prev content plans and next content plans. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Note: when giving context if there is diagrams, give the n previous and n next.**"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Vale se quiere anadir un párrafo, cual sería el input?\n", "\n", "<PERSON><PERSON><PERSON> falta. Contexto del plan de contenido inicial/ instrucciones iniciales, para que se sepa lo que se va a generar. \n", "\n", "Identificar según la intención del usuario si se quiere generar tabla, diagrama, etc. Esto meter los ejemplos.\n", "\n", "Contexto epígrafes después y temas después. \n", "\n", "Contenido generado alante y atrás del epígrafe. Con rolling window modificable de cuanto atrás y alante mirar como máximo. \n", "\n", "Realmente podría ser relativamente simple. \n", "\n", "Debería almacenar el de generar párrafo? O debería ser estilo confirmar o rechazar.\n", "\n", "<PERSON><PERSON>, para lo de meter párrafo se quieren usar las fuentes que están previas?\n", "\n", "Podríamos dejar realmente la opción de hacer function calling para decidir si busca algo adicional.\n", "\n", "En principio desde el front si se especifica herramienta se podrían anadir capacidades de crear tablas o diagramas. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["Crear distintos test cases y situaciones y ver cómo fuinciona flujo para agregar párrafo.\n", "\n", "Quizá hacer más dinámico el bloque de contexto que se le puede dar sin cambiar el prompt y ver que tal va?\n", "\n", "Otra cosa, para generar se debería crear un contentplanitem extra o directamente tirarle a contenido generado?\n", "\n", "Meter en plan lo que ha puesto el usuario de regenerar y anadir info de que es una solicitud de agregar el párrafo?"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["from ia_gen_core.prompts import OutputFewShot"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["prompt_manager = DependencyContainer.get_prompt_manager()"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["prompt = prompt_manager.get_prompt(name = \"add-paragraph\", prompt_type = \"chat\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now I want to add examples of tools"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["prompt_dinamico_herramientas = \"\"\"\n", "### <PERSON><PERSON> de herramientas\n", "\n", "Tienes a tu disposición herraminetas que tienes la capacidad de usar, en el tag de <reasoning></reasoning> pensarás como usarlas, basandote en la información que se te proporciona sobre las mismas.\n", "Seguirás sus instrucciones de formato.\n", "A continuación de proporcionan instrucciones y guias de uso de las herramientas.\n", "No reevelarás tu metaproceso sobre el uso de herraminetas en el contenido que generes, s<PERSON>lo pensarás en como usarlas en <reasoning> y el contenido las usarás si es adecuado según la consulta de usuario y contexto.\n", "\"\"\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Voy a probar así fuentes incluido y a ver qué tal"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["herramineta_fuentes = \"\"\"\n", "<fuentes_relacionadas>\n", "    Uso de fuentes:\n", "\n", "    -Se te proporcionarán segmentos de texto que pertenecen a documentos académicos relacionadas con el contenido a generar. Las podrás utilizar para escribir sobre el contenido que se te ha pedido.\n", "\n", "    -Cada segmento tendrá un score o puntuación de relevancia, cuanto más alto más probable es que tengan que ver con el contenido a generar. Se te dará el título del documento del que proceden y un pequeño resumen del mismo que te dará una idea general de lo que trata el documento entero y te permitirá saber si es relevante o no. \n", "\n", "    -Primero tendrás en cuenta si son relevantes y razonaras sobre ello en la etiqueta <reasoning>, habrá situaciones donde tu conocimiento implícito sea suficiente para generar contenidos. Úsalas de forma balanceada para escribir sobre un tema o como inspiración.\n", "\n", "    -Si mencionas explícitamente alguna parte de alguno de los documentos reelevantes lo citarás teniendo en cuenta las reglas APA y la inline_citation que aparece junto al documento al cual pertenece.\n", "\n", "    Consideraciones:\n", "\n", "    - Ten en cuenta que a veces alguna de esta información no estará totalmente relacionada al tema a tratar, si es así ignóralo, en el bloque de fuentes incluirás una lista con los números de documento relacionados al tema a tratar.\n", "\n", "    - No asumas que el lector conoce las fuentes proporcionadas, escribirás como si el lector no las conociera en el caso de utilizarlas.\n", "\n", "</fuentes_relacionadas>\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["herramienta_tablas = \"\"\"\n", "<tablas>\n", "        **Herramienta Tablas**: Las tablas se crean utilizando formato markdown y se encierran dentro de las etiquetas <tabla></tabla>. Después de la tabla, se incluye una leyenda concisa junto con el número de tabla. Las tablas son útiles para presentar datos estructurados de manera organizada y fácil de leer.\n", "\n", "        Puedes crear tablas en formato markdown. Cuando necesites crear una tabla la incluirás dentro de las etiquetas <tabla></tabla> y fuera incluirás una leyenda concisa junto al numero de tabla. Uso típico: Las tablas son ideales para presentar datos estructurados, comparar valores entre diferentes categorías o mostrar tendencias a lo largo del tiempo.\n", "\n", "        Las leyendas de las tablas irán en cursiva y proporcionarán una descripción concisa. Al principio de cada tabla irá: *Tabla [x]* (siendo [x] el numero de la tabla). Ej: *Tabla 3. La evolución del comercio en el siglo XX*..\n", "\n", "        Aquí tienes un ejemplo de cómo puedes usarlas.\n", "\n", "        Consideraciones:\n", "        * Usa alineaciones coherentes y mantén el mismo número de columnas en cada fila.\n", "        * Utiliza <br> o viñetas para listas dentro de una misma celda.\n", "        * Rodea la tabla con <tabla></tabla> y añade una leyenda breve al final en cursiva (e.g., *Tabla X. Descripción.*).\n", "        * Destaca encabezados con negritas o separadores :---: para facilitar la lectura.\n", "\n", "        Ejemplos:\n", "        <tabla>\n", "        | **Marca**       | **Q1**  | **Q2**  | **Q3**  | **Q4**  | **Total** |\n", "        |:----------------|--------:|--------:|--------:|--------:|----------:|\n", "        | Samsung         |  22.10% |  20.80% |  19.70% |  19.40% |    20.50% |\n", "        | Apple           |  21.10% |  16.70% |  16.20% |  24.70% |    19.68% |\n", "        | Xiaomi          |  12.70% |  13.90% |  13.40% |  12.90% |    13.23% |\n", "        | OPPO            |   8.80% |   9.60% |  10.00% |   8.80% |     9.30% |\n", "        | Otros           |  35.30% |  39.00% |  40.70% |  34.20% |    37.30% |\n", "        | **Total**       | 100.00% | 100.00% | 100.00% | 100.00% |   100.00% |\n", "        </tabla>\n", "        *Tabla 3. Cuota de Mercado Global de Teléfonos Inteligentes (Samsung, Apple, Xiaomi, OPPO).*\n", "\n", "        <tabla>\n", "        | **País**      | **Características**                        | **Indicadores Clave**               |\n", "        |:-------------:|:------------------------------------------:|:------------------------------------:|\n", "        | **Alemania**  | - Innovación<br>- Alta productividad       | - PIB: 3.8T<br>- Exportaciones: 1.6T |\n", "        | **España**    | - Turismo<br>- Agroindustria               | - PIB: 1.4T<br>- Exportaciones: 0.3T |\n", "        | **Japón**     | - Tecnología<br>- Longevidad               | - PIB: 5.0T<br>- Patentes: 295k      |\n", "        </tabla>\n", "        *Tabla 4. <PERSON><PERSON> y datos clave*\n", "    </tablas>\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["herramienta_diagramas = \"\"\"\n", "<diagramas>\n", "    **Herramienta Diagramas**: Los diagramas se crean utilizando la sintaxis de Mermaid y se encierran dentro de las etiquetas <mermaid></mermaid>. Se pueden crear varios tipos de visualizaciones, como diagramas de flujo, diagramas de secuencia, diagramas de estado, gráficos de pastel, mapas mentales, líneas de tiempo y más. Cada diagrama va acompañado de una leyenda concisa en cursiva utilizando formato markdown.\n", "    Dispones de la capacidad de crear visualizaciones mermaid. Lo usarás cuando sea relevante y se te pida usar gráficos, visualizaciones, lineas de tiempo u otro tipo de ilustraciones. Tendrás en cuenta las limitaciones de esto ya que no permite generar todo tipo de visualizaciones.\n", "\n", "    Las leyendas los diagramas irán en cursiva y proporcionarán una descripción concisa. Al principio de cada gráfico o diagrama irá: *Figura [x] + [Descripción breve]*. Ej: *Figura 5. Tipos de joins en base de datos SQL*.\n", "\n", "    Dentro del contexto de la redacción siempre los mencionarás como figuras.\n", "\n", "    Algunas visualizaciones que se pueden crear con mermaid y puedes usar son:\n", "    <ejemplos diagramas>\n", "        1. Diagramas de flujo. Uso típico: Los diagramas de flujo son excelentes para representar procesos, algoritmos o flujos de trabajo paso a paso. Ejemplo:\n", "        <mermaid>\n", "        flowchart TD\n", "            A[Start] --> B[Decision: Continue?]\n", "            B -- Yes --> <PERSON>[OK]\n", "            C --> D[Rethink]\n", "            D --> B\n", "            B -- No ----> E[End]\n", "        </mermaid>\n", "        Figura 1. *Proceso de toma de decisiones simple. Este diagrama ilustra un proceso básico de toma de decisiones con un bucle de retroalimentación.*\n", "\n", "        2. Recorridos de usuario. Uso típico: Modelado o ilustración de experiencia de usuario.Ejemplo: \n", "        <mermaid>\n", "        journey\n", "            title My working day\n", "            section Go to work\n", "            Make tea: 5: Me\n", "            Go upstairs: 3: Me\n", "            Do work: 1: <PERSON>, <PERSON>\n", "            section Go home\n", "            Go downstairs: 5: Me\n", "            Sit down: 5: Me\n", "        </mermaid>\n", "        Figura 2. *Jornada laboral típica de un empleado.*\n", "\n", "        3. Pie charts. Uso tipico: Proporciones de un todo Ejemplo: \n", "        <mermaid>\n", "        pie title Pets adopted by volunteers in 2023\n", "            \"Dogs\" : 386\n", "            \"Cats\" : 85\n", "            \"Rats\" : 15\n", "        </mermaid>\n", "        Figura 3. *Distribución de mascotas adaptadas por voluntarios en 2023*\n", "\n", "        4. Mapas mentales. Uso típico: Organización de ideas. Ejemplo:\n", "        <mermaid>\n", "        mindmap\n", "        root((Future Tech))\n", "            AI & ML\n", "            NLP\n", "                Natural Language Processing\n", "            Robotics\n", "                Advanced Automation\n", "            Quantum\n", "            Cryptography\n", "                Unbreakable Encryption\n", "            Simulations\n", "                Complex System Modeling\n", "            Bio-Tech\n", "            CRISPR\n", "                Gene Editing\n", "            Bionics\n", "                Human Augmentation\n", "            Green Tech\n", "            Solar\n", "                Advanced Photovoltaics\n", "            Fusion\n", "                Clean Energy Revolution\n", "            XR\n", "            VR/AR\n", "                Immersive Experiences\n", "            BCI\n", "                Brain-Computer Interfaces\n", "            IoT\n", "            Smart Homes\n", "                Automated Living\n", "            Wearables\n", "                Health & Fitness Tracking\n", "        </mermaid>\n", "        Figura 4. *Panorama de tecnologías futuras*. \n", "        5. Lineas de tiempo. Uso tí<PERSON>: mostrar eventos cronológicos. Ejemplo:\n", "        <mermaid>\n", "        timeline\n", "            title La Invención de Internet\n", "            section Orígenes\n", "                1969 : ARPANET establece su primera conexión\n", "                1973 : Desarrollo del protocolo TCP/IP\n", "            section Evolución\n", "                1983 : ARPANET adopta TCP/IP\n", "                1989 : <PERSON>-<PERSON> propone la World Wide Web\n", "            section Expansión\n", "                1991 : La WWW se hace pública\n", "                1993 : Lanzamiento del navegador Mosaic\n", "            section Era Moderna\n", "                1998 : Google es fundado\n", "                2004 : Lanzamiento de Facebook\n", "        </mermaid>\n", "        Figura 5. *Hitos en la historia de Internet*. Adaptado de \"Where Wizards Stay Up Late: The Origins of the Internet\" p<PERSON> <PERSON>, K., & Lyon, M. (1998).\n", "\n", "        6. Graf<PERSON><PERSON> xy. <PERSON><PERSON>: Relaciones numéricas. Ejemplo: \n", "        <mermaid>\n", "        xychart-beta\n", "        title \"Ingresos de Ventas TechCorp Inc 2023\"\n", "        x-axis [ene, feb, mar, abr, may, jun, jul, ago, sep, oct, nov, dic]\n", "        y-axis \"Ingresos (en $)\" 4000 --> 11000\n", "        bar [5000, 6000, 7500, 8200, 9500, 10500, 11000, 10200, 9200, 8500, 7000, 6000]\n", "        line [5000, 6000, 7500, 8200, 9500, 10500, 11000, 10200, 9200, 8500, 7000, 6000]\n", "        </mermaid>\n", "        Figura 6. *Ingresos mensuales de ventas de TechCorp Inc 2023*.\n", "\n", "        <mermaid>\n", "        xychart-beta\n", "        title \"Relacion entre felicidad y desempeño organizacional\"\n", "        x-axis \"Felicidad\" 0 --> 100\n", "        y-axis \"Desempeño organizacional\" 0 --> 100\n", "        line [10,30,45,55,70,80,85,88,90]\n", "        </mermaid>\n", "        Figura 7. *Relacion entre felicidad y desempeño organizacional*.\n", "    </ejemplos diagramas>\n", "\n", "    Limitaciones: \n", "\n", "    - Mantén las visualizaciones básicas y no utilices ningún estilo personalizado mermaid. Utilizar estilos personalizados puede dañar la estética o dificultar la visualización en visores de documentos básicos.\n", "\n", "    - **No creees diagramas demasiado complejos** o con muchos elementos, ya que pueden volverse ilegibles o dar errores.\n", "\n", "    - Ten en cuenta que \"las listas markdown dentro de los diagramas mermaid no están soportadas\". Eso implica que usar dentro del texto de diagramas cosas como [1. Lo que sea] o [- <PERSON><PERSON>], resultará en un error. Manten el formato del texto sencillo cuando uses mermaid y no trates de hacer cosas las cuales su estandar no soporte.\n", "\n", "    - Es importante que lo uses cuando se te pida explicitamente, para evitar sobrecargar el contenido de diagramas.\n", "    - Ten en cuenta que los caracteres especiales hay que escaparlos mediante comillas. \n", "      <PERSON>r e<PERSON><PERSON><PERSON>, el siguiente elemento de un flowchart resultará en error:\n", "        O --> P[Constitución Española (art. 117.3)]\n", "      <PERSON><PERSON><PERSON><PERSON>o:\n", "        O --> P[\"Constitución Española (art. 117.3)\"]\n", "\n", "    - En mindmaps, no utilices NUNCA comillas (\"\"), porque se verán de la siguiente manera:\n", "        &quot;Teoría de la AcciónRazonada y Teoría de laConducta Planeada&quot;\n", "        Evita generaciones que conduzcan al uso de las comillas en mindmaps.\n", "</diagramas>\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 84, "metadata": {}, "outputs": [], "source": ["few_shot_examples = [\n", "    OutputFewShot(output = herramienta_tablas, metadata= {\"herramienta\": \"tablas\"}),\n", "    OutputFewShot(output = herramienta_diagramas, metadata={\"herramienta\": \"diagramas\"}),\n", "]"]}, {"cell_type": "code", "execution_count": 85, "metadata": {}, "outputs": [], "source": ["prompt.few_shot_examples = few_shot_examples"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [], "source": ["prompt.save(prompt_manager)"]}, {"cell_type": "code", "execution_count": 67, "metadata": {}, "outputs": [{"data": {"text/plain": ["[OutputFewShot(text='\\n<tablas>\\n        **Herramienta Tablas**: Las tablas se crean utilizando formato markdown y se encierran dentro de las etiquetas <tabla></tabla>. Después de la tabla, se incluye una leyenda concisa junto con el número de tabla. Las tablas son útiles para presentar datos estructurados de manera organizada y fácil de leer.\\n\\n        Puedes crear tablas en formato markdown. Cuando necesites crear una tabla la incluirás dentro de las etiquetas <tabla></tabla> y fuera incluirás una leyenda concisa junto al numero de tabla. Uso típico: Las tablas son ideales para presentar datos estructurados, comparar valores entre diferentes categorías o mostrar tendencias a lo largo del tiempo.\\n\\n        Las leyendas de las tablas irán en cursiva y proporcionarán una descripción concisa. Al principio de cada tabla irá: *Tabla [x]* (siendo [x] el numero de la tabla). Ej: *Tabla 3. La evolución del comercio en el siglo XX*..\\n\\n        Aquí tienes un ejemplo de cómo puedes usarlas.\\n\\n        Consideraciones:\\n        * Usa alineaciones coherentes y mantén el mismo número de columnas en cada fila.\\n        * Utiliza <br> o viñetas para listas dentro de una misma celda.\\n        * Rodea la tabla con <tabla></tabla> y añade una leyenda breve al final en cursiva (e.g., *Tabla X. Descripción.*).\\n        * Destaca encabezados con negritas o separadores :---: para facilitar la lectura.\\n\\n        Ejemplos:\\n        <tabla>\\n        | **Marca**       | **Q1**  | **Q2**  | **Q3**  | **Q4**  | **Total** |\\n        |:----------------|--------:|--------:|--------:|--------:|----------:|\\n        | Samsung         |  22.10% |  20.80% |  19.70% |  19.40% |    20.50% |\\n        | Apple           |  21.10% |  16.70% |  16.20% |  24.70% |    19.68% |\\n        | Xiaomi          |  12.70% |  13.90% |  13.40% |  12.90% |    13.23% |\\n        | OPPO            |   8.80% |   9.60% |  10.00% |   8.80% |     9.30% |\\n        | Otros           |  35.30% |  39.00% |  40.70% |  34.20% |    37.30% |\\n        | **Total**       | 100.00% | 100.00% | 100.00% | 100.00% |   100.00% |\\n        </tabla>\\n        *Tabla 3. Cuota de Mercado Global de Teléfonos Inteligentes (Samsung, Apple, Xiaomi, OPPO).*\\n\\n        <tabla>\\n        | **País**      | **Características**                        | **Indicadores Clave**               |\\n        |:-------------:|:------------------------------------------:|:------------------------------------:|\\n        | **Alemania**  | - Innovación<br>- Alta productividad       | - PIB: 3.8T<br>- Exportaciones: 1.6T |\\n        | **España**    | - Turismo<br>- Agroindustria               | - PIB: 1.4T<br>- Exportaciones: 0.3T |\\n        | **Japón**     | - Tecnología<br>- Longevidad               | - PIB: 5.0T<br>- Patentes: 295k      |\\n        </tabla>\\n        *Tabla 4. Paises y datos clave*\\n    </tablas>\\n', example_type='OUTPUT', metadata={'herramienta': 'tablas'}, output='\\n<tablas>\\n        **Herramienta Tablas**: Las tablas se crean utilizando formato markdown y se encierran dentro de las etiquetas <tabla></tabla>. Después de la tabla, se incluye una leyenda concisa junto con el número de tabla. Las tablas son útiles para presentar datos estructurados de manera organizada y fácil de leer.\\n\\n        Puedes crear tablas en formato markdown. Cuando necesites crear una tabla la incluirás dentro de las etiquetas <tabla></tabla> y fuera incluirás una leyenda concisa junto al numero de tabla. Uso típico: Las tablas son ideales para presentar datos estructurados, comparar valores entre diferentes categorías o mostrar tendencias a lo largo del tiempo.\\n\\n        Las leyendas de las tablas irán en cursiva y proporcionarán una descripción concisa. Al principio de cada tabla irá: *Tabla [x]* (siendo [x] el numero de la tabla). Ej: *Tabla 3. La evolución del comercio en el siglo XX*..\\n\\n        Aquí tienes un ejemplo de cómo puedes usarlas.\\n\\n        Consideraciones:\\n        * Usa alineaciones coherentes y mantén el mismo número de columnas en cada fila.\\n        * Utiliza <br> o viñetas para listas dentro de una misma celda.\\n        * Rodea la tabla con <tabla></tabla> y añade una leyenda breve al final en cursiva (e.g., *Tabla X. Descripción.*).\\n        * Destaca encabezados con negritas o separadores :---: para facilitar la lectura.\\n\\n        Ejemplos:\\n        <tabla>\\n        | **Marca**       | **Q1**  | **Q2**  | **Q3**  | **Q4**  | **Total** |\\n        |:----------------|--------:|--------:|--------:|--------:|----------:|\\n        | Samsung         |  22.10% |  20.80% |  19.70% |  19.40% |    20.50% |\\n        | Apple           |  21.10% |  16.70% |  16.20% |  24.70% |    19.68% |\\n        | Xiaomi          |  12.70% |  13.90% |  13.40% |  12.90% |    13.23% |\\n        | OPPO            |   8.80% |   9.60% |  10.00% |   8.80% |     9.30% |\\n        | Otros           |  35.30% |  39.00% |  40.70% |  34.20% |    37.30% |\\n        | **Total**       | 100.00% | 100.00% | 100.00% | 100.00% |   100.00% |\\n        </tabla>\\n        *Tabla 3. Cuota de Mercado Global de Teléfonos Inteligentes (Samsung, Apple, Xiaomi, OPPO).*\\n\\n        <tabla>\\n        | **País**      | **Características**                        | **Indicadores Clave**               |\\n        |:-------------:|:------------------------------------------:|:------------------------------------:|\\n        | **Alemania**  | - Innovación<br>- Alta productividad       | - PIB: 3.8T<br>- Exportaciones: 1.6T |\\n        | **España**    | - Turismo<br>- Agroindustria               | - PIB: 1.4T<br>- Exportaciones: 0.3T |\\n        | **Japón**     | - Tecnología<br>- Longevidad               | - PIB: 5.0T<br>- Patentes: 295k      |\\n        </tabla>\\n        *Tabla 4. Paises y datos clave*\\n    </tablas>\\n'),\n", " OutputFewShot(text='\\n<fuentes_relacionadas>\\n    Uso de fuentes:\\n\\n    -Se te proporcionarán segmentos de texto que pertenecen a documentos académicos relacionadas con el contenido a generar. Las podrás utilizar para escribir sobre el contenido que se te ha pedido.\\n\\n    -Cada segmento tendrá un score o puntuación de relevancia, cuanto más alto más probable es que tengan que ver con el contenido a generar. Se te dará el título del documento del que proceden y un pequeño resumen del mismo que te dará una idea general de lo que trata el documento entero y te permitirá saber si es relevante o no. \\n\\n    -Primero tendrás en cuenta si son relevantes y razonaras sobre ello en la etiqueta <reasoning>, habrá situaciones donde tu conocimiento implícito sea suficiente para generar contenidos. Úsalas de forma balanceada para escribir sobre un tema o como inspiración.\\n\\n    -Si mencionas explícitamente alguna parte de alguno de los documentos reelevantes lo citarás teniendo en cuenta las reglas APA y la inline_citation que aparece junto al documento al cual pertenece.\\n\\n    Consideraciones:\\n\\n    - Ten en cuenta que a veces alguna de esta información no estará totalmente relacionada al tema a tratar, si es así ignóralo, en el bloque de fuentes incluirás una lista con los números de documento relacionados al tema a tratar.\\n\\n    - No asumas que el lector conoce las fuentes proporcionadas, escribirás como si el lector no las conociera en el caso de utilizarlas.\\n\\n</fuentes_relacionadas>\\n', example_type='OUTPUT', metadata={'herramienta': 'fuentes'}, output='\\n<fuentes_relacionadas>\\n    Uso de fuentes:\\n\\n    -Se te proporcionarán segmentos de texto que pertenecen a documentos académicos relacionadas con el contenido a generar. Las podrás utilizar para escribir sobre el contenido que se te ha pedido.\\n\\n    -Cada segmento tendrá un score o puntuación de relevancia, cuanto más alto más probable es que tengan que ver con el contenido a generar. Se te dará el título del documento del que proceden y un pequeño resumen del mismo que te dará una idea general de lo que trata el documento entero y te permitirá saber si es relevante o no. \\n\\n    -Primero tendrás en cuenta si son relevantes y razonaras sobre ello en la etiqueta <reasoning>, habrá situaciones donde tu conocimiento implícito sea suficiente para generar contenidos. Úsalas de forma balanceada para escribir sobre un tema o como inspiración.\\n\\n    -Si mencionas explícitamente alguna parte de alguno de los documentos reelevantes lo citarás teniendo en cuenta las reglas APA y la inline_citation que aparece junto al documento al cual pertenece.\\n\\n    Consideraciones:\\n\\n    - Ten en cuenta que a veces alguna de esta información no estará totalmente relacionada al tema a tratar, si es así ignóralo, en el bloque de fuentes incluirás una lista con los números de documento relacionados al tema a tratar.\\n\\n    - No asumas que el lector conoce las fuentes proporcionadas, escribirás como si el lector no las conociera en el caso de utilizarlas.\\n\\n</fuentes_relacionadas>\\n'),\n", " OutputFewShot(text='\\n<diagramas>\\n    **Herramienta Diagramas**: Los diagramas se crean utilizando la sintaxis de Mermaid y se encierran dentro de las etiquetas <mermaid></mermaid>. Se pueden crear varios tipos de visualizaciones, como diagramas de flujo, diagramas de secuencia, diagramas de estado, gráficos de pastel, mapas mentales, líneas de tiempo y más. Cada diagrama va acompañado de una leyenda concisa en cursiva utilizando formato markdown.\\n    Dispones de la capacidad de crear visualizaciones mermaid. Lo usarás cuando sea relevante y se te pida usar gráficos, visualizaciones, lineas de tiempo u otro tipo de ilustraciones. Tendrás en cuenta las limitaciones de esto ya que no permite generar todo tipo de visualizaciones.\\n\\n    Las leyendas los diagramas irán en cursiva y proporcionarán una descripción concisa. Al principio de cada gráfico o diagrama irá: *Figura [x] + [Descripción breve]*. Ej: *Figura 5. Tip<PERSON> de joins en base de datos SQL*.\\n\\n    Dentro del contexto de la redacción siempre los mencionarás como figuras.\\n\\n    Algunas visualizaciones que se pueden crear con mermaid y puedes usar son:\\n    <ejemplos diagramas>\\n        1. Diagramas de flujo. Uso típico: Los diagramas de flujo son excelentes para representar procesos, algoritmos o flujos de trabajo paso a paso. Ejemplo:\\n        <mermaid>\\n        flowchart TD\\n            A[Start] --> B[Decision: Continue?]\\n            B -- Yes --> C[OK]\\n            C --> D[Rethink]\\n            D --> B\\n            B -- No ----> E[End]\\n        </mermaid>\\n        Figura 1. *Proceso de toma de decisiones simple. Este diagrama ilustra un proceso básico de toma de decisiones con un bucle de retroalimentación.*\\n\\n        2. Recorridos de usuario. Uso típico: Modelado o ilustración de experiencia de usuario.Ejemplo: \\n        <mermaid>\\n        journey\\n            title My working day\\n            section Go to work\\n            Make tea: 5: Me\\n            Go upstairs: 3: Me\\n            Do work: 1: Me, Cat\\n            section Go home\\n            Go downstairs: 5: Me\\n            Sit down: 5: Me\\n        </mermaid>\\n        Figura 2. *Jornada laboral típica de un empleado.*\\n\\n        3. Pie charts. Uso tipico: Proporciones de un todo Ejemplo: \\n        <mermaid>\\n        pie title Pets adopted by volunteers in 2023\\n            \"Dogs\" : 386\\n            \"Cats\" : 85\\n            \"Rats\" : 15\\n        </mermaid>\\n        Figura 3. *Distribución de mascotas adaptadas por voluntarios en 2023*\\n\\n        4. Mapas mentales. Uso típico: Organización de ideas. Ejemplo:\\n        <mermaid>\\n        mindmap\\n        root((Future Tech))\\n            AI & ML\\n            NLP\\n                Natural Language Processing\\n            Robotics\\n                Advanced Automation\\n            Quantum\\n            Cryptography\\n                Unbreakable Encryption\\n            Simulations\\n                Complex System Modeling\\n            Bio-Tech\\n            CRISPR\\n                Gene Editing\\n            Bionics\\n                Human Augmentation\\n            Green Tech\\n            Solar\\n                Advanced Photovoltaics\\n            Fusion\\n                Clean Energy Revolution\\n            XR\\n            VR/AR\\n                Immersive Experiences\\n            BCI\\n                Brain-Computer Interfaces\\n            IoT\\n            Smart Homes\\n                Automated Living\\n            Wearables\\n                Health & Fitness Tracking\\n        </mermaid>\\n        Figura 4. *Panorama de tecnologías futuras*. \\n        5. Lineas de tiempo. Uso típico: mostrar eventos cronológicos. Ejemplo:\\n        <mermaid>\\n        timeline\\n            title La Invención de Internet\\n            section Orígenes\\n                1969 : ARPANET establece su primera conexión\\n                1973 : Desarrollo del protocolo TCP/IP\\n            section Evolución\\n                1983 : ARPANET adopta TCP/IP\\n                1989 : Tim Berners-Lee propone la World Wide Web\\n            section Expansión\\n                1991 : La WWW se hace pública\\n                1993 : Lanzamiento del navegador Mosaic\\n            section Era Moderna\\n                1998 : Google es fundado\\n                2004 : Lanzamiento de Facebook\\n        </mermaid>\\n        Figura 5. *Hitos en la historia de Internet*. Adaptado de \"Where Wizards Stay Up Late: The Origins of the Internet\" por Hafner, K., & Lyon, M. (1998).\\n\\n        6. Graficas xy. Uso típico: Relaciones numéricas. Ejemplo: \\n        <mermaid>\\n        xychart-beta\\n        title \"Ingresos de Ventas TechCorp Inc 2023\"\\n        x-axis [ene, feb, mar, abr, may, jun, jul, ago, sep, oct, nov, dic]\\n        y-axis \"Ingresos (en $)\" 4000 --> 11000\\n        bar [5000, 6000, 7500, 8200, 9500, 10500, 11000, 10200, 9200, 8500, 7000, 6000]\\n        line [5000, 6000, 7500, 8200, 9500, 10500, 11000, 10200, 9200, 8500, 7000, 6000]\\n        </mermaid>\\n        Figura 6. *Ingresos mensuales de ventas de TechCorp Inc 2023*.\\n\\n        <mermaid>\\n        xychart-beta\\n        title \"Relacion entre felicidad y desempeño organizacional\"\\n        x-axis \"Felicidad\" 0 --> 100\\n        y-axis \"Desempeño organizacional\" 0 --> 100\\n        line [10,30,45,55,70,80,85,88,90]\\n        </mermaid>\\n        Figura 7. *Relacion entre felicidad y desempeño organizacional*.\\n    </ejemplos diagramas>\\n\\n    Limitaciones: \\n\\n    - Mantén las visualizaciones básicas y no utilices ningún estilo personalizado mermaid. Utilizar estilos personalizados puede dañar la estética o dificultar la visualización en visores de documentos básicos.\\n\\n    - **No creees diagramas demasiado complejos** o con muchos elementos, ya que pueden volverse ilegibles o dar errores.\\n\\n    - Ten en cuenta que \"las listas markdown dentro de los diagramas mermaid no están soportadas\". Eso implica que usar dentro del texto de diagramas cosas como [1. Lo que sea] o [- Hola], resultará en un error. Manten el formato del texto sencillo cuando uses mermaid y no trates de hacer cosas las cuales su estandar no soporte.\\n\\n    - Es importante que lo uses cuando se te pida explicitamente, para evitar sobrecargar el contenido de diagramas.\\n    - Ten en cuenta que los caracteres especiales hay que escaparlos mediante comillas. \\n      Por ejemplo, el siguiente elemento de un flowchart resultará en error:\\n        O --> P[Constitución Española (art. 117.3)]\\n      Ejemplo correcto:\\n        O --> P[\"Constitución Española (art. 117.3)\"]\\n\\n    - En mindmaps, no utilices NUNCA comillas (\"\"), porque se verán de la siguiente manera:\\n        &quot;Teoría de la AcciónRazonada y Teoría de laConducta Planeada&quot;\\n        Evita generaciones que conduzcan al uso de las comillas en mindmaps.\\n</diagramas>\\n', example_type='OUTPUT', metadata={'herramienta': 'diagramas'}, output='\\n<diagramas>\\n    **Herramienta Diagramas**: Los diagramas se crean utilizando la sintaxis de Mermaid y se encierran dentro de las etiquetas <mermaid></mermaid>. Se pueden crear varios tipos de visualizaciones, como diagramas de flujo, diagramas de secuencia, diagramas de estado, gráficos de pastel, mapas mentales, líneas de tiempo y más. Cada diagrama va acompañado de una leyenda concisa en cursiva utilizando formato markdown.\\n    Dispones de la capacidad de crear visualizaciones mermaid. Lo usarás cuando sea relevante y se te pida usar gráficos, visualizaciones, lineas de tiempo u otro tipo de ilustraciones. Tendrás en cuenta las limitaciones de esto ya que no permite generar todo tipo de visualizaciones.\\n\\n    Las leyendas los diagramas irán en cursiva y proporcionarán una descripción concisa. Al principio de cada gráfico o diagrama irá: *Figura [x] + [Descripción breve]*. Ej: *Figura 5. Tipos de joins en base de datos SQL*.\\n\\n    Dentro del contexto de la redacción siempre los mencionarás como figuras.\\n\\n    Algunas visualizaciones que se pueden crear con mermaid y puedes usar son:\\n    <ejemplos diagramas>\\n        1. Diagramas de flujo. Uso típico: Los diagramas de flujo son excelentes para representar procesos, algoritmos o flujos de trabajo paso a paso. Ejemplo:\\n        <mermaid>\\n        flowchart TD\\n            A[Start] --> B[Decision: Continue?]\\n            B -- Yes --> C[OK]\\n            C --> D[Rethink]\\n            D --> B\\n            B -- No ----> E[End]\\n        </mermaid>\\n        Figura 1. *Proceso de toma de decisiones simple. Este diagrama ilustra un proceso básico de toma de decisiones con un bucle de retroalimentación.*\\n\\n        2. Recorridos de usuario. Uso típico: Modelado o ilustración de experiencia de usuario.Ejemplo: \\n        <mermaid>\\n        journey\\n            title My working day\\n            section Go to work\\n            Make tea: 5: Me\\n            Go upstairs: 3: Me\\n            Do work: 1: Me, Cat\\n            section Go home\\n            Go downstairs: 5: Me\\n            Sit down: 5: Me\\n        </mermaid>\\n        Figura 2. *Jornada laboral típica de un empleado.*\\n\\n        3. Pie charts. Uso tipico: Proporciones de un todo Ejemplo: \\n        <mermaid>\\n        pie title Pets adopted by volunteers in 2023\\n            \"Dogs\" : 386\\n            \"Cats\" : 85\\n            \"Rats\" : 15\\n        </mermaid>\\n        Figura 3. *Distribución de mascotas adaptadas por voluntarios en 2023*\\n\\n        4. Mapas mentales. Uso típico: Organización de ideas. Ejemplo:\\n        <mermaid>\\n        mindmap\\n        root((Future Tech))\\n            AI & ML\\n            NLP\\n                Natural Language Processing\\n            Robotics\\n                Advanced Automation\\n            Quantum\\n            Cryptography\\n                Unbreakable Encryption\\n            Simulations\\n                Complex System Modeling\\n            Bio-Tech\\n            CRISPR\\n                Gene Editing\\n            Bionics\\n                Human Augmentation\\n            Green Tech\\n            Solar\\n                Advanced Photovoltaics\\n            Fusion\\n                Clean Energy Revolution\\n            XR\\n            VR/AR\\n                Immersive Experiences\\n            BCI\\n                Brain-Computer Interfaces\\n            IoT\\n            Smart Homes\\n                Automated Living\\n            Wearables\\n                Health & Fitness Tracking\\n        </mermaid>\\n        Figura 4. *Panorama de tecnologías futuras*. \\n        5. Lineas de tiempo. Uso típico: mostrar eventos cronológicos. Ejemplo:\\n        <mermaid>\\n        timeline\\n            title La Invención de Internet\\n            section Orígenes\\n                1969 : ARPANET establece su primera conexión\\n                1973 : Desarrollo del protocolo TCP/IP\\n            section Evolución\\n                1983 : ARPANET adopta TCP/IP\\n                1989 : Tim Berners-Lee propone la World Wide Web\\n            section Expansión\\n                1991 : La WWW se hace pública\\n                1993 : Lanzamiento del navegador Mosaic\\n            section Era Moderna\\n                1998 : Google es fundado\\n                2004 : Lanzamiento de Facebook\\n        </mermaid>\\n        Figura 5. *Hitos en la historia de Internet*. Adaptado de \"Where Wizards Stay Up Late: The Origins of the Internet\" por Hafner, K., & Lyon, M. (1998).\\n\\n        6. Graficas xy. Uso típico: Relaciones numéricas. Ejemplo: \\n        <mermaid>\\n        xychart-beta\\n        title \"Ingresos de Ventas TechCorp Inc 2023\"\\n        x-axis [ene, feb, mar, abr, may, jun, jul, ago, sep, oct, nov, dic]\\n        y-axis \"Ingresos (en $)\" 4000 --> 11000\\n        bar [5000, 6000, 7500, 8200, 9500, 10500, 11000, 10200, 9200, 8500, 7000, 6000]\\n        line [5000, 6000, 7500, 8200, 9500, 10500, 11000, 10200, 9200, 8500, 7000, 6000]\\n        </mermaid>\\n        Figura 6. *Ingresos mensuales de ventas de TechCorp Inc 2023*.\\n\\n        <mermaid>\\n        xychart-beta\\n        title \"Relacion entre felicidad y desempeño organizacional\"\\n        x-axis \"Felicidad\" 0 --> 100\\n        y-axis \"Desempeño organizacional\" 0 --> 100\\n        line [10,30,45,55,70,80,85,88,90]\\n        </mermaid>\\n        Figura 7. *Relacion entre felicidad y desempeño organizacional*.\\n    </ejemplos diagramas>\\n\\n    Limitaciones: \\n\\n    - Mantén las visualizaciones básicas y no utilices ningún estilo personalizado mermaid. Utilizar estilos personalizados puede dañar la estética o dificultar la visualización en visores de documentos básicos.\\n\\n    - **No creees diagramas demasiado complejos** o con muchos elementos, ya que pueden volverse ilegibles o dar errores.\\n\\n    - Ten en cuenta que \"las listas markdown dentro de los diagramas mermaid no están soportadas\". Eso implica que usar dentro del texto de diagramas cosas como [1. Lo que sea] o [- Hola], resultará en un error. Manten el formato del texto sencillo cuando uses mermaid y no trates de hacer cosas las cuales su estandar no soporte.\\n\\n    - Es importante que lo uses cuando se te pida explicitamente, para evitar sobrecargar el contenido de diagramas.\\n    - Ten en cuenta que los caracteres especiales hay que escaparlos mediante comillas. \\n      Por ejemplo, el siguiente elemento de un flowchart resultará en error:\\n        O --> P[Constitución Española (art. 117.3)]\\n      Ejemplo correcto:\\n        O --> P[\"Constitución Española (art. 117.3)\"]\\n\\n    - En mindmaps, no utilices NUNCA comillas (\"\"), porque se verán de la siguiente manera:\\n        &quot;Teoría de la AcciónRazonada y Teoría de laConducta Planeada&quot;\\n        Evita generaciones que conduzcan al uso de las comillas en mindmaps.\\n</diagramas>\\n'),\n", " OutputFewShot(text='\\n### Uso de herramientas\\n\\nTienes a tu disposición herraminetas que tienes la capacidad de usar, en el tag de <reasoning></reasoning> pensarás como usarlas, basandote en la información que se te proporciona sobre las mismas.\\nSeguirás sus instrucciones de formato.\\nA continuación de proporcionan instrucciones y guias de uso de las herramientas.\\nNo reevelarás tu metaproceso sobre el uso de herraminetas en el contenido que generes, sólo pensarás en como usarlas en <reasoning> y el contenido las usarás si es adecuado según la consulta de usuario y contexto.\\n', example_type='OUTPUT', metadata={'herramienta': 'descripcion_herramientas'}, output='\\n### Uso de herramientas\\n\\nTienes a tu disposición herraminetas que tienes la capacidad de usar, en el tag de <reasoning></reasoning> pensarás como usarlas, basandote en la información que se te proporciona sobre las mismas.\\nSeguirás sus instrucciones de formato.\\nA continuación de proporcionan instrucciones y guias de uso de las herramientas.\\nNo reevelarás tu metaproceso sobre el uso de herraminetas en el contenido que generes, sólo pensarás en como usarlas en <reasoning> y el contenido las usarás si es adecuado según la consulta de usuario y contexto.\\n')]"]}, "execution_count": 67, "metadata": {}, "output_type": "execute_result"}], "source": ["prompt.few_shot_examples"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [], "source": ["prompt = prompt_manager.get_prompt('add-paragraph', 'chat')"]}, {"cell_type": "code", "execution_count": 80, "metadata": {}, "outputs": [], "source": ["few_shot_herramientas = ''.join([p.text for p in prompt.compile().filter(herramienta = ['fuentes']).few_shot_examples])"]}, {"cell_type": "code", "execution_count": 83, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "### <PERSON><PERSON> de herramientas\n", "\n", "Tienes a tu disposición herraminetas que tienes la capacidad de usar, en el tag de <reasoning></reasoning> pensarás como usarlas, basandote en la información que se te proporciona sobre las mismas.\n", "Seguirás sus instrucciones de formato.\n", "A continuación de proporcionan instrucciones y guias de uso de las herramientas.\n", "No reevelarás tu metaproceso sobre el uso de herraminetas en el contenido que generes, s<PERSON>lo pensarás en como usarlas en <reasoning> y el contenido las usarás si es adecuado según la consulta de usuario y contexto.\n", "\n", "<fuentes_relacionadas>\n", "    Uso de fuentes:\n", "\n", "    -Se te proporcionarán segmentos de texto que pertenecen a documentos académicos relacionadas con el contenido a generar. Las podrás utilizar para escribir sobre el contenido que se te ha pedido.\n", "\n", "    -Cada segmento tendrá un score o puntuación de relevancia, cuanto más alto más probable es que tengan que ver con el contenido a generar. Se te dará el título del documento del que proceden y un pequeño resumen del mismo que te dará una idea general de lo que trata el documento entero y te permitirá saber si es relevante o no. \n", "\n", "    -Primero tendrás en cuenta si son relevantes y razonaras sobre ello en la etiqueta <reasoning>, habrá situaciones donde tu conocimiento implícito sea suficiente para generar contenidos. Úsalas de forma balanceada para escribir sobre un tema o como inspiración.\n", "\n", "    -Si mencionas explícitamente alguna parte de alguno de los documentos reelevantes lo citarás teniendo en cuenta las reglas APA y la inline_citation que aparece junto al documento al cual pertenece.\n", "\n", "    Consideraciones:\n", "\n", "    - Ten en cuenta que a veces alguna de esta información no estará totalmente relacionada al tema a tratar, si es así ignóralo, en el bloque de fuentes incluirás una lista con los números de documento relacionados al tema a tratar.\n", "\n", "    - No asumas que el lector conoce las fuentes proporcionadas, escribirás como si el lector no las conociera en el caso de utilizarlas.\n", "\n", "</fuentes_relacionadas>\n", "\n"]}], "source": ["print(prompt_dinamico_herramientas + few_shot_herramientas)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Se metería esto al final del system prompt."]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}