{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/Library/CloudStorage/OneDrive-UNIR/Code/cont-gen-poc/venv/lib/python3.12/site-packages/pydantic/_internal/_config.py:334: UserWarning: Valid config keys have changed in V2:\n", "* 'allow_population_by_field_name' has been renamed to 'populate_by_name'\n", "* 'smart_union' has been removed\n", "  warnings.warn(message, UserWarning)\n", "/Users/<USER>/Library/CloudStorage/OneDrive-UNIR/Code/cont-gen-poc/venv/lib/python3.12/site-packages/pydantic/_internal/_fields.py:200: UserWarning: Field name \"schema\" in \"GenerateCompetenciesFromIndexInput\" shadows an attribute in parent \"GenerateCompetenciesInput\"\n", "  warnings.warn(\n", "/Users/<USER>/Library/CloudStorage/OneDrive-UNIR/Code/cont-gen-poc/venv/lib/python3.12/site-packages/pydantic/_internal/_fields.py:160: UserWarning: Field \"model_info\" has conflict with protected namespace \"model_\".\n", "\n", "You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.\n", "  warnings.warn(\n", "2024-10-24 11:50:29,028 - logger - INFO - Initial user 'Unir demo' already exists.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Phoenix endpoint reachable after 1 attempts.\n", "Instrument successful\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/Library/CloudStorage/OneDrive-UNIR/Code/cont-gen-poc/venv/lib/python3.12/site-packages/sentence_transformers/cross_encoder/CrossEncoder.py:11: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from tqdm.autonotebook import tqdm, trange\n", "2024-10-24 11:50:38,470 - logger - INFO - Include document router is true\n", "INFO:logger:Include document router is true\n", "2024-10-24 11:50:38,473 - logger - INFO - Entered inside include document router and search engine\n", "INFO:logger:Entered inside include document router and search engine\n"]}], "source": ["import sys\n", "from pathlib import Path\n", "\n", "parent_dir =Path.cwd().parent.parent\n", "sys.path.append(str(parent_dir))\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv()\n", "from src.api.common.dependencies.get_session import get_session\n", "from src.api.common.dependency_container import DependencyContainer\n", "\n", "DependencyContainer.initialize(observability= True, include_document_router=True)\n", "from langchain.output_parsers import YamlOutputParser\n", "from langchain_core.prompts import PromptTemplate\n", "\n", "session = next(get_session())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Indice numero 35 es didáctica de las matemáticas. "]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["search_engine = DependencyContainer.get_search_engine()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["prompt_search = \"\"\"You are tasked with generating a list of search queries to find useful external or academic information related to a specific educational subject and topic. These queries will be used in a search engine to gather high-quality, relevant information. Keep into account this search engine will only search in academic repositories.\n", "\n", "You will be provided with the following context:\n", "\n", "<subject_name>\n", "{SUBJECT_NAME}\n", "</subject_name>\n", "\n", "<content_block>\n", "{CONTENT_BLOCK}\n", "</content_block>\n", "\n", "<current_topic>\n", "{CURRENT_TOPIC}\n", "</current_topic>\n", "\n", "<subtopics>\n", "{SUBTOPICS}\n", "</subtopics>\n", "\n", "Using this information, generate a list of 5-10 search queries that would be useful for finding external and academic sources related to the subject matter. Follow these guidelines:\n", "\n", "1. Create queries in either Spanish or English, whichever is more appropriate for the subject.\n", "2. Make the queries concise and specific to get relevant results from a search engine that searches in academic repositories.\n", "3. Focus on academic or reliable sources by including terms like \"research\", \"study\", \"journal\", or \"academic\" where appropriate.\n", "4. Consider including the subject name, content block, or specific subtopics in the queries.\n", "5. Aim for a mix of broad and specific queries to cover different aspects of the topic.\n", "6. Do not search for really general terms that could be already answered by an artificial intelligence like yourself.\n", "\n", "Present your list of queries in the following format (following the YAML standard for output):\n", "\n", "<search_queries>\n", "search_queries:\n", " - [Query 1]\n", " - [Query 2]\n", " - [Query 3]\n", "...\n", "</search_queries>\n", "\n", "Here are two examples to guide you:\n", "\n", "Example 1:\n", "<subject_name>Biology</subject_name>\n", "<content_block>Genetics</content_block>\n", "<current_topic>Mendelian Inheritance</current_topic>\n", "<subtopics>\n", "- Dominant and recessive alleles\n", "- Punnett squares\n", "- Monohybrid and dihybrid crosses\n", "</subtopics>\n", "\n", "<search_queries>\n", "search_queries:\n", "  - \"Mendelian inheritance patterns research\"\n", "  - \"Dominant and recessive alleles examples biology\"\n", "  - \"Punnett square applications in genetics\"\n", "  - \"Monohybrid vs dihybrid crosses study\"\n", "  - \"Modern applications of Mendelian genetics\"\n", "</search_queries>\n", "\n", "Example 2:\n", "<subject_name>Historia de España</subject_name>\n", "<content_block>Edad Moderna</content_block>\n", "<current_topic>El Siglo de Oro</current_topic>\n", "<subtopics>\n", "- Literatura del Siglo de Oro\n", "- Arte barroco español\n", "- Economía y sociedad en la España del siglo XVII\n", "</subtopics>\n", "\n", "<search_queries>\n", "search_queries:\n", "  - \"Siglo de Oro español características principales\"\n", "  - \"Autores más importantes literatura Siglo de Oro\"\n", "  - \"Arte barroco español pintores y obras destacadas\"\n", "  - \"Economía España siglo XVII crisis y consecuencias\"\n", "  - \"Sociedad española Siglo de Oro clases sociales\"\n", "  - \"Influencia Siglo de Oro cultura española actual\"\n", "</search_queries>\n", "\n", "Remember to tailor your queries to the specific subject and topic provided, and aim for a balance between breadth and depth in your search terms.\n", "Output xml as instructed: \"\"\"\n", "\n", "prompt_template = PromptTemplate.from_template(prompt_search)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from pydantic import BaseModel\n", "\n", "\n", "class ProposedSearch(BaseModel):\n", "    search_queries: List[str]\n", "yaml_parser = YamlOutputParser(pydantic_object=ProposedSearch)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["from src.api.common.services import LLM\n", "from src.api.common.tools.utils import extract_content"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/Library/CloudStorage/OneDrive-UNIR/Code/cont-gen-poc/venv/lib/python3.12/site-packages/langchain_core/utils/utils.py:235: UserWarning: WARNING! extra_headers is not default parameter.\n", "                extra_headers was transferred to model_kwargs.\n", "                Please confirm that extra_headers is what you intended.\n", "  warnings.warn(\n", "/Users/<USER>/Library/CloudStorage/OneDrive-UNIR/Code/cont-gen-poc/venv/lib/python3.12/site-packages/langchain_core/utils/utils.py:235: UserWarning: WARNING! extra_headers is not default parameter.\n", "                extra_headers was transferred to model_kwargs.\n", "                Please confirm that extra_headers is what you intended.\n", "  warnings.warn(\n"]}], "source": ["content_generator = DependencyContainer().get_content_generator()\n", "indice_id= 1\n", "asignatura = content_generator.get_asignatura(indice_id=indice_id)\n", "temas = content_generator.get_indice_elements(session, indice_id, structure_type=\"tema\")"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["Asignatura(asignatura='Precálculo - Curso de Fundamentos Matemáticos', estructura=Estructura(bloques_tematicos=[BloqueTematico(nombre='FUNDAMENTOS ALGEBRAICOS Y FUNCIONES BÁSICAS', temas=[Tema(nombre='Números Reales y Expresiones Algebraicas', epigrafes=['Propiedades y operaciones con números reales', 'Exponentes, radicales y logaritmos básicos', 'Expresiones algebraicas y factorización', 'Operaciones con fracciones algebraicas']), <PERSON><PERSON>(nombre='Ecuaciones y Desigualdades', epigrafes=['Ecuaciones cuadráticas y de grado superior', 'Ecuaciones lineales y sistemas', 'Desigualdades y sus propiedades', 'Ecuaciones con valor absoluto']), <PERSON><PERSON>(nombre='Conceptos Fundamentales de Funciones', epigrafes=['Definición, dominio y rango', 'Representación gráfica y tabulación', 'Transformaciones de funciones', 'Composición y función inversa']), <PERSON><PERSON>(nombre='Funciones Polinómicas y Racionales', epigrafes=['Funciones lineales y cuadráticas', 'Polinomios de grado superior', 'Funciones racionales y asíntotas', 'Análisis del comportamiento gráfico']), Tema(nombre='Funciones Exponenciales y Logarítmicas', epigrafes=['Función exponencial y sus propiedades', 'Función logarítmica como inversa', 'Propiedades de los logaritmos', 'Ecuaciones exponenciales y logarítmicas'])]), BloqueTematico(nombre='TRIGONOMETRÍA Y FUNDAMENTOS PARA CÁLCULO', temas=[Tema(nombre='Fundamentos de Trigonometría', epigrafes=['Medición de ángulos y el círculo unitario', 'Razones trigonométricas básicas', 'Resolución de triángulos rectángulos', 'Ley de senos y cosenos']), Tema(nombre='Funciones Trigonométricas', epigrafes=['Funciones seno, coseno y tangente', 'Gráficas y transformaciones', 'Identidades fundamentales', 'Ecuaciones trigonométricas básicas']), Tema(nombre='Geometría Analítica Preparatoria', epigrafes=['Coordenadas cartesianas y polares', 'Distancia, punto medio y pendiente', 'Ecuaciones de rectas y parábolas', 'Introducción a las cónicas']), Tema(nombre='Vectores en el Plano', epigrafes=['Conceptos básicos y representación', 'Operaciones vectoriales', 'Productos escalar y vectorial', 'Aplicaciones geométricas']), Tema(nombre='Introducción a Límites', epigrafes=['Concepto intuitivo de límite', 'Límites por aproximación numérica', 'Límites infinitos y al infinito', 'Introducción a la continuidad'])])]))"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["asignatura"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["result_dict = {}"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://rerank-multilingual-v3.swedencentral.models.ai.azure.com/v1/rerank \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://rerank-multilingual-v3.swedencentral.models.ai.azure.com/v1/rerank \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://rerank-multilingual-v3.swedencentral.models.ai.azure.com/v1/rerank \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://rerank-multilingual-v3.swedencentral.models.ai.azure.com/v1/rerank \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://rerank-multilingual-v3.swedencentral.models.ai.azure.com/v1/rerank \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://rerank-multilingual-v3.swedencentral.models.ai.azure.com/v1/rerank \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://rerank-multilingual-v3.swedencentral.models.ai.azure.com/v1/rerank \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://rerank-multilingual-v3.swedencentral.models.ai.azure.com/v1/rerank \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://rerank-multilingual-v3.swedencentral.models.ai.azure.com/v1/rerank \"HTTP/1.1 200 OK\"\n", "ERROR:src.api.common.services.search_engine:Error fetching results using duckduckgo_search for query 'site:semanticscholar.org Aplicaciones de logaritmos en problemas matemáticos filetype:pdf': https://duckduckgo.com/ 202 Ratelimit\n", "INFO:httpx:HTTP Request: POST https://rerank-multilingual-v3.swedencentral.models.ai.azure.com/v1/rerank \"HTTP/1.1 200 OK\"\n"]}], "source": ["import time\n", "\n", "for tema in temas:\n", "    final_results = []\n", "    epigrafes = content_generator.get_epigrafes_with_context(session,indice_id=indice_id, tema_id=tema.id)\n", "    model = LLM().get_llm(provider=\"openai\", model_name= \"gpt-4o-2024-08-06\")\n", "    chain = prompt_template | model\n", "    result = chain.invoke({\"SUBJECT_NAME\": asignatura.asignatura, \"CONTENT_BLOCK\": epigrafes[0].nombre_bloque, \"CURRENT_TOPIC\": epigrafes[0].nombre_tema, \"SUBTOPICS\": [e.nombre for e in epigrafes]})\n", "    result = extract_content(result.content, tag_name=\"search_queries\")[0]\n", "    result = yaml_parser.invoke(result)\n", "    for query in result.search_queries:\n", "        search_results = await search_engine.search(query = query, max_results=1, max_results_per_domain=10, use_reranker=True)\n", "        final_results.extend(search_results)\n", "        time.sleep(4.0)\n", "    time.sleep(150.0)\n", "    urls = list(set([r.href for r in final_results]))\n", "    epigrafe_ids = [e.id for e in epigrafes]\n", "    result_dict[tema.id] = [urls,epigrafe_ids]\n", "    break"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Having the results now we can search with duckdu<PERSON><PERSON>. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["* Rotar user agents\n", "* Funcion serverless para buscar en duckduckgo"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tema"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Fuentes encontradas para el primer tema"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Temporarily hardcode this as get rate limited. "]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["final_request = {\"docs\": []}\n", "for result in result_dict.values():\n", "    for url in result[0]:\n", "        formatted_request = {\"url\": url, \"tipo_archivo\": \"PDF\", \"epigrafe_ids\": result[1],\"fuente_archivo\": \"DUCKDUCKGO\"}\n", "        final_request['docs'].append(formatted_request)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["# import json\n", "# with open(\"search_data.json\", 'r', encoding=\"utf-8\") as f:\n", "#     final_request = json.loads(f.read())"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["import requests\n", "\n", "result = requests.post(url=\"http://0.0.0.0:8000/api/v1/documents\", json=final_request)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["200"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["result.status_code"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}