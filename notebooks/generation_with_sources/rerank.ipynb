{"cells": [{"cell_type": "code", "execution_count": 1, "id": "319265c2", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-21 11:02:27,622 - logger - INFO - Initial user 'Unir demo' already exists.\n", "2025-07-21 11:02:28,151 - logger - INFO - Created prompts successfully\n"]}], "source": ["import sys\n", "from pathlib import Path\n", "\n", "parent_dir =Path.cwd().parent.parent\n", "import os\n", "sys.path.append(str(parent_dir))\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv()\n", "from src.api.common.dependencies.get_session import get_session\n", "from src.api.common.dependency_container import DependencyContainer\n", "\n", "os.environ[\"DbHost\"] = \"localhost\"\n", "os.environ[\"DbPort\"] = \"5436\"\n", "DependencyContainer.initialize(observability= False)\n", "\n", "session = next(get_session())"]}, {"cell_type": "code", "execution_count": 2, "id": "3fc94c69", "metadata": {}, "outputs": [], "source": ["reranker = DependencyContainer.get_reranker()"]}, {"cell_type": "code", "execution_count": 3, "id": "a016b8b7", "metadata": {}, "outputs": [], "source": ["from langchain.schema import Document\n", "synthetic_docs = [\n", "    Document(\n", "        page_content=(\n", "            \"LangChain is a Python framework that helps you build robust applications \"\n", "            \"with large language models. It comes with abstractions for prompt management, \"\n", "            \"LLM wrappers, and tooling integrations.\"\n", "        ),\n", "        metadata={\"id\": \"doc1\", \"source\": \"synthetic\"}\n", "    ),\n", "    Document(\n", "        page_content=(\n", "            \"The library includes document loaders, text splitters, vectorstores, \"\n", "            \"retrievers, and chains—everything you need for retrieval‑augmented generation.\"\n", "        ),\n", "        metadata={\"id\": \"doc2\", \"source\": \"synthetic\"}\n", "    ),\n", "    Document(\n", "        page_content=(\n", "            \"Lang<PERSON>hai<PERSON> also has built‑in support for memory modules, callback handlers, \"\n", "            \"and streaming outputs—making it easy to track state and debug.\"\n", "        ),\n", "        metadata={\"id\": \"doc3\", \"source\": \"synthetic\"}\n", "    ),\n", "    Document(\n", "        page_content=(\n", "            \"You can glue together chains of LLM calls, map‑reduce summarizations, \"\n", "            \"and even deploy conversational agents that call out to external tools.\"\n", "        ),\n", "        metadata={\"id\": \"doc4\", \"source\": \"synthetic\"}\n", "    ),\n", "]"]}, {"cell_type": "code", "execution_count": 4, "id": "fb8e58e6", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://cohere-rerank-v3-5-svhyz.swedencentral.models.ai.azure.com/v2/rerank \"HTTP/1.1 200 OK\"\n"]}], "source": ["reranked = reranker.rerank(query=\"Hola\", documents=synthetic_docs)"]}, {"cell_type": "code", "execution_count": 5, "id": "7c559a6c", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'index': 0, 'relevance_score': 0.04301479},\n", " {'index': 1, 'relevance_score': 0.03852772},\n", " {'index': 2, 'relevance_score': 0.027861731},\n", " {'index': 3, 'relevance_score': 0.024518725}]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["reranked"]}, {"cell_type": "code", "execution_count": null, "id": "90a3fe0d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ia_gestorcontenidosiagen_be", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}