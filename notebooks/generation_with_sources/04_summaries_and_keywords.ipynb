{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/mpu/cont_gen_poc/venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:334: UserWarning: Valid config keys have changed in V2:\n", "* 'allow_population_by_field_name' has been renamed to 'populate_by_name'\n", "* 'smart_union' has been removed\n", "  warnings.warn(message, UserWarning)\n", "/home/<USER>/mpu/cont_gen_poc/venv/lib/python3.11/site-packages/sentence_transformers/cross_encoder/CrossEncoder.py:11: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from tqdm.autonotebook import tqdm, trange\n", "2024-09-03 16:23:16,310 - logger - INFO - Initial user 'Unir demo' already exists.\n", "INFO:logger:Initial user 'Unir demo' already exists.\n", "INFO:phoenix.config:📋 Ensuring phoenix working directory: /home/<USER>/.phoenix\n", "INFO:phoenix.inferences.inferences:Dataset: phoenix_inferences_244f3dc7-468c-431a-a5e9-bf008acdd974 initialized\n", "INFO:httpx:HTTP Request: GET http://localhost:6006/arize_phoenix_version \"HTTP/1.1 200 OK\"\n"]}], "source": ["import ast\n", "import sys\n", "from pathlib import Path\n", "\n", "parent_dir =Path.cwd().parent.parent\n", "sys.path.append(str(parent_dir))\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv()\n", "from src.api.common.dependencies.get_session import get_session\n", "from src.api.common.dependency_container import DependencyContainer\n", "from src.api.common.tools.utils import extract_content\n", "\n", "DependencyContainer.initialize(observability= False)\n", "session = next(get_session())\n", "\n", "try:\n", "    from phoenix.trace.langchain import LangChainInstrumentor\n", "    LangChainInstrumentor(url = \"http://localhost:6006/v1/traces\").instrument()\n", "except Exception as e:\n", "    print(\"An exception instrumenting has ocurred\", e)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/mpu/cont_gen_poc/venv/lib/python3.11/site-packages/langchain_core/utils/utils.py:234: UserWarning: WARNING! extra_headers is not default parameter.\n", "                extra_headers was transferred to model_kwargs.\n", "                Please confirm that extra_headers is what you intended.\n", "  warnings.warn(\n"]}], "source": ["content_generator = DependencyContainer().content_generator\n", "indice_id= 1\n", "asignatura = content_generator.get_asignatura(session, indice_id=indice_id)\n", "epigrafes = content_generator.get_indice_elements(session, indice_id, structure_type=\"epigrafe\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["\n", "prompt_summary = \"\"\"\n", "You are tasked with creating a detailed summary of a given epigraph. Aim it to a student that wants to check and revise the concepts he learned in a segment.\n", "\n", "The epigraph name is what is after ### in the document.\n", "\n", "Here is the document with the epigraph to be summarized:\n", "\n", "<document>\n", "{document}\n", "</document>\n", "\n", "Follow these guidelines to create the summary:\n", "\n", "1. Identify the most important topics, to be summarized and which the student may find useful to refresh.\n", "2. Focus on factual content, important concepts, and central ideas , avoiding including specific examples introduced in the epigraph.\n", "3. Provide a final summary taking the previous points into account and orient it towards a student that wants a summary of what was covered in the epigraph.\n", "4. You will redact the summary in few paragraphs providing detailed explanations of the core knowledge covered in the epigraph.\n", "5. Summarize in the language of the document.\n", "6. Avoid metareferences or explanations regarding your task, refer only to the epigraph and learnings that can be extracted from it.\n", "7. Make it direct and don't use connectors or provide explanations at the end.\n", "\n", "And this guidelines to create the keyword list: \n", "1. You will provide the keywords in list format inside <keywords> tag. Example: <keywords>[\"management\", \"strategy\", \"innovation\", \"competitive advantage\"]</keywords>\n", "2. Provide keywords in the original language of the document.\n", "3. You will provide the 5-10 most representative and unique keywords regarding the epigraph, so it can be easily categorized.\n", "\n", "How to proceed:\n", "1. First do a reasoning on how to satisfy the previous instructions in the best possible way and include it in the <reasoning tag>.\n", "2. Provide summary and keywords in <summary> and <keywords> tags\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from langchain.prompts import ChatPromptTemplate\n", "\n", "llm = DependencyContainer.get_llm_manager().get_llm(provider=\"openai\", model_name=\"gpt-4o-mini\")\n", "prompt_template = ChatPromptTemplate.from_template(prompt_summary)\n", "chain = prompt_template | llm"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def process_result(epigrafe, result):\n", "    if not result:\n", "        print(f\"[Epigrafe: {epigrafe}] was not properly summarized\")\n", "        return None, None\n", "    try:\n", "        _, content = content_generator._extract_reasoning(result)\n", "        summary = extract_content(content.content, \"summary\")[0]\n", "        keywords = extract_content(content.content, \"keywords\")[0]\n", "        keywords = ast.literal_eval(f'{keywords}')\n", "        return summary, keywords\n", "    except Exception as e:\n", "        print(f\"Error processing epigrafe {epigrafe}: {e}\")\n", "        return None, None\n", "\n", "async def summarize_epigrafes(session, indice_id: int, tema_id: int = None, content_plan_version: int = 1, store=False):\n", "    epigrafes = content_generator.get_indice_elements(\n", "        session=session, indice_id=indice_id, structure_type=\"epigrafe\", tema_id=tema_id\n", "    )\n", "    docs = [\n", "        {\"document\": content_generator.get_content_markdown(session, epigrafe.id_tema, content_plan_version, False, epigrafe.id)}\n", "        for epigrafe in epigrafes\n", "    ]\n", "    resultados = await chain.abatch(docs)\n", "    for i, epigrafe in enumerate(epigrafes):\n", "        summary, keywords = process_result(epigrafe, resultados[i])\n", "        if summary is not None and keywords is not None:\n", "            epigrafe.summary, epigrafe.keywords = summary, keywords\n", "\n", "    if store:\n", "        session.commit()\n", "        for epigrafe in epigrafes:\n", "            session.refresh(epigrafe)\n", "    return epigrafes"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING:urllib3.connectionpool:Connection pool is full, discarding connection: localhost. Connection pool size: 10\n", "WARNING:urllib3.connectionpool:Connection pool is full, discarding connection: localhost. Connection pool size: 10\n", "WARNING:urllib3.connectionpool:Connection pool is full, discarding connection: localhost. Connection pool size: 10\n", "WARNING:urllib3.connectionpool:Connection pool is full, discarding connection: localhost. Connection pool size: 10\n", "WARNING:urllib3.connectionpool:Connection pool is full, discarding connection: localhost. Connection pool size: 10\n", "WARNING:urllib3.connectionpool:Connection pool is full, discarding connection: localhost. Connection pool size: 10\n", "WARNING:urllib3.connectionpool:Connection pool is full, discarding connection: localhost. Connection pool size: 10\n", "WARNING:urllib3.connectionpool:Connection pool is full, discarding connection: localhost. Connection pool size: 10\n", "WARNING:urllib3.connectionpool:Connection pool is full, discarding connection: localhost. Connection pool size: 10\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "WARNING:urllib3.connectionpool:Connection pool is full, discarding connection: localhost. Connection pool size: 10\n", "WARNING:urllib3.connectionpool:Connection pool is full, discarding connection: localhost. Connection pool size: 10\n", "WARNING:urllib3.connectionpool:Connection pool is full, discarding connection: localhost. Connection pool size: 10\n", "WARNING:urllib3.connectionpool:Connection pool is full, discarding connection: localhost. Connection pool size: 10\n", "WARNING:urllib3.connectionpool:Connection pool is full, discarding connection: localhost. Connection pool size: 10\n", "WARNING:urllib3.connectionpool:Connection pool is full, discarding connection: localhost. Connection pool size: 10\n", "WARNING:urllib3.connectionpool:Connection pool is full, discarding connection: localhost. Connection pool size: 10\n"]}], "source": ["epigrafes = await summarize_epigrafes(session, indice_id=1, store = True)"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 2}