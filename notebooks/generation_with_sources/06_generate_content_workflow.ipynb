{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "from pathlib import Path\n", "\n", "parent_dir =Path.cwd().parent.parent\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv()\n", "sys.path.append(str(parent_dir))"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/Library/CloudStorage/OneDrive-UNIR/Code/cont_gen_poc/venv/lib/python3.12/site-packages/pydantic/_internal/_config.py:341: UserWarning: Valid config keys have changed in V2:\n", "* 'allow_population_by_field_name' has been renamed to 'populate_by_name'\n", "* 'smart_union' has been removed\n", "  warnings.warn(message, UserWarning)\n", "/Users/<USER>/Library/CloudStorage/OneDrive-UNIR/Code/cont_gen_poc/venv/lib/python3.12/site-packages/pydantic/_internal/_fields.py:172: UserWarning: Field name \"schema\" in \"GenerateCompetenciesFromIndexInput\" shadows an attribute in parent \"GenerateCompetenciesInput\"\n", "  warnings.warn(\n", "/Users/<USER>/Library/CloudStorage/OneDrive-UNIR/Code/cont_gen_poc/venv/lib/python3.12/site-packages/pydantic/_internal/_fields.py:132: UserWarning: Field \"model_info\" in GenerateCompetenciesRequest has conflict with protected namespace \"model_\".\n", "\n", "You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.\n", "  warnings.warn(\n", "/Users/<USER>/Library/CloudStorage/OneDrive-UNIR/Code/cont_gen_poc/venv/lib/python3.12/site-packages/pydantic/_internal/_fields.py:132: UserWarning: Field \"model_info\" in RegenerateCompetenciesRequest has conflict with protected namespace \"model_\".\n", "\n", "You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.\n", "  warnings.warn(\n", "/Users/<USER>/Library/CloudStorage/OneDrive-UNIR/Code/cont_gen_poc/venv/lib/python3.12/site-packages/sentence_transformers/cross_encoder/CrossEncoder.py:11: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from tqdm.autonotebook import tqdm, trange\n", "/Users/<USER>/Library/CloudStorage/OneDrive-UNIR/Code/cont_gen_poc/venv/lib/python3.12/site-packages/pydantic/_internal/_fields.py:132: UserWarning: Field \"model_info\" in GenerateIndexRequest has conflict with protected namespace \"model_\".\n", "\n", "You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.\n", "  warnings.warn(\n", "/Users/<USER>/Library/CloudStorage/OneDrive-UNIR/Code/cont_gen_poc/venv/lib/python3.12/site-packages/pydantic/_internal/_fields.py:132: UserWarning: Field \"model_info\" in RegenerateIndexRequest has conflict with protected namespace \"model_\".\n", "\n", "You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.\n", "  warnings.warn(\n", "/Users/<USER>/Library/CloudStorage/OneDrive-UNIR/Code/cont_gen_poc/venv/lib/python3.12/site-packages/pydantic/_internal/_fields.py:132: UserWarning: Field \"model_info\" in GenerateSubjectCompetenciesRequest has conflict with protected namespace \"model_\".\n", "\n", "You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.\n", "  warnings.warn(\n", "/Users/<USER>/Library/CloudStorage/OneDrive-UNIR/Code/cont_gen_poc/venv/lib/python3.12/site-packages/pydantic/_internal/_fields.py:132: UserWarning: Field \"model_info\" in GenerateDidacticInstructionsRequest has conflict with protected namespace \"model_\".\n", "\n", "You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.\n", "  warnings.warn(\n", "/Users/<USER>/Library/CloudStorage/OneDrive-UNIR/Code/cont_gen_poc/venv/lib/python3.12/site-packages/pydantic/_internal/_fields.py:132: UserWarning: Field \"model_info\" in RegenerateDidacticInstructionsRequest has conflict with protected namespace \"model_\".\n", "\n", "You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.\n", "  warnings.warn(\n", "2024-12-18 11:53:31,969 - logger - INFO - Initial user 'Unir demo' already exists.\n", "2024-12-18 11:53:32,004 - logger - INFO - Include document router is True\n", "2024-12-18 11:53:32,004 - logger - INFO - Entered inside include document router and search engine\n", "2024-12-18 11:53:32,008 - logger - INFO - Prompt number > 5 found in the db, not creating them, if you added some new, run the prompt creation script for your specfic prompt, or add it to the db.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Phoenix endpoint reachable after 1 attempts.\n", "Instrument successful\n"]}], "source": ["from src.api.common.dependency_container import DependencyContainer\n", "\n", "DependencyContainer.initialize()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from src.api.workflows.indexes.generate_content.generate_content_workflow import (\n", "    GenerateContentRequest,\n", ")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["request = GenerateContentRequest(indice_id = 1)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["generate_content_workflow = DependencyContainer.get_generate_content_workflow()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2024-11-04 11:49:35,785 - logger - INFO - Bloques are: [<PERSON><PERSON><PERSON>(position=1, id=1, indice_id=1, name='FUNDAMENTOS MATEMÁTICOS PARA MACHINE LEARNING EN ENTORNOS EMPRESARIALES', created_at=datetime.datetime(2024, 10, 30, 12, 45, 18, 888897), updated_at=datetime.datetime(2024, 10, 30, 12, 45, 18, 888898))]\n", "INFO:logger:Bloques are: [<PERSON><PERSON><PERSON>(position=1, id=1, indice_id=1, name='FUNDAMENTOS MATEMÁTICOS PARA MACHINE LEARNING EN ENTORNOS EMPRESARIALES', created_at=datetime.datetime(2024, 10, 30, 12, 45, 18, 888897), updated_at=datetime.datetime(2024, 10, 30, 12, 45, 18, 888898))]\n", "2024-11-04 11:49:35,789 - logger - INFO - <PERSON><PERSON> is: [<PERSON><PERSON>(name='Fundamentos de Álgebra Lineal para Análisis de Datos', position=1, id=1, id_bloque=1, created_at=datetime.datetime(2024, 10, 30, 12, 45, 18, 889826), updated_at=datetime.datetime(2024, 10, 30, 12, 45, 18, 889827)), <PERSON><PERSON>(name='Espacios Vectoriales y Transformaciones en Análisis Predictivo', position=2, id=2, id_bloque=1, created_at=datetime.datetime(2024, 10, 30, 12, 45, 18, 892047), updated_at=datetime.datetime(2024, 10, 30, 12, 45, 18, 892048)), <PERSON><PERSON>(name='Cálculo para Optimización Empresarial', position=3, id=3, id_bloque=1, created_at=datetime.datetime(2024, 10, 30, 12, 45, 18, 893300), updated_at=datetime.datetime(2024, 10, 30, 12, 45, 18, 893301)), <PERSON><PERSON>(name='Análisis Multivariable en Entornos Empresariales', position=4, id=4, id_bloque=1, created_at=datetime.datetime(2024, 10, 30, 12, 45, 18, 894733), updated_at=datetime.datetime(2024, 10, 30, 12, 45, 18, 894734)), Tema(name='Probabilidad y Estadística para Business Intelligence', position=5, id=5, id_bloque=1, created_at=datetime.datetime(2024, 10, 30, 12, 45, 18, 896154), updated_at=datetime.datetime(2024, 10, 30, 12, 45, 18, 896154)), Tema(name='Optimización Matemática en Decisiones de Negocio', position=6, id=6, id_bloque=1, created_at=datetime.datetime(2024, 10, 30, 12, 45, 18, 897138), updated_at=datetime.datetime(2024, 10, 30, 12, 45, 18, 897138)), Tema(name='Teoría de la Información en Analytics', position=7, id=7, id_bloque=1, created_at=datetime.datetime(2024, 10, 30, 12, 45, 18, 898509), updated_at=datetime.datetime(2024, 10, 30, 12, 45, 18, 898510)), Tema(name='Matemática Computacional Aplicada', position=8, id=8, id_bloque=1, created_at=datetime.datetime(2024, 10, 30, 12, 45, 18, 899889), updated_at=datetime.datetime(2024, 10, 30, 12, 45, 18, 899890))]\n", "INFO:logger:<PERSON><PERSON> is: [<PERSON><PERSON>(name='Fundamentos de Álgebra Lineal para Análisis de Datos', position=1, id=1, id_bloque=1, created_at=datetime.datetime(2024, 10, 30, 12, 45, 18, 889826), updated_at=datetime.datetime(2024, 10, 30, 12, 45, 18, 889827)), <PERSON><PERSON>(name='Espacios Vectoriales y Transformaciones en Análisis Predictivo', position=2, id=2, id_bloque=1, created_at=datetime.datetime(2024, 10, 30, 12, 45, 18, 892047), updated_at=datetime.datetime(2024, 10, 30, 12, 45, 18, 892048)), <PERSON><PERSON>(name='Cálculo para Optimización Empresarial', position=3, id=3, id_bloque=1, created_at=datetime.datetime(2024, 10, 30, 12, 45, 18, 893300), updated_at=datetime.datetime(2024, 10, 30, 12, 45, 18, 893301)), <PERSON><PERSON>(name='Análisis Multivariable en Entornos Empresariales', position=4, id=4, id_bloque=1, created_at=datetime.datetime(2024, 10, 30, 12, 45, 18, 894733), updated_at=datetime.datetime(2024, 10, 30, 12, 45, 18, 894734)), Tema(name='Probabilidad y Estadística para Business Intelligence', position=5, id=5, id_bloque=1, created_at=datetime.datetime(2024, 10, 30, 12, 45, 18, 896154), updated_at=datetime.datetime(2024, 10, 30, 12, 45, 18, 896154)), Tema(name='Optimización Matemática en Decisiones de Negocio', position=6, id=6, id_bloque=1, created_at=datetime.datetime(2024, 10, 30, 12, 45, 18, 897138), updated_at=datetime.datetime(2024, 10, 30, 12, 45, 18, 897138)), Tema(name='Teoría de la Información en Analytics', position=7, id=7, id_bloque=1, created_at=datetime.datetime(2024, 10, 30, 12, 45, 18, 898509), updated_at=datetime.datetime(2024, 10, 30, 12, 45, 18, 898510)), Tema(name='Matemática Computacional Aplicada', position=8, id=8, id_bloque=1, created_at=datetime.datetime(2024, 10, 30, 12, 45, 18, 899889), updated_at=datetime.datetime(2024, 10, 30, 12, 45, 18, 899890))]\n", "INFO:azure.servicebus._pyamqp.aio._connection_async:Connection state changed: None -> <ConnectionState.START: 0>\n", "INFO:azure.servicebus._pyamqp.aio._connection_async:Connection state changed: <ConnectionState.START: 0> -> <ConnectionState.HDR_SENT: 2>\n", "INFO:azure.servicebus._pyamqp.aio._connection_async:Connection state changed: <ConnectionState.HDR_SENT: 2> -> <ConnectionState.HDR_SENT: 2>\n", "INFO:azure.servicebus._pyamqp.aio._connection_async:Connection state changed: <ConnectionState.HDR_SENT: 2> -> <ConnectionState.OPEN_PIPE: 4>\n", "INFO:azure.servicebus._pyamqp.aio._session_async:Session state changed: <SessionState.UNMAPPED: 0> -> <SessionState.BEGIN_SENT: 1>\n", "INFO:azure.servicebus._pyamqp.aio._link_async:Link state changed: <LinkState.DETACHED: 0> -> <LinkState.ATTACH_SENT: 1>\n", "INFO:azure.servicebus._pyamqp.aio._management_link_async:Management link receiver state changed: <LinkState.DETACHED: 0> -> <LinkState.ATTACH_SENT: 1>\n", "INFO:azure.servicebus._pyamqp.aio._link_async:Link state changed: <LinkState.DETACHED: 0> -> <LinkState.ATTACH_SENT: 1>\n", "INFO:azure.servicebus._pyamqp.aio._management_link_async:Management link sender state changed: <LinkState.DETACHED: 0> -> <LinkState.ATTACH_SENT: 1>\n", "INFO:azure.servicebus._pyamqp.aio._connection_async:Connection state changed: <ConnectionState.OPEN_PIPE: 4> -> <ConnectionState.OPEN_SENT: 7>\n", "INFO:azure.servicebus._pyamqp.aio._connection_async:Connection state changed: <ConnectionState.OPEN_SENT: 7> -> <ConnectionState.OPENED: 9>\n", "INFO:azure.servicebus._pyamqp.aio._session_async:Session state changed: <SessionState.BEGIN_SENT: 1> -> <SessionState.MAPPED: 3>\n", "INFO:azure.servicebus._pyamqp.aio._link_async:Link state changed: <LinkState.ATTACH_SENT: 1> -> <LinkState.ATTACHED: 3>\n", "INFO:azure.servicebus._pyamqp.aio._management_link_async:Management link receiver state changed: <LinkState.ATTACH_SENT: 1> -> <LinkState.ATTACHED: 3>\n", "INFO:azure.servicebus._pyamqp.aio._link_async:Link state changed: <LinkState.ATTACH_SENT: 1> -> <LinkState.ATTACHED: 3>\n", "INFO:azure.servicebus._pyamqp.aio._management_link_async:Management link sender state changed: <LinkState.ATTACH_SENT: 1> -> <LinkState.ATTACHED: 3>\n", "INFO:azure.servicebus._pyamqp.aio._cbs_async:CBS completed opening with status: <ManagementOpenResult.OK: 1>\n", "INFO:azure.servicebus._pyamqp.aio._cbs_async:CBS Put token error: b'amqp:not-found'\n", "INFO:azure.servicebus._pyamqp.aio._link_async:Link state changed: <LinkState.ATTACHED: 3> -> <LinkState.DETACH_SENT: 4>\n", "INFO:azure.servicebus._pyamqp.aio._management_link_async:Management link receiver state changed: <LinkState.ATTACHED: 3> -> <LinkState.DETACH_SENT: 4>\n", "INFO:azure.servicebus._pyamqp.aio._link_async:Link state changed: <LinkState.ATTACHED: 3> -> <LinkState.DETACH_SENT: 4>\n", "INFO:azure.servicebus._pyamqp.aio._management_link_async:Management link sender state changed: <LinkState.ATTACHED: 3> -> <LinkState.DETACH_SENT: 4>\n", "INFO:azure.servicebus._pyamqp.aio._session_async:Session state changed: <SessionState.MAPPED: 3> -> <SessionState.END_SENT: 4>\n", "INFO:azure.servicebus._pyamqp.aio._connection_async:Connection state changed: <ConnectionState.OPENED: 9> -> <ConnectionState.CLOSE_SENT: 11>\n", "INFO:azure.servicebus._pyamqp.aio._connection_async:Connection state changed: <ConnectionState.CLOSE_SENT: 11> -> <ConnectionState.END: 13>\n", "INFO:azure.servicebus._pyamqp.aio._session_async:Session state changed: <SessionState.END_SENT: 4> -> <SessionState.DISCARDING: 6>\n", "INFO:azure.servicebus._pyamqp.aio._link_async:Link state changed: <LinkState.DETACH_SENT: 4> -> <LinkState.DETACHED: 0>\n", "INFO:azure.servicebus._pyamqp.aio._management_link_async:Management link sender state changed: <LinkState.DETACH_SENT: 4> -> <LinkState.DETACHED: 0>\n", "INFO:azure.servicebus._pyamqp.aio._link_async:Link state changed: <LinkState.DETACH_SENT: 4> -> <LinkState.DETACHED: 0>\n", "INFO:azure.servicebus._pyamqp.aio._management_link_async:Management link receiver state changed: <LinkState.DETACH_SENT: 4> -> <LinkState.DETACHED: 0>\n", "INFO:azure.servicebus.aio._base_handler_async:AMQP error occurred: (TokenAuthFailure('CBS Token authentication failed.\\nStatus code: None')), condition: (<ErrorCondition.ClientError: b'amqp:client-error'>), description: (None).\n", "INFO:azure.servicebus.aio._base_handler_async:AMQP Connection authentication error occurred: (TokenAuthFailure('CBS Token authentication failed.\\nStatus code: None')).\n", "INFO:azure.servicebus.aio._base_handler_async:'servicebus.pysdk-679fc6ff' has an exception (ServiceBusAuthenticationError('Service Bus has encountered an error. Error condition: amqp:client-error.')). Retrying...\n", "INFO:azure.servicebus._pyamqp.aio._connection_async:Connection state changed: None -> <ConnectionState.START: 0>\n", "INFO:azure.servicebus._pyamqp.aio._connection_async:Connection state changed: <ConnectionState.START: 0> -> <ConnectionState.HDR_SENT: 2>\n", "INFO:azure.servicebus._pyamqp.aio._connection_async:Connection state changed: <ConnectionState.HDR_SENT: 2> -> <ConnectionState.HDR_SENT: 2>\n", "INFO:azure.servicebus._pyamqp.aio._connection_async:Connection state changed: <ConnectionState.HDR_SENT: 2> -> <ConnectionState.OPEN_PIPE: 4>\n", "INFO:azure.servicebus._pyamqp.aio._session_async:Session state changed: <SessionState.UNMAPPED: 0> -> <SessionState.BEGIN_SENT: 1>\n", "INFO:azure.servicebus._pyamqp.aio._link_async:Link state changed: <LinkState.DETACHED: 0> -> <LinkState.ATTACH_SENT: 1>\n", "INFO:azure.servicebus._pyamqp.aio._management_link_async:Management link receiver state changed: <LinkState.DETACHED: 0> -> <LinkState.ATTACH_SENT: 1>\n", "INFO:azure.servicebus._pyamqp.aio._link_async:Link state changed: <LinkState.DETACHED: 0> -> <LinkState.ATTACH_SENT: 1>\n", "INFO:azure.servicebus._pyamqp.aio._management_link_async:Management link sender state changed: <LinkState.DETACHED: 0> -> <LinkState.ATTACH_SENT: 1>\n", "INFO:azure.servicebus._pyamqp.aio._connection_async:Connection state changed: <ConnectionState.OPEN_PIPE: 4> -> <ConnectionState.OPEN_SENT: 7>\n", "INFO:azure.servicebus._pyamqp.aio._connection_async:Connection state changed: <ConnectionState.OPEN_SENT: 7> -> <ConnectionState.OPENED: 9>\n", "INFO:azure.servicebus._pyamqp.aio._session_async:Session state changed: <SessionState.BEGIN_SENT: 1> -> <SessionState.MAPPED: 3>\n", "INFO:azure.servicebus._pyamqp.aio._link_async:Link state changed: <LinkState.ATTACH_SENT: 1> -> <LinkState.ATTACHED: 3>\n", "INFO:azure.servicebus._pyamqp.aio._management_link_async:Management link receiver state changed: <LinkState.ATTACH_SENT: 1> -> <LinkState.ATTACHED: 3>\n", "INFO:azure.servicebus._pyamqp.aio._link_async:Link state changed: <LinkState.ATTACH_SENT: 1> -> <LinkState.ATTACHED: 3>\n", "INFO:azure.servicebus._pyamqp.aio._management_link_async:Management link sender state changed: <LinkState.ATTACH_SENT: 1> -> <LinkState.ATTACHED: 3>\n", "INFO:azure.servicebus._pyamqp.aio._cbs_async:CBS completed opening with status: <ManagementOpenResult.OK: 1>\n", "INFO:azure.servicebus._pyamqp.aio._cbs_async:CBS Put token error: b'amqp:not-found'\n", "INFO:azure.servicebus._pyamqp.aio._link_async:Link state changed: <LinkState.ATTACHED: 3> -> <LinkState.DETACH_SENT: 4>\n", "INFO:azure.servicebus._pyamqp.aio._management_link_async:Management link receiver state changed: <LinkState.ATTACHED: 3> -> <LinkState.DETACH_SENT: 4>\n", "INFO:azure.servicebus._pyamqp.aio._link_async:Link state changed: <LinkState.ATTACHED: 3> -> <LinkState.DETACH_SENT: 4>\n", "INFO:azure.servicebus._pyamqp.aio._management_link_async:Management link sender state changed: <LinkState.ATTACHED: 3> -> <LinkState.DETACH_SENT: 4>\n", "INFO:azure.servicebus._pyamqp.aio._session_async:Session state changed: <SessionState.MAPPED: 3> -> <SessionState.END_SENT: 4>\n", "INFO:azure.servicebus._pyamqp.aio._connection_async:Connection state changed: <ConnectionState.OPENED: 9> -> <ConnectionState.CLOSE_SENT: 11>\n", "INFO:azure.servicebus._pyamqp.aio._connection_async:Connection state changed: <ConnectionState.CLOSE_SENT: 11> -> <ConnectionState.END: 13>\n", "INFO:azure.servicebus._pyamqp.aio._session_async:Session state changed: <SessionState.END_SENT: 4> -> <SessionState.DISCARDING: 6>\n", "INFO:azure.servicebus._pyamqp.aio._link_async:Link state changed: <LinkState.DETACH_SENT: 4> -> <LinkState.DETACHED: 0>\n", "INFO:azure.servicebus._pyamqp.aio._management_link_async:Management link sender state changed: <LinkState.DETACH_SENT: 4> -> <LinkState.DETACHED: 0>\n", "INFO:azure.servicebus._pyamqp.aio._link_async:Link state changed: <LinkState.DETACH_SENT: 4> -> <LinkState.DETACHED: 0>\n", "INFO:azure.servicebus._pyamqp.aio._management_link_async:Management link receiver state changed: <LinkState.DETACH_SENT: 4> -> <LinkState.DETACHED: 0>\n", "INFO:azure.servicebus.aio._base_handler_async:AMQP error occurred: (TokenAuthFailure('CBS Token authentication failed.\\nStatus code: None')), condition: (<ErrorCondition.ClientError: b'amqp:client-error'>), description: (None).\n", "INFO:azure.servicebus.aio._base_handler_async:AMQP Connection authentication error occurred: (TokenAuthFailure('CBS Token authentication failed.\\nStatus code: None')).\n", "INFO:azure.servicebus.aio._base_handler_async:'servicebus.pysdk-679fc6ff' has an exception (ServiceBusAuthenticationError('Service Bus has encountered an error. Error condition: amqp:client-error.')). Retrying...\n", "INFO:azure.servicebus._pyamqp.aio._connection_async:Connection state changed: None -> <ConnectionState.START: 0>\n", "INFO:azure.servicebus._pyamqp.aio._connection_async:Connection state changed: <ConnectionState.START: 0> -> <ConnectionState.HDR_SENT: 2>\n", "INFO:azure.servicebus._pyamqp.aio._connection_async:Connection state changed: <ConnectionState.HDR_SENT: 2> -> <ConnectionState.HDR_SENT: 2>\n", "INFO:azure.servicebus._pyamqp.aio._connection_async:Connection state changed: <ConnectionState.HDR_SENT: 2> -> <ConnectionState.OPEN_PIPE: 4>\n", "INFO:azure.servicebus._pyamqp.aio._session_async:Session state changed: <SessionState.UNMAPPED: 0> -> <SessionState.BEGIN_SENT: 1>\n", "INFO:azure.servicebus._pyamqp.aio._link_async:Link state changed: <LinkState.DETACHED: 0> -> <LinkState.ATTACH_SENT: 1>\n", "INFO:azure.servicebus._pyamqp.aio._management_link_async:Management link receiver state changed: <LinkState.DETACHED: 0> -> <LinkState.ATTACH_SENT: 1>\n", "INFO:azure.servicebus._pyamqp.aio._link_async:Link state changed: <LinkState.DETACHED: 0> -> <LinkState.ATTACH_SENT: 1>\n", "INFO:azure.servicebus._pyamqp.aio._management_link_async:Management link sender state changed: <LinkState.DETACHED: 0> -> <LinkState.ATTACH_SENT: 1>\n", "INFO:azure.servicebus._pyamqp.aio._connection_async:Connection state changed: <ConnectionState.OPEN_PIPE: 4> -> <ConnectionState.OPEN_SENT: 7>\n", "INFO:azure.servicebus._pyamqp.aio._connection_async:Connection state changed: <ConnectionState.OPEN_SENT: 7> -> <ConnectionState.OPENED: 9>\n", "INFO:azure.servicebus._pyamqp.aio._session_async:Session state changed: <SessionState.BEGIN_SENT: 1> -> <SessionState.MAPPED: 3>\n", "INFO:azure.servicebus._pyamqp.aio._link_async:Link state changed: <LinkState.ATTACH_SENT: 1> -> <LinkState.ATTACHED: 3>\n", "INFO:azure.servicebus._pyamqp.aio._management_link_async:Management link receiver state changed: <LinkState.ATTACH_SENT: 1> -> <LinkState.ATTACHED: 3>\n", "INFO:azure.servicebus._pyamqp.aio._link_async:Link state changed: <LinkState.ATTACH_SENT: 1> -> <LinkState.ATTACHED: 3>\n", "INFO:azure.servicebus._pyamqp.aio._management_link_async:Management link sender state changed: <LinkState.ATTACH_SENT: 1> -> <LinkState.ATTACHED: 3>\n", "INFO:azure.servicebus._pyamqp.aio._cbs_async:CBS completed opening with status: <ManagementOpenResult.OK: 1>\n", "INFO:azure.servicebus._pyamqp.aio._cbs_async:CBS Put token error: b'amqp:not-found'\n", "INFO:azure.servicebus._pyamqp.aio._link_async:Link state changed: <LinkState.ATTACHED: 3> -> <LinkState.DETACH_SENT: 4>\n", "INFO:azure.servicebus._pyamqp.aio._management_link_async:Management link receiver state changed: <LinkState.ATTACHED: 3> -> <LinkState.DETACH_SENT: 4>\n", "INFO:azure.servicebus._pyamqp.aio._link_async:Link state changed: <LinkState.ATTACHED: 3> -> <LinkState.DETACH_SENT: 4>\n", "INFO:azure.servicebus._pyamqp.aio._management_link_async:Management link sender state changed: <LinkState.ATTACHED: 3> -> <LinkState.DETACH_SENT: 4>\n", "INFO:azure.servicebus._pyamqp.aio._session_async:Session state changed: <SessionState.MAPPED: 3> -> <SessionState.END_SENT: 4>\n", "INFO:azure.servicebus._pyamqp.aio._connection_async:Connection state changed: <ConnectionState.OPENED: 9> -> <ConnectionState.CLOSE_SENT: 11>\n", "INFO:azure.servicebus._pyamqp.aio._connection_async:Connection state changed: <ConnectionState.CLOSE_SENT: 11> -> <ConnectionState.END: 13>\n", "INFO:azure.servicebus._pyamqp.aio._session_async:Session state changed: <SessionState.END_SENT: 4> -> <SessionState.DISCARDING: 6>\n", "INFO:azure.servicebus._pyamqp.aio._link_async:Link state changed: <LinkState.DETACH_SENT: 4> -> <LinkState.DETACHED: 0>\n", "INFO:azure.servicebus._pyamqp.aio._management_link_async:Management link sender state changed: <LinkState.DETACH_SENT: 4> -> <LinkState.DETACHED: 0>\n", "INFO:azure.servicebus._pyamqp.aio._link_async:Link state changed: <LinkState.DETACH_SENT: 4> -> <LinkState.DETACHED: 0>\n", "INFO:azure.servicebus._pyamqp.aio._management_link_async:Management link receiver state changed: <LinkState.DETACH_SENT: 4> -> <LinkState.DETACHED: 0>\n", "INFO:azure.servicebus.aio._base_handler_async:AMQP error occurred: (TokenAuthFailure('CBS Token authentication failed.\\nStatus code: None')), condition: (<ErrorCondition.ClientError: b'amqp:client-error'>), description: (None).\n", "INFO:azure.servicebus.aio._base_handler_async:AMQP Connection authentication error occurred: (TokenAuthFailure('CBS Token authentication failed.\\nStatus code: None')).\n"]}, {"ename": "CancelledError", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTokenAuthFailure\u001b[0m                          Trace<PERSON> (most recent call last)", "File \u001b[0;32m~/Library/CloudStorage/OneDrive-UNIR/Code/cont-gen-poc/venv/lib/python3.12/site-packages/azure/servicebus/aio/_base_handler_async.py:260\u001b[0m, in \u001b[0;36mBaseHandler._do_retryable_operation\u001b[0;34m(self, operation, timeout, **kwargs)\u001b[0m\n\u001b[1;32m    259\u001b[0m         kwargs[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtimeout\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m remaining_timeout\n\u001b[0;32m--> 260\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;01mawait\u001b[39;00m operation(\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[1;32m    261\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mStopAsyncIteration\u001b[39;00m:\n", "File \u001b[0;32m~/Library/CloudStorage/OneDrive-UNIR/Code/cont-gen-poc/venv/lib/python3.12/site-packages/azure/servicebus/aio/_servicebus_sender_async.py:222\u001b[0m, in \u001b[0;36mServiceBusSender._open\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    221\u001b[0m \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_handler\u001b[38;5;241m.\u001b[39mopen_async(connection\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_connection)\n\u001b[0;32m--> 222\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_handler\u001b[38;5;241m.\u001b[39mclient_ready_async():\n\u001b[1;32m    223\u001b[0m     \u001b[38;5;28;01mawait\u001b[39;00m asyncio\u001b[38;5;241m.\u001b[39msleep(\u001b[38;5;241m0.05\u001b[39m)\n", "File \u001b[0;32m~/Library/CloudStorage/OneDrive-UNIR/Code/cont-gen-poc/venv/lib/python3.12/site-packages/azure/servicebus/_pyamqp/aio/_client_async.py:328\u001b[0m, in \u001b[0;36mAMQPClientAsync.client_ready_async\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    320\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    321\u001b[0m \u001b[38;5;124;03mWhether the handler has completed all start up processes such as\u001b[39;00m\n\u001b[1;32m    322\u001b[0m \u001b[38;5;124;03mestablishing the connection, session, link and authentication, and\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    326\u001b[0m \u001b[38;5;124;03m:rtype: bool\u001b[39;00m\n\u001b[1;32m    327\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[0;32m--> 328\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mauth_complete_async():\n\u001b[1;32m    329\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;01mFalse\u001b[39;00m\n", "File \u001b[0;32m~/Library/CloudStorage/OneDrive-UNIR/Code/cont-gen-poc/venv/lib/python3.12/site-packages/azure/servicebus/_pyamqp/aio/_client_async.py:314\u001b[0m, in \u001b[0;36mAMQPClientAsync.auth_complete_async\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    308\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"Whether the authentication handshake is complete during\u001b[39;00m\n\u001b[1;32m    309\u001b[0m \u001b[38;5;124;03mconnection initialization.\u001b[39;00m\n\u001b[1;32m    310\u001b[0m \n\u001b[1;32m    311\u001b[0m \u001b[38;5;124;03m:return: Whether the authentication handshake is complete.\u001b[39;00m\n\u001b[1;32m    312\u001b[0m \u001b[38;5;124;03m:rtype: bool\u001b[39;00m\n\u001b[1;32m    313\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[0;32m--> 314\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_cbs_authenticator \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_cbs_authenticator\u001b[38;5;241m.\u001b[39mhandle_token():\n\u001b[1;32m    315\u001b[0m     \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_connection\u001b[38;5;241m.\u001b[39mlisten(wait\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_socket_timeout)\n", "File \u001b[0;32m~/Library/CloudStorage/OneDrive-UNIR/Code/cont-gen-poc/venv/lib/python3.12/site-packages/azure/servicebus/_pyamqp/aio/_cbs_async.py:275\u001b[0m, in \u001b[0;36mCBSAuthenticator.handle_token\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    274\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mauth_state \u001b[38;5;241m==\u001b[39m CbsAuthState\u001b[38;5;241m.\u001b[39mERROR:\n\u001b[0;32m--> 275\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m TokenAuthFailure(\n\u001b[1;32m    276\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_token_status_code,\n\u001b[1;32m    277\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_token_status_description,\n\u001b[1;32m    278\u001b[0m         encoding\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_encoding,  \u001b[38;5;66;03m# TODO: drop off all the encodings\u001b[39;00m\n\u001b[1;32m    279\u001b[0m     )\n\u001b[1;32m    280\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mauth_state \u001b[38;5;241m==\u001b[39m CbsAuthState\u001b[38;5;241m.\u001b[39mTIMEOUT:\n", "\u001b[0;31mTokenAuthFailure\u001b[0m: CBS Token authentication failed.\nStatus code: None", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[0;31mCancelledError\u001b[0m                            Traceback (most recent call last)", "Cell \u001b[0;32mIn[7], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mawait\u001b[39;00m generate_content_workflow\u001b[38;5;241m.\u001b[39mexecute(request)\n", "File \u001b[0;32m~/Library/CloudStorage/OneDrive-UNIR/Code/cont-gen-poc/src/api/workflows/indexes/generate_content/generate_content_workflow.py:44\u001b[0m, in \u001b[0;36mGenerateContentWorkflow.execute\u001b[0;34m(self, request)\u001b[0m\n\u001b[1;32m     39\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m Session(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_db_engine) \u001b[38;5;28;01mas\u001b[39;00m session:\n\u001b[1;32m     40\u001b[0m     processes \u001b[38;5;241m=\u001b[39m [\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_ai_tracer\u001b[38;5;241m.\u001b[39mstart_process(session, \n\u001b[1;32m     41\u001b[0m                                                AIProcessType\u001b[38;5;241m.\u001b[39mGENERATE_CONTENT_FOR_TOPIC, \n\u001b[1;32m     42\u001b[0m                                                request\u001b[38;5;241m.\u001b[39mindice_id, tema\u001b[38;5;241m.\u001b[39mid)\n\u001b[1;32m     43\u001b[0m                   \u001b[38;5;28;01mfor\u001b[39;00m tema \u001b[38;5;129;01min\u001b[39;00m temas]\n\u001b[0;32m---> 44\u001b[0m     \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_send_to_queue(processes)\n\u001b[1;32m     45\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m GenerateContentResponse(ai_processes\u001b[38;5;241m=\u001b[39m[TemaAIProcess(ai_process_type\u001b[38;5;241m=\u001b[39mp\u001b[38;5;241m.\u001b[39mprocess_type,\n\u001b[1;32m     46\u001b[0m                                                             ai_process_id\u001b[38;5;241m=\u001b[39mp\u001b[38;5;241m.\u001b[39mid, \u001b[38;5;66;03m# type: ignore\u001b[39;00m\n\u001b[1;32m     47\u001b[0m                                                             tema_id\u001b[38;5;241m=\u001b[39mp\u001b[38;5;241m.\u001b[39mtema_id)  \u001b[38;5;66;03m# type: ignore\u001b[39;00m\n\u001b[1;32m     48\u001b[0m                                              \u001b[38;5;28;01mfor\u001b[39;00m p \u001b[38;5;129;01min\u001b[39;00m processes])\n", "File \u001b[0;32m~/Library/CloudStorage/OneDrive-UNIR/Code/cont-gen-poc/src/api/workflows/indexes/generate_content/generate_content_workflow.py:58\u001b[0m, in \u001b[0;36mGenerateContentWorkflow._send_to_queue\u001b[0;34m(self, processes)\u001b[0m\n\u001b[1;32m     57\u001b[0m \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_send_to_queue\u001b[39m(\u001b[38;5;28mself\u001b[39m, processes: List[AIProcess]):\n\u001b[0;32m---> 58\u001b[0m     \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_async_service_bus_client\u001b[38;5;241m.\u001b[39mget_queue_sender(queue_name\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_queue_name) \u001b[38;5;28;01mas\u001b[39;00m sender:\n\u001b[1;32m     59\u001b[0m         \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m     60\u001b[0m             messages \u001b[38;5;241m=\u001b[39m [\n\u001b[1;32m     61\u001b[0m                 ServiceBusMessage(json\u001b[38;5;241m.\u001b[39mdumps({\n\u001b[1;32m     62\u001b[0m                     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mai_process_id\u001b[39m\u001b[38;5;124m\"\u001b[39m: process\u001b[38;5;241m.\u001b[39mid,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     67\u001b[0m                 \u001b[38;5;28;01mfor\u001b[39;00m process \u001b[38;5;129;01min\u001b[39;00m processes\n\u001b[1;32m     68\u001b[0m             ]\n", "File \u001b[0;32m~/Library/CloudStorage/OneDrive-UNIR/Code/cont-gen-poc/venv/lib/python3.12/site-packages/azure/servicebus/aio/_servicebus_sender_async.py:160\u001b[0m, in \u001b[0;36mServiceBusSender.__aenter__\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    155\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_shutdown\u001b[38;5;241m.\u001b[39mis_set():\n\u001b[1;32m    156\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[1;32m    157\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mThe handler has already been shutdown. Please use ServiceBusClient to \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    158\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcreate a new instance.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    159\u001b[0m     )\n\u001b[0;32m--> 160\u001b[0m \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_open_with_retry()\n\u001b[1;32m    161\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\n", "File \u001b[0;32m~/Library/CloudStorage/OneDrive-UNIR/Code/cont-gen-poc/venv/lib/python3.12/site-packages/azure/servicebus/aio/_base_handler_async.py:410\u001b[0m, in \u001b[0;36mBaseHandler._open_with_retry\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    409\u001b[0m \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_open_with_retry\u001b[39m(\u001b[38;5;28mself\u001b[39m):\n\u001b[0;32m--> 410\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_do_retryable_operation(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_open)\n", "File \u001b[0;32m~/Library/CloudStorage/OneDrive-UNIR/Code/cont-gen-poc/venv/lib/python3.12/site-packages/azure/servicebus/aio/_base_handler_async.py:286\u001b[0m, in \u001b[0;36mBaseHandler._do_retryable_operation\u001b[0;34m(self, operation, timeout, **kwargs)\u001b[0m\n\u001b[1;32m    284\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m error \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mlast_exception\u001b[39;00m\n\u001b[1;32m    285\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m last_exception \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m\n\u001b[0;32m--> 286\u001b[0m \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backoff(\n\u001b[1;32m    287\u001b[0m     retried_times\u001b[38;5;241m=\u001b[39mretried_times,\n\u001b[1;32m    288\u001b[0m     last_exception\u001b[38;5;241m=\u001b[39mlast_exception,\n\u001b[1;32m    289\u001b[0m     abs_timeout_time\u001b[38;5;241m=\u001b[39mabs_timeout_time,\n\u001b[1;32m    290\u001b[0m )\n", "File \u001b[0;32m~/Library/CloudStorage/OneDrive-UNIR/Code/cont-gen-poc/venv/lib/python3.12/site-packages/azure/servicebus/aio/_base_handler_async.py:305\u001b[0m, in \u001b[0;36mBaseHandler._backoff\u001b[0;34m(self, retried_times, last_exception, abs_timeout_time, entity_name)\u001b[0m\n\u001b[1;32m    296\u001b[0m backoff \u001b[38;5;241m=\u001b[39m _get_backoff_time(\n\u001b[1;32m    297\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_config\u001b[38;5;241m.\u001b[39mretry_mode,\n\u001b[1;32m    298\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_config\u001b[38;5;241m.\u001b[39mretry_backoff_factor,\n\u001b[1;32m    299\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_config\u001b[38;5;241m.\u001b[39mretry_backoff_max,\n\u001b[1;32m    300\u001b[0m     retried_times,\n\u001b[1;32m    301\u001b[0m )\n\u001b[1;32m    302\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m backoff \u001b[38;5;241m<\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_config\u001b[38;5;241m.\u001b[39mretry_backoff_max \u001b[38;5;129;01mand\u001b[39;00m (\n\u001b[1;32m    303\u001b[0m     abs_timeout_time \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;129;01mor\u001b[39;00m (backoff \u001b[38;5;241m+\u001b[39m time\u001b[38;5;241m.\u001b[39mtime()) \u001b[38;5;241m<\u001b[39m\u001b[38;5;241m=\u001b[39m abs_timeout_time\n\u001b[1;32m    304\u001b[0m ):\n\u001b[0;32m--> 305\u001b[0m     \u001b[38;5;28;01mawait\u001b[39;00m asyncio\u001b[38;5;241m.\u001b[39msleep(backoff)\n\u001b[1;32m    306\u001b[0m     _LOGGER\u001b[38;5;241m.\u001b[39minfo(\n\u001b[1;32m    307\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m%r\u001b[39;00m\u001b[38;5;124m has an exception (\u001b[39m\u001b[38;5;132;01m%r\u001b[39;00m\u001b[38;5;124m). Retrying...\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m    308\u001b[0m         entity_name,\n\u001b[1;32m    309\u001b[0m         last_exception,\n\u001b[1;32m    310\u001b[0m     )\n\u001b[1;32m    311\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n", "File \u001b[0;32m/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/tasks.py:655\u001b[0m, in \u001b[0;36msleep\u001b[0;34m(delay, result)\u001b[0m\n\u001b[1;32m    651\u001b[0m h \u001b[38;5;241m=\u001b[39m loop\u001b[38;5;241m.\u001b[39mcall_later(delay,\n\u001b[1;32m    652\u001b[0m                     futures\u001b[38;5;241m.\u001b[39m_set_result_unless_cancelled,\n\u001b[1;32m    653\u001b[0m                     future, result)\n\u001b[1;32m    654\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 655\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;01mawait\u001b[39;00m future\n\u001b[1;32m    656\u001b[0m \u001b[38;5;28;01mfinally\u001b[39;00m:\n\u001b[1;32m    657\u001b[0m     h\u001b[38;5;241m.\u001b[39mcancel()\n", "\u001b[0;31mCancelledError\u001b[0m: "]}], "source": ["result = await generate_content_workflow.execute(request)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["[TemaAIProcess(ai_process_type='GENERATE_CONTENT_FOR_TOPIC', ai_process_id=82, tema_id=1),\n", " TemaAIProcess(ai_process_type='GENERATE_CONTENT_FOR_TOPIC', ai_process_id=83, tema_id=2),\n", " TemaAIProcess(ai_process_type='GENERATE_CONTENT_FOR_TOPIC', ai_process_id=84, tema_id=3),\n", " TemaAIProcess(ai_process_type='GENERATE_CONTENT_FOR_TOPIC', ai_process_id=85, tema_id=4),\n", " TemaAIProcess(ai_process_type='GENERATE_CONTENT_FOR_TOPIC', ai_process_id=86, tema_id=5),\n", " TemaAIProcess(ai_process_type='GENERATE_CONTENT_FOR_TOPIC', ai_process_id=87, tema_id=6),\n", " TemaAIProcess(ai_process_type='GENERATE_CONTENT_FOR_TOPIC', ai_process_id=88, tema_id=7),\n", " TemaAIProcess(ai_process_type='GENERATE_CONTENT_FOR_TOPIC', ai_process_id=89, tema_id=8)]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["result.ai_processes"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Before generating, check if there is already a succesfull process for generating content for that topic."]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}