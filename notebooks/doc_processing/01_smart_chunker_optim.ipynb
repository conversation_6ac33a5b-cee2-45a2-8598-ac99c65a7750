{"cells": [{"cell_type": "code", "execution_count": 1, "id": "30806e5a", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import sys\n", "from pathlib import Path\n", "\n", "parent_dir =Path.cwd().parent.parent\n", "sys.path.append(str(parent_dir))\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv()"]}, {"cell_type": "code", "execution_count": 2, "id": "05a2032e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Looking in links: /var/folders/4b/djmst1cd65390hn17bdx76km0000gp/T/tmp3fyzcdm4\n", "Requirement already satisfied: pip in /Users/<USER>/Code/ia_gestorcontenidosiagen_be/.venv/lib/python3.12/site-packages (25.1.1)\n", "Requirement already satisfied: pip in /Users/<USER>/Code/ia_gestorcontenidosiagen_be/.venv/lib/python3.12/site-packages (25.1.1)\n", "Collecting en-core-web-sm==3.8.0\n", "  Downloading https://github.com/explosion/spacy-models/releases/download/en_core_web_sm-3.8.0/en_core_web_sm-3.8.0-py3-none-any.whl (12.8 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m12.8/12.8 MB\u001b[0m \u001b[31m37.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25h\u001b[38;5;2m✔ Download and installation successful\u001b[0m\n", "You can now load the package via spacy.load('en_core_web_sm')\n", "Collecting es-core-news-sm==3.8.0\n", "  Downloading https://github.com/explosion/spacy-models/releases/download/es_core_news_sm-3.8.0/es_core_news_sm-3.8.0-py3-none-any.whl (12.9 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m12.9/12.9 MB\u001b[0m \u001b[31m33.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m00:01\u001b[0m0:01\u001b[0m\n", "\u001b[?25h\u001b[38;5;2m✔ Download and installation successful\u001b[0m\n", "You can now load the package via spacy.load('es_core_news_sm')\n"]}], "source": ["! python -m ensurepip --upgrade \\\n", "&& python -m pip install --upgrade pip \\\n", "&& python -m spacy download en_core_web_sm \\\n", "&& python -m spacy download es_core_news_sm"]}, {"cell_type": "code", "execution_count": 2, "id": "bc816918", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-24 18:02:30,425 - logger - INFO - Initial user 'Unir demo' already exists.\n", "2025-06-24 18:02:30,701 - logger - ERROR - Error: <PERSON>ript execution failed with return code 1\n", "2025-06-24 18:02:30,702 - logger - ERROR - Output: \n", "2025-06-24 18:02:30,702 - logger - ERROR - Error Message: /Users/<USER>/Code/ia_gestorcontenidosiagen_be/.venv/bin/python: Error while finding module specification for 'src.scripts.create_prompts' (ModuleNotFoundError: No module named 'src')\n", "\n"]}], "source": ["from src.api.common.tools.loaders import WebsiteLoader\n", "from src.api.workflows.document_ingestion.chunking import SmartSemanticChunker\n", "from src.api.common.dependency_container import DependencyContainer\n", "\n", "dependency_container = DependencyContainer.initialize(False)"]}, {"cell_type": "markdown", "id": "dd713c57", "metadata": {}, "source": ["Si tiene lo máximo async, por este tipo de cosas como que la lectura tarda, beneficia maximizar la concurrencia. El unico problema es que ahora hace operaciones de db uno a uno. Y podría dar un problema de muchas conexiones a la vez."]}, {"cell_type": "code", "execution_count": 3, "id": "f3715364", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://r.jina.ai/ \"HTTP/1.1 200 OK\"\n"]}, {"data": {"text/plain": ["<src.api.common.tools.loaders.WebsiteLoader at 0x13eb4ba70>"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["doc_loader = WebsiteLoader(url=\"https://en.wikipedia.org/wiki/List_of_Nvidia_graphics_processing_units\")\n", "await doc_loader.extract_all(extract_text=True)"]}, {"cell_type": "code", "execution_count": 4, "id": "c61a7b42", "metadata": {}, "outputs": [], "source": ["results = await doc_loader.get_results()"]}, {"cell_type": "code", "execution_count": 6, "id": "f2577be9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["### GeForce 6 (6xxx) series\n", "[[edit](https://en.wikipedia.org/w/index.php?title=List_of_Nvidia_graphics_processing_units&action=edit&section=9 \"Edit section: GeForce 6 (6xxx) series\")]\n", "*   All models support [Direct3D](https://en.wikipedia.org/wiki/Direct3D \"Direct3D\") 9.0c and [OpenGL](https://en.wikipedia.org/wiki/OpenGL \"OpenGL\") 2.1\n", "*   All models support Transparency [AA](https://en.wikipedia.org/wiki/Spatial_anti-aliasing \"Spatial anti-aliasing\") (starting with version 91.47 of the ForceWare drivers) and PureVideo\n", "*   ![Image 1: {\\textstyle Gflops={\\bigl (}{\\text{PixelShader}}\\times 12+{\\text{VertexShader}}\\times 8{\\bigr )}\\times {\\frac {\\text{clock (MHz)}}{1000}}}](https://wikimedia.org/api/rest_v1/media/math/render/svg/e728156249ee0a6e59c4e82857693972ee37499f)[[27]](https://en.wikipedia.org/wiki/List_of_Nvidia_graphics_processing_units#cite_note-:17-35)[[28]](https://en.wikipedia.org/wiki/List_of_Nvidia_graphics_processing_units#cite_note-:16-36)\n", "| Model | Launch | [Code name](https://en.wikipedia.org/wiki/Code_name \"Code name\") | [Fab](https://en.wikipedia.org/wiki/Semiconductor_device_fabrication \"Semiconductor device fabrication\") ([nm](https://en.wikipedia.org/wiki/Nanometer \"Nanometer\"))[[2]](https://en.wikipedia.org/wiki/List_of_Nvidia_graphics_processing_units#cite_note-vintage3d-2) | Transistors (million) Die size (mm 2) | [Bus](https://en.wikipedia.org/wiki/Computer_bus \"Computer bus\")[interface](https://en.wikipedia.org/wiki/I/O_interface \"I/O interface\") | Core clock ([MHz](https://en.wikipedia.org/wiki/Hertz \"Hertz\")) | Memory clock ([MHz](https://en.wikipedia.org/wiki/Hertz \"Hertz\")) | Core config[[a]](https://en.wikipedia.org/wiki/List_of_Nvidia_graphics_processing_units#cite_note-geforce_6_1-37) | Memory | [Fillrate](https://en.wikipedia.org/wiki/Fillrate \"Fillrate\") | Performance[[28]](https://en.wikipedia.org/wiki/List_of_Nvidia_graphics_processing_units#cite_note-:16-36) (GFLOPS)[[27]](https://en.wikipedia.org/wiki/List_of_Nvidia_graphics_processing_units#cite_note-:17-35) | TDP (Watts) |\n", "| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |\n", "| Size ([MiB](https://en.wikipedia.org/wiki/Mebibyte \"Mebibyte\")) | Bandwidth ([GB/s](https://en.wikipedia.org/wiki/Data-rate_units#Gigabyte_per_second \"Data-rate units\")) | Bus type | Bus width ([bit](https://en.wikipedia.org/wiki/Bit \"Bit\")) | MOperations/s | MPixels/s | MTexels/s | MVertices/s |\n", "| GeForce 6100 + nForce 410 | October 20, 2005 | MCP51 | [TSMC](https://en.wikipedia.org/wiki/TSMC \"TSMC\")[90 nm](https://en.wikipedia.org/wiki/90_nm \"90 nm\") |  | [HyperTransport](https://en.wikipedia.org/wiki/HyperTransport \"HyperTransport\") | 425 | 100–200(DDR) 200–533(DDR2) | 2:1:2:1 | Up to 256 system RAM | 1.6–6.4(DDR) 3.2–17.056(DDR2) | DDR DDR2 | 64 128 | 850 | 425 | 850 | 106.25 | 13.6 | ? |\n", "| GeForce 6150 SE + nForce 430 | June 2006 | MCP61 |  | 200 400[_[citation needed](https://en.wikipedia.org/wiki/Wikipedia:Citation\\_needed \"Wikipedia:Citation needed\")_] | 3.2 16.0[_[citation needed](https://en.wikipedia.org/wiki/Wikipedia:Citation\\_needed \"Wikipedia:Citation needed\")_] | DDR2 | 13.6 | ? |\n", "| GeForce 6150 LE + nForce 430 | MCP61 | 100–200(DDR) 200–533(DDR2) | 1.6–6.4(DDR) 3.2–17.056(DDR2) | DDR DDR2 | 13.6 | ? |\n", "\n"]}], "source": ["print(results['text_segments'][9].text)"]}, {"cell_type": "code", "execution_count": 5, "id": "acd79b94", "metadata": {}, "outputs": [], "source": ["from src.api.workflows.document_ingestion.embedding_functions import OpenAIEmbeddingFunction\n", "import os"]}, {"cell_type": "markdown", "id": "6cb860b6", "metadata": {}, "source": ["Time this stuff"]}, {"cell_type": "code", "execution_count": 6, "id": "3b4f555e", "metadata": {}, "outputs": [], "source": ["results = await doc_loader.get_results()"]}, {"cell_type": "code", "execution_count": 7, "id": "8bac6d86", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Language Lenguaje.EN\n", "Embedding function present\n", "Start split text\n", "Instance is list\n", "[Start] text splitter spacy\n", "Len chunks in split text is: 1\n", "[End] text splitter spacy\n", "[START] Map sentences to segments\n", "[END] Map sentences to segments\n", "[START] Vectorize sentences\n", "Total len utf8 sents is 7032\n", "Processing 4 batches concurrently with max 4 workers\n", "[START] Vectorizing batch 1/4, size: 2048\n", "[START] Vectorizing batch 2/4, size: 2048\n", "[START] Vectorizing batch 3/4, size: 2048\n", "[START] Vectorizing batch 4/4, size: 888\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[END] Vectorizing batch 4/4\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[END] Vectorizing batch 1/4\n", "[END] Vectorizing batch 3/4\n", "[END] Vectorizing batch 2/4\n", "[END] Vectorizing sentences in batch end (threaded)\n", "[END] Vectorize sentences\n", "[START] Tokenize Sentences\n", "[END] Tokenize Sentences\n"]}], "source": ["if \"text_segments\" in results:\n", "    for segment in results[\"text_segments\"]:\n", "        if hasattr(segment, \"text\"):\n", "            segment.text = str(segment.text)\n", "        if hasattr(segment, \"metadata\") and segment.metadata:\n", "            segment.metadata = segment.metadata\n", "    splitted_chunks, chunker = SmartSemanticChunker.from_text_segments(\n", "        text_segments=results[\"text_segments\"],\n", "        embedding_function=OpenAIEmbeddingFunction(\n", "        api_key=os.getenv(\"OpenaiApiKey\"),\n", "        model_name=\"text-embedding-3-large\",\n", "        \n", "    ),batch_size = 2048\n", ")"]}, {"cell_type": "code", "execution_count": 13, "id": "d9e69d50", "metadata": {}, "outputs": [{"data": {"text/plain": ["TextSegment(text='*   _API support section_\\n*   _[Direct3D](https://en.wikipedia.org/wiki/Direct3D \"Direct3D\")_ – Maximum version of Direct3D fully supported.\\n*   _[OpenGL](https://en.wikipedia.org/wiki/OpenGL\"OpenGL\")_ – Maximum version of OpenGL fully supported.\\n*   [OpenCL](https://en.wikipedia.org/wiki/OpenCL \"OpenCL\") – Maximum version of OpenCL fully supported.\\n*   [Vulkan](https://en.wikipedia.org/wiki/Vulkan_(API)\"Vulkan (API)\") – Maximum version of Vulkan fully supported.\\n*   [CUDA](https://en.wikipedia.org/wiki/CUDA \"CUDA\") - Maximum version of Cuda fully supported.\\n*   _Features_ –Added features that are not standard as a part of the two graphics libraries.\\n', page_number=1, metadata={'page_start': 1, 'page_end': 1, 'total_tokens': 171}, file_name='https://en.wikipedia.org/wiki/List_of_Nvidia_graphics_processing_units', start_char=0, end_char=5078, tokens=171)"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["splitted_chunks[4]"]}, {"cell_type": "markdown", "id": "8ee6ad91", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": null, "id": "48c99ce3", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}