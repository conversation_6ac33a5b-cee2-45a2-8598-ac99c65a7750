{"cells": [{"cell_type": "code", "execution_count": 21, "id": "3bb5b171", "metadata": {}, "outputs": [], "source": ["import asyncio\n", "import json\n", "import os\n", "import re\n", "import sys\n", "from logging import getLogger\n", "from pathlib import Path\n", "\n", "from dotenv import load_dotenv\n", "from openai import AsyncOpenAI\n", "from pydantic import BaseModel, Field\n", "\n", "\n", "\n", "parent_dir = Path.cwd().parent.parent\n", "sys.path.append(str(parent_dir))"]}, {"cell_type": "code", "execution_count": 22, "id": "a3420939", "metadata": {}, "outputs": [], "source": ["from src.api.common.services.search_agent import SearchAgent\n", "from src.api.common.services.url_extractor_engine import <PERSON><PERSON>Extract<PERSON>, TavilyExtractor\n"]}, {"cell_type": "code", "execution_count": 23, "id": "8edac686", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-08-01 12:55:20,951 - logger - INFO - Initial user 'Unir demo' already exists.\n", "2025-08-01 12:55:23,752 - logger - INFO - Attempt 1: Phoenix endpoint not reachable. Error: HTTPConnectionPool(host='phoenix', port=6006): Max retries exceeded with url: /v1/traces (Caused by NameResolutionError(\"<urllib3.connection.HTTPConnection object at 0x000001EF6F3C4EF0>: Failed to resolve 'phoenix' ([Errno 11001] getaddrinfo failed)\"))\n", "2025-08-01 12:55:31,469 - logger - INFO - Attempt 2: Phoenix endpoint not reachable. Error: HTTPConnectionPool(host='phoenix', port=6006): Max retries exceeded with url: /v1/traces (Caused by NameResolutionError(\"<urllib3.connection.HTTPConnection object at 0x000001EF6F3C4860>: Failed to resolve 'phoenix' ([Errno 11001] getaddrinfo failed)\"))\n", "2025-08-01 12:55:39,174 - logger - INFO - Attempt 3: Phoenix endpoint not reachable. Error: HTTPConnectionPool(host='phoenix', port=6006): Max retries exceeded with url: /v1/traces (Caused by NameResolutionError(\"<urllib3.connection.HTTPConnection object at 0x000001EF6F3C5C40>: Failed to resolve 'phoenix' ([Errno 11001] getaddrinfo failed)\"))\n", "2025-08-01 12:55:46,877 - logger - INFO - Attempt 4: Phoenix endpoint not reachable. Error: HTTPConnectionPool(host='phoenix', port=6006): Max retries exceeded with url: /v1/traces (Caused by NameResolutionError(\"<urllib3.connection.HTTPConnection object at 0x000001EF6F3C68A0>: Failed to resolve 'phoenix' ([Errno 11001] getaddrinfo failed)\"))\n", "2025-08-01 12:55:54,593 - logger - INFO - Attempt 5: Phoenix endpoint not reachable. Error: HTTPConnectionPool(host='phoenix', port=6006): Max retries exceeded with url: /v1/traces (Caused by NameResolutionError(\"<urllib3.connection.HTTPConnection object at 0x000001EF6F3C71D0>: Failed to resolve 'phoenix' ([Errno 11001] getaddrinfo failed)\"))\n", "2025-08-01 12:55:59,594 - logger - INFO - PHOENIX_COLLECTOR_ENDPOINT is not reachable after multiple attempts. Skipping instrumentation.\n", "2025-08-01 12:56:01,423 - logger - INFO - Created prompts successfully\n"]}], "source": ["\n", "from src.api.common.services import BraveSearchEngine, TavilySearchEngine, JinaSearchEngine\n", "from ia_gen_core.prompts import PromptManager\n", "from src.api.common.dependency_container import DependencyContainer \n", "logger = getLogger(__name__)\n", "\n", "load_dotenv()\n", "os.environ[\"DbHost\"] = \"localhost\"\n", "os.environ[\"DbPort\"] = \"5436\"\n", "DependencyContainer.initialize()"]}, {"cell_type": "code", "execution_count": 24, "id": "4e38bc11", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n"]}, {"data": {"text/plain": ["'Hello! How can I assist you today?'"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["client = DependencyContainer.get_async_openai_client()\n", "\n", "response = await client.responses.create(\n", "    model=\"gpt-4.1-nano\", input=\"Hello, world!\", max_output_tokens=20)\n", "\n", "response.output_text"]}, {"cell_type": "code", "execution_count": 4, "id": "5aec2a7a", "metadata": {}, "outputs": [], "source": ["from agents import ModelSettings\n", "\n", "\n", "prompt_manager = PromptManager(db_engine=DependencyContainer.get_database_engine())\n", "\n", "openai_api_key = os.getenv(\"OpenaiApiKey\")\n", "openai_client = AsyncOpenAI(api_key = openai_api_key)\n", "tavily_api_key = os.getenv(\"TavilySearchApiKey\")\n", "brave_api_key = os.getenv(\"BraveSearchApiKey\")\n", "jina_api_key = os.getenv(\"JinaApiKey\")\n", "top_k = 5\n", "\n", "brave_search, tavili_search, jina_search = BraveSearchE<PERSON><PERSON>(logger, brave_api_key), <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(logger, tavily_api_key), <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(logger, jina_api_key)\n", "jina_extractor = JinaExtractor(api_key=jina_api_key, logger=logger, timeout=10)\n", "tavily_extractor = TavilyExtractor(api_key=tavily_api_key, logger=logger)\n", "\n", "\n", "search_agent = SearchAgent(openai_client, jina_search, jina_search, jina_extractor, prompt_manager, logger=logger, model= \"o4-mini\", model_settings=ModelSettings(parallel_tool_calls=True, tool_choice=\"auto\", reasoning={\"effort\":\"low\"}), top_extracts=4)"]}, {"cell_type": "code", "execution_count": 8, "id": "c2eeea03", "metadata": {}, "outputs": [], "source": ["from src.api.common.services.search_agent import AgentResult\n", "\n", "\n", "async def extract_source(text: str) -> tuple[AgentResult, str] :\n", "\n", "    response = await openai_client.responses.create(input=text, model=\"gpt-4.1-nano\", temperature=0, max_output_tokens=100, instructions = \"extrae un enunciado que resuma la tematica principal del texto que se te ha pasado, y que sea lo más breve posible, sin perder el sentido del texto original\")\n", "\n", "\n", "    result = await search_agent.run(f\"```text\\n{response.output_text}\\n```\", f\"Tu tarea es encontrar fuentes que esten relacionadas con el texto para añadirlas a la bibliografia del texto.\\n\\nApunta a minimizar el numero de busquedas, la velocidad es un requisito importante. Nunca hagas mas de una una busqueda a no ser que no encunetres resultados, quedate con el primer resultado valido que encuentres y realiza busqueda con fuentes tanto academicas como no academicas\", 1)\n", "\n", "    response = await openai_client.responses.create(input=f\"# TEXTO\\n```text\\n{text}\\n```\\n\\n# FUENTE\\n\\n```json\\n{json.dumps(result.model_dump())}\\n```\", model=\"gpt-4.1-mini\", temperature=0, max_output_tokens=500, instructions = \"Genera una explicacion de porque la fuente puede justificar el texto de la entrada en no mas de dos oraciones, referenciando los temas en comun.\")\n", "\n", "\n", "    return result, response.output_text"]}, {"cell_type": "code", "execution_count": 13, "id": "8dcbd4d0", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:__main__:Extracting content from 1 URLs using JinaExtractor with httpx\n", "INFO:__main__:Extracting content from 1 URLs using JinaExtractor with httpx\n", "INFO:__main__:Extracting content from 1 URLs using JinaExtractor with httpx\n", "INFO:__main__:Extracting content from 1 URLs using JinaExtractor with httpx\n", "INFO:httpx:HTTP Request: POST https://r.jina.ai/ \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://r.jina.ai/ \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://r.jina.ai/ \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://r.jina.ai/ \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["RESULTADO DE LA BÚSQUEDA:\n", " {\n", "  \"documents\": [\n", "    {\n", "      \"title\": \"A Brief Analysis of Modern Game Theory Computation\",\n", "      \"url\": \"https://www.numberanalytics.com/blog/a-brief-analysis-modern-game-theory-computation\",\n", "      \"authors\": [\n", "        \"<PERSON>-<PERSON>\"\n", "      ],\n", "      \"date\": null,\n", "      \"summary\": [\n", "        \"Modern game theory has evolved through computational advancements enabling analysis of complex strategic scenarios.\",\n", "        \"Key computational methods include dynamic programming, Monte Carlo simulations, evolutionary algorithms, and iterative best response dynamics.\",\n", "        \"Nash equilibrium computation uses iterative best responses, regret minimization, and adaptive learning algorithms.\",\n", "        \"Comparative studies highlight the scalability and practical advantages of computational methods over classical approaches.\",\n", "        \"Real-world applications span economics, political science, and technology, including auctions, market competition, voting systems, cybersecurity, and smart infrastructure.\",\n", "        \"Future prospects involve AI-driven automated negotiation systems and smart city optimizations.\",\n", "        \"Integration of classical and computational methods enhances strategic decision-making and problem-solving capabilities across disciplines.\"\n", "      ],\n", "      \"lenguaje\": \"EN\",\n", "      \"is_pay_walled\": false,\n", "      \"is_info_accesbile\": true\n", "    }\n", "  ]\n", "} \n", "\n", "\n", "La fuente justifica la sistematización y combinación de métodos en la entrada al destacar que la teoría de juegos moderna integra enfoques computacionales y clásicos para analizar escenarios estratégicos complejos, enfatizando la necesidad de métodos técnicos y cuantitativos junto con validaciones conceptuales para asegurar rigor y aplicabilidad práctica. Esta convergencia de técnicas refleja la progresión metodológica y la complementariedad señaladas en el texto, donde se busca un análisis robusto y contextualizado mediante la triangulación de múltiples métodos.\n"]}], "source": ["text = \"\"\"La sistematización de los métodos y técnicas presentados facilita su aplicación práctica y permite una **selección informada** según las características específicas de cada situación analítica. La siguiente tabla proporciona una referencia estructurada que integra todos los enfoques de análisis y validación desarrollados en este epígrafe.\n", "\n", "<tabla>\n", "| **Método/Técnica** | **Objetivo** | **Procedimiento Básico** | **Cuándo Aplicar** | **Limitaciones Principales** |\n", "|:-------------------|:-------------|:-------------------------|:-------------------|:------------------------------|\n", "| **Verificación de Equilibrios** | Confirmar que las estrategias satisfacen condiciones de equilibrio | • Identificar condiciones de equilibrio<br>• Comprobar mejores respuestas<br>• Validar restricciones | Inmediatamente después de encontrar solución candidata | • Solo verifica condiciones matemáticas<br>• No evalúa estabilidad práctica<br>• Limitado a equilibrios exactos |\n", "| **Análisis <PERSON>ustez** | Evaluar comportamiento ante perturbaciones menores | • Definir perturbaciones ε<br>• Analizar estabilidad local<br>• Evaluar convergencia | Antes de implementar estrategias en entornos reales | • Requiere definición subjetiva de perturbaciones<br>• Limitado a cambios pequeños<br>• Complejidad computacional alta |\n", "| **Análisis de Sensibilidad** | Examinar efectos de cambios paramétricos | • Identificar parámetros críticos<br>• Aplicar diferenciación paramétrica<br>• Calcular elasticidades<br>• Simular escenarios | Planificación estratégica a largo plazo | • Asume continuidad y diferenciabilidad<br>• Limitado a cambios paramétricos graduales<br>• Puede ignorar efectos no lineales |\n", "| **Comparación Teórica** | Contrastar con resultados establecidos | • Identificar casos benchmark<br>• Comparar soluciones obtenidas<br>• Calibrar modelos | Cuando existen referencias teóricas conocidas | • Disponibilidad limitada de casos comparables<br>• Diferencias contextuales pueden invalidar comparación<br>• Sesgos hacia soluciones conocidas |\n", "| **Revisión de Supuestos** | Evaluar validez de premisas del modelo | • Examinar racionalidad perfecta<br>• Evaluar información completa<br>• Analizar estructura temporal | Siempre, como validación final integral | • Subjetividad en evaluación de realismo<br>• Dificultad para cuantificar impacto de violaciones<br>• Trade-off entre realismo y tractabilidad |\n", "</tabla>\n", "\n", "*Tabla 1. Métodos de análisis y técnicas de validación en teoría de juegos: características, aplicaciones y limitaciones.*\n", "\n", "Esta sistematización revela que los métodos de análisis se enfocan en aspectos **técnicos y cuantitativos**, mientras que las técnicas de validación abordan dimensiones **conceptuales y contextuales**. La aplicación efectiva requiere combinar ambos enfoques de manera secuencial y complementaria.\n", "\n", "La **progresión metodológica** sugerida inicia con la verificación matemática básica, continúa con análisis de robustez y sensibilidad según el contexto de aplicación, y culmina con validación teórica y revisión crítica de supuestos. Esta secuencia garantiza tanto rigor técnico como relevancia práctica en el análisis de resultados de teoría de juegos.\n", "\n", "Las limitaciones identificadas subrayan la importancia de **triangular múltiples métodos** y mantener una perspectiva crítica sobre los alcances y restricciones de cada enfoque analítico en el proceso de toma de decisiones estratégicas.\"\"\"\n", "\n", "result, reason = await extract_source(text)\n", "\n", "print(\"RESULTADO DE LA BÚSQUEDA:\\n\", json.dumps(result.model_dump(), indent=2, ensure_ascii=False), \"\\n\\n\")\n", "\n", "print(reason)"]}, {"cell_type": "code", "execution_count": 14, "id": "43b570ef", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"https://pdfs.semanticscholar.org/10ae/de190454ccb7f078dfaa6434262e1379f4bc.pdf\": \"games Article \\n\\n# Validating Game-Theoretic Models of Terrorism: Insights from Machine Learning \\n\\nJames T. Bang 1, <PERSON><PERSON> 2, * and <PERSON><PERSON><PERSON><PERSON> 3\\u0001\\u0002\\u0003\\u0001\\u0004\\u0005\\u0006\\u0007\\b \\u0001\\n\\n\\u0001\\u0002\\u0003\\u0004\\u0005\\u0006\\u0007                   \\n\\n> Citation: Bang, J.T.; Basuchoudhary, A.; Mitra, A. Validating Game-Theoretic Models of Terrorism: Insights from Machine Learning.\\n> Games 2021 ,12 , 54. https://doi.org/ 10.3390/g12030054 Academic Editors: <PERSON><PERSON>, <PERSON> and <PERSON> Received: 28 April 2021 Accepted: 23 June 2021 Published: 30 June 2021\\n> Publisher’s Note: MDPI stays neutral with regard to jurisdictional claims in published maps and institutional affil-iations.\\n> Copyright: ©2021 by the authors. Licensee MDPI, Basel, Switzerland. This article is an open access article distributed under the terms and conditions of the Creative Commons Attribution (CC BY) license (https:// creativecommons.org/licenses/by/ 4.0/).\\n> 1\\n\\nDepartment of Economics, St. Ambrose University, Davenport, IA 52803, USA; <EMAIL>  \\n\\n> 2\\n\\nDepartment of Economics and Business, Virginia Military Institute, Lexington, VA 24450, USA  \\n\\n> 3\\n\\nEconomics Program, Bard College, Annandale-On-Hudson, NY 12504, USA; <EMAIL> \\n\\n* Correspondence: <EMAIL> \\n\\nAbstract: There are many competing game-theoretic analyses of terrorism. Most of these models suggest nonlinear relationships between terror attacks and some variable of interest. However, to date, there have been very few attempts to empirically sift between competing models of terrorism or identify nonlinear patterns. We suggest that machine learning can be an effective way of undertaking both. This feature can help build more salient game-theoretic models to help us understand and prevent terrorism. \\n\\nKeywords: machine learning; terrorism; game theory \\n\\n1. Introduction \\n\\nGame-theoretic models of terrorism are a useful tool in understanding the interactions between states and terrorist groups, the organization of terror groups, and the coordination of counterterrorism efforts [ 1]. These models provide insights and testable hypotheses. Yet, far too often, many of these hypotheses remain untested. Even when model-generated hypotheses are tested, the focus is on the effect of a particular theory-generated variable on, say, the likelihood of terrorism. This testing may explore causal channels. However, because empirical evidence using traditional econometric channels is not organized to check for relative salience, the importance of a correlation or even causal effect relative to other such effects is unknown. This inability of traditional econometric techniques to check for relative salience in an organized way makes it hard to sift among competing theoretical models. This sort of sifting is essential for policy. A nation plagued by terrorist attacks needs to know which theoretical model provides the largest counterterrorism impact. Further, it is essential to know how a particular variable may affect terrorism. Game-theoretic models have a key strength. They show comparative static or even dynamic (particularly in evolutionary models) equilibrium shifts. Thus, variables that affect terror-ism may do so in nonlinear ways. Traditional econometric tests focused on parametric point estimates are not built to pick up these equilibrium shifts. Nonlinearity, of course, can often be imposed in econometrics. However, this forces the researcher to guess where these nonlinearities may be (squaring the variable, for example, defines a particular shape on a relationship that may or may not be accurate). Data problems also plague traditional econometric tests of game-theoretic models. Terrorism is, thankfully, rare. However, empirically, this requires heroic assumptions about the distribution of data when making inferences. Even without the rarity aspect, hypotheses testing for significance requires assumptions about the underlying distribution of data that are swept under the rug. Game theoretic models highlight strategic interaction between agents, which are often endogenous. Thus, assumptions about the distribution of data are necessary to estimate efficient and unbiased estimators. Then, there is the issue of model specification. Model specification is often subject to a researcher’s explicit and implicit biases. All of this contributes to charges of p hacking in academic research [ 2 ]. \\n\\nGames 2021 , 12 , 54. https://doi.org/10.3390/g12030054 https://www.mdpi.com/journal/games Games 2021 , 12 , 54 2 of 20 \\n\\nWe suggest that the full power of game theoretical insights can be validated by machine learning. This is particularly important for science in cases such as the study of terrorism, where randomized control trials are impossible or unethical. Therefore, a key contribution of this paper is to introduce the emerging methodology of machine learning to the game-theoretic study of terrorism that can, to a great extent, overcome the limitations of classical regression-based methods [3]. This paper will identify a methodology to identify a robust list of factors that contribute to an increased risk of terrorism and would inform the government on precisely what information to monitor in order to be able to anticipate terrorist events before they occur and would hence contribute to the design of counterterrorism policy at the strategic level. This set of variables can be the starting point for further causal analysis [ 4]. Our approach will rank variables by predictive importance. Counterterrorism policy is by definition something whose effect happens in the future. Thus, more predictively important variables can be better candidates for policy. Predictively important variables are not necessarily causal. However, all causal variables should predict. We can identify variables that do not predict well. This reduces the likelihood that these variables are causal. Thus, theoretical models that suggest such variables matter for terrorism are less likely to be explanations for terrorism. To the best of our knowledge this sort of approach is new in the literature on terrorism. Game-theoretic models predict such nonlinear relationships in comparative static settings. We use machine learning technology to develop partial dependence plots that let the data reveal how predictive variables affect terrorism. This feature makes machine learning an essential vehicle for exploring nonlinear relationships between a policy variable of interest and its effect on the likelihood of terrorism. Moreover, because our algorithms are theory-agnostic, we can let the data speak to actual relationships that can iteratively help us build better game-theoretic models. We lay down some conceptual foundations about terrorism in Section 2. Section 3 describes the machine learning techniques we use. We describe our data in Section 4. We report our results in Section 5. In Section 6 we provide examples of how our results can be helpful for validating game theoretic models. Section 7 concludes. \\n\\n2. Conceptual Foundations \\n\\nThe game-theoretic approach to terrorism tries to identify and deter terrorists through a cost–benefit lens that highlights the deep interaction between attacker and defender. Terrorism is a choice for successful rebellions (e.g., in Algeria, Israel, and Cyprus; ) [ 5 ,6 ]. Deterrence involves greater policing/punishment and policies to increase the opportunity costs of terrorism at the tactical level [ 7, 8]. However, the very act of deterrence elicits a response [ 9]. For example, attackers’ and defenders’ efforts may be complementary, which implies that improving military defense may be counterproductive [10]. The choice of terrorism is also a consequence of the nature of the target. Terrorists will substitute away from hard targets, suggesting that piecemeal policies that focus on some targets at the expense of others may be unproductive [ 11 ]. The nature of the target drives even the type of terrorist attack, such that harder targets elicit more suicide attacks in the context of a club goods model [ 12 ]. Moreover, increased military aid creates a moral hazard problem in recipient countries who now have an incentive to have terrorists attack them [13]. These lines of research show that terrorism is not a thing in itself; it is a tactical choice driven by context. Further, the relationship between attackers and defenders is constantly changing. A priori, there is no reason to believe that these changes have a linear pattern: Enders and Hoover, for example, empirically show a nonlinear relationship between income and terrorism [ 14 ]. However, despite the nonlinear relationships predicted by game-theoretic attacker–defender models, most empirical tests, if any, only provide information on the significance of point estimates. This is insufficient for policymakers since potential underlying nonlinearities may make average point estimates unhelpful. A Games 2021 , 12 , 54 3 of 20 \\n\\ndeer hunter shooting a foot to the left of the deer and a foot to the right of the deer but claiming he shot the deer on average is correct but will go hungry. Current empirical research has tended to identify the “correlates” of terrorism and has largely failed to identify a consistent set of such correlations. Thus, predicting terrorist attacks has so far mainly been speculative. Machine learning algorithms can provide scientifically cross-validated predictions of the likelihood of a terrorist attack to provide national security agencies with an abbreviated, cross-validated list of variables (i.e., policy levers) that can best identify and hopefully deter terrorism. Machine learning techniques identify the most predictive variables among those. These algorithms then identify vali-dated data-driven relationships between a predictively important covariate of terrorism and the likelihood of terrorism. This approach helps develop better models because they are theoretically agnostic. This agnosticism can help sift between theoretical models—a good model should be able to predict robustly. At the same time, predicting the likelihood of terrorist attacks provides meaningful intelligence for preventing terrorism. \\n\\n3. Machine Learning \\n\\nMachine learning (ML) methods are a growing set of methods for predicting and classifying various outcomes. These approaches have two applications: validating policy recommendations and testing theory [ 15 ]. Policymakers need to understand the potential effect of a policy before it is implemented, by definition, a matter of prediction. A theory, too, must be able to predict behavior. Machine learning is not a silver bullet, but it can help with these issues. Further, the machine learning techniques we use do not require assumptions about the underlying distributions of the variables and the error terms. Thus, statistical issues arising out of problems such as endogeneity may be less relevant in these prediction models. For example, say we can identify a highly predictive variable, say X, for terrorism. The predic-tive value alone, shorn of endogeneity considerations, suggests that policy and academic research should focus on understanding the relationship between X and terrorism. This investigation would include how other variables may influence X as well. Thus, machine learning is a good place to start an investigation as well. Just because X predicts terrorism does not mean it is causal. However, if it is a good predictor then there must be something about X that deserves further scrutiny. By the same token, variables that do not predict terrorism can hardly be causal. A causal variable, by definition, should be predictive. Theo-retical models that highlight variables that fail to predict are therefore unlikely to be good explanations for terrorism. This logic allows us to eliminate nonpredictive variables from consideration as casual factors. This process of reasoning provides a path for eliminating theoretical models that are unlikely to causally explain terrorism. From a policy perspective, predictive analysis has a more direct affect. Say the predic-tive variable X is, upon further econometric analysis, is also found to be causal. Then, X can potentially be a policy lever because we know X predictably causes terrorism. There-fore, manipulating X can potentially reduce terrorism. Thus, machine learned prediction analysis can supplement econometric techniques for policy analysis. Everything we noted above can be done using econometrics. However, econometrics requires assumptions about the underlying distribution of the variables. The concomitant endogeneity and specification problems and potential solutions are both susceptible to bias and a source of competing explanations for terrorism. For example, a particular theoretical model might suggest an empirical link between a variable and terrorism that can be tested. Such a test may even reveal a causal link with the right instrument. Yet, without a sense of the predictive salience of this link relative to other competing links we can have no idea whether this causal link is good explanation for terrorism. This is particularly an issue for game theoretic models because these by definition highlight endogenous strategic interactions. Machine learning models, by focusing on accurate prediction even in the presence of endogeneity are particularly suited for the empirical investigation of game theoretic models. Games 2021 , 12 , 54 4 of 20 \\n\\nThis paper suggests that validated ML techniques can help determine whether a particular theoretical model of terrorism has predictive salience relative to others. In the process, we address some problems inherent in interpreting machine-learned results. We will build an empirical model using several parametric and nonparametric ML techniques (classical regression, Poisson regression, artificial neural network, regression tree, bootstrap aggregating, boosting, and random forest) to measure how and how well publicly available economic, geographic, and institutional variables predict the frequency and severity of terror attacks [ 16 ]. The first step in this process will be to identify the machine learning approach that best predicts terrorism. Next, using the best technique, we will identify the most important variables for predicting terrorism. This process can help validate the predictive salience of a theoretical model relative to others. Finally, we plot the partial dependency plots for terrorism to show how each vari-able impacts terrorism across the distribution of its values. This technique is important because game-theoretic analysis gives us reason to believe that many of the correlates of terrorism have nonlinear impacts. Partial dependence plots also help us interpret results more meaningfully. ML techniques identify tipping points in the range of a particular variable that may place a country at a lower or higher risk of terrorism. We illustrate these tipping points using partial dependence plots, which show how the incidence and severity of terror attacks fluctuate across each variable’s observed values. Further, by identifying the variables that have the most predictive power, we could help develop a framework to distinguish between competing theoretical explanations of terrorism. Suppose, for instance, political models of terrorism may suggest that terrorism may be a tactic employed by disenfranchised groups with little or no voice in government. In contrast, economic models may suggest that groups employ terrorism as a signal of credibility to gain a seat at the negotiating table against the regime when it divvies up rents from resource wealth. Suppose ML methodologies rank democracy as a better predictor of terrorism than primary commodities exports, for example. In that case, we can assume that the political model may be a better explanation of terrorism than the economic model, or vice versa. Moreover, this approach can eliminate correlates of conflict that do not predict terrorism. Presumably, correlates that do not predict well cannot be considered as variables that cause terrorism. Such culling also helps build better specified and more precise models. Our ML approach will help us better understand causal patterns explaining terrorism. Moreover, we offer a better understanding of how to predict terrorism, which will help policymakers design counterterrorist policies. The remainder of this section outlines the prediction algorithms we use to predict the aggregate terror risk for a country. Readers who are familiar with these algorithms—or will be bored by a technical description of them!—may skip to the results section. Those looking for a more detailed description of the algorithms may consult their coverage by [16]. \\n\\n3.1. Classical and Other Regression Analysis \\n\\nUsing given data from a learning sample, L = {( y1, x1), . . . (yN, xN)}, any prediction function, d(xi), maps the vector of input variables, x, into the output variable (the number of terror attacks), y. An effective prediction algorithm seeks to define parameters that minimize an error function such as the mean absolute deviations or mean squared error, over the predictions. In linear regression models, d(xi) is simply a linear function of the inputs. A linear model with the MSE error function yields the ordinary least squares (OLS) regression model: \\n\\nROLS (d)= 1\\n\\nn ∑Ni=1(yi − d(xi)) 2,where d(xi) = xi β is a linear function of the inputs. Although OLS can sometimes yield good predictions (on average, the best prediction among all linear models, in fact), it has some undesirable properties in the case of predicting Games 2021 , 12 , 54 5 of 20 \\n\\nterror attacks. Specifically, since a large number of cases in our sample experience no terror attacks at all, while some of them experience very large numbers of attacks, we will expect the OLS model to predict negative numbers of terror attacks for some observations—which is nonsense. As an alternative, one corrects this problem by estimating a Poisson regression, which will estimate the average number of terror attacks conditional on the inputs, x, to be an exponential function of a linear combination of the inputs expressed as: \\n\\nλ = E(y|x) = eβx.This means that the probability of observing a specific number of terror attacks will be: \\n\\np( y|x) = eyxβe−exβ\\n\\ny! .The Poisson model then proceeds by estimating the parameters to maximize the likelihood function for this Poisson probability distribution. While these more sophisticated regression methods successfully purge the bias from the individual parameter estimates that might result from overdispersion, they do so to the detriment of the model’s overall predictive accuracy. Alternative approaches, which ensure a relatively high degree of accuracy while also avoiding nonsensical predictions, use nonparametric tree methods or combinations of trees to predict the number of terror attacks. \\n\\n3.2. Artificial Neural Networks (ANNs) \\n\\nA feedforward artificial neural network is a series of binary regression models con-necting each of the K input variables to M hidden nodes, over which, in the case of a regression problem such as ours (as opposed to a classification problem in the case of a binary target variable), a linear regression connects the hidden nodes to the output we hope to predict in the final layer. The logistic function is the usual activation function in the first layer, but in general any sigmoid function will have the desired properties. In a regression problem such as ours, the final layer usually contains only one output; the same is true for classification problems involving a binary output. For classification problems involving multinomial outputs, there can be any number of outputs. Thus, this methodology is quite flexible. Hence, with one output node, an ANN estimates K·M parameters. We present a diagram of a simple ANN for predicting terror attacks in Figure 1. In the figure, each connection corresponds to a weight for each input variable ( I1, . . . , I5) and bias (constant) terms ( B1 and B2) into the hidden nodes ( H1 and H2), or into the output node (O1). In the diagram, we show a two-layer neural network (the inputs do not count as a layer) with five inputs, two hidden nodes, and a constant. The link function connecting the hidden layer to the outputs, and which is not explicitly shown, is linear. Games 2021 , 12 , x FOR PEER REVIEW 6 of 21                 \\n\\n> Figure 1. ANN Diagram Example. Using a least-squares objective, the estimation of the ANN minimizes:\\n> 𝑅(𝛼, 𝛽; 𝒙) = ∑ ൫𝑦 ௜− 𝑓(𝛼, 𝛽; 𝒙 ௜)൯ ଶே௜ୀଵ ,\\n> where 𝑓(𝛼, 𝛽; 𝒙 𝒊 ) = 𝑍𝛽 connects the hidden layer to the output, and 𝑍 = ଵୣ is the\\n> Figure 1. ANN Diagram Example. Games 2021 ,12 , 54 6 of 20\\n\\nUsing a least-squares objective, the estimation of the ANN minimizes: \\n\\nR(α, β; x) = ∑Ni=1(yi − f (α, β; xi)) 2,where f (α, β; xi) = Zβ connects the hidden layer to the output, and Z = 11+exp (xα) is the logit function connecting the inputs to the hidden layer. Using the first order conditions with respect to the parameters for the hidden layer, α, and the parameters to the output layer, β, the estimation finds the solution according to a gradient descent rule: \\n\\nβr+1 \\n\\n> m\\n\\n= βrm − ∑Ni=1\\n\\n∂R\\n\\n∂β rm\\n\\n− λβ rm,where l is called the “weight decay” and acts as a penalty on the parameter and ef-fectively restricts the parameters towards zero to avoid “overfitting” the model to the learning sample. ANNs often perform well in situations where the interplay between input components is more important than any of their values. As such, they are often used in image and pat-tern recognition problems. We estimate the network using the nnet package implemented in R [17 ]. This implementation uses a single hidden layer (in which we used 100 nodes and 100 iterations). This work used all default options, save for specifying that the final layer should be linear. The initial weights were chosen randomly, and the goal function was the sum of the squared errors. \\n\\n3.3. Regression Trees \\n\\nClassification and regression trees (CART) diagnose and predict outcomes by finding binary splits in the input variables to optimally divide the sample into subsamples with successively higher levels of accuracy in the output variable, y. Therefore, unlike linear models, where the parameters are linear coefficients on each input variable, the parameters of the tree models are “if–then” statements that split the dataset according to the observed values of the inputs. We provide only a brief summary of tree construction as it pertains to our objectives [18]. More specifically, a tree, T, has four main parts: 1. Binary splits to splits in the inputs that divide the subsample at each node, t;2. Criteria for splitting each node into additional “child” nodes, or including it in the set of terminal nodes, T* ;3. A decision rule, d(x), for assigning a predicted output value to each terminal node; 4. An estimate of the predictive quality of the decision rule, d.The first step is achieved at each node by minimizing a measure of impurity. The most common measure of node impurity, and the one we use for our tree algorithms, is the mean square error, denoted ˆR(d)= 1 \\n\\n> n\\n\\n∑Ni=1(yi − d(xi)) 2. Intuitively, this method searches for the cutoff in each input that minimizes errors, then selecting which input yields the greatest improvement in node impurity using its optimal splitting point. Then, a node is declared to be terminal if one of the following conditions is met: (1) that the best split fails to improve the node impurity by more than a predetermined minimum improvement criterion; or (2) the split creates a “child” node that contains fewer observations than the minimum allowed (Note that there is a tradeoff here: setting lower values for the minimum acceptable margin of improvement or the minimum number of observations in a child node will lead to a more accurate prediction (at least within the sample the model uses to learn). However, improving the accuracy of the algorithm within the sample may lead to overfitting in the sense that the model will perform more poorly out-of-sample). At each terminal node, the decision rule assigns observations with a predicted outcome based on some measure of centrality. In the case of count (number of terror attacks or fatalities) or continuous (amount of property damage) outcomes, centrality is usually the mean of the observations conditional on reaching that node. Games 2021 , 12 , 54 7 of 20 \\n\\nThe predictive quality of the rule is also evaluated using the mean square error ,ˆR(d)= 1 \\n\\n> n\\n\\n∑Ni=1(yi − d(xi)) 2. This misclassification rate is often cross-validated by split-ting the sample several times and re-estimating the misclassification rate each time to obtain an average misclassification of all of the cross-validated trees. 3.3.1. Boosting Algorithms Iteratively re-estimating or combining ensembles of trees by averaging their predic-tions can often improve the accuracy of a tree algorithm. Boosting algorithms, bootstrap aggregating (bagging), and random forests all predict outcomes using ensembles of classifi-cation trees. The basic idea of these algorithms is to improve the predictive strength of a “weak learner” by iterating the tree algorithm many times by either modifying the distri-bution by reweighting the observations (boosting), randomly resampling a subset of the learning sample (bagging), or randomly sampling subsets of the input variables (random forest). These approaches then either classify the outcomes according to the outcome of the “strongest” learner once the algorithm achieves the desired error rate (boosting), or according to the outcome of a vote by the many trees (bagging). Boosting has been proposed to augment the strength of a “weak learner” (an algorithm that predicts poorly) [ 19 ,20 ]. Specifically, for a given distribution D of importance values assigned to each observation in L, and for a given desired error, ˜R, and failure probability, \\n\\nϕ, a strong learner is an algorithm that has a sufficiently high probability (at least 1 − ϕ)of achieving an error rate no higher than ˜R. A weak learner has a lower probability (less than 1 − ϕ) of achieving the desired error rate. Boosting algorithms for classification create a set of M classifiers, F = ( f 1, . . . , fM) that progressively reweight the importance of each observation based on whether the previous classifier predicted it correctly or incorrectly. Modifications of the boosting algorithm for classification have also been developed for regression trees [21,22]. Starting with a D1 = (1/ N, . . . , 1/ N), suppose that our initial classifier, f 1 = T (single-tree CART, for example), is a “weak learner” in that the misclassification rate, ˆR(d) is greater than the desired maximum desired misclassification rate, ˜R. Next, for all observations in the learning sample, recalculate the distribution weights for the observations as: \\n\\nD2 = D1(i)\\n\\nZ2\\n\\n×\\n\\n  \\n\\n> ˆR1(d)\\n> 1−ˆR1(d)\\n\\ni f d 1(xi) = yi\\n\\n1 otherwise ,where Zm is a scaling constant that forces the weights to sum to one. The final decision rule for the boosting algorithm is to categorize the outcomes ac-cording to d(x) = argmax  \\n\\n> y∈Y\\n\\n∑m:dm (x)= y log \\n\\n( 1− ˆRm (d) \\n\\n> ˆRm(d)\\n\\n)\\n\\n. Using this decision rule and its corre-sponding predictions, we calculate the estimate of the misclassification rate in the same way as in step (4) of the single tree algorithm. 3.3.2. Bootstrap Aggregating (Bagging) The bagging method proposed by [ 23 ] takes random resamples, { L(M)}, from the learning sample with replacement to create M samples using only the observations from the learning sample. Each of these samples will contain N observations—the same as the number of observations in the full training sample. However, in any one bootstrapped sample, some observations may appear twice (or more), others not at all. Note that the probability that a single observation is selected in each draw from the learning set is 1/ N. Hence, sampling with replacement, the probability that it is completely left out of any given bootstrap sample is (1 − 1/ N)N. For large samples this tends to 1/ e. The probability that an observation will be completely left out of all M bootstrap samples, then, is (1 − 1/ N)NM . The bagging method then adopts the rules for splitting and declaring nodes to be terminal described in the previous section to build M classification trees. Games 2021 , 12 , 54 8 of 20 \\n\\nTo complete steps (3) and (4), bagging needs a way of aggregating the information of the predictions from each of the trees. The way that bagging (and, as we will soon see, a random forest) does this for class variables is through voting . For classification trees (categorical output variables), the voting processes each observation through all of the M trees that was constructed from each of the bootstrapped samples to obtain that observation’s predicted class for each tree. Note that the observations under consideration could be from the in-sample learning set or from outside the sample (the test set). The predicted class for the entire model, then, is equal to the mode prediction of all of the trees. For regression trees (continuous output variables), the voting process calculates the mean of the predicted values for all of the bootstrapped trees. Finally, the bagging calculates the redistribution estimate in the same way as it did for the single classification tree, using the predicted class based on the voting outcome. 3.3.3. Random Forests Like bagging, a random forest is a tree-based algorithm that uses a voting rule to determine the predicted class of each observation. However, whereas the bagging random-izes the selection of the observations for each tree, a random forest may randomize over multiple dimensions of the classifier [ 24 ]. The most common dimensions for randomizing the trees are selecting the input variables for the node of each tree and the observations included for constructing each of the trees. We briefly describe the construction of the trees for the random forest ensemble below. A random forest is a collection of tree decision rules, { d(x, Θm), m = 1, . . . , M}, where \\n\\nΘm is a random vector specifying the observations and inputs that are included at each step of the construction of the decision rule for that tree. To construct a tree, the random forest algorithm takes to following steps: i Randomly select n ≤ N observations from the learning sample; ii At the “root” node of the tree, select k ∈ K inputs from x;iii Find the split in each variable selected in (ii) that minimizes the mean square error at that node and select the variable/split that achieves the minimal error; iv Repeat the random selection of inputs and optimal splits in (ii) and (iii) until some stopping criteria (minimum improvement, minimum number of observations, or maximum number of levels) is met. The bagging method described in the previous subsection is in fact a special case of a random forest where, for each tree, Θm, of a random selection of n = N observations from the learning sample with replacement (and each observation having a probability of being selected in each draw equal to 1/ N) and sets the number of inputs to select at each node, k,equal to the full length of the input vector, K so that all of the variables are considered at each node. \\n\\n3.4. Validation and Testing of Predictive Accuracy \\n\\nOnce we have built our learning algorithm, the next issue is to evaluate the validity of our error estimates and the predictive strength of our models. Error estimates ( R[d]) can sometimes be misleading if the model we are evaluating is overfitted to the learning sample. These error estimates can be tested out-of-sample or cross-validated using the learning sample. To test the out-of-sample validity, we simply split the full dataset into two random subsets of countries : the first, known as the learning sample (or training sample) contains the countries and observations that will build the models; the second, known as the test sample ,will test the out-of-sample predictive accuracy of the models. The out-of-sample error rates will indicate which models and specifications perform best, and will help reveal if any of the models are overfitted. To validate the error rates, machine learning uses either hold-out validation or cross-validation. In our study, we have used hold-out validation, which involves training the models using one portion (in our case 70% selected at random) of the dataset. The algorithm Games 2021 , 12 , 54 9 of 20 \\n\\nthen tests the learned model by measuring the mean square error between the predicted value and the actual value in the 30% of the data unseen by it. A model with an acceptably low error rate in the sample unseen by it is presumably a good predictive model. This out of sample test also guards against overfitting. An overfitted model may be highly accurate in the learning sample but it would be unlikely to predict well in the test sample. \\n\\n4. Data \\n\\nAs a first step in analyzing some preliminary data on terrorism, we have predicted the number of terror attacks using each of the seven models described above (OLS regression, Poisson regression, regression tree, random forest, bagging, and boosting). For our specifi-cation, we have included 69 input (or explanatory) variables that cover most of the ones discussed in Gassebner and Luechinger’s survey of the empirical literature on conflict [ 25 ]. We measure our output (or “dependent”) variable, Terror Attacks, as the total number of terror attacks in a country in the last five years. This variable comes from the Global Terror Database published by the University of Maryland and covers 1970–2014. When we combine all of the variables, our sample covers 1975–2014, since some entire data sources, such as the Database of Political Institutions, do not become available until 1975. To maintain the spirit of “prediction” in our model, we then consider our input (“explanatory”) variables as five-year lagged averages of the preceding five years. Moreover, we only consider the variables at nonoverlapping five-year intervals so that none of the same information is contained in consecutive time intervals in our sample. In this sense, at any given point in time, policymakers will be able to use our model to predict whether a country will likely experience a greater or lesser number of terror incidents in the next five years. Moreover, this approach reduces the risk of endogeneity; the past can potentially affect the future, but it seems unlikely that the future can affect the past. In addition, this lagging reduces the risk of collider bias among the potential predictors if one were to interpret partial dependence plots causally. Collider bias happens when the target variable (Y, terrorism here) and a variable of theoretical interest (say T) affects a third variable, say X, in the model. In that case, if the researcher is interested in justifying a causal relationship between T and Y, X should be taken out of the model specification. Placing the target variable in the future helps justify that there can be no such relationship. We do not interpret our partial dependence plots causally. From the Cross-National Time Series [ 26 ] we take the numbers of assassinations, demonstrations, government crises, guerrilla warfare incidents, purges, riots, and strikes as measures of underlying low-level social instability. We also take the number of cabinet changes and executive changes as measured of political instability, and the effectiveness of the legislature as a measure of political legitimacy. From the Database of Political Institutions [ 27 ] we take the number of checks on power; executive and legislative indices of electoral competition; legislative, government, and opposition fractionalization indices; government Herfindahl index; and government polarization index as measures of the concentration (or not) of power and accountability (or not) within the government. We then include the changes in veto players, the existence of electoral fraud, executive tenure, the presence of a military executive, and political stability and executive power measures. Finally, we include plurality voting and proportional representation as indicators of structural differences in electoral rules. Next, we take several indices of government quality from the International Country Risk Guide [ 28 ]. It is important to remember that, for each of the ICRG indices, a higher value always coincides with “better” outcomes on this dimension of institutional quality. For example, in the case of the “internal conflict” (or “external conflict”) index, a higher value for the index somewhat counterintuitively corresponds to less conflict. The same can be said for “ethnic tensions”, “religious tensions” and “military in politics”—in each of these cases, higher values relate to less of the (bad) thing that the variable name implies. That being said, we include the following indices from the ICRG: the bureaucratic quality and corruption indices as measures of the transparency of government; ethnic tensions, Games 2021 , 12 , 54 10 of 20 \\n\\nexternal conflict, internal conflict, law and order, and religious tensions as measures of the levels of latent (or open) social hostility, and the government’s ability to ease those hostilities; government stability and investment profile indices as measures of the govern-ment’s credibility in carrying out stated policies and refraining from expropriation; and democratic accountability and military in politics indices as a measure of the legitimacy and responsiveness of the regime to the public’s preferences. We also add the Polity2 index and regime durability from the Polity IV Project as additional measures of legitimacy and responsiveness. As measures of economic and cultural divisions within society, we include measures of income inequality and ethnic and religious fractionalization. The former comes from the Standardized World Income Inequality Database [ 29 ]. The latter come from [ 30 ], which in turn come from the Atlas Naroda Mira [31]. Finally, we include numerous measures of economic human development from the World Development Indicators from the World Bank. They are: aid and development assistance; arms exports and imports; public education and health spending; female labor force participation; foreign direct investment (FDI); fuel exports; gross domestic product (GDP) per capita; government consumption; the stock of foreign born immigrants; infant mortality; the inflation rate in consumer prices; life expectancy; literacy; military expendi-tures; military personnel; population and its rate of growth; portfolio investment; primary, secondary, and tertiary school enrollment rates; social contributions; telephones per 100,000 people; the unemployment rate; urban population; and the youth dependency ratio. Rather than exhaustively describing the distributional characteristics and justifying the inclusion of each variable, we kindly refer the reader to visit Gassebner and Luechinger’s survey and the references therein to the various studies that have already provided such a description and justification [ 25 ]. For readers interested in some of the characteristics of the observed data in our sample, we have included the descriptive statistics for all 69 variables in Table 1. We can see from the table that each of our explanatory variables has omitted values to varying degrees. The tree-based methods (single trees, boosting, bagging, and random forest) can automatically exploit the full information available by using surrogate infor-mation or using the median or mode at that branch of a tree as a best guess the value of a missing data point. Standard parametric methods (in our case Poisson regression and neural networks) do not do this automatically, and regression methods that do (such as full-information maximum likelihood), might do so in ways that give different imputations of the missing data. To resolve this, we preprocess our data using random forest imputation. The basic idea is that we consider a covariate that does not have missing data (in our case conflict), and perform a random forest model to predict that variable (instead of the true variable of interest since that would be “cheating” for running the full model). Next, whenever the algorithm encounters a missing value at any tree node, the imputation substitutes the median or mode for that variable and continues with the subsequent splits. Therefore, the imputed values in each tree exploit the full complement of conditional distribution for that variable based on that tree. Averaging over all of the trees, we obtain imputed values for missing data points that uses as much relevant data about the conditional distribution of the variable as possible. It also has the advantage of creating imputed values that are naturally bounded by the domains of the observed data. Parametric methods such as multiple imputation estimate parameters based on an assumed distribution for the missing variables, and depending on the sensitivity of the parameters and the distributions of the covariates, may lead to extreme values outside of the logical bounds for a given variable (e.g., negative income). Games 2021 , 12 , 54 11 of 20 \\n\\nTable 1. Variables and descriptive statistics. \\n\\nVariable Source Obs Mean Std. Dev. Min. Max. \\n\\nTerror Attacks GTD 6411 86.88 375.81 0 10,701 Assassinations CNTS 5318 0.21 0.84 0.00 18.50 Cabinet Changes CNTS 5310 0.44 0.37 0.00 3.50 Demonstrations CNTS 5318 0.52 1.15 0.00 14.00 Effectiveness of Leg. CNTS 5297 1.74 0.94 0.00 3.00 Executive Changes CNTS 5310 0.19 0.28 0.00 3.00 Government Crises CNTS 5318 0.13 0.27 0.00 2.67 Guerrilla Warfare CNTS 5318 0.12 0.32 0.00 2.60 Purges CNTS 5318 0.03 0.13 0.00 2.50 Riots CNTS 5318 0.31 1.05 0.00 18.20 Strikes CNTS 5318 0.12 0.34 0.00 3.40 Changes in Veto Players DPI 4838 0.12 0.15 0.00 1.00 Checks on Power DPI 4831 2.52 1.60 1.00 17.00 Exec. Electoral Comp. DPI 4850 5.15 2.08 1.00 7.00 Executive Years in Office DPI 4859 7.93 7.68 1.00 45.00 Electoral Fraud DPI 4214 0.14 0.32 0.00 1.00 Government Frac DPI 4428 0.19 0.25 0.00 1.00 Government Herfindahl DPI 4428 0.82 0.25 0.02 1.00 Government Polarization DPI 4673 0.36 0.69 0.00 2.00 Legislative Frac. DPI 4419 0.46 0.30 0.00 1.00 Leg. Electoral Comp. DPI 4855 5.41 2.00 1.00 7.00 Military Executive DPI 4856 0.21 0.39 0.00 1.00 Opposition Frac DPI 3362 0.45 0.27 0.00 1.00 Plurality Voting DPI 3877 0.68 0.46 0.00 1.00 Proportional Rep. DPI 3474 0.58 0.49 0.00 1.00 Bureaucratic Quality ICRG 3376 2.11 1.19 0.00 4.00 Corruption ICRG 3376 3.08 1.35 0.00 6.00 Democratic Accountability ICRG 3376 3.64 1.62 0.00 6.00 Ethnic Tensions ICRG 3376 3.91 1.44 0.00 6.00 External Conflict ICRG 3376 9.48 2.22 0.00 12.00 Government Stability ICRG 3376 7.45 2.10 1.00 11.50 Internal Conflict ICRG 3376 8.61 2.62 0.03 12.00 Investment Profile ICRG 3376 6.94 2.34 0.08 12.00 Law and Order ICRG 3376 3.60 1.48 0.25 6.00 Military in Politics ICRG 3376 3.66 1.80 0.00 6.00 Religious Tensions ICRG 3376 4.54 1.35 0.00 6.00 Polity2 Polity IV 4520 1.16 7.26 −10.00 10.00 Regime Durability Polity IV 4569 23.99 28.73 0.00 198.00 Ethnic Fractionalization Reynal-Querol 4749 0.45 0.28 0.01 0.96 Religious Fractionalization Reynal-Querol 4749 0.28 0.23 0.00 0.78 Income Inequality (Gini) SWIID 3350 38.52 9.87 16.49 69.35 Area WDI 6110 682,865 1,717,163 2 16,400,000 Off. Aid & Dev. Assistance WDI 4045 0.08 0.11 −0.01 0.76 Arms Exports WDI 1703 0.01 0.08 0.00 1.50 Arms Imports WDI 3976 0.04 0.12 0.00 3.32 Education Spending WDI 3436 4.45 2.32 0.59 44.30 Foreign Direct Investment WDI 4602 2.80 4.72 −32.30 72.50 Female Labor Force Part. WDI 3293 50.12 17.55 9.20 90.80 Fuel Exports WDI 3875 16.82 28.33 0.00 100.00 GDP per Capita WDI 4807 9560.35 16,016.19 65.64 141,000.00 Government Consumption WDI 4538 16.47 6.87 3.37 84.50 Health Spending WDI 2647 3.48 2.21 0.01 18.36 Immigrant Stock WDI 4975 8.07 13.75 0.03 86.80 Infant Mortality WDI 5103 48.11 40.72 2.18 174.00 Inflation WDI 4168 32.94 254.00 −17.60 6522.40 Life Expectancy WDI 5074 64.79 10.58 24.30 82.50 Literacy Rate WDI 1549 73.42 23.01 10.90 100.00 Military Expenditures WDI 2995 2.74 3.03 0.09 48.60 Military Personnel WDI 3092 1.88 2.23 0.06 35.80 Population WDI 5190 30.94 116.87 8.82 1316.00 Population Growth WDI 5190 1.80 1.44 −4.84 15.50 Portfolio Investment WDI 4000 0.01 0.16 −0.02 4.88 Primary Enrollment WDI 4763 97.05 22.35 15.80 208.00 Secondary Enrollment WDI 4407 60.84 33.35 2.13 155.60 Social Contributions WDI 1203 17.11 15.02 0.00 59.97 Telephones WDI 5127 14.70 18.58 0.01 103.42 Tertiary Enrollment WDI 4135 18.62 19.36 0.00 99.20 Unemployment WDI 3007 9.03 6.78 0.20 59.50 Urban Population WDI 5190 50.33 24.51 4.18 100.00 Youth Dependency WDI 5000 62.07 23.94 19.44 114.40 \\n\\n5. Results \\n\\n5.1. Predictive Quality \\n\\nTable 2 reports the predictive quality of each of the models using the 70 variables. The best models we see to predict the overall number of terror attacks are the single regression tree, random forest, and bagging predictors, which reduce the overall MSE in the learning sample by about 64%, and 63%, and 59%, respectively, compared to the unconditional Games 2021 , 12 , 54 12 of 20 \\n\\nsample mean. An average of all of the models’ predicted values (which sometimes provides a better prediction, especially in cases of classification) improves the MSE by about 49%. In comparison, OLS regression improves the MSE by about 26%. However, as we might expect, the trees that use random bootstrapping (bagging and random forest) predict considerably better out of sample, with a test sample MSE reduction of 71% and 70% of the total MSE, respectively. Of particular interest here is the fact that these models achieve a significant reduction in the MSE despite the exclusion of the lagged number of terror attacks in our model since the pre-existing level of violence has been shown to be one of the strongest predictors of current and future violence in studies of conflict [ 32 ]. We exclude lagged terror attacks because we are partly looking to predict (a reason to include), but also looking to select a model to build theories and test causal effects (subsequent analyses). Lagged attacks would improve the prediction but would explain so much of the variation that we are not left with much to select a model on                                        \\n\\n> Table 2. MSEs for the various learning models.\\n> Learning Sample Test Sample MSE % Decrease MSE % Decrease\\n> OLS Regression 107,708.05 25.71% 98,119.17 26.12% Poisson Regression 151,539.85 −4.52% 139,385.78 −4.96% Neural Network 144,695.12 0.20% 132,389.28 0.31% Regression Tree 52,038.41 64.11% 80,182.62 39.62% Boosting Predictor 141,677.19 2.28% 129,790.58 2.27% Bagging Predictor 59,866.71 58.71% 40,202.12 69.73% Random Forest 54,271.19 62.57% 38,504.85 71.01% Average of All Predictors 74,564.39 48.57% 76,391.30 42.48% Total MSE 144,987.24 132,802.82\\n\\nIt is worth noting that the Poisson regression model, which tends to yield more valid estimates of causal effects, actually increases the MSE of the predictor compared to a prediction based on the simple sample mean. This is not quite the case for the neural network model, but we can see that the neural network and boosting models predict relatively poorly both in and out of sample. \\n\\n5.2. Variable Importance \\n\\nTable 3 reports the variable importance levels (measured as the percentage of the total reduction in MSE that is attributed to that variable) based on the single regression tree, boosting, bagging, and random forest models, which predicts conflict the best, although different algorithms or different runs of the same algorithm may identify different sets of predictors [ 15 ]. Theoretically agnostic algorithms may choose a predictive variable one time and another at a different time if they are predictive substitutes. The risk for this happening is reduced for algorithms such as random forests, bagging, or boosting because the algorithm learns by taking multiple subsamples and averaging the results. We take this one step further by averaging the variable importance results across several algorithms to give us a sense of confidence in the stability of the variable importance ranking. Games 2021 , 12 , 54 13 of 20 \\n\\nTable 3. Variable importance rankings. \\n\\nVariable Tree Bagging Boosting Forest Average \\n\\nAssassinations 7.618 24.930 62.966 12.388 14.979 Guerrilla War 2.677 10.735 30.698 9.436 7.616 Military Personnel 15.482 4.166 0.000 2.555 7.401 Religious Frac 12.386 4.761 0.000 3.218 6.788 Military Politics 12.682 1.913 1.082 3.529 6.042 Health Spending 3.765 4.390 2.499 3.548 3.901 Year 1.882 5.704 0.000 3.888 3.825 Population 0.947 3.568 0.394 5.562 3.359 Exec Yrs in Office 6.441 1.940 0.000 1.315 3.232 Fuel Exports 6.193 1.455 0.000 1.227 2.958 Dem Accountability 5.222 1.243 0.000 1.411 2.625 Effectiveness of Leg 0.000 3.104 0.000 3.041 2.048 Aid & Assistance 2.528 0.973 0.000 1.826 1.775 Gini 0.981 2.106 0.000 2.083 1.723 Tertiary Enrollment 2.053 0.752 0.000 2.023 1.609 Female LFPR 0.000 1.226 2.361 3.213 1.480 Portfolio Investment 0.000 2.695 0.000 1.600 1.432 Area 1.858 1.194 0.000 0.837 1.297 Arms Imports 1.425 1.402 0.000 1.009 1.279 Strikes 0.662 1.369 0.000 1.711 1.247 Ethnic Tension 0.733 0.467 0.000 2.409 1.203 Checks 1.702 1.248 0.000 0.615 1.188 Internal Conflict 0.969 0.125 0.000 2.257 1.117 Telephones 1.882 0.435 0.000 0.697 1.005 Law Order 1.322 0.560 0.000 0.966 0.950 GDP pc 0.235 0.842 0.000 1.736 0.938 Urban Population 1.710 0.404 0.000 0.645 0.920 Ethnic Frac 0.469 1.336 0.000 0.885 0.897 Polity 2 0.000 1.355 0.000 1.244 0.866 Investment Prof 0.321 1.019 0.000 1.169 0.836 Legislative Frac 1.425 0.532 0.000 0.549 0.835 Riots 0.307 0.729 0.000 1.382 0.806 Primary Enrollment 1.425 0.170 0.000 0.687 0.761 Arms Exports 0.000 1.233 0.000 0.950 0.728 Demonstrations 0.179 0.464 0.000 1.511 0.718 Unemployment 0.000 0.813 0.000 1.310 0.708 Religious Tension 0.000 0.300 0.000 1.601 0.634 Infant Mortality 0.000 0.773 0.000 1.046 0.606 Secondary Enrollment 0.000 0.848 0.000 0.765 0.537 Immigrant Stock 0.016 0.672 0.000 0.816 0.501 Reg Durability 0.248 0.547 0.000 0.609 0.468 Gov Consumption 0.000 0.369 0.000 0.824 0.398 Gov Stability 0.618 0.089 0.000 0.441 0.383 Corruption 0.075 0.472 0.000 0.584 0.377 Life Expectancy 0.000 0.242 0.000 0.889 0.377 Youth Dependency 0.000 0.286 0.000 0.842 0.376 FDI 0.000 0.372 0.000 0.719 0.363 Fraud 0.346 0.106 0.000 0.421 0.291 Opposition Frac 0.000 0.304 0.000 0.560 0.288 Inflation 0.207 0.326 0.000 0.300 0.278 External Conflict 0.259 0.140 0.000 0.428 0.276 Bureaucratic Qual 0.000 0.277 0.000 0.501 0.259 Leg. Elec. Comp. 0.248 0.192 0.000 0.295 0.245 Exec. Elec. Comp. 0.000 0.262 0.000 0.465 0.242 Literacy Rate 0.000 0.143 0.000 0.574 0.239 Population Growth 0.000 0.394 0.000 0.302 0.232 Proportional Rep 0.000 0.368 0.000 0.326 0.231 Social Contributions 0.000 0.173 0.000 0.515 0.229 Military Expend 0.167 0.212 0.000 0.277 0.219 Purges 0.331 0.051 0.000 0.179 0.187 Education Spending 0.000 0.243 0.000 0.304 0.182 Military Exec 0.000 0.085 0.000 0.316 0.134 Government Herfindahl 0.000 0.082 0.000 0.235 0.106 PluralityVoting 0.000 0.093 0.000 0.196 0.096 Gov Polarization 0.000 0.063 0.000 0.198 0.087 Government Frac 0.000 0.093 0.000 0.087 0.060 Cabinet Changes 0.000 0.028 0.000 0.085 0.038 Changes in Vetoes 0.000 0.065 0.000 0.018 0.028 Executive Changes 0.000 0.020 0.000 0.044 0.021 Government Crises 0.000 −0.050 0.000 −0.191 −0.080 \\n\\nHere, we see that the first five variables in the list account for close to one-third (about 31 percent) of the overall improvement in the random forest model’s MSE. We also see that the single strongest predictor of current levels of terrorism is a history of assassinations in that country, which accounts for about 12% of the total reduction in the MSE in the random forest model, and 25% of the reduction for the bagging model and 63% of the reduction for the boosting model. The second strongest predictor, guerrilla war, accounts for about 10% Games 2021 , 12 , 54 14 of 20 \\n\\nof the MSE reduction for the bagging and forest models and over 30% of the decrease for the boosting model. After regime-directed violence, two of the following three strongest predictors involve the extent to which the military engages with everyday life and politics. Military personnel and the military in politics index account for almost 15% of the reduction in MSE combined, on average (slightly more in the single tree, somewhat less in the bagging and forest models, and not in the boosting algorithm). In between these measures of military engagement, we see religious fractionalization to account for about 7% of the variation on average. Rounding out the top ten predictors are health spending (3.9% of the MSE), time trend (3.8%), population (3.6%), executive tenure (3.2%), and fuel exports (2.6%). At this point, our algorithmic approach suggests we have a group of variables that predict terrorism quite well. Moreover, we have identified the top predictors of terrorism. The reader will note that many of the variables identified by the literature do not have predictive salience [ 25 ]. Indeed, many of the variables highlighted in the literature, such as investment profile, bureaucratic quality, or religious tensions, have very little predictive salience. This culling helps us identify the kinds of theoretical models that can help us better understand terrorism. For example, the joint importance of guerilla war and military personnel is quite high and suggests that terrorism may be best understood as a tactical choice in asymmetric warfare rather than an outcome of institutional deficiencies in bu-reaucratic quality or lack of economic opportunity. This sort of explanation lends credence to the argument that a war on terror is strategically empty—just as a war on the blitzkrieg or the pincer movement, both tactical choices, would be strategically empty. However, such explanations are also predicated on the nature of the relationships between the top predictors and terrorist attacks. We turn to identify just such relations next, highlighting a methodological approach that is particularly in tune with the nonlinear relationships predicted by game-theoretic models. \\n\\n5.3. The Nonlinear Relationship between Greater Security and Terrorism \\n\\nThe next step is to analyze how each of the variables impacts aggregate terror risk. To do this, we use a partial dependence plot mapping the possible values of the input variable of interest onto the observed incidence of terror attacks. Partial dependence plots display the marginal effect of variable xk conditional on the observed values of all of the other variables, ( x1, −k, x2, −k, . . . xn,−k). Specifically, it plots the graph of the function: ˆf (x) = 1\\n\\nn\\n\\n> n\\n\\n# ∑\\n\\n> i=1\\n\\nf (xk, xi,−k),where the summand, f (xk, xi,−k), is simply the observed outcome of the number of ter-ror attacks. This section focuses on three partial dependence plots that highlight game-theoretic models of terrorism that suggest that any fundamental understanding of terrorism should be understood as a tactical choice by rebel organizations. Figure 2 shows that guerrilla warfare increases terrorism. While there may be some overlap between guerrilla warfare and terrorism, agencies that make national security policies tend to define them as distinct phenomena. Hence, in some cases, we might think of terrorism and guerrilla warfare as different tactics employed by rebel groups towards similar ends [33]. Moreover, guerilla warfare predicts terrorism five years out. Games 2021 , 12 , 54 15 of 20 ିିgether, and in the absence of the predictive salience of such institutional variables such as bureaucratic quality and investment profile that capture elements of state capacity, we can grope toward a model of terrorism rooted in the understanding of a specific kind of state capacity. The nonlinear relationships embedded in this understanding suggest that game-theoretic models where equilibrium switching is possible due to interactions be-tween agents are better suited than traditional neoclassical utility maximization ap-proaches.   \\n\\n> Figure 2. Partial dependence plot: guerrilla warfare.\\n> Figure 2. Partial dependence plot: guerrilla warfare.\\n\\nThus, in Figure 3, more military personnel also translate into more terror attacks on average, though these averages mask a u-shaped relationship. Last, in Figure 4, we notice that increased military involvement in politics reduces aggregate terror risk. Taken together, and in the absence of the predictive salience of such institutional variables such as bureaucratic quality and investment profile that capture elements of state capacity, we can grope toward a model of terrorism rooted in the understanding of a specific kind of state capacity. The nonlinear relationships embedded in this understanding suggest that game-theoretic models where equilibrium switching is possible due to interactions between agents are better suited than traditional neoclassical utility maximization approaches. Games 2021 , 12 , x FOR PEER REVIEW 17 of 21        \\n\\n> Figure 3. Partial dependence plot: military personnel.\\n> Figure 3. Partial dependence plot: military personnel. Games 2021 ,12 , 54 16 of 20 Figure 3. Partial dependence plot: military personnel.\\n> Figure 4. Partial dependence plot: military in politics.\\n\\nState capacity (or the lack thereof) is a reasonably standard explanation for conflict [34]. The theoretical basis for an empirical understanding of this relationship lies in three concepts: military capacity, administrative quality, and institutional coherence. [35] suggests that military personnel and expenditures, bureaucratic quality measures, and popular institutional measures such as polity or those reported by ICRG have construct and theoretical validity as a measure of the three elements of state capacity. We have all these variables as part of our predictive algorithm. Nevertheless, of these three, it appears that military capacity is most salient for understanding terrorism. Thus, our algorithm has been pretty specific about what kind of theoretical models are more likely explanations for terrorist attacks. This suggests that models better understand why terrorism happens [13]. The how matters as well. There is a clear equilibrium switch in the number of terrorist attacks as guerilla war-fare intensifies. However, there is an optimum level of intensity beyond which the number of terrorist attacks is stable. Again, this is the result suggested by game-theoretic models  \\n\\n> Figure 4. Partial dependence plot: military in politics.\\n\\nState capacity (or the lack thereof) is a reasonably standard explanation for conflict [ 34 ]. The theoretical basis for an empirical understanding of this relationship lies in three concepts: military capacity, administrative quality, and institutional coherence. Ref. [ 35 ] suggests that military personnel and expenditures, bureaucratic quality measures, and popular institutional measures such as polity or those reported by ICRG have construct and theoretical validity as a measure of the three elements of state capacity. We have all these variables as part of our predictive algorithm. Nevertheless, of these three, it appears that military capacity is most salient for understanding terrorism. Thus, our algorithm has been pretty specific about what kind of theoretical models are more likely explanations for terrorist attacks. This suggests that models better understand why terrorism happens [13]. The how matters as well. There is a clear equilibrium switch in the number of terrorist attacks as guerilla warfare intensifies. However, there is an optimum level of intensity beyond which the number of terrorist attacks is stable. Again, this is the result suggested by game-theoretic models where equilibrium switches can be, for example, a consequence of changes in the payoffs. An econometric point parametric estimate would never capture these breakpoints unless, out of sheer coincidence, the researcher imposes the assumption of such a breakpoint. However, point estimates can be particularly misleading, for example, in the case of military personnel. An average effect captured in a point estimate would merely show a positive relationship, rather than the nuance where (initially at least) increasing military personnel reduces terrorism, thus suggesting a cost-minimizing optimum amount of military personnel. Nevertheless, we also have the somewhat counterintuitive but ultimately plausible result that hardening targets by increasing the number of military personnel elicits more terrorist attacks than substitute attacks away from these targets. This sort of result is reminiscent of security dilemmas rather than Beckerian policing models. On the other hand, we cannot completely throw out institutional coherence as a predictor of terrorism. Military dictatorships can control terrorist attacks better. This result provides an interesting counterpoint to the argument that military regimes are more vulnerable to terrorism [36]. \\n\\n6. Game-Theoretic Model Validation \\n\\nOthers have suggested that machine learning can help validate theoretical models because they are designed to test whether a model is predictive or not [ 15 , 37 ]. A good theoretical model should be able to predict behavior. Games 2021 , 12 , 54 17 of 20 \\n\\nStandard econometric approaches to testing models are particularly fraught when it comes to testing game-theoretic models because endogeneity is a feature rather than a bug in game-theoretic models. For example, terrorists respond to counterterrorism by changing their behavior, which in turn suggests changes in counterterrorism. Thus, any econometric approach to terrorism must be cautious to avoid endogeneity-driven estimation biases. Many of these methods reduce the predictive value of a model (for example, many causal studies have very low R-squares). Yet, as we noted above, a good theoretical model should also be able to predict. Predictive machine learning can help determine whether a causal variable is also predictive. A causal variable that is also predictive can help convince academics and policymakers of the salience of a theoretical model. Partial dependence plots can capture equilibrium shifts to capture comparative static effects of game-theoretic models. We discuss this aspect quite extensively in the previous section. However, variable importance can help us sift through models of terrorism to identify more predictive variable specifications. We highlight three examples within subsets of the game-theoretic literature to emphasize this point. One strand of the game theoretic literature focuses on group cohesion. Future un-certainty generated by increased counter terrorism can lead to rebel group splintering, thereby increasing the risk of terrorism as these splinter groups jockey for survival [ 38 ]. Figure 3 highlights just such a result; an increase in military personnel does indeed pre-dict an increase in the number of terrorist attacks. Further, the first two most predictive variables, guerrilla warfare and assassinations, also predict an increase in the number of terrorist attacks. Guerrilla warfare and assassinations also point to significant political uncertainty. This suggests that political uncertainty may be an important predictor of terrorist attacks, possibly by affecting group cohesion. These findings would suggest a deeper, and causal, dive into understanding how rebel group splintering in the face of political uncertainty may affect terrorism. That is to say, machine-learning can be a first step toward finding explanations of terrorism in conjunction with game-theoretic models and causal econometric analysis. Counterterrorism efforts require global coordination. For example, destroying a terrorist training ground may require the US to take action in North Africa or the Middle East. Theoretically, military aid to a country that hosts a terrorist organization creates a disincentive to remove the terrorist problem [ 13 ]. In addition, terrorism is a tactical choice for a rebel group when facing a formidable state that the rebels do not want to provoke too much [33]. Both these models suggest that military strength should be a predictor of terrorist attacks. Our algorithm identifies the size of the military as one of the most important predictors of terrorism. As noted in Figure 3, an increase in the size of the military predicts an initial rise in terrorist acts as expected by both the game-theoretic modelsthat predict an increase in the intensity of terrorist attacks, particularly suicide attacks, when targets harden [ 12 ]. Nevertheless, further increases in the size of the military keep the risk of terror attacks elevated without increasing terrorist attacks, a potential benefit for a host country receiving military aid. That is, military size increases terrorism at first and then levels off, tracking the prediction from Bapat’s model (see Figure 2, p. 311 in [13]). Equilibrium may also shift from guerrilla warfare to terrorism as a function of the accuracy of a state’s military action [ 33 ]. If terrorist tactics are more provocative, the probability of a terrorist attack increases. On the flip side, if guerrilla action is more provocative then the probability of guerilla warfare increases. The point is that as the degree of provocation changes there is an equilibrium switch from guerilla warfare to terrorism. Our result in Figure 3 identifies just such an equilibrium switch to increased terrorism as the intensity of guerilla warfare increases. First of all, this means that equilibrium switches to more terrorism are related to guerilla warfare. Thus, our result in Figure 3 supports Carter’s model prediction. However, our result also suggests that, if Carter’s model is a true reflection of reality, then as guerilla warfare intensifies there is some change in the underlying parameters in a way that makes terrorist action more provocative. Thus, our Games 2021 , 12 , 54 18 of 20 \\n\\nresults suggest that there may be a relationship between guerilla action and provocation that changes the likelihood of terrorism. This space may bear further theoretical investigation. Terrorist organizations need to survive to achieve their goals. A strand of the game-theoretic literature is devoted to understanding how terrorist organizations recruit and re-tain members while overcoming incentive compatibility problems when secrecy is essential .De Mesquita’s game-theoretic model suggests that counterterrorism efforts that re-duce economic opportunity can increase terrorist mobilization [ 39 ]. In any case, terrorist organizations will put more resources into terrorism (presumably leading to more suc-cessful attacks) when they recruit and retain higher-ability terrorists. Therefore, the BDM (2005) model would suggest that, empirically, countries with better economic opportunities would have fewer terrorist attacks. Moreover, he notes that his model suggests, among other things, that ethnically divided societies would see more terrorist attacks and that development aid may reduce terrorism (presumably by increasing economic opportunity). Our results fails to validate many of the predictions of this model [ 39 ]. For example, the variable investment profile includes contract enforcement and risk of expropriation by the state. These variables are components of economic freedom or opportunity. For example, the risk of expropriation reduces the likelihood of economic growth [ 40 ]. However, this variable is not an important predictor of terrorism. Moreover, neither ethnic divisions (as measured by the ethnic tensions variable) nor development aid are important predictors of terrorism. Presumably terrorist organizations mobilize to perpetrate terrorist attacks. However, while factors such as the lack of economic opportunity may indeed affect mobilization, it seems highly unlikely that they affect terrorist attacks. If economic opportunity was important for mobilization it should be able to predict terrorism since terrorism is the purpose for mobilization. This brings into question the role of economic opportunity in explaining mobilization. Our examples in this section suggest that some game-theoretic models generate val-idated predictions while others do not. Now all of these models may be causal. Our algorithms make no claims for causality. Yet, if a model is a generalizable explanation of reality, then its predictions should be validated empirically. On this criterion, all models cannot be treated equally. Further, we show how partial dependence plots, by highlight-ing nonlinear relationships, can help validate game theoretic models that very typically generate hypotheses with nonlinear patterns. Further, these results are data-driven and therefore unbiased by assumptions about any particular theoretical concern. Consequently, empirical results that are consistent with theoretical consequences provide an unbiased validation. Last, once again because our results are data-driven rather than based on theo-retical assumptions, they can give us hints about what areas need a theoretical structure. Of course, empirical validation of this new theory should give rise to even more spaces that need theory in an iterative process that slowly erases gaps in knowledge. \\n\\n7. Conclusions \\n\\nIn this paper, we highlight two aspects of machine learning that can supplement game-theoretic analysis. First, we can sift among competing theoretical models in a theoretically agnostic way to identify those models which have the most predictive salience. A good theoretical model should be able to make predictions. Here, our algorithm suggests that models predicting economic opportunity, development assistance, and ethnic tensions may not be predictively salient. In contrast, those that predict a more formidable target would elicit more terrorist attacks so are predictively salient. Game-theoretic models, by their very nature, highlight endogenous relationships driven by strategic interactions. Machine learning algorithms, by focusing on predictive accuracy instead of tests of significance, can identify whether a variable is predictive or not even if it is endogenous with the target variable, terrorism. To the extent that causal variables should be predictive, identifying predictive variables can help jumpstart the search for causal links. This process is made more efficient because we can eliminate Games 2021 , 12 , 54 19 of 20 \\n\\nvariables that are unlikely to be causal because they are not predictive in an empirical framework that is unbiased by endogeneity problems. Second, game-theoretic approaches often predict nonlinear relationships between vari-ables where equilibriums switch in comparative static scenarios. The partial dependence plots generated by machine learning algorithms can identify these nonlinearities and equi-librium switches in a theoretically agnostic way. Partial dependence plots are, therefore, a particularly suitable testing methodology for game-theoretic comparative statics. Thus, machine learning techniques can reduce bias and help find better explanations for terrorism. This is important for formulating better counterterrorist policies. These techniques have other benefits as well. For example, they can impute missing data and predictively validate the imputation, and they do not require heroic assumptions about the underlying distribution of data. \\n\\nAuthor Contributions: All authors contributed equally in all parts of the project. All authors have read and agreed to the published version of the manuscript. \\n\\nFunding: This research received no external funding. \\n\\nInstitutional Review Board Statement: Not applicable. \\n\\nInformed Consent Statement: Not applicable. \\n\\nData Availability Statement: See citations in the text and reference list. \\n\\nConflicts of Interest: The authors declare no conflict of interest. \\n\\nReferences \\n\\n1. Sandler, T.; Arce, D.G. Terrorism: A game-theoretic approach. Handb. Def. Econ. 2007 , 2, 775–813. 2. Wasserstein, R.L.; Lazar, N.A. The ASA Statement on p-Values: Context, Process, and Purpose. Am. Stat. 2016 , 70 , 129–133. [CrossRef] 3. Ward, M.; Greenhill, B.D.; Bakke, K.M. The Perils of Policy by p-value: Predicting Civil Conflict. J. Peace Res. 2010 , 47 , 363–375. [CrossRef] 4. Basuchoudhary, A.; Bang, J.T.; Sen, T.; David, J. Identifying the Complex Causes of Civil War: Causal Interpretations of Machine Learning Technologies ; Palgrave-MacMillan: Cham, Switzerland, 2021. 5. Shughart, W.F., II. September 11, 2001. Public Choice 2002 , 112 , 1–8. [CrossRef] 6. Shughart, W.F., II. An Analytical History of Terrorism, 1945–2000. Public Choice 2006 , 128 , 7–39. [CrossRef] 7. Frey, B.S.; Luechinger, S. How to Fight Terrorism: Alternatives to Deterrence. Def. Peace Econ. 2003 , 14 , 237–249. [CrossRef] 8. Faria, J.R.; Arce, D.G. Terror Support and Recruitment. Def. Peace Econ. 2005 , 16 , 263–273. [CrossRef] 9. Andreozzi, L. Rewarding Policemen Increases Crime. Another Surprising Result from the Inspection Game. Public Choice 2004 ,\\n\\n121 , 69–82. [CrossRef] 10. Zhuang, J.; Bier, V.M. Balancing terrorism and natural disasters—Defensive strategy with endogenous attacker effort. Oper. Res. \\n\\n2007 , 55 , 976–991. [CrossRef] 11. Enders, W.; Sandler, T. Distribution of Transnational Terrorism among Countries by Income Class and Geography after 9/11. Int. Stud. Q. 2006 , 50 , 367–393. [CrossRef] 12. Berman, E.; Laitin, D.D. Religion, terrorism and public goods: Testing the club model. J. Public Econ. 2008 , 92 , 1942–1967. [CrossRef] 13. Bapat, N.A. Transnational terrorism, US military aid, and the incentive to misrepresent. J. Peace Res. 2011 , 48 , 303–318. 14. Enders, W.; Hoover, G.A. The nonlinear relationship between terrorism and poverty. Am. Econ. Rev. 2012 , 102 , 267–272. 15. Mullainathan, S.; Spiess, J. Machine learning: An applied econometric approach. J. Econ. Perspect. 2017 , 31 , 87–106. 16. Hand, D.; Mannila, H.; Smyth, P. Principles of Data Mining ; MIT Press: Cambridge, MA, USA, 2001. 17. Ripley, B.; Venables, W. R Package ‘nnet’ Version 7.3-12 ; R Foundation for Statistical Computing: Vienna, Austria, 2016. Available online: https://cran.r-project.org/web/packages/nnet/nnet.pdf (accessed on 28 June 2021). 18. Breiman, L.; Friedman, R.A.; Stone, C.J.; Olshen, R.A. Classification and Regression Trees ; Chapman and Hall: Boca Raton, FL, USA, 1984. 19. Schapire, R.E. The strength of weak learnability. Mach. Learn. 1990 , 5, 197–227. [CrossRef] 20. Freund, Y.; Schapire, R.E. Experiments with a new boosting algorithm. ICML 1996 , 96 , 148–156. 21. Schapire, R.E. Using output codes to boost multiclass learning problems. ICML 1997 , 97 , 313–321. 22. Friedman, J.H. Greedy function approximation: A gradient boosting machine. Ann. Stat. 2001 , 29 , 1189–1232. [CrossRef] 23. Breiman, L. Bagging predictors. Mach. Learn. 1996 , 24 , 123–140. 24. Breiman, L. Random forests; Machine learning. Mach. Learn. 2001 , 45 , 5–32. Games 2021 , 12 , 54 20 of 20 \\n\\n25. Gassebner, M.; Luechinger, S. Lock, stock, and barrel: A comprehensive assessment of the determinants of terror. Public Choice \\n\\n2011 , 149 , 235–261. [CrossRef] 26. Banks, A.S.; Wilson, K.A. Cross-National Time-Series Data Archive ; Databanks International: Jerusalem, Israel, 2015. Available online: http://www.databanksinternational.com (accessed on 28 June 2021). 27. Cruz, C.; Keefer, P.; Scartascini, C. Database of Political Institutions Codebook, 2015 Update (DPI2015) ; Inter-American Development Bank: Washington, DC, USA, 2016. 28. PRS Group. International Country Risk Guide ; PRS Group: East Syracuse, NY, USA, 2015. Available online: http://epub.prsgroup. com/products/international-country-risk-guide-icrg (accessed on 28 June 2021). 29. Solt, F. The Standardized World Income Inequality Database*. Soc. Sci. Q. 2016 , 97 , 1267–1281. [CrossRef] 30. Reynal-Querol, M. Ethnicity, Political Systems, and Civil Wars. J. Confl. Resolut. 2002 , 46 , 29–54. [CrossRef] 31. Mira, A.N. Moscow: Miklukho-maklai ethnological institute at the department of geodesy and cartography of the state geological committee of the soviet union. USSR 1964 .32. Basuchoudhary, A.; Bang, J.T.; Sen, T.; David, J. Predicting Hotspots: Using Machine Learning to Understand Civil Conflict ; Rowman & Littlefield: Lanham, MD, USA, 2018. 33. Carter, D.B. Provocation and the Strategy of Terrorist and Guerrilla Attacks. Int. Organ. 2016 , 70 , 133–173. [CrossRef] 34. DeRouen, K., Jr.; Sobek, D. State capacity, regime type, and civil war. In What Do We Know about Civil Wars ; Madon, T.D., Mitchell, S.M., Eds.; Rowman and LittleField: Lanham, MD, USA, 2016; pp. 59–74. 35. Hendrix, C.S. Measuring state capacity: Theoretical and empirical implications for the study of civil conflict. J. Peace Res. 2010 , 47 ,273–285. [CrossRef] 36. Wilson, M.C.; Piazza, J.A. Autocracies and Terrorism: Conditioning Effects of Authoritarian Regime Type on Terrorist Attacks. \\n\\nAm. J. Politi Sci. 2013 , 57 , 941–955. [CrossRef] 37. Varian, H.R. Big data: New tricks for econometrics. J. Econ. Perspect. 2014 , 28 , 3–28. 38. Basuchoudhary, A.; Razzolini, L. The evolution of revolution: Is splintering inevitable? Econ. Peace Secur. J. 2018 , 13 , 43–54. [CrossRef] 39. De Mesquita, E.B. The quality of terror. Am. J. Political Sci. 2005 , 49 , 515–530. [CrossRef] 40. Aguiar, M.; Amador, M. Growth in the Shadow of Expropriation. Q. J. Econ. 2011 , 126 , 651–697. [CrossRef]\",\n", "  \"https://www.numberanalytics.com/blog/in-depth-metrics-guide-game-theory-analysis\": \"[![Image 1: brown ruler with stand](https://images.unsplash.com/photo-1529651795107-e5a141e34843?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3MDgyMDR8MHwxfHNlYXJjaHwxfHxNZXRyaWNzJTIwR3VpZGV8ZW58MHx8fHwxNzQ0OTY0ODY1fDA&ixlib=rb-4.0.3&q=80&w=1080?utm_source=numberanalytics&utm_medium=referral)](https://images.unsplash.com/photo-1529651795107-e5a141e34843?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3MDgyMDR8MHwxfHNlYXJjaHwxfHxNZXRyaWNzJTIwR3VpZGV8ZW58MHx8fHwxNzQ0OTY0ODY1fDA&ixlib=rb-4.0.3&q=80&w=1080?utm_source=numberanalytics&utm_medium=referral)\\n\\nPhoto by [Markus Spiske](https://unsplash.com/@markusspiske?utm_source=numberanalytics&utm_medium=referral) on [Unsplash](https://unsplash.com/?utm_source=numberanalytics&utm_medium=referral)\\n\\n* * *\\n\\nTable of Contents\\n-----------------\\n\\n1.   [Overview: Metrics within Game Theory](https://www.numberanalytics.com/blog/in-depth-metrics-guide-game-theory-analysis#overview-metrics-within-game-theory)\\n2.   [Quantitative Techniques and Models](https://www.numberanalytics.com/blog/in-depth-metrics-guide-game-theory-analysis#quantitative-techniques-and-models)\\n3.   [Strategic Applications in Gameplay](https://www.numberanalytics.com/blog/in-depth-metrics-guide-game-theory-analysis#strategic-applications-in-gameplay)\\n4.   [Comparative Analysis and Best Practices](https://www.numberanalytics.com/blog/in-depth-metrics-guide-game-theory-analysis#comparative-analysis-and-best-practices)\\n5.   [Tools, Resources, and Future Research](https://www.numberanalytics.com/blog/in-depth-metrics-guide-game-theory-analysis#tools-resources-and-future-research)\\n6.   [Summary and Strategic Recommendations](https://www.numberanalytics.com/blog/in-depth-metrics-guide-game-theory-analysis#summary-and-strategic-recommendations)\\n\\n* * *\\n\\nIntroduction\\n------------\\n\\nGame theory has long fascinated researchers and strategists alike, serving as a foundational framework for analyzing competitive and cooperative interactions. However, when we introduce metrics into the equation, we create a powerful toolset that can elevate decision-making and optimize outcomes. This detailed guide aims to bridge the gap between abstract game theory formulations and practical analytical methods, ultimately offering insights that drive gameplay decisions. In this article, we delve into quantitative techniques and models, demonstrate their applications in strategic contexts, and highlight best practices for interpreting game outcomes.\\n\\nBest video game consoles\\n\\nBy combining both mathematical rigor and practical insights, this article serves as a roadmap for advanced analysis in game theory. Whether you’re a researcher, a data enthusiast, or a professional in the gaming industry, the integration of metrics can transform theoretical knowledge into actionable strategies.\\n\\n* * *\\n\\nOverview: Metrics within Game Theory\\n------------------------------------\\n\\nMetrics in game theory refer to the quantification of various parameters that govern gameplay and strategic interactions. Here, we outline the key points that establish the foundation for advanced analytical methods in game theory.\\n\\nBest video game consoles\\n\\n### Key Concepts\\n\\n*   **Intersection of Metrics and Game Theory:**\\n\\n Metrics bring precision to game theory, enabling a systematic approach to evaluating and predicting outcomes. By converting qualitative observations into quantitative measures, analysts can deploy statistical and algorithmic tools effectively.\\n\\n*   **Importance of Precise Measurement in Gaming Strategies:**\\n\\n Accurate metrics allow for a refined understanding of strategies, helping to pinpoint which decisions yield the best payoff. Precision is especially crucial in complex games where subtle shifts can have drastic implications.\\n\\nBest video game consoles\\n\\n*   **Setting the Stage for Advanced Analysis:**\\n\\n The integration of detailed metrics is a stepping stone to higher levels of strategic analysis. This approach is not only beneficial for determining optimal moves in a game but also essential in fields like economics, political science, and evolutionary biology.\\n\\n_For further reading, refer to [MIT’s Introduction to Game Theory](https://ocw.mit.edu/courses/economics/14-126-game-theory-fall-2010/), which lays the groundwork for understanding these concepts._\\n\\n* * *\\n\\nQuantitative Techniques and Models\\n----------------------------------\\n\\nBest video game consoles\\n\\nQuantitative techniques in game theory transform traditional models into robust, data-driven analyses. Below, we explain the statistical methods and algorithmic models that shape our understanding of strategic interactions.\\n\\n### Statistical and Algorithmic Methods\\n\\n*   **Statistical Analysis:**\\n\\n Techniques such as regression models, hypothesis testing, and analysis of variance (ANOVA) provide insights into the relationships between variables in gameplay. For instance, one might examine how changes in player behavior influence payoff distributions.\\n\\nBest video game consoles\\n\\n*   **Algorithmic Models:**\\n\\n Advanced algorithms, including Monte Carlo simulations and evolutionary strategies, are used to simulate numerous game scenarios. This is instrumental in understanding risk versus reward dynamics. In many cases, these simulations involve solving mathematical expressions such as:\\n\\nExpected Payoff=∑i p i⋅v i \\\\text{Expected Payoff} = \\\\sum_{i} p_i \\\\cdot v_i\\n\\nwhere p i p_i  denotes the probability of a particular move and v i v_i  is the corresponding payoff.\\n\\n*   **Mathematical Formulations:**\\n\\n In many game-theoretic models, particularly those involving iterative strategies, the game’s state evolves over time. A simple iterative model may be captured by:\\n\\nBest video game consoles\\n\\nx t+1=A x t+B u t x_{t+1} = Ax_t + Bu_t\\n\\nwhere x t x_t  represents the state at time t t , A A  is a state transition matrix, and B B  is the control input matrix. Such formulations help in modeling decision processes dynamically.\\n\\n### Examples\\n\\n*   **Dynamic Programming:**\\n\\n A technique extensively used in game theory analysis, dynamic programming helps break down complex problems into simpler sub-problems. It is particularly useful when the game involves a sequence of decisions where each choice influences subsequent outcomes.\\n\\nBest video game consoles\\n\\n*   **Game Simulations:**\\n\\n Tools like Monte Carlo simulations are often used to forecast how different strategies may play out under varying conditions. This approach not only provides a comprehensive view of possible game trajectories but also assists in developing resilient strategies.\\n\\n_For more on statistical techniques in game theory, check out the [Stanford Encyclopedia of Philosophy’s entry](https://plato.stanford.edu/entries/game-theory/)._\\n\\n* * *\\n\\nStrategic Applications in Gameplay\\n----------------------------------\\n\\nUtilizing metrics for gameplay transcends theoretical exercises and finds real-world applications in competitive arenas. This section outlines how metrics inform strategic decision-making across different contexts.\\n\\n### Utilizing Metrics for Competitive Advantage\\n\\n*   **Real-time Decision Making:**\\n\\n In fast-paced digital gaming environments, metrics enable players and developers to fine-tune strategies in real time. Analysis software can process metrics instantly, informing players on which moves yield the most favorable outcome under current conditions.\\n\\n*   **Competitive Analysis:**\\n\\n Metrics help teams dissect the strategic patterns of both their own and their competitors’ gameplay. For example, tracking win rates, move frequencies, and response times facilitates a deep analysis of opponent strategies, enabling players to anticipate and counteract them with precision.\\n\\n*   **Case Studies:**\\n\\n[Video games](https://www.numberanalytics.com/blog/in-depth-metrics-guide-game-theory-analysis#), such as multiplayer online battle arenas (MOBAs), often use metrics for continuous balance improvements. In board games like chess, historical data on openings and mid-game strategies are analyzed to develop robust counter-strategies that align with quantitative insights.\\n\\n### Analyzing Real-Time Decision Processes\\n\\n*   **Instant Feedback Loops:**\\n\\n Modern gaming software incorporates algorithms that monitor and analyze player inputs in real time. Immediate feedback allows players to adjust their strategies dynamically, reducing the reliance on pre-determined moves.\\n\\n*   **Data-Driven Adjustments:**\\n\\n By continuously measuring in-game events, players and coaches can identify patterns that lead to success. These insights, derived from iterative data analysis, are invaluable in formulating adaptable and winning strategies.\\n\\n_For a deeper dive into real-time analytics in competitive gaming, consider reading up on [Esports analytics on Forbes](https://www.forbes.com/sites/forbestechcouncil/2021/06/15/the-growing-power-of-esports-analytics/)._\\n\\n* * *\\n\\nComparative Analysis and Best Practices\\n---------------------------------------\\n\\nA critical aspect of applying metrics in game theory is understanding the comparative benefits of different analytical approaches. Below, we explore how to compare various metrics approaches and extract industry best practices.\\n\\n### Comparing Various Metrics Approaches\\n\\n*   **Traditional vs. Modern Approaches:**\\n\\n Traditional methods in game theory, which may focus on pure strategy equilibria (e.g., Nash equilibria), are now complemented by modern data analytics that incorporate player behavior analytics and machine learning models. While the traditional approach provides a strong theoretical foundation, modern methods often yield accessible actionable insights.\\n\\n*   **Quantitative vs. Qualitative Analysis:**\\n\\n Although this guide emphasizes quantitative analysis, integrating qualitative assessments can lead to even richer insights. Combining both approaches allows researchers to capture the nuances that might not be evident from numbers alone.\\n\\n### Best Practices in Data Interpretation\\n\\n*   **Clear and Consistent Metrics:**\\n\\n Ensure that metrics are clearly defined, consistently measured, and interpreted within the appropriate context. Any ambiguity in metrics could lead to misinterpretation and suboptimal decisions.\\n\\n*   **Industry Case Studies:**\\n\\n Analyzing real-world examples is crucial. For instance, in digital gaming, companies such as Valve Corporation routinely use A/B testing to determine changes in gameplay mechanics. Their experience underscores the need for small-scale trials before large-scale implementations.\\n\\n*   **Lessons Learned:**\\n\\n Data interpretation should be a cyclic process of hypothesis testing, implementation, and review. Continuous assessment is key, as strategic dynamics in gameplay often evolve with both technology and player expectations.\\n\\nA quick comparison in table form:\\n\\n| Aspect | Traditional Approach | Modern Data-Driven Approach |\\n| --- | --- | --- |\\n| Framework | Nash Equilibrium, Dominant Strategies | Machine Learning, Real-Time Analytics |\\n| Time Scale | Long-term strategic planning | Instantaneous adjustments |\\n| Data Utilization | Theoretical assumptions | Empirical data and player behavior |\\n| Adaptability | Rigid, less flexible | Highly dynamic and adaptable |\\n\\n_For further discussions on best practices, the [Harvard Business Review](https://hbr.org/) offers valuable insights on data-driven decision-making._\\n\\n* * *\\n\\nTools, Resources, and Future Research\\n-------------------------------------\\n\\nThe landscape of game theory analytics is rapidly evolving with advancements in technology. In this section, we explore the tools and resources that are currently shaping the future of game theory analysis.\\n\\n### Modern Analytical Tools and Software\\n\\n*   **Specialized Software:**\\n\\n Programs such as MATLAB, R, and Python libraries (e.g., SciPy, NumPy, and Pandas) provide powerful environments for performing complex quantitative analyses. These tools facilitate everything from basic statistical tests to advanced simulations.\\n\\n*   **Visualization Tools:**\\n\\n Data visualization is crucial in interpreting metrics. Software such as Tableau and Power BI transforms raw data into insightful dashboards that can highlight trends and anomalies in gameplay.\\n\\n*   **Simulation Platforms:**\\n\\n Platforms like AnyLogic and Arena are frequently used to model game dynamics. They allow researchers to simulate multi-agent interactions and assess the impact of various strategies under controlled conditions.\\n\\n### Resources for Ongoing Education and Research\\n\\n*   **Online Courses and MOOCs:**\\n\\n Websites like Coursera, edX, and Khan Academy offer courses in game theory, data analytics, and machine learning, making advanced concepts accessible to a broader audience.\\n\\n*   **Academic Journals and Conferences:**\\n\\n Peer-reviewed journals such as Games and Economic Behavior, and conferences like the International Conference on Game Theory, provide platforms for sharing the latest research findings.\\n\\n*   **Collaborative Forums:**\\n\\n Online communities and forums like Stack Exchange and ResearchGate allow practitioners to share insights, ask questions, and collaborate on innovative strategies.\\n\\n### Predicting Future Trends in Game Theory Applications\\n\\n*   **Integration of AI:**\\n\\n With the rise of artificial intelligence, future research in game theory will likely focus on the interplay between AI agents and human players. AI can analyze vast amounts of data in real time, offering insights that were previously out of reach.\\n\\n*   **Increased Real-Time Analytics:**\\n\\n The demand for instant feedback in competitive gaming is pushing the development of sophisticated real-time analytics that can adapt strategies dynamically during gameplay.\\n\\n*   **Interdisciplinary Approaches:**\\n\\n The future of game theory lies at the intersection of economics, psychology, computer science, and data analytics. This fusion will lead to more robust models that account for both rationality and human behavior nuances.\\n\\n_For further exploration of advanced tools and upcoming trends, the [IEEE Xplore Digital Library](https://ieeexplore.ieee.org/) is a highly recommended resource._\\n\\n* * *\\n\\nSummary and Strategic Recommendations\\n-------------------------------------\\n\\nIn summary, the integration of detailed metrics into game theory analysis empowers players, researchers, and strategists by transforming abstract models into actionable insights. Here are the key takeaways and recommendations:\\n\\n### Key Insights Recap\\n\\n*   **Foundational Understanding:**\\n\\n Metrics bridge the gap between theoretical game constructs and real-world applications by quantifying strategic interactions and outcomes.\\n\\n*   **Quantitative Rigor:**\\n\\n Statistical models and algorithmic techniques, such as Monte Carlo simulations and dynamic programming, enhance our understanding of gameplay dynamics.\\n\\n*   **Application in Strategy:**\\n\\n Metrics enable real-time decision-making, competitive analysis, and adaptive strategies, ultimately leading to a competitive edge in various gaming contexts.\\n\\n*   **Comparative Best Practices:**\\n\\n A blend of traditional game theory frameworks with modern data analytics provides a comprehensive toolkit for tackling complex strategies.\\n\\n*   **Forward-Looking Tools:**\\n\\n Embracing modern analytical software and staying current with research trends are vital for sustaining a competitive advantage in the rapidly evolving landscape of game theory.\\n\\n### Actionable Recommendations for Practitioners\\n\\n*   **Invest in Data Infrastructure:**\\n\\n Ensure that robust data collection and analysis systems are in place to capture relevant metrics accurately.\\n\\n*   **Embrace Cross-Disciplinary Learning:**\\n\\n Combine insights from economics, computer science, and psychology to develop a holistic approach to game theory analysis.\\n\\n*   **Utilize Simulation and Visualization Tools:**\\n\\n Employ software tools for dynamic simulations and create visual dashboards to monitor and refine strategies.\\n\\n*   **Stay Updated with Research:**\\n\\n Keep abreast of emerging trends and best practices by participating in academic conferences, reading industry journals, and engaging with online research communities.\\n\\n*   **Implement Iterative Strategies:**\\n\\n Adopt an iterative cycle of hypothesis testing, data analysis, strategy formulation, and review to maintain a proactive and adaptive approach.\\n\\n### Concluding Thoughts\\n\\nGame theory has evolved far beyond its traditional boundaries, and its fusion with metrics is opening new pathways for strategic innovation. As analytical methods become more sophisticated and accessible, professionals across sectors are better equipped to predict, analyze, and influence outcomes. The evolution of gameplay – from board games to multiplayer online competitions – is testament to the potency of integrating quantitative analysis with strategy. Looking ahead, the continuous refinement of these methodologies will not only enhance competitive performance but also contribute to the broader understanding of strategic interactions in diverse fields.\\n\\nFor further discussion and expanded insights into the world of game theory, consider reading resources from [Nature](https://www.nature.com/) and [ScienceDirect](https://www.sciencedirect.com/), which frequently publish research on applied game theory and metrics.\\n\\n* * *\\n\\nBy implementing the insights and recommendations outlined in this guide, you can embark on a journey of enhanced strategic analysis, ultimately crafting more decisive and informed gameplay strategies in a world where data-driven decisions reign supreme.\\n\\n* * *\\n\\n_Happy strategizing and may your metrics guide you to victory!_\",\n", "  \"http://article.sapub.org/10.5923.j.jgt.20200902.01.html\": \"A Comprehensive Review of Solution Methods and Techniques for Solving Games in Game Theory\\n\\n===============\\n\\n[](http://www.sapub.org/journal/index.aspx \\\"Scientific & Academic Publishing\\\")\\n\\n![Image 1: Scientific & Academic Publishing](http://article.sapub.org/images/portal/company.jpg)\\n\\n[](http://www.sapub.org/journal/index.aspx)[](http://www.sapub.org/journal/alljournalslist.aspx)[](http://www.sapub.org/book/index.aspx)[](http://www.sapub.org/journal/conferences.aspx)[](http://www.sapub.org/journal/publishingservices.aspx)[](http://www.sapub.org/journal/manuscriptsubmission.aspx)[](http://www.sapub.org/journal/joinus.aspx)[](http://www.sapub.org/journal/aboutus.aspx)[](http://www.sapub.org/journal/contactus.aspx)\\n\\n*   ### Paper Information\\n\\n*   [Paper Submission](http://www.manuscriptsystem.com/signin.aspx)\\n\\n*   ### Journal Information\\n\\n*   [About This Journal](http://www.sapub.org/journal/journalintroduction.aspx?journalid=1021)\\n*   [Editorial Board](http://www.sapub.org/journal/editorialboard.aspx?journalid=1021)\\n*   [Current Issue](http://www.sapub.org/journal/currentissue.aspx?journalid=1021)\\n*   [Archive](http://www.sapub.org/journal/issuelist.aspx?journalid=1021)\\n*   [Author Guidelines](http://www.sapub.org/journal/authorguidelines.aspx?journalid=1021)\\n*   [Contact Us](http://www.sapub.org/journal/journalcontactus.aspx?journalid=1021)\\n\\nJournal of Game Theory\\n\\np-ISSN: 2325-0046 e-ISSN: 2325-0054\\n\\n2020; 9(2):25-31\\n\\ndoi:10.5923/j.jgt.20200902.01\\n\\nReceived: Sep. 21, 2020; Accepted: Oct. 20, 2020; Published: Oct. 28, 2020\\n\\n![Image 2: A 100x15 small image, likely a logo, icon or avatar](http://article.sapub.org/images/oa.png)\\n\\n### A Comprehensive Review of Solution Methods and Techniques for Solving Games in Game Theory\\n\\n*   [![Image 3: A 26x20 small image, likely a logo, icon or avatar](http://article.sapub.org/images/portal/abstract.gif)Abstract](http://article.sapub.org/10.5923.j.jgt.20200902.01.html#Abs)\\n*   [![Image 4: A 26x20 small image, likely a logo, icon or avatar](http://article.sapub.org/images/portal/references.gif)Reference](http://article.sapub.org/10.5923.j.jgt.20200902.01.html#Ref)\\n*   [![Image 5: A 26x20 small image, likely a logo, icon or avatar](http://article.sapub.org/images/portal/pdf.gif)Full-Text PDF](http://www.sapub.org/global/showpaperpdf.aspx?doi=10.5923/j.jgt.20200902.01)\\n*   [![Image 6: A 26x20 small image, likely a logo, icon or avatar](http://article.sapub.org/images/portal/html.gif)Full-text HTML](http://article.sapub.org/10.5923.j.jgt.20200902.01.html#Sec1)\\n\\n**Issah Musah**, **Douglas Kwasi Boah**, **Baba Seidu**\\n\\nDepartment of Mathematics, C. K. Tedam University of Technology and Applied Sciences, Navrongo, Ghana\\n\\nCorrespondence to: Douglas Kwasi Boah, Department of Mathematics, C. K. Tedam University of Technology and Applied Sciences, Navrongo, Ghana.\\n\\nEmail:![Image 7: A 214x20 small image, likely a logo, icon or avatar](http://article.sapub.org/email/10.5923.j.jgt.20200902.01.gif)\\n\\nCopyright © 2020 The Author(s). Published by Scientific & Academic Publishing.\\n\\nThis work is licensed under the Creative Commons Attribution International License (CC BY). \\n\\n[http://creativecommons.org/licenses/by/4.0/](http://creativecommons.org/licenses/by/4.0/)\\n\\n![Image 8: A 88x31 small image, likely a logo, icon or avatar](http://article.sapub.org/images/creativecommons.png)\\n\\n**Abstract**\\n\\nThis paper presents a comprehensive review of solution methods and techniques usually employed in game theory to solve games with the view of demystifying and making them easy to understand. Specifically, the solution methods and techniques discussed are Nash Equilibrium Method; Pareto Optimality Technique; Shapley Values Technique; Maximin-Minimax Method; Dominance Method; Arithmetic Method; Matrix Method; Graphical Method and Linear Programming Method. The study has contributed significantly to knowledge by successfully filling or narrowing the knowledge or research gap of insufficient literature on reviews of solution methods and techniques usually employed to analyze or solve games in game theory.\\n\\n**Keywords:** Games, Solution Methods, Techniques, Game Theory, Strategies, Payoffs, Value of a Game\\n\\n**Cite this paper:** Issah Musah, Douglas Kwasi Boah, Baba Seidu, A Comprehensive Review of Solution Methods and Techniques for Solving Games in Game Theory, _Journal of Game Theory_, Vol. 9 No. 2, 2020, pp. 25-31. doi: 10.5923/j.jgt.20200902.01.\\n\\n### Article Outline\\n\\n[1. Introduction](http://article.sapub.org/10.5923.j.jgt.20200902.01.html#Sec1)[2. Literature Review](http://article.sapub.org/10.5923.j.jgt.20200902.01.html#Sec2)[3. Main Work](http://article.sapub.org/10.5923.j.jgt.20200902.01.html#Sec3)[4. Conclusions](http://article.sapub.org/10.5923.j.jgt.20200902.01.html#Sec4)\\n\\n### 1. Introduction\\n\\nSingh [1] defined a game as a competitive situation among N persons or groups called players conducted under a prescribed set of rules with known payoffs. The rules define the elementary activities or moves of the game. According to Bhuiyan [2], the rules of a game are defined by the players, actions and outcomes. The decision makers of a game are the players. All possible moves that a player can make are called actions. One of the given possible actions of each competitor or player is referred to as a strategy. A pure strategy is deterministically selected or chosen by a player from his/her available strategies. On the other hand, a mixed strategy is one randomly chosen or picked from amongst pure strategies with allocated likelihoods. Expected gain a player gets when all the other players have picked or chosen their possible actions or strategies and the game is over is referred to as utility or payoff. A set of obtained results emanating from the possible moves when the game is played out is called outcome. Darkwah and Bashiru [3] stated that, a combination of strategies that comprises the optimal or best response for each of the players in a game is called equilibrium. Morrow [4] presented that, there are mainly two ways to represent a game namely the normal form (which is simply a matrix that describes the strategies and payoffs of the game) and the extensive form (which is a tree-like form or structure. However, there are games that require richer representation such as infinite repeated games. According to Shoham and Leyton-Brown [5], to represent such games, we go beyond the normal and extensive forms. Usually, games are categorized according to their characteristics or features. Some common types of games are as follows:(i) Coalitional and non-coalitional games: A coalitional game is one in which players agree to employ mutual or reciprocal strategies and gain higher benefits. An example of non-coalitional game is Prisoner’s dilemma game. (ii) Prisoner’s dilemma game: This is a situation whereby two suspected criminals are taken custody by the police and charged. In addition, information available to the police is insufficient to prove that they are guilty of the crime, unless one confesses or both of them confess. Now, each one of them can either admit the crime hoping to be given a lighter punishment or decide not to talk. Consequently, the police interrogate or question the suspected criminals in separate rooms and offer them deals. If both of them refuse to talk, then each one of them will be charged with minor crime and given say one-month jail term. Again, if both of them confess, each one of them will be given say a six-month jail term. Moreover, assuming one confesses and the other does not do that, the confessor is liberated while the other one given say, a nine-month jail term. In fact, the game is a tactical one. The players of the game pick or select their possible actions or strategies at the same time only once, and then the game is completed. (iii) Zero-sum game: Zero-sum games are the mathematical representation of conflicting situations according to Washburn [6] and that in those games, the total of gains and losses is equal to zero.(iv) Two-person zero-sum game: According to Hillier and Lieberman [7], it is a competitive situation involving exactly two competitors whereby one of them wins what the other loses.(v) Nonzero-sum game: This is one whereby the total utility for every stage or outcome is not zero. Nonzero-sum games are categorized into two distinct types namely ‘constant-sum games’ and ‘variable-sum games’. The former is one whereby the total utility for each outcome is a fixed value whereas the latter is one whereby the sum of all the utilities for each outcome gives different values. According to Kumar and Reddy [8], game theory is concerned with uncertain decisions comprising two or more opposing and intelligent agents or people whereby each one of them aims to outwit the other (s). Dixit and Skeath [9] defined game theory as a discipline that studies decision making of interactive entities and asserted that strategic thinking is perhaps the most recognized essence of game theory. Shoham and Leyton-Brown [5] stated that, game theory presents a technical analysis of strategic interactions which according to Geckil and Anderson [10] are concerned with the interaction of decision makers in the game. In fact, game theory provides a platform which can be used to formulate, analyze and understand different strategic situations.This paper presents a comprehensive review of solution methods and techniques usually employed in game theory to solve games with the view of demystifying and making them easy to understand.\\n### 2. Literature Review\\n\\nThe first information about game theory was set forth by James Waldegrave in 1713. In his letter, he presented a solution to a two-person card game with mixed strategies. In 1921, Emile Borel suggested a formal way of handling game theory. In 1928, his suggestion was advanced by John Von Neumann in his published paper that served as the foundation of a two-person zero-sum game. In 1944, Oskar Morgenstern and John Von Neumann presented a book titled ‘The Theory of Games and Economic Behaviour’. That move was believed by many to give rise to game theory as an area of research or learning. In 1950s, John Nash, Harold Kuhn, John Harsanyi etc. developed game theory intensely. According to Von Stengel [11], John Nash advanced the tools and ideas of game theory proposing the universal ‘cooperative bargaining and non-cooperative theories’. He brought forth what is presently termed the ‘Nash equilibrium’ of a tactical game in 1951 by establishing that every finite game at all times has an equilibrium point, at which every competitor selects moves which are paramount for them being fully aware of the selections or picks of his/her competitors or challengers. Game theory was expanded theoretically and used to solve warlike, political and philosophical issues in the 1950s and 1960s. The concept of game theory has since received much attention. A number of papers in the theory and applications of queuing theory have been reported in the literature. Moulin and Vial [12] proposed a class of games with special payoff structure called strategic zero-sum games. Ponssard and Sorin [13] discussed zero-sum games with incomplete information. McCabe et al [14] studied a three-person matching pennies game. Athey [15] studied incomplete information games and proposed a restriction called single crossing condition for those games. Chang and Marcus [16] studied two-person zero-sum game taking into consideration optimal equilibrium game value and then analysis of error bounds. Maeda [17] considered games that have fuzzy payoffs. Edmonds and Pruhs [18] proposed a randomized algorithm taking into consideration cake cutting algorithm. Al-Tamimi et al [19] discussed Q-learning designs for the zero-sum game and used a model-free approach to obtain a solution for the game. Larsson et al [20] studied signal processing and communications in a game theoretic way. Li and Cruz [21] used a zero-sum game model with an asymmetrical structure to study deception. Singh [1] presented optimal solution strategy for games. Duersch et al [22] obtained Nash equilibrium for the 2-player symmetric game. Perea and Puerto [23] used game theory approach in network security. They examined a game between a network operator and attackers. Procaccia [24] discussed a cake cutting game describing it as a powerful tool to divide heterogeneous goods and resources. Spyridopoulos [25] studied a problem of cyber-attacks using a zero-sum one-shot game theoretic model. Bell et al [26] proposed a game theoretic approach for modelling degradable transport networks. Bensoussan et al [27] studied non-zero-sum stochastic differential game and modelled the performance of two insurance companies. Gensbittel [28] studied zero-sum incomplete information games. Grauberger and Kimms [29] computed Nash equilibrium points for network revenue management games. Singh and Hemachandra [30] presented Nash equilibrium for stochastic games with independent state processes. Marlow and Peart [31] studied soil acidification describing a zero-sum game between a sugar maple and American beach. Boah et al [32] used game theory to analyse the levels of patronage two radio stations in Kumasi, Ghana. Daskalakis et al [33] proposed a no-regret algorithm capable of achieving regret when applied against an adversary. Bockova et al [34] applied game theory to project management. Farooqui and Niazi [35] reviewed game theory models for communication between agents. Gryzl et al [36] presented how game theory can be employed to manage conflict in a construction contract. Stankova et al [37] presented how treatment of cancer can be optimized using game theory. Padarian et al [38] employed game theory to interpret a ‘digital soil mapping model’. To the best of our knowledge and from literature, reviews of solution methods and techniques for solving games in game theory are not sufficient and comprehensive enough. The study was therefore intended to fill or narrow that knowledge or research gap. \\n### 3. Main Work\\n\\nThe solution methods and techniques usually employed to solve games in game theory are as discussed below:**(i) Nash Equilibrium Method**Nash equilibrium is a solution method of a ‘non-cooperative’ game concerning two or more competitors in which each competitor is assumed to have knowledge of the equilibrium or stability tactics of the other competitors, and no competitor has whatsoever to gain by varying only his/her individual tactic. If each competitor has selected a tactic or strategy, and no competitor can profit by varying tactics while the other competitors hold onto their tactics or strategies, then the present chosen tactics or strategies and their analogous utilities constitute ‘Nash equilibrium’. For example, Daniela and Derrick will be in ‘Nash equilibrium’ if Daniela makes the finest decision possible, taking into account Derrick’s decision which remains constant, and Derrick also makes the finest decision possible, taking into account Daniela's decision which remains constant. Similarly, a set of competitors will be in ‘Nash equilibrium’ if every single one makes the finest decision possible, taking into consideration the decisions of the other competitors in the contest which remain constant. According to Nash, “there is Nash equilibrium for every finite game”. Experts in game theory utilize the concept of ‘Nash equilibrium’ to examine the consequence of tactical collaboration of a number of decision makers. Ever since the concept of ‘Nash equilibrium’ was developed, experts in the field of game theory have revealed that, it creates misrepresentative forecasts or predictions in some situations according to Nash Jr [39].**(ii) Pareto Optimality Technique**Pareto optimality was first introduced by Vilfredo Pareto according to Yeung and Petrosjan [40]. In a Pareto optimal game, there exists a strategy or tactic that increases a player’s gain without harming others. For example, when an economy is perfectly competitive, then it is Pareto optimal. This is because no changes in the economy can improve the gain of one person and worsen the gain of another simultaneously. **(iii) Shapley Values Technique**According to Shapley [41], this is a solution technique or concept used in cooperative game theory. It assigns a distribution to all the players in a game. The distribution is unique and the value of the game depends on some desired abstract characteristics. In simple words, Shapley value assigns credits among a group of cooperating players. For example, let us assume that there are three red, blue and green players and the red player cooperates more than the blue and green players. Now, if the objective or goal is to form pairs and then assign credits to them, each pair must have a red player as it cooperates more than the other two. Therefore, there can be two possible pairs namely red player-blue player and red player-green player. The red player cooperates more and for that matter will get more profit than the blue player in the first pair. Similarly, the red player will get more profit than the green player in the second pair.**(iv) Maximin-Minimax Method**This method is used in both games with pure and mixed strategies. In the case of a game with pure strategies, the exploiting player reaches his/her optimum strategy on the basis of the maximin principle. Conversely, the diminishing player reaches his/her optimum strategy on the basis of the minimax principle. The difference between pure and mixed strategy games is that, pure strategy games possess saddle points whereas mixed strategy games do not. According to Washburn [42], a saddle point is a point at which ‘the minimum of column maximum’ is equal to ‘the maximum of row minimum’. If both the ‘maximin’ and ‘minimax’ values are zero, such a game is referred to as a fair game. When both the maximin and minimax values are equal but not zero, then the game is referred to as strictly determinable.\\n**Table 1****.** Pay-off Matrix for Players A and B ![Image 9: A matrix game with two players, A and B, with payoffs for player A shown in the matrix](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_001.gif)\\nTo illustrate the Maximin-Minimax Method, the value of the game, V for player A in the payoff matrix given in Table 1 is obtained as follows:\\n**Table 2****.** Pay-off Matrix for Players A and B with Column Maximum and Row Minimum Values ![Image 10: This is a table showing the row minimums and column maximums for Player A and Player B, with the values 4, 3, 2, 2, 6, 5, 1, 1, 6, 5, and 2 filling the table](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_002.gif)\\nFrom Table 2, Maximum of Row Minimum (Maximin) is 2 and Minimum of Column Maximum (Minimax) is also 2. Now, since Maximin = Minimax = 2, the saddle point of the game is 2. Therefore, the value of the game for Player A is 2.**(v) Dominance Method**This method can be applied to both games with pure strategies and games with mixed strategies. In a game involving pure strategies, the overall solution is readily found after ‘the dominance method’ has been applied to reduce the dimension of the problem. In a game with mixed strategies, the dominance method can be employed to reduce the dimension of the problem before using other methods to solve the problem entirely. According to Piyush [43], given a mixed strategy game, the dominance method is applied as follows:(i) If all the entries of column i are bigger than or the same as the equivalent entries of any other column, j, then column i is inferior to column j and it is deleted from the pay-off matrix.(ii) If every entry of row i is smaller than or the same as the equivalent entry of any other row, j, then row i is inferior to row j and it is deleted from the pay-off matrix. (iii) Repeat (i) and (ii) if any row or column is dominated, otherwise stop the process.The Dominance Method is illustrated with the pay-off matrix, ![Image 11: A 80x57 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_003.gif) by first of all deleting Row 3 which is dominated by Row 2 to obtain ![Image 12: A 76x35 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_004.gif) and finally deleting Column 3 which is dominated by Column 1 to obtain ![Image 13: A 51x37 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_005.gif). **(vi) Arithmetic Method**This is a method that provides a comprehensible approach for obtaining optimal strategies for every player in a 2 ![Image 14: A 14x16 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_006.gif) 2 pay-off matrix with no saddle point. If the payoff matrix is lengthier than 2 ![Image 15: A 14x16 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_007.gif) 2, then the dominance method would be employed and finally the algebraic procedure to help obtain the optimal strategies and also the value of the game. This method or procedure is also called the Method of the Oddments according to Piyush [43].\\n**Table 3****.** A Pay-off Matrix for Players E and F ![Image 16: Two-by-two table showing Player E vs Player F with the values a, b, c, and d](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_008.gif)\\nAccording to Piyush [43], given a pay-off matrix as in Table 3, the procedures followed in the arithmetic method are as given below: (i) Find the absolute value of the difference between the two values of row 1 (i.e. **| a-b|)** and put this value against row 2.(ii) Find the absolute value of the difference between the two values of row 2 (i.e. **| c-d |)** and put this value against row 1.(iii) Find the absolute value of the difference between the two values of column 1 (i.e. **| a-c |)** and put this value against column 2.(iv) Find the absolute value of the difference between the two values of column 2 (i.e. **| b-d |)** and put this value against column 1.(v) Find the probabilities of each entry as follows:• ![Image 17: A 308x33 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_009.gif)• ![Image 18: A 308x31 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_010.gif)Steps (i) to (v) therefore yield the resulting matrix as given in Table 4.\\n**Table 4****.** Resulting Pay-off Matrix for Players E and F ![Image 19: A table describing the odds and probabilities of two players, E and F, in a game involving variables a, b, c, d, N, x1, x2, y1, and y2](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_011.gif)\\n(vi) Obtain the value of the game, V by using the algebraic method as follows:![Image 20: Mathematical equations arranged vertically are displayed](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_012.gif)**(vii) Matrix Method**This method provides a way of finding optimal strategies for players in 2 ![Image 21: A 13x13 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_013.gif) 2 games. Given a payoff matrix ![Image 22: A 107x38 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_014.gif), the best strategies for both players A and B could be obtained in addition to the value of the game for player A.Player ![Image 23: A 24x21 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_015.gif) optimal strategies ![Image 24: A 115x40 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_016.gif)![Image 25: A 67x25 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_017.gif)Player ![Image 26: A 24x22 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_018.gif) optimal strategies ![Image 27: A 161x40 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_019.gif)The value of the game, V= (Player ![Image 28: A 26x22 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_020.gif) optimal strategies) × (Payoff matrix, P) × (Player ![Image 29: A 27x21 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_021.gif) optimal strategies)That is, ![Image 30: A 169x34 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_022.gif) where ![Image 31: A 50x19 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_023.gif) and ![Image 32: A 23x19 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_024.gif)![Image 33: A 19x18 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_025.gif) represent the probabilities of players A and B’s optimal strategies respectively. ![Image 34: A 29x23 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_026.gif) and ![Image 35: A 30x22 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_027.gif) are the adjoint and cofactor of the payoff matrix respectively as presented by Piyush [43].**(viii)****Graphical Method**The graphical method is used to solve games with no saddle points. It is restricted to 2 x m or n x 2 matrix games only according to Kumar and Reddy [8]. To illustrate the graphical method, let us consider the payoff matrix given in Table 5.\\n**Table 5****.** A Pay-off Matrix for Players G and H ![Image 36: A payoff matrix comparing the strategies of two players, Player G and Player H, with values for each combination of strategies](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_028.gif)\\nTesting for saddle point, we have the following:\\n**Table 6****.** Pay-off Matrix for Players G and H with Column Maximum and Row Minimum Values ![Image 37: The payoff matrix shows the values for each combination of moves by Player G and Player H, as well as the row minimum and column maximum](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_029.gif)\\nFrom Table 6, Maximum of Row Minimum (Maximin) is 3 and Minimum of Column Maximum (Minimax) is 4. Since Maximin ![Image 38: A 13x13 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_030.gif) Minimax, the game has no saddle point. Therefore, the problem is handled using the graphical method as follows:\\n![Image 39: A 100x60 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_031.gif)**Figure 1****.** Graph of the Payoff Matrix for Players G and H\\nThe point, V at the shaded region represents the value of the game for player G which is 3.4 units as shown in Figure 1.The steps involved are as follows:i. First draw two parallel lines and mark a scale of 1 unit each on each line.ii. The two parallel lines represent strategies of player H.The value, 1 is plotted along the ordinate axis under strategy H 1 and the value, -3 is plotted along the ordinate axis under strategy H 2.iii. A straight line joining the two points is then drawn.iv. Similarly, plot strategies _G_ _2_, _G_ _3_, _G_ _4_, _G_ _5_, _G_ _6_.v. The problem is then graphed.The point, V in the shaded region denotes the value of the game. The point of optimal solution occurs at the intersection of two lines:\\n![Image 40: A 109x21 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_032.gif)(1)\\n\\n![Image 41: A 108x21 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_033.gif)(2)\\nEquating equations [1] and [2], we have\\n![Image 42: A 150x23 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_034.gif)(3)\\nSubstituting ![Image 43: A 81x19 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_035.gif) into equation (3), we have![Image 44: A 230x24 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_036.gif)yielding ![Image 45: A 51x31 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_037.gif) and ![Image 46: A 172x30 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_038.gif)Substituting the values of ![Image 47: A 67x22 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_039.gif) into equation (1), we obtain![Image 48: A 199x33 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_040.gif)Therefore, the value of the game, V to Player G is 3.4 as already indicated.**(ix) Linear Programming Method**Linear programming (LP), also called linear optimization, is a method used to achieve the best outcome for an objective (such as maximum profit or minimum cost or other measures of effectiveness) in a mathematical model whose requirements are represented by linear relationships according to Williams [44]. According to Sharma [45], the two-person zero-sum games can be solved by LP. The major advantage of using linear programming technique is that, it helps to handle mixed-strategy games of higher dimension payoff matrix. To illustrate the transformation of a game problem to an LP problem, let us consider a payoff matrix of size m x n. Let ![Image 49: A 23x22 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_041.gif) be the entry in the ![Image 50: A 22x24 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_042.gif) row and ![Image 51: A 24x23 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_043.gif) column of the game’s payoff matrix, and let ![Image 52: A 17x22 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_044.gif) be the probabilities of m strategies ![Image 53: A 102x21 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_045.gif) for player A. Then, the expected gains for Player A, for each of Player B’s strategies will be:![Image 54: A 193x26 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_046.gif)The aim of player A is to select a set of strategies with probability ![Image 55: A 126x22 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_047.gif) on any play of game such that he can maximize his minimum expected gains. Now to obtain values of probability ![Image 56: A 20x18 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_048.gif) the value of the game to player A for all strategies by player B must be at least equal to V. Thus to maximize the minimum expected gains, it is necessary that:![Image 57: A system of equations with variables and coefficients is shown, where each equation is greater than or equal to a variable V](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_049.gif)where ![Image 58: A 154x20 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_050.gif) and ![Image 59: A 45x21 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_051.gif)_for all_![Image 60: A 9x18 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_052.gif)Dividing both sides of the m inequalities and equation by V, the division is valid as long as ![Image 61: A 47x19 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_053.gif) In case, ![Image 62: A 47x21 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_054.gif) the direction of inequality constraints is reversed. If ![Image 63: A 48x20 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_055.gif) the division becomes meaningless. In this case, a constant can be added to each of the entries of the matrix, ensuring that the value of the game (V) for the revised or updated matrix becomes more than zero. After the optimal solution is obtained, the actual value of the game is found by deducting the same constant. Let ![Image 64: A 107x21 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_056.gif) we then have![Image 65: Mathematical equations and inequalities are displayed in the image](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_057.gif)Since the objective of player A is to maximize the value of the game, V, which is equivalent to minimizing ![Image 66: A 15x31 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_058.gif), the resulting linear programming problem, can be stated as:![Image 67: A 279x33 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_059.gif)Subject to the constraints: \\n![Image 68: Several lines of mathematical equations with symbols and numbers](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_060.gif)(4)\\nwhere, ![Image 69: A 181x32 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_061.gif)Similarly, player B has a similar problem with the inequalities of the constraints reversed, i.e. minimize the expected losses. Since minimizing V is equivalent maximizing ![Image 70: A 14x30 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_062.gif), the resulting linear programming problem can be stated as:![Image 71: A 279x34 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_063.gif)Subject to the constraints: \\n![Image 72: Mathematical formula of inequalities](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_064.gif)(5)\\n_where,_![Image 73: A 179x31 small image, likely a logo, icon or avatar](http://article.sapub.org/image/10.5923.j.jgt.20200902.01_065.gif)\\n### 4. Conclusions\\n\\nIn this paper, a comprehensive review of solution methods and techniques usually employed in game theory to solve games has been presented with the view of demystifying and making them easy to understand. Specifically, the solution methods and techniques discussed are Nash Equilibrium Method; Pareto Optimality Technique; Shapley Values Technique; Maximin-Minimax Method; Dominance Method; Arithmetic Method; Matrix Method; Graphical Method and Linear Programming Method. The study has contributed significantly to knowledge by filling or narrowing the knowledge or research gap of insufficient literature on reviews of solution methods and techniques for solving games in game theory. \\n\\n### References\\n\\n* * *\\n\\n[1]Singh, A. P. (2010). Optimal solution strategy for games, _International Journal of Computer Science and Engineering,_ 2 (9), 2778-2782.\\n[2]Bhuiyan, B. A. (2016). An overview of game theory and some applications, Philosophy and Progress. LIX-LX, pp. 113-114. (accessed: January 15, 2020).\\n[3]Darkwah, K. and Bashiru, A. (2017). Game theory model of consumers’ response to mobile telecommunication service offers - A case study of Motens and Vodag in the Tamale Metropolis in Ghana, _International Journal of Advanced Research,_ 5, 1352-1365.\\n[4]Morrow, J. D. (1994). Game theory for political scientists, Princeton University Press, Princeton.\\n[5]Shoham, Y. and Leyton-Brown, K. (2008). Multi-agent systems: algorithmic, game-theoretic, and logical foundations. Cambridge University Press, New York.\\n[6]Washburn, A. R. (2003). Two-person zero-sum games. Springer, Berlin.\\n[7]Hillier, F. S. and Lieberman, G. J. (2001). Introduction to Operations Research, 7th edition, McGraw-Hill-New York, p. 726-748.\\n[8]Kumar, S. and Reddy, D. S. N. (1999). Graphical solution of n x m matrix of a game theory, _European Journal of Operational Research,_ 112 (2), 467- 471.\\n[9]Dixit, A. K. and Skeath, S. (1999). Games of strategy. Norton, New York.\\n[10]Geckil, I. K. and Anderson, P. L. (2009). Applied game theory and strategic behavior. CRC Press, Boca Raton.\\n[11]Von Stengel, B. (2008). Game Theory Basics. Department of Mathematics, London School of Economics, Houghton St, London WC2A 2AE, UK, Page 50.\\n[12]Moulin, H. and Vial, J. P. (1978). Strategically zero-sum games: the class of games whose completely mixed equilibria cannot be improved upon, _International Journal of Game Theory,_ 7 (3–4), 201–221.\\n[13]Ponssard, J. P. and Sorin, S. (1980). Some results on zero-sum games with incomplete information: the dependent case, _International Journal of Game Theory,_ 9 (4), 233–245.\\n[14]McCabe, K. A., Mukherji, A. and Runkle, D. E. (2000). An experimental study of information and mixed-strategy play in the three-person matching-pennies game, _Economic Theory,_ 15 (2), 421–462.\\n[15]Athey, S. (2001). Single crossing properties and the existence of pure strategy equilibria in games of incomplete information, _Econometrica,_ 69 (4), 861–889.\\n[16]Chang, H. S. and Marcus, S. I. (2003). Two-person zero-sum Markov games: receding horizon approach. _IEEE Trans Autom Control,_ 48 (11), 1951–1961.\\n[17]Maeda, T. (2003). On characterization of equilibrium strategy of two-person zero-sum games with fuzzy payoffs, _Fuzzy Sets Systems,_ 139 (2), 283–296.\\n[18]Edmonds, J. and Pruhs, K. (2006). Balanced allocations of cake. In: Null, IEEE, New York, p. 623–634.\\n[19]Al-Tamimi, A., Lewis, F. L. and Abu-Khalaf, M. (2007). Model-free q-learning designs for linear discrete-time zero-sum games with application to h-infinity control, _Automatica,_ 43 (3), 473–481.\\n[20]Larsson, E. G., Jorswieck, E. A., Lindblom. J. and Mochaourab, R. (2009). Game theory and the flat-fading gaussian interference channel, _IEEE Signal Process Magazine,_ 26 (5), 18–27.\\n[21]Li, D. and Cruz, J. B. (2009). Information, decision-making and deception in games, _Decision Support System,_ 47 (4), 518–527.\\n[22]Duersch, P., Oechssler, J. and Schipper, B. C. (2012). Pure strategy equilibria in symmetric two-player zero-sum games, _International Journal of Game Theory,_ 41 (3), 553–564.\\n[23]Perea, F. and Puerto, J. (2013). Revisiting a game theoretic framework for the robust railway network design against intentional attacks,_European Journal of Operations Research,_ 226 (2), 286–292.\\n[24]Procaccia, A. D. (2013). Cake cutting: not just child’s play, _Commun ACM,_ 56 (7), 78–87.\\n[25]Spyridopoulos, T. (2013). A game theoretic defence framework against DoS/DDoS cyber-attacks, _Computer Security,_ 38, 39–50.\\n[26]Bell, M. G. H., Fonzone, A. and Polyzoni, C. (2014). Depot location in degradable transport networks, Transp Res Part B Methodology, 66, 148–161.\\n[27]Bensoussan, A., Siu, C. C., Yam, S. C. P. and Yang, H. (2014). A class of non-zero-sum stochastic differential investment and reinsurance games, _Automatica,_ 50 (8), 2025–2037.\\n[28]Gensbittel, F. (2014). Extensions of the cav (u) theorem for repeated games with incomplete information on one side, Mathematics and Operations Research, 40 (1), 80–104.\\n[29]Grauberger, W. and Kimms, A. (2014). Computing approximate Nash equilibria in general network revenue management games, European Journal of Operational Research, 237(3), 1008–1020.\\n[30]Singh, V. V. and Hemachandra, N. (2014). A characterization of stationary Nash equilibria of constrained stochastic games with independent state processes, _Operations Research Letter,_ 42 (1), 48–52.\\n[31]Marlow, J. and Peart, D. R. (2014). Experimental reversal of soil acidification in a deciduous forest: implications for seedling performance and changes in dominance of shade-tolerant species, _Forestry, Ecology and Management,_ 313, 63–68.\\n[32]Boah, D. K., Twum, S. B. and Amponsah, S. K. (2014). Patronage of two radio stations in Kumasi using game theory, _Journal of Innovative Technology and Education,_ 1, 17-23.\\n[33]Daskalakis, C., Deckelbaum, A. and Kim, A. (2015) Near-optimal no-regret algorithms for zero-sum games, _Games and Economic Behaviour,_ 92, 327–348.\\n[34]Bockova, K. H., Slavikova, G. and Gabrhel, J. (2015). Game theory as a tool of project management, _Procedia – Social and Behavioral Sciences,_ 213, 709 – 715.\\n[35]Farooqui, A. D. and Niazi, M. A. (2016). Game theory models for communication between agents: a review, _Complex Adaptive Systems Modeling,_ 4 (13), 1 - 31.\\n[36]Gryzl, B., Apollo, M. and Kristowski, A. (2019). Application of game theory to conflict management in a construction contract, _Sustainability,_ 11, 1 – 12.\\n[37]Stankova, K., Brown, J. S. and Dalton, W. S. (2019). Optimizing cancer treatment using game theory, _JAMA Oncology,_ 5 (1), 96 - 103.\\n[38]Padarian, J., McBratney, A. B. and Minasny, B. (2020). Game theory interpretation of digital soil mapping convolutional neural networks, _SOIL,_ 6, 389 – 397.\\n[39]Nash Jr, J. F. (1950). Equilibrium points in n-person games, _Proceedings of the National Academy of Sciences,_ 36 (1), 48 – 49.\\n[40]Yeung, D. W. K. and Petrosjan, L. A. (2006). Cooperative stochastic differential games. Springer Science & Business Media, Berlin.\\n[41]Shapley, L. S. (1953). A value for n-person games, _Contrib Theory Games,_ 2, 307–317.\\n[42]Washburn, A. R (2003). Two-person zero-sum games, Springer, Berlin.\\n[43]Piyush, N. S. (2005). Game theory problem solving using linear programming method and examples. https://cbom.atozmath.com/example/CBOM/GameTheory.aspx?he=e&q=lpp. (accessed: April 23, 2020).\\n[44]Williams, H. P. (2013). Model building in mathematical programming, 5th Edition. John Wiley & Sons Ltd., England.\\n[45]Sharma, J. K. (2010). Quantitative Methods-Theory and Applications, Macmillan, 168 - 170.\\n\\n[Home](http://www.sapub.org/journal/index.aspx)|[About Us](http://www.sapub.org/journal/aboutus.aspx)|[Terms and Conditions](http://www.sapub.org/journal/termsandcondition.aspx)|[Privacy Policy](http://www.sapub.org/journal/privacypolicy.aspx)|[Feedback](mailto:<EMAIL>)|[Sitemap](http://www.sapub.org/sitemap/index.html)|[Contact Us](http://www.sapub.org/journal/contactus.aspx)\\n\\n Copyright © 2020 Scientific & Academic Publishing Co. All rights reserved.\",\n", "  \"http://strategicreasoning.org/wp-content/uploads/2010/03/MethodsEmpiricalGameTheoretic.pdf\": \"# Methods for Empirical Game-Theoretic Analysis (Extended Abstract) \\n\\n# <PERSON>man \\n\\nUniversity of Michigan Computer Science & Engineering Ann Arbor, MI 48109-2121 USA \\n\\<EMAIL> \\n\\nAbstract \\n\\n> An emerging empirical methodology bridges the gap between game theory and simulation for practical strategic reasoning.\\n\\n# Game-Theoretic Analysis \\n\\nGame-theoretic analysis typically takes at its starting point, most naturally, a description of its subject—the game, a for-mal model of a multiagent interaction. The recent surge in interest among AI researchers in game theory has led to nu-merous advances in game modeling (Gal & Pfeffer 2004; <PERSON><PERSON><PERSON>, Littman, & Singh 2001; <PERSON> & Milch 2003; Leyton-Brown & Tennenholtz 2003) and solution tech-niques (Gilpin & Sandholm 2006; <PERSON>, <PERSON>, & <PERSON> 2004), substantially expanding the class of games amenable to computational analysis. Nevertheless, a great many games of interest lie well beyond the boundary of tractable modeling and reasoning. Complexity may be man-ifest in the number of agents or the size of their strategy sets (policy spaces), or the degree of incomplete and imperfect information. The issue here is not merely computational complexity of the analysis task (e.g., finding equilibrium), but actually the apparent impracticality of producing an ex-plicit game model amenable to automated reasoning. For example, consider the Trading Agent Competition Supply Chain Management (TAC/SCM) game (Eriksson, Finne, & Janson 2006). This is a well-defined six-player symmetric game of imperfect information, with interaction rules and exogenous stochastic processes described in a brief specification document. There is nothing particularly un-usual about this game, nevertheless it presents a difficult challenge for game-theoretic analysis. The policy spaces and payoff functions are clearly induced by the specified rules, but the description is quite indirect. Even given com-plete policies for all six agents, there is no apparent means to derive the expected payoffs, short of sampling from the stochastic environment using an available game simulator. In this case, each sample point takes an hour to compute.   \\n\\n> Copyright c©2006, American Association for Artificial Intelli-gence (www.aaai.org). All rights reserved.\\n\\n# Empirical Games \\n\\nThe approach we have been pursuing in my research group for the past few years 1 is to take the game simulator as the fundamental input, and perform strategic reasoning through interleaved simulation and game-theoretic analysis. The ba-sic object of analysis is an empirical game, a description of the interaction scenario where payoff information comes in the form of data from observations or simulations. Con-structing and reasoning about such games presents many in-teresting subproblems, which can be addressed by existing as well as new methods from simulation, statistics, search, and of course, standard game-theoretic analysis. I find it useful to decompose empirical game-theoretic analysis into three basic steps. Many of the research con-tributions in this area manifest as techniques applicable to one of these subproblems, or results from approaches taken to them in a given domain. \\n\\n## Parametrize Strategy Space \\n\\nOften the complexity of a game resides in vast policy spaces available to agents. Large spaces can arise, for example, from continuous or multi-dimensional action sets, as well as from imperfect information (when actions are conditioned on observation histories). It is often useful in such cases to approximate the game by restricting the strategy space, and structuring the space to admit a sensible search procedure. Results from analysis of restricted subgames often provide insight into the original game. Arguably, all applications of game theory in the social sciences employ stylized abstrac-tions, which are manually designed restricted versions of ac-tual games. From our perspective the interesting question is how to automate the abstraction process starting from a rich but intractable game specification. One generic approach to generating candidate strategies is to start from some baseline or skeletal structure, and sys-tematically introduce parametric variations. Some examples of natural baselines include: 1. Truthful revelation. For example, in an auction game, the baseline would be to bid one’s true value. In the first- \\n\\n> 1Similar or identical techniques have also been employed by other researchers, especially those working experimentally with multiagent systems. Our main claim is in systematizing the methodology, in explicit game-theoretic terms.\\n\\n1552 price sealed-bid auction, this strategy guarantees zero sur-plus (!), but it turns out that the one-dimensional family of strategies defined by shading one’s bid by a multiplicative factor includes excellent strategies, including the unique symmetric equilibrium (Reeves 2005). 2. Myopic best response. For example, in simultaneous auc-tions (SAAs), a natural starting point is straightforward bidding (Milgrom 2000), where the agent bids as though the current prices are final. We have explored an extensive family of bidding strategies for SAAs starting from this baseline, ultimately producing what we consider the lead-ing contender in this domain (Osepayshvili et al. 2005). 3. Game tree search. The starting point for most programs designed to play complete-information turn-taking zero-sum games is minimax search. In a recent study of a 4-player chess game (Kiekintveld, Wellman, & Singh 2006), we defined the strategy space as a set of paramet-ric variations on the basic game search architecture (e.g., control knobs for search depth and evaluation function weights). \\n\\n## Estimate Empirical Game \\n\\nTo illustrate some concepts associated with empirical games, we employ an example from a recent analysis of agents from the 2005 TAC/SCM tournament (Wellman et al. 2006). Fig-ure 1 displays the empirical game, estimated from a sample of over 2000 game instances played with various combina-tions of six agent strategies. We describe the interpretation of this diagram in the course of explaining the game estima-tion and analysis. \\n\\nDirect Estimation The most straightforward approach to estimate an empirical game from data is to treat the observa-tions as direct evidence for the payoffs of the strategy pro-files played. Toward this end we can bring to bear all the tools of Monte Carlo analysis, and related statistical tech-niques. We have found especially useful the method of con-trol variates (L’Ecuyer 1994) for reducing variance based on adjusting for observable factors with known effects on pay-offs. In the case of TAC/SCM, the most important factor is customer demand, which can significantly influence profits regardless of agent strategy. Applying control variates, we derive a measure of demand-adjusted profit , which we then employ as a proxy for payoffs in the empirical game estima-tion (Wellman et al. 2005a). Each node in the graph of Figure 1 represents a pro-file of agent strategies. TAC/SCM is a 6-player symmetric game, and so with six possible strategies there are a total of (11 5\\n\\n) = 462 distinct strategy profiles to consider. We can re-duce the game to a smaller version by requiring multiples of players to play the same strategy. Specifically, by restricting attention to cases where strategies are assigned to pairs of agents, we get an effective 3-player game, which we denote SCM ↓3. This game is combinatorially smaller, comprising only (83\\n\\n) = 56 profiles over the same 6-strategy set. The payoff to a strategy in an SCM ↓3 profile is defined as the \\n\\naverage payoff to the two agents playing this strategy in the original 6-player game. In several contexts, we have found experimentally and theoretically that this form of hierarchical game reduction \\n\\nproduces results approximating well the original unreduced game, with great computational savings (Reeves 2005; Well-man et al. 2005b). Although we have not validated this specifically in TAC/SCM, intuitively we would expect that payoffs vary smoothly with the number of other agents play-ing a given strategy. Our 2110 sample game instances (each requiring seven processor-hours to generate, not counting setup time and overhead due to failures) are distributed roughly evenly over the 56 SCM ↓3 profiles. In general, one may wish to de-ploy samples in a much more actively targeted manner. In other studies, we allocate samples with a view toward con-firming or refuting candidate equilibria. The idea is to focus on promising profiles, and their neighbors in profile space— profiles that differ in the strategy choice of one agent. Walsh et al. (2003) have proposed information-theoretic criteria for selecting profiles to sample, and other approaches from Monte Carlo analysis and active learning should be applica-ble as well. One special issue for empirical games is the need to han-dle partial coverage of observation data. Although in our illustrative example we have payoff estimates for all possi-ble profiles, in many cases this will not be possible. We have found it useful in such cases to classify a profile s into one of four disjoint categories: 1. If the profile s has not been empirically evaluated (i.e., sampled), then we say it is unevaluated .2. Otherwise, and for some neighbor t of s, the estimated payoff for the deviating agent is greater in t than s. In this case, we say s is refuted .3. Otherwise, and some neighbor t of s is unevaluated. In this case, we say s is a candidate .4. Otherwise, in which case we say s is confirmed .In the empirical game of Figure 1, all profiles are evalu-ated. There is an arrow from each profile to its best devia-tion, which we define as the neighbor offering the greatest (positive) gain to the agent switching strategies to reach it. Deviations that are statistically significant are indicated by solid arrows. Since all nodes have outgoing arrows, we find that every profile is refuted in this empirical game, and there-fore there are no pure Nash equilibria. \\n\\nRegression An alternative approach to game estimation attempts to generalize the data across profiles, including those that are unevaluated in the sense of our classification above. The idea is to apply machine learning (regression) techniques to fit a payoff function over the entire profile space given the available data (Vorobeychik, Wellman, & Singh 2005). This approach offers the potential for reason-ing over very large, even infinite, profile spaces. \\n\\n## Analyze Empirical Game \\n\\nAnalyzing an empirical game is much like analyzing any other; standard methods apply. Given the inherent uncer-tainty in reasoning about empirical games, it may be espe-1553 Figure 1: Deviation analysis of pure profiles of SCM ↓3.cially appropriate to consider approximate equilibrium con-cepts, or more generally to reason about degrees of game-theoretic stability rather than categorical classes. Let \\u000f(s)\\n\\ndenote the maximum gain to deviating from s, over all agents and evaluated neighbors of s. If all neighbors are evaluated, then profile s is an \\u000f(s)-Nash equilibrium, and for the special case \\u000f(s) = 0 it is a (confirmed) Nash equi-librium. If some neighbors are unevaluated, then \\u000f(s) repre-sents a lower bound on the \\u000f rendering s an \\u000f-Nash equilib-rium. The profiles in Figure 1 are arranged according to this \\u000f\\n\\nmeasure. Profiles in the inner ellipse have \\u000f ≤ 0.6M (“M” represents a million dollars), with succeeding outer rings corresponding to increasing levels of this measure as indi-cated. With this interpretation, we can draw several conclu-sions by direct inspection of the diagram. \\n\\n• Although no pure profiles are equilibria, some are much more stable than others. \\n\\n• Each of four most stable profiles (and 11 out of the top 13) involve at least one agent playing the Mertacor strategy. \\n\\n• Profiles where all agents play the same strategy (except \\n\\nPhantAgent ) are among the least stable. \\n\\n• Of the 35 profiles without Mertacor , 30 of them have a best deviation where some strategy changes to Merta-cor . Of the 21 profiles with Mertacor , the best deviation changes from Mertacor in only three. Of course, more specific analysis (e.g., deriving mixed-strategy equilibria) requires evaluation of the more precise payoff estimates. Given finite data, it is also important to apply sensitiv-ity analysis to the estimated game (Reeves 2005), or em-ploy statistical bounds in reasoning about its implications (Vorobeychik, Kiekintveld, & Wellman 2006). The \\u000f mea-sure is useful for both purposes; we can derive distributions over \\u000f(s) for each s, or provide confidence intervals with re-spect to \\u000f(s). These measures can also provide guidance in sampling strategy, for example we might focus on refuting profiles with low \\u000f(s). Finally, we have also employed the \\u000f\\n\\nconcept in evaluating techniques for payoff function regres-sion (Vorobeychik, Wellman, & Singh 2005). \\n\\n# Applications \\n\\nWe have applied this methodology to a variety of games, especially market-based scenarios. In several cases we have been able to support conclusions about strategic issues in these games not accessible through standard analytic means. \\n\\n• Verification that aggressive early procurement was a sta-ble (but very destructive) behavior in the original (2003) TAC/SCM game (Arunachalam & Sadeh 2005), and that the preemptive policy of Deep Maize was an effective remedy (Wellman et al. 2005a). \\n\\n• Identifying and validating a new strategy for dealing with the exposure problem in SAAs (Osepayshvili et al. 2005). \\n\\n• Systematically evaluating strategies for the TAC travel game, leading to a choice that proved highly effective in TAC-05 (Wellman et al. 2005c). Although much further work is required to develop the empirical game-theoretic approach into a precise, rigorous and fully automated methodology, the early results seem 1554 quite encouraging. Further innovations in technique and ex-perience in application promise to widen the scope of prac-tical game-theoretic reasoning. \\n\\n# Acknowledgments \\n\\nThe work described here owes much to collaborations with Jeffrey MacKie-Mason and Satinder Singh, and our many student co-authors. This work was supported in part by the National Science Foundation under grants IIS-0205435 and IIS-0414710. \\n\\n# References \\n\\nArunachalam, R., and Sadeh, N. M. 2005. The supply chain trading agent competition. Electronic Commerce Re-search and Applications 4:63–81. Eriksson, J.; Finne, N.; and Janson, S. 2006. Evolution of a supply chain management game for the trading agent competition. AI Communications 19:1–12. Gal, Y., and Pfeffer, A. 2004. Reasoning about rationality and beliefs. In Third International Joint Conference on Autonomous Agents and Multi-Agent Systems , 774–781. Gilpin, A., and Sandholm, T. 2006. Finding equilibria in large sequential games of imperfect information. In Sev-enth ACM Conference on Electronic Commerce .Kearns, M.; Littman, M. L.; and Singh, S. 2001. Graphi-cal models for game theory. In Seventeenth Conference on Uncertainty in Artificial Intelligence , 253–260. Kiekintveld, C.; Wellman, M. P.; and Singh, S. 2006. Em-pirical game-theoretic analysis of chaturanga. In AAMAS-06 Workshop on Game-Theoretic and Decision-Theoretic Agents .Koller, D., and Milch, B. 2003. Multi-agent influence di-agrams for representing and solving games. Games and Economic Behavior 45:181–221. L’Ecuyer, P. 1994. Efficiency improvement and variance reduction. In Twenty-Sixth Winter Simulation Conference ,122–132. Leyton-Brown, K., and Tennenholtz, M. 2003. Local-effect games. In Eighteenth International Joint Conference on Artificial Intelligence , 772–780. Milgrom, P. 2000. Putting auction theory to work: The simultaneous ascending auction. Journal of Political Econ-omy 108:245–272. Osepayshvili, A.; Wellman, M. P.; Reeves, D. M.; and MacKie-Mason, J. K. 2005. Self-confirming price pre-diction for bidding in simultaneous ascending auctions. In \\n\\nTwenty-First Conference on Uncertainty in Artificial Intel-ligence , 441–449. Porter, R.; Nudelman, E.; and Shoham, Y. 2004. Simple search methods for finding a Nash equilibrium. In Nine-teenth National Conference on Artificial Intelligence , 664– 669. Reeves, D. M. 2005. Generating Trading Agent Strategies: Analytic and Empirical Methods for Infinite and Large Games . Ph.D. Dissertation, University of Michigan. Vorobeychik, Y.; Kiekintveld, C.; and Wellman, M. P. 2006. Empirical mechanism design: Methods, with ap-plication to a supply chain scenario. In Seventh ACM Con-ference on Electronic Commerce .Vorobeychik, Y.; Wellman, M. P.; and Singh, S. 2005. Learning payoff functions in infinite games. In Nineteenth International Joint Conference on Artificial Intelligence ,977–982. Walsh, W. E.; Parkes, D.; and Das, R. 2003. Choosing samples to compute heuristic-strategy Nash equilibrium. In \\n\\nAAMAS-03 Workshop on Agent-Mediated Electronic Com-merce .Wellman, M. P.; Estelle, J.; Singh, S.; Vorobeychik, Y.; Kiekintveld, C.; and Soni, V. 2005a. Strategic interactions in a supply chain game. Computational Intelligence 21:1– 26. Wellman, M. P.; Reeves, D. M.; Lochner, K. M.; Cheng, S.-F.; and Suri, R. 2005b. Approximate strategic reasoning through hierarchical reduction of large symmetric games. In Twentieth National Conference on Artificial Intelligence ,502–508. Wellman, M. P.; Reeves, D. M.; Lochner, K. M.; and Suri, R. 2005c. Searching for Walverine 2005. In IJCAI-05 Workshop on Trading Agent Design and Analysis .Wellman, M. P.; Jordan, P. R.; Kiekintveld, C.; Miller, J.; and Reeves, D. M. 2006. Empirical game-theoretic analy-sis of the TAC market games. In AAMAS-06 Workshop on Game-Theoretic and Decision-Theoretic Agents .1555\",\n", "  \"https://iopscience.iop.org/article/10.1088/1742-6596/1988/1/012056/pdf\": \"We apologize for the inconvenience...\\n-------------------------------------\\n\\nTo ensure we keep this website safe, please can you confirm you are a human by ticking the box below.\\n\\nIf you are unable to complete the above request please contact us using the below link, providing a screenshot of your experience.\\n\\n[https://ioppublishing.org/contacts/](https://ioppublishing.org/contacts/)\",\n", "  \"https://www.numberanalytics.com/blog/a-brief-analysis-modern-game-theory-computation\": \"[![Image 1: person holding game controller in-front of television](https://images.unsplash.com/photo-1486572788966-cfd3df1f5b42?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3MDgyMDR8MHwxfHNlYXJjaHwxfHxjb21wdXRhdGlvbmFsJTIwZ2FtZSUyMHRoZW9yeXxlbnwwfHx8fDE3NDQ5NjUxNTF8MA&ixlib=rb-4.0.3&q=80&w=1080?utm_source=numberanalytics&utm_medium=referral)](https://images.unsplash.com/photo-1486572788966-cfd3df1f5b42?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3MDgyMDR8MHwxfHNlYXJjaHwxfHxjb21wdXRhdGlvbmFsJTIwZ2FtZSUyMHRoZW9yeXxlbnwwfHx8fDE3NDQ5NjUxNTF8MA&ixlib=rb-4.0.3&q=80&w=1080?utm_source=numberanalytics&utm_medium=referral)\\n\\nPhoto by [Glenn Carstens-Peters](https://unsplash.com/@glenncarstenspeters?utm_source=numberanalytics&utm_medium=referral) on [Unsplash](https://unsplash.com/?utm_source=numberanalytics&utm_medium=referral)\\n\\n* * *\\n\\nTable of Contents\\n-----------------\\n\\n1.   [Overview](https://www.numberanalytics.com/blog/a-brief-analysis-modern-game-theory-computation#overview)\\n2.   [Algorithmic Approach in Game Theory](https://www.numberanalytics.com/blog/a-brief-analysis-modern-game-theory-computation#algorithmic-approach-in-game-theory)\\n3.   [Equilibrium Analysis and Strategic Solutions](https://www.numberanalytics.com/blog/a-brief-analysis-modern-game-theory-computation#equilibrium-analysis-and-strategic-solutions)\\n4.   [Comparative Studies and Theoretical Insights](https://www.numberanalytics.com/blog/a-brief-analysis-modern-game-theory-computation#comparative-studies-and-theoretical-insights)\\n5.   [Real-World Applications](https://www.numberanalytics.com/blog/a-brief-analysis-modern-game-theory-computation#real-world-applications)\\n6.   [Conclusion](https://www.numberanalytics.com/blog/a-brief-analysis-modern-game-theory-computation#conclusion)\\n\\n* * *\\n\\nOverview\\n--------\\n\\nGame theory, historically rooted in classical economics and strategic decision-making, has undergone tremendous evolution in recent years due to computational advancements. Modern computation has allowed researchers and practitioners to analyze increasingly complex strategic scenarios that were once intractable using traditional methods. This article demystifies the computational techniques that underpin modern game theory, exploring algorithms, equilibria, and real-world applications.\\n\\nBest video game consoles\\n\\nIn our exploration, we will provide:\\n\\n*   An introduction to computational methods in game theory.\\n*   A detailed discussion of key algorithms and techniques utilized in the field.\\n*   An analysis of equilibrium concepts such as Nash equilibrium and their computation through iterative simulations and mathematical models.\\n*   Comparative insights into recent research juxtaposed with time-tested traditional methods.\\n*   Exploration of real-world applications in economics, politics, and technology.\\n\\nUnderstanding these components largely hinges on the computational approach – a tool that not only augments theoretical insights but also opens doors for effectively solving practical problems.\\n\\n* * *\\n\\nAlgorithmic Approach in Game Theory\\n-----------------------------------\\n\\nModern game theory computation leverages a combination of classical algorithms and innovative simulation techniques. Here, we dive into both these methods and their roles in enhancing strategic analysis.\\n\\nBest video game consoles\\n\\n### Key Algorithms and Techniques\\n\\nAs game theory scenarios become more complex, so too do the methods required to solve them. Below are some key approaches:\\n\\n*   **Dynamic Programming:**\\n\\n Many computational problems modeled in game theory, such as sequential games, rely on the principle of optimality. Dynamic programming enables optimal decision-making through backward induction, ensuring that decisions made at earlier stages take into account the expected future outcomes. For example, solving extensive-form games can often be treated as a problem for dynamic programming.\\n\\nBest video game consoles\\n\\n*   **Monte Carlo Simulations:**\\n\\n In cases where analytical solutions are difficult to derive, Monte Carlo simulations provide a probabilistic approach to evaluating strategies. By simulating thousands—or even millions—of iterations of a game, one can approximate equilibrium states and assess the variability in outcomes. This method is particularly useful for complex stochastic games.\\n\\n*   **Evolutionary Algorithms:**\\n\\n These algorithms mimic natural selection processes to explore strategy spaces. They are useful when the strategy space is large, and traditional methods falter. Over successive generations, strategies evolve towards stable configurations, effectively approximating equilibrium in dynamic environments.\\n\\n*   **Iterative Best Response Dynamics:**\\n\\n This method involves players updating their strategies iteratively based on the assumption that the opponents’ strategies are fixed. This continual adjustment often leads to convergence towards a Nash equilibrium in many types of games.\\n\\n### Classical vs. Computational Methods\\n\\nBest video game consoles\\n\\nA comparative view of these methods shows distinct strengths and limitations:\\n\\n| Feature | Classical Methods | Computational Methods |\\n| --- | --- | --- |\\n| Analytical Simplicity | High for simple games | High for complex, high-dimensional games |\\n| Scalability | Limited | Highly scalable with modern computing power |\\n| Handling Uncertainty | Often limited | Effectively handles stochastic elements |\\n| Real-time Applications | Challenging | Well-suited to simulation and rapid updates |\\n\\n_Reference:_ For an in-depth overview of these methods, see the [MIT OpenCourseWare on game theory](https://ocw.mit.edu/courses/economics/14-126-game-theory-fall-2010/).\\n\\n### Benefits and Limitations\\n\\n*   **Benefits:**\\n\\n    *   **Scalability:** Modern algorithms are designed to handle large-scale problems with many players and complex strategies.\\n    *   **Precision:** The use of computational methods increases the accuracy of equilibrium detection.\\n    *   **Adaptability:** These techniques can be tailored to various types of games, including zero-sum, cooperative, and non-cooperative models.\\n    \\nBest video game consoles\\n\\n*   **Limitations:**\\n\\n    *   **Computational Demand:** High complexity models may require significant computational resources.\\n    *   **Approximation Issues:** Results from simulations can sometimes be approximations rather than exact analytical solutions.\\n    *   **Model Dependencies:** The accuracy of these computational models is highly dependent on the assumptions built into them.\\n\\n* * *\\n\\nEquilibrium Analysis and Strategic Solutions\\n--------------------------------------------\\n\\nA central pillar of game theory is the notion of equilibrium—specifically, the Nash equilibrium. This section explores the computational techniques used to determine equilibrium and the strategic implications thereof.\\n\\nBest video game consoles\\n\\n### Nash Equilibrium and Its Computation\\n\\nThe Nash equilibrium represents a scenario where no player can benefit by unilaterally deviating from their strategy. In mathematical terms, if we denote the strategy profile by **s**, then **s** is a Nash equilibrium if, for every player _i_, the following condition holds:\\n\\nu i(s i∗,s−i∗)≥u i(s i,s−i∗) u_i(s_i^_, s\\\\_{-i}^_) \\\\geq u_i(s_i, s_{-i}^*)\\n\\nHere:\\n\\n*   u i u_i  is the utility function for player _i_.\\n*   s i∗ s_i^*  is the equilibrium strategy for player _i_.\\n*   s−i∗ s_{-i}^*  represents the strategies of all other players at equilibrium.\\n\\n### Iterative and Simulation Methods\\n\\nTo computationally attain the Nash equilibrium:\\n\\n*   **Iterative Best Responses:**\\n\\n Players repeatedly choose the best response to the strategies of others. This iterative process stops once the strategies converge, typically reaching an equilibrium state.\\n\\n*   **Regret Minimization Techniques:**\\n\\n These are based on the principle that players adjust their strategies based on historical performance, minimizing regret over time. Methods such as Counterfactual Regret Minimization (CFR) have been successfully applied in complex games like poker.\\n\\nBest video game consoles\\n\\n*   **Adaptive Learning Algorithms:**\\n\\n Algorithms that allow players to adapt their strategies over time, learning and converging towards equilibrium. The evolution of such strategies can be traced using simulations and differential equations.\\n\\n### Case Examples\\n\\n1.   **Auction Models:**\\n\\n In auction theory, computational methods have improved the design and analysis of auction mechanisms. Simulations allow researchers to determine bidding strategies and evaluate the robustness of various auction formats.\\n\\n2.   **Market Competition:**\\n\\n In oligopolistic markets, firms use iterative best response dynamics to adjust prices and outputs. Computational models provide insights into competitive equilibria that inform regulatory policies.\\n\\n3.   **Network and Internet Protocols:**\\n\\n In modern network technologies, game-theoretic models help optimize data routing and resource allocation. Equilibrium analysis here aids in designing protocols that are both efficient and fair.\\n\\n_Reference:_ For further reading on Nash equilibrium computation, check out the detailed study published in the [Journal of Economic Theory](https://www.journals.elsevier.com/journal-of-economic-theory).\\n\\n* * *\\n\\nComparative Studies and Theoretical Insights\\n--------------------------------------------\\n\\nThe evolution of computational game theory has been well-documented in recent research. Comparative studies help bridge the gap between classical approaches and modern computational methods.\\n\\n### Recent Research Highlights\\n\\n*   **Algorithmic Convergence:**\\n\\n Researchers have observed that iterative algorithms often converge more reliably when augmented with machine learning techniques. This cross-pollination fosters improved convergence rates and solution accuracy.\\n\\n*   **Computational Complexity Analysis:**\\n\\n Recent studies analyze the computational complexity of various algorithms. Understanding the trade-offs between computational time and precision is crucial for practical applications.\\n\\n*   **Hybrid Methods:**\\n\\n A growing body of research focuses on hybrid methods combining classical analytical tools with numerical simulations. These methods bridge the gap between theory and practice, delivering robust predictions in intricate scenarios.\\n\\n### Comparative Table: Classical vs. Modern Approaches\\n\\n| Aspect | Classical Game Theory | Modern Computational Game Theory |\\n| --- | --- | --- |\\n| **Complexity Handling** | Limited to simpler models | Can handle multi-agent, high-dimensional models |\\n| **Analytical Elegance** | High intellectual rigor | Emphasis on practical computation and simulations |\\n| **Data Integration** | Primarily theoretical | Leverages vast computational data streams |\\n| **Predictive Power** | Good for small-scale systems | Superior for large, dynamic, and networked games |\\n\\n### Theoretical Implications\\n\\nThe combination of computational and classical methods provides several valuable insights:\\n\\n*   **Validation of Classical Theorems:**\\n\\n Computational experiments often confirm the validity of long-standing theoretical results, reinforcing the robustness of classical models.\\n\\n*   **Discovery of Anomalies:**\\n\\n In certain scenarios, computational methods reveal complexities and anomalies not predicted by traditional theories. These findings spotlight the need for tailored analytical frameworks that account for “real-world” dynamics.\\n\\n*   **Enhanced Algorithm Design:**\\n\\n Insights from simulations help refine algorithmic strategies, ensuring that equilibrium solutions are both accurate and applicable to large-scale environments.\\n\\n_Reference:_ For a deeper dive into these comparative studies, see the paper on [Hybrid Game Theoretic Models](https://www.sciencedirect.com/science/article/pii/S0022002715001234) published by Elsevier.\\n\\n* * *\\n\\nReal-World Applications\\n-----------------------\\n\\nThe practical implications of computational game theory are far-reaching, impacting various fields such as economics, political science, and technology. In this section, we explore several real-world applications that exemplify these techniques.\\n\\n### Economics and Market Design\\n\\n*   **Auctions and Bidding Strategies:**\\n\\n Auction mechanisms, from online ad auctions to spectrum sales, benefit enormously from computational game theory. Algorithms help design auctions that maximize revenue while maintaining fairness. For example, Google’s ad auction system utilizes complex algorithms to balance competitive dynamics and optimize pricing strategies.\\n\\n*   **Market Competition:**\\n\\n In industries like telecommunications, firms engage in strategic pricing and output decisions. Models based on iterative and simulation methods enable these firms to predict competitor moves and adjust their strategies accordingly.\\n\\n### Political Science and Social Choice\\n\\n*   **Voting Systems:**\\n\\n Game theory provides insights into the design of voting mechanisms that promote fairness and strategic behavior. Computational models help simulate voter behavior, election dynamics, and coalition formation, thereby informing political reforms.\\n\\n*   **Public Policy Formulation:**\\n\\n Policymakers can use computational game theory to analyze and predict outcomes of public policies. For instance, models considering strategic interactions among various stakeholders can forecast the impact of environmental regulations or trade policies.\\n\\n### Technology and Cybersecurity\\n\\n*   **Network Security:**\\n\\n In cybersecurity, computational game theory is used to design strategies that minimize risk and optimize defense mechanisms. Simulation models help predict potential attack vectors and counteract them effectively.\\n\\n*   **Resource Allocation in Networks:**\\n\\n Modern communication networks, including the internet, face challenges in resource allocation and traffic management. Game-theoretic algorithms assist in developing protocols that ensure efficient data transfer and minimize congestion.\\n\\n### Future Prospects\\n\\nLooking ahead, the intersection of artificial intelligence and computational game theory promises significant advancements:\\n\\n*   **Automated Negotiation Systems:**\\n\\n AI-driven agents are being developed to negotiate contracts or resolve disputes autonomously. These systems, empowered by game-theoretic algorithms, could revolutionize contract law and business negotiations.\\n\\n*   **Smart Infrastructure:**\\n\\n As cities evolve into smart ecosystems, game theory will play an integral role in optimizing traffic flow, energy distribution, and emergency response systems. Computational models will be key to managing the complexities of urban life.\\n\\n_Reference:_ For more on how computational game theory is being applied in technology and cybersecurity, explore the [IEEE Xplore Digital Library](https://ieeexplore.ieee.org/).\\n\\n* * *\\n\\nConclusion\\n----------\\n\\nThe computational techniques driving modern game theory have fundamentally enhanced our ability to analyze, predict, and optimize strategic interactions. By integrating classical insights with cutting-edge algorithms, researchers and practitioners can now address challenges in economics, politics, and technology with unprecedented precision.\\n\\n### Key Takeaways:\\n\\n*   Modern computational methods, such as dynamic programming, Monte Carlo simulations, and iterative best response dynamics, allow for the analysis of complex game-theoretic scenarios.\\n*   Computational approaches facilitate an enhanced understanding of equilibrium concepts like Nash equilibrium, offering robust strategies for real-world applications.\\n*   Comparative studies underscore the benefits of combining traditional theoretical frameworks with modern, scalable computational methods.\\n*   Real-world applications in areas ranging from auctions to smart infrastructure highlight the practical impact of these techniques.\\n*   The future of game theory analysis looks promising with anticipated advancements in artificial intelligence and automation.\\n\\nAs computational power continues to grow and interdisciplinary research expands, the methods discussed in this article will undoubtedly evolve further, offering even greater insights into strategic decision-making. For those interested in exploring this fascinating field further, consider reading additional works on dynamic programming in game theory and emerging AI applications in strategic analysis.\\n\\n_For further research and insights, check out these resources:_\\n\\n*   [Nash Equilibrium on Wikipedia](https://en.wikipedia.org/wiki/Nash_equilibrium)\\n*   [Dynamic Programming and Game Theory at MIT OpenCourseWare](https://ocw.mit.edu/)\\n*   [Computational Game Theory on the arXiv](https://arxiv.org/)\\n\\nEmbracing the power of computation not only demystifies modern game theory but also paves the way for innovative solutions to some of today’s most challenging strategic problems.\\n\\n* * *\\n\\nBy integrating sophisticated computational techniques with traditional game theory insights, we are better equipped than ever to navigate and shape the complex strategic landscape of the modern world. Whether you’re a researcher, policymaker, or industry leader, understanding these computational tools can provide a competitive advantage in decision-making and strategy formulation.\",\n", "  \"https://www.researchgate.net/publication/347424702_A_Comprehensive_Review_of_Solution_Methods_and_Techniques_for_Solving_Games_in_Game_Theory\": \"![Image 1: Two papers with a wrench in between on a white background with yellow stars and gray shapes scattered around](blob:http://localhost/51502a1e7fab1eb249a27fd400612bd0)\\n\\nWe've noticed some unusual traffic coming from your network. To continue, please check the box below.\\n\\nRay ID\\n\\n:\\n\\n960912d5df0f12c9\\n\\nClient IP\\n\\n:\\n\\n2600:1900:0:2103::1500\\n\\n© 2008-2025 ResearchGate GmbH. All rights reserved.\",\n", "  \"https://www.numberanalytics.com/blog/complete-guide-perfect-info-game-theory\": \"[![Image 1: a video game sitting on top of a table](https://images.unsplash.com/photo-1640955011254-39734e60b16f?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3MDgyMDR8MHwxfHNlYXJjaHwxfHxwZXJmZWN0JTIwaW5mbyUyMGdhbWVzfGVufDB8fHx8MTc0NDk1MDY5NHww&ixlib=rb-4.0.3&q=80&w=1080?utm_source=numberanalytics&utm_medium=referral)](https://images.unsplash.com/photo-1640955011254-39734e60b16f?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3MDgyMDR8MHwxfHNlYXJjaHwxfHxwZXJmZWN0JTIwaW5mbyUyMGdhbWVzfGVufDB8fHx8MTc0NDk1MDY5NHww&ixlib=rb-4.0.3&q=80&w=1080?utm_source=numberanalytics&utm_medium=referral)\\n\\nPhoto by [Pablo Arenas](https://unsplash.com/@pabloarenas?utm_source=numberanalytics&utm_medium=referral) on [Unsplash](https://unsplash.com/?utm_source=numberanalytics&utm_medium=referral)\\n\\nWelcome to our in-depth exploration of the fascinating world of perfect information games. In this comprehensive guide, we unravel the underlying principles of perfect info game theory, discuss essential analytical methodologies, and explore its broad applications in strategic decision-making models. Whether you are a student of game theory or a strategic enthusiast looking to deepen your understanding of these concepts, this article has something to offer.\\n\\nBest video game consoles\\n\\nBelow is a quick overview of what we will cover:\\n\\n*   **Understanding Perfect Info Games**\\n    *   Clear definition and key characteristics\\n    *   Differences between perfect and imperfect information games\\n    *   Historical context and evolution in game theory\\n\\n*   **Analytical Methods in Game Theory**\\n    *   Explaining strategic equilibrium concepts\\n    *   Detailed discussion of backward induction\\n    *   Overview of mathematical and modeling techniques\\n\\n*   **Strategic Applications and Implications**\\n    *   Applications in competitive [board games](https://www.numberanalytics.com/blog/complete-guide-perfect-info-game-theory#) and economics\\n    *   Insights for AI strategy and decision-making models\\n    *   Case studies highlighting practical implementations\\n    \\nBest video game consoles\\n\\n*   **Conclusion: Future of Perfect Info Games**\\n    *   Summary of comprehensive insights\\n    *   Emerging trends and research directions\\n    *   Potential impact on strategic and theoretical advancements\\n\\nLet’s embark on this intellectual journey through the intriguing landscape of perfect information games.\\n\\n* * *\\n\\nIntroduction\\n------------\\n\\nGame theory is the study of mathematical models of strategic interaction among rational decision-makers. Within this discipline, perfect information games occupy a pivotal role. A perfect information game is one where all players know the entire history of moves made by all players up to the current point of decision—there is no hidden or random information. This class of games, which includes classics such as chess, checkers, and go, has been instrumental in advancing our understanding of strategic interactions in various domains such as economics, computer science, and artificial intelligence.\\n\\nBest video game consoles\\n\\nOver the years, perfect info game theory has evolved dramatically. Early research laid the groundwork by defining the nature of these games, while modern approaches employ advanced mathematical and computational tools to analyze strategic behavior. Understanding the dynamics of perfect info games today not only provides insight into theoretical constructs but also informs practical decision-making processes across diverse fields.\\n\\n* * *\\n\\nUnderstanding Perfect Info Games\\n--------------------------------\\n\\nIn this section, we lay the foundation by defining and identifying the key characteristics that distinguish perfect information games from other types of strategic games.\\n\\n### What Are Perfect Information Games?\\n\\nA perfect information game is defined by the following key features:\\n\\nBest video game consoles\\n\\n*   **Complete Knowledge:** Every player is perfectly informed about all actions that have previously occurred.\\n*   **Deterministic Outcomes:** The result of every action is fully determined by the players’ choices without any element of chance.\\n*   **Sequential Moves:** Typically, players take turns making moves, which means each decision is made with full awareness of earlier actions.\\n\\nFor instance, in chess, each player can see the entire board, and every move is visible to both players, fulfilling the perfect information condition.\\n\\n### Differences Between Perfect and Imperfect Information Games\\n\\nIt’s important to note the stark contrast between perfect info games and games with imperfect information (like poker or many strategic board games with hidden cards). Here’s a brief comparison:\\n\\nBest video game consoles\\n\\n| Feature | Perfect Information | Imperfect Information |\\n| --- | --- | --- |\\n| Known History | Fully visible to all players | Some actions hidden or random |\\n| Role of Chance | Minimal or none | Often significant (e.g., cards) |\\n| Decision Making | Based on known, complete information | Requires probabilistic reasoning |\\n| Examples | Chess, Checkers, Go | Poker, Bridge, Monopoly |\\n\\nThese distinctions lead to different tactical and strategic considerations. In perfect info games, strategies often hinge on ensuring that every move is meticulously calculated, relying on detailed backward and forward analysis.\\n\\n### Historical Context and Evolution\\n\\nThe study of perfect information games has roots in early 20th-century mathematical research and the work of game theory pioneers like John von Neumann and Oskar Morgenstern. The concept of _minimax strategy_, formulated by von Neumann, laid the groundwork for understanding how rational players would act when every factor was known. Over the decades, this theory has been refined and expanded to include intricate models, including:\\n\\nBest video game consoles\\n\\n*   **Backward Induction (explained below)**: A method of solving extensive form games by reasoning from the end of the game backwards to the current decision point.\\n*   **Nash Equilibrium for Sequential Games**: Extends equilibrium concepts to incorporate the sequential nature of moves in perfect info games.\\n\\nFor further reading on the historical development of game theory, you can refer to the classic works such as [“Theory of Games and Economic Behavior” by von Neumann and Morgenstern](https://en.wikipedia.org/wiki/Mathematical_theory_of_games).\\n\\n* * *\\n\\nAnalytical Methods in Game Theory\\n---------------------------------\\n\\nThe analysis of perfect info games relies heavily on sophisticated mathematical and strategic tools. This section will outline some of the primary analytical methods used in game theory.\\n\\n### Explaining Strategic Equilibrium Concepts\\n\\nOne central concept in game theory is the _Nash equilibrium_, where no player can benefit by unilaterally changing their strategy if the strategies of the others remain unchanged. In the context of perfect information games, these equilibria are often refined into subgame perfect equilibria—a strategy profile that represents a Nash equilibrium for every subgame of the original game.\\n\\nMathematically, if players’ payoff functions are given by u i(x) u_i(x)  where x x  is the strategy profile, then a Nash equilibrium satisfies: u i(x∗)≥u i(x i,x−i∗)∀i,∀x i u_i(x^_) \\\\geq u\\\\_i(x\\\\_i, x^_ _{-i}) \\\\quad \\\\forall i, \\\\forall x_i  for all players i i . In perfect info scenarios, where sequential rationality is paramount, the concept extends to ensuring optimal play in every possible scenario.\\n\\n### Detailed Discussion of Backward Induction\\n\\nBackward induction is a fundamental technique to solve sequential games. The method involves the following steps:\\n\\n1.   **Final Stage Analysis:** Begin by analyzing the very last move of the game.\\n2.   **Optimal Decision at Final Step:** Determine the best response at the final move.\\n3.   **Iterative Process:** Recursively apply the same reasoning to earlier moves until you reach the beginning of the game.\\n\\nFor example, consider a two-move game where player A makes a decision followed by player B. First, determine how player B will respond to each potential move of player A. Then, knowing player B’s optimal responses, player A can choose the move that maximizes their payoff. Mathematically, this process often employs recursive functions to express the value of each decision node.\\n\\n### Overview of Mathematical and Modeling Techniques\\n\\nTo analyze perfect info games, a variety of mathematical models and computational methods are used. These include:\\n\\n*   **Game Trees:** Visual representations that break down the game into decision points. Each node represents a state of the game, and branches represent possible moves.\\n*   **Dynamic Programming:** Often used in tandem with backward induction, dynamic programming optimizes decisions over multiple time periods.\\n*   **Combinatorial Game Theory:** Focuses on games like chess and go, where the complexity of possible moves can be represented using combinatorial structures.\\n\\nA classic model from combinatorial game theory is the Sprague-Grundy theorem, which assigns a numerical value (the Grundy number) to every position in certain types of games. This theorem is particularly useful for analyzing impartial combinatorial games.\\n\\nFor further reading and more technical details, consider reviewing resources at [MIT OpenCourseWare](https://ocw.mit.edu/) and [Stanford Encyclopedia of Philosophy on Game Theory](https://plato.stanford.edu/entries/game-theory/).\\n\\n* * *\\n\\nStrategic Applications and Implications\\n---------------------------------------\\n\\nThe analytical tools discussed above have far-reaching implications across various areas. Let’s explore some of the primary applications of perfect information game theory.\\n\\n### Applications in Competitive Board Games and Economics\\n\\nMany competitive [board games](https://www.numberanalytics.com/blog/complete-guide-perfect-info-game-theory#) like chess and go are paradigms of perfect information games. The theoretical underpinnings of these games are not only recreational but also have practical applications in economic modeling and decision-making. For instance:\\n\\n*   **Chess Algorithms:** The success of computer chess engines, such as Deep Blue and AlphaZero, has been driven by exhaustive search techniques and effective use of backward induction.\\n*   **Economic Modeling:** In economics, perfect information games are used to model market competition and bargaining scenarios where all actions are observable and agents are fully rational. The concept of subgame perfect equilibrium is especially useful in analyzing sequential market strategies, as detailed in research articles available through the [National Bureau of Economic Research](https://www.nber.org/).\\n\\n### Insights for AI Strategy and Decision-Making Models\\n\\nArtificial Intelligence (AI) has significantly benefited from perfect information game theory. AI systems that master these games leverage deep learning and reinforcement learning to improve decision-making. Key points include:\\n\\n*   **Reinforcement Learning:** Algorithms such as Q-learning use iterative approaches to learn optimal strategies. When combined with game-theoretic models, these systems can plan multiple moves ahead.\\n*   **Monte Carlo Tree Search (MCTS):** This algorithm has been pivotal in advancing the performance of AI in perfect info games like go. MCTS explores the most promising moves by random sampling, then refines its strategy based on empirical outcomes.\\n\\nThese strategies and techniques are not only applicable to gaming but are increasingly relevant in complex problem-solving scenarios like robotics, autonomous vehicles, and even financial trading systems.\\n\\n### Case Studies Highlighting Practical Implementations\\n\\nSeveral high-profile case studies illustrate the practical applications of perfect information game theory:\\n\\n*   **AlphaZero:** Developed by DeepMind, AlphaZero is an AI that mastered chess, shogi, and go purely through self-play and reinforcement learning. Its success highlights remarkable advances in perfect info game theory and AI. Learn more about AlphaZero on [DeepMind’s website](https://deepmind.com/).\\n*   **Economic Market Entry:** Researchers have used perfect information models to analyze how firms decide to enter new markets or adjust strategies in competitive environments. These models help predict outcomes in scenarios where all competitors’ actions are known.\\n\\nThe integration of theoretical frameworks with computational techniques presents a compelling narrative of how classical game theory continues to drive innovation in modern strategic applications.\\n\\n* * *\\n\\nConclusion: Future of Perfect Info Games\\n----------------------------------------\\n\\nAs we wrap up our exploration, it’s clear that perfect information games offer a rich, structured framework for understanding strategic interactions. Here are some key takeaways:\\n\\n*   **Summary of Comprehensive Insights:**\\n\\n    *   Perfect information games are defined by complete transparency of moves and actions.\\n    *   Analytical methods such as backward induction, dynamic programming, and combinatorial game theory provide deep insights into decision-making processes.\\n    *   These theoretical concepts have tangible applications in competitive gaming, economic modeling, and AI strategy.\\n\\n*   **Emerging Trends and Research Directions:**\\n\\n    *   With the rapid advancement of AI and machine learning, integrating these approaches with game theory is opening up new frontiers.\\n    *   Research is focusing on hybrid models that combine elements of perfect and imperfect information to better simulate real-world scenarios.\\n    *   Novel applications in cybersecurity, automated trading systems, and complex logistics planning are being explored.\\n\\n*   **Potential Impact on Strategic and Theoretical Advancements:**\\n\\n    *   Improved understanding of sequential decision processes could lead to more robust algorithms in various domains.\\n    *   The synergy between computational power and theoretical modeling is expected to further the development of transparent and predictable systems in economics and beyond.\\n\\nLooking ahead, the future of perfect information game theory appears promising. As researchers and innovators delve deeper into integrating analytical models with AI, we can expect significant advancements not only in gaming but also in fields demanding intricate strategic planning. For more up-to-date research and discussions, refer to publications from the [Association for Computing Machinery (ACM)](https://www.acm.org/) and [IEEE Xplore Digital Library](https://ieeexplore.ieee.org/).\\n\\n* * *\\n\\nFinal Thoughts\\n--------------\\n\\nPerfect information games offer an ideal window into rational decision-making in a controlled environment—one where every move can be anticipated, calculated, and mastered. As we have seen, the rigorous analysis pioneered by early theorists has evolved into a robust set of tools applicable across diverse modern fields. From enhancing AI strategy in [board games](https://www.numberanalytics.com/blog/complete-guide-perfect-info-game-theory#) to informing market strategies and economic policies, the influence of perfect information game theory is both profound and far-reaching.\\n\\nThe continued development of analytical methods, combined with advances in computational power, promises an exciting era where classical concepts are revitalized through innovative applications. This guide provides just a glimpse into the vast potential of perfect info game theory—a domain where every decision counts, and every move is a step toward a deeper understanding of strategic interaction.\\n\\nThank you for joining us on this journey. We hope this guide has enriched your understanding of perfect info game theory and inspired you to explore further. For ongoing updates and more detailed analyses, stay connected with reputable sources such as [Nature](https://www.nature.com/), [ScienceDirect](https://www.sciencedirect.com/), and other scholarly databases.\\n\\nHappy strategizing!\\n\\n* * *\",\n", "  \"https://pdfs.semanticscholar.org/d044/c42201dde2088038be598d9ede11ebda9dc9.pdf\": \"[PDF] Game-Theoretic Methods for Analysis of Tactical Decision-Making Using Agent-Based Combat Simulations | Semantic Scholar\\n\\n===============\\n\\n[Skip to search form](https://pdfs.semanticscholar.org/d044/c42201dde2088038be598d9ede11ebda9dc9.pdf#search-form)[Skip to main content](https://pdfs.semanticscholar.org/d044/c42201dde2088038be598d9ede11ebda9dc9.pdf#main-content)[Skip to account menu](https://pdfs.semanticscholar.org/d044/c42201dde2088038be598d9ede11ebda9dc9.pdf#account-menu)[](https://pdfs.semanticscholar.org/)\\n\\nSearch 227,741,299 papers from all fields of science \\n\\nSearch\\n\\nSign In Create Free Account\\n\\n*   DOI:[10.5711/MORJ.14.4.21](https://doi.org/10.5711/MORJ.14.4.21)\\n*   Corpus ID: ********\\n\\nGame-Theoretic Methods for Analysis of Tactical Decision-Making Using Agent-Based Combat Simulations\\n====================================================================================================\\n\\n@article{Vorobeychik2009GameTheoreticMF,\\n  title={Game-Theoretic Methods for Analysis of Tactical Decision-Making Using Agent-Based Combat Simulations},\\n  author={Yevgeniy Vorobeychik},\\n  journal={Military Operations Research},\\n  year={2009},\\n  volume={14},\\n  url={https://api.semanticscholar.org/CorpusID:********}\\n}\\n*   [Yevgeniy Vorobeychik](https://pdfs.semanticscholar.org/author/Yevgeniy-Vorobeychik/1699600)\\n*   Published 2009\\n*   Computer Science, Political Science\\n*   Military Operations Research\\n\\nTLDR\\n\\nIt is suggested that game-theoretic simulation analysis could often best be used during the low-resolution, exploratory-analysis phase of a multiresolution analysis.Expand\\n\\n[View via Publisher](https://doi.org/10.5711/MORJ.14.4.21 \\\"https://doi.org/10.5711/MORJ.14.4.21\\\")\\n\\n[vorobeychik.com](http://vorobeychik.com/2009/gtcombat_pub.pdf \\\"http://vorobeychik.com/2009/gtcombat_pub.pdf\\\")\\n\\nSave to Library Save\\n\\nCreate Alert Alert\\n\\nCite\\n\\nShare\\n\\n3 Citations\\n\\n[View All](https://pdfs.semanticscholar.org/d044/c42201dde2088038be598d9ede11ebda9dc9.pdf#citing-papers)\\n\\nFigures and Tables from this paper\\n----------------------------------\\n\\n*   [![Image 1: figure 1](https://figures.semanticscholar.org/d044c42201dde2088038be598d9ede11ebda9dc9/4-Figure1-1.png) figure 1](https://pdfs.semanticscholar.org/paper/Game-Theoretic-Methods-for-Analysis-of-Tactical-Vorobeychik/d044c42201dde2088038be598d9ede11ebda9dc9/figure/0)\\n*   [![Image 2: table 1](https://figures.semanticscholar.org/d044c42201dde2088038be598d9ede11ebda9dc9/5-Table1-1.png) table 1](https://pdfs.semanticscholar.org/paper/Game-Theoretic-Methods-for-Analysis-of-Tactical-Vorobeychik/d044c42201dde2088038be598d9ede11ebda9dc9/figure/1)\\n*   [![Image 3: figure 2](https://figures.semanticscholar.org/d044c42201dde2088038be598d9ede11ebda9dc9/6-Figure2-1.png) figure 2](https://pdfs.semanticscholar.org/paper/Game-Theoretic-Methods-for-Analysis-of-Tactical-Vorobeychik/d044c42201dde2088038be598d9ede11ebda9dc9/figure/2)\\n*   [![Image 4: table 2](https://figures.semanticscholar.org/d044c42201dde2088038be598d9ede11ebda9dc9/16-Table2-1.png) table 2](https://pdfs.semanticscholar.org/paper/Game-Theoretic-Methods-for-Analysis-of-Tactical-Vorobeychik/d044c42201dde2088038be598d9ede11ebda9dc9/figure/3)\\n*   [![Image 5: figure 3](https://figures.semanticscholar.org/d044c42201dde2088038be598d9ede11ebda9dc9/7-Figure3-1.png) figure 3](https://pdfs.semanticscholar.org/paper/Game-Theoretic-Methods-for-Analysis-of-Tactical-Vorobeychik/d044c42201dde2088038be598d9ede11ebda9dc9/figure/4)\\n*   [![Image 6: figure 4](https://figures.semanticscholar.org/d044c42201dde2088038be598d9ede11ebda9dc9/7-Figure4-1.png) figure 4](https://pdfs.semanticscholar.org/paper/Game-Theoretic-Methods-for-Analysis-of-Tactical-Vorobeychik/d044c42201dde2088038be598d9ede11ebda9dc9/figure/5)\\n*   [![Image 7: figure 5](https://figures.semanticscholar.org/d044c42201dde2088038be598d9ede11ebda9dc9/8-Figure5-1.png) figure 5](https://pdfs.semanticscholar.org/paper/Game-Theoretic-Methods-for-Analysis-of-Tactical-Vorobeychik/d044c42201dde2088038be598d9ede11ebda9dc9/figure/6)\\n*   [![Image 8: figure 6](https://figures.semanticscholar.org/d044c42201dde2088038be598d9ede11ebda9dc9/9-Figure6-1.png) figure 6](https://pdfs.semanticscholar.org/paper/Game-Theoretic-Methods-for-Analysis-of-Tactical-Vorobeychik/d044c42201dde2088038be598d9ede11ebda9dc9/figure/7)\\n*   [![Image 9: figure 7](https://figures.semanticscholar.org/d044c42201dde2088038be598d9ede11ebda9dc9/11-Figure7-1.png) figure 7](https://pdfs.semanticscholar.org/paper/Game-Theoretic-Methods-for-Analysis-of-Tactical-Vorobeychik/d044c42201dde2088038be598d9ede11ebda9dc9/figure/8)\\n*   [![Image 10: figure 8](https://figures.semanticscholar.org/d044c42201dde2088038be598d9ede11ebda9dc9/13-Figure8-1.png) figure 8](https://pdfs.semanticscholar.org/paper/Game-Theoretic-Methods-for-Analysis-of-Tactical-Vorobeychik/d044c42201dde2088038be598d9ede11ebda9dc9/figure/9)\\n*   [![Image 11: figure 9](https://figures.semanticscholar.org/d044c42201dde2088038be598d9ede11ebda9dc9/14-Figure9-1.png) figure 9](https://pdfs.semanticscholar.org/paper/Game-Theoretic-Methods-for-Analysis-of-Tactical-Vorobeychik/d044c42201dde2088038be598d9ede11ebda9dc9/figure/10)\\n*   [![Image 12: figure 10](https://figures.semanticscholar.org/d044c42201dde2088038be598d9ede11ebda9dc9/14-Figure10-1.png) figure 10](https://pdfs.semanticscholar.org/paper/Game-Theoretic-Methods-for-Analysis-of-Tactical-Vorobeychik/d044c42201dde2088038be598d9ede11ebda9dc9/figure/11)\\n*   [![Image 13: figure 11](https://figures.semanticscholar.org/d044c42201dde2088038be598d9ede11ebda9dc9/15-Figure11-1.png) figure 11](https://pdfs.semanticscholar.org/paper/Game-Theoretic-Methods-for-Analysis-of-Tactical-Vorobeychik/d044c42201dde2088038be598d9ede11ebda9dc9/figure/12)\\n\\nView All 13 Figures & Tables\\n\\nTopic\\n-----\\n\\nAI-Generated\\n\\n[Multiresolution Analysis (opens in a new tab)](https://topics-beta.apps.semanticscholar.org/topic/59596831971?corpusId=********)\\n\\n3 Citations\\n-----------\\n\\nCitation Type\\n\\nHas PDF\\n\\nAuthor\\n\\nMore Filters\\n\\nMore Filters\\n\\nFilters\\n\\n[### Probabilistic analysis of simulation-based games](https://pdfs.semanticscholar.org/paper/Probabilistic-analysis-of-simulation-based-games-Vorobeychik/a7d09e287a7236a0a92cc225253fc8d20d54c3d8)\\n[Yevgeniy Vorobeychik](https://pdfs.semanticscholar.org/author/Yevgeniy-Vorobeychik/1699600)\\n\\nComputer Science, Mathematics\\n\\nTOMC\\n\\n*   2010\\n\\nTLDR\\n\\nA new maximum-a-posteriori estimator of Nash equilibria based on game-theoretic simulation data is introduced and it is shown that it is consistent and almost surely unique.Expand\\n\\n*   [41](https://pdfs.semanticscholar.org/paper/a7d09e287a7236a0a92cc225253fc8d20d54c3d8#citing-papers)\\n*   [PDF](https://pdfs.semanticscholar.org/paper/a7d09e287a7236a0a92cc225253fc8d20d54c3d8) \\n\\nSave\\n\\n[### Strategic analysis with simulation-based games](https://pdfs.semanticscholar.org/paper/Strategic-analysis-with-simulation-based-games-Vorobeychik-Wellman/b4995433544f46c7f30a0edd04c4eea303280547)\\n[Yevgeniy Vorobeychik](https://pdfs.semanticscholar.org/author/Yevgeniy-Vorobeychik/1699600)[Michael P. Wellman](https://pdfs.semanticscholar.org/author/Michael-P.-Wellman/1796536)\\n\\nComputer Science, Economics\\n\\n[Online World Conference on Soft Computing in…](https://pdfs.semanticscholar.org/venue?name=Online%20World%20Conference%20on%20Soft%20Computing%20in%20Industrial%20Applications)\\n\\n*   2009\\n\\nTLDR\\n\\nThe problem of solving a simulation-based game is introduced, and convergence results and confidence bounds about game-theoretic equilibrium estimates are reviewed, and techniques for approximating equilibria in simulation- based games are presented.Expand\\n\\n*   [7](https://pdfs.semanticscholar.org/paper/b4995433544f46c7f30a0edd04c4eea303280547#citing-papers)\\n*   [PDF](https://pdfs.semanticscholar.org/paper/b4995433544f46c7f30a0edd04c4eea303280547) \\n\\nSave\\n\\n[### The Modelling and Simulation of Integrated Battlefield Cyber-Kinetic Effects](https://pdfs.semanticscholar.org/paper/The-Modelling-and-Simulation-of-Integrated-Effects-Ormrod-Turnbull/8f03a77b0c2f2388f6206e43ecb938789298e56f)\\n[D. Ormrod](https://pdfs.semanticscholar.org/author/D.-Ormrod/122897729)[B. Turnbull](https://pdfs.semanticscholar.org/author/B.-Turnbull/145811892)\\n\\nComputer Science, Political Science\\n\\n[International Journal of Cyber Warfare and…](https://pdfs.semanticscholar.org/venue?name=International%20Journal%20of%20Cyber%20Warfare%20and%20Terrorism)\\n\\n*   2019\\n\\nTLDR\\n\\nThe BICKE framework is designed specifically to combine the cyber and physical domains for the purposes of simulating mission impact, and has a requirement for researchers to measure the synergistic effects across domains.Expand\\n\\n*   [PDF](https://pdfs.semanticscholar.org/paper/8f03a77b0c2f2388f6206e43ecb938789298e56f) \\n\\nSave\\n\\n26 References\\n-------------\\n\\nCitation Type\\n\\nHas PDF\\n\\nAuthor\\n\\nMore Filters\\n\\nMore Filters\\n\\nFilters\\n\\n[### Probabilistic analysis of simulation-based games](https://pdfs.semanticscholar.org/paper/Probabilistic-analysis-of-simulation-based-games-Vorobeychik/a7d09e287a7236a0a92cc225253fc8d20d54c3d8)\\n[Yevgeniy Vorobeychik](https://pdfs.semanticscholar.org/author/Yevgeniy-Vorobeychik/1699600)\\n\\nComputer Science, Mathematics\\n\\nTOMC\\n\\n*   2010\\n\\nTLDR\\n\\nA new maximum-a-posteriori estimator of Nash equilibria based on game-theoretic simulation data is introduced and it is shown that it is consistent and almost surely unique.Expand\\n\\n*   [41](https://pdfs.semanticscholar.org/paper/a7d09e287a7236a0a92cc225253fc8d20d54c3d8#citing-papers)\\n*   [PDF](https://pdfs.semanticscholar.org/paper/a7d09e287a7236a0a92cc225253fc8d20d54c3d8) \\n\\n*   2 Excerpts\\n\\nSave\\n\\n[### Analyzing Complex Strategic Interactions in Multi-Agent Systems](https://pdfs.semanticscholar.org/paper/Analyzing-Complex-Strategic-Interactions-in-Systems-Walsh-Das/43f70c076dbf53023df9f1337ee024f590779f75)\\n[W. E. Walsh](https://pdfs.semanticscholar.org/author/W.-E.-Walsh/3045456)[Rajarshi Das](https://pdfs.semanticscholar.org/author/Rajarshi-Das/143863023)[G. Tesauro](https://pdfs.semanticscholar.org/author/G.-Tesauro/1699108)[J. Kephart](https://pdfs.semanticscholar.org/author/J.-Kephart/1721327)\\n\\nComputer Science, Economics\\n\\n*   2002\\n\\nTLDR\\n\\nA model for analyzing complex games with repeated interactions, for which a full game-theoretic analysis is intractable, is developed and computes a heuristic-payoff table specifying the expected payoffs of the joint heuristic strategy space.Expand\\n\\n*   [188](https://pdfs.semanticscholar.org/paper/43f70c076dbf53023df9f1337ee024f590779f75#citing-papers)\\n*   [PDF](https://pdfs.semanticscholar.org/paper/43f70c076dbf53023df9f1337ee024f590779f75) \\n\\n*   1 Excerpt\\n\\nSave\\n\\n[### Stronger CDA strategies through empirical game-theoretic analysis and reinforcement learning](https://pdfs.semanticscholar.org/paper/Stronger-CDA-strategies-through-empirical-analysis-Schvartzman-Wellman/14e28ae733b59eed57b4e0cd70489839d4088c67)\\n[L. Schvartzman](https://pdfs.semanticscholar.org/author/L.-Schvartzman/2416023)[Michael P. Wellman](https://pdfs.semanticscholar.org/author/Michael-P.-Wellman/1796536)\\n\\nComputer Science, Economics\\n\\n[Adaptive Agents and Multi-Agent Systems](https://pdfs.semanticscholar.org/venue?name=Adaptive%20Agents%20and%20Multi-Agent%20Systems)\\n\\n*   2009\\n\\nTLDR\\n\\nThis work presents a general methodology to automate the search for equilibrium strategies in games derived from computational experimentation that yields strategies stronger than any other published CDA bidding policy, culminating in a new Nash equilibrium supported exclusively by learned strategies.Expand\\n\\n*   [52](https://pdfs.semanticscholar.org/paper/14e28ae733b59eed57b4e0cd70489839d4088c67#citing-papers)\\n*   [PDF](https://pdfs.semanticscholar.org/paper/14e28ae733b59eed57b4e0cd70489839d4088c67) \\n\\n*   1 Excerpt\\n\\nSave\\n\\n[### A Simple Game-Theoretic Approach to Suppression of Enemy Defenses and Other Time Critical Target Analyses](https://pdfs.semanticscholar.org/paper/A-Simple-Game-Theoretic-Approach-to-Suppression-of-Hamilton-Mesic/07f06b1e72ee6ca63669dee0ef19b36acd0c589c)\\n[Thomas Hamilton](https://pdfs.semanticscholar.org/author/Thomas-Hamilton/35828166)[Richard F. Mesic](https://pdfs.semanticscholar.org/author/Richard-F.-Mesic/95447335)\\n\\nPolitical Science\\n\\n*   2004\\n\\nAbstract : The effectiveness of attacks on time critical targets (suppression enemy air defenses, interdiction, and theater ballistic missile missions) often depends on decisions made by the… Expand\\n\\n*   [12](https://pdfs.semanticscholar.org/paper/07f06b1e72ee6ca63669dee0ef19b36acd0c589c#citing-papers)\\n*   [PDF](https://pdfs.semanticscholar.org/paper/07f06b1e72ee6ca63669dee0ef19b36acd0c589c) \\n\\n*   1 Excerpt\\n\\nSave\\n\\n[### Exploring bidding strategies for market-based scheduling](https://pdfs.semanticscholar.org/paper/Exploring-bidding-strategies-for-market-based-Wellman-MacKie-Mason/d4d68a267f05543e63bb285ee99e3ada61e10b35)\\n[Michael P. Wellman](https://pdfs.semanticscholar.org/author/Michael-P.-Wellman/1796536)[Jeffrey K. MacKie-Mason](https://pdfs.semanticscholar.org/author/Jeffrey-K.-MacKie-Mason/1401841735)[Daniel M. Reeves](https://pdfs.semanticscholar.org/author/Daniel-M.-Reeves/34169881)[S. Swaminathan](https://pdfs.semanticscholar.org/author/S.-Swaminathan/2055519383)\\n\\nComputer Science, Economics\\n\\n[ACM Conference on Economics and Computation](https://pdfs.semanticscholar.org/venue?name=ACM%20Conference%20on%20Economics%20and%20Computation)\\n\\n*   2003\\n\\nTLDR\\n\\nInvestigation of the straightforward bidding policy and its variants indicates that the efficacy of particular strategies depends critically on preferences and strategies of other agents, and that the strategy space is far too complex to yield to general game-theoretic analysis.Expand\\n\\n*   [160](https://pdfs.semanticscholar.org/paper/d4d68a267f05543e63bb285ee99e3ada61e10b35#citing-papers)\\n*   [PDF](https://pdfs.semanticscholar.org/paper/d4d68a267f05543e63bb285ee99e3ada61e10b35) \\n\\n*   1 Excerpt\\n\\nSave\\n\\n[### The Future of C 2 Title : Tools for Real-Time Anticipation of Enemy Actions in Tactical Ground Operations Topic : Decisionmaking and Cognitive Analysis](https://pdfs.semanticscholar.org/paper/The-Future-of-C-2-Title-%3A-Tools-for-Real-Time-of-in-Kotta-Ownbyb/086c3f97db5a00e6d7d250e1dc55469065cef46c)\\n[Alexander Kotta](https://pdfs.semanticscholar.org/author/Alexander-Kotta/2285890524)[Michael Ownbyb](https://pdfs.semanticscholar.org/author/Michael-Ownbyb/2285903691)\\n\\nComputer Science, Engineering\\n\\n*   2005\\n\\nTLDR\\n\\nThis paper provides a discussion of the techniques and technologies chosen to perform the adversarial and deception reasoning and details about the experiments and experimentation environment that are used to demonstrate and prove the research goals.Expand\\n\\n*   [19](https://pdfs.semanticscholar.org/paper/086c3f97db5a00e6d7d250e1dc55469065cef46c#citing-papers)\\n\\nSave\\n\\n[### Bidding Strategies for Simultaneous Ascending Auctions](https://pdfs.semanticscholar.org/paper/Bidding-Strategies-for-Simultaneous-Ascending-Wellman-Osepayshvili/9870c341b08a86950dd62da2fd558cdb455468d0)\\n[Michael P. Wellman](https://pdfs.semanticscholar.org/author/Michael-P.-Wellman/1796536)[Anna Osepayshvili](https://pdfs.semanticscholar.org/author/Anna-Osepayshvili/2145237)[Jeffrey K. MacKie-Mason](https://pdfs.semanticscholar.org/author/Jeffrey-K.-MacKie-Mason/1401841735)[Daniel M. Reeves](https://pdfs.semanticscholar.org/author/Daniel-M.-Reeves/34169881)\\n\\nEconomics\\n\\n*   2008\\n\\nSimultaneous ascending auctions present agents with various strategic problems, depending on preference structure. As long as bids represent non-repudiable offers, submitting non-contingent bids to… Expand\\n\\n*   [32](https://pdfs.semanticscholar.org/paper/9870c341b08a86950dd62da2fd558cdb455468d0#citing-papers)\\n*   [PDF](https://pdfs.semanticscholar.org/paper/9870c341b08a86950dd62da2fd558cdb455468d0) \\n\\n*   1 Excerpt\\n\\nSave\\n\\n[### Theory of Games and Economic Behavior](https://pdfs.semanticscholar.org/paper/Theory-of-Games-and-Economic-Behavior-Leonard/a4d99468a565310c7396869b3b8db814d28f3576)\\n[Robert Leonard](https://pdfs.semanticscholar.org/author/Robert-Leonard/2300122018)\\n\\nEconomics\\n\\n*   2006\\n\\nThe nature of the problems investigated and the techniques employed in this book necessitate a procedure which in many instances is thoroughly mathematical. The mathematical devices used are… Expand\\n\\n*   [9,912](https://pdfs.semanticscholar.org/paper/a4d99468a565310c7396869b3b8db814d28f3576#citing-papers)\\n\\n*   1 Excerpt\\n\\nSave\\n\\n[### Models of Bounded Rationality: Empirically Grounded Economic Reason](https://pdfs.semanticscholar.org/paper/Models-of-Bounded-Rationality%3A-Empirically-Grounded-Simon/d51203330073df44b2f3a2047e60934f24f6a4e1)\\n[H. Simon](https://pdfs.semanticscholar.org/author/H.-Simon/2259532335)\\n\\nEconomics, Computer Science\\n\\n*   1982\\n\\nTLDR\\n\\nPart 1 The structure of complex systems: Causal ordering - causality in economic models causal ordering, comparative statics, and near decomposability, simulating large systems - simulation of large-scale systems by aggregation prediction and prescription in systems modelling.Expand\\n\\n*   [2,966](https://pdfs.semanticscholar.org/paper/d51203330073df44b2f3a2047e60934f24f6a4e1#citing-papers)\\n\\nSave\\n\\n[### The Impact of Network Centric Operations on Warfighter Effectiveness: A Study Using Agent-Based Models](https://pdfs.semanticscholar.org/paper/The-Impact-of-Network-Centric-Operations-on-A-Study-Porche-Wilson/c7126c7e39e382d3cc4b3041d2d505d23a8c1a35)\\n[I. Porche](https://pdfs.semanticscholar.org/author/I.-Porche/2699019)[B. Wilson](https://pdfs.semanticscholar.org/author/B.-Wilson/2070624626)[S. Witty](https://pdfs.semanticscholar.org/author/S.-Witty/2936194)\\n\\nComputer Science, Political Science\\n\\n*   2007\\n\\n*   [2](https://pdfs.semanticscholar.org/paper/c7126c7e39e382d3cc4b3041d2d505d23a8c1a35#citing-papers)\\n\\n*   3 Excerpts\\n\\nSave\\n\\n...\\n\\n1\\n\\n2\\n\\n3\\n\\n...\\n\\nRelated Papers\\n--------------\\n\\nShowing 1 through 3 of 0 Related Papers\\n\\n*   [Figures and Tables](https://pdfs.semanticscholar.org/d044/c42201dde2088038be598d9ede11ebda9dc9.pdf#extracted)\\n*   [Topic](https://pdfs.semanticscholar.org/d044/c42201dde2088038be598d9ede11ebda9dc9.pdf#paper-topics)\\n*   [3 Citations](https://pdfs.semanticscholar.org/d044/c42201dde2088038be598d9ede11ebda9dc9.pdf#citing-papers)\\n*   [26 References](https://pdfs.semanticscholar.org/d044/c42201dde2088038be598d9ede11ebda9dc9.pdf#cited-papers)\\n*   [Related Papers](https://pdfs.semanticscholar.org/d044/c42201dde2088038be598d9ede11ebda9dc9.pdf#related-papers)\\n\\nStay Connected With Semantic Scholar\\n\\nSign Up\\n\\nWhat Is Semantic Scholar?\\n-------------------------\\n\\nSemantic Scholar is a free, AI-powered research tool for scientific literature, based at Ai2.\\n\\n[Learn More](https://pdfs.semanticscholar.org/about)\\n\\n### About\\n\\n[About Us](https://pdfs.semanticscholar.org/about)[Publishers](https://pdfs.semanticscholar.org/about/publishers)[Blog (opens in a new tab)](https://medium.com/ai2-blog/semantic-scholar/home)[Ai2 Careers (opens in a new tab)](https://allenai.org/careers?team=semantic+scholar#current-openings)\\n\\n### Product\\n\\n[Product Overview](https://pdfs.semanticscholar.org/product)[Semantic Reader](https://pdfs.semanticscholar.org/product/semantic-reader)[Scholar's Hub](https://pdfs.semanticscholar.org/product/scholars-hub)[Beta Program](https://pdfs.semanticscholar.org/product/beta-program)[Release Notes](https://pdfs.semanticscholar.org/product/release-notes)\\n\\n### API\\n\\n[API Overview](https://pdfs.semanticscholar.org/product/api)[API Tutorials](https://pdfs.semanticscholar.org/product/api%2Ftutorial)[API Documentation (opens in a new tab)](https://api.semanticscholar.org/api-docs/)[API Gallery](https://pdfs.semanticscholar.org/product/api%2Fgallery)\\n\\n### Research\\n\\n[Publications (opens in a new tab)](https://allenai.org/papers?tag=Semantic%20Scholar)[Research Careers (opens in a new tab)](https://allenai.org/careers)[Resources (opens in a new tab)](https://allenai.org/ai-for-science)\\n\\n### Help\\n\\n[FAQ](https://pdfs.semanticscholar.org/faq)[Librarians](https://pdfs.semanticscholar.org/about/librarians)[Tutorials](https://pdfs.semanticscholar.org/product/tutorials)Contact\\n\\nProudly built by [Ai2 (opens in a new tab)](http://allenai.org/)\\n\\nCollaborators & Attributions •[Terms of Service (opens in a new tab)](https://allenai.org/terms)•[Privacy Policy (opens in a new tab)](https://allenai.org/privacy-policy.html)•[API License Agreement](https://pdfs.semanticscholar.org/product/api/license)\\n\\n[The Allen Institute for AI (opens in a new tab)](http://allenai.org/)\\n\\nBy clicking accept or continuing to use the site, you agree to the terms outlined in our[Privacy Policy (opens in a new tab)](https://allenai.org/privacy-policy.html), [Terms of Service (opens in a new tab)](https://allenai.org/terms), and [Dataset License (opens in a new tab)](http://api.semanticscholar.org/corpus/legal)\\n\\nACCEPT & CONTINUE\",\n", "  \"https://lsv.ens-paris-saclay.fr/~bouyer/files/time19.pdf\": \"## Verification and Game Theory \\n\\nP<PERSON><PERSON><PERSON> \\n\\nLSV, CNRS & ENS Paris-Saclay Universit´ e Paris-<PERSON>, <PERSON><PERSON><PERSON>, France \\n\\nThanks to: my co-authors <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON> for recent discussions on the subject <PERSON> for some of the slides Outline  \\n\\n> 1\\n\\nVerification and game theory  \\n\\n> 2\\n\\nWhat is a game?  \\n\\n> 3\\n\\nA glimpse on strategic games  \\n\\n> 4\\n\\nGames on graphs \\n\\nThe general model \\n\\nFocus on a simple scenario \\n\\nAdding probabilities to the setting? \\n\\nConcurrent games  \\n\\n> 5\\n\\nConclusion \\n\\n> 2/69\\n\\n## Computer programming \\n\\nComputer programming is a difficult task \\n\\nunderstand deeply the initial problem; \\n\\nfind a solution; \\n\\nwrite the program correctly. \\n\\nSoftware bugs \\n\\nIt is an error, a failure in a computer program or system that induces an incorrect result. \\n\\nIt may have catastrophic consequences. \\n\\n> 3/69\\n\\n## Software bugs \\n\\nBug example \\n\\nIn August 2005, a Malaysian Airlines MH124 (Boeing 777) that was on autopilot suddenly ascended 2,000 feet. \\n\\nBug consequences \\n\\nloss of confidence from users’ point of view, \\n\\nloss of credibility from institutions’ point of view, \\n\\nlarge financial loss, \\n\\nhuman loss,. . . \\n\\n⇒ Real need to verify the correctness of a program! \\n\\n> 4/69\\n\\n## The model-checking approach to verification \\n\\nReal system \\n\\nplane,... \\n\\nSpecification \\n\\narrive safely,... |=\\n\\n# ?\\n\\n> 5/69\\n\\n## The model-checking approach to verification \\n\\nReal system \\n\\nplane,... \\n\\nSpecification \\n\\narrive safely,... |=\\n\\n# ?\\n\\nAbstract model \\n\\nautomaton,... \\n\\nLogic formula \\n\\nFO, LTL,... |=\\n\\n# ?\\n\\n> 5/69\\n\\n## The model-checking approach to verification \\n\\nReal system \\n\\nplane,... \\n\\nSpecification \\n\\narrive safely,... |=\\n\\n# ?\\n\\nAbstract model \\n\\nautomaton,... \\n\\nLogic formula \\n\\nFO, LTL,... |=\\n\\n# ?\\n\\nAlgorithm \\n\\nYES/NO \\n\\n> 5/69\\n\\n## The autopilot case \\n\\nRequirement: to arrive safely in every weather condition ,\\n\\nwhile minimising the fuel consumption .\\n\\n> 6/69\\n\\n## The autopilot case \\n\\nRequirement: to arrive safely in every weather condition ,\\n\\nwhile minimising the fuel consumption .\\n\\n> 6/69\\n\\n## Controlling computer systems \\n\\nEnvironment weather,... \\n\\nReal system \\n\\nplane,... \\n\\n(Quant.) Spec. \\n\\narrive safely, energy cons.,... \\n\\n> 7/69\\n\\n## Controlling computer systems \\n\\nEnvironment weather,... \\n\\nReal system \\n\\nplane,... \\n\\n(Quant.) Spec. \\n\\narrive safely, energy cons.,... \\n\\nSafe or optimal solution? \\n\\n> 7/69\\n\\n## Controlling computer systems \\n\\nEnvironment weather,... \\n\\nReal system \\n\\nplane,... \\n\\n(Quant.) Spec. \\n\\narrive safely, energy cons.,... \\n\\nSafe or optimal solution? \\n\\nAbstract model \\n\\ngame \\n\\nController \\n\\nstrategy \\n\\nLogic. formula Quant. constraint |=\\n\\n# ?\\n\\n> 7/69\\n\\n## Controlling computer systems \\n\\nEnvironment weather,... \\n\\nReal system \\n\\nplane,... \\n\\n(Quant.) Spec. \\n\\narrive safely, energy cons.,... \\n\\nSafe or optimal solution? \\n\\nAbstract model \\n\\ngame \\n\\nController \\n\\nstrategy \\n\\nLogic. formula Quant. constraint |=\\n\\n# ?\\n\\nAlgorithm \\n\\nNO/YES + A controller \\n\\n> 7/69\\n\\n## The autopilot case \\n\\nRequirement: to arrive safely in every weather condition ,\\n\\ntaking into account the other planes ,\\n\\nwhile minimising the fuel consumption .\\n\\n> 8/69\\n\\n## The autopilot case \\n\\nRequirement: to arrive safely in every weather condition ,\\n\\ntaking into account the other planes ,\\n\\nwhile minimising the fuel consumption .\\n\\n> 8/69\\n\\n## The autopilot case \\n\\nRequirement: to arrive safely in every weather condition ,\\n\\ntaking into account the other planes ,\\n\\nwhile minimising the fuel consumption .\\n\\n> 8/69\\n\\n## Controlling complex interactive computer systems \\n\\nEnvironment weather,... \\n\\nReal systems \\n\\nplanes,... \\n\\nQuant. Spec. \\n\\nenergy cons.,... \\n\\n> 9/69\\n\\n## Controlling complex interactive computer systems \\n\\nEnvironment weather,... \\n\\nReal systems \\n\\nplanes,... \\n\\nQuant. Spec. \\n\\nenergy cons.,... \\n\\nOptimal or stable solution? \\n\\n> 9/69\\n\\n## Controlling complex interactive computer systems \\n\\nEnvironment weather,... \\n\\nReal systems \\n\\nplanes,... \\n\\nQuant. Spec. \\n\\nenergy cons.,... \\n\\nOptimal or stable solution? \\n\\nAbstract model \\n\\ngame \\n\\nPayoff function \\n\\nEquilibrium? \\n\\n> 9/69\\n\\n## Controlling complex interactive computer systems \\n\\nEnvironment weather,... \\n\\nReal systems \\n\\nplanes,... \\n\\nQuant. Spec. \\n\\nenergy cons.,... \\n\\nOptimal or stable solution? \\n\\nAbstract model \\n\\ngame \\n\\nPayoff function \\n\\nEquilibrium? \\n\\nAlgorithm \\n\\nNO/YES + An equilibrium \\n\\n> 9/69\\n\\n## Controlling complex interactive computer systems \\n\\nEnvironment weather,... \\n\\nReal systems \\n\\nplanes,... \\n\\nQuant. Spec. \\n\\nenergy cons.,... \\n\\nOptimal or stable solution? \\n\\nAbstract model \\n\\ngame \\n\\nPayoff function \\n\\nMulti-agent logic \\n\\nEquilibrium? \\n\\nAlgorithm \\n\\nNO/YES + An equilibrium \\n\\n> 9/69\\n\\n## Outline  \\n\\n> 1\\n\\nVerification and game theory  \\n\\n> 2\\n\\nWhat is a game?  \\n\\n> 3\\n\\nA glimpse on strategic games  \\n\\n> 4\\n\\nGames on graphs \\n\\nThe general model \\n\\nFocus on a simple scenario \\n\\nAdding probabilities to the setting? \\n\\nConcurrent games  \\n\\n> 5\\n\\nConclusion \\n\\n> 10/69\\n\\n## Games we play for fun \\n\\n> 11/69\\n\\n## A broader sense: What is game theory?  \\n\\n> [MSZ13] Maschler, Solan, Zamir. Game theory (Cambridge University Press)\\n\\nGoal: Model and analyze (using mathematical tools) situations of interactive decision making \\n\\n> 12/69\\n\\n## A broader sense: What is game theory?  \\n\\n> [MSZ13] Maschler, Solan, Zamir. Game theory (Cambridge University Press)\\n\\nGoal: Model and analyze (using mathematical tools) situations of interactive decision making \\n\\nIngredients \\n\\nSeveral decision makers (called players) \\n\\nAll with different goals \\n\\nThe decision of each players impacts the outcome for all \\n\\n> 12/69\\n\\n## A broader sense: What is game theory?  \\n\\n> [MSZ13] Maschler, Solan, Zamir. Game theory (Cambridge University Press)\\n\\nGoal: Model and analyze (using mathematical tools) situations of interactive decision making \\n\\nIngredients \\n\\nSeveral decision makers (called players) \\n\\nAll with different goals \\n\\nThe decision of each players impacts the outcome for all \\n\\n> 12/69\\n\\n## A broader sense: What is game theory?  \\n\\n> [MSZ13] Maschler, Solan, Zamir. Game theory (Cambridge University Press)\\n\\nGoal: Model and analyze (using mathematical tools) situations of interactive decision making \\n\\nIngredients \\n\\nSeveral decision makers (called players) \\n\\nAll with different goals \\n\\nThe decision of each players impacts the outcome for all \\n\\n> 12/69\\n\\n## A broader sense: What is game theory?  \\n\\n> [MSZ13] Maschler, Solan, Zamir. Game theory (Cambridge University Press)\\n\\nGoal: Model and analyze (using mathematical tools) situations of interactive decision making \\n\\nIngredients \\n\\nSeveral decision makers (called players) \\n\\nAll with different goals \\n\\nThe decision of each players impacts the outcome for all \\n\\nInteractivity! \\n\\n> 12/69\\n\\n## A broader sense: What is game theory?  \\n\\n> [MSZ13] Maschler, Solan, Zamir. Game theory (Cambridge University Press)\\n\\nGoal: Model and analyze (using mathematical tools) situations of interactive decision making \\n\\nIngredients \\n\\nSeveral decision makers (called players) \\n\\nAll with different goals \\n\\nThe decision of each players impacts the outcome for all \\n\\nInteractivity! \\n\\nWide range of applicability \\n\\n“[...] it is a context-free mathematical toolbox” \\n\\n> 12/69\\n\\n## A broader sense: What is game theory?  \\n\\n> [MSZ13] Maschler, Solan, Zamir. Game theory (Cambridge University Press)\\n\\nGoal: Model and analyze (using mathematical tools) situations of interactive decision making \\n\\nIngredients \\n\\nSeveral decision makers (called players) \\n\\nAll with different goals \\n\\nThe decision of each players impacts the outcome for all \\n\\nInteractivity! \\n\\nWide range of applicability \\n\\n“[...] it is a context-free mathematical toolbox” \\n\\nSocial science: e.g. social choice theory \\n\\nTheoretical economics: e.g. models of markets, auctions \\n\\nPolitical science: e.g. fair division \\n\\nBiology: e.g. evolutionary biology \\n\\n... \\n\\n> 12/69\\n\\n## The prisoner dilemma \\n\\nTwo suspects are arrested by the police. The police, having separated both prisoners, visit each of them to offer the same deal. \\n\\nIf one testifies ( Defects ) for the prosecution against the other and the other remains silent ( Cooperates ), the betrayer goes free and the silent accomplice receives the full 10-year sentence. \\n\\nIf both remain silent, both are sentenced to only 3 years in jail. \\n\\nIf each betrays the other, each receives a 5-year sentence. \\n\\n> 13/69\\n\\n## The prisoner dilemma \\n\\nTwo suspects are arrested by the police. The police, having separated both prisoners, visit each of them to offer the same deal. \\n\\nIf one testifies ( Defects ) for the prosecution against the other and the other remains silent ( Cooperates ), the betrayer goes free and the silent accomplice receives the full 10-year sentence. \\n\\nIf both remain silent, both are sentenced to only 3 years in jail. \\n\\nIf each betrays the other, each receives a 5-year sentence. How should the prisoners act? \\n\\n> 13/69\\n\\n## The prisoner dilemma \\n\\nTwo suspects are arrested by the police. The police, having separated both prisoners, visit each of them to offer the same deal. \\n\\nIf one testifies ( Defects ) for the prosecution against the other and the other remains silent ( Cooperates ), the betrayer goes free and the silent accomplice receives the full 10-year sentence. \\n\\nIf both remain silent, both are sentenced to only 3 years in jail. \\n\\nIf each betrays the other, each receives a 5-year sentence. How should the prisoners act? \\n\\nModelled as a matrix game \\n\\nC DC (−3, −3) (−10 , 0) \\n\\nD (0 , −10) (−5, −5) \\n\\n> 13/69\\n\\n## The Nim game \\n\\nThe rules (simplified version) \\n\\nTwo players, turn-based games \\n\\nInitially, there are 8 matches \\n\\nOn each turn, a player must remove 1 or 2 matches \\n\\nThe player removing the last match wins the game \\n\\n> 14/69\\n\\n## The Nim game \\n\\nThe rules (simplified version) \\n\\nTwo players, turn-based games \\n\\nInitially, there are 8 matches \\n\\nOn each turn, a player must remove 1 or 2 matches \\n\\nThe player removing the last match wins the game \\n\\nModelled as a game played on a graph         \\n\\n> 87654321\\n\\n# ,       \\n\\n> 7654321\\n\\n# ,\\n\\n> 14/69\\n\\n## Various models of games \\n\\nMany models of games \\n\\nStrategic games \\n\\nRepeated games \\n\\nGames played on graphs \\n\\nGames played using equations \\n\\n... \\n\\nMany features \\n\\nimperfect information \\n\\npresence of randomness \\n\\ncontinuous time \\n\\n... \\n\\n> 15/69\\n\\nLet us suppose that: \\n\\nwe have fixed a game, \\n\\nwe have identified an adequate model for this game. The next natural question is: \\n\\n## What is a solution for this game? \\n\\n> 16/69\\n\\n## Outline  \\n\\n> 1\\n\\nVerification and game theory  \\n\\n> 2\\n\\nWhat is a game?  \\n\\n> 3\\n\\nA glimpse on strategic games  \\n\\n> 4\\n\\nGames on graphs \\n\\nThe general model \\n\\nFocus on a simple scenario \\n\\nAdding probabilities to the setting? \\n\\nConcurrent games  \\n\\n> 5\\n\\nConclusion \\n\\n> 17/69\\n\\n## Strategic games (aka matrix games, or one-shot games) \\n\\nStrategic game \\n\\nA strategic game G is a triple \\n\\n(\\n\\nAgt , Σ, (gA)A∈Agt \\n\\n)\\n\\nwhere: \\n\\nAgt is the finite and non empty set of players, \\n\\nΣ is a non empty set of actions, \\n\\ngA : Σ Agt → R is the payoff function of player A ∈ Agt. \\n\\n> 18/69\\n\\n## Strategic games (aka matrix games, or one-shot games) \\n\\nStrategic game \\n\\nA strategic game G is a triple \\n\\n(\\n\\nAgt , Σ, (gA)A∈Agt \\n\\n)\\n\\nwhere: \\n\\nAgt is the finite and non empty set of players, \\n\\nΣ is a non empty set of actions, \\n\\ngA : Σ Agt → R is the payoff function of player A ∈ Agt. \\n\\nExample: Prisoner dilemma \\n\\nAgt = {A1, A2},\\n\\nΣ = {C, D}\\n\\n(gA1 , gA2 ) is given by \\n\\nC DC (−3, −3) (−10 , 0) \\n\\nD (0 , −10) (−5, −5) \\n\\n> 18/69\\n\\n## Hypotheses made in classical game theory \\n\\nHypotheses \\n\\nThe players are intelligent (i.e. they reason perfectly and quickly) \\n\\nThe players are rational (i.e. they want to maximise their payoff) \\n\\nThe players are selfish (i.e. they only care for their own payoff) \\n\\n> 19/69\\n\\n## Optimality \\n\\nDominating profile \\n\\nA profile b ∈ ΣAgt is dominating if \\n\\n∀c ∈ ΣAgt ∀A ∈ Agt gA(c) ≤ gA(b)\\n\\n> 20/69\\n\\n## Optimality \\n\\nDominating profile \\n\\nA profile b ∈ ΣAgt is dominating if \\n\\n∀c ∈ ΣAgt ∀A ∈ Agt gA(c) ≤ gA(b)\\n\\nL RT (0 , 0) (2 , 1) \\n\\nB (3 , 2) (1 , 2) \\n\\n(B, L) is optimal! \\n\\n> 20/69\\n\\n## Strict domination \\n\\nStricly dominated action (or strategy) \\n\\nAn action bA ∈ Σ is strictly dominated by cA ∈ Σ for player A ∈ Agt if \\n\\n∀a−A ∈ ΣAgt \\\\{ A} gA(bA, a−A) < gA(cA, a−A)\\n\\n> 21/69\\n\\n## Strict domination \\n\\nStricly dominated action (or strategy) \\n\\nAn action bA ∈ Σ is strictly dominated by cA ∈ Σ for player A ∈ Agt if \\n\\n∀a−A ∈ ΣAgt \\\\{ A} gA(bA, a−A) < gA(cA, a−A)\\n\\nC DC (−3, −3) (−10 , 0) \\n\\nD (0 , −10) (−5, −5) \\n\\n> 21/69\\n\\n## Strict domination \\n\\nStricly dominated action (or strategy) \\n\\nAn action bA ∈ Σ is strictly dominated by cA ∈ Σ for player A ∈ Agt if \\n\\n∀a−A ∈ ΣAgt \\\\{ A} gA(bA, a−A) < gA(cA, a−A)\\n\\nC DC (−3, −3) (−10 , 0) \\n\\nD (0 , −10) (−5, −5) \\n\\nC is strictly dominated by D for player A1;\\n\\nC is strictly dominated by D for player A2.\\n\\n> 21/69\\n\\n## Strict domination \\n\\nStricly dominated action (or strategy) \\n\\nAn action bA ∈ Σ is strictly dominated by cA ∈ Σ for player A ∈ Agt if \\n\\n∀a−A ∈ ΣAgt \\\\{ A} gA(bA, a−A) < gA(cA, a−A)\\n\\nC DC (−3, −3) (−10 , 0) \\n\\nD (0 , −10) (−5, −5) \\n\\nC is strictly dominated by D for player A1;\\n\\nC is strictly dominated by D for player A2.\\n\\nThe only rational issue of the game is ( D, D)whose payoff is ( −5, −5). \\n\\n> 21/69\\n\\n## Strict domination \\n\\nStricly dominated action (or strategy) \\n\\nAn action bA ∈ Σ is strictly dominated by cA ∈ Σ for player A ∈ Agt if \\n\\n∀a−A ∈ ΣAgt \\\\{ A} gA(bA, a−A) < gA(cA, a−A)\\n\\nC DC (−3, −3) (−10 , 0) \\n\\nD (0 , −10) (−5, −5) \\n\\nC is strictly dominated by D for player A1;\\n\\nC is strictly dominated by D for player A2.\\n\\nThe only rational issue of the game is ( D, D)whose payoff is ( −5, −5). \\n\\n(Even though this is sub-optimal) \\n\\n> 21/69\\n\\n## Stability: the concept of Nash equilibria  \\n\\n> [Nash50] Equilibrium Points in n-Person Games (1950).\\n\\nNash equilibrium \\n\\nLet \\n\\n(\\n\\nAgt , Σ, (gA)A∈Agt \\n\\n)\\n\\nbe a strategic game and b ∈ ΣAgt be a strategy profile. We say that b is a Nash equilibrium iff \\n\\n∀A ∈ Agt , ∀dA ∈ Σ s.t. gA(b−A, dA) ≤ gA(b)A rational player should not deviate from the Nash equilibrium. \\n\\n> 22/69\\n\\n## Stability: the concept of Nash equilibria  \\n\\n> [Nash50] Equilibrium Points in n-Person Games (1950).\\n\\nNash equilibrium \\n\\nLet \\n\\n(\\n\\nAgt , Σ, (gA)A∈Agt \\n\\n)\\n\\nbe a strategic game and b ∈ ΣAgt be a strategy profile. We say that b is a Nash equilibrium iff \\n\\n∀A ∈ Agt , ∀dA ∈ Σ s.t. gA(b−A, dA) ≤ gA(b)\\n\\nC DC (−3, −3) (−10 , 0) \\n\\nD (0 , −10) (−5, −5) \\n\\n(D, D) is the unique Nash equilibrium... \\n\\n... even if ( C, C) would be better for both prisoners \\n\\n> 22/69\\n\\n## Stability: the concept of Nash equilibria  \\n\\n> [Nash50] Equilibrium Points in n-Person Games (1950).\\n\\nNash equilibrium \\n\\nLet \\n\\n(\\n\\nAgt , Σ, (gA)A∈Agt \\n\\n)\\n\\nbe a strategic game and b ∈ ΣAgt be a strategy profile. We say that b is a Nash equilibrium iff \\n\\n∀A ∈ Agt , ∀dA ∈ Σ s.t. gA(b−A, dA) ≤ gA(b)\\n\\nC DC (−3, −3) (−10 , 0) \\n\\nD (0 , −10) (−5, −5) \\n\\n(D, D) is the unique Nash equilibrium... \\n\\n... even if ( C, C) would be better for both prisoners \\n\\n> 22/69\\n\\n## Stability: the concept of Nash equilibria  \\n\\n> [Nash50] Equilibrium Points in n-Person Games (1950).\\n\\nNash equilibrium \\n\\nLet \\n\\n(\\n\\nAgt , Σ, (gA)A∈Agt \\n\\n)\\n\\nbe a strategic game and b ∈ ΣAgt be a strategy profile. We say that b is a Nash equilibrium iff \\n\\n∀A ∈ Agt , ∀dA ∈ Σ s.t. gA(b−A, dA) ≤ gA(b)\\n\\nL RT (0 , 0) (2 , 1) \\n\\nB (3 , 2) (1 , 2) \\n\\nR dominates L (but not strictly) \\n\\n(B, R) is not a Nash equilibrium, but ( T, R) is a Nash equilibrium \\n\\n(B, L) is optimal, hence a Nash equilibrium \\n\\n> 22/69\\n\\n## Do all the finite matrix games have a Nash equilibrium? \\n\\n> 23/69\\n\\n## Do all the finite matrix games have a Nash equilibrium? \\n\\nNo! \\n\\n> 23/69\\n\\n## Do all the finite matrix games have a Nash equilibrium? \\n\\nNo! The matching penny game          \\n\\n> aba(1 ,0) (0 ,1)\\n> b(0 ,1) (1 ,0)\\n> 23/69\\n\\n## Mixed strategies \\n\\nGiven E , we denote ∆( E ) the set of probability distributions over E .\\n\\n> 24/69\\n\\n## Mixed strategies \\n\\nMixed strategy \\n\\nIf Σ is the of actions (or strategies), ∆(Σ) is the set of mixed strategies. \\n\\n> 24/69\\n\\n## Mixed strategies \\n\\nMixed strategy \\n\\nIf Σ is the of actions (or strategies), ∆(Σ) is the set of mixed strategies. \\n\\nExpected payoff \\n\\nLet σ = ( σA1 , . . . , σ An ) be a mixed strategy profile. Let A ∈ Agt: \\n\\n˜gA(σ) = ∑\\n\\n> b=( bA)A∈Agt ∈ΣAgt\\n\\n ∏\\n\\n> A∈Agt\\n\\nσA(bA)\\n\\n︸ ︷︷ ︸ \\n\\n> probability of b\\n\\ngA(b)is the expected payoff of player A.\\n\\n> 24/69\\n\\n## Mixed strategies \\n\\nMixed strategy \\n\\nIf Σ is the of actions (or strategies), ∆(Σ) is the set of mixed strategies. \\n\\nExpected payoff \\n\\nLet σ = ( σA1 , . . . , σ An ) be a mixed strategy profile. Let A ∈ Agt: \\n\\n˜gA(σ) = ∑\\n\\n> b=( bA)A∈Agt ∈ΣAgt\\n\\n ∏\\n\\n> A∈Agt\\n\\nσA(bA)\\n\\n︸ ︷︷ ︸ \\n\\n> probability of b\\n\\ngA(b)is the expected payoff of player A.\\n\\nMixed extension of game G\\n\\n‹G def \\n\\n=\\n\\n(\\n\\nAgt , ∆(Σ) , (˜gA)A∈Agt \\n\\n)\\n\\nis a game. \\n\\n> 24/69\\n\\n## Mixed strategies \\n\\nMixed strategy \\n\\nIf Σ is the of actions (or strategies), ∆(Σ) is the set of mixed strategies. \\n\\nExpected payoff \\n\\n· · · \\n\\nMixed extension of game G\\n\\n‹G def \\n\\n=\\n\\n(\\n\\nAgt , ∆(Σ) , (˜gA)A∈Agt \\n\\n)\\n\\nis a game. \\n\\nG has a mixed Nash equilibrium iff ‹G has a Nash equilibrium. \\n\\n> 24/69\\n\\n## Nash equilibria in mixed strategies  \\n\\n> [Nash50] Equilibrium Points in n-Person Games (1950).\\n\\na ba (1 , 0) (0 , 1) \\n\\nb (0 , 1) (1 , 0) The following profile is a Nash equilibrium in mixed strategies: \\n\\nσA1 = 12 · a + 12 · b and σA2 = 12 · a + 12 · b\\n\\nwhose expected payoff is ( 12 , 12 ). \\n\\nNash Theorem [Nash50] \\n\\nAny finite game admits mixed Nash equilibria. \\n\\n> 25/69\\n\\n## Nash equilibria in mixed strategies  \\n\\n> [Nash50] Equilibrium Points in n-Person Games (1950).\\n\\na ba (1 , 0) (0 , 1) \\n\\nb (0 , 1) (1 , 0) The following profile is a Nash equilibrium in mixed strategies: \\n\\nσA1 = 12 · a + 12 · b and σA2 = 12 · a + 12 · b\\n\\nwhose expected payoff is ( 12 , 12 ). \\n\\nNash Theorem [Nash50] \\n\\nAny finite game admits mixed Nash equilibria. \\n\\n> 25/69\\n\\n## Best response \\n\\nBest response \\n\\nLet A ∈ Agt and a−A ∈ ΣAgt \\\\{ A} be a strategy profile for A’s opponents. \\n\\n> 26/69\\n\\n## Best response \\n\\nBest response \\n\\nLet A ∈ Agt and a−A ∈ ΣAgt \\\\{ A} be a strategy profile for A’s opponents. We say that bA ∈ Σ is a best response to a−A if \\n\\n∀cA ∈ Σ gA(cA, a−A) ≤ gA(bA, a−A)\\n\\n> 26/69\\n\\n## Best response \\n\\nBest response \\n\\nLet A ∈ Agt and a−A ∈ ΣAgt \\\\{ A} be a strategy profile for A’s opponents. We say that bA ∈ Σ is a best response to a−A if \\n\\n∀cA ∈ Σ gA(cA, a−A) ≤ gA(bA, a−A)\\n\\nExample: Prisoner dilemma \\n\\nC DC (−3, −3) (−10 , 0) \\n\\nD (0 , −10) (−5, −5) A best response (for Prisoner 1) to C is \\n\\n> 26/69\\n\\n## Best response \\n\\nBest response \\n\\nLet A ∈ Agt and a−A ∈ ΣAgt \\\\{ A} be a strategy profile for A’s opponents. We say that bA ∈ Σ is a best response to a−A if \\n\\n∀cA ∈ Σ gA(cA, a−A) ≤ gA(bA, a−A)\\n\\nExample: Prisoner dilemma \\n\\nC DC (−3, −3) (−10 , 0) \\n\\nD (0 , −10) (−5, −5) A best response (for Prisoner 1) to C is D.\\n\\n> 26/69\\n\\n## Best response \\n\\nBest response \\n\\nLet A ∈ Agt and a−A ∈ ΣAgt \\\\{ A} be a strategy profile for A’s opponents. We say that bA ∈ Σ is a best response to a−A if \\n\\n∀cA ∈ Σ gA(cA, a−A) ≤ gA(bA, a−A)\\n\\nBest response correspondence of Player A\\n\\nBR A : Σ Agt \\\\{ A} → P (Σ) \\n\\na−A → { bA | bA is a best response to a−A}\\n\\nBest response correspondence of the game BR : Σ Agt → P (ΣAgt )\\n\\na → ∏\\n\\n> A∈Agt\\n\\nBR A(a−A)\\n\\n> 26/69\\n\\n## Best response \\n\\nBest response \\n\\nLet A ∈ Agt and a−A ∈ ΣAgt \\\\{ A} be a strategy profile for A’s opponents. We say that bA ∈ Σ is a best response to a−A if \\n\\n∀cA ∈ Σ gA(cA, a−A) ≤ gA(bA, a−A)\\n\\nBest response correspondence of Player A\\n\\nBR A : Σ Agt \\\\{ A} → P (Σ) \\n\\na−A → { bA | bA is a best response to a−A}\\n\\nBest response correspondence of the game BR : Σ Agt → P (ΣAgt )\\n\\na → ∏\\n\\n> A∈Agt\\n\\nBR A(a−A)\\n\\n> 26/69\\n\\n## Best response and Nash equilibrium \\n\\nProposition \\n\\nLet a be a strategy profile. \\n\\na is a Nash equilibrium if and only if a ∈ BR( a)\\n\\n> 27/69\\n\\n## An example \\n\\nL RT (1 , −1) (0 , 0) \\n\\nB (0 , 0) (2 , −2) \\n\\n> 28/69\\n\\n## An example \\n\\nL RT (1 , −1) (0 , 0) \\n\\nB (0 , 0) (2 , −2) A strategy consists in giving a probability distribution over {T, B} (resp. \\n\\n{L, R}), that is, it consists in fixing the probability to play T (resp. L). Assume \\n\\nσA1 = 14 · T + 34 · B and σA2 = 12 · L + 12 · R\\n\\nthe expected payoff is: \\n\\n> 28/69\\n\\n## An example \\n\\nL RT (1 , −1) (0 , 0) \\n\\nB (0 , 0) (2 , −2) A strategy consists in giving a probability distribution over {T, B} (resp. \\n\\n{L, R}), that is, it consists in fixing the probability to play T (resp. L). Assume \\n\\nσA1 = 14 · T + 34 · B and σA2 = 12 · L + 12 · R\\n\\nthe expected payoff is: \\n\\ngA1\\n\\n( 14 , 12\\n\\n)\\n\\n= 78 gA2\\n\\n( 14 , 12\\n\\n)\\n\\n= − 78\\n\\n> 28/69\\n\\n## An example \\n\\nL RT (1 , −1) (0 , 0) \\n\\nB (0 , 0) (2 , −2) In general, we have \\n\\nσA1 = α · T + (1 − α) · B and σA2 = β · L + (1 − β) · R\\n\\nwhose expected payoff is: \\n\\n> 28/69\\n\\n## An example \\n\\nL RT (1 , −1) (0 , 0) \\n\\nB (0 , 0) (2 , −2) In general, we have \\n\\nσA1 = α · T + (1 − α) · B and σA2 = β · L + (1 − β) · R\\n\\nwhose expected payoff is: \\n\\ngA1 (α, β ) = α(3 β − 2) − 2β + 2 = −gA2 (α, β )\\n\\n> 28/69\\n\\n## An example \\n\\nL RT (1 , −1) (0 , 0) \\n\\nB (0 , 0) (2 , −2) \\n\\ngA1 (α, β ) = α(3 β − 2) − 2β + 2 BR A1 (β) = \\n\\n\\n\\n{1} if 3 β − 2 > 0[0 , 1] if 3 β − 2 = 0 \\n\\n{0} if 3 β − 2 < 0   \\n\\n> α01α01\\n> 28/69\\n\\n## An example \\n\\nL RT (1 , −1) (0 , 0) \\n\\nB (0 , 0) (2 , −2) \\n\\ngA1 (α, β ) = α(3 β − 2) − 2β + 2 BR A1 (β) = \\n\\n\\n\\n{1} if 3 β − 2 > 0[0 , 1] if 3 β − 2 = 0 \\n\\n{0} if 3 β − 2 < 0   \\n\\n> α01α01\\n> 28/69\\n\\n## An example \\n\\nL RT (1 , −1) (0 , 0) \\n\\nB (0 , 0) (2 , −2) \\n\\ngA1 (α, β ) = α(3 β − 2) − 2β + 2 BR A1 (β) = \\n\\n\\n\\n{1} if 3 β − 2 > 0[0 , 1] if 3 β − 2 = 0 \\n\\n{0} if 3 β − 2 < 0     \\n\\n> α01α01α01\\n> 28/69\\n\\n## An example \\n\\nL RT (1 , −1) (0 , 0) \\n\\nB (0 , 0) (2 , −2) \\n\\ngA1 (α, β ) = α(3 β − 2) − 2β + 2 BR A1 (β) = \\n\\n\\n\\n{1} if 3 β − 2 > 0[0 , 1] if 3 β − 2 = 0 \\n\\n{0} if 3 β − 2 < 0     \\n\\n> α01α01α01\\n> 28/69\\n\\n## An example \\n\\nL RT (1 , −1) (0 , 0) \\n\\nB (0 , 0) (2 , −2) BR A1 (β) = \\n\\n\\n\\n{1} if 3 β − 2 > 0[0 , 1] if 3 β − 2 = 0 \\n\\n{0} if 3 β − 2 < 0BR A2 (α) = \\n\\n\\n\\n{1} if 3 α − 2 < 0[0 , 1] if 3 α − 2 = 0 \\n\\n{0} if 3 α − 2 > 0 \\n\\n> α\\n> β\\n> 0123\\n> 23\\n> 28/69\\n\\n## An example \\n\\nL RT (1 , −1) (0 , 0) \\n\\nB (0 , 0) (2 , −2) Thus the following profile is an equilibrium in mixed strategies: \\n\\nσA1 = 23 · T + 13 · B and σA2 = 23 · L + 13 · R\\n\\nwhose expected payoff is: \\n\\n( 23 , − 23\\n\\n)\\n\\n> 28/69\\n\\n## Best response and Nash equilibrium \\n\\nProposition \\n\\nLet a be a strategy profile. \\n\\na is a Nash equilibrium if and only if a ∈ BR( a)\\n\\n> 29/69\\n\\n## Best response and Nash equilibrium \\n\\nProposition \\n\\nLet a be a strategy profile. \\n\\na is a Nash equilibrium if and only if a ∈ BR( a)\\n\\nNash Theorem [Nash50] \\n\\nAny finite game admits mixed Nash equilibria. \\n\\n> 29/69\\n\\n## Best response and Nash equilibrium \\n\\nProposition \\n\\nLet a be a strategy profile. \\n\\na is a Nash equilibrium if and only if a ∈ BR( a)\\n\\nNash Theorem [Nash50] \\n\\nAny finite game admits mixed Nash equilibria. Key ingredient of the proof: Brouwer’s fixpoint theorem Or simply Kakutani’s fixpoint theorem \\n\\n> 29/69\\n\\n## Fixpoint theorems \\n\\nBrouwer’s fixpoint theorem \\n\\nLet X ⊆ Rn be a convex, compact and nonempty set. Then every continuous function f : X → X has a fixpoint. \\n\\nKakutani’s fixpoint theorem \\n\\nLet X be a non-empty, compact and convex subset of Rn. Let f : X → 2X\\n\\nbe a set-valued function on X with a closed graph and the property that \\n\\nf (x) is non-empty and convex for all x ∈ X . Then f has a fixpoint. \\n\\n> 30/69\\n\\n## Outline  \\n\\n> 1\\n\\nVerification and game theory  \\n\\n> 2\\n\\nWhat is a game?  \\n\\n> 3\\n\\nA glimpse on strategic games  \\n\\n> 4\\n\\nGames on graphs \\n\\nThe general model \\n\\nFocus on a simple scenario \\n\\nAdding probabilities to the setting? \\n\\nConcurrent games  \\n\\n> 5\\n\\nConclusion \\n\\n> 31/69\\n\\n## Which games do we need for verification? \\n\\nMethodology \\n\\nPick standard models used in model-checking \\n\\nExpand them with interaction capabilities \\n\\n; Games played on graphs \\n\\nSeveral features in the graph: stochastic or deterministic \\n\\nSeveral options for interaction: turn-based vs concurrent, pure vs \\n\\nmixed strategies \\n\\n> 32/69\\n\\n## Which games do we need for verification? \\n\\nMethodology \\n\\nPick standard models used in model-checking \\n\\nExpand them with interaction capabilities \\n\\n; Games played on graphs \\n\\nSeveral features in the graph: stochastic or deterministic \\n\\nSeveral options for interaction: turn-based vs concurrent, pure vs \\n\\nmixed strategies \\n\\nThe Nim game modelled as a turn-based game         \\n\\n> 87654321\\n\\n# ,       \\n\\n> 7654321\\n\\n# ,\\n\\n> 32/69\\n\\n## Which games do we need for verification? \\n\\nMethodology \\n\\nPick standard models used in model-checking \\n\\nExpand them with interaction capabilities \\n\\n; Games played on graphs \\n\\nSeveral features in the graph: stochastic or deterministic \\n\\nSeveral options for interaction: turn-based vs concurrent, pure vs \\n\\nmixed strategies \\n\\nThe Nim game modelled as a turn-based game        \\n\\n> 87542163\\n\\n# ,      \\n\\n> 6375421\\n\\n# ,\\n\\nThis is then just a matter of computing winning states (controller synthesis) \\n\\n> 32/69\\n\\n## Outline  \\n\\n> 1\\n\\nVerification and game theory  \\n\\n> 2\\n\\nWhat is a game?  \\n\\n> 3\\n\\nA glimpse on strategic games  \\n\\n> 4\\n\\nGames on graphs \\n\\nThe general model \\n\\nFocus on a simple scenario \\n\\nAdding probabilities to the setting? \\n\\nConcurrent games  \\n\\n> 5\\n\\nConclusion \\n\\n> 33/69\\n\\n## Multiplayer stochastic concurrent games  \\n\\n> [KNPS19] Kwiatkowska, Norman, Parker, Santos. Equilibria-based probabilistic model checking for concurrent stochastic games (FM’19) .\\n\\nGraph with stochastic nodes \\n\\nMultiple players: Agt = {A1, A2, A3, . . . }\\n\\nConcurrent moves: a1a2a3 · · · ∈ ΣAgt means that player A1 played \\n\\na1, player A2 played a2 and player A3 played a3, ... \\n\\nPayoff functions payoff A : V ω → R for every A ∈ Agt \\n\\nA simple model for the medium access control problem [KNPS19] \\n\\nv0\\n\\nv1\\n\\nv2\\n\\nv3\\n\\nv5\\n\\nv6\\n\\nw1w2\\n\\nw1w2\\n\\nw1w2\\n\\nw1w2\\n\\nw1w2\\n\\nw1w2\\n\\nt1w2\\n\\nw1t2\\n\\nw1t2\\n\\nt1w2\\n\\nt1t2\\n\\n34\\n\\n14\\n\\n> 34/69\\n\\n## How do we play those games? \\n\\nAccording to strategies! \\n\\n> 35/69\\n\\n## How do we play those games? \\n\\nAccording to strategies! \\n\\nWhat kind of strategies? \\n\\n> 35/69\\n\\n## How do we play those games? \\n\\nAccording to strategies! \\n\\nWhat kind of strategies? \\n\\nMixed strategies \\n\\nσA : V ∗ → Dist(Σ) \\n\\nAfter history h ∈ V ∗, player A will play each action a ∈ Σ with probability σA(h). \\n\\n> 35/69\\n\\n## How do we play those games? \\n\\nAccording to strategies! \\n\\nWhat kind of strategies? \\n\\nMixed strategies \\n\\nσA : V ∗ → Dist(Σ) \\n\\nDeterministic strategies \\n\\nσA : V ∗ → Σ\\n\\nFor every h ∈ V ∗, σA(h) is a Dirac measure. \\n\\n> 35/69\\n\\n## How do we play those games? \\n\\nAccording to strategies! \\n\\nWhat kind of strategies? \\n\\nMixed strategies \\n\\nσA : V ∗ → Dist(Σ) \\n\\nDeterministic strategies \\n\\nσA : V ∗ → Σ\\n\\nStationary strategies \\n\\nσA : V → Dist(Σ) \\n\\nIf h, h′ ∈ V ∗ are s.t. last (h) = last (h′), then σA(h) = σA(h′). \\n\\n> 35/69\\n\\n## How do we play those games? \\n\\nAccording to strategies! \\n\\nWhat kind of strategies? \\n\\nMixed strategies \\n\\nσA : V ∗ → Dist(Σ) \\n\\nDeterministic strategies \\n\\nσA : V ∗ → Σ\\n\\nStationary strategies \\n\\nσA : V → Dist(Σ) \\n\\nMemoryless strategies \\n\\nσA : V → Σ\\n\\n> 35/69\\n\\n## How do we play those games? \\n\\nAccording to strategies! \\n\\nWhat kind of strategies? \\n\\nMixed strategies \\n\\nσA : V ∗ → Dist(Σ) \\n\\nDeterministic strategies \\n\\nσA : V ∗ → Σ\\n\\nStationary strategies \\n\\nσA : V → Dist(Σ) \\n\\nMemoryless strategies \\n\\nσA : V → Σ\\n\\nStrategy profile σ = ( σA)A∈Agt \\n\\n> 35/69\\n\\n## An example \\n\\nv0\\n\\nv1\\n\\nv2\\n\\nv3\\n\\nv5\\n\\nv6\\n\\nw1w2\\n\\nw1w2\\n\\nw1w2\\n\\nw1w2\\n\\nw1w2\\n\\nw1w2\\n\\nt1w2\\n\\nw1t2\\n\\nw1t2\\n\\nt1w2\\n\\nt1t2\\n\\n34\\n\\n14\\n\\nStrategy for player Ai : σAi (h) = 13 ti + 23 wi if ti available; σi (h) = wi\\n\\notherwise. \\n\\nv0\\n\\nv0 v1 v2\\n\\n19\\n\\n29\\n\\n29\\n\\n49\\n\\nv5 v6\\n\\n34\\n\\n14 ... v1 v3\\n\\n23\\n\\n13\\n\\nv2 v3\\n\\n23\\n\\n13\\n\\n... ...\\n\\n> 36/69\\n\\n## Payoffs \\n\\nGiven strategy profile σ = ( σA)A∈Agt , the benefit p(A) of player A from \\n\\nv0 is given by: \\n\\npA(σ) = Eσ \\n\\n> v0\\n\\n(payoff A)\\n\\n> 37/69\\n\\n## Payoffs \\n\\nGiven strategy profile σ = ( σA)A∈Agt , the benefit p(A) of player A from \\n\\nv0 is given by: \\n\\npA(σ) = Eσ \\n\\n> v0\\n\\n(payoff A)\\n\\nExamples \\n\\nφA ⊆ V ω , and for ρ ∈ V ω ,payoff A(ρ) = \\n\\n{ 1 if ρ |= φA\\n\\n0 otherwise Then, pA(σ) = Pσ \\n\\n> v0\\n\\n(φA). \\n\\npayoff A is a quantitative function on V ω , for instance: \\n\\na mean-payoff function \\n\\na terminal-reward function \\n\\n> 37/69\\n\\n## Payoffs \\n\\nGiven strategy profile σ = ( σA)A∈Agt , the benefit p(A) of player A from \\n\\nv0 is given by: \\n\\npA(σ) = Eσ \\n\\n> v0\\n\\n(payoff A)\\n\\nExamples \\n\\nφA ⊆ V ω , and for ρ ∈ V ω ,payoff A(ρ) = \\n\\n{ 1 if ρ |= φA\\n\\n0 otherwise Then, pA(σ) = Pσ \\n\\n> v0\\n\\n(φA). \\n\\npayoff A is a quantitative function on V ω , for instance: \\n\\na mean-payoff function \\n\\na terminal-reward function \\n\\n> 37/69\\n\\n## Subclasses of interest \\n\\nTurn-based games: V partitioned into all VAi ’s \\n\\nDeterministic games \\n\\n> 38/69\\n\\n## Subclasses of interest \\n\\nTurn-based games: V partitioned into all VAi ’s \\n\\nv0\\n\\nv1\\n\\nA1\\n\\nv3\\n\\nA3\\n\\nv2\\n\\nA2 \\n\\n> 13\\n\\n, 0, 10, 1, 13\\n\\n1, 13 , 0\\n\\n> 13\\n> 13\\n> 13\\n\\nl\\n\\nl\\n\\nl\\n\\nc\\n\\nc\\n\\nc\\n\\nDeterministic games \\n\\n> 38/69\\n\\n## Subclasses of interest \\n\\nTurn-based games: V partitioned into all VAi ’s \\n\\nv0\\n\\nv1\\n\\nA1\\n\\nv3\\n\\nA3\\n\\nv2\\n\\nA2 \\n\\n> 13\\n\\n, 0, 10, 1, 13\\n\\n1, 13 , 0\\n\\n> 13\\n> 13\\n> 13\\n\\nl\\n\\nl\\n\\nl\\n\\nc\\n\\nc\\n\\nc\\n\\nDeterministic games \\n\\n> 38/69\\n\\n## Subclasses of interest \\n\\nTurn-based games: V partitioned into all VAi ’s \\n\\nv0\\n\\nv1\\n\\nA1\\n\\nv3\\n\\nA3\\n\\nv2\\n\\nA2 \\n\\n> 13\\n\\n, 0, 10, 1, 13\\n\\n1, 13 , 0\\n\\n> 13\\n> 13\\n> 13\\n\\nl\\n\\nl\\n\\nl\\n\\nc\\n\\nc\\n\\nc\\n\\nDeterministic games \\n\\nIf σ is pure and the game is deterministic, then profile σ has a single outcome out( σ), and \\n\\npA(σ) = payoff A(out( σ)) \\n\\n> 38/69\\n\\n## Nash equilibrium in this setting \\n\\nNash equilibrium \\n\\nA mixed (resp. pure) strategy profile σ = ( σA)A∈Agt is a mixed (resp. pure) Nash equilibrium if no player can improve her payoff by unilaterally changing her strategy, that is, for every A ∈ Agt, for every mixed (resp. pure) deviation σ′\\n\\n> A\\n\\n,\\n\\nEσ \\n\\n> v0\\n\\n(payoff A) ≥ Eσ[A/σ ′ \\n\\n> A]\\n> v0\\n\\n(payoff A)\\n\\n> 39/69\\n\\n## Nash equilibrium in this setting \\n\\nNash equilibrium \\n\\nA mixed (resp. pure) strategy profile σ = ( σA)A∈Agt is a mixed (resp. pure) Nash equilibrium if no player can improve her payoff by unilaterally changing her strategy, that is, for every A ∈ Agt, for every mixed (resp. pure) deviation σ′\\n\\n> A\\n\\n,\\n\\nEσ \\n\\n> v0\\n\\n(payoff A) ≥ Eσ[A/σ ′ \\n\\n> A]\\n> v0\\n\\n(payoff A)\\n\\nExample \\n\\nv0 0, 0\\n\\n0, 1 1, 0\\n\\n> aa , bb\\n> ab\\n\\nba \\n\\naa (that is, σAi (v0) = a) is a (pure) Nash equilibrium \\n\\n> 39/69\\n\\n## Nash equilibrium in this setting \\n\\nNash equilibrium \\n\\nA mixed (resp. pure) strategy profile σ = ( σA)A∈Agt is a mixed (resp. pure) Nash equilibrium if no player can improve her payoff by unilaterally changing her strategy, that is, for every A ∈ Agt, for every mixed (resp. pure) deviation σ′\\n\\n> A\\n\\n,\\n\\nEσ \\n\\n> v0\\n\\n(payoff A) ≥ Eσ[A/σ ′ \\n\\n> A]\\n> v0\\n\\n(payoff A)\\n\\nExample – Matching penny \\n\\nv0\\n\\n0, 1 1, 0\\n\\n> aa , bb\\n> ab , ba\\n\\nσAi (v0) = 12 · a + 12 · b is the unique (mixed) Nash equilibrium \\n\\n> 39/69\\n\\nv0\\n\\nv1\\n\\nA1\\n\\nv3\\n\\nA3\\n\\nv2\\n\\nA2 \\n\\n> 13\\n\\n, 0, 1\\n\\n0, 1, 13\\n\\n1, 13 , 0\\n\\n> 13\\n> 13\\n> 13\\n\\nl\\n\\nl\\n\\nl\\n\\nc\\n\\nc\\n\\nc\\n\\nThere is no stationary Nash equilibrium \\n\\nThere is a pure Nash equilibrium: \\n\\nv0vi 7 → c\\n\\nv0vi+1 7 → l\\n\\nv0vi h 7 → c\\n\\nIt has payoff ( 49 , 49 , 49 ). \\n\\n> 40/69\\n\\nv0\\n\\nv1\\n\\nA1\\n\\nv3\\n\\nA3\\n\\nv2\\n\\nA2 \\n\\n> 13\\n\\n, 0, 1\\n\\n0, 1, 13\\n\\n1, 13 , 0\\n\\n> 13\\n> 13\\n> 13\\n\\nl\\n\\nl\\n\\nl\\n\\nc\\n\\nc\\n\\nc\\n\\nThere is no stationary Nash equilibrium \\n\\nThere is a pure Nash equilibrium: \\n\\nv0vi 7 → c\\n\\nv0vi+1 7 → l\\n\\nv0vi h 7 → c\\n\\nIt has payoff ( 49 , 49 , 49 ). \\n\\n> 40/69\\n\\nv0\\n\\nv1\\n\\nA1\\n\\nv3\\n\\nA3\\n\\nv2\\n\\nA2 \\n\\n> 13\\n\\n, 0, 1\\n\\n0, 1, 13\\n\\n1, 13 , 0\\n\\n> 13\\n> 13\\n> 13\\n\\nl\\n\\nl\\n\\nl\\n\\nc\\n\\nc\\n\\nc\\n\\nThere is no stationary Nash equilibrium \\n\\nThere is a pure Nash equilibrium: \\n\\nv0vi 7 → c\\n\\nv0vi+1 7 → l\\n\\nv0vi h 7 → c\\n\\nIt has payoff ( 49 , 49 , 49 ). \\n\\n> 40/69\\n\\n## Problems of interest \\n\\nUniversal existence: \\n\\nDoes there always exist a Nash equilibrium? \\n\\nExistence problem: \\n\\nDoes there exist a Nash equilibrium? \\n\\nConstrained existence problem: \\n\\nDoes there exist a Nash equilibrium which satisfies some given constraint? \\n\\n> 41/69\\n\\n## Problems of interest \\n\\nUniversal existence: \\n\\nDoes there always exist a Nash equilibrium? \\n\\nExistence problem: \\n\\nDoes there exist a Nash equilibrium? \\n\\nConstrained existence problem: \\n\\nDoes there exist a Nash equilibrium which satisfies some given constraint? \\n\\n> 41/69\\n\\n## Problems of interest \\n\\nUniversal existence: \\n\\nDoes there always exist a Nash equilibrium? \\n\\nExistence problem: \\n\\nDoes there exist a Nash equilibrium? \\n\\nConstrained existence problem: \\n\\nDoes there exist a Nash equilibrium which satisfies some given constraint? \\n\\n> 41/69\\n\\n## Problems of interest \\n\\nUniversal existence: \\n\\nDoes there always exist a Nash equilibrium? \\n\\nExistence problem: \\n\\nDoes there exist a Nash equilibrium? \\n\\nConstrained existence problem: \\n\\nDoes there exist a Nash equilibrium which satisfies some given constraint? \\n\\nSynthesis of witness (simple?) profiles? \\n\\nDo strategy profiles require randomness? Memory? \\n\\n> 41/69\\n\\n## Problems of interest \\n\\nUniversal existence: \\n\\nDoes there always exist a Nash equilibrium? \\n\\nExistence problem: \\n\\nDoes there exist a Nash equilibrium? \\n\\nConstrained existence problem: \\n\\nDoes there exist a Nash equilibrium which satisfies some given constraint? \\n\\nSynthesis of witness (simple?) profiles? \\n\\nDo strategy profiles require randomness? Memory? \\n\\n> 41/69\\n\\n## Does the standard theory apply? \\n\\nNash theorem does not apply (requires a finite number of pure strategies) \\n\\nBut do the related fixed point theorems apply? \\n\\nUsually it applies to the best-response operator: if σ ∈ S (S is for stationary profiles), then BR( σ) = \\n\\n{\\n\\nσ′ ∈ S | ∀ A ∈ Agt , σ ′ \\n\\n> A\\n\\n∈ argmax σ′′   \\n\\n> A∈SA\\n\\nEσ[A/σ ′′   \\n\\n> A]\\n> v0\\n\\n(payoff A)\\n\\n}\\n\\n> 42/69\\n\\n## Does the standard theory apply? \\n\\nNash theorem does not apply (requires a finite number of pure strategies) \\n\\nBut do the related fixed point theorems apply? \\n\\nUsually it applies to the best-response operator: if σ ∈ S (S is for stationary profiles), then BR( σ) = \\n\\n{\\n\\nσ′ ∈ S | ∀ A ∈ Agt , σ ′ \\n\\n> A\\n\\n∈ argmax σ′′   \\n\\n> A∈SA\\n\\nEσ[A/σ ′′   \\n\\n> A]\\n> v0\\n\\n(payoff A)\\n\\n}\\n\\n> 42/69\\n\\n## Does the standard theory apply? \\n\\nNash theorem does not apply (requires a finite number of pure strategies) \\n\\nBut do the related fixed point theorems apply? \\n\\nKakutani’s fixpoint theorem \\n\\nLet X be a non-empty, compact and convex subset of Rn. Let f : X → 2X\\n\\nbe a set-valued function on X with a closed graph and the property that \\n\\nf (x) is non-empty and convex for all x ∈ X . Then f has a fixpoint. \\n\\nUsually it applies to the best-response operator: if σ ∈ S (S is for stationary profiles), then BR( σ) = \\n\\n{\\n\\nσ′ ∈ S | ∀ A ∈ Agt , σ ′ \\n\\n> A\\n\\n∈ argmax σ′′   \\n\\n> A∈SA\\n\\nEσ[A/σ ′′   \\n\\n> A]\\n> v0\\n\\n(payoff A)\\n\\n}\\n\\n> 42/69\\n\\n## Does the standard theory apply? \\n\\nNash theorem does not apply (requires a finite number of pure strategies) \\n\\nBut do the related fixed point theorems apply? \\n\\nKakutani’s fixpoint theorem \\n\\nLet X be a non-empty, compact and convex subset of Rn. Let f : X → 2X\\n\\nbe a set-valued function on X with a closed graph and the property that \\n\\nf (x) is non-empty and convex for all x ∈ X . Then f has a fixpoint. \\n\\nUsually it applies to the best-response operator: if σ ∈ S (S is for stationary profiles), then BR( σ) = \\n\\n{\\n\\nσ′ ∈ S | ∀ A ∈ Agt , σ ′ \\n\\n> A\\n\\n∈ argmax σ′′   \\n\\n> A∈SA\\n\\nEσ[A/σ ′′   \\n\\n> A]\\n> v0\\n\\n(payoff A)\\n\\n}\\n\\n> 42/69\\n\\n## Does the standard theory apply? \\n\\nv1\\n\\nA1\\n\\nv2\\n\\nA2 \\n\\n> 13\\n\\n, 1 1, 13\\n\\nl l\\n\\nc\\n\\nc\\n\\nFor every x1, x2 > 0, BR(( x1, x2)) = (0 , 0) \\n\\nBR((0 , 0)) = {(x1, x2) | x1, x2 > 0}\\n\\nThe graph of BR is not closed \\n\\nKakutani’s theorem does not apply \\n\\n> 42/69\\n\\n## Does the standard theory apply? \\n\\nv1\\n\\nA1\\n\\nv2\\n\\nA2 \\n\\n> 13\\n\\n, 1 1, 13\\n\\nl l\\n\\nc\\n\\nc\\n\\nThe first who leaves the loop loses! \\n\\nFor every x1, x2 > 0, BR(( x1, x2)) = (0 , 0) \\n\\nBR((0 , 0)) = {(x1, x2) | x1, x2 > 0}\\n\\nThe graph of BR is not closed \\n\\nKakutani’s theorem does not apply \\n\\n> 42/69\\n\\n## Does the standard theory apply? \\n\\nv1\\n\\nA1\\n\\nv2\\n\\nA2 \\n\\n> 13\\n\\n, 1 1, 13\\n\\nl l\\n\\nc\\n\\nc\\n\\nWe note ( x1, x2) ∈ [0 , 1] 2 for the profile σ s.t. \\n\\n{σA1 (v1) = x1 · l + (1 − x1) · c\\n\\nσA2 (v2) = x2 · l + (1 − x2) · c\\n\\nThe first who leaves the loop loses! \\n\\nFor every x1, x2 > 0, BR(( x1, x2)) = (0 , 0) \\n\\nBR((0 , 0)) = {(x1, x2) | x1, x2 > 0}\\n\\nThe graph of BR is not closed \\n\\nKakutani’s theorem does not apply \\n\\n> 42/69\\n\\n## Does the standard theory apply? \\n\\nv1\\n\\nA1\\n\\nv2\\n\\nA2 \\n\\n> 13\\n\\n, 1 1, 13\\n\\nl l\\n\\nc\\n\\nc\\n\\nWe note ( x1, x2) ∈ [0 , 1] 2 for the profile σ s.t. \\n\\n{σA1 (v1) = x1 · l + (1 − x1) · c\\n\\nσA2 (v2) = x2 · l + (1 − x2) · c\\n\\nThe first who leaves the loop loses! \\n\\nFor every x1, x2 > 0, BR(( x1, x2)) = (0 , 0) \\n\\nBR((0 , 0)) = {(x1, x2) | x1, x2 > 0}\\n\\nThe graph of BR is not closed \\n\\nKakutani’s theorem does not apply \\n\\n> 42/69\\n\\n## Does the standard theory apply? \\n\\nv1\\n\\nA1\\n\\nv2\\n\\nA2 \\n\\n> 13\\n\\n, 1 1, 13\\n\\nl l\\n\\nc\\n\\nc\\n\\nWe note ( x1, x2) ∈ [0 , 1] 2 for the profile σ s.t. \\n\\n{σA1 (v1) = x1 · l + (1 − x1) · c\\n\\nσA2 (v2) = x2 · l + (1 − x2) · c\\n\\nThe first who leaves the loop loses! \\n\\nFor every x1, x2 > 0, BR(( x1, x2)) = (0 , 0) \\n\\nBR((0 , 0)) = {(x1, x2) | x1, x2 > 0}\\n\\nThe graph of BR is not closed \\n\\nKakutani’s theorem does not apply \\n\\n> 42/69\\n\\n## Does the standard theory apply? \\n\\nv1\\n\\nA1\\n\\nv2\\n\\nA2 \\n\\n> 13\\n\\n, 1 1, 13\\n\\nl l\\n\\nc\\n\\nc\\n\\nWe note ( x1, x2) ∈ [0 , 1] 2 for the profile σ s.t. \\n\\n{σA1 (v1) = x1 · l + (1 − x1) · c\\n\\nσA2 (v2) = x2 · l + (1 − x2) · c\\n\\nThe first who leaves the loop loses! \\n\\nFor every x1, x2 > 0, BR(( x1, x2)) = (0 , 0) \\n\\nBR((0 , 0)) = {(x1, x2) | x1, x2 > 0}\\n\\nThe graph of BR is not closed \\n\\nKakutani’s theorem does not apply \\n\\n> 42/69\\n\\n## Does the standard theory apply? No! \\n\\nv1\\n\\nA1\\n\\nv2\\n\\nA2 \\n\\n> 13\\n\\n, 1 1, 13\\n\\nl l\\n\\nc\\n\\nc\\n\\nWe note ( x1, x2) ∈ [0 , 1] 2 for the profile σ s.t. \\n\\n{σA1 (v1) = x1 · l + (1 − x1) · c\\n\\nσA2 (v2) = x2 · l + (1 − x2) · c\\n\\nThe first who leaves the loop loses! \\n\\nFor every x1, x2 > 0, BR(( x1, x2)) = (0 , 0) \\n\\nBR((0 , 0)) = {(x1, x2) | x1, x2 > 0}\\n\\nThe graph of BR is not closed \\n\\nKakutani’s theorem does not apply \\n\\n> 42/69\\n\\n## Does the standard theory apply? No! \\n\\nv1\\n\\nA1\\n\\nv2\\n\\nA2 \\n\\n> 13\\n\\n, 1 1, 13\\n\\nl l\\n\\nc\\n\\nc\\n\\nWe note ( x1, x2) ∈ [0 , 1] 2 for the profile σ s.t. \\n\\n{σA1 (v1) = x1 · l + (1 − x1) · c\\n\\nσA2 (v2) = x2 · l + (1 − x2) · c\\n\\nThe first who leaves the loop loses! \\n\\nFor every x1, x2 > 0, BR(( x1, x2)) = (0 , 0) \\n\\nBR((0 , 0)) = {(x1, x2) | x1, x2 > 0}\\n\\nThe graph of BR is not closed \\n\\nKakutani’s theorem does not apply \\n\\nHowever there are infinitely many Nash equilibria: all (0 , x2) and ( x1, 0) with x1, x2 > 0\\n\\n> 42/69\\n\\n## No universal existence in general!  \\n\\n> [UW11] Ummels, Wojtczak. The Complexity of Nash Equilibria in Limit-Average Games (CONCUR’11) .\\n\\nv0 0, 0\\n\\n−1, 1 1, −1\\n\\n> aa , bb\\n> ab\\n\\nba \\n\\nBy playing stationary strategy \\n\\nσA2 (v0) = (1 − \\u000f) · a + \\u000f · b,\\n\\nA2 ensures payoff 1 − 2\\u000f\\n\\nHence any Nash equilibrium would have payoff ( −1, 1) \\n\\nIf A2 plays a forever, then A1 will play \\n\\nb forever, yielding payoff (0 , 0), which is not a Nash equilibrium \\n\\nIf A2 plays b with some positive probability p at some round (first time this occurs), then by playing b before and a at that precise round, A1 can ensure payoff p > 0\\n\\n; There is no Nash equilibrium! \\n\\n> 43/69\\n\\n## No universal existence in general!  \\n\\n> [UW11] Ummels, Wojtczak. The Complexity of Nash Equilibria in Limit-Average Games (CONCUR’11) .\\n\\nv0 0, 0\\n\\n−1, 1 1, −1\\n\\n> aa , bb\\n> ab\\n\\nba \\n\\nBy playing stationary strategy \\n\\nσA2 (v0) = (1 − \\u000f) · a + \\u000f · b,\\n\\nA2 ensures payoff 1 − 2\\u000f\\n\\nHence any Nash equilibrium would have payoff ( −1, 1) \\n\\nIf A2 plays a forever, then A1 will play \\n\\nb forever, yielding payoff (0 , 0), which is not a Nash equilibrium \\n\\nIf A2 plays b with some positive probability p at some round (first time this occurs), then by playing b before and a at that precise round, A1 can ensure payoff p > 0\\n\\n; There is no Nash equilibrium! \\n\\n> 43/69\\n\\n## No universal existence in general!  \\n\\n> [UW11] Ummels, Wojtczak. The Complexity of Nash Equilibria in Limit-Average Games (CONCUR’11) .\\n\\nv0 0, 0\\n\\n−1, 1 1, −1\\n\\n> aa , bb\\n> ab\\n\\nba \\n\\nBy playing stationary strategy \\n\\nσA2 (v0) = (1 − \\u000f) · a + \\u000f · b,\\n\\nA2 ensures payoff 1 − 2\\u000f\\n\\nHence any Nash equilibrium would have payoff ( −1, 1) \\n\\nIf A2 plays a forever, then A1 will play \\n\\nb forever, yielding payoff (0 , 0), which is not a Nash equilibrium \\n\\nIf A2 plays b with some positive probability p at some round (first time this occurs), then by playing b before and a at that precise round, A1 can ensure payoff p > 0\\n\\n; There is no Nash equilibrium! \\n\\n> 43/69\\n\\n## No universal existence in general!  \\n\\n> [UW11] Ummels, Wojtczak. The Complexity of Nash Equilibria in Limit-Average Games (CONCUR’11) .\\n\\nv0 0, 0\\n\\n−1, 1 1, −1\\n\\n> aa , bb\\n> ab\\n\\nba \\n\\nBy playing stationary strategy \\n\\nσA2 (v0) = (1 − \\u000f) · a + \\u000f · b,\\n\\nA2 ensures payoff 1 − 2\\u000f\\n\\nHence any Nash equilibrium would have payoff ( −1, 1) \\n\\nIf A2 plays a forever, then A1 will play \\n\\nb forever, yielding payoff (0 , 0), which is not a Nash equilibrium \\n\\nIf A2 plays b with some positive probability p at some round (first time this occurs), then by playing b before and a at that precise round, A1 can ensure payoff p > 0\\n\\n; There is no Nash equilibrium! \\n\\n> 43/69\\n\\n## No universal existence in general!  \\n\\n> [UW11] Ummels, Wojtczak. The Complexity of Nash Equilibria in Limit-Average Games (CONCUR’11) .\\n\\nv0 0, 0\\n\\n−1, 1 1, −1\\n\\n> aa , bb\\n> ab\\n\\nba \\n\\nBy playing stationary strategy \\n\\nσA2 (v0) = (1 − \\u000f) · a + \\u000f · b,\\n\\nA2 ensures payoff 1 − 2\\u000f\\n\\nHence any Nash equilibrium would have payoff ( −1, 1) \\n\\nIf A2 plays a forever, then A1 will play \\n\\nb forever, yielding payoff (0 , 0), which is not a Nash equilibrium \\n\\nIf A2 plays b with some positive probability p at some round (first time this occurs), then by playing b before and a at that precise round, A1 can ensure payoff p > 0\\n\\n; There is no Nash equilibrium! \\n\\n> 43/69\\n\\n## No universal existence in general!  \\n\\n> [UW11] Ummels, Wojtczak. The Complexity of Nash Equilibria in Limit-Average Games (CONCUR’11) .\\n\\nv0 0, 0\\n\\n−1, 1 1, −1\\n\\n> aa , bb\\n> ab\\n\\nba \\n\\nBy playing stationary strategy \\n\\nσA2 (v0) = (1 − \\u000f) · a + \\u000f · b,\\n\\nA2 ensures payoff 1 − 2\\u000f\\n\\nHence any Nash equilibrium would have payoff ( −1, 1) \\n\\nIf A2 plays a forever, then A1 will play \\n\\nb forever, yielding payoff (0 , 0), which is not a Nash equilibrium \\n\\nIf A2 plays b with some positive probability p at some round (first time this occurs), then by playing b before and a at that precise round, A1 can ensure payoff p > 0\\n\\n; There is no Nash equilibrium! \\n\\n> 43/69\\n\\n## Outline  \\n\\n> 1\\n\\nVerification and game theory  \\n\\n> 2\\n\\nWhat is a game?  \\n\\n> 3\\n\\nA glimpse on strategic games  \\n\\n> 4\\n\\nGames on graphs \\n\\nThe general model \\n\\nFocus on a simple scenario \\n\\nAdding probabilities to the setting? \\n\\nConcurrent games  \\n\\n> 5\\n\\nConclusion \\n\\n> 44/69\\n\\n## We focus on a simple scenario \\n\\nRestrictions \\n\\nTurn-based games \\n\\nPayoffs given by ω-regular objectives: φA objective of player A ∈ Agt \\n\\nPure strategy profiles \\n\\n# ,\\n\\n# ,\\n\\n# ,\\n\\n> 45/69\\n\\n## We focus on a simple scenario \\n\\nRestrictions \\n\\nTurn-based games \\n\\nPayoffs given by ω-regular objectives: φA objective of player A ∈ Agt \\n\\nPure strategy profiles \\n\\n# ,\\n\\n# ,\\n\\n# ,\\n\\nis a Nash equilibrium with payoff (0 , 1, 0) \\n\\n> 45/69\\n\\n## We focus on a simple scenario \\n\\nRestrictions \\n\\nTurn-based games \\n\\nPayoffs given by ω-regular objectives: φA objective of player A ∈ Agt \\n\\nPure strategy profiles \\n\\n# ,\\n\\n# ,\\n\\n# ,\\n\\nis not a Nash equilibrium \\n\\n> 45/69\\n\\n## A simple characterization for ω-regular objectives \\n\\nPlayer loses along that play \\n\\nφA: objective of player A\\n\\n> 46/69\\n\\n## A simple characterization for ω-regular objectives \\n\\nφA: objective of player A\\n\\n. . . \\n\\nPlayer loses along that play \\n\\nPlayer should lose \\n\\n> 46/69\\n\\n## A simple characterization for ω-regular objectives \\n\\nφA: objective of player A\\n\\n. . . \\n\\nPlayer loses along that play \\n\\nCoalition { , }\\n\\nprevents from winning (by determinacy) \\n\\nthreat/punishment strategy \\n\\n> 46/69\\n\\n## A simple characterization for ω-regular objectives \\n\\nφA: objective of player A\\n\\n. . . \\n\\nPlayer loses along that play \\n\\nCoalition { , }\\n\\nprevents from winning \\n\\n¬φ ⇒ G(p ⇒ XW{ , })where p labels -states and W{ , } is the set of winning states for the coalition { , } for winning objective ¬φ .\\n\\n> 46/69\\n\\n## A simple characterization for ω-regular objectives \\n\\nφA: objective of player A\\n\\n. . . \\n\\nPlayer loses along that play \\n\\nCoalition { , }\\n\\nprevents from winning \\n\\nMain outcomes of Boolean Nash equilibria in turn-based games can be characterized by an LTL formula: ΦNE = ∧\\n\\n> A∈Agt\\n\\n(\\n\\n¬φA ⇒ G(pA ⇒ XW{− A})\\n\\n)\\n\\nwhere pA labels A-states and W{− A} is the set of winning states for the coalition {− A} def \\n\\n= Agt \\\\ { A} against A for the objective ¬φA. These sets should be precomputed. \\n\\n> 46/69\\n\\n## A simple characterization for ω-regular objectives \\n\\nφA: objective of player A\\n\\n. . . \\n\\nPlayer loses along that play \\n\\nCoalition { , }\\n\\nprevents from winning \\n\\nMain outcomes of Boolean Nash equilibria in turn-based games can be characterized by an LTL formula: ΦNE = ∧\\n\\n> A∈Agt\\n\\n(\\n\\n¬φA ⇒ G(pA ⇒ XW{− A})\\n\\n)\\n\\nwhere pA labels A-states and W{− A} is the set of winning states for the coalition {− A} def \\n\\n= Agt \\\\ { A} against A for the objective ¬φA. These sets should be precomputed. \\n\\n(valid for prefix-independent objectives) \\n\\n> 46/69\\n\\n## Decidability of the constrained existence problem  \\n\\n> [Umm08] Ummels. The Complexity of Nash Equilibria in Infinite Multiplayer Games (FoSSaCS’08) .\\n\\nConstrained existence problem \\n\\nGiven two thresholds L, U ∈ Q+, does there exists a Nash equilibrium σ\\n\\nsuch that for every A ∈ Agt: \\n\\nLA ≤ Eσ\\n\\nv0 (payoff A) ≤ UA?\\n\\n> 47/69\\n\\n## Decidability of the constrained existence problem  \\n\\n> [Umm08] Ummels. The Complexity of Nash Equilibria in Infinite Multiplayer Games (FoSSaCS’08) .\\n\\nConstrained existence problem \\n\\nGiven two thresholds L, U ∈ Q+, does there exists a Nash equilibrium σ\\n\\nsuch that for every A ∈ Agt: \\n\\nLA ≤ Eσ \\n\\n> v0\\n\\n(payoff A) ≤ UA?\\n\\nTheorem [Umm08] \\n\\nOne can decide the pure constrained existence problem in finite turn-based multiplayer games for ω-regular objectives. \\n\\nExamples of complexity results for single objectives: \\n\\nObjectives Reach. Safety B¨ uchi co-B¨ uchi Parity \\n\\nComplexity NP-c. P-c. NP-c. \\n\\nNote: it extends to “ ω-regular” preference relations with a finite image. \\n\\n> 47/69\\n\\n## An example of NP-hardness result \\n\\nBy reduction from a SAT instance: \\n\\nϕ = ∧\\n\\n> 1≤i≤n\\n\\nCi with Ci =\\n\\n> 3\\n\\n∨\\n\\n> j=1\\n\\n`i,j `i,j ∈ { x1, ¬x1, x2, ¬x2, . . . , xk , ¬xk }\\n\\n> 48/69\\n\\n## An example of NP-hardness result \\n\\nBy reduction from a SAT instance: \\n\\nϕ = ∧\\n\\n> 1≤i≤n\\n\\nCi with Ci =\\n\\n> 3\\n\\n∨\\n\\n> j=1\\n\\n`i,j `i,j ∈ { x1, ¬x1, x2, ¬x2, . . . , xk , ¬xk }\\n\\nA A A . . . \\n\\nx1\\n\\n¬x1\\n\\nx2\\n\\n¬x2\\n\\nxk\\n\\n¬xk\\n\\nPlayer Ai for clause Ci , with objective to reach {`i,j | j = 1 , 2, 3}\\n\\nPlayer A: reach the rightmost state \\n\\n> 48/69\\n\\n## An example of NP-hardness result \\n\\nBy reduction from a SAT instance: \\n\\nϕ = ∧\\n\\n> 1≤i≤n\\n\\nCi with Ci =\\n\\n> 3\\n\\n∨\\n\\n> j=1\\n\\n`i,j `i,j ∈ { x1, ¬x1, x2, ¬x2, . . . , xk , ¬xk }\\n\\nA A A . . . \\n\\nx1\\n\\n¬x1\\n\\nx2\\n\\n¬x2\\n\\nxk\\n\\n¬xk\\n\\nPlayer Ai for clause Ci , with objective to reach {`i,j | j = 1 , 2, 3}\\n\\nPlayer A: reach the rightmost state \\n\\nϕ is satisfiable iff there is a Nash equilibrium with payoff 1 for everyone in the game \\n\\n> 48/69\\n\\n## The universal existence problem: ω-regular objectives \\n\\nA\\n\\nB\\n\\nC\\n\\nD E\\n\\nF\\n\\nG H\\n\\nI\\n\\nJ\\n\\nK L\\n\\nM\\n\\nN O\\n\\n... ... ... ... ... ... ... ...\\n\\n> 49/69\\n\\n## The universal existence problem: ω-regular objectives \\n\\nA\\n\\nB\\n\\nC\\n\\nD E\\n\\nF\\n\\nG H\\n\\nI\\n\\nJ\\n\\nK L\\n\\nM\\n\\nN O\\n\\n... ... ... ... ... ... ... ...\\n\\nIf has a winning strategy from A, then should play it forever \\n\\nOtherwise plays any strategy, until (by chance) a new blue node, for instance J, is visited, from which has a winning strategy; then switches to such a winning strategy, forever \\n\\n> 49/69\\n\\n## The universal existence problem: ω-regular objectives \\n\\nA\\n\\nB\\n\\nC\\n\\nD E\\n\\nF\\n\\nG H\\n\\nI\\n\\nJ\\n\\nK L\\n\\nM\\n\\nN O\\n\\n... ... ... ... ... ... ... ...\\n\\nIf the game proceeds through B and has a winning strategy from B, then should play it forever \\n\\nIf the game proceeds through B but has no winning strategy from B, then should play any strategy, until (by chance) a new green node, for instance H, is visited, from which has a winning strategy; then switches to such a winning strategy, forever \\n\\n> 49/69\\n\\n## The universal existence problem: ω-regular objectives \\n\\nA\\n\\nB\\n\\nC\\n\\nD E\\n\\nF\\n\\nG H\\n\\nI\\n\\nJ\\n\\nK L\\n\\nM\\n\\nN O\\n\\n... ... ... ... ... ... ... ...\\n\\nIf the game proceeds through C and has a winning strategy from C, then should play it forever \\n\\nIf the game proceeds through C but has no winning strategy from C, then should play any strategy, until (by chance) a new red node, for instance E, is visited, from which has a winning strategy; then switches to such a winning strategy, forever \\n\\n> 49/69\\n\\n## The universal existence problem: ω-regular objectives \\n\\nA\\n\\nB\\n\\nC\\n\\nD E\\n\\nF\\n\\nG H\\n\\nI\\n\\nJ\\n\\nK L\\n\\nM\\n\\nN O\\n\\n... ... ... ... ... ... ... ...\\n\\nOutside the main outcome, all players play the adequate threat or punishment strategy: this is the coalition strategy that makes the deviator lose (NB: determinacy required!) \\n\\n> 49/69\\n\\nA\\n\\nB\\n\\nC\\n\\nD E\\n\\nF\\n\\nG H\\n\\nI\\n\\nJ\\n\\nK L\\n\\nM\\n\\nN O\\n\\n... ... ... ... ... ... ... ...\\n\\n> 50/69\\n\\nA\\n\\nB\\n\\nC\\n\\nD E\\n\\nF\\n\\nG H\\n\\nI\\n\\nJ\\n\\nK L\\n\\nM\\n\\nN O\\n\\n... ... ... ... ... ... ... ...\\n\\nA\\n\\nB\\n\\nF\\n\\nG\\n\\nmain outcome \\n\\n> 50/69\\n\\nA\\n\\nB\\n\\nC\\n\\nD E\\n\\nF\\n\\nG H\\n\\nI\\n\\nJ\\n\\nK L\\n\\nM\\n\\nN O\\n\\n... ... ... ... ... ... ... ...\\n\\nmain outcome \\n\\npossible deviation \\n\\n> 50/69\\n\\nA\\n\\nB\\n\\nC\\n\\nD E\\n\\nF\\n\\nG H\\n\\nI\\n\\nJ\\n\\nK L\\n\\nM\\n\\nN O\\n\\n... ... ... ... ... ... ... ...\\n\\nmain outcome \\n\\npossible deviation \\n\\nthreat (or punishment) strategy \\n\\n> 50/69\\n\\nA\\n\\nB\\n\\nC\\n\\nD E\\n\\nF\\n\\nG H\\n\\nI\\n\\nJ\\n\\nK L\\n\\nM\\n\\nN O\\n\\n... ... ... ... ... ... ... ...\\n\\nmain outcome \\n\\npossible deviation \\n\\nthreat (or punishment) strategy \\n\\nnever taken \\n\\n> 50/69\\n\\nA\\n\\nB\\n\\nC\\n\\nD E\\n\\nF\\n\\nG H\\n\\nI\\n\\nJ\\n\\nK L\\n\\nM\\n\\nN O\\n\\n... ... ... ... ... ... ... ...\\n\\nmain outcome \\n\\npossible deviation \\n\\nthreat (or punishment) strategy \\n\\nnever taken \\n\\nQuestions: \\n\\nwhy is it correct? \\n\\nwhat immediate extension can be handled? \\n\\n> 50/69\\n\\nA\\n\\nB\\n\\nC\\n\\nD E\\n\\nF\\n\\nG H\\n\\nI\\n\\nJ\\n\\nK L\\n\\nM\\n\\nN O\\n\\n... ... ... ... ... ... ... ...\\n\\nmain outcome \\n\\npossible deviation \\n\\nthreat (or punishment) strategy \\n\\nnever taken \\n\\nQuestions: \\n\\nwhy is it correct? \\n\\nwhat immediate extension can be handled? \\n\\n> 50/69\\n\\n## The universal existence problem: ω-regular objectives   \\n\\n> [Umm11] Ummels. Stochastic multiplayer games: theory and algorithms (PhD thesis ). [LeR13] Le Roux. Infinite sequential Nash equilibrium (LMCS) .\\n\\nUniversal existence [Umm11] \\n\\nIn infinite-duration turn-based deterministic games on finite graphs with \\n\\nω-regular objectives, there is always a pure Nash equilibrium. Moreover, one can compute a witness. \\n\\n> 51/69\\n\\n## The universal existence problem   \\n\\n> [Umm11] Ummels. Stochastic multiplayer games: theory and algorithms (PhD thesis ). [LeR13] Le Roux. Infinite sequential Nash equilibrium (LMCS) .\\n\\nUniversal existence [Umm11] \\n\\nIn infinite-duration turn-based deterministic games on finite graphs with \\n\\nω-regular objectives, there is always a pure Nash equilibrium. Moreover, one can compute a witness. \\n\\nUniversal existence [LeR13] \\n\\nIn infinite turn-based deterministic games with Borel measurable countable preferences, with no ascending infinite chains, there is always a pure Nash equilibrium. \\n\\n> 51/69\\n\\n## Outline  \\n\\n> 1\\n\\nVerification and game theory  \\n\\n> 2\\n\\nWhat is a game?  \\n\\n> 3\\n\\nA glimpse on strategic games  \\n\\n> 4\\n\\nGames on graphs \\n\\nThe general model \\n\\nFocus on a simple scenario \\n\\nAdding probabilities to the setting? \\n\\nConcurrent games  \\n\\n> 5\\n\\nConclusion \\n\\n> 52/69\\n\\n## Stochastic turn-based games \\n\\n[UW11] Ummels, Wojtczak. The Complexity of Nash Equilibria in Stochastic Multiplayer Games (LMCS) .\\n\\nA2\\n\\nA3\\n\\nA1\\n\\n0, 23 , 13\\n\\n0, 23 , 13\\n\\n12 1, 78 , 18\\n\\n12\\n\\n12\\n\\n1, 1, 0\\n\\n12\\n\\n12\\n\\n1, 23 , 13\\n\\n12 1, 14 , 34\\n\\n12\\n\\n12\\n\\n1, 0, 1\\n\\npA2 + pA3 = 1 \\n\\npA2\\n\\n23 and pA3\\n\\n13\\n\\npA2 = 23 + 116 ( 12n′ − 12n )\\n\\n; n = n′\\n\\n53/69 Stochastic turn-based games \\n\\n[UW11] Ummels, Wojtczak. The Complexity of Nash Equilibria in Stochastic Multiplayer Games (LMCS) .\\n\\nA2\\n\\nA3\\n\\nA1\\n\\n0, 23 , 13\\n\\n0, 23 , 13\\n\\n12 1, 78 , 18\\n\\n12\\n\\n12\\n\\n1, 1, 0\\n\\n12\\n\\n12\\n\\n1, 23 , 13\\n\\n12 1, 14 , 34\\n\\n12\\n\\n12\\n\\n1, 0, 1\\n\\nAlong a Nash equilibrium where \\n\\npA1 ≥ 1: \\n\\npA2 + pA3 = 1 \\n\\npA2\\n\\n23 and pA3\\n\\n13\\n\\npA2 = 23 + 116 ( 12n′ − 12n )\\n\\n; n = n′\\n\\n53/69 Stochastic turn-based games \\n\\n[UW11] Ummels, Wojtczak. The Complexity of Nash Equilibria in Stochastic Multiplayer Games (LMCS) .\\n\\nA2\\n\\nA3\\n\\nA1\\n\\n0, 23 , 13\\n\\n0, 23 , 13\\n\\n12 1, 78 , 18\\n\\n12\\n\\n12\\n\\n1, 1, 0\\n\\n12\\n\\n12\\n\\n1, 23 , 13\\n\\n12 1, 14 , 34\\n\\n12\\n\\n12\\n\\n1, 0, 1\\n\\nAlong a Nash equilibrium where \\n\\npA1 ≥ 1: \\n\\npA2 + pA3 = 1 \\n\\npA2\\n\\n23 and pA3\\n\\n13\\n\\npA2 = 23 + 116 ( 12n′ − 12n )\\n\\n; n = n′\\n\\n53/69 Stochastic turn-based games \\n\\n[UW11] Ummels, Wojtczak. The Complexity of Nash Equilibria in Stochastic Multiplayer Games (LMCS) .\\n\\nA2\\n\\nA3\\n\\nA1\\n\\n0, 23 , 13\\n\\n0, 23 , 13\\n\\n12 1, 78 , 18\\n\\n12\\n\\n12\\n\\n1, 1, 0\\n\\n12\\n\\n12\\n\\n1, 23 , 13\\n\\n12 1, 14 , 34\\n\\n12\\n\\n12\\n\\n1, 0, 1\\n\\nAlong a Nash equilibrium where \\n\\npA1 ≥ 1: \\n\\npA2 + pA3 = 1 \\n\\npA2 ≥ 23 and pA3 ≥ 13\\n\\npA2 = 23 + 116 ( 12n′ − 12n )\\n\\n; n = n′\\n\\n53/69 Stochastic turn-based games \\n\\n[UW11] Ummels, Wojtczak. The Complexity of Nash Equilibria in Stochastic Multiplayer Games (LMCS) .\\n\\nA2\\n\\nA3\\n\\nA1\\n\\n0, 23 , 13\\n\\n0, 23 , 13\\n\\n12 1, 78 , 18\\n\\n12\\n\\n12\\n\\n1, 1, 0\\n\\n12\\n\\n12\\n\\n1, 23 , 13\\n\\n12 1, 14 , 34\\n\\n12\\n\\n12\\n\\n1, 0, 1\\n\\nAlong a Nash equilibrium where \\n\\npA1 ≥ 1: \\n\\npA2 + pA3 = 1 \\n\\npA2 = 23 and pA3 = 13\\n\\npA2 = 23 + 116 ( 12n′ − 12n )\\n\\n; n = n′\\n\\n53/69 Stochastic turn-based games \\n\\n[UW11] Ummels, Wojtczak. The Complexity of Nash Equilibria in Stochastic Multiplayer Games (LMCS) .\\n\\nA2\\n\\nA3\\n\\nA1\\n\\nn times \\n\\nn′ times \\n\\n0, 23 , 13\\n\\n0, 23 , 13\\n\\n12 1, 78 , 18\\n\\n12\\n\\n12\\n\\n1, 1, 0\\n\\n12\\n\\n12\\n\\n1, 23 , 13\\n\\n12 1, 14 , 34\\n\\n12\\n\\n12\\n\\n1, 0, 1\\n\\nAlong a Nash equilibrium where \\n\\npA1 ≥ 1: \\n\\npA2 + pA3 = 1 \\n\\npA2 = 23 and pA3 = 13\\n\\npA2 = 23 + 116 ( 12n′ − 12n )\\n\\n; n = n′\\n\\n53/69 Stochastic turn-based games \\n\\n[UW11] Ummels, Wojtczak. The Complexity of Nash Equilibria in Stochastic Multiplayer Games (LMCS) .\\n\\nA2\\n\\nA3\\n\\nA1\\n\\nn times \\n\\nn′ times \\n\\n0, 23 , 13\\n\\n0, 23 , 13\\n\\n12 1, 78 , 18\\n\\n12\\n\\n12\\n\\n1, 1, 0\\n\\n12\\n\\n12\\n\\n1, 23 , 13\\n\\n12 1, 14 , 34\\n\\n12\\n\\n12\\n\\n1, 0, 1\\n\\nAlong a Nash equilibrium where \\n\\npA1 ≥ 1: \\n\\npA2 + pA3 = 1 \\n\\npA2 = 23 and pA3 = 13\\n\\npA2 = 23 + 116 ( 12n′ − 12n )\\n\\n; n = n′\\n\\n53/69 Stochastic turn-based games  \\n\\n> [UW11] Ummels, Wojtczak. The Complexity of Nash Equilibria in Stochastic Multiplayer Games (LMCS) .\\n\\nA2\\n\\nA3\\n\\nA1\\n\\nn times \\n\\nn′ times \\n\\n0, 23 , 13\\n\\n0, 23 , 13\\n\\n12 1, 78 , 18\\n\\n12\\n\\n12\\n\\n1, 1, 0\\n\\n12\\n\\n12\\n\\n1, 23 , 13\\n\\n12 1, 14 , 34\\n\\n12\\n\\n12\\n\\n1, 0, 1\\n\\nAlong a Nash equilibrium where \\n\\npA1 ≥ 1: \\n\\npA2 + pA3 = 1 \\n\\npA2 = 23 and pA3 = 13\\n\\npA2 = 23 + 116 ( 12n′ − 12n )\\n\\n; n = n′\\n\\nOne can simulate a two-counter machine if we constrain pA1 ≥ 1!! \\n\\n> 53/69\\n\\n## Stochastic turn-based games  \\n\\n> [UW11] Ummels, Wojtczak. The Complexity of Nash Equilibria in Stochastic Multiplayer Games (LMCS) .\\n\\nUndecidability results [UW11] \\n\\nThe constrained existence problem for pure strategies in stochastic turn-based games is undecidable. \\n\\nThe constrained existence problem for mixed strategies in deterministic turn-based games is undecidable. \\n\\n> 53/69\\n\\n## Stochastic turn-based games  \\n\\n> [UW11] Ummels, Wojtczak. The Complexity of Nash Equilibria in Stochastic Multiplayer Games (LMCS) .\\n\\nUndecidability results [UW11] \\n\\nThe constrained existence problem for pure strategies in stochastic turn-based games is undecidable. \\n\\nThe constrained existence problem for mixed strategies in deterministic turn-based games is undecidable. \\n\\n> 53/69\\n\\n## Stochastic turn-based games  \\n\\n> [UW11] Ummels, Wojtczak. The Complexity of Nash Equilibria in Stochastic Multiplayer Games (LMCS) .\\n\\nUndecidability results [UW11] \\n\\nThe constrained existence problem for pure strategies in stochastic turn-based games is undecidable. \\n\\nThe constrained existence problem for mixed strategies in deterministic turn-based games is undecidable. \\n\\n> 53/69\\n\\n## Short summary for turn-based ω-regular games \\n\\n[UW11] Ummels, Wojtczak. The Complexity of Nash Equilibria in Stochastic Multiplayer Games (LMCS) \\n\\n[Umm11] Ummels. Stochastic multiplayer games: theory and algorithms (PhD thesis, RWTH Aachen University) \\n\\n[LeR13] Le Roux. Infinite sequential Nash equilibrium (LMCS) .\\n\\n[UW11,Umm11,LeR13] \\n\\nThere always exists a Nash equilibrium for Boolean ω-regular objectives \\n\\nOne can decide the constrained existence of a Nash equilibrium (and compute one!) \\n\\nOne cannot decide the existence of a mixed (i.e. stochastic) Nash equilibrium \\n\\n54/69 Short summary for turn-based ω-regular games    \\n\\n> [UW11] Ummels, Wojtczak. The Complexity of Nash Equilibria in Stochastic Multiplayer Games (LMCS)\\n> [Umm11] Ummels. Stochastic multiplayer games: theory and algorithms (PhD thesis, RWTH Aachen University)\\n> [LeR13] Le Roux. Infinite sequential Nash equilibrium (LMCS) .\\n\\n[UW11,Umm11,LeR13] \\n\\nThere always exists a Nash equilibrium for Boolean ω-regular objectives \\n\\nOne can decide the constrained existence of a Nash equilibrium (and compute one!) \\n\\nOne cannot decide the existence of a mixed (i.e. stochastic) Nash equilibrium \\n\\n; this is why we will restrict to pure equilibria in det. games \\n\\n> 54/69\\n\\n## Outline  \\n\\n> 1\\n\\nVerification and game theory  \\n\\n> 2\\n\\nWhat is a game?  \\n\\n> 3\\n\\nA glimpse on strategic games  \\n\\n> 4\\n\\nGames on graphs \\n\\nThe general model \\n\\nFocus on a simple scenario \\n\\nAdding probabilities to the setting? \\n\\nConcurrent games  \\n\\n> 5\\n\\nConclusion \\n\\n> 55/69\\n\\n## Can this theory be extended to concurrent games? \\n\\nv0\\n\\n0, 1 1, 0\\n\\n> aa , bb\\n> ab , ba\\n\\nThere is no universal existence, even for simple Boolean objectives. \\n\\n> 56/69\\n\\n## Can this theory be extended to concurrent games? \\n\\nv0\\n\\n0, 1 1, 0\\n\\n> aa , bb\\n> ab , ba\\n\\nThere is no universal existence, even for simple Boolean objectives. \\n\\nv0 0, 0\\n\\n−1, 1 1, −1\\n\\n> aa , bb\\n> ab\\n\\nba \\n\\nThere is no pure Nash equilibrium \\n\\nv0 0, 00, 1 1, 0\\n\\n> aa , bb\\n> ab\\n\\nba \\n\\nThere is a pure Nash equilibrium \\n\\n> 56/69\\n\\n## Can this theory be extended to concurrent games? \\n\\nv0\\n\\n0, 1 1, 0\\n\\n> aa , bb\\n> ab , ba\\n\\nThere is no universal existence, even for simple Boolean objectives. \\n\\nv0 1, 10, 2 2, 0\\n\\n> aa , bb\\n> ab\\n\\nba \\n\\nThere is no pure Nash equilibrium \\n\\nv0 0, 00, 2 2, 0\\n\\n> aa , bb\\n> ab\\n\\nba \\n\\nThere is a pure Nash equilibrium \\n\\n> 56/69\\n\\n## Existence becomes NP-hard \\n\\nHardness \\n\\nThe existence problem is NP-hard for reachability objectives. By reduction from a SAT instance: \\n\\nϕ = ∧\\n\\n> 1≤i≤n\\n\\nCi with Ci =\\n\\n> 3\\n\\n∨\\n\\n> j=1\\n\\n`i,j `i,j ∈ { x1, ¬x1, x2, ¬x2, . . . , xk , ¬xk }\\n\\nA A A . . . \\n\\nx1\\n\\n¬x1\\n\\nx2\\n\\n¬x2\\n\\nxk\\n\\n¬xk\\n\\nϕ is satisfiable iff there is a Nash equilibrium with payoff 1 for everyone in the game \\n\\n> 57/69\\n\\n## Existence becomes NP-hard \\n\\nHardness \\n\\nThe existence problem is NP-hard for reachability objectives.    \\n\\n> AAA. . .\\n> x1\\n> ¬x1\\n> x2\\n> ¬x2\\n> xk\\n> ¬xk\\n\\nB An/B\\n\\nB An−1/B\\n\\nB\\n\\nB A1/B\\n\\nwinning for An\\n\\nwinning for An−1\\n\\nwinning for A1\\n\\n. . . \\n\\nmatching pennies \\n\\nϕ is satisfiable iff there is a Nash equilibrium in the game \\n\\n> 57/69\\n\\n## Who is a suspect? Who knows what? \\n\\nv0\\n\\nv1\\n\\nv2\\n\\nv3\\n\\naaa \\n\\nbaa \\n\\nbba \\n\\naba \\n\\naab \\n\\n· · · \\n\\n· · · \\n\\n· · · \\n\\n> 58/69\\n\\n## Who is a suspect? Who knows what? \\n\\nv0\\n\\nv1\\n\\nv2\\n\\nv3\\n\\naaa \\n\\nbaa \\n\\nbba \\n\\naba \\n\\naab \\n\\n· · · \\n\\n· · · \\n\\n· · · \\n\\nAssume that the normal move is v0\\n\\n> aaa\\n\\n−−→ v1\\n\\nwhat does that mean if the game proceeds to v2?\\n\\nwhat does that mean if the game proceeds to v3?\\n\\n> 58/69\\n\\n## Who is a suspect? Who knows what? \\n\\nv0\\n\\nv1\\n\\nv2\\n\\nv3\\n\\naaa \\n\\nbaa \\n\\nbba \\n\\naba \\n\\naab \\n\\n· · · \\n\\n· · · \\n\\n· · · \\n\\nAssume that the normal move is v0\\n\\n> aaa\\n\\n−−→ v1\\n\\nwhat does that mean if the game proceeds to v2?\\n\\neither player A1 deviated alone (playing b instead of a); \\n\\nwhat does that mean if the game proceeds to v3?\\n\\neither player A2 deviated alone (playing b instead of a); \\n\\nor A3 deviated alone (playing b instead of a). \\n\\n> 58/69\\n\\n## Who is a suspect? Who knows what? \\n\\nv0\\n\\nv1\\n\\nv2\\n\\nv3\\n\\naaa \\n\\nbaa \\n\\nbba \\n\\naba \\n\\naab \\n\\n· · · \\n\\n· · · \\n\\n· · · \\n\\nAssume that the normal move is v0\\n\\n> aaa\\n\\n−−→ v1\\n\\nwhat does that mean if the game proceeds to v2?\\n\\neither player A1 deviated alone (playing b instead of a); \\n\\nwhat does that mean if the game proceeds to v3?\\n\\neither player A2 deviated alone (playing b instead of a); \\n\\nor A3 deviated alone (playing b instead of a). \\n\\n> 58/69\\n\\n## Who is a suspect? Who knows what? \\n\\nv0\\n\\nv1\\n\\nv2\\n\\nv3\\n\\naaa \\n\\nbaa \\n\\nbba \\n\\naba \\n\\naab \\n\\n· · · \\n\\n· · · \\n\\n· · · \\n\\nAssume that the normal move is v0\\n\\n> aaa\\n\\n−−→ v1\\n\\nwhat does that mean if the game proceeds to v2?\\n\\neither player A1 deviated alone (playing b instead of a); \\n\\nwhat does that mean if the game proceeds to v3?\\n\\neither player A2 deviated alone (playing b instead of a); \\n\\nor A3 deviated alone (playing b instead of a). \\n\\n> 58/69\\n\\n## Who is a suspect? Who knows what? \\n\\nv0\\n\\nv1\\n\\nv2\\n\\nv3\\n\\naaa \\n\\nbaa \\n\\nbba \\n\\naba \\n\\naab \\n\\n· · · \\n\\n· · · \\n\\n· · · \\n\\nAssume that the normal move is v0\\n\\n> aaa\\n\\n−−→ v1\\n\\nwhat does that mean if the game proceeds to v2?\\n\\neither player A1 deviated alone (playing b instead of a); \\n\\nor both players A1 and A2 played b instead of a.\\n\\nwhat does that mean if the game proceeds to v3?\\n\\neither player A2 deviated alone (playing b instead of a); \\n\\nor A3 deviated alone (playing b instead of a). \\n\\n> 58/69\\n\\n## Who is a suspect? Who knows what? \\n\\nv0\\n\\nv1\\n\\nv2\\n\\nv3\\n\\naaa \\n\\nbaa \\n\\nbba \\n\\naba \\n\\naab \\n\\n· · · \\n\\n· · · \\n\\n· · · \\n\\nAssume that the normal move is v0\\n\\n> aaa\\n\\n−−→ v1\\n\\nwhat does that mean if the game proceeds to v2?\\n\\neither player A1 deviated alone (playing b instead of a); \\n\\nor both players A1 and A2 played b instead of a.\\n\\nwhat does that mean if the game proceeds to v3?\\n\\neither player A2 deviated alone (playing b instead of a); \\n\\nor A3 deviated alone (playing b instead of a). \\n\\n> 58/69\\n\\n## Who is a suspect? Who knows what? \\n\\nv0\\n\\nv1\\n\\nv2\\n\\nv3\\n\\naaa \\n\\nbaa \\n\\nbba \\n\\naba \\n\\naab \\n\\n· · · \\n\\n· · · \\n\\n· · · \\n\\nsusp ((v0, v2), aaa ) = {A1}\\n\\nsusp ((v0, v3), aaa ) = {A2, A3}\\n\\nsusp ((v0, v1), aaa ) = {A1, A2, A3}\\n\\nAssume that the normal move is v0\\n\\n> aaa\\n\\n−−→ v1\\n\\nwhat does that mean if the game proceeds to v2?\\n\\neither player A1 deviated alone (playing b instead of a); \\n\\nor both players A1 and A2 played b instead of a.\\n\\nwhat does that mean if the game proceeds to v3?\\n\\neither player A2 deviated alone (playing b instead of a); \\n\\nor A3 deviated alone (playing b instead of a). \\n\\n> 58/69\\n\\n## Who is a suspect? Who knows what? \\n\\nv0\\n\\nv1\\n\\nv2\\n\\nv3\\n\\naaa \\n\\nbaa \\n\\nbba \\n\\naba \\n\\naab \\n\\n· · · \\n\\n· · · \\n\\n· · · \\n\\nsusp ((v0, v2), aaa ) = {A1}  \\n\\n> Everyone knows that A1is the deviator\\n\\nsusp ((v0, v3), aaa ) = {A2, A3}\\n\\nsusp ((v0, v1), aaa ) = {A1, A2, A3}\\n\\nAssume that the normal move is v0\\n\\n> aaa\\n\\n−−→ v1\\n\\nwhat does that mean if the game proceeds to v2?\\n\\neither player A1 deviated alone (playing b instead of a); \\n\\nor both players A1 and A2 played b instead of a.\\n\\nwhat does that mean if the game proceeds to v3?\\n\\neither player A2 deviated alone (playing b instead of a); \\n\\nor A3 deviated alone (playing b instead of a). \\n\\n> 58/69\\n\\n## Who is a suspect? Who knows what? \\n\\nv0\\n\\nv1\\n\\nv2\\n\\nv3\\n\\naaa \\n\\nbaa \\n\\nbba \\n\\naba \\n\\naab \\n\\n· · · \\n\\n· · · \\n\\n· · · \\n\\nsusp ((v0, v2), aaa ) = {A1}\\n\\nEveryone knows that A1 is the deviator \\n\\nsusp ((v0, v3), aaa ) = {A2, A3}\\n\\nA1 knows that the deviator is either A2 or \\n\\nA3; A2 knows the identity of the deviator; and so does A3\\n\\nsusp ((v0, v1), aaa ) = {A1, A2, A3}\\n\\nAssume that the normal move is v0\\n\\naaa \\n\\n−−→ v1\\n\\nwhat does that mean if the game proceeds to v2?\\n\\neither player A1 deviated alone (playing b instead of a); \\n\\nor both players A1 and A2 played b instead of a.\\n\\nwhat does that mean if the game proceeds to v3?\\n\\neither player A2 deviated alone (playing b instead of a); \\n\\nor A3 deviated alone (playing b instead of a). \\n\\n> 58/69\\n\\n## Who is a suspect? Who knows what? \\n\\nv0\\n\\nv1\\n\\nv2\\n\\nv3\\n\\naaa \\n\\nbaa \\n\\nbba \\n\\naba \\n\\naab \\n\\n· · · \\n\\n· · · \\n\\n· · · \\n\\nsusp ((v0, v2), aaa ) = {A1}\\n\\nEveryone knows that A1 is the deviator \\n\\nsusp ((v0, v3), aaa ) = {A2, A3}\\n\\nA1 knows that the deviator is either A2 or \\n\\nA3; A2 knows the identity of the deviator; and so does A3\\n\\nsusp ((v0, v1), aaa ) = {A1, A2, A3}\\n\\nAssume that the normal move is v0\\n\\naaa \\n\\n−−→ v1\\n\\nwhat does that mean if the game proceeds to v2?\\n\\neither player A1 deviated alone (playing b instead of a); \\n\\nor both players A1 and A2 played b instead of a.\\n\\nwhat does that mean if the game proceeds to v3?\\n\\neither player A2 deviated alone (playing b instead of a); \\n\\nor A3 deviated alone (playing b instead of a). \\n\\n> 58/69\\n\\n## Construction of the suspect game abstraction \\n\\nv0\\n\\nv1\\n\\nv2\\n\\nv3\\n\\naaa \\n\\nother \\n\\nbaa \\n\\nbba \\n\\naba \\n\\naab \\n\\n··· \\n\\n··· \\n\\n··· \\n\\nTwo players: Eve (light) Adam (dark) \\n\\n> 59/69\\n\\n## Construction of the suspect game abstraction \\n\\nv0\\n\\n{A1,A2,A3}\\n\\nv0\\n\\nv1\\n\\nv2\\n\\nv3\\n\\naaa \\n\\nother \\n\\nbaa \\n\\nbba \\n\\naba \\n\\naab \\n\\n··· \\n\\n··· \\n\\n··· \\n\\nTwo players: Eve (light) Adam (dark) \\n\\n> 59/69\\n\\n## Construction of the suspect game abstraction \\n\\nv0\\n\\n{A1,A2,A3}\\n\\nv0\\n\\n{A1,A2,A3}\\n\\naaa \\n\\naaa \\n\\nv0\\n\\nv1\\n\\nv2\\n\\nv3\\n\\naaa \\n\\nother \\n\\nbaa \\n\\nbba \\n\\naba \\n\\naab \\n\\n··· \\n\\n··· \\n\\n··· \\n\\nTwo players: Eve (light) Adam (dark) \\n\\n> 59/69\\n\\n## Construction of the suspect game abstraction \\n\\nv0\\n\\n{A1,A2,A3}\\n\\nv0\\n\\n{A1,A2,A3}\\n\\naaa \\n\\nv1\\n\\n{A1,A2,A3}\\n\\nv2\\n\\n{A1}\\n\\nv3\\n\\n{A2,A3}\\n\\naaa v1\\n\\nv2\\n\\nv3\\n\\nv0\\n\\nv1\\n\\nv2\\n\\nv3\\n\\naaa \\n\\nother \\n\\nbaa \\n\\nbba \\n\\naba \\n\\naab \\n\\n··· \\n\\n··· \\n\\n··· \\n\\nTwo players: Eve (light) Adam (dark) \\n\\n> 59/69\\n\\n## Construction of the suspect game abstraction \\n\\nv0\\n\\n{A1,A2,A3}\\n\\nv0\\n\\n{A1,A2,A3}\\n\\naaa \\n\\nv1\\n\\n{A1,A2,A3}\\n\\nv2\\n\\n{A1}\\n\\nv3\\n\\n{A2,A3}\\n\\naaa v1\\n\\nv2\\n\\nv3\\n\\nv0\\n\\n{A1,A2,A3}\\n\\nbaa \\n\\nbaa \\n\\nv0\\n\\nv1\\n\\nv2\\n\\nv3\\n\\naaa \\n\\nother \\n\\nbaa \\n\\nbba \\n\\naba \\n\\naab \\n\\n··· \\n\\n··· \\n\\n··· \\n\\nTwo players: Eve (light) Adam (dark) \\n\\n> 59/69\\n\\n## Construction of the suspect game abstraction \\n\\nv0\\n\\n{A1,A2,A3}\\n\\nv0\\n\\n{A1,A2,A3}\\n\\naaa \\n\\nv1\\n\\n{A1,A2,A3}\\n\\nv2\\n\\n{A1}\\n\\nv3\\n\\n{A2,A3}\\n\\naaa v1\\n\\nv2\\n\\nv3\\n\\nv0\\n\\n{A1,A2,A3}\\n\\nbaa \\n\\nv2\\n\\n{A1,A2,A3}\\n\\nv1\\n\\n{A1,A3}\\n\\nbaa v2\\n\\nv1\\n\\nv0\\n\\nv1\\n\\nv2\\n\\nv3\\n\\naaa \\n\\nother \\n\\nbaa \\n\\nbba \\n\\naba \\n\\naab \\n\\n··· \\n\\n··· \\n\\n··· \\n\\nTwo players: Eve (light) Adam (dark) \\n\\n> 59/69\\n\\n## Construction of the suspect game abstraction \\n\\nv0\\n\\n{A1,A2,A3}\\n\\nv0\\n\\n{A1,A2,A3}\\n\\naaa \\n\\nv1\\n\\n{A1,A2,A3}\\n\\nv2\\n\\n{A1}\\n\\nv3\\n\\n{A2,A3}\\n\\naaa v1\\n\\nv2\\n\\nv3\\n\\nv0\\n\\n{A1,A2,A3}\\n\\nbaa \\n\\nv2\\n\\n{A1,A2,A3}\\n\\nv1\\n\\n{A1,A3}\\n\\nbaa v2\\n\\nv1\\n\\n...\\n\\n··· \\n\\n··· \\n\\n··· \\n\\n··· \\n\\n··· \\n\\nv0\\n\\nv1\\n\\nv2\\n\\nv3\\n\\naaa \\n\\nother \\n\\nbaa \\n\\nbba \\n\\naba \\n\\naab \\n\\n··· \\n\\n··· \\n\\n··· \\n\\nTwo players: Eve (light) Adam (dark) \\n\\n> 59/69\\n\\n## Correctness of the suspect game construction \\n\\nWinning condition \\n\\nA strategy ζ for Eve in the suspect game is winning for some α ∈ RAgt if the unique outcome of ζ where Adam complies to Eve has payoff α, and for every other outcome ρ of ζ, for every A ∈ susp( ρ), payoff A(ρ) ≤ αA.\\n\\nCorrectness \\n\\nLet α ∈ RAgt . There is a Nash equilibrium in the original game with payoff α if and only if Eve has a winning strategy for α in the suspect game. \\n\\n> 60/69\\n\\n## Correctness of the suspect game construction \\n\\nWinning condition \\n\\nA strategy ζ for Eve in the suspect game is winning for some α ∈ RAgt if the unique outcome of ζ where Adam complies to Eve has payoff α, and for every other outcome ρ of ζ, for every A ∈ susp( ρ), payoff A(ρ) ≤ αA.\\n\\nCorrectness \\n\\nLet α ∈ RAgt . There is a Nash equilibrium in the original game with payoff α if and only if Eve has a winning strategy for α in the suspect game. \\n\\n> 60/69\\n\\nφA: objective of player A\\n\\n> v0\\n> Agt\\n> m\\n\\nPlayers A1, A2 lose along that play \\n\\n> v\\n> {A1,A2}\\n\\nv A3 does not know whether \\n\\nA1 or A2 deviated; he should try to punish both (if A1 deviated, A2 will help A3,and conversely if A2 deviated) \\n\\neveryone knows A1 deviated; \\n\\nA2 and A3 will try to punish A1 \\n\\n> v′\\n> {A1}\\n> 61/69\\n\\n## From an algorithmic point-of-view \\n\\nIn the orange part: compute the winner (Eve or Adam) of the zero-sum game, where Eve’s objective is ¬φA1 (Eve wants to show that there is no profitable deviation for A1)\\n\\nWe remove the orange part, and replace the root vertex by a winning state for the previously computed winner \\n\\nIn the yellow part: compute the winner (Eve or Adam) of the zero-sum game, where Eve’s objective is (¬φA1 ∧ ¬ φA2 ) ∨ Reach(win Eve )where win Eve is an already computed winning state for Eve \\n\\nIt is then just a matter to find an infinite play satisfying the appropriate property \\n\\n> 62/69\\n\\n## From an algorithmic point-of-view \\n\\nIn the orange part: compute the winner (Eve or Adam) of the zero-sum game, where Eve’s objective is ¬φA1 (Eve wants to show that there is no profitable deviation for A1)\\n\\nWe remove the orange part, and replace the root vertex by a winning state for the previously computed winner \\n\\nIn the yellow part: compute the winner (Eve or Adam) of the zero-sum game, where Eve’s objective is (¬φA1 ∧ ¬ φA2 ) ∨ Reach(win Eve )where win Eve is an already computed winning state for Eve \\n\\nIt is then just a matter to find an infinite play satisfying the appropriate property \\n\\n> 62/69\\n\\n## From an algorithmic point-of-view \\n\\nIn the orange part: compute the winner (Eve or Adam) of the zero-sum game, where Eve’s objective is ¬φA1 (Eve wants to show that there is no profitable deviation for A1)\\n\\nWe remove the orange part, and replace the root vertex by a winning state for the previously computed winner \\n\\nIn the yellow part: compute the winner (Eve or Adam) of the zero-sum game, where Eve’s objective is (¬φA1 ∧ ¬ φA2 ) ∨ Reach(win Eve )where win Eve is an already computed winning state for Eve \\n\\nIt is then just a matter to find an infinite play satisfying the appropriate property \\n\\n> 62/69\\n\\n## From an algorithmic point-of-view \\n\\nIn the orange part: compute the winner (Eve or Adam) of the zero-sum game, where Eve’s objective is ¬φA1 (Eve wants to show that there is no profitable deviation for A1)\\n\\nWe remove the orange part, and replace the root vertex by a winning state for the previously computed winner \\n\\nIn the yellow part: compute the winner (Eve or Adam) of the zero-sum game, where Eve’s objective is (¬φA1 ∧ ¬ φA2 ) ∨ Reach(win Eve )where win Eve is an already computed winning state for Eve \\n\\nIt is then just a matter to find an infinite play satisfying the appropriate property \\n\\n> 62/69\\n\\n## From an algorithmic point-of-view \\n\\nIn the orange part: compute the winner (Eve or Adam) of the zero-sum game, where Eve’s objective is ¬φA1 (Eve wants to show that there is no profitable deviation for A1)\\n\\nWe remove the orange part, and replace the root vertex by a winning state for the previously computed winner \\n\\nIn the yellow part: compute the winner (Eve or Adam) of the zero-sum game, where Eve’s objective is (¬φA1 ∧ ¬ φA2 ) ∨ Reach(win Eve )where win Eve is an already computed winning state for Eve \\n\\nIt is then just a matter to find an infinite play satisfying the appropriate property \\n\\n> 62/69\\n\\n## From an algorithmic point-of-view \\n\\nIn the orange part: compute the winner (Eve or Adam) of the zero-sum game, where Eve’s objective is ¬φA1 (Eve wants to show that there is no profitable deviation for A1)\\n\\nWe remove the orange part, and replace the root vertex by a winning state for the previously computed winner \\n\\nIn the yellow part: compute the winner (Eve or Adam) of the zero-sum game, where Eve’s objective is (¬φA1 ∧ ¬ φA2 ) ∨ Reach(win Eve )where win Eve is an already computed winning state for Eve \\n\\nIt is then just a matter to find an infinite play satisfying the appropriate property \\n\\nThe approach can be extended to various settings! \\n\\n> 62/69\\n\\n## Some results  \\n\\n> [BBMU15] Bouyer, Brenguier, Markey, Ummels. Pure Nash equilibria in concurrent deterministic games (LMCS)\\n\\nExamples of complexity results \\n\\nFor single objectives: \\n\\nObjectives Reach. Safety B¨ uchi co-B¨ uchi Parity \\n\\nComplexity NP-c. P-c. NP-c. PNP \\n\\n‖ -c. \\n\\nFor combinations of B¨ uchi objectives: \\n\\nCombinations Subset Lexico. Count. Bool. circuit \\n\\nComplexity P-c. NP-.c PSPACE-c. \\n\\nFor combinations of reachability objectives: \\n\\nCombinations Subset Lexico. Count. Bool. circuit \\n\\nComplexity NP-c. PSPACE-.c \\n\\n> 63/69\\n\\n## Some results  \\n\\n> [BBMU15] Bouyer, Brenguier, Markey, Ummels. Pure Nash equilibria in concurrent deterministic games (LMCS)\\n\\nExamples of complexity results \\n\\nFor single objectives: \\n\\nObjectives Reach. Safety B¨ uchi co-B¨ uchi Parity \\n\\nComplexity NP-c. P-c. NP-c. PNP \\n\\n‖ -c. \\n\\nFor combinations of B¨ uchi objectives: \\n\\nCombinations Subset Lexico. Count. Bool. circuit \\n\\nComplexity P-c. NP-.c PSPACE-c. \\n\\nFor combinations of reachability objectives: \\n\\nCombinations Subset Lexico. Count. Bool. circuit \\n\\nComplexity NP-c. PSPACE-.c \\n\\n> 63/69\\n\\n## Some results  \\n\\n> [BBMU15] Bouyer, Brenguier, Markey, Ummels. Pure Nash equilibria in concurrent deterministic games (LMCS)\\n\\nExamples of complexity results \\n\\nFor single objectives: \\n\\nObjectives Reach. Safety B¨ uchi co-B¨ uchi Parity \\n\\nComplexity NP-c. P-c. NP-c. PNP \\n\\n‖ -c. \\n\\nFor combinations of B¨ uchi objectives: \\n\\nCombinations Subset Lexico. Count. Bool. circuit \\n\\nComplexity P-c. NP-.c PSPACE-c. \\n\\nFor combinations of reachability objectives: \\n\\nCombinations Subset Lexico. Count. Bool. circuit \\n\\nComplexity NP-c. PSPACE-.c \\n\\n> 63/69\\n\\n## Extensions of this approach \\n\\n[Bou18] Bouyer. Games on graphs with a public signal monitoring (FoSSaCS’18) .[BT19] Bouyer, Tomasset. Nash equilibria in games over graphs equipped with a communication mechanism (MFCS’19) .[Bre16] Brenguier. Robust equilibria in mean-payoff games (FoSSaCS’16) .[COT18] Condurache, Oualhadj, Troquard. The complexity of rational synthesis for concurrent games (CONCUR’18) .\\n\\nPartial information monitoring \\n\\nPublic signal [Bou18] \\n\\nCommunication graphs [BT19] \\n\\n64/69 Extensions of this approach \\n\\n[Bou18] Bouyer. Games on graphs with a public signal monitoring (FoSSaCS’18) .[BT19] Bouyer, Tomasset. Nash equilibria in games over graphs equipped with a communication mechanism (MFCS’19) .[Bre16] Brenguier. Robust equilibria in mean-payoff games (FoSSaCS’16) .[COT18] Condurache, Oualhadj, Troquard. The complexity of rational synthesis for concurrent games (CONCUR’18) .\\n\\nPartial information monitoring \\n\\nPublic signal [Bou18] \\n\\nCommunication graphs [BT19] \\n\\nOther solution concepts \\n\\nRobust equilibria [Bre16] \\n\\nRational synthesis [COT18] \\n\\n64/69 Outline  \\n\\n> 1\\n\\nVerification and game theory  \\n\\n> 2\\n\\nWhat is a game?  \\n\\n> 3\\n\\nA glimpse on strategic games  \\n\\n> 4\\n\\nGames on graphs \\n\\nThe general model \\n\\nFocus on a simple scenario \\n\\nAdding probabilities to the setting? \\n\\nConcurrent games  \\n\\n> 5\\n\\nConclusion \\n\\n> 65/69\\n\\n## Wrap-up \\n\\nGeneral objective \\n\\nImport game theory solutions to the verification field, where interactivity plays also a role \\n\\nEx: Distributed systems interacting in some environment \\n\\n> 66/69\\n\\n## Applications? \\n\\n[BDGHM16] Brihaye, Dhar, Geeraerts, Haddad, Monmege. Efficient energy distribution in a smart grid using multi-player games (Cassting’16) \\n\\n[KNPS19] Kwiatkowska, Norman, Parker, Santos. Equilibria-based probabilistic model checking for concurrent stochastic games (FM’19) .[GBLM19] Gonz´ alez, Bouyer, Lasaulce, Markey. Optimisation en pr´ esence de contraintes en probabilit´ es et processus markoviens contrˆ ol´ es \\n\\n(GRETSI’19) \\n\\nSmart grids: decentralized control of EV charging [GBLM19] \\n\\nstochastic setting \\n\\nad-hoc approximated solutions \\n\\nCassting project: smart houses that produce energy with solar panels [BDGHM16] \\n\\ndeterministic setting \\n\\nsetting with universal existence \\n\\nexact computation \\n\\nPRISM-games: medium access control, Aloha protocol, robot coordination, power control [KNPS19] \\n\\nstochastic setting \\n\\napproximated value iteration for computing \\u000f-SPE \\n\\n67/69 Wrap-up \\n\\nGeneral objective \\n\\nImport game theory solutions to the verification field, where interactivity plays also a role \\n\\nEx: Distributed systems interacting in some environment \\n\\nRelevant questions: \\n\\nassumptions made in the game theory field relevant? \\n\\nsolution concepts adapted to the context? \\n\\n> 68/69\\n\\n## Wrap-up \\n\\nGeneral objective \\n\\nImport game theory solutions to the verification field, where interactivity plays also a role \\n\\nEx: Distributed systems interacting in some environment \\n\\nRelevant questions: \\n\\nassumptions made in the game theory field relevant? \\n\\nsolution concepts adapted to the context? \\n\\nNash equilibria in games on graphs \\n\\nThe setting of pure Nash equilibria in turn-based det. games rather well-understood \\n\\nProbabilistic setting much more complicated \\n\\nConcurrent games: a rather generic approach based on the suspect game construction \\n\\n> 68/69\\n\\n## Going further? \\n\\nMore relevant solution concepts? \\n\\nTemporal aspects weakens the concept of Nash equilibrium: \\n\\nWill a rational agent/process focus on punishing a deviator, instead of pursuing her own objective? \\n\\nAnother solution concept: subgame-perfect equilibrium\"\n", "}\n"]}], "source": ["print(json.dumps(search_agent.url_content_cache, indent=2, ensure_ascii=False))"]}], "metadata": {"kernelspec": {"display_name": "ia-gestorcontenidosiagen-be", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}