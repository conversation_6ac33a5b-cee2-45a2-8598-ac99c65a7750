{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "import sys\n", "\n", "parent_dir = os.path.dirname(os.getcwd())\n", "sys.path.append(parent_dir)\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv()"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import logging\n", "\n", "logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2024-07-22 14:59:06,701 - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False\n", "2024-07-22 14:59:06,702 - DEBUG - load_verify_locations cafile='/usr/lib/ssl/certs/ca-certificates.crt'\n", "2024-07-22 14:59:06,719 - DEBUG - Patching `client.chat.completions.create` with mode=<Mode.TOOLS: 'tool_call'>\n", "/home/<USER>/mpu/cont_gen_poc/venv/lib/python3.11/site-packages/sentence_transformers/cross_encoder/CrossEncoder.py:11: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from tqdm.autonotebook import tqdm, trange\n"]}], "source": ["from src.api.workflows.document_ingestion import (\n", "    SmartSemanticChunker,\n", ")\n", "from src.api.workflows.document_ingestion.utils import Lenguaje"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can use openai or local embeddings of huggingface"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Language Lenguaje.EN\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2024-07-22 14:59:08,938 - INFO - Use pytorch device_name: cuda\n", "2024-07-22 14:59:08,938 - INFO - Load pretrained SentenceTransformer: BAAI/bge-m3\n", "2024-07-22 14:59:08,939 - DEBUG - Starting new HTTPS connection (1): huggingface.co:443\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Creating new Embedding instance\n", "Using device: cuda\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2024-07-22 14:59:09,296 - DEBUG - https://huggingface.co:443 \"HEAD /BAAI/bge-m3/resolve/main/modules.json HTTP/1.1\" 200 0\n", "2024-07-22 14:59:09,427 - DEBUG - https://huggingface.co:443 \"HEAD /BAAI/bge-m3/resolve/main/config_sentence_transformers.json HTTP/1.1\" 200 0\n", "2024-07-22 14:59:09,545 - DEBUG - https://huggingface.co:443 \"HEAD /BAAI/bge-m3/resolve/main/README.md HTTP/1.1\" 200 0\n", "2024-07-22 14:59:09,660 - DEBUG - https://huggingface.co:443 \"HEAD /BAAI/bge-m3/resolve/main/modules.json HTTP/1.1\" 200 0\n", "2024-07-22 14:59:09,774 - DEBUG - https://huggingface.co:443 \"HEAD /BAAI/bge-m3/resolve/main/sentence_bert_config.json HTTP/1.1\" 200 0\n", "2024-07-22 14:59:09,887 - DEBUG - https://huggingface.co:443 \"HEAD /BAAI/bge-m3/resolve/main/config.json HTTP/1.1\" 200 0\n", "2024-07-22 14:59:10,063 - DEBUG - https://huggingface.co:443 \"HEAD /BAAI/bge-m3/resolve/main/model.safetensors HTTP/1.1\" 404 0\n", "2024-07-22 14:59:10,494 - DEBUG - https://huggingface.co:443 \"HEAD /BAAI/bge-m3/resolve/main/tokenizer_config.json HTTP/1.1\" 200 0\n", "2024-07-22 14:59:11,544 - DEBUG - https://huggingface.co:443 \"GET /api/models/BAAI/bge-m3/revision/main HTTP/1.1\" 200 3557\n", "2024-07-22 14:59:11,670 - DEBUG - https://huggingface.co:443 \"GET /api/models/BAAI/bge-m3 HTTP/1.1\" 200 3557\n"]}], "source": ["smart_chunker = SmartSemanticChunker(language=Lenguaje.EN, threshold=0.4, min_chunk_size=200)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2024-07-22 14:59:13,291 - DEBUG - Split text into 756 sentences\n", "2024-07-22 14:59:13,292 - DEB<PERSON>G - Sentence 0: 'What I Worked On\n", "\n", "February 2021\n", "\n", "Before college th...' (129 chars)\n", "2024-07-22 14:59:13,292 - DEBUG - Sentence 1: 'I didn't write essays....' (22 chars)\n", "2024-07-22 14:59:13,292 - DEBUG - Sentence 2: 'I wrote what beginning writers were supposed to wr...' (98 chars)\n", "2024-07-22 14:59:13,293 - DEBUG - Sentence 3: 'My stories were awful....' (22 chars)\n", "2024-07-22 14:59:13,293 - DEBUG - Sentence 4: 'They had hardly any plot, just characters with str...' (98 chars)\n", "2024-07-22 14:59:13,293 - DEBUG - Sentence 5: 'The first programs I tried writing were on the IBM...' (129 chars)\n", "2024-07-22 14:59:13,293 - <PERSON>B<PERSON><PERSON> - Sen<PERSON><PERSON> 6: 'This was in 9th grade, so I was 13 or 14....' (41 chars)\n", "2024-07-22 14:59:13,294 - DEBUG - Sentence 7: 'The school district's 1401 happened to be in the b...' (142 chars)\n", "2024-07-22 14:59:13,294 - DEBUG - Sentence 8: 'It was like a mini Bond villain's lair down there,...' (195 chars)\n", "2024-07-22 14:59:13,294 - DEBUG - Sentence 9: 'The language we used was an early version of Fortr...' (53 chars)\n", "Batches: 100%|██████████| 4/4 [00:00<00:00,  6.26it/s]\n", "Batches: 100%|██████████| 4/4 [00:00<00:00, 18.26it/s]\n", "Batches: 100%|██████████| 4/4 [00:00<00:00, 25.91it/s]\n", "Batches: 100%|██████████| 4/4 [00:00<00:00, 27.11it/s]\n", "Batches: 100%|██████████| 4/4 [00:00<00:00, 26.58it/s]\n", "Batches: 100%|██████████| 4/4 [00:00<00:00, 26.62it/s]\n", "Batches: 100%|██████████| 4/4 [00:00<00:00, 27.72it/s]\n", "Batches: 100%|██████████| 2/2 [00:00<00:00, 28.45it/s]\n", "2024-07-22 14:59:15,062 - DEBUG - <PERSON><PERSON> counts for first 10 sentences: [30  6 19  5 19 27 17 33 41 12]\n", "2024-07-22 14:59:15,063 - DEBUG - Created 756 Sentence objects\n", "2024-07-22 14:59:15,064 - DEBUG - Starting _cluster_recursively at depth 0 with 756 sentences\n", "2024-07-22 14:59:15,067 - DEBUG - Sentence 9: tokens=209, similarity=0.47044105531056113, threshold=0.4\n", "2024-07-22 14:59:15,068 - DEBUG - Continuing cluster at sentence 9\n", "2024-07-22 14:59:15,068 - DEBUG - Sentence 10: tokens=239, similarity=0.5120759729287263, threshold=0.4\n", "2024-07-22 14:59:15,068 - DEBUG - Continuing cluster at sentence 10\n", "2024-07-22 14:59:15,069 - DEBUG - Sentence 11: tokens=254, similarity=0.45499206879650134, threshold=0.4\n", "2024-07-22 14:59:15,069 - DEBUG - Continuing cluster at sentence 11\n", "2024-07-22 14:59:15,070 - DEBUG - Sentence 12: tokens=263, similarity=0.6242876388227081, threshold=0.4\n", "2024-07-22 14:59:15,070 - DEBUG - Continuing cluster at sentence 12\n", "2024-07-22 14:59:15,070 - DEBUG - Sentence 13: tokens=274, similarity=0.75391095350909, threshold=0.4\n", "2024-07-22 14:59:15,071 - DEBUG - Continuing cluster at sentence 13\n", "2024-07-22 14:59:15,071 - DEBUG - Sentence 14: tokens=288, similarity=0.5894357770192281, threshold=0.4\n", "2024-07-22 14:59:15,071 - DEBUG - Continuing cluster at sentence 14\n", "2024-07-22 14:59:15,072 - DEBUG - Sentence 15: tokens=314, similarity=0.6403208693188867, threshold=0.4\n", "2024-07-22 14:59:15,072 - DEBUG - Continuing cluster at sentence 15\n", "2024-07-22 14:59:15,072 - DEBUG - Sentence 16: tokens=352, similarity=0.6076854699203211, threshold=0.4\n", "2024-07-22 14:59:15,073 - DEBUG - Continuing cluster at sentence 16\n", "2024-07-22 14:59:15,073 - DEBUG - Sentence 17: tokens=374, similarity=0.5967970985396707, threshold=0.4\n", "2024-07-22 14:59:15,074 - DEBUG - Continuing cluster at sentence 17\n", "2024-07-22 14:59:15,074 - DEBUG - Sentence 18: tokens=400, similarity=0.48986647082800655, threshold=0.4\n", "2024-07-22 14:59:15,074 - DEBUG - Continuing cluster at sentence 18\n", "2024-07-22 14:59:15,075 - DEBUG - Sentence 19: tokens=428, similarity=0.4968226888758696, threshold=0.4\n", "2024-07-22 14:59:15,075 - DEBUG - Continuing cluster at sentence 19\n", "2024-07-22 14:59:15,075 - DEBUG - Sentence 20: tokens=436, similarity=0.5854257474747928, threshold=0.4\n", "2024-07-22 14:59:15,076 - DEBUG - Continuing cluster at sentence 20\n", "2024-07-22 14:59:15,076 - DEBUG - Sentence 21: tokens=479, similarity=0.5253585285078334, threshold=0.4\n", "2024-07-22 14:59:15,076 - DEBUG - Continuing cluster at sentence 21\n", "2024-07-22 14:59:15,079 - DEBUG - Sentence 22: tokens=496, similarity=0.4936131609112875, threshold=0.4\n", "2024-07-22 14:59:15,079 - DEBUG - Continuing cluster at sentence 22\n", "2024-07-22 14:59:15,079 - DEBUG - Sentence 23: tokens=506, similarity=0.413448470322488, threshold=0.4\n", "2024-07-22 14:59:15,080 - DEBUG - Continuing cluster at sentence 23\n", "2024-07-22 14:59:15,080 - DEBUG - Sentence 24: tokens=532, similarity=0.559879542914294, threshold=0.4\n", "2024-07-22 14:59:15,081 - DEBUG - Continuing cluster at sentence 24\n", "2024-07-22 14:59:15,081 - DEBUG - Sentence 25: tokens=568, similarity=0.6631631849940047, threshold=0.4\n", "2024-07-22 14:59:15,081 - DEBUG - Continuing cluster at sentence 25\n", "2024-07-22 14:59:15,082 - DEBUG - Sentence 26: tokens=587, similarity=0.44248285016946254, threshold=0.4\n", "2024-07-22 14:59:15,082 - DEBUG - Continuing cluster at sentence 26\n", "2024-07-22 14:59:15,082 - DEBUG - Sentence 27: tokens=595, similarity=0.5749301611270242, threshold=0.4\n", "2024-07-22 14:59:15,083 - DEBUG - Continuing cluster at sentence 27\n", "2024-07-22 14:59:15,083 - DEBUG - Sentence 28: tokens=627, similarity=0.5773141893766249, threshold=0.4\n", "2024-07-22 14:59:15,083 - DEBUG - Cluster exceeds max size (627 > 600), splitting\n", "2024-07-22 14:59:15,084 - DEBUG - Split result: 29 sentences in new cluster, 0 sentences remaining\n", "2024-07-22 14:59:15,084 - DEBUG - Sentence 37: tokens=200, similarity=0.5691287131396083, threshold=0.4\n", "2024-07-22 14:59:15,084 - DEBUG - Continuing cluster at sentence 37\n", "2024-07-22 14:59:15,085 - DEBUG - Sentence 38: tokens=264, similarity=0.5582632560895249, threshold=0.4\n", "2024-07-22 14:59:15,085 - DEBUG - Continuing cluster at sentence 38\n", "2024-07-22 14:59:15,086 - DEBUG - Sentence 39: tokens=303, similarity=0.4229181653822456, threshold=0.4\n", "2024-07-22 14:59:15,086 - DEBUG - Continuing cluster at sentence 39\n", "2024-07-22 14:59:15,086 - DEBUG - Sentence 40: tokens=340, similarity=0.6025485358741994, threshold=0.4\n", "2024-07-22 14:59:15,087 - DEBUG - Continuing cluster at sentence 40\n", "2024-07-22 14:59:15,087 - DEBUG - Sentence 41: tokens=353, similarity=0.48451004581873364, threshold=0.4\n", "2024-07-22 14:59:15,087 - DEBUG - Continuing cluster at sentence 41\n", "2024-07-22 14:59:15,088 - DEBUG - Sentence 42: tokens=377, similarity=0.5377874979171473, threshold=0.4\n", "2024-07-22 14:59:15,088 - DEBUG - Continuing cluster at sentence 42\n", "2024-07-22 14:59:15,088 - DEBUG - Sentence 43: tokens=395, similarity=0.5654851647716791, threshold=0.4\n", "2024-07-22 14:59:15,089 - DEBUG - Continuing cluster at sentence 43\n", "2024-07-22 14:59:15,089 - DEBUG - Sentence 44: tokens=413, similarity=0.5968537900328071, threshold=0.4\n", "2024-07-22 14:59:15,090 - DEBUG - Continuing cluster at sentence 44\n", "2024-07-22 14:59:15,090 - DEBUG - Sentence 45: tokens=434, similarity=0.48821312285763085, threshold=0.4\n", "2024-07-22 14:59:15,090 - DEBUG - Continuing cluster at sentence 45\n", "2024-07-22 14:59:15,091 - DEBUG - Sentence 46: tokens=462, similarity=0.5070312025304812, threshold=0.4\n", "2024-07-22 14:59:15,091 - DEBUG - Continuing cluster at sentence 46\n", "2024-07-22 14:59:15,091 - DEBUG - Sentence 47: tokens=478, similarity=0.6482798950498891, threshold=0.4\n", "2024-07-22 14:59:15,092 - DEBUG - Continuing cluster at sentence 47\n", "2024-07-22 14:59:15,092 - DEBUG - Sentence 48: tokens=497, similarity=0.5373664028956546, threshold=0.4\n", "2024-07-22 14:59:15,092 - DEBUG - Continuing cluster at sentence 48\n", "2024-07-22 14:59:15,093 - DEBUG - Sentence 49: tokens=508, similarity=0.6789716263223855, threshold=0.4\n", "2024-07-22 14:59:15,093 - DEBUG - Continuing cluster at sentence 49\n", "2024-07-22 14:59:15,093 - DEBUG - Sentence 50: tokens=517, similarity=0.4726177168938529, threshold=0.4\n", "2024-07-22 14:59:15,094 - DEBUG - Continuing cluster at sentence 50\n", "2024-07-22 14:59:15,094 - DEBUG - Sentence 51: tokens=530, similarity=0.46690145112813114, threshold=0.4\n", "2024-07-22 14:59:15,095 - DEBUG - Continuing cluster at sentence 51\n", "2024-07-22 14:59:15,095 - DEBUG - Sentence 52: tokens=540, similarity=0.5424067874223016, threshold=0.4\n", "2024-07-22 14:59:15,095 - DEBUG - Continuing cluster at sentence 52\n", "2024-07-22 14:59:15,096 - DEBUG - Sentence 53: tokens=583, similarity=0.4600055806585759, threshold=0.4\n", "2024-07-22 14:59:15,096 - DEBUG - Continuing cluster at sentence 53\n", "2024-07-22 14:59:15,096 - DEBUG - Sentence 54: tokens=600, similarity=0.557783935586259, threshold=0.4\n", "2024-07-22 14:59:15,097 - DEBUG - Continuing cluster at sentence 54\n", "2024-07-22 14:59:15,097 - DEBUG - Sentence 55: tokens=619, similarity=0.4983894249933992, threshold=0.4\n", "2024-07-22 14:59:15,097 - DEBUG - Cluster exceeds max size (619 > 600), splitting\n", "2024-07-22 14:59:15,098 - DEBUG - Split result: 26 sentences in new cluster, 1 sentences remaining\n", "2024-07-22 14:59:15,098 - DEBUG - Sentence 62: tokens=234, similarity=0.5747544749501252, threshold=0.4\n", "2024-07-22 14:59:15,099 - DEBUG - Continuing cluster at sentence 62\n", "2024-07-22 14:59:15,099 - DEBUG - Sentence 63: tokens=254, similarity=0.5945699400846338, threshold=0.4\n", "2024-07-22 14:59:15,099 - DEBUG - Continuing cluster at sentence 63\n", "2024-07-22 14:59:15,100 - DEBUG - Sentence 64: tokens=260, similarity=0.4571773743292977, threshold=0.4\n", "2024-07-22 14:59:15,100 - DEBUG - Continuing cluster at sentence 64\n", "2024-07-22 14:59:15,100 - DEBUG - Sentence 65: tokens=282, similarity=0.569408004453031, threshold=0.4\n", "2024-07-22 14:59:15,101 - DEBUG - Continuing cluster at sentence 65\n", "2024-07-22 14:59:15,101 - DEBUG - Sentence 66: tokens=300, similarity=0.5170946862607545, threshold=0.4\n", "2024-07-22 14:59:15,101 - DEBUG - Continuing cluster at sentence 66\n", "2024-07-22 14:59:15,102 - DEBUG - Sentence 67: tokens=320, similarity=0.4773265351936896, threshold=0.4\n", "2024-07-22 14:59:15,102 - DEBUG - Continuing cluster at sentence 67\n", "2024-07-22 14:59:15,102 - DEBUG - Sentence 68: tokens=360, similarity=0.4717246350837655, threshold=0.4\n", "2024-07-22 14:59:15,103 - DEBUG - Continuing cluster at sentence 68\n", "2024-07-22 14:59:15,103 - DEBUG - Sentence 69: tokens=382, similarity=0.65581806285654, threshold=0.4\n", "2024-07-22 14:59:15,103 - DEBUG - Continuing cluster at sentence 69\n", "2024-07-22 14:59:15,104 - DEBUG - Sentence 70: tokens=418, similarity=0.6792720748403784, threshold=0.4\n", "2024-07-22 14:59:15,104 - DEBUG - Continuing cluster at sentence 70\n", "2024-07-22 14:59:15,105 - DEBUG - Sentence 71: tokens=426, similarity=0.7880282056593526, threshold=0.4\n", "2024-07-22 14:59:15,105 - DEBUG - Continuing cluster at sentence 71\n", "2024-07-22 14:59:15,105 - DEBUG - Sentence 72: tokens=439, similarity=0.7883068825845063, threshold=0.4\n", "2024-07-22 14:59:15,106 - DEBUG - Continuing cluster at sentence 72\n", "2024-07-22 14:59:15,106 - DEBUG - Sentence 73: tokens=458, similarity=0.5767383067266862, threshold=0.4\n", "2024-07-22 14:59:15,106 - DEBUG - Continuing cluster at sentence 73\n", "2024-07-22 14:59:15,107 - DEBUG - Sentence 74: tokens=474, similarity=0.491152106192235, threshold=0.4\n", "2024-07-22 14:59:15,107 - DEBUG - Continuing cluster at sentence 74\n", "2024-07-22 14:59:15,107 - DEBUG - Sentence 75: tokens=498, similarity=0.3881269883115197, threshold=0.4\n", "2024-07-22 14:59:15,108 - DEBUG - Appending cluster with 21 sentences, 498 tokens\n", "2024-07-22 14:59:15,108 - DEBUG - Sentence 87: tokens=205, similarity=0.47640721827937826, threshold=0.4\n", "2024-07-22 14:59:15,108 - DEBUG - Continuing cluster at sentence 87\n", "2024-07-22 14:59:15,109 - DEBUG - Sentence 88: tokens=217, similarity=0.537567375833435, threshold=0.4\n", "2024-07-22 14:59:15,109 - DEBUG - Continuing cluster at sentence 88\n", "2024-07-22 14:59:15,109 - DEBUG - Sentence 89: tokens=225, similarity=0.5258249523386224, threshold=0.4\n", "2024-07-22 14:59:15,110 - DEBUG - Continuing cluster at sentence 89\n", "2024-07-22 14:59:15,110 - DEBUG - Sentence 90: tokens=241, similarity=0.4194392723726289, threshold=0.4\n", "2024-07-22 14:59:15,110 - DEBUG - Continuing cluster at sentence 90\n", "2024-07-22 14:59:15,111 - DEBUG - Sentence 91: tokens=268, similarity=0.5796823103340298, threshold=0.4\n", "2024-07-22 14:59:15,111 - DEBUG - Continuing cluster at sentence 91\n", "2024-07-22 14:59:15,112 - DEBUG - Sentence 92: tokens=290, similarity=0.5837428551147696, threshold=0.4\n", "2024-07-22 14:59:15,112 - DEBUG - Continuing cluster at sentence 92\n", "2024-07-22 14:59:15,112 - DEBUG - Sentence 93: tokens=312, similarity=0.6030428140026747, threshold=0.4\n", "2024-07-22 14:59:15,113 - DEBUG - Continuing cluster at sentence 93\n", "2024-07-22 14:59:15,113 - DEBUG - Sentence 94: tokens=328, similarity=0.5462764212158815, threshold=0.4\n", "2024-07-22 14:59:15,113 - DEBUG - Continuing cluster at sentence 94\n", "2024-07-22 14:59:15,114 - DEBUG - Sentence 95: tokens=335, similarity=0.5844853084561312, threshold=0.4\n", "2024-07-22 14:59:15,114 - DEBUG - Continuing cluster at sentence 95\n", "2024-07-22 14:59:15,114 - DEBUG - Sentence 96: tokens=346, similarity=0.5585529256946653, threshold=0.4\n", "2024-07-22 14:59:15,115 - DEBUG - Continuing cluster at sentence 96\n", "2024-07-22 14:59:15,115 - DEBUG - Sentence 97: tokens=358, similarity=0.6679432262748861, threshold=0.4\n", "2024-07-22 14:59:15,115 - DEBUG - Continuing cluster at sentence 97\n", "2024-07-22 14:59:15,116 - DEBUG - Sentence 98: tokens=397, similarity=0.5792186367104004, threshold=0.4\n", "2024-07-22 14:59:15,116 - DEBUG - Continuing cluster at sentence 98\n", "2024-07-22 14:59:15,116 - DEBUG - Sentence 99: tokens=407, similarity=0.561730033665869, threshold=0.4\n", "2024-07-22 14:59:15,117 - DEBUG - Continuing cluster at sentence 99\n", "2024-07-22 14:59:15,117 - DEBUG - Sentence 100: tokens=422, similarity=0.33308250554614904, threshold=0.4\n", "2024-07-22 14:59:15,118 - DEBUG - Appending cluster with 25 sentences, 422 tokens\n", "2024-07-22 14:59:15,118 - DEBUG - Sentence 112: tokens=208, similarity=0.45652112367318637, threshold=0.4\n", "2024-07-22 14:59:15,118 - DEBUG - Continuing cluster at sentence 112\n", "2024-07-22 14:59:15,119 - DEBUG - Sentence 113: tokens=219, similarity=0.5599933782205968, threshold=0.4\n", "2024-07-22 14:59:15,119 - DEBUG - Continuing cluster at sentence 113\n", "2024-07-22 14:59:15,119 - DEBUG - Sentence 114: tokens=240, similarity=0.5569377107903979, threshold=0.4\n", "2024-07-22 14:59:15,120 - DEBUG - Continuing cluster at sentence 114\n", "2024-07-22 14:59:15,120 - DEBUG - Sentence 115: tokens=281, similarity=0.46523870566244574, threshold=0.4\n", "2024-07-22 14:59:15,120 - DEBUG - Continuing cluster at sentence 115\n", "2024-07-22 14:59:15,121 - DEBUG - Sentence 116: tokens=296, similarity=0.44658255335399216, threshold=0.4\n", "2024-07-22 14:59:15,121 - DEBUG - Continuing cluster at sentence 116\n", "2024-07-22 14:59:15,121 - DEBUG - Sentence 117: tokens=316, similarity=0.5067550259301854, threshold=0.4\n", "2024-07-22 14:59:15,122 - DEBUG - Continuing cluster at sentence 117\n", "2024-07-22 14:59:15,122 - DEBUG - Sentence 118: tokens=393, similarity=0.5757809735275574, threshold=0.4\n", "2024-07-22 14:59:15,122 - DEBUG - Continuing cluster at sentence 118\n", "2024-07-22 14:59:15,123 - DEBUG - Sentence 119: tokens=405, similarity=0.6128143056223145, threshold=0.4\n", "2024-07-22 14:59:15,123 - DEBUG - Continuing cluster at sentence 119\n", "2024-07-22 14:59:15,123 - DEBUG - Sentence 120: tokens=416, similarity=0.6102603578413335, threshold=0.4\n", "2024-07-22 14:59:15,124 - DEBUG - Continuing cluster at sentence 120\n", "2024-07-22 14:59:15,124 - DEBUG - Sentence 121: tokens=428, similarity=0.4356303777758476, threshold=0.4\n", "2024-07-22 14:59:15,125 - DEBUG - Continuing cluster at sentence 121\n", "2024-07-22 14:59:15,125 - DEBUG - Sentence 122: tokens=440, similarity=0.4397218118952466, threshold=0.4\n", "2024-07-22 14:59:15,125 - DEBUG - Continuing cluster at sentence 122\n", "2024-07-22 14:59:15,126 - DEBUG - Sentence 123: tokens=463, similarity=0.5792570907893411, threshold=0.4\n", "2024-07-22 14:59:15,126 - DEBUG - Continuing cluster at sentence 123\n", "2024-07-22 14:59:15,126 - DEBUG - Sentence 124: tokens=471, similarity=0.7161310333724858, threshold=0.4\n", "2024-07-22 14:59:15,127 - DEBUG - Continuing cluster at sentence 124\n", "2024-07-22 14:59:15,127 - DEBUG - Sentence 125: tokens=512, similarity=0.5907714064446823, threshold=0.4\n", "2024-07-22 14:59:15,127 - DEBUG - Continuing cluster at sentence 125\n", "2024-07-22 14:59:15,128 - DEBUG - Sentence 126: tokens=536, similarity=0.643567751642017, threshold=0.4\n", "2024-07-22 14:59:15,128 - DEBUG - Continuing cluster at sentence 126\n", "2024-07-22 14:59:15,129 - DEBUG - Sentence 127: tokens=562, similarity=0.43481026599139694, threshold=0.4\n", "2024-07-22 14:59:15,129 - DEBUG - Continuing cluster at sentence 127\n", "2024-07-22 14:59:15,129 - DEBUG - Sentence 128: tokens=589, similarity=0.4514667048115914, threshold=0.4\n", "2024-07-22 14:59:15,130 - DEBUG - Continuing cluster at sentence 128\n", "2024-07-22 14:59:15,130 - DEBUG - Sentence 129: tokens=609, similarity=0.5356248773742643, threshold=0.4\n", "2024-07-22 14:59:15,130 - DEBUG - Cluster exceeds max size (609 > 600), splitting\n", "2024-07-22 14:59:15,131 - DEBUG - Split result: 28 sentences in new cluster, 1 sentences remaining\n", "2024-07-22 14:59:15,131 - DEBUG - Sentence 137: tokens=207, similarity=0.4252059865555823, threshold=0.4\n", "2024-07-22 14:59:15,131 - DEBUG - Continuing cluster at sentence 137\n", "2024-07-22 14:59:15,132 - DEBUG - Sentence 138: tokens=240, similarity=0.5589260489728909, threshold=0.4\n", "2024-07-22 14:59:15,132 - DEBUG - Continuing cluster at sentence 138\n", "2024-07-22 14:59:15,132 - DEBUG - Sentence 139: tokens=278, similarity=0.46054454560754743, threshold=0.4\n", "2024-07-22 14:59:15,133 - DEBUG - Continuing cluster at sentence 139\n", "2024-07-22 14:59:15,133 - DEBUG - Sentence 140: tokens=297, similarity=0.5130670871688627, threshold=0.4\n", "2024-07-22 14:59:15,133 - DEBUG - Continuing cluster at sentence 140\n", "2024-07-22 14:59:15,134 - DEBUG - Sentence 141: tokens=327, similarity=0.457428955757194, threshold=0.4\n", "2024-07-22 14:59:15,134 - DEBUG - Continuing cluster at sentence 141\n", "2024-07-22 14:59:15,135 - DEBUG - Sentence 142: tokens=384, similarity=0.4893568700302878, threshold=0.4\n", "2024-07-22 14:59:15,135 - DEBUG - Continuing cluster at sentence 142\n", "2024-07-22 14:59:15,135 - DEBUG - Sentence 143: tokens=407, similarity=0.543504238320029, threshold=0.4\n", "2024-07-22 14:59:15,136 - DEBUG - Continuing cluster at sentence 143\n", "2024-07-22 14:59:15,136 - DEBUG - Sentence 144: tokens=448, similarity=0.49170882156671547, threshold=0.4\n", "2024-07-22 14:59:15,136 - DEBUG - Continuing cluster at sentence 144\n", "2024-07-22 14:59:15,137 - DEBUG - Sentence 145: tokens=457, similarity=0.4673259170528394, threshold=0.4\n", "2024-07-22 14:59:15,137 - DEBUG - Continuing cluster at sentence 145\n", "2024-07-22 14:59:15,137 - DEBUG - Sentence 146: tokens=481, similarity=0.5035834074230916, threshold=0.4\n", "2024-07-22 14:59:15,137 - DEBUG - Continuing cluster at sentence 146\n", "2024-07-22 14:59:15,138 - DEBUG - Sentence 147: tokens=494, similarity=0.597433904165806, threshold=0.4\n", "2024-07-22 14:59:15,138 - DEBUG - Continuing cluster at sentence 147\n", "2024-07-22 14:59:15,138 - DEBUG - Sentence 148: tokens=513, similarity=0.6195602287233466, threshold=0.4\n", "2024-07-22 14:59:15,139 - DEBUG - Continuing cluster at sentence 148\n", "2024-07-22 14:59:15,139 - DEBUG - Sentence 149: tokens=542, similarity=0.47082063600261753, threshold=0.4\n", "2024-07-22 14:59:15,139 - DEBUG - Continuing cluster at sentence 149\n", "2024-07-22 14:59:15,139 - DEBUG - Sentence 150: tokens=566, similarity=0.5927651779406888, threshold=0.4\n", "2024-07-22 14:59:15,140 - DEBUG - Continuing cluster at sentence 150\n", "2024-07-22 14:59:15,140 - DEBUG - Sentence 151: tokens=597, similarity=0.551726459744158, threshold=0.4\n", "2024-07-22 14:59:15,140 - DEBUG - Continuing cluster at sentence 151\n", "2024-07-22 14:59:15,141 - DEBUG - Sentence 152: tokens=620, similarity=0.5161861202944577, threshold=0.4\n", "2024-07-22 14:59:15,141 - DEBUG - Cluster exceeds max size (620 > 600), splitting\n", "2024-07-22 14:59:15,141 - DEBUG - Split result: 23 sentences in new cluster, 1 sentences remaining\n", "2024-07-22 14:59:15,142 - DEBUG - Sentence 158: tokens=205, similarity=0.5123673650064392, threshold=0.4\n", "2024-07-22 14:59:15,142 - DEBUG - Continuing cluster at sentence 158\n", "2024-07-22 14:59:15,142 - DEBUG - Sentence 159: tokens=223, similarity=0.47485101598184964, threshold=0.4\n", "2024-07-22 14:59:15,143 - DEBUG - Continuing cluster at sentence 159\n", "2024-07-22 14:59:15,143 - DEBUG - Sentence 160: tokens=238, similarity=0.635012445448069, threshold=0.4\n", "2024-07-22 14:59:15,143 - DEBUG - Continuing cluster at sentence 160\n", "2024-07-22 14:59:15,143 - DEBUG - Sentence 161: tokens=292, similarity=0.5540567848003204, threshold=0.4\n", "2024-07-22 14:59:15,144 - DEBUG - Continuing cluster at sentence 161\n", "2024-07-22 14:59:15,144 - DEBUG - Sentence 162: tokens=303, similarity=0.5177133074367319, threshold=0.4\n", "2024-07-22 14:59:15,144 - DEBUG - Continuing cluster at sentence 162\n", "2024-07-22 14:59:15,145 - DEBUG - Sentence 163: tokens=318, similarity=0.47868828754310777, threshold=0.4\n", "2024-07-22 14:59:15,145 - DEBUG - Continuing cluster at sentence 163\n", "2024-07-22 14:59:15,145 - DEBUG - Sentence 164: tokens=344, similarity=0.6647819500026445, threshold=0.4\n", "2024-07-22 14:59:15,146 - DEBUG - Continuing cluster at sentence 164\n", "2024-07-22 14:59:15,146 - DEBUG - Sentence 165: tokens=384, similarity=0.5893387361221083, threshold=0.4\n", "2024-07-22 14:59:15,146 - DEBUG - Continuing cluster at sentence 165\n", "2024-07-22 14:59:15,146 - DEBUG - Sentence 166: tokens=393, similarity=0.6846311615789239, threshold=0.4\n", "2024-07-22 14:59:15,157 - DEBUG - Continuing cluster at sentence 166\n", "2024-07-22 14:59:15,158 - DEBUG - Sentence 167: tokens=409, similarity=0.5283441208264555, threshold=0.4\n", "2024-07-22 14:59:15,158 - DEBUG - Continuing cluster at sentence 167\n", "2024-07-22 14:59:15,158 - DEBUG - Sentence 168: tokens=421, similarity=0.557609115260339, threshold=0.4\n", "2024-07-22 14:59:15,158 - DEBUG - Continuing cluster at sentence 168\n", "2024-07-22 14:59:15,159 - DEBUG - Sentence 169: tokens=433, similarity=0.6027195945913626, threshold=0.4\n", "2024-07-22 14:59:15,159 - DEBUG - Continuing cluster at sentence 169\n", "2024-07-22 14:59:15,159 - DEBUG - Sentence 170: tokens=460, similarity=0.4703252858050275, threshold=0.4\n", "2024-07-22 14:59:15,159 - DEBUG - Continuing cluster at sentence 170\n", "2024-07-22 14:59:15,160 - DEBUG - Sentence 171: tokens=495, similarity=0.6051013813613662, threshold=0.4\n", "2024-07-22 14:59:15,160 - DEBUG - Continuing cluster at sentence 171\n", "2024-07-22 14:59:15,160 - DEBUG - Sentence 172: tokens=539, similarity=0.4107674903284534, threshold=0.4\n", "2024-07-22 14:59:15,160 - DEBUG - Continuing cluster at sentence 172\n", "2024-07-22 14:59:15,160 - DEBUG - Sentence 173: tokens=556, similarity=0.6164725456104, threshold=0.4\n", "2024-07-22 14:59:15,161 - DEBUG - Continuing cluster at sentence 173\n", "2024-07-22 14:59:15,161 - DEBUG - Sentence 174: tokens=562, similarity=0.5406598217756878, threshold=0.4\n", "2024-07-22 14:59:15,161 - DEBUG - Continuing cluster at sentence 174\n", "2024-07-22 14:59:15,161 - DEBUG - Sentence 175: tokens=564, similarity=0.4419019053673131, threshold=0.4\n", "2024-07-22 14:59:15,161 - DEBUG - Continuing cluster at sentence 175\n", "2024-07-22 14:59:15,162 - DEBUG - Sentence 176: tokens=580, similarity=0.3824707487908246, threshold=0.4\n", "2024-07-22 14:59:15,162 - DEBUG - Appending cluster with 25 sentences, 580 tokens\n", "2024-07-22 14:59:15,162 - DEBUG - Sentence 186: tokens=217, similarity=0.5977350924158652, threshold=0.4\n", "2024-07-22 14:59:15,162 - DEBUG - Continuing cluster at sentence 186\n", "2024-07-22 14:59:15,162 - DEBUG - Sentence 187: tokens=236, similarity=0.5768737287165105, threshold=0.4\n", "2024-07-22 14:59:15,163 - DEBUG - Continuing cluster at sentence 187\n", "2024-07-22 14:59:15,163 - DEBUG - Sentence 188: tokens=259, similarity=0.5888462293961587, threshold=0.4\n", "2024-07-22 14:59:15,163 - DEBUG - Continuing cluster at sentence 188\n", "2024-07-22 14:59:15,163 - DEBUG - Sentence 189: tokens=283, similarity=0.47973295794263754, threshold=0.4\n", "2024-07-22 14:59:15,163 - DEBUG - Continuing cluster at sentence 189\n", "2024-07-22 14:59:15,164 - DEBUG - Sentence 190: tokens=310, similarity=0.48129412282495426, threshold=0.4\n", "2024-07-22 14:59:15,164 - DEBUG - Continuing cluster at sentence 190\n", "2024-07-22 14:59:15,164 - DEBUG - Sentence 191: tokens=329, similarity=0.5351248344229673, threshold=0.4\n", "2024-07-22 14:59:15,164 - DEBUG - Continuing cluster at sentence 191\n", "2024-07-22 14:59:15,165 - DEBUG - Sentence 192: tokens=442, similarity=0.5975953544430079, threshold=0.4\n", "2024-07-22 14:59:15,165 - DEBUG - Continuing cluster at sentence 192\n", "2024-07-22 14:59:15,165 - DEBUG - Sentence 193: tokens=515, similarity=0.49502403429545294, threshold=0.4\n", "2024-07-22 14:59:15,165 - DEBUG - Continuing cluster at sentence 193\n", "2024-07-22 14:59:15,165 - DEBUG - Sentence 194: tokens=526, similarity=0.3356481059434584, threshold=0.4\n", "2024-07-22 14:59:15,166 - DEBUG - Appending cluster with 18 sentences, 526 tokens\n", "2024-07-22 14:59:15,166 - DEBUG - Sentence 202: tokens=207, similarity=0.5701953760346605, threshold=0.4\n", "2024-07-22 14:59:15,166 - DEBUG - Continuing cluster at sentence 202\n", "2024-07-22 14:59:15,166 - DEBUG - Sentence 203: tokens=214, similarity=0.5748998004397163, threshold=0.4\n", "2024-07-22 14:59:15,166 - DEBUG - Continuing cluster at sentence 203\n", "2024-07-22 14:59:15,167 - DEBUG - Sentence 204: tokens=232, similarity=0.5650029445504183, threshold=0.4\n", "2024-07-22 14:59:15,167 - DEBUG - Continuing cluster at sentence 204\n", "2024-07-22 14:59:15,167 - DEBUG - Sentence 205: tokens=240, similarity=0.6890317946770765, threshold=0.4\n", "2024-07-22 14:59:15,167 - DEBUG - Continuing cluster at sentence 205\n", "2024-07-22 14:59:15,168 - DEBUG - Sentence 206: tokens=248, similarity=0.5848853258767932, threshold=0.4\n", "2024-07-22 14:59:15,168 - DEBUG - Continuing cluster at sentence 206\n", "2024-07-22 14:59:15,168 - DEBUG - Sentence 207: tokens=276, similarity=0.5286471081399471, threshold=0.4\n", "2024-07-22 14:59:15,168 - DEBUG - Continuing cluster at sentence 207\n", "2024-07-22 14:59:15,168 - DEBUG - Sentence 208: tokens=311, similarity=0.49102310717326003, threshold=0.4\n", "2024-07-22 14:59:15,169 - DEBUG - Continuing cluster at sentence 208\n", "2024-07-22 14:59:15,169 - DEBUG - Sentence 209: tokens=338, similarity=0.6094019082204007, threshold=0.4\n", "2024-07-22 14:59:15,169 - DEBUG - Continuing cluster at sentence 209\n", "2024-07-22 14:59:15,169 - DEBUG - Sentence 210: tokens=368, similarity=0.5934059514683144, threshold=0.4\n", "2024-07-22 14:59:15,169 - DEBUG - Continuing cluster at sentence 210\n", "2024-07-22 14:59:15,170 - DEBUG - Sentence 211: tokens=392, similarity=0.4759325059346068, threshold=0.4\n", "2024-07-22 14:59:15,170 - DEBUG - Continuing cluster at sentence 211\n", "2024-07-22 14:59:15,170 - DEBUG - Sentence 212: tokens=438, similarity=0.5961184382216664, threshold=0.4\n", "2024-07-22 14:59:15,170 - DEBUG - Continuing cluster at sentence 212\n", "2024-07-22 14:59:15,170 - DEBUG - Sentence 213: tokens=468, similarity=0.593676588497638, threshold=0.4\n", "2024-07-22 14:59:15,171 - DEBUG - Continuing cluster at sentence 213\n", "2024-07-22 14:59:15,171 - DEBUG - Sentence 214: tokens=502, similarity=0.6610712774134226, threshold=0.4\n", "2024-07-22 14:59:15,171 - DEBUG - Continuing cluster at sentence 214\n", "2024-07-22 14:59:15,171 - DEBUG - Sentence 215: tokens=535, similarity=0.4397478443915563, threshold=0.4\n", "2024-07-22 14:59:15,171 - DEBUG - Continuing cluster at sentence 215\n", "2024-07-22 14:59:15,172 - DEBUG - Sentence 216: tokens=544, similarity=0.4946192974442835, threshold=0.4\n", "2024-07-22 14:59:15,172 - DEBUG - Continuing cluster at sentence 216\n", "2024-07-22 14:59:15,172 - DEBUG - Sentence 217: tokens=566, similarity=0.44642294524248316, threshold=0.4\n", "2024-07-22 14:59:15,172 - DEBUG - Continuing cluster at sentence 217\n", "2024-07-22 14:59:15,172 - DEBUG - Sentence 218: tokens=583, similarity=0.4415002857645326, threshold=0.4\n", "2024-07-22 14:59:15,173 - DEBUG - Continuing cluster at sentence 218\n", "2024-07-22 14:59:15,173 - DEBUG - Sentence 219: tokens=588, similarity=0.455186555473492, threshold=0.4\n", "2024-07-22 14:59:15,173 - DEBUG - Continuing cluster at sentence 219\n", "2024-07-22 14:59:15,173 - DEBUG - Sentence 220: tokens=610, similarity=0.42369462853251294, threshold=0.4\n", "2024-07-22 14:59:15,173 - DEBUG - Cluster exceeds max size (610 > 600), splitting\n", "2024-07-22 14:59:15,174 - DEBUG - Split result: 25 sentences in new cluster, 1 sentences remaining\n", "2024-07-22 14:59:15,174 - DEBUG - Sentence 228: tokens=212, similarity=0.575201806315039, threshold=0.4\n", "2024-07-22 14:59:15,174 - DEBUG - Continuing cluster at sentence 228\n", "2024-07-22 14:59:15,174 - DEBUG - Sentence 229: tokens=231, similarity=0.4065388111000303, threshold=0.4\n", "2024-07-22 14:59:15,174 - DEBUG - Continuing cluster at sentence 229\n", "2024-07-22 14:59:15,175 - DEBUG - Sentence 230: tokens=248, similarity=0.5367851595174994, threshold=0.4\n", "2024-07-22 14:59:15,175 - DEBUG - Continuing cluster at sentence 230\n", "2024-07-22 14:59:15,175 - DEBUG - Sentence 231: tokens=270, similarity=0.37127170218814165, threshold=0.4\n", "2024-07-22 14:59:15,175 - DEBUG - Appending cluster with 12 sentences, 270 tokens\n", "2024-07-22 14:59:15,175 - DEBUG - Sentence 243: tokens=212, similarity=0.5255931382232458, threshold=0.4\n", "2024-07-22 14:59:15,176 - DEBUG - Continuing cluster at sentence 243\n", "2024-07-22 14:59:15,176 - DEBUG - Sentence 244: tokens=225, similarity=0.6670658431570256, threshold=0.4\n", "2024-07-22 14:59:15,176 - DEBUG - Continuing cluster at sentence 244\n", "2024-07-22 14:59:15,176 - DEBUG - Sentence 245: tokens=242, similarity=0.6612137030821162, threshold=0.4\n", "2024-07-22 14:59:15,177 - DEBUG - Continuing cluster at sentence 245\n", "2024-07-22 14:59:15,177 - DEBUG - Sentence 246: tokens=255, similarity=0.5307839122461931, threshold=0.4\n", "2024-07-22 14:59:15,177 - DEBUG - Continuing cluster at sentence 246\n", "2024-07-22 14:59:15,177 - DEBUG - Sentence 247: tokens=271, similarity=0.5778168620088177, threshold=0.4\n", "2024-07-22 14:59:15,177 - DEBUG - Continuing cluster at sentence 247\n", "2024-07-22 14:59:15,178 - DEBUG - Sentence 248: tokens=278, similarity=0.6688254476346066, threshold=0.4\n", "2024-07-22 14:59:15,178 - DEBUG - Continuing cluster at sentence 248\n", "2024-07-22 14:59:15,178 - DEBUG - Sentence 249: tokens=286, similarity=0.5365587628353976, threshold=0.4\n", "2024-07-22 14:59:15,178 - DEBUG - Continuing cluster at sentence 249\n", "2024-07-22 14:59:15,178 - DEBUG - Sentence 250: tokens=299, similarity=0.4998169784972382, threshold=0.4\n", "2024-07-22 14:59:15,179 - DEBUG - Continuing cluster at sentence 250\n", "2024-07-22 14:59:15,179 - DEBUG - Sentence 251: tokens=330, similarity=0.3928590623299694, threshold=0.4\n", "2024-07-22 14:59:15,179 - DEBUG - Appending cluster with 20 sentences, 330 tokens\n", "2024-07-22 14:59:15,179 - DEBUG - Sentence 262: tokens=203, similarity=0.5796414396865459, threshold=0.4\n", "2024-07-22 14:59:15,180 - DEBUG - Continuing cluster at sentence 262\n", "2024-07-22 14:59:15,180 - DEBUG - Sentence 263: tokens=224, similarity=0.5418048240994153, threshold=0.4\n", "2024-07-22 14:59:15,180 - DEBUG - Continuing cluster at sentence 263\n", "2024-07-22 14:59:15,180 - DEBUG - Sentence 264: tokens=231, similarity=0.6214990051515383, threshold=0.4\n", "2024-07-22 14:59:15,181 - DEBUG - Continuing cluster at sentence 264\n", "2024-07-22 14:59:15,181 - DEBUG - Sentence 265: tokens=247, similarity=0.6655630765952678, threshold=0.4\n", "2024-07-22 14:59:15,181 - DEBUG - Continuing cluster at sentence 265\n", "2024-07-22 14:59:15,181 - DEBUG - Sentence 266: tokens=269, similarity=0.5705016996315593, threshold=0.4\n", "2024-07-22 14:59:15,181 - DEBUG - Continuing cluster at sentence 266\n", "2024-07-22 14:59:15,182 - DEBUG - Sentence 267: tokens=280, similarity=0.573574773534181, threshold=0.4\n", "2024-07-22 14:59:15,182 - DEBUG - Continuing cluster at sentence 267\n", "2024-07-22 14:59:15,182 - DEBUG - Sentence 268: tokens=300, similarity=0.5801352357765183, threshold=0.4\n", "2024-07-22 14:59:15,182 - DEBUG - Continuing cluster at sentence 268\n", "2024-07-22 14:59:15,182 - DEBUG - Sentence 269: tokens=318, similarity=0.5523209995606554, threshold=0.4\n", "2024-07-22 14:59:15,183 - DEBUG - Continuing cluster at sentence 269\n", "2024-07-22 14:59:15,183 - DEBUG - Sentence 270: tokens=341, similarity=0.3827976718604458, threshold=0.4\n", "2024-07-22 14:59:15,183 - DEBUG - Appending cluster with 19 sentences, 341 tokens\n", "2024-07-22 14:59:15,183 - DEBUG - Sentence 281: tokens=200, similarity=0.40061603231491627, threshold=0.4\n", "2024-07-22 14:59:15,183 - DEBUG - Continuing cluster at sentence 281\n", "2024-07-22 14:59:15,184 - DEBUG - Sentence 282: tokens=229, similarity=0.5381006206777355, threshold=0.4\n", "2024-07-22 14:59:15,184 - DEBUG - Continuing cluster at sentence 282\n", "2024-07-22 14:59:15,184 - DEBUG - Sentence 283: tokens=241, similarity=0.4506099993358078, threshold=0.4\n", "2024-07-22 14:59:15,184 - DEBUG - Continuing cluster at sentence 283\n", "2024-07-22 14:59:15,185 - DEBUG - Sentence 284: tokens=280, similarity=0.5588327316566264, threshold=0.4\n", "2024-07-22 14:59:15,185 - DEBUG - Continuing cluster at sentence 284\n", "2024-07-22 14:59:15,185 - DEBUG - Sentence 285: tokens=306, similarity=0.5586901937358856, threshold=0.4\n", "2024-07-22 14:59:15,185 - DEBUG - Continuing cluster at sentence 285\n", "2024-07-22 14:59:15,185 - DEBUG - Sentence 286: tokens=321, similarity=0.49316603843524737, threshold=0.4\n", "2024-07-22 14:59:15,186 - DEBUG - Continuing cluster at sentence 286\n", "2024-07-22 14:59:15,186 - DEBUG - Sentence 287: tokens=336, similarity=0.4353564350602981, threshold=0.4\n", "2024-07-22 14:59:15,186 - DEBUG - Continuing cluster at sentence 287\n", "2024-07-22 14:59:15,186 - DEBUG - Sentence 288: tokens=371, similarity=0.4890203212603119, threshold=0.4\n", "2024-07-22 14:59:15,186 - DEBUG - Continuing cluster at sentence 288\n", "2024-07-22 14:59:15,187 - DEBUG - Sentence 289: tokens=391, similarity=0.5119685459900745, threshold=0.4\n", "2024-07-22 14:59:15,187 - DEBUG - Continuing cluster at sentence 289\n", "2024-07-22 14:59:15,187 - DEBUG - Sentence 290: tokens=397, similarity=0.39592555388645556, threshold=0.4\n", "2024-07-22 14:59:15,187 - DEBUG - Appending cluster with 20 sentences, 397 tokens\n", "2024-07-22 14:59:15,187 - DEBUG - Sentence 297: tokens=210, similarity=0.27007688566442034, threshold=0.4\n", "2024-07-22 14:59:15,188 - DEBUG - Appending cluster with 7 sentences, 210 tokens\n", "2024-07-22 14:59:15,188 - DEBUG - Sentence 307: tokens=210, similarity=0.30006766107232324, threshold=0.4\n", "2024-07-22 14:59:15,188 - DEBUG - Appending cluster with 10 sentences, 210 tokens\n", "2024-07-22 14:59:15,188 - DEBUG - Sentence 315: tokens=221, similarity=0.441971511122095, threshold=0.4\n", "2024-07-22 14:59:15,189 - DEBUG - Continuing cluster at sentence 315\n", "2024-07-22 14:59:15,189 - DEBUG - Sentence 316: tokens=240, similarity=0.3769218826489885, threshold=0.4\n", "2024-07-22 14:59:15,189 - DEBUG - Appending cluster with 9 sentences, 240 tokens\n", "2024-07-22 14:59:15,189 - DEBUG - Sentence 327: tokens=221, similarity=0.5743713469997567, threshold=0.4\n", "2024-07-22 14:59:15,189 - DEBUG - Continuing cluster at sentence 327\n", "2024-07-22 14:59:15,190 - DEBUG - Sentence 328: tokens=227, similarity=0.5032309757250962, threshold=0.4\n", "2024-07-22 14:59:15,190 - DEBUG - Continuing cluster at sentence 328\n", "2024-07-22 14:59:15,190 - DEBUG - Sentence 329: tokens=240, similarity=0.42029514249374866, threshold=0.4\n", "2024-07-22 14:59:15,190 - DEBUG - Continuing cluster at sentence 329\n", "2024-07-22 14:59:15,191 - DEBUG - Sentence 330: tokens=287, similarity=0.613475918534677, threshold=0.4\n", "2024-07-22 14:59:15,191 - DEBUG - Continuing cluster at sentence 330\n", "2024-07-22 14:59:15,191 - DEBUG - Sentence 331: tokens=311, similarity=0.6009396278842569, threshold=0.4\n", "2024-07-22 14:59:15,191 - DEBUG - Continuing cluster at sentence 331\n", "2024-07-22 14:59:15,191 - DEBUG - Sentence 332: tokens=321, similarity=0.5778883007282497, threshold=0.4\n", "2024-07-22 14:59:15,192 - DEBUG - Continuing cluster at sentence 332\n", "2024-07-22 14:59:15,192 - DEBUG - Sentence 333: tokens=336, similarity=0.4988825077868833, threshold=0.4\n", "2024-07-22 14:59:15,192 - DEBUG - Continuing cluster at sentence 333\n", "2024-07-22 14:59:15,192 - DEBUG - Sentence 334: tokens=355, similarity=0.5991859722703443, threshold=0.4\n", "2024-07-22 14:59:15,193 - DEBUG - Continuing cluster at sentence 334\n", "2024-07-22 14:59:15,193 - DEBUG - Sentence 335: tokens=412, similarity=0.5359119750608072, threshold=0.4\n", "2024-07-22 14:59:15,193 - DEBUG - Continuing cluster at sentence 335\n", "2024-07-22 14:59:15,193 - DEBUG - Sentence 336: tokens=421, similarity=0.5816370394636279, threshold=0.4\n", "2024-07-22 14:59:15,194 - DEBUG - Continuing cluster at sentence 336\n", "2024-07-22 14:59:15,194 - DEBUG - Sentence 337: tokens=452, similarity=0.4996777180367642, threshold=0.4\n", "2024-07-22 14:59:15,194 - DEBUG - Continuing cluster at sentence 337\n", "2024-07-22 14:59:15,194 - DEBUG - Sentence 338: tokens=473, similarity=0.6115473743430082, threshold=0.4\n", "2024-07-22 14:59:15,195 - DEBUG - Continuing cluster at sentence 338\n", "2024-07-22 14:59:15,195 - DEBUG - Sentence 339: tokens=479, similarity=0.5565300568158172, threshold=0.4\n", "2024-07-22 14:59:15,195 - DEBUG - Continuing cluster at sentence 339\n", "2024-07-22 14:59:15,195 - DEBUG - Sentence 340: tokens=504, similarity=0.494771337300146, threshold=0.4\n", "2024-07-22 14:59:15,195 - DEBUG - Continuing cluster at sentence 340\n", "2024-07-22 14:59:15,196 - DEBUG - Sentence 341: tokens=518, similarity=0.49266846248645096, threshold=0.4\n", "2024-07-22 14:59:15,196 - DEBUG - Continuing cluster at sentence 341\n", "2024-07-22 14:59:15,196 - DEBUG - Sentence 342: tokens=553, similarity=0.5407870331111192, threshold=0.4\n", "2024-07-22 14:59:15,196 - DEBUG - Continuing cluster at sentence 342\n", "2024-07-22 14:59:15,197 - DEBUG - Sentence 343: tokens=568, similarity=0.40551367490935675, threshold=0.4\n", "2024-07-22 14:59:15,197 - DEBUG - Continuing cluster at sentence 343\n", "2024-07-22 14:59:15,197 - DEBUG - Sentence 344: tokens=600, similarity=0.43549401029093693, threshold=0.4\n", "2024-07-22 14:59:15,197 - DEBUG - Continuing cluster at sentence 344\n", "2024-07-22 14:59:15,198 - DEBUG - Sentence 345: tokens=609, similarity=0.4843651914177193, threshold=0.4\n", "2024-07-22 14:59:15,198 - DEBUG - Cluster exceeds max size (609 > 600), splitting\n", "2024-07-22 14:59:15,198 - DEBUG - Split result: 28 sentences in new cluster, 1 sentences remaining\n", "2024-07-22 14:59:15,198 - DEBUG - Sentence 355: tokens=215, similarity=0.5297656143722204, threshold=0.4\n", "2024-07-22 14:59:15,198 - DEBUG - Continuing cluster at sentence 355\n", "2024-07-22 14:59:15,199 - DEBUG - Sentence 356: tokens=228, similarity=0.46551872781590886, threshold=0.4\n", "2024-07-22 14:59:15,199 - DEBUG - Continuing cluster at sentence 356\n", "2024-07-22 14:59:15,199 - DEBUG - Sentence 357: tokens=243, similarity=0.5377123695410504, threshold=0.4\n", "2024-07-22 14:59:15,199 - DEBUG - Continuing cluster at sentence 357\n", "2024-07-22 14:59:15,200 - DEBUG - Sentence 358: tokens=264, similarity=0.5235566069512042, threshold=0.4\n", "2024-07-22 14:59:15,200 - DEBUG - Continuing cluster at sentence 358\n", "2024-07-22 14:59:15,200 - DEBUG - Sentence 359: tokens=282, similarity=0.4163962379384933, threshold=0.4\n", "2024-07-22 14:59:15,200 - DEBUG - Continuing cluster at sentence 359\n", "2024-07-22 14:59:15,200 - DEBUG - Sentence 360: tokens=311, similarity=0.46940604288567955, threshold=0.4\n", "2024-07-22 14:59:15,201 - DEBUG - Continuing cluster at sentence 360\n", "2024-07-22 14:59:15,201 - DEBUG - Sentence 361: tokens=336, similarity=0.5929819490843724, threshold=0.4\n", "2024-07-22 14:59:15,201 - DEBUG - Continuing cluster at sentence 361\n", "2024-07-22 14:59:15,201 - DEBUG - Sentence 362: tokens=383, similarity=0.599821088854769, threshold=0.4\n", "2024-07-22 14:59:15,202 - DEBUG - Continuing cluster at sentence 362\n", "2024-07-22 14:59:15,202 - DEBUG - Sentence 363: tokens=398, similarity=0.47889760617180477, threshold=0.4\n", "2024-07-22 14:59:15,202 - DEBUG - Continuing cluster at sentence 363\n", "2024-07-22 14:59:15,202 - DEBUG - Sentence 364: tokens=411, similarity=0.6159850562289576, threshold=0.4\n", "2024-07-22 14:59:15,202 - DEBUG - Continuing cluster at sentence 364\n", "2024-07-22 14:59:15,203 - DEBUG - Sentence 365: tokens=447, similarity=0.4779683013341747, threshold=0.4\n", "2024-07-22 14:59:15,203 - DEBUG - Continuing cluster at sentence 365\n", "2024-07-22 14:59:15,203 - DEBUG - Sentence 366: tokens=469, similarity=0.4863820649326601, threshold=0.4\n", "2024-07-22 14:59:15,203 - DEBUG - Continuing cluster at sentence 366\n", "2024-07-22 14:59:15,204 - DEBUG - Sentence 367: tokens=490, similarity=0.5422027990292217, threshold=0.4\n", "2024-07-22 14:59:15,204 - DEBUG - Continuing cluster at sentence 367\n", "2024-07-22 14:59:15,204 - DEBUG - Sentence 368: tokens=507, similarity=0.48688612238095175, threshold=0.4\n", "2024-07-22 14:59:15,204 - DEBUG - Continuing cluster at sentence 368\n", "2024-07-22 14:59:15,204 - DEBUG - Sentence 369: tokens=515, similarity=0.6908831858051624, threshold=0.4\n", "2024-07-22 14:59:15,205 - DEBUG - Continuing cluster at sentence 369\n", "2024-07-22 14:59:15,205 - DEBUG - Sentence 370: tokens=536, similarity=0.5008079757627109, threshold=0.4\n", "2024-07-22 14:59:15,205 - DEBUG - Continuing cluster at sentence 370\n", "2024-07-22 14:59:15,205 - DEBUG - Sentence 371: tokens=557, similarity=0.587620874622516, threshold=0.4\n", "2024-07-22 14:59:15,206 - DEBUG - Continuing cluster at sentence 371\n", "2024-07-22 14:59:15,206 - DEBUG - Sentence 372: tokens=571, similarity=0.5874485186414979, threshold=0.4\n", "2024-07-22 14:59:15,206 - DEBUG - Continuing cluster at sentence 372\n", "2024-07-22 14:59:15,206 - DEBUG - Sentence 373: tokens=586, similarity=0.6326349960783009, threshold=0.4\n", "2024-07-22 14:59:15,206 - DEBUG - Continuing cluster at sentence 373\n", "2024-07-22 14:59:15,207 - DEBUG - Sentence 374: tokens=598, similarity=0.46845465293910843, threshold=0.4\n", "2024-07-22 14:59:15,207 - DEBUG - Continuing cluster at sentence 374\n", "2024-07-22 14:59:15,208 - DEBUG - Sentence 375: tokens=612, similarity=0.5516859306301586, threshold=0.4\n", "2024-07-22 14:59:15,208 - DEBUG - Cluster exceeds max size (612 > 600), splitting\n", "2024-07-22 14:59:15,208 - DEBUG - Split result: 30 sentences in new cluster, 1 sentences remaining\n", "2024-07-22 14:59:15,208 - DEBUG - Sentence 385: tokens=205, similarity=0.5583352924487031, threshold=0.4\n", "2024-07-22 14:59:15,209 - DEBUG - Continuing cluster at sentence 385\n", "2024-07-22 14:59:15,209 - DEBUG - Sentence 386: tokens=220, similarity=0.5864717500727863, threshold=0.4\n", "2024-07-22 14:59:15,209 - DEBUG - Continuing cluster at sentence 386\n", "2024-07-22 14:59:15,209 - DEBUG - Sentence 387: tokens=245, similarity=0.5596835324490981, threshold=0.4\n", "2024-07-22 14:59:15,209 - DEBUG - Continuing cluster at sentence 387\n", "2024-07-22 14:59:15,210 - DEBUG - Sentence 388: tokens=294, similarity=0.5109429225808706, threshold=0.4\n", "2024-07-22 14:59:15,210 - DEBUG - Continuing cluster at sentence 388\n", "2024-07-22 14:59:15,210 - DEBUG - Sentence 389: tokens=323, similarity=0.5222888498795122, threshold=0.4\n", "2024-07-22 14:59:15,210 - DEBUG - Continuing cluster at sentence 389\n", "2024-07-22 14:59:15,210 - DEBUG - Sentence 390: tokens=342, similarity=0.5236838353574383, threshold=0.4\n", "2024-07-22 14:59:15,211 - DEBUG - Continuing cluster at sentence 390\n", "2024-07-22 14:59:15,211 - DEBUG - Sentence 391: tokens=350, similarity=0.6040619788095352, threshold=0.4\n", "2024-07-22 14:59:15,211 - DEBUG - Continuing cluster at sentence 391\n", "2024-07-22 14:59:15,211 - DEBUG - Sentence 392: tokens=368, similarity=0.4942596861606229, threshold=0.4\n", "2024-07-22 14:59:15,212 - DEBUG - Continuing cluster at sentence 392\n", "2024-07-22 14:59:15,212 - DEBUG - Sentence 393: tokens=402, similarity=0.5911085097541146, threshold=0.4\n", "2024-07-22 14:59:15,212 - DEBUG - Continuing cluster at sentence 393\n", "2024-07-22 14:59:15,212 - DEBUG - Sentence 394: tokens=418, similarity=0.5862617971974039, threshold=0.4\n", "2024-07-22 14:59:15,212 - DEBUG - Continuing cluster at sentence 394\n", "2024-07-22 14:59:15,213 - DEBUG - Sentence 395: tokens=425, similarity=0.7041471139530526, threshold=0.4\n", "2024-07-22 14:59:15,213 - DEBUG - Continuing cluster at sentence 395\n", "2024-07-22 14:59:15,213 - DEBUG - Sentence 396: tokens=432, similarity=0.6038563775619099, threshold=0.4\n", "2024-07-22 14:59:15,214 - DEBUG - Continuing cluster at sentence 396\n", "2024-07-22 14:59:15,214 - DEBUG - Sentence 397: tokens=495, similarity=0.5044367295253939, threshold=0.4\n", "2024-07-22 14:59:15,214 - DEBUG - Continuing cluster at sentence 397\n", "2024-07-22 14:59:15,214 - DEBUG - Sentence 398: tokens=504, similarity=0.6801273749506485, threshold=0.4\n", "2024-07-22 14:59:15,214 - DEBUG - Continuing cluster at sentence 398\n", "2024-07-22 14:59:15,215 - DEBUG - Sentence 399: tokens=515, similarity=0.47996602923206855, threshold=0.4\n", "2024-07-22 14:59:15,215 - DEBUG - Continuing cluster at sentence 399\n", "2024-07-22 14:59:15,215 - DEBUG - Sentence 400: tokens=533, similarity=0.5763276329508134, threshold=0.4\n", "2024-07-22 14:59:15,215 - DEBUG - Continuing cluster at sentence 400\n", "2024-07-22 14:59:15,216 - DEBUG - Sentence 401: tokens=550, similarity=0.44097022273530423, threshold=0.4\n", "2024-07-22 14:59:15,216 - DEBUG - Continuing cluster at sentence 401\n", "2024-07-22 14:59:15,216 - DEBUG - Sentence 402: tokens=553, similarity=0.5412728447192177, threshold=0.4\n", "2024-07-22 14:59:15,216 - DEBUG - Continuing cluster at sentence 402\n", "2024-07-22 14:59:15,216 - DEBUG - Sentence 403: tokens=570, similarity=0.4607309004439796, threshold=0.4\n", "2024-07-22 14:59:15,217 - DEBUG - Continuing cluster at sentence 403\n", "2024-07-22 14:59:15,217 - DEBUG - Sentence 404: tokens=587, similarity=0.6078714074369095, threshold=0.4\n", "2024-07-22 14:59:15,217 - DEBUG - Continuing cluster at sentence 404\n", "2024-07-22 14:59:15,217 - DEBUG - Sentence 405: tokens=598, similarity=0.7253203838637983, threshold=0.4\n", "2024-07-22 14:59:15,218 - DEBUG - Continuing cluster at sentence 405\n", "2024-07-22 14:59:15,218 - DEBUG - Sentence 406: tokens=620, similarity=0.5878125412677991, threshold=0.4\n", "2024-07-22 14:59:15,218 - DEBUG - Cluster exceeds max size (620 > 600), splitting\n", "2024-07-22 14:59:15,218 - DEBUG - Split result: 31 sentences in new cluster, 1 sentences remaining\n", "2024-07-22 14:59:15,218 - DEBUG - Sentence 413: tokens=210, similarity=0.47736813491911073, threshold=0.4\n", "2024-07-22 14:59:15,219 - DEBUG - Continuing cluster at sentence 413\n", "2024-07-22 14:59:15,219 - DEBUG - Sentence 414: tokens=219, similarity=0.43436557359236316, threshold=0.4\n", "2024-07-22 14:59:15,219 - DEBUG - Continuing cluster at sentence 414\n", "2024-07-22 14:59:15,219 - DEBUG - Sentence 415: tokens=268, similarity=0.4497708988103525, threshold=0.4\n", "2024-07-22 14:59:15,220 - DEBUG - Continuing cluster at sentence 415\n", "2024-07-22 14:59:15,220 - DEBUG - Sentence 416: tokens=282, similarity=0.49082111412753526, threshold=0.4\n", "2024-07-22 14:59:15,220 - DEBUG - Continuing cluster at sentence 416\n", "2024-07-22 14:59:15,220 - DEBUG - Sentence 417: tokens=312, similarity=0.3587919563033266, threshold=0.4\n", "2024-07-22 14:59:15,220 - DEBUG - Appending cluster with 12 sentences, 312 tokens\n", "2024-07-22 14:59:15,221 - DEBUG - Sentence 426: tokens=211, similarity=0.6259646167425574, threshold=0.4\n", "2024-07-22 14:59:15,221 - DEBUG - Continuing cluster at sentence 426\n", "2024-07-22 14:59:15,221 - DEBUG - Sentence 427: tokens=255, similarity=0.565523702589126, threshold=0.4\n", "2024-07-22 14:59:15,221 - DEBUG - Continuing cluster at sentence 427\n", "2024-07-22 14:59:15,222 - DEBUG - Sentence 428: tokens=281, similarity=0.5776102212688219, threshold=0.4\n", "2024-07-22 14:59:15,222 - DEBUG - Continuing cluster at sentence 428\n", "2024-07-22 14:59:15,222 - DEBUG - Sentence 429: tokens=337, similarity=0.36888664166634233, threshold=0.4\n", "2024-07-22 14:59:15,222 - DEBUG - Appending cluster with 12 sentences, 337 tokens\n", "2024-07-22 14:59:15,222 - DEBUG - Sentence 442: tokens=200, similarity=0.578982584239835, threshold=0.4\n", "2024-07-22 14:59:15,223 - DEBUG - Continuing cluster at sentence 442\n", "2024-07-22 14:59:15,223 - DEBUG - Sentence 443: tokens=227, similarity=0.6262461414563931, threshold=0.4\n", "2024-07-22 14:59:15,223 - DEBUG - Continuing cluster at sentence 443\n", "2024-07-22 14:59:15,223 - DEBUG - Sentence 444: tokens=233, similarity=0.5036461465759138, threshold=0.4\n", "2024-07-22 14:59:15,223 - DEBUG - Continuing cluster at sentence 444\n", "2024-07-22 14:59:15,224 - DEBUG - Sentence 445: tokens=253, similarity=0.529283706065361, threshold=0.4\n", "2024-07-22 14:59:15,224 - DEBUG - Continuing cluster at sentence 445\n", "2024-07-22 14:59:15,224 - DEBUG - Sentence 446: tokens=289, similarity=0.6861476185273703, threshold=0.4\n", "2024-07-22 14:59:15,224 - DEBUG - Continuing cluster at sentence 446\n", "2024-07-22 14:59:15,225 - DEBUG - Sentence 447: tokens=301, similarity=0.5421184083176157, threshold=0.4\n", "2024-07-22 14:59:15,225 - DEBUG - Continuing cluster at sentence 447\n", "2024-07-22 14:59:15,225 - DEBUG - Sentence 448: tokens=313, similarity=0.5608175426944032, threshold=0.4\n", "2024-07-22 14:59:15,225 - DEBUG - Continuing cluster at sentence 448\n", "2024-07-22 14:59:15,225 - DEBUG - Sentence 449: tokens=333, similarity=0.5581669313703872, threshold=0.4\n", "2024-07-22 14:59:15,226 - DEBUG - Continuing cluster at sentence 449\n", "2024-07-22 14:59:15,226 - DEBUG - Sentence 450: tokens=365, similarity=0.5308595594181003, threshold=0.4\n", "2024-07-22 14:59:15,226 - DEBUG - Continuing cluster at sentence 450\n", "2024-07-22 14:59:15,226 - DEBUG - Sentence 451: tokens=386, similarity=0.47829647164094435, threshold=0.4\n", "2024-07-22 14:59:15,227 - DEBUG - Continuing cluster at sentence 451\n", "2024-07-22 14:59:15,227 - DEBUG - Sentence 452: tokens=399, similarity=0.6004765466632377, threshold=0.4\n", "2024-07-22 14:59:15,227 - DEBUG - Continuing cluster at sentence 452\n", "2024-07-22 14:59:15,227 - DEBUG - Sentence 453: tokens=437, similarity=0.6519004505033417, threshold=0.4\n", "2024-07-22 14:59:15,227 - DEBUG - Continuing cluster at sentence 453\n", "2024-07-22 14:59:15,228 - DEBUG - Sentence 454: tokens=455, similarity=0.6068284172365914, threshold=0.4\n", "2024-07-22 14:59:15,228 - DEBUG - Continuing cluster at sentence 454\n", "2024-07-22 14:59:15,228 - DEBUG - Sentence 455: tokens=468, similarity=0.5088195217416883, threshold=0.4\n", "2024-07-22 14:59:15,228 - DEBUG - Continuing cluster at sentence 455\n", "2024-07-22 14:59:15,229 - DEBUG - Sentence 456: tokens=501, similarity=0.4137256496886226, threshold=0.4\n", "2024-07-22 14:59:15,229 - DEBUG - Continuing cluster at sentence 456\n", "2024-07-22 14:59:15,229 - DEBUG - Sentence 457: tokens=517, similarity=0.6362554829794222, threshold=0.4\n", "2024-07-22 14:59:15,229 - DEBUG - Continuing cluster at sentence 457\n", "2024-07-22 14:59:15,230 - DEBUG - Sentence 458: tokens=550, similarity=0.48361296584045327, threshold=0.4\n", "2024-07-22 14:59:15,230 - DEBUG - Continuing cluster at sentence 458\n", "2024-07-22 14:59:15,230 - DEBUG - Sentence 459: tokens=562, similarity=0.34917094992930225, threshold=0.4\n", "2024-07-22 14:59:15,230 - DEBUG - Appending cluster with 30 sentences, 562 tokens\n", "2024-07-22 14:59:15,231 - DEBUG - Sentence 468: tokens=212, similarity=0.4872331662254103, threshold=0.4\n", "2024-07-22 14:59:15,231 - DEBUG - Continuing cluster at sentence 468\n", "2024-07-22 14:59:15,231 - DEBUG - Sentence 469: tokens=239, similarity=0.5221596624683694, threshold=0.4\n", "2024-07-22 14:59:15,231 - DEBUG - Continuing cluster at sentence 469\n", "2024-07-22 14:59:15,231 - DEBUG - Sentence 470: tokens=252, similarity=0.49759174330382633, threshold=0.4\n", "2024-07-22 14:59:15,232 - DEBUG - Continuing cluster at sentence 470\n", "2024-07-22 14:59:15,232 - DEBUG - Sentence 471: tokens=276, similarity=0.46571545328027875, threshold=0.4\n", "2024-07-22 14:59:15,232 - DEBUG - Continuing cluster at sentence 471\n", "2024-07-22 14:59:15,233 - DEBUG - Sentence 472: tokens=305, similarity=0.4849984398177607, threshold=0.4\n", "2024-07-22 14:59:15,233 - DEBUG - Continuing cluster at sentence 472\n", "2024-07-22 14:59:15,233 - DEBUG - Sentence 473: tokens=321, similarity=0.5821586308965234, threshold=0.4\n", "2024-07-22 14:59:15,233 - DEBUG - Continuing cluster at sentence 473\n", "2024-07-22 14:59:15,233 - DEBUG - Sentence 474: tokens=342, similarity=0.5956748047029403, threshold=0.4\n", "2024-07-22 14:59:15,234 - DEBUG - Continuing cluster at sentence 474\n", "2024-07-22 14:59:15,234 - DEBUG - Sentence 475: tokens=354, similarity=0.7051614359330061, threshold=0.4\n", "2024-07-22 14:59:15,234 - DEBUG - Continuing cluster at sentence 475\n", "2024-07-22 14:59:15,234 - DEBUG - Sentence 476: tokens=377, similarity=0.6653447560238452, threshold=0.4\n", "2024-07-22 14:59:15,235 - DEBUG - Continuing cluster at sentence 476\n", "2024-07-22 14:59:15,235 - DEBUG - Sentence 477: tokens=402, similarity=0.6368636479083416, threshold=0.4\n", "2024-07-22 14:59:15,235 - DEBUG - Continuing cluster at sentence 477\n", "2024-07-22 14:59:15,235 - DEBUG - Sentence 478: tokens=411, similarity=0.49752911234339803, threshold=0.4\n", "2024-07-22 14:59:15,235 - DEBUG - Continuing cluster at sentence 478\n", "2024-07-22 14:59:15,236 - DEBUG - Sentence 479: tokens=423, similarity=0.5337261970171442, threshold=0.4\n", "2024-07-22 14:59:15,236 - DEBUG - Continuing cluster at sentence 479\n", "2024-07-22 14:59:15,236 - DEBUG - Sentence 480: tokens=455, similarity=0.4897381121906067, threshold=0.4\n", "2024-07-22 14:59:15,236 - DEBUG - Continuing cluster at sentence 480\n", "2024-07-22 14:59:15,237 - DEBUG - Sentence 481: tokens=462, similarity=0.501118493013917, threshold=0.4\n", "2024-07-22 14:59:15,237 - DEBUG - Continuing cluster at sentence 481\n", "2024-07-22 14:59:15,237 - DEBUG - Sentence 482: tokens=475, similarity=0.574323092694895, threshold=0.4\n", "2024-07-22 14:59:15,237 - DEBUG - Continuing cluster at sentence 482\n", "2024-07-22 14:59:15,238 - DEBUG - Sentence 483: tokens=498, similarity=0.5729286781273215, threshold=0.4\n", "2024-07-22 14:59:15,238 - DEBUG - Continuing cluster at sentence 483\n", "2024-07-22 14:59:15,238 - DEBUG - Sentence 484: tokens=515, similarity=0.5692073345052171, threshold=0.4\n", "2024-07-22 14:59:15,238 - DEBUG - Continuing cluster at sentence 484\n", "2024-07-22 14:59:15,238 - DEBUG - Sentence 485: tokens=546, similarity=0.5179431157398007, threshold=0.4\n", "2024-07-22 14:59:15,239 - DEBUG - Continuing cluster at sentence 485\n", "2024-07-22 14:59:15,239 - DEBUG - Sentence 486: tokens=590, similarity=0.4126912543801321, threshold=0.4\n", "2024-07-22 14:59:15,239 - DEBUG - Continuing cluster at sentence 486\n", "2024-07-22 14:59:15,239 - DEBUG - Sentence 487: tokens=604, similarity=0.5656517793341432, threshold=0.4\n", "2024-07-22 14:59:15,240 - DEBUG - Cluster exceeds max size (604 > 600), splitting\n", "2024-07-22 14:59:15,240 - DEBUG - Split result: 27 sentences in new cluster, 1 sentences remaining\n", "2024-07-22 14:59:15,240 - DEBUG - Sentence 495: tokens=214, similarity=0.4748494991371341, threshold=0.4\n", "2024-07-22 14:59:15,240 - DEBUG - Continuing cluster at sentence 495\n", "2024-07-22 14:59:15,240 - DEBUG - Sentence 496: tokens=224, similarity=0.4353439620857953, threshold=0.4\n", "2024-07-22 14:59:15,241 - DEBUG - Continuing cluster at sentence 496\n", "2024-07-22 14:59:15,241 - DEBUG - Sentence 497: tokens=242, similarity=0.7226247989109812, threshold=0.4\n", "2024-07-22 14:59:15,241 - DEBUG - Continuing cluster at sentence 497\n", "2024-07-22 14:59:15,241 - DEBUG - Sentence 498: tokens=262, similarity=0.5628857224185202, threshold=0.4\n", "2024-07-22 14:59:15,242 - DEBUG - Continuing cluster at sentence 498\n", "2024-07-22 14:59:15,242 - DEBUG - Sentence 499: tokens=290, similarity=0.3672058378130788, threshold=0.4\n", "2024-07-22 14:59:15,242 - DEBUG - Appending cluster with 13 sentences, 290 tokens\n", "2024-07-22 14:59:15,242 - DEBUG - Sentence 510: tokens=226, similarity=0.4600746284465993, threshold=0.4\n", "2024-07-22 14:59:15,243 - DEBUG - Continuing cluster at sentence 510\n", "2024-07-22 14:59:15,243 - DEBUG - Sentence 511: tokens=237, similarity=0.6056319336604534, threshold=0.4\n", "2024-07-22 14:59:15,243 - DEBUG - Continuing cluster at sentence 511\n", "2024-07-22 14:59:15,243 - DEBUG - Sentence 512: tokens=256, similarity=0.5561594559800871, threshold=0.4\n", "2024-07-22 14:59:15,243 - DEBUG - Continuing cluster at sentence 512\n", "2024-07-22 14:59:15,244 - DEBUG - Sentence 513: tokens=282, similarity=0.5357206011384577, threshold=0.4\n", "2024-07-22 14:59:15,244 - DEBUG - Continuing cluster at sentence 513\n", "2024-07-22 14:59:15,244 - DEBUG - Sentence 514: tokens=313, similarity=0.5143308635199602, threshold=0.4\n", "2024-07-22 14:59:15,244 - DEBUG - Continuing cluster at sentence 514\n", "2024-07-22 14:59:15,245 - DEBUG - Sentence 515: tokens=335, similarity=0.44743688445202656, threshold=0.4\n", "2024-07-22 14:59:15,245 - DEBUG - Continuing cluster at sentence 515\n", "2024-07-22 14:59:15,245 - DEBUG - Sentence 516: tokens=343, similarity=0.46388665450474903, threshold=0.4\n", "2024-07-22 14:59:15,245 - DEBUG - Continuing cluster at sentence 516\n", "2024-07-22 14:59:15,245 - DEBUG - Sentence 517: tokens=358, similarity=0.4341810865141451, threshold=0.4\n", "2024-07-22 14:59:15,246 - DEBUG - Continuing cluster at sentence 517\n", "2024-07-22 14:59:15,246 - DEBUG - Sentence 518: tokens=384, similarity=0.3504337471445172, threshold=0.4\n", "2024-07-22 14:59:15,246 - DEBUG - Appending cluster with 19 sentences, 384 tokens\n", "2024-07-22 14:59:15,246 - DEBUG - Sentence 529: tokens=236, similarity=0.5185284419919565, threshold=0.4\n", "2024-07-22 14:59:15,247 - DEBUG - Continuing cluster at sentence 529\n", "2024-07-22 14:59:15,247 - DEBUG - Sentence 530: tokens=249, similarity=0.5260321685642948, threshold=0.4\n", "2024-07-22 14:59:15,247 - DEBUG - Continuing cluster at sentence 530\n", "2024-07-22 14:59:15,247 - DEBUG - Sentence 531: tokens=294, similarity=0.5915844079059693, threshold=0.4\n", "2024-07-22 14:59:15,248 - DEBUG - Continuing cluster at sentence 531\n", "2024-07-22 14:59:15,248 - DEBUG - Sentence 532: tokens=338, similarity=0.44383789477230806, threshold=0.4\n", "2024-07-22 14:59:15,248 - DEBUG - Continuing cluster at sentence 532\n", "2024-07-22 14:59:15,248 - DEBUG - Sentence 533: tokens=370, similarity=0.45036389942145527, threshold=0.4\n", "2024-07-22 14:59:15,248 - DEBUG - Continuing cluster at sentence 533\n", "2024-07-22 14:59:15,249 - DEBUG - Sentence 534: tokens=411, similarity=0.5363754659826697, threshold=0.4\n", "2024-07-22 14:59:15,249 - DEBUG - Continuing cluster at sentence 534\n", "2024-07-22 14:59:15,249 - DEBUG - Sentence 535: tokens=427, similarity=0.4551482542644171, threshold=0.4\n", "2024-07-22 14:59:15,249 - DEBUG - Continuing cluster at sentence 535\n", "2024-07-22 14:59:15,250 - DEBUG - Sentence 536: tokens=452, similarity=0.5284103625637189, threshold=0.4\n", "2024-07-22 14:59:15,250 - DEBUG - Continuing cluster at sentence 536\n", "2024-07-22 14:59:15,250 - DEBUG - Sentence 537: tokens=458, similarity=0.4328378647696075, threshold=0.4\n", "2024-07-22 14:59:15,250 - DEBUG - Continuing cluster at sentence 537\n", "2024-07-22 14:59:15,250 - DEBUG - Sentence 538: tokens=520, similarity=0.4458385068909444, threshold=0.4\n", "2024-07-22 14:59:15,251 - DEBUG - Continuing cluster at sentence 538\n", "2024-07-22 14:59:15,251 - DEBUG - Sentence 539: tokens=536, similarity=0.44218487901755543, threshold=0.4\n", "2024-07-22 14:59:15,251 - DEBUG - Continuing cluster at sentence 539\n", "2024-07-22 14:59:15,251 - DEBUG - Sentence 540: tokens=570, similarity=0.557796414697133, threshold=0.4\n", "2024-07-22 14:59:15,251 - DEBUG - Continuing cluster at sentence 540\n", "2024-07-22 14:59:15,252 - DEBUG - Sentence 541: tokens=608, similarity=0.6661512475870763, threshold=0.4\n", "2024-07-22 14:59:15,252 - DEBUG - Cluster exceeds max size (608 > 600), splitting\n", "2024-07-22 14:59:15,252 - DEBUG - Split result: 22 sentences in new cluster, 1 sentences remaining\n", "2024-07-22 14:59:15,252 - DEBUG - Sentence 548: tokens=202, similarity=0.5425003267019423, threshold=0.4\n", "2024-07-22 14:59:15,253 - DEBUG - Continuing cluster at sentence 548\n", "2024-07-22 14:59:15,253 - DEBUG - Sentence 549: tokens=216, similarity=0.4985606629877001, threshold=0.4\n", "2024-07-22 14:59:15,253 - DEBUG - Continuing cluster at sentence 549\n", "2024-07-22 14:59:15,253 - DEBUG - Sentence 550: tokens=242, similarity=0.6059145508228561, threshold=0.4\n", "2024-07-22 14:59:15,254 - DEBUG - Continuing cluster at sentence 550\n", "2024-07-22 14:59:15,254 - DEBUG - Sentence 551: tokens=255, similarity=0.5264835328199838, threshold=0.4\n", "2024-07-22 14:59:15,254 - DEBUG - Continuing cluster at sentence 551\n", "2024-07-22 14:59:15,254 - DEBUG - Sentence 552: tokens=281, similarity=0.4088725827940898, threshold=0.4\n", "2024-07-22 14:59:15,255 - DEBUG - Continuing cluster at sentence 552\n", "2024-07-22 14:59:15,255 - DEBUG - Sentence 553: tokens=299, similarity=0.38875909582049145, threshold=0.4\n", "2024-07-22 14:59:15,255 - DEBUG - Appending cluster with 13 sentences, 299 tokens\n", "2024-07-22 14:59:15,255 - DEBUG - Sentence 565: tokens=215, similarity=0.4395431090660754, threshold=0.4\n", "2024-07-22 14:59:15,256 - DEBUG - Continuing cluster at sentence 565\n", "2024-07-22 14:59:15,256 - DEBUG - Sentence 566: tokens=235, similarity=0.4128316827494005, threshold=0.4\n", "2024-07-22 14:59:15,256 - DEBUG - Continuing cluster at sentence 566\n", "2024-07-22 14:59:15,256 - DEBUG - Sentence 567: tokens=245, similarity=0.42662357266716294, threshold=0.4\n", "2024-07-22 14:59:15,257 - DEBUG - Continuing cluster at sentence 567\n", "2024-07-22 14:59:15,257 - DEBUG - Sentence 568: tokens=269, similarity=0.48234193565953387, threshold=0.4\n", "2024-07-22 14:59:15,257 - DEBUG - Continuing cluster at sentence 568\n", "2024-07-22 14:59:15,257 - DEBUG - Sentence 569: tokens=304, similarity=0.5175235184423236, threshold=0.4\n", "2024-07-22 14:59:15,257 - DEBUG - Continuing cluster at sentence 569\n", "2024-07-22 14:59:15,258 - DEBUG - Sentence 570: tokens=346, similarity=0.5274906532209113, threshold=0.4\n", "2024-07-22 14:59:15,258 - DEBUG - Continuing cluster at sentence 570\n", "2024-07-22 14:59:15,258 - DEBUG - Sentence 571: tokens=367, similarity=0.5815690862693188, threshold=0.4\n", "2024-07-22 14:59:15,258 - DEBUG - Continuing cluster at sentence 571\n", "2024-07-22 14:59:15,258 - DEBUG - Sentence 572: tokens=421, similarity=0.5861066390814476, threshold=0.4\n", "2024-07-22 14:59:15,259 - DEBUG - Continuing cluster at sentence 572\n", "2024-07-22 14:59:15,259 - DEBUG - Sentence 573: tokens=439, similarity=0.6826113824503266, threshold=0.4\n", "2024-07-22 14:59:15,259 - DEBUG - Continuing cluster at sentence 573\n", "2024-07-22 14:59:15,259 - DEBUG - Sentence 574: tokens=451, similarity=0.6043926989874101, threshold=0.4\n", "2024-07-22 14:59:15,260 - DEBUG - Continuing cluster at sentence 574\n", "2024-07-22 14:59:15,260 - DEBUG - Sentence 575: tokens=467, similarity=0.62596042612366, threshold=0.4\n", "2024-07-22 14:59:15,260 - DEBUG - Continuing cluster at sentence 575\n", "2024-07-22 14:59:15,260 - DEBUG - Sentence 576: tokens=491, similarity=0.5951306105825398, threshold=0.4\n", "2024-07-22 14:59:15,260 - DEBUG - Continuing cluster at sentence 576\n", "2024-07-22 14:59:15,261 - DEBUG - Sentence 577: tokens=512, similarity=0.48540335163009773, threshold=0.4\n", "2024-07-22 14:59:15,261 - DEBUG - Continuing cluster at sentence 577\n", "2024-07-22 14:59:15,261 - DEBUG - Sentence 578: tokens=542, similarity=0.4308825556560843, threshold=0.4\n", "2024-07-22 14:59:15,261 - DEBUG - Continuing cluster at sentence 578\n", "2024-07-22 14:59:15,262 - DEBUG - Sentence 579: tokens=553, similarity=0.5272521409135607, threshold=0.4\n", "2024-07-22 14:59:15,262 - DEBUG - Continuing cluster at sentence 579\n", "2024-07-22 14:59:15,262 - DEBUG - Sentence 580: tokens=583, similarity=0.5194102693476026, threshold=0.4\n", "2024-07-22 14:59:15,262 - DEBUG - Continuing cluster at sentence 580\n", "2024-07-22 14:59:15,262 - DEBUG - Sentence 581: tokens=596, similarity=0.5184934720925769, threshold=0.4\n", "2024-07-22 14:59:15,263 - DEBUG - Continuing cluster at sentence 581\n", "2024-07-22 14:59:15,263 - DEBUG - Sentence 582: tokens=617, similarity=0.5107191506839535, threshold=0.4\n", "2024-07-22 14:59:15,263 - DEBUG - Cluster exceeds max size (617 > 600), splitting\n", "2024-07-22 14:59:15,263 - DEBUG - Split result: 28 sentences in new cluster, 1 sentences remaining\n", "2024-07-22 14:59:15,264 - DEBUG - Sentence 591: tokens=223, similarity=0.5058561613453841, threshold=0.4\n", "2024-07-22 14:59:15,264 - DEBUG - Continuing cluster at sentence 591\n", "2024-07-22 14:59:15,264 - DEBUG - Sentence 592: tokens=235, similarity=0.5698441974367017, threshold=0.4\n", "2024-07-22 14:59:15,264 - DEBUG - Continuing cluster at sentence 592\n", "2024-07-22 14:59:15,265 - DEBUG - Sentence 593: tokens=256, similarity=0.4731377055205701, threshold=0.4\n", "2024-07-22 14:59:15,265 - DEBUG - Continuing cluster at sentence 593\n", "2024-07-22 14:59:15,265 - DEBUG - Sentence 594: tokens=262, similarity=0.5596902259592775, threshold=0.4\n", "2024-07-22 14:59:15,265 - DEBUG - Continuing cluster at sentence 594\n", "2024-07-22 14:59:15,265 - DEBUG - Sentence 595: tokens=292, similarity=0.4379086434183358, threshold=0.4\n", "2024-07-22 14:59:15,266 - DEBUG - Continuing cluster at sentence 595\n", "2024-07-22 14:59:15,266 - DEBUG - Sentence 596: tokens=308, similarity=0.40213769773179303, threshold=0.4\n", "2024-07-22 14:59:15,266 - DEBUG - Continuing cluster at sentence 596\n", "2024-07-22 14:59:15,266 - DEBUG - Sentence 597: tokens=324, similarity=0.5412144037624282, threshold=0.4\n", "2024-07-22 14:59:15,266 - DEBUG - Continuing cluster at sentence 597\n", "2024-07-22 14:59:15,267 - DEBUG - Sentence 598: tokens=333, similarity=0.35448759799105045, threshold=0.4\n", "2024-07-22 14:59:15,267 - DEBUG - Appending cluster with 17 sentences, 333 tokens\n", "2024-07-22 14:59:15,267 - DEBUG - Sentence 606: tokens=203, similarity=0.6353091885431104, threshold=0.4\n", "2024-07-22 14:59:15,267 - DEBUG - Continuing cluster at sentence 606\n", "2024-07-22 14:59:15,268 - DEBUG - Sentence 607: tokens=219, similarity=0.5071568617653666, threshold=0.4\n", "2024-07-22 14:59:15,268 - DEBUG - Continuing cluster at sentence 607\n", "2024-07-22 14:59:15,268 - DEBUG - Sentence 608: tokens=234, similarity=0.4180070602922289, threshold=0.4\n", "2024-07-22 14:59:15,268 - DEBUG - Continuing cluster at sentence 608\n", "2024-07-22 14:59:15,269 - DEBUG - Sentence 609: tokens=253, similarity=0.4825855405531422, threshold=0.4\n", "2024-07-22 14:59:15,269 - DEBUG - Continuing cluster at sentence 609\n", "2024-07-22 14:59:15,269 - DEBUG - Sentence 610: tokens=264, similarity=0.5328269682723328, threshold=0.4\n", "2024-07-22 14:59:15,269 - DEBUG - Continuing cluster at sentence 610\n", "2024-07-22 14:59:15,270 - DEBUG - Sentence 611: tokens=280, similarity=0.6166947507988589, threshold=0.4\n", "2024-07-22 14:59:15,270 - DEBUG - Continuing cluster at sentence 611\n", "2024-07-22 14:59:15,270 - DEBUG - Sentence 612: tokens=295, similarity=0.5081171302928448, threshold=0.4\n", "2024-07-22 14:59:15,270 - DEBUG - Continuing cluster at sentence 612\n", "2024-07-22 14:59:15,270 - DEBUG - Sentence 613: tokens=336, similarity=0.3589485743674334, threshold=0.4\n", "2024-07-22 14:59:15,271 - DEBUG - Appending cluster with 15 sentences, 336 tokens\n", "2024-07-22 14:59:15,271 - DEBUG - Sentence 625: tokens=204, similarity=0.5811476644612739, threshold=0.4\n", "2024-07-22 14:59:15,271 - DEBUG - Continuing cluster at sentence 625\n", "2024-07-22 14:59:15,271 - DEBUG - Sentence 626: tokens=211, similarity=0.45261231259501644, threshold=0.4\n", "2024-07-22 14:59:15,272 - DEBUG - Continuing cluster at sentence 626\n", "2024-07-22 14:59:15,272 - DEBUG - Sentence 627: tokens=229, similarity=0.5839128153492346, threshold=0.4\n", "2024-07-22 14:59:15,272 - DEBUG - Continuing cluster at sentence 627\n", "2024-07-22 14:59:15,272 - DEBUG - Sentence 628: tokens=261, similarity=0.5493043131101605, threshold=0.4\n", "2024-07-22 14:59:15,272 - DEBUG - Continuing cluster at sentence 628\n", "2024-07-22 14:59:15,273 - DEBUG - Sentence 629: tokens=277, similarity=0.4611652528972161, threshold=0.4\n", "2024-07-22 14:59:15,273 - DEBUG - Continuing cluster at sentence 629\n", "2024-07-22 14:59:15,273 - DEBUG - Sentence 630: tokens=281, similarity=0.538189404295454, threshold=0.4\n", "2024-07-22 14:59:15,273 - DEBUG - Continuing cluster at sentence 630\n", "2024-07-22 14:59:15,274 - DEBUG - Sentence 631: tokens=290, similarity=0.49893021625453454, threshold=0.4\n", "2024-07-22 14:59:15,274 - DEBUG - Continuing cluster at sentence 631\n", "2024-07-22 14:59:15,274 - DEBUG - Sentence 632: tokens=298, similarity=0.3815081121227315, threshold=0.4\n", "2024-07-22 14:59:15,274 - DEBUG - Appending cluster with 19 sentences, 298 tokens\n", "2024-07-22 14:59:15,275 - DEBUG - Sentence 643: tokens=222, similarity=0.678008861884718, threshold=0.4\n", "2024-07-22 14:59:15,275 - DEBUG - Continuing cluster at sentence 643\n", "2024-07-22 14:59:15,275 - DEBUG - Sentence 644: tokens=252, similarity=0.5270548468269096, threshold=0.4\n", "2024-07-22 14:59:15,275 - DEBUG - Continuing cluster at sentence 644\n", "2024-07-22 14:59:15,275 - DEBUG - Sentence 645: tokens=273, similarity=0.5368322247484043, threshold=0.4\n", "2024-07-22 14:59:15,276 - DEBUG - Continuing cluster at sentence 645\n", "2024-07-22 14:59:15,276 - DEBUG - Sentence 646: tokens=292, similarity=0.32692004628575044, threshold=0.4\n", "2024-07-22 14:59:15,276 - DEBUG - Appending cluster with 14 sentences, 292 tokens\n", "2024-07-22 14:59:15,276 - DEBUG - Sentence 657: tokens=221, similarity=0.5686494155110189, threshold=0.4\n", "2024-07-22 14:59:15,277 - DEBUG - Continuing cluster at sentence 657\n", "2024-07-22 14:59:15,277 - DEBUG - Sentence 658: tokens=231, similarity=0.4104067167143811, threshold=0.4\n", "2024-07-22 14:59:15,277 - DEBUG - Continuing cluster at sentence 658\n", "2024-07-22 14:59:15,277 - DEBUG - Sentence 659: tokens=254, similarity=0.40679536696247975, threshold=0.4\n", "2024-07-22 14:59:15,277 - DEBUG - Continuing cluster at sentence 659\n", "2024-07-22 14:59:15,278 - DEBUG - Sentence 660: tokens=279, similarity=0.4704176942757385, threshold=0.4\n", "2024-07-22 14:59:15,278 - DEBUG - Continuing cluster at sentence 660\n", "2024-07-22 14:59:15,278 - DEBUG - Sentence 661: tokens=293, similarity=0.5148059144414128, threshold=0.4\n", "2024-07-22 14:59:15,278 - DEBUG - Continuing cluster at sentence 661\n", "2024-07-22 14:59:15,279 - DEBUG - Sentence 662: tokens=321, similarity=0.6379852396566636, threshold=0.4\n", "2024-07-22 14:59:15,279 - DEBUG - Continuing cluster at sentence 662\n", "2024-07-22 14:59:15,279 - DEBUG - Sentence 663: tokens=347, similarity=0.5487905074955335, threshold=0.4\n", "2024-07-22 14:59:15,279 - DEBUG - Continuing cluster at sentence 663\n", "2024-07-22 14:59:15,279 - DEBUG - Sentence 664: tokens=356, similarity=0.45181445785216856, threshold=0.4\n", "2024-07-22 14:59:15,280 - DEBUG - Continuing cluster at sentence 664\n", "2024-07-22 14:59:15,280 - DEBUG - Sentence 665: tokens=377, similarity=0.5439719155052428, threshold=0.4\n", "2024-07-22 14:59:15,280 - DEBUG - Continuing cluster at sentence 665\n", "2024-07-22 14:59:15,280 - DEBUG - Sentence 666: tokens=406, similarity=0.5407403177338321, threshold=0.4\n", "2024-07-22 14:59:15,281 - DEBUG - Continuing cluster at sentence 666\n", "2024-07-22 14:59:15,281 - DEBUG - Sentence 667: tokens=424, similarity=0.5475939398810685, threshold=0.4\n", "2024-07-22 14:59:15,281 - DEBUG - Continuing cluster at sentence 667\n", "2024-07-22 14:59:15,281 - DEBUG - Sentence 668: tokens=462, similarity=0.3455888306472621, threshold=0.4\n", "2024-07-22 14:59:15,282 - DEBUG - Appending cluster with 22 sentences, 462 tokens\n", "2024-07-22 14:59:15,282 - DEBUG - Sentence 679: tokens=210, similarity=0.5884911418458523, threshold=0.4\n", "2024-07-22 14:59:15,282 - DEBUG - Continuing cluster at sentence 679\n", "2024-07-22 14:59:15,282 - DEBUG - Sentence 680: tokens=241, similarity=0.5576555057141244, threshold=0.4\n", "2024-07-22 14:59:15,282 - DEBUG - Continuing cluster at sentence 680\n", "2024-07-22 14:59:15,283 - DEBUG - Sentence 681: tokens=262, similarity=0.3882060535231201, threshold=0.4\n", "2024-07-22 14:59:15,283 - DEBUG - Appending cluster with 13 sentences, 262 tokens\n", "2024-07-22 14:59:15,283 - DEBUG - Sentence 693: tokens=217, similarity=0.5973117473209593, threshold=0.4\n", "2024-07-22 14:59:15,283 - DEBUG - Continuing cluster at sentence 693\n", "2024-07-22 14:59:15,284 - DEBUG - Sentence 694: tokens=239, similarity=0.3906506690009456, threshold=0.4\n", "2024-07-22 14:59:15,284 - DEBUG - Appending cluster with 13 sentences, 239 tokens\n", "2024-07-22 14:59:15,284 - DEBUG - Sentence 701: tokens=212, similarity=0.36474273273876445, threshold=0.4\n", "2024-07-22 14:59:15,284 - DEBUG - Appending cluster with 7 sentences, 212 tokens\n", "2024-07-22 14:59:15,285 - DEBUG - Sentence 710: tokens=209, similarity=0.612558282518181, threshold=0.4\n", "2024-07-22 14:59:15,285 - DEBUG - Continuing cluster at sentence 710\n", "2024-07-22 14:59:15,285 - DEBUG - Sentence 711: tokens=240, similarity=0.5083882823817032, threshold=0.4\n", "2024-07-22 14:59:15,285 - DEBUG - Continuing cluster at sentence 711\n", "2024-07-22 14:59:15,285 - DEBUG - Sentence 712: tokens=262, similarity=0.5162827282197167, threshold=0.4\n", "2024-07-22 14:59:15,286 - DEBUG - Continuing cluster at sentence 712\n", "2024-07-22 14:59:15,286 - DEBUG - Sentence 713: tokens=276, similarity=0.503235598167386, threshold=0.4\n", "2024-07-22 14:59:15,286 - DEBUG - Continuing cluster at sentence 713\n", "2024-07-22 14:59:15,286 - DEBUG - Sentence 714: tokens=302, similarity=0.37616717636969343, threshold=0.4\n", "2024-07-22 14:59:15,287 - DEBUG - Appending cluster with 13 sentences, 302 tokens\n", "2024-07-22 14:59:15,287 - DEBUG - Sentence 723: tokens=212, similarity=0.5616489728169699, threshold=0.4\n", "2024-07-22 14:59:15,287 - DEBUG - Continuing cluster at sentence 723\n", "2024-07-22 14:59:15,287 - DEBUG - Sentence 724: tokens=232, similarity=0.5176125582314252, threshold=0.4\n", "2024-07-22 14:59:15,287 - DEBUG - Continuing cluster at sentence 724\n", "2024-07-22 14:59:15,288 - DEBUG - Sentence 725: tokens=248, similarity=0.5411596320486742, threshold=0.4\n", "2024-07-22 14:59:15,288 - DEBUG - Continuing cluster at sentence 725\n", "2024-07-22 14:59:15,288 - DEBUG - Sentence 726: tokens=283, similarity=0.49754053215909544, threshold=0.4\n", "2024-07-22 14:59:15,288 - DEBUG - Continuing cluster at sentence 726\n", "2024-07-22 14:59:15,289 - DEBUG - Sentence 727: tokens=322, similarity=0.589012652726219, threshold=0.4\n", "2024-07-22 14:59:15,289 - DEBUG - Continuing cluster at sentence 727\n", "2024-07-22 14:59:15,289 - DEBUG - Sentence 728: tokens=344, similarity=0.3900644144621474, threshold=0.4\n", "2024-07-22 14:59:15,289 - DEBUG - Appending cluster with 14 sentences, 344 tokens\n", "2024-07-22 14:59:15,289 - DEBUG - Sentence 736: tokens=211, similarity=0.5460126454015243, threshold=0.4\n", "2024-07-22 14:59:15,290 - DEBUG - Continuing cluster at sentence 736\n", "2024-07-22 14:59:15,290 - DEBUG - Sentence 737: tokens=229, similarity=0.38270888692219784, threshold=0.4\n", "2024-07-22 14:59:15,290 - DEBUG - Appending cluster with 9 sentences, 229 tokens\n", "2024-07-22 14:59:15,291 - DEBUG - Sentence 745: tokens=234, similarity=0.514178916789668, threshold=0.4\n", "2024-07-22 14:59:15,291 - DEBUG - Continuing cluster at sentence 745\n", "2024-07-22 14:59:15,291 - DEBUG - Sentence 746: tokens=257, similarity=0.3575450845491077, threshold=0.4\n", "2024-07-22 14:59:15,291 - DEBUG - Appending cluster with 9 sentences, 257 tokens\n", "2024-07-22 14:59:15,291 - DEBUG - Appending cluster with 9 sentences, 222 tokens\n", "2024-07-22 14:59:15,292 - DEBUG - Processing cluster 0: 29 sentences, 627 tokens\n", "2024-07-22 14:59:15,292 - DEBUG - Cluster 0 exceeds max size, recursively clustering\n", "2024-07-22 14:59:15,292 - DEBUG - Increased threshold for recursive call: 0.4 -> 0.54\n", "2024-07-22 14:59:15,292 - DEBUG - Starting _cluster_recursively at depth 1 with 29 sentences\n", "2024-07-22 14:59:15,293 - DEBUG - Sentence 9: tokens=209, similarity=0.47044105531056113, threshold=0.54\n", "2024-07-22 14:59:15,294 - DEBUG - Appending cluster with 10 sentences, 209 tokens\n", "2024-07-22 14:59:15,294 - DEBUG - Sentence 19: tokens=219, similarity=0.4968226888758696, threshold=0.54\n", "2024-07-22 14:59:15,294 - DEBUG - Appending cluster with 10 sentences, 219 tokens\n", "2024-07-22 14:59:15,294 - DEBUG - Processing final cluster with 9 sentences\n", "2024-07-22 14:59:15,294 - DEBUG - Merging final cluster with previous\n", "2024-07-22 14:59:15,295 - DEBUG - Processing cluster 0: 10 sentences, 209 tokens\n", "2024-07-22 14:59:15,295 - DEBUG - Processing cluster 1: 19 sentences, 418 tokens\n", "2024-07-22 14:59:15,295 - DEBUG - Finished _cluster_recursively at depth 1, returning 2 clusters\n", "2024-07-22 14:59:15,295 - DEBUG - Processing cluster 1: 26 sentences, 600 tokens\n", "2024-07-22 14:59:15,296 - DEBUG - Processing cluster 2: 21 sentences, 498 tokens\n", "2024-07-22 14:59:15,296 - DEBUG - Processing cluster 3: 25 sentences, 422 tokens\n", "2024-07-22 14:59:15,296 - DEBUG - Processing cluster 4: 28 sentences, 589 tokens\n", "2024-07-22 14:59:15,296 - DEBUG - Processing cluster 5: 23 sentences, 597 tokens\n", "2024-07-22 14:59:15,297 - DEBUG - Processing cluster 6: 25 sentences, 580 tokens\n", "2024-07-22 14:59:15,297 - DEBUG - Processing cluster 7: 18 sentences, 526 tokens\n", "2024-07-22 14:59:15,297 - DEBUG - Processing cluster 8: 25 sentences, 588 tokens\n", "2024-07-22 14:59:15,297 - DEBUG - Processing cluster 9: 12 sentences, 270 tokens\n", "2024-07-22 14:59:15,298 - DEBUG - Processing cluster 10: 20 sentences, 330 tokens\n", "2024-07-22 14:59:15,298 - DEBUG - Processing cluster 11: 19 sentences, 341 tokens\n", "2024-07-22 14:59:15,298 - DEBUG - Processing cluster 12: 20 sentences, 397 tokens\n", "2024-07-22 14:59:15,298 - DEBUG - Processing cluster 13: 7 sentences, 210 tokens\n", "2024-07-22 14:59:15,299 - DEBUG - Processing cluster 14: 10 sentences, 210 tokens\n", "2024-07-22 14:59:15,299 - DEBUG - Processing cluster 15: 9 sentences, 240 tokens\n", "2024-07-22 14:59:15,299 - DEBUG - Processing cluster 16: 28 sentences, 600 tokens\n", "2024-07-22 14:59:15,299 - DEBUG - Processing cluster 17: 30 sentences, 598 tokens\n", "2024-07-22 14:59:15,300 - DEBUG - Processing cluster 18: 31 sentences, 598 tokens\n", "2024-07-22 14:59:15,300 - DEBUG - Processing cluster 19: 12 sentences, 312 tokens\n", "2024-07-22 14:59:15,300 - DEBUG - Processing cluster 20: 12 sentences, 337 tokens\n", "2024-07-22 14:59:15,300 - DEBUG - Processing cluster 21: 30 sentences, 562 tokens\n", "2024-07-22 14:59:15,301 - DEBUG - Processing cluster 22: 27 sentences, 590 tokens\n", "2024-07-22 14:59:15,301 - DEBUG - Processing cluster 23: 13 sentences, 290 tokens\n", "2024-07-22 14:59:15,301 - DEBUG - Processing cluster 24: 19 sentences, 384 tokens\n", "2024-07-22 14:59:15,301 - DEBUG - Processing cluster 25: 22 sentences, 570 tokens\n", "2024-07-22 14:59:15,301 - DEBUG - Processing cluster 26: 13 sentences, 299 tokens\n", "2024-07-22 14:59:15,302 - DEBUG - Processing cluster 27: 28 sentences, 596 tokens\n", "2024-07-22 14:59:15,302 - DEBUG - Processing cluster 28: 17 sentences, 333 tokens\n", "2024-07-22 14:59:15,302 - DEBUG - Processing cluster 29: 15 sentences, 336 tokens\n", "2024-07-22 14:59:15,302 - DEBUG - Processing cluster 30: 19 sentences, 298 tokens\n", "2024-07-22 14:59:15,302 - DEBUG - Processing cluster 31: 14 sentences, 292 tokens\n", "2024-07-22 14:59:15,303 - DEBUG - Processing cluster 32: 22 sentences, 462 tokens\n", "2024-07-22 14:59:15,303 - DEBUG - Processing cluster 33: 13 sentences, 262 tokens\n", "2024-07-22 14:59:15,303 - DEBUG - Processing cluster 34: 13 sentences, 239 tokens\n", "2024-07-22 14:59:15,303 - DEBUG - Processing cluster 35: 7 sentences, 212 tokens\n", "2024-07-22 14:59:15,304 - DEBUG - Processing cluster 36: 13 sentences, 302 tokens\n", "2024-07-22 14:59:15,304 - DEBUG - Processing cluster 37: 14 sentences, 344 tokens\n", "2024-07-22 14:59:15,304 - DEBUG - Processing cluster 38: 9 sentences, 229 tokens\n", "2024-07-22 14:59:15,304 - DEBUG - Processing cluster 39: 9 sentences, 257 tokens\n", "2024-07-22 14:59:15,304 - DEBUG - Processing cluster 40: 9 sentences, 222 tokens\n", "2024-07-22 14:59:15,305 - DEBUG - Finished _cluster_recursively at depth 0, returning 42 clusters\n", "2024-07-22 14:59:15,305 - DEBUG - Clustering resulted in 42 clusters\n", "2024-07-22 14:59:15,305 - DEBUG - Chunk 0: 938 characters, 209 tokens\n", "2024-07-22 14:59:15,305 - DEBUG - Chunk 0 content: 'What I Worked On\n", "\n", "February 2021\n", "\n", "Before college the two main things I worked on, outside of school, ...'\n", "2024-07-22 14:59:15,306 - DEBUG - Chunk 0 contains 10 sentences\n", "2024-07-22 14:59:15,306 - DEBUG - Chunk 1: 1886 characters, 418 tokens\n", "2024-07-22 14:59:15,306 - DEBUG - Chunk 1 content: 'You had to type programs on punch cards, then stack them in the card reader and press a button to lo...'\n", "2024-07-22 14:59:15,306 - DEBUG - Chunk 1 contains 19 sentences\n", "2024-07-22 14:59:15,307 - DEBUG - Chunk 2: 2698 characters, 600 tokens\n", "2024-07-22 14:59:15,307 - DEBUG - Chunk 2 content: 'There was only room in memory for about 2 pages of text, so he'd write 2 pages at a time and then pr...'\n", "2024-07-22 14:59:15,307 - DEBUG - Chunk 2 contains 26 sentences\n", "2024-07-22 14:59:15,307 - DEBUG - Chunk 3: 2274 characters, 498 tokens\n", "2024-07-22 14:59:15,308 - DEBUG - Chunk 3 content: 'You could take whatever classes you liked, and choose whatever you liked to put on your degree. I of...'\n", "2024-07-22 14:59:15,308 - DEBUG - Chunk 3 contains 21 sentences\n", "2024-07-22 14:59:15,308 - DEBUG - Chunk 4: 1951 characters, 422 tokens\n", "2024-07-22 14:59:15,308 - DEBUG - Chunk 4 content: 'Computer Science is an uneasy alliance between two halves, theory and systems. The theory people pro...'\n", "2024-07-22 14:59:15,308 - DEBUG - Chunk 4 contains 25 sentences\n", "2024-07-22 14:59:15,315 - DEBUG - Chunk 5: 2611 characters, 589 tokens\n", "2024-07-22 14:59:15,315 - DEBUG - Chunk 5 content: 'I had always liked looking at paintings. Could I make them? I had no idea. I'd never imagined it was...'\n", "2024-07-22 14:59:15,316 - DEBUG - Chunk 5 contains 28 sentences\n", "2024-07-22 14:59:15,316 - DEBUG - Chunk 6: 2831 characters, 597 tokens\n", "2024-07-22 14:59:15,316 - DEBUG - Chunk 6 content: 'RISD counted me as a transfer sophomore and said I had to do the foundation that summer. The foundat...'\n", "2024-07-22 14:59:15,317 - DEBUG - Chunk 6 contains 23 sentences\n", "2024-07-22 14:59:15,317 - DEBUG - Chunk 7: 2611 characters, 580 tokens\n", "2024-07-22 14:59:15,317 - DEBUG - Chunk 7 content: 'Painting still lives is different from painting people, because the subject, as its name suggests, c...'\n", "2024-07-22 14:59:15,317 - DEBUG - Chunk 7 contains 25 sentences\n", "2024-07-22 14:59:15,317 - DEBUG - Chunk 8: 2403 characters, 526 tokens\n", "2024-07-22 14:59:15,318 - DEBUG - Chunk 8 content: 'But Interleaf still had a few years to live yet. [5]\n", "\n", "Interleaf had done something pretty bold. Insp...'\n", "2024-07-22 14:59:15,318 - DEBUG - Chunk 8 contains 18 sentences\n", "2024-07-22 14:59:15,318 - DEBUG - Chunk 9: 2663 characters, 588 tokens\n", "2024-07-22 14:59:15,318 - DEBUG - Chunk 9 content: 'When I left to go back to RISD the next fall, I arranged to do freelance work for the group that did...'\n", "2024-07-22 14:59:15,318 - DEBUG - Chunk 9 contains 25 sentences\n", "2024-07-22 14:59:15,319 - DEBUG - Chunk 10: 1166 characters, 270 tokens\n", "2024-07-22 14:59:15,319 - DEBUG - Chunk 10 content: 'It wasn't much more than my current place, and New York was supposed to be where the artists were. S...'\n", "2024-07-22 14:59:15,319 - DEBUG - Chunk 10 contains 12 sentences\n", "2024-07-22 14:59:15,319 - DEBUG - Chunk 11: 1463 characters, 330 tokens\n", "2024-07-22 14:59:15,320 - DEBUG - Chunk 11 content: 'The best thing about New York for me was the presence of <PERSON><PERSON><PERSON> and <PERSON>. <PERSON><PERSON><PERSON> was a...'\n", "2024-07-22 14:59:15,320 - DEBUG - Chunk 11 contains 20 sentences\n", "2024-07-22 14:59:15,320 - DEBUG - Chunk 12: 1607 characters, 341 tokens\n", "2024-07-22 14:59:15,320 - DEBUG - Chunk 12 content: 'Art galleries didn't want to be online, and still don't, not the fancy ones. That's not how they sel...'\n", "2024-07-22 14:59:15,321 - DEBUG - Chunk 12 contains 19 sentences\n", "2024-07-22 14:59:15,321 - DEBUG - Chunk 13: 1826 characters, 397 tokens\n", "2024-07-22 14:59:15,321 - DEBUG - Chunk 13 content: 'What if we ran the software on the server, and let users control it by clicking on links? Then we'd ...'\n", "2024-07-22 14:59:15,321 - DEBUG - Chunk 13 contains 20 sentences\n", "2024-07-22 14:59:15,321 - DEBUG - Chunk 14: 983 characters, 210 tokens\n", "2024-07-22 14:59:15,322 - DEBUG - Chunk 14 content: 'So although <PERSON> had his graduate student stipend, I needed that seed funding to live on.\n", "\n", " We ori...'\n", "2024-07-22 14:59:15,322 - DEBUG - Chunk 14 contains 7 sentences\n", "2024-07-22 14:59:15,322 - DEBUG - Chunk 15: 954 characters, 210 tokens\n", "2024-07-22 14:59:15,322 - DEBUG - Chunk 15 content: 'In September, <PERSON> rebelled. \"We've been working on this for a month,\" he said, \"and it's still no...'\n", "2024-07-22 14:59:15,322 - DEBUG - Chunk 15 contains 10 sentences\n", "2024-07-22 14:59:15,323 - DEBUG - Chunk 16: 1073 characters, 240 tokens\n", "2024-07-22 14:59:15,323 - DEBUG - Chunk 16 content: 'We opened for business, with 6 stores, in January 1996. It was just as well we waited a few months, ...'\n", "2024-07-22 14:59:15,323 - DEBUG - Chunk 16 contains 9 sentences\n", "2024-07-22 14:59:15,323 - DEBUG - Chunk 17: 2667 characters, 600 tokens\n", "2024-07-22 14:59:15,323 - DEBUG - Chunk 17 content: 'We were determined to be the Microsoft Word, not the Interleaf. Which meant being easy to use and in...'\n", "2024-07-22 14:59:15,324 - DEBUG - Chunk 17 contains 28 sentences\n", "2024-07-22 14:59:15,324 - DEBUG - Chunk 18: 2632 characters, 598 tokens\n", "2024-07-22 14:59:15,324 - DEBUG - Chunk 18 content: 'You're growing 7x a year. Just don't hire too many more people and you'll soon be profitable, and th...'\n", "2024-07-22 14:59:15,324 - DEBUG - Chunk 18 contains 30 sentences\n", "2024-07-22 14:59:15,325 - DEBUG - Chunk 19: 2633 characters, 598 tokens\n", "2024-07-22 14:59:15,325 - DEBUG - Chunk 19 content: 'My options at that point were worth about $2 million a month. If I was leaving that kind of money on...'\n", "2024-07-22 14:59:15,325 - DEBUG - Chunk 19 contains 31 sentences\n", "2024-07-22 14:59:15,325 - DEBUG - Chunk 20: 1420 characters, 312 tokens\n", "2024-07-22 14:59:15,326 - DEBUG - Chunk 20 content: 'Why not let people edit code on our server through the browser, and then host the resulting applicat...'\n", "2024-07-22 14:59:15,326 - DEBUG - Chunk 20 contains 12 sentences\n", "2024-07-22 14:59:15,326 - DEBUG - Chunk 21: 1547 characters, 337 tokens\n", "2024-07-22 14:59:15,326 - DEBUG - Chunk 21 content: 'By then there was a name for the kind of company Viaweb was, an \"application service provider,\" or A...'\n", "2024-07-22 14:59:15,327 - DEBUG - Chunk 21 contains 12 sentences\n", "2024-07-22 14:59:15,327 - DEBUG - Chunk 22: 2578 characters, 562 tokens\n", "2024-07-22 14:59:15,327 - DEBUG - Chunk 22 content: 'I certainly did. So at the end of the summer <PERSON> and I switched to working on this new dialect of Li...'\n", "2024-07-22 14:59:15,327 - DEBUG - Chunk 22 contains 30 sentences\n", "2024-07-22 14:59:15,328 - DEBUG - Chunk 23: 2735 characters, 590 tokens\n", "2024-07-22 14:59:15,328 - DEBUG - Chunk 23 content: 'Viaweb and <PERSON> Combinator both seemed lame when we started them. I still get the glassy eye from stran...'\n", "2024-07-22 14:59:15,328 - DEBUG - Chunk 23 contains 27 sentences\n", "2024-07-22 14:59:15,328 - DEBUG - Chunk 24: 1358 characters, 290 tokens\n", "2024-07-22 14:59:15,328 - DEBUG - Chunk 24 content: 'One of my tricks for writing essays had always been to give talks. The prospect of having to stand u...'\n", "2024-07-22 14:59:15,329 - DEBUG - Chunk 24 contains 13 sentences\n", "2024-07-22 14:59:15,329 - DEBUG - Chunk 25: 1771 characters, 384 tokens\n", "2024-07-22 14:59:15,329 - DEBUG - Chunk 25 content: 'Screw the VCs who were taking so long to make up their minds. We'd start our own investment firm and...'\n", "2024-07-22 14:59:15,329 - DEBUG - Chunk 25 contains 19 sentences\n", "2024-07-22 14:59:15,329 - DEBUG - Chunk 26: 2634 characters, 570 tokens\n", "2024-07-22 14:59:15,330 - DEBUG - Chunk 26 content: 'But once again, this was not due to any particular insight on our part. We didn't know how VC firms ...'\n", "2024-07-22 14:59:15,330 - DEBUG - Chunk 26 contains 22 sentences\n", "2024-07-22 14:59:15,330 - DEBUG - Chunk 27: 1401 characters, 299 tokens\n", "2024-07-22 14:59:15,330 - DEBUG - Chunk 27 content: 'The deal for startups was based on a combination of the deal we did with <PERSON> ($10k for 10%) and w...'\n", "2024-07-22 14:59:15,331 - DEBUG - Chunk 27 contains 13 sentences\n", "2024-07-22 14:59:15,331 - DEBUG - Chunk 28: 2626 characters, 596 tokens\n", "2024-07-22 14:59:15,331 - DEBUG - Chunk 28 content: 'I had not originally intended <PERSON><PERSON> to be a full-time job. I was going to do three things: hack, write ...'\n", "2024-07-22 14:59:15,331 - DEBUG - Chunk 28 contains 28 sentences\n", "2024-07-22 14:59:15,331 - DEBUG - Chunk 29: 1422 characters, 333 tokens\n", "2024-07-22 14:59:15,332 - DEBUG - Chunk 29 content: 'I was haunted by something <PERSON> once said about companies: \"No one works harder than the boss....'\n", "2024-07-22 14:59:15,332 - DEBUG - Chunk 29 contains 17 sentences\n", "2024-07-22 14:59:15,332 - DEBUG - Chunk 30: 1410 characters, 336 tokens\n", "2024-07-22 14:59:15,332 - DEBUG - Chunk 30 content: 'In the summer of 2012 my mother had a stroke, and the cause turned out to be a blood clot caused by ...'\n", "2024-07-22 14:59:15,333 - DEBUG - Chunk 30 contains 15 sentences\n", "2024-07-22 14:59:15,333 - DEBUG - Chunk 31: 1269 characters, 298 tokens\n", "2024-07-22 14:59:15,333 - DEBUG - Chunk 31 content: 'She died on January 15, 2014. We knew this was coming, but it was still hard when it did.\n", "\n", " I kept w...'\n", "2024-07-22 14:59:15,333 - DEBUG - Chunk 31 contains 19 sentences\n", "2024-07-22 14:59:15,334 - DEBUG - Chunk 32: 1415 characters, 292 tokens\n", "2024-07-22 14:59:15,334 - DEBUG - Chunk 32 content: 'If you can choose what to work on, and you choose a project that's not the best one (or at least a g...'\n", "2024-07-22 14:59:15,334 - DEBUG - Chunk 32 contains 14 sentences\n", "2024-07-22 14:59:15,334 - DEBUG - Chunk 33: 2090 characters, 462 tokens\n", "2024-07-22 14:59:15,334 - DEBUG - Chunk 33 content: '<PERSON>'s 1960 Lisp did nothing more than interpret Lisp expressions. It was missing a lot of thing...'\n", "2024-07-22 14:59:15,335 - DEBUG - Chunk 33 contains 22 sentences\n", "2024-07-22 14:59:15,335 - DEBUG - Chunk 34: 1173 characters, 262 tokens\n", "2024-07-22 14:59:15,335 - DEBUG - Chunk 34 content: 'So I said no more essays till <PERSON> was done. But I told few people about <PERSON> while I was working on i...'\n", "2024-07-22 14:59:15,335 - DEBUG - Chunk 34 contains 13 sentences\n", "2024-07-22 14:59:15,336 - DEBUG - Chunk 35: 1102 characters, 239 tokens\n", "2024-07-22 14:59:15,336 - DEBUG - Chunk 35 content: 'So most of Bel was written in England.\n", "\n", " In the fall of 2019, Bel was finally finished. Like McCarth...'\n", "2024-07-22 14:59:15,336 - DEBUG - Chunk 35 contains 13 sentences\n", "2024-07-22 14:59:15,336 - DEBUG - Chunk 36: 957 characters, 212 tokens\n", "2024-07-22 14:59:15,337 - DEBUG - Chunk 36 content: '[2] Italian words for abstract concepts can nearly always be predicted from their English cognates (...'\n", "2024-07-22 14:59:15,337 - DEBUG - Chunk 36 contains 7 sentences\n", "2024-07-22 14:59:15,337 - DEBUG - Chunk 37: 1404 characters, 302 tokens\n", "2024-07-22 14:59:15,337 - DEBUG - Chunk 37 content: '[5] Interleaf was one of many companies that had smart people and built impressive technology, and y...'\n", "2024-07-22 14:59:15,338 - DEBUG - Chunk 37 contains 13 sentences\n", "2024-07-22 14:59:15,338 - DEBUG - Chunk 38: 1685 characters, 344 tokens\n", "2024-07-22 14:59:15,338 - DEBUG - Chunk 38 content: '[10] This was the first instance of what is now a familiar experience, and so was what happened next...'\n", "2024-07-22 14:59:15,339 - DEBUG - Chunk 38 contains 14 sentences\n", "2024-07-22 14:59:15,339 - DEBUG - Chunk 39: 975 characters, 229 tokens\n", "2024-07-22 14:59:15,340 - DEBUG - Chunk 39 content: 'Obviously software and venture capital will be, but who would have predicted that essay writing woul...'\n", "2024-07-22 14:59:15,340 - DEBUG - Chunk 39 contains 9 sentences\n", "2024-07-22 14:59:15,340 - DEBUG - Chunk 40: 1230 characters, 257 tokens\n", "2024-07-22 14:59:15,340 - DEBUG - Chunk 40 content: '[15] I've never liked the term \"deal flow,\" because it implies that the number of new startups at an...'\n", "2024-07-22 14:59:15,340 - DEBUG - Chunk 40 contains 9 sentences\n", "2024-07-22 14:59:15,341 - DEBUG - Chunk 41: 1048 characters, 222 tokens\n", "2024-07-22 14:59:15,341 - DEBUG - Chunk 41 content: '[18] The worst thing about leaving YC was not working with <PERSON> anymore. We'd been working on YC ...'\n", "2024-07-22 14:59:15,341 - DEBUG - Chunk 41 contains 9 sentences\n", "2024-07-22 14:59:15,341 - DEBUG - Final result: 42 text chunks\n"]}], "source": ["smart_chunks = smart_chunker.split_text(\"\"\"What I Worked On\n", "\n", "February 2021\n", "\n", "Before college the two main things I worked on, outside of school, were writing and programming. I didn't write essays. I wrote what beginning writers were supposed to write then, and probably still are: short stories. My stories were awful. They had hardly any plot, just characters with strong feelings, which I imagined made them deep.\n", "\n", "The first programs I tried writing were on the IBM 1401 that our school district used for what was then called \"data processing.\" This was in 9th grade, so I was 13 or 14. The school district's 1401 happened to be in the basement of our junior high school, and my friend <PERSON> and I got permission to use it. It was like a mini Bond villain's lair down there, with all these alien-looking machines — CPU, disk drives, printer, card reader — sitting up on a raised floor under bright fluorescent lights.\n", "\n", "The language we used was an early version of Fortran. You had to type programs on punch cards, then stack them in the card reader and press a button to load the program into memory and run it. The result would ordinarily be to print something on the spectacularly loud printer.\n", "\n", "I was puzzled by the 1401. I couldn't figure out what to do with it. And in retrospect there's not much I could have done with it. The only form of input to programs was data stored on punched cards, and I didn't have any data stored on punched cards. The only other option was to do things that didn't rely on any input, like calculate approximations of pi, but I didn't know enough math to do anything interesting of that type. So I'm not surprised I can't remember any programs I wrote, because they can't have done much. My clearest memory is of the moment I learned it was possible for programs not to terminate, when one of mine didn't. On a machine without time-sharing, this was a social as well as a technical error, as the data center manager's expression made clear.\n", "\n", "With microcomputers, everything changed. Now you could have a computer sitting right in front of you, on a desk, that could respond to your keystrokes as it was running instead of just churning through a stack of punch cards and then stopping. [1]\n", "\n", "The first of my friends to get a microcomputer built it himself. It was sold as a kit by Heathkit. I remember vividly how impressed and envious I felt watching him sitting in front of it, typing programs right into the computer.\n", "\n", "Computers were expensive in those days and it took me years of nagging before I convinced my father to buy one, a TRS-80, in about 1980. The gold standard then was the Apple II, but a TRS-80 was good enough. This was when I really started programming. I wrote simple games, a program to predict how high my model rockets would fly, and a word processor that my father used to write at least one book. There was only room in memory for about 2 pages of text, so he'd write 2 pages at a time and then print them out, but it was a lot better than a typewriter.\n", "\n", "Though I liked programming, I didn't plan to study it in college. In college I was going to study philosophy, which sounded much more powerful. It seemed, to my naive high school self, to be the study of the ultimate truths, compared to which the things studied in other fields would be mere domain knowledge. What I discovered when I got to college was that the other fields took up so much of the space of ideas that there wasn't much left for these supposed ultimate truths. All that seemed left for philosophy were edge cases that people in other fields felt could safely be ignored.\n", "\n", "I couldn't have put this into words when I was 18. All I knew at the time was that I kept taking philosophy courses and they kept being boring. So I decided to switch to AI.\n", "\n", "AI was in the air in the mid 1980s, but there were two things especially that made me want to work on it: a novel by <PERSON><PERSON><PERSON> called The Moon is a Harsh Mistress, which featured an intelligent computer called <PERSON>, and a PBS documentary that showed <PERSON> using SHRDLU. I haven't tried rereading The Moon is a Harsh Mistress, so I don't know how well it has aged, but when I read it I was drawn entirely into its world. It seemed only a matter of time before we'd have <PERSON>, and when I saw <PERSON><PERSON><PERSON> using SHRDLU, it seemed like that time would be a few years at most. All you had to do was teach SHRD<PERSON>U more words.\n", "\n", "There weren't any classes in AI at Cornell then, not even graduate classes, so I started trying to teach myself. Which meant learning Lisp, since in those days Lisp was regarded as the language of AI. The commonly used programming languages then were pretty primitive, and programmers' ideas correspondingly so. The default language at Cornell was a Pascal-like language called PL/I, and the situation was similar elsewhere. Learning Lisp expanded my concept of a program so fast that it was years before I started to have a sense of where the new limits were. This was more like it; this was what I had expected college to do. It wasn't happening in a class, like it was supposed to, but that was ok. For the next couple years I was on a roll. I knew what I was going to do.\n", "\n", "For my undergraduate thesis, I reverse-engineered SHRDLU. My God did I love working on that program. It was a pleasing bit of code, but what made it even more exciting was my belief — hard to imagine now, but not unique in 1985 — that it was already climbing the lower slopes of intelligence.\n", "\n", "I had gotten into a program at Cornell that didn't make you choose a major. You could take whatever classes you liked, and choose whatever you liked to put on your degree. I of course chose \"Artificial Intelligence.\" When I got the actual physical diploma, I was dismayed to find that the quotes had been included, which made them read as scare-quotes. At the time this bothered me, but now it seems amusingly accurate, for reasons I was about to discover.\n", "\n", "I applied to 3 grad schools: MIT and Yale, which were renowned for AI at the time, and Harvard, which I'd visited because <PERSON> went there, and was also home to <PERSON>, who'd invented the type of parser I used in my SHRDLU clone. Only Harvard accepted me, so that was where I went.\n", "\n", "I don't remember the moment it happened, or if there even was a specific moment, but during the first year of grad school I realized that AI, as practiced at the time, was a hoax. By which I mean the sort of AI in which a program that's told \"the dog is sitting on the chair\" translates this into some formal representation and adds it to the list of things it knows.\n", "\n", "What these programs really showed was that there's a subset of natural language that's a formal language. But a very proper subset. It was clear that there was an unbridgeable gap between what they could do and actually understanding natural language. It was not, in fact, simply a matter of teaching SHRDLU more words. That whole way of doing AI, with explicit data structures representing concepts, was not going to work. Its brokenness did, as so often happens, generate a lot of opportunities to write papers about various band-aids that could be applied to it, but it was never going to get us Mike.\n", "\n", "So I looked around to see what I could salvage from the wreckage of my plans, and there was <PERSON><PERSON>. I knew from experience that Lisp was interesting for its own sake and not just for its association with AI, even though that was the main reason people cared about it at the time. So I decided to focus on Lisp. In fact, I decided to write a book about Lisp hacking. It's scary to think how little I knew about Lisp hacking when I started writing that book. But there's nothing like writing a book about something to help you learn it. The book, On Lisp, wasn't published till 1993, but I wrote much of it in grad school.\n", "\n", "Computer Science is an uneasy alliance between two halves, theory and systems. The theory people prove things, and the systems people build things. I wanted to build things. I had plenty of respect for theory — indeed, a sneaking suspicion that it was the more admirable of the two halves — but building things seemed so much more exciting.\n", "\n", "The problem with systems work, though, was that it didn't last. Any program you wrote today, no matter how good, would be obsolete in a couple decades at best. People might mention your software in footnotes, but no one would actually use it. And indeed, it would seem very feeble work. Only people with a sense of the history of the field would even realize that, in its time, it had been good.\n", "\n", "There were some surplus Xerox Dandelions floating around the computer lab at one point. Anyone who wanted one to play around with could have one. I was briefly tempted, but they were so slow by present standards; what was the point? No one else wanted one either, so off they went. That was what happened to systems work.\n", "\n", "I wanted not just to build things, but to build things that would last.\n", "\n", "In this dissatisfied state I went in 1988 to visit <PERSON> at CMU, where he was in grad school. One day I went to visit the Carnegie Institute, where I'd spent a lot of time as a kid. While looking at a painting there I realized something that might seem obvious, but was a big surprise to me. There, right on the wall, was something you could make that would last. Paintings didn't become obsolete. Some of the best ones were hundreds of years old.\n", "\n", "And moreover this was something you could make a living doing. Not as easily as you could by writing software, of course, but I thought if you were really industrious and lived really cheaply, it had to be possible to make enough to survive. And as an artist you could be truly independent. You wouldn't have a boss, or even need to get research funding.\n", "\n", "I had always liked looking at paintings. Could I make them? I had no idea. I'd never imagined it was even possible. I knew intellectually that people made art — that it didn't just appear spontaneously — but it was as if the people who made it were a different species. They either lived long ago or were mysterious geniuses doing strange things in profiles in Life magazine. The idea of actually being able to make art, to put that verb before that noun, seemed almost miraculous.\n", "\n", "That fall I started taking art classes at Harvard. Grad students could take classes in any department, and my advisor, <PERSON>, was very easy going. If he even knew about the strange classes I was taking, he never said anything.\n", "\n", "So now I was in a PhD program in computer science, yet planning to be an artist, yet also genuinely in love with Lisp hacking and working away at On Lisp. In other words, like many a grad student, I was working energetically on multiple projects that were not my thesis.\n", "\n", "I didn't see a way out of this situation. I didn't want to drop out of grad school, but how else was I going to get out? I remember when my friend <PERSON> got kicked out of Cornell for writing the internet worm of 1988, I was envious that he'd found such a spectacular way to get out of grad school.\n", "\n", "Then one day in April 1990 a crack appeared in the wall. I ran into professor <PERSON><PERSON><PERSON> and he asked if I was far enough along to graduate that June. I didn't have a word of my dissertation written, but in what must have been the quickest bit of thinking in my life, I decided to take a shot at writing one in the 5 weeks or so that remained before the deadline, reusing parts of On Lisp where I could, and I was able to respond, with no perceptible delay \"Yes, I think so. I'll give you something to read in a few days.\"\n", "\n", "I picked applications of continuations as the topic. In retrospect I should have written about macros and embedded languages. There's a whole world there that's barely been explored. But all I wanted was to get out of grad school, and my rapidly written dissertation sufficed, just barely.\n", "\n", "Meanwhile I was applying to art schools. I applied to two: RISD in the US, and the Accademia di Belli Arti in Florence, which, because it was the oldest art school, I imagined would be good. RISD accepted me, and I never heard back from the Accademia, so off to Providence I went.\n", "\n", "I'd applied for the BFA program at RISD, which meant in effect that I had to go to college again. This was not as strange as it sounds, because I was only 25, and art schools are full of people of different ages. RISD counted me as a transfer sophomore and said I had to do the foundation that summer. The foundation means the classes that everyone has to take in fundamental subjects like drawing, color, and design.\n", "\n", "Toward the end of the summer I got a big surprise: a letter from the Accademia, which had been delayed because they'd sent it to Cambridge England instead of Cambridge Massachusetts, inviting me to take the entrance exam in Florence that fall. This was now only weeks away. My nice landlady let me leave my stuff in her attic. I had some money saved from consulting work I'd done in grad school; there was probably enough to last a year if I lived cheaply. Now all I had to do was learn Italian.\n", "\n", "Only stranieri (foreigners) had to take this entrance exam. In retrospect it may well have been a way of excluding them, because there were so many stranieri attracted by the idea of studying art in Florence that the Italian students would otherwise have been outnumbered. I was in decent shape at painting and drawing from the RISD foundation that summer, but I still don't know how I managed to pass the written exam. I remember that I answered the essay question by writing about <PERSON><PERSON><PERSON>, and that I cranked up the intellectual level as high as I could to make the most of my limited vocabulary. [2]\n", "\n", "I'm only up to age 25 and already there are such conspicuous patterns. Here I was, yet again about to attend some august institution in the hopes of learning about some prestigious subject, and yet again about to be disappointed. The students and faculty in the painting department at the Accademia were the nicest people you could imagine, but they had long since arrived at an arrangement whereby the students wouldn't require the faculty to teach anything, and in return the faculty wouldn't require the students to learn anything. And at the same time all involved would adhere outwardly to the conventions of a 19th century atelier. We actually had one of those little stoves, fed with kindling, that you see in 19th century studio paintings, and a nude model sitting as close to it as possible without getting burned. Except hardly anyone else painted her besides me. The rest of the students spent their time chatting or occasionally trying to imitate things they'd seen in American art magazines.\n", "\n", "Our model turned out to live just down the street from me. She made a living from a combination of modelling and making fakes for a local antique dealer. She'd copy an obscure old painting out of a book, and then he'd take the copy and maltreat it to make it look old. [3]\n", "\n", "While I was a student at the Accademia I started painting still lives in my bedroom at night. These paintings were tiny, because the room was, and because I painted them on leftover scraps of canvas, which was all I could afford at the time. Painting still lives is different from painting people, because the subject, as its name suggests, can't move. People can't sit for more than about 15 minutes at a time, and when they do they don't sit very still. So the traditional m.o. for painting people is to know how to paint a generic person, which you then modify to match the specific person you're painting. Whereas a still life you can, if you want, copy pixel by pixel from what you're seeing. You don't want to stop there, of course, or you get merely photographic accuracy, and what makes a still life interesting is that it's been through a head. You want to emphasize the visual cues that tell you, for example, that the reason the color changes suddenly at a certain point is that it's the edge of an object. By subtly emphasizing such things you can make paintings that are more realistic than photographs not just in some metaphorical sense, but in the strict information-theoretic sense. [4]\n", "\n", "I liked painting still lives because I was curious about what I was seeing. In everyday life, we aren't consciously aware of much we're seeing. Most visual perception is handled by low-level processes that merely tell your brain \"that's a water droplet\" without telling you details like where the lightest and darkest points are, or \"that's a bush\" without telling you the shape and position of every leaf. This is a feature of brains, not a bug. In everyday life it would be distracting to notice every leaf on every bush. But when you have to paint something, you have to look more closely, and when you do there's a lot to see. You can still be noticing new things after days of trying to paint something people usually take for granted, just as you can after days of trying to write an essay about something people usually take for granted.\n", "\n", "This is not the only way to paint. I'm not 100% sure it's even a good way to paint. But it seemed a good enough bet to be worth trying.\n", "\n", "Our teacher, professor <PERSON><PERSON><PERSON>, was a nice guy. He could see I worked hard, and gave me a good grade, which he wrote down in a sort of passport each student had. But the Accademia wasn't teaching me anything except Italian, and my money was running out, so at the end of the first year I went back to the US.\n", "\n", "I wanted to go back to RISD, but I was now broke and RISD was very expensive, so I decided to get a job for a year and then return to RISD the next fall. I got one at a company called Interleaf, which made software for creating documents. You mean like Microsoft Word? Exactly. That was how I learned that low end software tends to eat high end software. But Interleaf still had a few years to live yet. [5]\n", "\n", "Interleaf had done something pretty bold. Inspired by Emacs, they'd added a scripting language, and even made the scripting language a dialect of Lisp. Now they wanted a Lisp hacker to write things in it. This was the closest thing I've had to a normal job, and I hereby apologize to my boss and coworkers, because I was a bad employee. Their Lisp was the thinnest icing on a giant C cake, and since I didn't know C and didn't want to learn it, I never understood most of the software. Plus I was terribly irresponsible. This was back when a programming job meant showing up every day during certain working hours. That seemed unnatural to me, and on this point the rest of the world is coming around to my way of thinking, but at the time it caused a lot of friction. Toward the end of the year I spent much of my time surreptitiously working on On Lisp, which I had by this time gotten a contract to publish.\n", "\n", "The good part was that I got paid huge amounts of money, especially by art student standards. In Florence, after paying my part of the rent, my budget for everything else had been $7 a day. Now I was getting paid more than 4 times that every hour, even when I was just sitting in a meeting. By living cheaply I not only managed to save enough to go back to RISD, but also paid off my college loans.\n", "\n", "I learned some useful things at Interleaf, though they were mostly about what not to do. I learned that it's better for technology companies to be run by product people than sales people (though sales is a real skill and people who are good at it are really good at it), that it leads to bugs when code is edited by too many people, that cheap office space is no bargain if it's depressing, that planned meetings are inferior to corridor conversations, that big, bureaucratic customers are a dangerous source of money, and that there's not much overlap between conventional office hours and the optimal time for hacking, or conventional offices and the optimal place for it.\n", "\n", "But the most important thing I learned, and which I used in both Viaweb and Y Combinator, is that the low end eats the high end: that it's good to be the \"entry level\" option, even though that will be less prestigious, because if you're not, someone else will be, and will squash you against the ceiling. Which in turn means that prestige is a danger sign.\n", "\n", "When I left to go back to RISD the next fall, I arranged to do freelance work for the group that did projects for customers, and this was how I survived for the next several years. When I came back to visit for a project later on, someone told me about a new thing called HTML, which was, as he described it, a derivative of SGML. Markup language enthusiasts were an occupational hazard at Interleaf and I ignored him, but this HTML thing later became a big part of my life.\n", "\n", "In the fall of 1992 I moved back to Providence to continue at RISD. The foundation had merely been intro stuff, and the Accademia had been a (very civilized) joke. Now I was going to see what real art school was like. But alas it was more like the Accademia than not. Better organized, certainly, and a lot more expensive, but it was now becoming clear that art school did not bear the same relationship to art that medical school bore to medicine. At least not the painting department. The textile department, which my next door neighbor belonged to, seemed to be pretty rigorous. No doubt illustration and architecture were too. But painting was post-rigorous. Painting students were supposed to express themselves, which to the more worldly ones meant to try to cook up some sort of distinctive signature style.\n", "\n", "A signature style is the visual equivalent of what in show business is known as a \"schtick\": something that immediately identifies the work as yours and no one else's. For example, when you see a painting that looks like a certain kind of cartoon, you know it's by <PERSON>. So if you see a big painting of this type hanging in the apartment of a hedge fund manager, you know he paid millions of dollars for it. That's not always why artists have a signature style, but it's usually why buyers pay a lot for such work. [6]\n", "\n", "There were plenty of earnest students too: kids who \"could draw\" in high school, and now had come to what was supposed to be the best art school in the country, to learn to draw even better. They tended to be confused and demoralized by what they found at RISD, but they kept going, because painting was what they did. I was not one of the kids who could draw in high school, but at RISD I was definitely closer to their tribe than the tribe of signature style seekers.\n", "\n", "I learned a lot in the color class I took at RISD, but otherwise I was basically teaching myself to paint, and I could do that for free. So in 1993 I dropped out. I hung around Providence for a bit, and then my college friend <PERSON> did me a big favor. A rent-controlled apartment in a building her mother owned in New York was becoming vacant. Did I want it? It wasn't much more than my current place, and New York was supposed to be where the artists were. So yes, I wanted it! [7]\n", "\n", "Asterix comics begin by zooming in on a tiny corner of Roman Gaul that turns out not to be controlled by the Romans. You can do something similar on a map of New York City: if you zoom in on the Upper East Side, there's a tiny corner that's not rich, or at least wasn't in 1993. It's called Yorkville, and that was my new home. Now I was a New York artist — in the strictly technical sense of making paintings and living in New York.\n", "\n", "I was nervous about money, because I could sense that Interleaf was on the way down. Freelance Lisp hacking work was very rare, and I didn't want to have to program in another language, which in those days would have meant C++ if I was lucky. So with my unerring nose for financial opportunity, I decided to write another book on Lisp. This would be a popular book, the sort of book that could be used as a textbook. I imagined myself living frugally off the royalties and spending all my time painting. (The painting on the cover of this book, ANSI Common Lisp, is one that I painted around this time.)\n", "\n", "The best thing about New York for me was the presence of <PERSON><PERSON><PERSON> and <PERSON>. <PERSON><PERSON><PERSON> was a painter, one of the early photorealists, and I'd taken her painting class at Harvard. I've never known a teacher more beloved by her students. Large numbers of former students kept in touch with her, including me. After I moved to New York I became her de facto studio assistant.\n", "\n", "She liked to paint on big, square canvases, 4 to 5 feet on a side. One day in late 1994 as I was stretching one of these monsters there was something on the radio about a famous fund manager. He wasn't that much older than me, and was super rich. The thought suddenly occurred to me: why don't I become rich? Then I'll be able to work on whatever I want.\n", "\n", "Meanwhile I'd been hearing more and more about this new thing called the World Wide Web. <PERSON> showed it to me when I visited him in Cambridge, where he was now in grad school at Harvard. It seemed to me that the web would be a big deal. I'd seen what graphical user interfaces had done for the popularity of microcomputers. It seemed like the web would do the same for the internet.\n", "\n", "If I wanted to get rich, here was the next train leaving the station. I was right about that part. What I got wrong was the idea. I decided we should start a company to put art galleries online. I can't honestly say, after reading so many Y Combinator applications, that this was the worst startup idea ever, but it was up there. Art galleries didn't want to be online, and still don't, not the fancy ones. That's not how they sell. I wrote some software to generate web sites for galleries, and <PERSON> wrote some to resize images and set up an http server to serve the pages. Then we tried to sign up galleries. To call this a difficult sale would be an understatement. It was difficult to give away. A few galleries let us make sites for them for free, but none paid us.\n", "\n", "Then some online stores started to appear, and I realized that except for the order buttons they were identical to the sites we'd been generating for galleries. This impressive-sounding thing called an \"internet storefront\" was something we already knew how to build.\n", "\n", "So in the summer of 1995, after I submitted the camera-ready copy of ANSI Common Lisp to the publishers, we started trying to write software to build online stores. At first this was going to be normal desktop software, which in those days meant Windows software. That was an alarming prospect, because neither of us knew how to write Windows software or wanted to learn. We lived in the Unix world. But we decided we'd at least try writing a prototype store builder on Unix. <PERSON> wrote a shopping cart, and I wrote a new site generator for stores — in Lisp, of course.\n", "\n", "We were working out of <PERSON>'s apartment in Cambridge. His roommate was away for big chunks of time, during which I got to sleep in his room. For some reason there was no bed frame or sheets, just a mattress on the floor. One morning as I was lying on this mattress I had an idea that made me sit up like a capital L. What if we ran the software on the server, and let users control it by clicking on links? Then we'd never have to write anything to run on users' computers. We could generate the sites on the same server we'd serve them from. Users wouldn't need anything more than a browser.\n", "\n", "This kind of software, known as a web app, is common now, but at the time it wasn't clear that it was even possible. To find out, we decided to try making a version of our store builder that you could control through the browser. A couple days later, on August 12, we had one that worked. The UI was horrible, but it proved you could build a whole store through the browser, without any client software or typing anything into the command line on the server.\n", "\n", "Now we felt like we were really onto something. I had visions of a whole new generation of software working this way. You wouldn't need versions, or ports, or any of that crap. At Interleaf there had been a whole group called Release Engineering that seemed to be at least as big as the group that actually wrote the software. Now you could just update the software right on the server.\n", "\n", "We started a new company we called Viaweb, after the fact that our software worked via the web, and we got $10,000 in seed funding from <PERSON><PERSON><PERSON>'s husband <PERSON>. In return for that and doing the initial legal work and giving us business advice, we gave him 10% of the company. Ten years later this deal became the model for Y Combinator's. We knew founders needed something like this, because we'd needed it ourselves.\n", "\n", "At this stage I had a negative net worth, because the thousand dollars or so I had in the bank was more than counterbalanced by what I owed the government in taxes. (Had I diligently set aside the proper proportion of the money I'd made consulting for Interleaf? No, I had not.) So although <PERSON> had his graduate student stipend, I needed that seed funding to live on.\n", "\n", "We originally hoped to launch in September, but we got more ambitious about the software as we worked on it. Eventually we managed to build a WYSIWYG site builder, in the sense that as you were creating pages, they looked exactly like the static ones that would be generated later, except that instead of leading to static pages, the links all referred to closures stored in a hash table on the server.\n", "\n", "It helped to have studied art, because the main goal of an online store builder is to make users look legit, and the key to looking legit is high production values. If you get page layouts and fonts and colors right, you can make a guy running a store out of his bedroom look more legit than a big company.\n", "\n", "(If you're curious why my site looks so old-fashioned, it's because it's still made with this software. It may look clunky today, but in 1996 it was the last word in slick.)\n", "\n", "In September, <PERSON> rebelled. \"We've been working on this for a month,\" he said, \"and it's still not done.\" This is funny in retrospect, because he would still be working on it almost 3 years later. But I decided it might be prudent to recruit more programmers, and I asked <PERSON> who else in grad school with him was really good. He recommended <PERSON>, which surprised me at first, because at that point I knew <PERSON> mainly for his plan to reduce everything in his life to a stack of notecards, which he carried around with him. But Rtm was right, as usual. <PERSON> turned out to be a frighteningly effective hacker.\n", "\n", "It was a lot of fun working with <PERSON> and <PERSON>. They're the two most independent-minded people I know, and in completely different ways. If you could see inside Rtm's brain it would look like a colonial New England church, and if you could see inside <PERSON>'s it would look like the worst excesses of Austrian Rococo.\n", "\n", "We opened for business, with 6 stores, in January 1996. It was just as well we waited a few months, because although we worried we were late, we were actually almost fatally early. There was a lot of talk in the press then about ecommerce, but not many people actually wanted online stores. [8]\n", "\n", "There were three main parts to the software: the editor, which people used to build sites and which I wrote, the shopping cart, which <PERSON> wrote, and the manager, which kept track of orders and statistics, and which <PERSON> wrote. In its time, the editor was one of the best general-purpose site builders. I kept the code tight and didn't have to integrate with any other software except <PERSON>'s and <PERSON>'s, so it was quite fun to work on. If all I'd had to do was work on this software, the next 3 years would have been the easiest of my life. Unfortunately I had to do a lot more, all of it stuff I was worse at than programming, and the next 3 years were instead the most stressful.\n", "\n", "There were a lot of startups making ecommerce software in the second half of the 90s. We were determined to be the Microsoft Word, not the Interleaf. Which meant being easy to use and inexpensive. It was lucky for us that we were poor, because that caused us to make Viaweb even more inexpensive than we realized. We charged $100 a month for a small store and $300 a month for a big one. This low price was a big attraction, and a constant thorn in the sides of competitors, but it wasn't because of some clever insight that we set the price low. We had no idea what businesses paid for things. $300 a month seemed like a lot of money to us.\n", "\n", "We did a lot of things right by accident like that. For example, we did what's now called \"doing things that don't scale,\" although at the time we would have described it as \"being so lame that we're driven to the most desperate measures to get users.\" The most common of which was building stores for them. This seemed particularly humiliating, since the whole raison d'etre of our software was that people could use it to make their own stores. But anything to get users.\n", "\n", "We learned a lot more about retail than we wanted to know. For example, that if you could only have a small image of a man's shirt (and all images were small then by present standards), it was better to have a closeup of the collar than a picture of the whole shirt. The reason I remember learning this was that it meant I had to rescan about 30 images of men's shirts. My first set of scans were so beautiful too.\n", "\n", "Though this felt wrong, it was exactly the right thing to be doing. Building stores for users taught us about retail, and about how it felt to use our software. I was initially both mystified and repelled by \"business\" and thought we needed a \"business person\" to be in charge of it, but once we started to get users, I was converted, in much the same way I was converted to fatherhood once I had kids. Whatever users wanted, I was all theirs. Maybe one day we'd have so many users that I couldn't scan their images for them, but in the meantime there was nothing more important to do.\n", "\n", "Another thing I didn't get at the time is that growth rate is the ultimate test of a startup. Our growth rate was fine. We had about 70 stores at the end of 1996 and about 500 at the end of 1997. I mistakenly thought the thing that mattered was the absolute number of users. And that is the thing that matters in the sense that that's how much money you're making, and if you're not making enough, you might go out of business. But in the long term the growth rate takes care of the absolute number. If we'd been a startup I was advising at Y Combinator, I would have said: Stop being so stressed out, because you're doing fine. You're growing 7x a year. Just don't hire too many more people and you'll soon be profitable, and then you'll control your own destiny.\n", "\n", "Alas I hired lots more people, partly because our investors wanted me to, and partly because that's what startups did during the Internet Bubble. A company with just a handful of employees would have seemed amateurish. So we didn't reach breakeven until about when Yahoo bought us in the summer of 1998. Which in turn meant we were at the mercy of investors for the entire life of the company. And since both we and our investors were noobs at startups, the result was a mess even by startup standards.\n", "\n", "It was a huge relief when Yahoo bought us. In principle our Viaweb stock was valuable. It was a share in a business that was profitable and growing rapidly. But it didn't feel very valuable to me; I had no idea how to value a business, but I was all too keenly aware of the near-death experiences we seemed to have every few months. Nor had I changed my grad student lifestyle significantly since we started. So when Yahoo bought us it felt like going from rags to riches. Since we were going to California, I bought a car, a yellow 1998 VW GTI. I remember thinking that its leather seats alone were by far the most luxurious thing I owned.\n", "\n", "The next year, from the summer of 1998 to the summer of 1999, must have been the least productive of my life. I didn't realize it at the time, but I was worn out from the effort and stress of running Viaweb. For a while after I got to California I tried to continue my usual m.o. of programming till 3 in the morning, but fatigue combined with Yahoo's prematurely aged culture and grim cube farm in Santa Clara gradually dragged me down. After a few months it felt disconcertingly like working at Interleaf.\n", "\n", "Yahoo had given us a lot of options when they bought us. At the time I thought Yahoo was so overvalued that they'd never be worth anything, but to my astonishment the stock went up 5x in the next year. I hung on till the first chunk of options vested, then in the summer of 1999 I left. It had been so long since I'd painted anything that I'd half forgotten why I was doing this. My brain had been entirely full of software and men's shirts for 4 years. But I had done this to get rich so I could paint, I reminded myself, and now I was rich, so I should go paint.\n", "\n", "When I said I was leaving, my boss at Yahoo had a long conversation with me about my plans. I told him all about the kinds of pictures I wanted to paint. At the time I was touched that he took such an interest in me. Now I realize it was because he thought I was lying. My options at that point were worth about $2 million a month. If I was leaving that kind of money on the table, it could only be to go and start some new startup, and if I did, I might take people with me. This was the height of the Internet Bubble, and Yahoo was ground zero of it. My boss was at that moment a billionaire. Leaving then to start a new startup must have seemed to him an insanely, and yet also plausibly, ambitious plan.\n", "\n", "But I really was quitting to paint, and I started immediately. There was no time to lose. I'd already burned 4 years getting rich. Now when I talk to founders who are leaving after selling their companies, my advice is always the same: take a vacation. That's what I should have done, just gone off somewhere and done nothing for a month or two, but the idea never occurred to me.\n", "\n", "So I tried to paint, but I just didn't seem to have any energy or ambition. Part of the problem was that I didn't know many people in California. I'd compounded this problem by buying a house up in the Santa Cruz Mountains, with a beautiful view but miles from anywhere. I stuck it out for a few more months, then in desperation I went back to New York, where unless you understand about rent control you'll be surprised to hear I still had my apartment, sealed up like a tomb of my old life. <PERSON><PERSON>e was in New York at least, and there were other people trying to paint there, even though I didn't know any of them.\n", "\n", "When I got back to New York I resumed my old life, except now I was rich. It was as weird as it sounds. I resumed all my old patterns, except now there were doors where there hadn't been. Now when I was tired of walking, all I had to do was raise my hand, and (unless it was raining) a taxi would stop to pick me up. Now when I walked past charming little restaurants I could go in and order lunch. It was exciting for a while. Painting started to go better. I experimented with a new kind of still life where I'd paint one painting in the old way, then photograph it and print it, blown up, on canvas, and then use that as the underpainting for a second still life, painted from the same objects (which hopefully hadn't rotted yet).\n", "\n", "Meanwhile I looked for an apartment to buy. Now I could actually choose what neighborhood to live in. Where, I asked myself and various real estate agents, is the Cambridge of New York? Aided by occasional visits to actual Cambridge, I gradually realized there wasn't one. Huh.\n", "\n", "Around this time, in the spring of 2000, I had an idea. It was clear from our experience with Viaweb that web apps were the future. Why not build a web app for making web apps? Why not let people edit code on our server through the browser, and then host the resulting applications for them? [9] You could run all sorts of services on the servers that these applications could use just by making an API call: making and receiving phone calls, manipulating images, taking credit card payments, etc.\n", "\n", "I got so excited about this idea that I couldn't think about anything else. It seemed obvious that this was the future. I didn't particularly want to start another company, but it was clear that this idea would have to be embodied as one, so I decided to move to Cambridge and start it. I hoped to lure <PERSON> into working on it with me, but there I ran into a hitch. <PERSON> was now a postdoc at MIT, and though he'd made a lot of money the last time I'd lured him into working on one of my schemes, it had also been a huge time sink. So while he agreed that it sounded like a plausible idea, he firmly refused to work on it.\n", "\n", "Hmph. Well, I'd do it myself then. I recruited <PERSON>, who had worked for Viaweb, and two undergrads who wanted summer jobs, and we got to work trying to build what it's now clear is about twenty companies and several open source projects worth of software. The language for defining applications would of course be a dialect of Lisp. But I wasn't so naive as to assume I could spring an overt Lisp on a general audience; we'd hide the parentheses, like <PERSON> did.\n", "\n", "By then there was a name for the kind of company Viaweb was, an \"application service provider,\" or ASP. This name didn't last long before it was replaced by \"software as a service,\" but it was current for long enough that I named this new company after it: it was going to be called Aspra.\n", "\n", "I started working on the application builder, <PERSON> worked on network infrastructure, and the two undergrads worked on the first two services (images and phone calls). But about halfway through the summer I realized I really didn't want to run a company — especially not a big one, which it was looking like this would have to be. I'd only started Viaweb because I needed the money. Now that I didn't need money anymore, why was I doing this? If this vision had to be realized as a company, then screw the vision. I'd build a subset that could be done as an open source project.\n", "\n", "Much to my surprise, the time I spent working on this stuff was not wasted after all. After we started Y Combinator, I would often encounter startups working on parts of this new architecture, and it was very useful to have spent so much time thinking about it and even trying to write some of it.\n", "\n", "The subset I would build as an open source project was the new Lisp, whose parentheses I now wouldn't even have to hide. A lot of Lisp hackers dream of building a new Lisp, partly because one of the distinctive features of the language is that it has dialects, and partly, I think, because we have in our minds a Platonic form of Lisp that all existing dialects fall short of. I certainly did. So at the end of the summer <PERSON> and I switched to working on this new dialect of Lisp, which I called Arc, in a house I bought in Cambridge.\n", "\n", "The following spring, lightning struck. I was invited to give a talk at a Lisp conference, so I gave one about how we'd used Lisp at Viaweb. Afterward I put a postscript file of this talk online, on paulgraham.com, which I'd created years before using Viaweb but had never used for anything. In one day it got 30,000 page views. What on earth had happened? The referring urls showed that someone had posted it on Slashdot. [10]\n", "\n", "Wow, I thought, there's an audience. If I write something and put it on the web, anyone can read it. That may seem obvious now, but it was surprising then. In the print era there was a narrow channel to readers, guarded by fierce monsters known as editors. The only way to get an audience for anything you wrote was to get it published as a book, or in a newspaper or magazine. Now anyone could publish anything.\n", "\n", "This had been possible in principle since 1993, but not many people had realized it yet. I had been intimately involved with building the infrastructure of the web for most of that time, and a writer as well, and it had taken me 8 years to realize it. Even then it took me several years to understand the implications. It meant there would be a whole new generation of essays. [11]\n", "\n", "In the print era, the channel for publishing essays had been vanishingly small. Except for a few officially anointed thinkers who went to the right parties in New York, the only people allowed to publish essays were specialists writing about their specialties. There were so many essays that had never been written, because there had been no way to publish them. Now they could be, and I was going to write them. [12]\n", "\n", "I've worked on several different things, but to the extent there was a turning point where I figured out what to work on, it was when I started publishing essays online. From then on I knew that whatever else I did, I'd always write essays too.\n", "\n", "I knew that online essays would be a marginal medium at first. Socially they'd seem more like rants posted by nutjobs on their GeoCities sites than the genteel and beautifully typeset compositions published in The New Yorker. But by this point I knew enough to find that encouraging instead of discouraging.\n", "\n", "One of the most conspicuous patterns I've noticed in my life is how well it has worked, for me at least, to work on things that weren't prestigious. Still life has always been the least prestigious form of painting. <PERSON><PERSON><PERSON> and <PERSON> both seemed lame when we started them. I still get the glassy eye from strangers when they ask what I'm writing, and I explain that it's an essay I'm going to publish on my web site. Even <PERSON><PERSON>, though prestigious intellectually in something like the way Latin is, also seems about as hip.\n", "\n", "It's not that unprestigious types of work are good per se. But when you find yourself drawn to some kind of work despite its current lack of prestige, it's a sign both that there's something real to be discovered there, and that you have the right kind of motives. Impure motives are a big danger for the ambitious. If anything is going to lead you astray, it will be the desire to impress people. So while working on things that aren't prestigious doesn't guarantee you're on the right track, it at least guarantees you're not on the most common type of wrong one.\n", "\n", "Over the next several years I wrote lots of essays about all kinds of different topics. <PERSON><PERSON><PERSON> reprinted a collection of them as a book, called Hackers & Painters after one of the essays in it. I also worked on spam filters, and did some more painting. I used to have dinners for a group of friends every thursday night, which taught me how to cook for groups. And I bought another building in Cambridge, a former candy factory (and later, twas said, porn studio), to use as an office.\n", "\n", "One night in October 2003 there was a big party at my house. It was a clever idea of my friend <PERSON>, who was one of the thursday diners. Three separate hosts would all invite their friends to one party. So for every guest, two thirds of the other guests would be people they didn't know but would probably like. One of the guests was someone I didn't know but would turn out to like a lot: a woman called <PERSON>. A couple days later I asked her out.\n", "\n", "<PERSON> was in charge of marketing at a Boston investment bank. This bank thought it understood startups, but over the next year, as she met friends of mine from the startup world, she was surprised how different reality was. And how colorful their stories were. So she decided to compile a book of interviews with startup founders.\n", "\n", "When the bank had financial problems and she had to fire half her staff, she started looking for a new job. In early 2005 she interviewed for a marketing job at a Boston VC firm. It took them weeks to make up their minds, and during this time I started telling her about all the things that needed to be fixed about venture capital. They should make a larger number of smaller investments instead of a handful of giant ones, they should be funding younger, more technical founders instead of MBAs, they should let the founders remain as CEO, and so on.\n", "\n", "One of my tricks for writing essays had always been to give talks. The prospect of having to stand up in front of a group of people and tell them something that won't waste their time is a great spur to the imagination. When the Harvard Computer Society, the undergrad computer club, asked me to give a talk, I decided I would tell them how to start a startup. Maybe they'd be able to avoid the worst of the mistakes we'd made.\n", "\n", "So I gave this talk, in the course of which I told them that the best sources of seed funding were successful startup founders, because then they'd be sources of advice too. Whereupon it seemed they were all looking expectantly at me. Horrified at the prospect of having my inbox flooded by business plans (if I'd only known), I blurted out \"But not me!\" and went on with the talk. But afterward it occurred to me that I should really stop procrastinating about angel investing. I'd been meaning to since Yahoo bought us, and now it was 7 years later and I still hadn't done one angel investment.\n", "\n", "Meanwhile I had been scheming with <PERSON> and <PERSON> about projects we could work on together. I missed working with them, and it seemed like there had to be something we could collaborate on.\n", "\n", "As <PERSON> and I were walking home from dinner on March 11, at the corner of Garden and Walker streets, these three threads converged. Screw the VCs who were taking so long to make up their minds. We'd start our own investment firm and actually implement the ideas we'd been talking about. I'd fund it, and <PERSON> could quit her job and work for it, and we'd get <PERSON> and <PERSON> as partners too. [13]\n", "\n", "Once again, ignorance worked in our favor. We had no idea how to be angel investors, and in Boston in 2005 there were no <PERSON> to learn from. So we just made what seemed like the obvious choices, and some of the things we did turned out to be novel.\n", "\n", "There are multiple components to Y Combinator, and we didn't figure them all out at once. The part we got first was to be an angel firm. In those days, those two words didn't go together. There were VC firms, which were organized companies with people whose job it was to make investments, but they only did big, million dollar investments. And there were angels, who did smaller investments, but these were individuals who were usually focused on other things and made investments on the side. And neither of them helped founders enough in the beginning. We knew how helpless founders were in some respects, because we remembered how helpless we'd been. For example, one thing <PERSON> had done for us that seemed to us like magic was to get us set up as a company. We were fine writing fairly difficult software, but actually getting incorporated, with bylaws and stock and all that stuff, how on earth did you do that? Our plan was not only to make seed investments, but to do for startups everything <PERSON> had done for us.\n", "\n", "YC was not organized as a fund. It was cheap enough to run that we funded it with our own money. That went right by 99% of readers, but professional investors are thinking \"Wow, that means they got all the returns.\" But once again, this was not due to any particular insight on our part. We didn't know how VC firms were organized. It never occurred to us to try to raise a fund, and if it had, we wouldn't have known where to start. [14]\n", "\n", "The most distinctive thing about YC is the batch model: to fund a bunch of startups all at once, twice a year, and then to spend three months focusing intensively on trying to help them. That part we discovered by accident, not merely implicitly but explicitly due to our ignorance about investing. We needed to get experience as investors. What better way, we thought, than to fund a whole bunch of startups at once? We knew undergrads got temporary jobs at tech companies during the summer. Why not organize a summer program where they'd start startups instead? We wouldn't feel guilty for being in a sense fake investors, because they would in a similar sense be fake founders. So while we probably wouldn't make much money out of it, we'd at least get to practice being investors on them, and they for their part would probably have a more interesting summer than they would working at Microsoft.\n", "\n", "We'd use the building I owned in Cambridge as our headquarters. We'd all have dinner there once a week — on tuesdays, since I was already cooking for the thursday diners on thursdays — and after dinner we'd bring in experts on startups to give talks.\n", "\n", "We knew undergrads were deciding then about summer jobs, so in a matter of days we cooked up something we called the Summer Founders Program, and I posted an announcement on my site, inviting undergrads to apply. I had never imagined that writing essays would be a way to get \"deal flow,\" as investors call it, but it turned out to be the perfect source. [15] We got 225 applications for the Summer Founders Program, and we were surprised to find that a lot of them were from people who'd already graduated, or were about to that spring. Already this SFP thing was starting to feel more serious than we'd intended.\n", "\n", "We invited about 20 of the 225 groups to interview in person, and from those we picked 8 to fund. They were an impressive group. That first batch included reddit, <PERSON> and <PERSON><PERSON>, who went on to found Twitch, <PERSON>, who had already helped write the RSS spec and would a few years later become a martyr for open access, and <PERSON>, who would later become the second president of YC. I don't think it was entirely luck that the first batch was so good. You had to be pretty bold to sign up for a weird thing like the Summer Founders Program instead of a summer job at a legit place like Microsoft or Goldman Sachs.\n", "\n", "The deal for startups was based on a combination of the deal we did with <PERSON> ($10k for 10%) and what <PERSON> said MIT grad students got for the summer ($6k). We invested $6k per founder, which in the typical two-founder case was $12k, in return for 6%. That had to be fair, because it was twice as good as the deal we ourselves had taken. Plus that first summer, which was really hot, <PERSON> brought the founders free air conditioners. [16]\n", "\n", "Fairly quickly I realized that we had stumbled upon the way to scale startup funding. Funding startups in batches was more convenient for us, because it meant we could do things for a lot of startups at once, but being part of a batch was better for the startups too. It solved one of the biggest problems faced by founders: the isolation. Now you not only had colleagues, but colleagues who understood the problems you were facing and could tell you how they were solving them.\n", "\n", "As YC grew, we started to notice other advantages of scale. The alumni became a tight community, dedicated to helping one another, and especially the current batch, whose shoes they remembered being in. We also noticed that the startups were becoming one another's customers. We used to refer jokingly to the \"YC GDP,\" but as YC grows this becomes less and less of a joke. Now lots of startups get their initial set of customers almost entirely from among their batchmates.\n", "\n", "I had not originally intended <PERSON><PERSON> to be a full-time job. I was going to do three things: hack, write essays, and work on YC. As YC grew, and I grew more excited about it, it started to take up a lot more than a third of my attention. But for the first few years I was still able to work on other things.\n", "\n", "In the summer of 2006, <PERSON> and <PERSON> started working on a new version of Arc. This one was reasonably fast, because it was compiled into Scheme. To test this new Arc, I wrote Hacker News in it. It was originally meant to be a news aggregator for startup founders and was called Startup News, but after a few months I got tired of reading about nothing but startups. Plus it wasn't startup founders we wanted to reach. It was future startup founders. So I changed the name to Hacker News and the topic to whatever engaged one's intellectual curiosity.\n", "\n", "HN was no doubt good for Y<PERSON>, but it was also by far the biggest source of stress for me. If all I'd had to do was select and help founders, life would have been so easy. And that implies that HN was a mistake. Surely the biggest source of stress in one's work should at least be something close to the core of the work. Whereas I was like someone who was in pain while running a marathon not from the exertion of running, but because I had a blister from an ill-fitting shoe. When I was dealing with some urgent problem during YC, there was about a 60% chance it had to do with HN, and a 40% chance it had do with everything else combined. [17]\n", "\n", "As well as HN, I wrote all of YC's internal software in Arc. But while I continued to work a good deal in Arc, I gradually stopped working on Arc, partly because I didn't have time to, and partly because it was a lot less attractive to mess around with the language now that we had all this infrastructure depending on it. So now my three projects were reduced to two: writing essays and working on YC.\n", "\n", "YC was different from other kinds of work I've done. Instead of deciding for myself what to work on, the problems came to me. Every 6 months there was a new batch of startups, and their problems, whatever they were, became our problems. It was very engaging work, because their problems were quite varied, and the good founders were very effective. If you were trying to learn the most you could about startups in the shortest possible time, you couldn't have picked a better way to do it.\n", "\n", "There were parts of the job I didn't like. Disputes between cofounders, figuring out when people were lying to us, fighting with people who maltreated the startups, and so on. But I worked hard even at the parts I didn't like. I was haunted by something <PERSON> once said about companies: \"No one works harder than the boss.\" He meant it both descriptively and prescriptively, and it was the second part that scared me. I wanted Y<PERSON> to be good, so if how hard I worked set the upper bound on how hard everyone else worked, I'd better work very hard.\n", "\n", "One day in 2010, when he was visiting California for interviews, <PERSON> did something astonishing: he offered me unsolicited advice. I can only remember him doing that once before. One day at Viaweb, when I was bent over double from a kidney stone, he suggested that it would be a good idea for him to take me to the hospital. That was what it took for Rtm to offer unsolicited advice. So I remember his exact words very clearly. \"You know,\" he said, \"you should make sure Y Combinator isn't the last cool thing you do.\"\n", "\n", "At the time I didn't understand what he meant, but gradually it dawned on me that he was saying I should quit. This seemed strange advice, because Y<PERSON> was doing great. But if there was one thing rarer than Rtm offering advice, it was Rtm being wrong. So this set me thinking. It was true that on my current trajectory, YC would be the last thing I did, because it was only taking up more of my attention. It had already eaten Arc, and was in the process of eating essays too. Either YC was my life's work or I'd have to leave eventually. And it wasn't, so I would.\n", "\n", "In the summer of 2012 my mother had a stroke, and the cause turned out to be a blood clot caused by colon cancer. The stroke destroyed her balance, and she was put in a nursing home, but she really wanted to get out of it and back to her house, and my sister and I were determined to help her do it. I used to fly up to Oregon to visit her regularly, and I had a lot of time to think on those flights. On one of them I realized I was ready to hand Y<PERSON> over to someone else.\n", "\n", "I asked <PERSON> if she wanted to be president, but she didn't, so we decided we'd try to recruit <PERSON>. We talked to <PERSON> and <PERSON> and we agreed to make it a complete changing of the guard. Up till that point YC had been controlled by the original LLC we four had started. But we wanted YC to last for a long time, and to do that it couldn't be controlled by the founders. So if <PERSON> said yes, we'd let him reorganize YC. <PERSON> and <PERSON> would retire, and <PERSON> and <PERSON> would become ordinary partners.\n", "\n", "When we asked <PERSON> if he wanted to be president of YC, initially he said no. He wanted to start a startup to make nuclear reactors. But I kept at it, and in October 2013 he finally agreed. We decided he'd take over starting with the winter 2014 batch. For the rest of 2013 I left running YC more and more to <PERSON>, partly so he could learn the job, and partly because I was focused on my mother, whose cancer had returned.\n", "\n", "She died on January 15, 2014. We knew this was coming, but it was still hard when it did.\n", "\n", "I kept working on YC till March, to help get that batch of startups through Demo Day, then I checked out pretty completely. (I still talk to alumni and to new startups working on things I'm interested in, but that only takes a few hours a week.)\n", "\n", "What should I do next? Rtm's advice hadn't included anything about that. I wanted to do something completely different, so I decided I'd paint. I wanted to see how good I could get if I really focused on it. So the day after I stopped working on YC, I started painting. I was rusty and it took a while to get back into shape, but it was at least completely engaging. [18]\n", "\n", "I spent most of the rest of 2014 painting. I'd never been able to work so uninterruptedly before, and I got to be better than I had been. Not good enough, but better. Then in November, right in the middle of a painting, I ran out of steam. Up till that point I'd always been curious to see how the painting I was working on would turn out, but suddenly finishing this one seemed like a chore. So I stopped working on it and cleaned my brushes and haven't painted since. So far anyway.\n", "\n", "I realize that sounds rather wimpy. But attention is a zero sum game. If you can choose what to work on, and you choose a project that's not the best one (or at least a good one) for you, then it's getting in the way of another project that is. And at 50 there was some opportunity cost to screwing around.\n", "\n", "I started writing essays again, and wrote a bunch of new ones over the next few months. I even wrote a couple that weren't about startups. Then in March 2015 I started working on Lisp again.\n", "\n", "The distinctive thing about Lisp is that its core is a language defined by writing an interpreter in itself. It wasn't originally intended as a programming language in the ordinary sense. It was meant to be a formal model of computation, an alternative to the Turing machine. If you want to write an interpreter for a language in itself, what's the minimum set of predefined operators you need? The Lisp that <PERSON> invented, or more accurately discovered, is an answer to that question. [19]\n", "\n", "<PERSON> didn't realize this <PERSON><PERSON> could even be used to program computers till his grad student <PERSON> suggested it. <PERSON> translated <PERSON>'s interpreter into IBM 704 machine language, and from that point Lisp started also to be a programming language in the ordinary sense. But its origins as a model of computation gave it a power and elegance that other languages couldn't match. It was this that attracted me in college, though I didn't understand why at the time.\n", "\n", "<PERSON>'s 1960 Lisp did nothing more than interpret Lisp expressions. It was missing a lot of things you'd want in a programming language. So these had to be added, and when they were, they weren't defined using <PERSON>'s original axiomatic approach. That wouldn't have been feasible at the time. <PERSON> tested his interpreter by hand-simulating the execution of programs. But it was already getting close to the limit of interpreters you could test that way — indeed, there was a bug in it that <PERSON> had overlooked. To test a more complicated interpreter, you'd have had to run it, and computers then weren't powerful enough.\n", "\n", "Now they are, though. Now you could continue using <PERSON>'s axiomatic approach till you'd defined a complete programming language. And as long as every change you made to <PERSON>'s Lisp was a discoveredness-preserving transformation, you could, in principle, end up with a complete language that had this quality. Harder to do than to talk about, of course, but if it was possible in principle, why not try? So I decided to take a shot at it. It took 4 years, from March 26, 2015 to October 12, 2019. It was fortunate that I had a precisely defined goal, or it would have been hard to keep at it for so long.\n", "\n", "I wrote this new Lisp, called Bel, in itself in Arc. That may sound like a contradiction, but it's an indication of the sort of trickery I had to engage in to make this work. By means of an egregious collection of hacks I managed to make something close enough to an interpreter written in itself that could actually run. Not fast, but fast enough to test.\n", "\n", "I had to ban myself from writing essays during most of this time, or I'd never have finished. In late 2015 I spent 3 months writing essays, and when I went back to working on Bel I could barely understand the code. Not so much because it was badly written as because the problem is so convoluted. When you're working on an interpreter written in itself, it's hard to keep track of what's happening at what level, and errors can be practically encrypted by the time you get them.\n", "\n", "So I said no more essays till <PERSON> was done. But I told few people about <PERSON> while I was working on it. So for years it must have seemed that I was doing nothing, when in fact I was working harder than I'd ever worked on anything. Occasionally after wrestling for hours with some gruesome bug I'd check Twitter or HN and see someone asking \"Does <PERSON> still code?\"\n", "\n", "Working on <PERSON> was hard but satisfying. I worked on it so intensively that at any given time I had a decent chunk of the code in my head and could write more there. I remember taking the boys to the coast on a sunny day in 2015 and figuring out how to deal with some problem involving continuations while I watched them play in the tide pools. It felt like I was doing life right. I remember that because I was slightly dismayed at how novel it felt. The good news is that I had more moments like this over the next few years.\n", "\n", "In the summer of 2016 we moved to England. We wanted our kids to see what it was like living in another country, and since I was a British citizen by birth, that seemed the obvious choice. We only meant to stay for a year, but we liked it so much that we still live there. So most of Bel was written in England.\n", "\n", "In the fall of 2019, Bel was finally finished. Like <PERSON>'s original Lisp, it's a spec rather than an implementation, although like <PERSON>'s Lisp it's a spec expressed as code.\n", "\n", "Now that I could write essays again, I wrote a bunch about topics I'd had stacked up. I kept writing essays through 2020, but I also started to think about other things I could work on. How should I choose what to do? Well, how had I chosen what to work on in the past? I wrote an essay for myself to answer that question, and I was surprised how long and messy the answer turned out to be. If this surprised me, who'd lived it, then I thought perhaps it would be interesting to other people, and encouraging to those with similarly messy lives. So I wrote a more detailed version for others to read, and this is the last sentence of it.\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "Notes\n", "\n", "[1] My experience skipped a step in the evolution of computers: time-sharing machines with interactive OSes. I went straight from batch processing to microcomputers, which made microcomputers seem all the more exciting.\n", "\n", "[2] Italian words for abstract concepts can nearly always be predicted from their English cognates (except for occasional traps like polluzione). It's the everyday words that differ. So if you string together a lot of abstract concepts with a few simple verbs, you can make a little Italian go a long way.\n", "\n", "[3] I lived at Piazza San Felice 4, so my walk to the Accademia went straight down the spine of old Florence: past the Pitti, across the bridge, past Orsanmichele, between the Duomo and the Baptistery, and then up Via Ricasoli to Piazza San Marco. I saw Florence at street level in every possible condition, from empty dark winter evenings to sweltering summer days when the streets were packed with tourists.\n", "\n", "[4] You can of course paint people like still lives if you want to, and they're willing. That sort of portrait is arguably the apex of still life painting, though the long sitting does tend to produce pained expressions in the sitters.\n", "\n", "[5] Interleaf was one of many companies that had smart people and built impressive technology, and yet got crushed by <PERSON>'s Law. In the 1990s the exponential growth in the power of commodity (i.e. Intel) processors rolled up high-end, special-purpose hardware and software companies like a bulldozer.\n", "\n", "[6] The signature style seekers at RISD weren't specifically mercenary. In the art world, money and coolness are tightly coupled. Anything expensive comes to be seen as cool, and anything seen as cool will soon become equally expensive.\n", "\n", "[7] Technically the apartment wasn't rent-controlled but rent-stabilized, but this is a refinement only New Yorkers would know or care about. The point is that it was really cheap, less than half market price.\n", "\n", "[8] Most software you can launch as soon as it's done. But when the software is an online store builder and you're hosting the stores, if you don't have any users yet, that fact will be painfully obvious. So before we could launch publicly we had to launch privately, in the sense of recruiting an initial set of users and making sure they had decent-looking stores.\n", "\n", "[9] We'd had a code editor in Viaweb for users to define their own page styles. They didn't know it, but they were editing Lisp expressions underneath. But this wasn't an app editor, because the code ran when the merchants' sites were generated, not when shoppers visited them.\n", "\n", "[10] This was the first instance of what is now a familiar experience, and so was what happened next, when I read the comments and found they were full of angry people. How could I claim that Lisp was better than other languages? Weren't they all Turing complete? People who see the responses to essays I write sometimes tell me how sorry they feel for me, but I'm not exaggerating when I reply that it has always been like this, since the very beginning. It comes with the territory. An essay must tell readers things they don't already know, and some people dislike being told such things.\n", "\n", "[11] People put plenty of stuff on the internet in the 90s of course, but putting something online is not the same as publishing it online. Publishing online means you treat the online version as the (or at least a) primary version.\n", "\n", "[12] There is a general lesson here that our experience with <PERSON> Combinator also teaches: Customs continue to constrain you long after the restrictions that caused them have disappeared. Customary VC practice had once, like the customs about publishing essays, been based on real constraints. Startups had once been much more expensive to start, and proportionally rare. Now they could be cheap and common, but the VCs' customs still reflected the old world, just as customs about writing essays still reflected the constraints of the print era.\n", "\n", "Which in turn implies that people who are independent-minded (i.e. less influenced by custom) will have an advantage in fields affected by rapid change (where customs are more likely to be obsolete).\n", "\n", "Here's an interesting point, though: you can't always predict which fields will be affected by rapid change. Obviously software and venture capital will be, but who would have predicted that essay writing would be?\n", "\n", "[13] Y Combinator was not the original name. At first we were called Cambridge Seed. But we didn't want a regional name, in case someone copied us in Silicon Valley, so we renamed ourselves after one of the coolest tricks in the lambda calculus, the Y combinator.\n", "\n", "I picked orange as our color partly because it's the warmest, and partly because no VC used it. In 2005 all the VCs used staid colors like maroon, navy blue, and forest green, because they were trying to appeal to LPs, not founders. The YC logo itself is an inside joke: the Viaweb logo had been a white V on a red circle, so I made the YC logo a white Y on an orange square.\n", "\n", "[14] YC did become a fund for a couple years starting in 2009, because it was getting so big I could no longer afford to fund it personally. But after <PERSON><PERSON> got bought we had enough money to go back to being self-funded.\n", "\n", "[15] I've never liked the term \"deal flow,\" because it implies that the number of new startups at any given time is fixed. This is not only false, but it's the purpose of YC to falsify it, by causing startups to be founded that would not otherwise have existed.\n", "\n", "[16] She reports that they were all different shapes and sizes, because there was a run on air conditioners and she had to get whatever she could, but that they were all heavier than she could carry now.\n", "\n", "[17] Another problem with HN was a bizarre edge case that occurs when you both write essays and run a forum. When you run a forum, you're assumed to see if not every conversation, at least every conversation involving you. And when you write essays, people post highly imaginative misinterpretations of them on forums. Individually these two phenomena are tedious but bearable, but the combination is disastrous. You actually have to respond to the misinterpretations, because the assumption that you're present in the conversation means that not responding to any sufficiently upvoted misinterpretation reads as a tacit admission that it's correct. But that in turn encourages more; anyone who wants to pick a fight with you senses that now is their chance.\n", "\n", "[18] The worst thing about leaving YC was not working with <PERSON> anymore. We'd been working on YC almost the whole time we'd known each other, and we'd neither tried nor wanted to separate it from our personal lives, so leaving was like pulling up a deeply rooted tree.\n", "\n", "[19] One way to get more precise about the concept of invented vs discovered is to talk about space aliens. Any sufficiently advanced alien civilization would certainly know about the Pythagorean theorem, for example. I believe, though with less certainty, that they would also know about the Lisp in <PERSON>'s 1960 paper.\n", "\n", "But if so there's no reason to suppose that this is the limit of the language that might be known to them. Presumably aliens need numbers and errors and I/O too. So it seems likely there exists at least one path out of <PERSON>'s Lisp along which discoveredness is preserved.\n", "\n", "\n", "\n", "Thanks to <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> for reading drafts of this.\"\"\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 2}