{"cells": [{"cell_type": "code", "execution_count": 1, "id": "3bb5b171", "metadata": {}, "outputs": [], "source": ["import asyncio\n", "import json\n", "import os\n", "import re\n", "import sys\n", "from logging import getLogger\n", "from pathlib import Path\n", "\n", "from dotenv import load_dotenv\n", "from openai import AsyncOpenAI\n", "from pydantic import BaseModel, Field\n", "\n", "parent_dir = Path.cwd().parent.parent\n", "sys.path.append(str(parent_dir))"]}, {"cell_type": "code", "execution_count": 2, "id": "a3420939", "metadata": {}, "outputs": [], "source": ["from src.api.common.services.evaluation.document_info_service import (\n", "    DocumentInfo,\n", ")\n", "from src.api.common.services.evaluation.document_info_sources import Author\n", "from src.api.common.services.search_agent import SearchAgent\n", "from src.api.common.services.url_extractor_engine import <PERSON><PERSON>Extract<PERSON>, TavilyExtractor\n"]}, {"cell_type": "code", "execution_count": 3, "id": "8edac686", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "from src.api.common.services import BraveSearchEngine, TavilySearchEngine, JinaSearchEngine\n", "from ia_gen_core.prompts import PromptManager\n", "from src.api.common.dependency_container import DependencyContainer \n", "logger = getLogger(__name__)\n", "\n", "load_dotenv()\n"]}, {"cell_type": "code", "execution_count": 4, "id": "5aec2a7a", "metadata": {}, "outputs": [], "source": ["openai_api_key = os.getenv(\"OpenaiApiKey\")\n", "openai_client = AsyncOpenAI(api_key = openai_api_key)"]}, {"cell_type": "code", "execution_count": 16, "id": "337cd780", "metadata": {}, "outputs": [], "source": ["class RewritingAnswer(BaseModel):\n", "    \"\"\"\n", "    Represents the answer from the rewriting process.\n", "    \"\"\"\n", "    thought_process: list[str] = Field(..., description=\"The thought process and reasoning behind the rewriting decision.\")\n", "    text: str = Field(..., description=\"The rewritten text that connects seamlessly with the surrounding context.\")\n", "\n", "prompt = \"\"\"## Reescribe la sección {text_to_rewrite}\n", "\n", "Reescribe la sección **{text_to_rewrite}** del párrafo proporcionado según el **{feedback}**, asegurándote de que el texto revisado se conecte de forma fluida y lógica tanto con el **{previous_text}** como con el **{post_text}**. Tras los cambios, el párrafo completo debe mantenerse coherente, natural y contextualmente preciso.\n", "\n", "### Definiciones\n", "- **{previous_text}:** Texto que precede al texto que se va a reescribir.  \n", "- **{text_to_rewrite}:** Texto que necesita ser reescrito.  \n", "- **{post_text}:** Texto que sigue al texto que se va a reescribir.  \n", "- **{feedback}:** Observaciones sobre la tarea de reescritura que especifican los cambios necesarios para mejorar el texto.\n", "\n", "### Proceso de pensamiento\n", "1. <PERSON><PERSON>za cuida<PERSON>amente el **{feedback}** y determina qué cambios específicos se requieren.  \n", "2. Considera cómo conectar el **{previous_text}** y el **{post_text}** con la forma en que se escribió el texto original, para esto, comprueba si el texto a reescribir contiene parte de oraciónes o palabras que puedan estar cortadas y aparzcan en el {previous_text} o el {post_text}, asegúrate de que la reescritura mantenga la continuidad y coherencia con ambos textos.\n", "3. Analiza como se conecta el texto a reescribir con el testo previo y con el texto posterior.\n", "3. Genera una propuesta de reescritura que mantenga la coherencia del párrafo completo, asegurando que el texto revisado fluya naturalmente con el contexto circundante.\n", "4. <PERSON><PERSON><PERSON> de manera sistemá<PERSON>, paso a paso, cómo enlazar la sección reescrita con el texto circundante para que el párrafo quede sin fisuras.  \n", "\n", "### Instrucciones\n", "- Proporciona la versión revisada de **{text_to_rewrite}** (y nada más), manteniendo la estructura y el tono del original.  \n", "- Mantén claro el significado y ajusta la redacción según sea necesario para lograr una transición fluida, para esto, comprueba si el texto a reescribir contiene parte de oraciónes o palabras que puedan estar cortadas y aparzcan en el {previous_text} o el {post_text}, asegúrate de que la reescritura mantenga la continuidad y coherencia con ambos textos.\n", "- No alteres el **{previous_text}** ni el **{post_text}**.  \n", "- Si se necesita aclaración o inferencia basada en el **{feedback}**, asegúrate de que tus decisiones sean lógicas y mantengan la coherencia general.\n", "\n", "### Notas:\n", "- El texto original esta compuesto por {previous_text}{text_to_rewrite}{post_text} sin ningun conector entre ellos, por lo que es importante que el texto reescrito se conecte con ambos textos circundantes.\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "fe42afae", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"context\": {\n", "    \"previous_text\": \"Aunque el término modelo grande de lenguaje no tiene una definición formal, a menudo se refiere a modelos de aprendizaje profundo que tienen un recuento de parámetros del orden de miles de millones o más.\",\n", "    \"post_text\": \".[7]​ La habilidad con la que realizan las tareas y la gama de tareas de las que son capaces parece ser una función de la cantidad de recursos (datos, número de parámetros, capacidad de cálculo) que se les dedican, de una manera que no depende sobre avances adicionales en el diseño.[8]\"\n", "  },\n", "  \"text_to_rewrite\": \" Los LLMs son modelos de propósito general que se destacan en una amplia gama de tareas, en lugar de estar capacitados para una tarea específica (como el análisis de sentimientos, el reconocimiento de entidades nombradas o el razonamiento matemático)\",\n", "  \"feedback\": \"escribelo como si fuera un señor de 1500 hablando castellano antiguo y estuvieras sorprendido por la tecnologia\"\n", "}\n"]}], "source": ["fulltext = \"\"\"Aunque el término modelo grande de lenguaje no tiene una definición formal, a menudo se refiere a modelos de aprendizaje profundo que tienen un recuento de parámetros del orden de miles de millones o más. Los LLMs son modelos de propósito general que se destacan en una amplia gama de tareas, en lugar de estar capacitados para una tarea específica (como el análisis de sentimientos, el reconocimiento de entidades nombradas o el razonamiento matemático).[7]​ La habilidad con la que realizan las tareas y la gama de tareas de las que son capaces parece ser una función de la cantidad de recursos (datos, número de parámetros, capacidad de cálculo) que se les dedican, de una manera que no depende sobre avances adicionales en el diseño.[8]\"\"\"\n", "\n", "text_to_rewrite = \"\"\" Los LLMs son modelos de propósito general que se destacan en una amplia gama de tareas, en lugar de estar capacitados para una tarea específica (como el análisis de sentimientos, el reconocimiento de entidades nombradas o el razonamiento matemático).\"\"\"\n", "\n", "previous_text = fulltext.split(text_to_rewrite)[0]\n", "post_text = fulltext.split(text_to_rewrite)[1]\n", "\n", "feedback = \"escribelo como si fuera un señor de 1500 hablando castellano antiguo y estuvieras sorprendido por la tecnologia\"\n", "\n", "user_input = {\n", "    \"context\":{\n", "        \"previous_text\": previous_text,\n", "        \"post_text\": post_text,\n", "    },\n", "    \"text_to_rewrite\": text_to_rewrite,\n", "    \"feedback\": feedback\n", "}\n", "print(json.dumps(user_input, indent=2, ensure_ascii=False))\n"]}, {"cell_type": "code", "execution_count": 18, "id": "b37a820c", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "Aunque el término modelo grande de lenguaje no tiene una definición formal, a menudo se refiere a modelos de aprendizaje profundo que tienen un recuento de parámetros del orden de miles de millones o\n", "más.¡Oh maravilla sin par! Estos LLMs, que no son sino ingenios de propósito general, se muestran excelsos en multitud de labores diversas, y no se hallan limitados a una sola faena, como pudiera ser\n", "el discernimiento de sentimientos, el reconocimiento de nombres o el arte del cálculo matemático..[7]​ La habilidad con la que realizan las tareas y la gama de tareas de las que son capaces parece ser\n", "una función de la cantidad de recursos (datos, número de parámetros, capacidad de cálculo) que se les dedican, de una manera que no depende sobre avances adicionales en el diseño.[8]\n"]}], "source": ["import textwrap\n", "\n", "response = await openai_client.responses.parse(\n", "    instructions=prompt,\n", "    input=json.dumps(user_input, indent=2, ensure_ascii=False),\n", "    text_format=RewritingAnswer,\n", "    model=\"gpt-4.1-mini\",\n", "    temperature=0,\n", "    max_output_tokens=32000,\n", ")\n", "\n", "wrapped_text = \"\\n\".join(textwrap.wrap(f\"{previous_text}{response.output_parsed.text}{post_text}\", width=200, break_long_words=False, replace_whitespace=False))\n", "print(\"\\n\",wrapped_text, sep=\"\\n\")\n", "\n"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}