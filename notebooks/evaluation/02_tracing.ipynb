{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/Code/ia-gestorcontenidosiagen-be/venv/lib/python3.12/site-packages/pydantic/_internal/_fields.py:172: UserWarning: Field name \"schema\" in \"GenerateCompetenciesFromIndexInput\" shadows an attribute in parent \"GenerateCompetenciesInput\"\n", "  warnings.warn(\n", "/Users/<USER>/Code/ia-gestorcontenidosiagen-be/venv/lib/python3.12/site-packages/pydantic/_internal/_fields.py:132: UserWarning: Field \"model_info\" in RegenerateCompetenciesRequest has conflict with protected namespace \"model_\".\n", "\n", "You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.\n", "  warnings.warn(\n", "2025-01-29 16:56:16,634 - logger - INFO - Initial user 'Unir demo' already exists.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Phoenix endpoint reachable after 1 attempts.\n", "Instrument successful\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/Code/ia-gestorcontenidosiagen-be/venv/lib/python3.12/site-packages/pydantic/_internal/_config.py:341: UserWarning: Valid config keys have changed in V2:\n", "* 'allow_population_by_field_name' has been renamed to 'populate_by_name'\n", "* 'smart_union' has been removed\n", "  warnings.warn(message, UserWarning)\n", "2025-01-29 16:56:17,387 - logger - INFO - Prompt number > 5 found in the db, not creating them, if you added some new, run the prompt creation script for your specfic prompt, or add it to the db.\n"]}], "source": ["import sys\n", "from pathlib import Path\n", "\n", "parent_dir =Path.cwd().parent.parent\n", "sys.path.append(str(parent_dir))\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv()\n", "from src.api.common.dependency_container import DependencyContainer\n", "\n", "DependencyContainer.initialize(observability= True)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["tracer = DependencyContainer.get_ai_tracer()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["execution = tracer.get_specific_execution(7, 'SCHEMA_GENERATION', chain_name = \"Evaluate Samples\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'asignatura': 'Artes meméticas - Prueba end to end',\n", " 'estructura': {'bloques_tematicos': [{'temas': [{'nombre': 'Introducción a la Memética',\n", "      'epigrafes': ['Origen y evolución histórica del concepto de meme',\n", "       'Principios fundamentales de la transmisión cultural memética',\n", "       'Bases biológicas y culturales de la replicación memética']},\n", "     {'nombre': 'Anatomía y Taxonomía del Meme Digital',\n", "      'epigrafes': ['Componentes estructurales y semióticos del meme',\n", "       'Sistemas de clasificación y categorización memética',\n", "       'Patrones de evolución y mutación de memes']},\n", "     {'nombre': 'Dinámica de la Propagación Memética',\n", "      'epigrafes': ['Mecanismos de viralización y difusión digital',\n", "       'Factores psicosociales en la transmisión memética',\n", "       'Análisis cuantitativo de la propagación memética']},\n", "     {'nombre': 'Creación y Optimización de Memes',\n", "      'epigrafes': ['Metodologías avanzadas de diseño memético',\n", "       'Técnicas de adaptación multiplataforma',\n", "       'Estrategias de optimización y engagement']},\n", "     {'nombre': 'Impacto Sociocultural de la Memética',\n", "      'epigrafes': ['Transformación de la comunicación digital contemporánea',\n", "       'Influencia en movimientos sociales y culturales',\n", "       'Consideraciones éticas y responsabilidad creativa']}],\n", "    'nombre': 'FUNDAMENTOS Y APLICACIONES DE LA MEMÉTICA'}]}}"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["execution.output_data"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}