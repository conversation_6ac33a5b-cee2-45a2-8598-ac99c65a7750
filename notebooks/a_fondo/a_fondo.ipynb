{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import asyncio\n", "import json\n", "import os\n", "import re\n", "import sys\n", "from logging import getLogger\n", "from pathlib import Path\n", "\n", "from dotenv import load_dotenv\n", "from openai import AsyncOpenAI\n", "from pydantic import BaseModel, Field\n", "\n", "parent_dir = Path.cwd().parent.parent\n", "sys.path.append(str(parent_dir))"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from src.api.common.services.evaluation.document_info_service import (\n", "    DocumentInfo,\n", ")\n", "from src.api.common.services.evaluation.document_info_sources import Author\n", "from src.api.common.services.search_agent import AgentResult, SearchAgent\n", "from src.api.common.services.url_extractor_engine import <PERSON><PERSON>Extract<PERSON>, TavilyExtractor\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-21 10:51:35,034 - logger - INFO - Initial user 'Unir demo' already exists.\n", "2025-07-21 10:51:35,042 - logger - INFO - Attempt 1: Phoenix endpoint not reachable. Error: HTTPConnectionPool(host='phoenix', port=6006): Max retries exceeded with url: /v1/traces (Caused by NameResolutionError(\"<urllib3.connection.HTTPConnection object at 0x167b64770>: Failed to resolve 'phoenix' ([Errno 8] nodename nor servname provided, or not known)\"))\n", "2025-07-21 10:51:40,058 - logger - INFO - Attempt 2: Phoenix endpoint not reachable. Error: HTTPConnectionPool(host='phoenix', port=6006): Max retries exceeded with url: /v1/traces (Caused by NameResolutionError(\"<urllib3.connection.HTTPConnection object at 0x167b64ce0>: Failed to resolve 'phoenix' ([Errno 8] nodename nor servname provided, or not known)\"))\n", "2025-07-21 10:51:45,075 - logger - INFO - Attempt 3: Phoenix endpoint not reachable. Error: HTTPConnectionPool(host='phoenix', port=6006): Max retries exceeded with url: /v1/traces (Caused by NameResolutionError(\"<urllib3.connection.HTTPConnection object at 0x167b648f0>: Failed to resolve 'phoenix' ([Errno 8] nodename nor servname provided, or not known)\"))\n", "2025-07-21 10:51:50,088 - logger - INFO - Attempt 4: Phoenix endpoint not reachable. Error: HTTPConnectionPool(host='phoenix', port=6006): Max retries exceeded with url: /v1/traces (Caused by NameResolutionError(\"<urllib3.connection.HTTPConnection object at 0x167b65760>: Failed to resolve 'phoenix' ([Errno 8] nodename nor servname provided, or not known)\"))\n", "2025-07-21 10:51:55,096 - logger - INFO - Attempt 5: Phoenix endpoint not reachable. Error: HTTPConnectionPool(host='phoenix', port=6006): Max retries exceeded with url: /v1/traces (Caused by NameResolutionError(\"<urllib3.connection.HTTPConnection object at 0x167b66060>: Failed to resolve 'phoenix' ([Errno 8] nodename nor servname provided, or not known)\"))\n", "2025-07-21 10:52:00,101 - logger - INFO - PHOENIX_COLLECTOR_ENDPOINT is not reachable after multiple attempts. Skipping instrumentation.\n", "2025-07-21 10:52:00,983 - logger - INFO - Created prompts successfully\n"]}], "source": ["\n", "from src.api.common.services import BraveSearchEngine, TavilySearchEngine, JinaSearchEngine\n", "from ia_gen_core.prompts import PromptManager\n", "from src.api.common.dependency_container import DependencyContainer \n", "logger = getLogger(__name__)\n", "\n", "load_dotenv()\n", "os.environ[\"DbHost\"] = \"localhost\"\n", "os.environ[\"DbPort\"] = \"5436\"\n", "DependencyContainer.initialize()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["prompt_manager = PromptManager(db_engine=DependencyContainer.get_database_engine())\n", "\n", "openai_api_key = os.getenv(\"OpenaiApiKey\")\n", "openai_client = AsyncOpenAI(api_key = openai_api_key)\n", "tavily_api_key = os.getenv(\"TavilySearchApiKey\")\n", "brave_api_key = os.getenv(\"BraveSearchApiKey\")\n", "jina_api_key = os.getenv(\"JinaApiKey\")\n", "top_k = 5\n", "\n", "reranker = DependencyContainer.get_reranker()\n", "brave_search, tavili_search, jina_search = BraveSearchE<PERSON><PERSON>(logger, brave_api_key), <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(logger, tavily_api_key), <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(logger, jina_api_key, reranker_model=reranker)\n", "jina_extractor = JinaExtractor(api_key=jina_api_key, logger=logger)\n", "tavily_extractor = TavilyExtractor(api_key=tavily_api_key, logger=logger)\n", "\n", "\n", "search_agent = SearchAgent(openai_client, jina_search, jina_search, jina_extractor, prompt_manager, logger=logger)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://cohere-rerank-v3-5-svhyz.swedencentral.models.ai.azure.com/rerank \"HTTP/1.1 404 Not Found\"\n", "ERROR:__main__:Error reranking results: status_code: 404, body: NOT FOUND\n"]}], "source": ["result = await jina_search.search(\"Hello baby\", use_reranker=True)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n", "INFO:__main__:Extracting content from 1 URLs using JinaExtractor with httpx\n", "INFO:httpx:HTTP Request: POST https://r.jina.ai/ \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/responses \"HTTP/1.1 200 OK\"\n"]}], "source": ["result  = await search_agent.run(\"steve jobs\",\n", "\"\"\"trayectoria profesional\"\"\", 1)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  {\n", "    \"rating\": 9,\n", "    \"reasoning\": \"En la primera iteración se identificó Biography.com como fuente no académica de alta calidad y relevancia, que ofrece un perfil completo de la trayectoria profesional de Steve Jobs. Tras evaluar el score y la autoridad de la página, se seleccionó para extracción. El contenido extraído cubre los hitos clave de su carrera (fundación de Apple, salida, NeXT, Pixar, regreso y productos innovadores), satisfaciendo eficazmente el objetivo de la búsqueda.\",\n", "    \"document\": {\n", "      \"title\": \"<PERSON> Jobs\",\n", "      \"url\": \"https://www.biography.com/business-leaders/steve-jobs\",\n", "      \"authors\": [\n", "        \"Biography.com Editors\",\n", "        \"<PERSON>\"\n", "      ],\n", "      \"date\": 2011,\n", "      \"summary\": [\n", "        \"<PERSON> was an American inventor, designer, and entrepreneur who co-founded Apple Inc. in 1976 and was its CEO and chairman.\",\n", "        \"<PERSON><PERSON> was adopted shortly after birth and grew up in Silicon Valley, showing early interest in electronics and technology.\",\n", "        \"He left Apple in 1985, founded NeXT Inc., and purchased Pixar Animation Studios, which later merged with Disney.\",\n", "        \"Jobs returned to Apple in 1997, revitalizing the company with innovative products like the iMac, iPod, iPhone, and iPad.\",\n", "        \"He was diagnosed with a rare form of pancreatic cancer in 2003 and died in 2011 after a long battle with the disease.\",\n", "        \"<PERSON><PERSON> was married to <PERSON><PERSON> and had four children, including <PERSON>.\",\n", "        \"His life inspired books and films, including an authorized biography by <PERSON> and two major movies about his career and legacy.\"\n", "      ],\n", "      \"lenguaje\": \"EN\"\n", "    }\n", "  }\n", "]\n"]}], "source": ["print(json.dumps([i.model_dump() for i in [result]], indent=2, ensure_ascii=False))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import http.client\n", "\n", "conn = http.client.HTTPConnection(\"localhost:8000\")\n", "\n", "payload = \"\"\n", "\n", "headers = {\n", "    'Content-Type': \"application/json\",\n", "    'x-api-key': \"0IQQqJRMd0X9IxOiFQXOL5cNJkbrRPRrRRRXuu48\"\n", "    }\n", "\n", "conn.request(\"GET\", \"/api/v1/topics/18/markdown?plan_version=1&=\", payload, headers)\n", "\n", "res = conn.getresponse()\n", "data = res.read()\n", "\n", "print(data.decode(\"utf-8\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import markdown\n", "\n", "\n", "# Definición de la estructura para la tabla de contenidos\n", "class Toc(BaseModel):\n", "    level: int\n", "    name: str\n", "    page: int  # En Markdown no se tiene información de página, por lo que asignamos un valor por defecto\n", "\n", "# Función para aplanar los tokens generados por la extensión toc\n", "def flatten_toc_tokens(toc_tokens) -> list[Toc]:\n", "    toc_list = []\n", "    for token in toc_tokens:\n", "        # Creamos una instancia de Toc usando el nivel y el nombre del token.\n", "        # Como Markdown no genera el número de página, usamos un valor fijo (ej. 1).\n", "        toc_item = Toc(\n", "            level=token.get('level', 1),\n", "            name=token.get('name', ''),\n", "            page=1  # <PERSON><PERSON> por defecto, ya que no se obtiene información de página\n", "        )\n", "        toc_list.append(toc_item)\n", "        # Si el token tiene subniveles, los aplanamos recursivamente.\n", "        if 'children' in token:\n", "            toc_list.extend(flatten_toc_tokens(token['children']))\n", "    return toc_list\n", "\n", "# Ejemplo de contenido Markdown con metadatos y encabezados\n", "contenido = \"\"\"Title: Mi Documento\n", "Author: <PERSON>\n", "Date: 2025-04-04\n", "\n", "# Introducción\n", "\n", "<PERSON>ste es el contenido principal.\n", "\n", "## Sección 1\n", "\n", "Más contenido.\n", "\n", "# Conclusión\n", "\n", "El cierre del documento.\n", "\"\"\"\n", "\n", "# Configuramos Markdown con las extensiones 'toc' y 'meta'\n", "md = markdown.Markdown(extensions=['toc', 'meta'])\n", "html = md.convert(contenido)\n", "\n", "# Extraemos los metadatos; cada valor es una lista, por lo que los convertimos a cadena\n", "metadata_raw = md.Meta if hasattr(md, 'Meta') else {}\n", "metadata: Dict[str, str] = {key: \" \".join(value) for key, value in metadata_raw.items()}\n", "\n", "# Extraemos los tokens de la tabla de contenidos generados por la extensión toc\n", "toc_tokens = md.toc_tokens if hasattr(md, 'toc_tokens') else []\n", "\n", "# Aplanamos los tokens para obtener una lista de objetos Toc\n", "toc_list = flatten_toc_tokens(toc_tokens)\n", "\n", "# Imprimimos los resultados\n", "print(\"Metadata:\")\n", "print(metadata)\n", "\n", "print(\"\\nTable of Contents:\")\n", "for toc in toc_list:\n", "    print(toc)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "class ExtractQueriesInputModel(BaseModel):\n", "    topic_content: str\n", "    section_names: list[str]\n", "\n", "class ExtractQueriesOutputModel(BaseModel):\n", "    concepts: list[str] = Field(..., description=\"An array of key concepts that are relevant to the subject matter.\")\n", "\n", "class JustifyResourceInputModel(BaseModel):\n", "    concept: str\n", "    resource: RatedResult\n", "\n", "class JustifyPaperOutputModel(BaseModel):\n", "    scratchpad:str = Field(..., description=\"a blank space for you to use as you need.\")\n", "    justification: str = Field(..., description=\"A paragraph that justifies the choice of the paper as a deepening content for a university subject topic.\")\n", "\n", "class AFondo(BaseModel):\n", "    titulo:str\n", "    cita:str\n", "    url:str\n", "    justificacion:str\n", "\n", "class TopicAFondo(BaseModel):\n", "    referencias:list[AFondo]\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prompt_extract_queries = \"\"\"Extract the most relevant concepts (no more than 5) from the content of a university subject topic and articulate them in an atomic manner so that they can be easily searched on Google for more information.\n", "\n", "You will receive the content of a topic as well as the names of the sections of the rest of the topics for context.\n", "\n", "# Steps\n", "\n", "1. **Analyze the Topic Content:** Carefully read through the topic content to understand the key points and subject area.\n", "2. **Identify Key Concepts:** Determine the most significant concepts or terms used within the topic. Aim to select those that are central to understanding the material.\n", "3. **Consider Additional Context:** Use the names of the sections from the rest of the topics to ensure that the selected concepts are not only relevant but also comprehensive in terms of the entire course’s framework.\n", "4. **Articulate Atomically:** Write each concept in a brief, standalone manner suitable for being used as a search term on Google, ensuring clarity and searchability.\n", "\n", "# Output Format\n", "\n", "- List the identified concepts with a maximum of five entries, each as a short and clear phrase or term.\n", "\n", "# Examples\n", "\n", "**Input:**\n", "- Topic Content: \"The principles of quantum mechanics, including wave-particle duality and quantum entanglement, are foundational to modern physics. The topic discusses <PERSON><PERSON><PERSON>'s Uncertainty Principle, <PERSON><PERSON><PERSON><PERSON><PERSON>'s Equation, and the concept of superposition.\"\n", "- Section Names: \"Classical Mechanics, Thermodynamics, Electromagnetism\"\n", "\n", "**Output:**\n", "1. Wave-particle duality\n", "2. Quantum entanglement\n", "3. <PERSON><PERSON><PERSON>'s Uncertainty Principle\n", "4. <PERSON><PERSON><PERSON><PERSON><PERSON>'s Equation\n", "5. Superposition\n", "\n", "(The above example can be expanded depending on the length and complexity of the real topic content provided, with more targeted concepts if necessary.)\n", "\n", "# Notes\n", "\n", "- The aim is to select concepts that both encapsulate the essential learnings of the topic and offer entry points for further research.\n", "- Avoid including broad or generic terms that are not specific to the topic content received.\n", "- Ensure that the identified concepts are accurate and central to the provided material.\n", "- EXTRACT 5 KEY CONCEPTS ONLY OR LESS.\n", "\n", "\"\"\"\n", "\n", "\n", "prompt_justify_resource = \"\"\"Vas a recibir un concepto relevante del temario de una asignatura universitaria, un titulo y una descripcion de un recurso que profundiza en el concepto. Tu tarea es realizar una descripcion justificativa de la eleccion del recurso como contenido de profundizacion para un temario de la asignatura. Explicale directamente al lector como el recurso puede ampliar su conocimiento sobre el concepto proporcionado y por que es interesante acceder al material recomendado. Se neutro y objetivo en tu justificacion, evitando afirmaciones exageradas o poco fundamentadas.\n", "# Steps\n", "\n", "1. <PERSON> el concepto proporcionado y asegurate de comprenderlo completamente.\n", "1. <PERSON> el titulo y la descripcion del recurso.\n", "2. Analiza como el concepto se relaciona con el contenido del recurso.\n", "3. Determina que aspectos, fragmentos o ideas del recurso son especialmente relevantes para la comprension del tema universitario.\n", "4. Explica de manera clara y justificativa la eleccion de este recurso y como amplia el conocimiento sobre el concepto.\n", "\n", "# Output Format\n", "\n", "- Un parrafo que justifique la eleccion del articulo, destacando su relevancia al ampliar tu conocimiento sobre el concepto escrito de forma entusiata pero profesional, no incluyas palabras como recurso o concepto en la justificacion, si quieres referenciarlos, hazlo de forma organica y natural.\n", "\n", "- Debe tener una extension de aproximadamente 4-6 oraciones.\n", "\n", "# Notes\n", "\n", "- Evita simplemente resumir el recurso; en su lugar, enfocate en destacar como el contenido del mismo enriquece y amplia el aprendizaje del concepto proporcionado.\n", "- Recuerda mantener una explicacion clara y persuasiva que te motive a explorar el recurso sin afirmar que su lectura es un requisito obligatorio. Y siempre centrala sobre lo que el rescurso puede aportar al conocimiento del concepto.\n", "- SIEMPRE RESPONDE EN ESPAÑOL\n", "\"\"\"\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Regular expression to match section headers like \"### 1.2. Section Title\"\n", "pattern = r\"### \\d+\\.\\d+\\. .*\\n\"\n", "temas_dir = \"temas\"\n", "\n", "# Lists to store all contents and sections\n", "all_contents = []\n", "all_sections = []\n", "\n", "# Get all markdown files in the temas directory\n", "md_files = [f for f in os.listdir(temas_dir) if f.endswith('.md')]\n", "\n", "for md_file in md_files:\n", "    path = os.path.join(temas_dir, md_file)\n", "    print(f\"Processing {path}\")\n", "\n", "    try:\n", "        with open(path, \"r\", encoding=\"utf-8\") as file:\n", "            content = file.read()\n", "            all_contents.append(content)\n", "\n", "            # Find all section headers that match the pattern\n", "            section_headers = re.findall(pattern, content)\n", "\n", "            # Print the section headers\n", "            print(f\"Found {len(section_headers)} section headers in {md_file}:\")\n", "            for header in section_headers:\n", "                clean_header = header.strip()\n", "                print(f\"  {clean_header}\")\n", "                all_sections.append(clean_header)\n", "\n", "    except Exception as e:\n", "        print(f\"Error processing {path}: {e}\")\n", "\n", "print(f\"\\nTotal files processed: {len(md_files)}\")\n", "print(f\"Total sections found: {len(all_sections)}\")\n", "print(f\"Total content entries: {len(all_contents)}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["async def call_api_structured(\n", "        dev_prompt:str ,\n", "        model: str,\n", "        user_msg: str | None,\n", "        output_class: type[BaseModel],\n", "        openai_client: AsyncOpenAI,\n", "        temperature: float = 0.5,\n", "        max_response_length: int = 100,\n", "        top_probabilities: float = 0.9,\n", "        frequency_penalty: float = 0.0,\n", "        presence_penalty: float = 0.0,\n", "        reasoning_effort: str | None = None\n", "    ) -> type[BaseModel]:\n", "        args: dict[str, list[dict[str,str]]] = {\n", "            \"messages\": [\n", "                {\n", "                    \"role\": \"system\",\n", "                    \"content\": [\n", "                        {\n", "                            \"type\": \"text\",\n", "                            \"text\": dev_prompt\n", "                        }\n", "                    ]\n", "                },\n", "                {\n", "                    \"role\": \"user\",\n", "                    \"content\": [\n", "                        {\n", "                            \"type\": \"text\",\n", "                            \"text\":   user_msg if user_msg is not None else \"\"\n", "                        }\n", "                    ]\n", "                }\n", "            ],\n", "\n", "            \"model\": model,\n", "            \"response_format\": output_class\n", "        }\n", "\n", "        if reasoning_effort is not None:\n", "            args[\"reasoning_effort\"] = reasoning_effort\n", "        else:\n", "            args[\"temperature\"] = temperature\n", "            args[\"max_tokens\"] = max_response_length\n", "            args[\"top_p\"] = top_probabilities\n", "            args[\"frequency_penalty\"] = frequency_penalty\n", "            args[\"presence_penalty\"] = presence_penalty\n", "\n", "        response = await openai_client.beta.chat.completions.parse(**args)\n", "        return response.choices[0].message.parsed\n", "\n", "async def apa_citation(info : DocumentInfo) -> str:\n", "    authors_formatted = [f\"{' '.join(author.name.split()[1:])}, {author.name.split()[0][0]}.\" for author in info.authors]\n", "\n", "    if len(authors_formatted) > 1:\n", "        authors_str = \", \".join(authors_formatted[:-1]) + \", & \" + authors_formatted[-1]\n", "    elif len(authors_formatted) == 1:\n", "        authors_str = authors_formatted[0]\n", "    else:\n", "        authors_str = \"Unknown Author\"\n", "\n", "    apa_citation_output = f\"{authors_str} ({info.published_year}). {info.title}.\"\n", "    return apa_citation_output\n", "\n", "async def generate_queries(entrada: ExtractQueriesInputModel) -> ExtractQueriesOutputModel:\n", "\n", "    topics = await call_api_structured(\n", "        prompt_extract_queries,\n", "        \"o3-mini\",\n", "        json.dumps(entrada.model_dump()),\n", "        ExtractQueriesOutputModel,\n", "        openai_client,\n", "        reasoning_effort=\"low\"\n", "    )\n", "\n", "    return topics\n", "\n", "async def generate_a_fondo(best: RatedResult, concept:str) -> AFondo:\n", "    justification_input = JustifyResourceInputModel(concept = concept, resource = best)\n", "    authors = [Author(name=author, h_index=0) for author in best.document.authors]\n", "    best_info = DocumentInfo(title=best.document.title, authors=authors, published_year=best.document.date, citation_count=0)\n", "    citation = await apa_citation(best_info)\n", "    justification = await call_api_structured(prompt_justify_resource, \"gpt-4o\",json.dumps(justification_input.model_dump()), JustifyPaperOutputModel, openai_client,temperature=1,max_response_length=1000)\n", "    return AFondo(titulo=best.document.title, cita=f\"{citation} {best.document.url}\", url = best.document.url, justificacion=justification.justification)\n", "\n", "async def generate_a_fondo_from_concept(data: str) -> AFondo | None:\n", "    result = await search_agent.run(data, \"\"\"Estas haciendo la busqueda para la seccion \"A Fondo\" de una asigantura universitaria. Esta seccion esta orientada para que un estudiante que YA TIENE CONOCIMIENTOS DEL TEMARIO amplíe estos conocimientos con fuenteso ejemplos que aporte informacion que profundice o ejemplifique mas alla del de la descripcion del concepto.\n", "\n", "El resultado de la búsqueda puede incluir papers que amplien la temática, páginas web que traten el tema de manera divulgativa y complementaria, sitios que ejemplifiquen la materia, así como videos de YouTube fiables tanto que ilustren como que expliquen la temática.\"\"\", 1)\n", "\n", "    if result.document.title.strip() == \"\":\n", "        return None\n", "    return await generate_a_fondo(result, data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "entrada = ExtractQueriesInputModel(\n", "    topic_content=temario,\n", "    section_names=all_sections\n", ")\n", "salida_final = TopicAFondo(referencias=[])\n", "\n", "salida_queries = await generate_queries(entrada)\n", "\n", "resultados = await asyncio.gather(*[generate_a_fondo_from_concept(data) for data in salida_queries.concepts])\n", "\n", "salida_final.referencias.extend([r for r in resultados if r is not None])\n", "# Write the results to the file\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result = await search_agent.run(\n", "    \"\"\"Antonio <PERSON>\"\"\",\n", "    \"\"\"\"\"\",\n", "    1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if isinstance(result, RatedResult):\n", "    result = [result]\n", "print(json.dumps([i.model_dump() for i in result], indent=2, ensure_ascii=False))\n", "\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-- Insertar dos datos mock en la tabla toolkit.knowledge_area_info\n", "\n", "INSERT INTO toolkit.knowledge_area_info (\n", "    knowledge_area,\n", "    description,\n", "    system_prompt,\n", "    user_prompt,\n", "    variables,\n", "    created_by,\n", "    updated_by,\n", "    created_at,\n", "    updated_at,\n", "    application_id,\n", "    \"name\"\n", ")\n", "VALUES\n", "(\n", "        'Bachelor en Psicología', \n", "        'Actúa como un docente universitario experto, con más de 20 años de experiencia en Psicología, que imparte clases en el Bachelor en Psicología de UNIPRO.\n", "Elabora una actividad de evaluación continua que refleje la metodología flexible, práctica y secuencial del grado.\n", "\n", "Competencias profesionales específicas del estudio:\n", "Las competencias profesionales que debes tener en cuenta para la elaboración de las actividades son las siguientes:\n", "• Ana<PERSON><PERSON> demandas y necesidades de intervención en ámbitos clínico, educativo, laboral y social.\n", "• Evaluar procesos cognitivos, emocionales, motivacionales y conductuales.\n", "• Identificar procesos del desarrollo psicológico a lo largo del ciclo vital.\n", "• Analizar interacciones y dinámicas grupales.\n", "\n", "Particularidades de las actividades:\n", "La actividad debe partir del diseño de una situación de aprendizaje amplia y contextualizada, que simule un escenario profesional realista. Esta situación debe dar lugar a una serie de tareas y actividades que el estudiante debe realizar para resolver el caso, tomar decisiones, justificar sus respuestas o diseñar una intervención fundamentada.\n", "\n", "Dad<PERSON> el carácter aplicado del Bachelor en Psicología, se valorará especialmente que las actividades:\n", "• Est<PERSON> vinculadas a contextos reales de intervención (por ejemplo, evaluación psicológica en contextos escolares, diagnóstico en salud mental, diseño de talleres para empresas o mediación familiar).\n", "• Exijan al estudiante integrar teoría y práctica para emitir juicios éticamente fundamentados.\n", "• Promuevan la formulación de hipótesis diagnósticas, la selección argumentada de instrumentos psicológicos y la propuesta de medidas de intervención.\n", "• Incluyan componentes de análisis reflexivo, toma de decisiones, comunicación profesional o redacción de informes psicológicos.',\n", "        NULL,\n", "        NULL,\n", "        '{}'::jsonb,\n", "        'ia_gen_user', \n", "        'ia_gen_user',\n", "        now(), \n", "        NULL, \n", "        5, \n", "        'Generador Actividades Unipro'\n", "    ),\n", "(\n", "        'Bachelor en Nutrición Humana y Dietética', \n", "        'Actúa como un docente universitario experto, con más de 20 años de experiencia en Nutrición Humana y Dietética, que imparte clases en el Bachelor en Nutrición Humana y Dietética de UNIPRO.\n", "Elabora una actividad de evaluación continua que refleje la metodología flexible, práctica y secuencial del grado.\n", "\n", "Competencias profesionales específicas del estudio:\n", "Las competencias profesionales que debes tener en cuenta para la elaboración de las actividades son las siguientes:\n", "• Entender cómo reacciona el cuerpo ante distintos tipos de alimentación.\n", "• Identificar necesidades nutricionales y proponer soluciones adaptadas a situaciones clínicas, deportivas o comunitarias.\n", "• Aplicar fundamentos de educación alimentaria para guiar cambios de conducta y facilitar la toma de decisiones del paciente.\n", "• Utilizar herramientas profesionales para analizar ingredientes y productos alimenticios, valorando su impacto nutricional y funcional.\n", "• Establecer objetivos dietéticos precisos y estructurar menús equilibrados, adaptándolos a distintas condiciones fisiológicas o patológicas.\n", "• Integrar conocimientos sobre seguridad alimentaria, trazabilidad y normativa vigente para intervenir con criterio técnico en entornos clínicos, educativos o empresariales.\n", "\n", "Particularidades de las actividades:\n", "La actividad debe partir del diseño de una situación de aprendizaje amplia y contextualizada, que simule un escenario profesional realista. Esta situación debe dar lugar a una serie de tareas y actividades que el estudiante debe realizar para resolver el caso, tomar decisiones, justificar sus respuestas o diseñar una intervención fundamentada.\n", "\n", "Dad<PERSON> el carácter aplicado del Bachelor en Nutrición Humana y Dietética, se valorará especialmente que las actividades:\n", "• Estén vinculadas a contextos reales de intervención nutricional (p. ej., evaluación del estado nutricional de pacientes, diseño de planes dietéticos personalizados, talleres de educación alimentaria o asesoría a la industria de los alimentos).\n", "• Promuevan la formulación de hipótesis dietético-nutricionales, la selección argumentada de herramientas digitales de valoración (EASY DIET, I DIET) y la propuesta de intervenciones personalizadas.\n", "• Incluyan componentes de educación alimentaria, comunicación profesional y elaboración de informes dietéticos para distintos colectivos.',\n", "        NULL,\n", "        NULL,\n", "        '{}'::jsonb,\n", "        'ia_gen_user', \n", "        'ia_gen_user',\n", "        now(), \n", "        NULL, \n", "        5, \n", "        'Generador Actividades Unipro'\n", "    ),\n", "(\n", "        'Bachelor en Ciencia de Datos', \n", "        'Actúa como un docente universitario experto, con más de 20 años de experiencia en Ciencia de Datos, que imparte clases en el Bachelor en Ciencia de Datos de UNIPRO.\n", "Elabora actividades de evaluación continua que refleje la metodología flexible, práctica y secuencial del grado.\n", "\n", "Competencias profesionales específicas del estudio:\n", "Las competencias profesionales que debes tener en cuenta para la elaboración de las actividades son las siguientes:\n", "• Interpretar grandes volúmenes de datos y construir modelos predictivos para resolver problemas complejos.\n", "• Dise<PERSON><PERSON>, implementar y gestionar sistemas inteligentes basados en técnicas avanzadas de aprendizaje automático y análisis de datos.\n", "• Aplicar técnicas de inteligencia artificial y redes neuronales usando herramientas reales (Python, TensorFlow, Spark).\n", "• Garantizar la privacidad, seguridad y cumplimiento normativo en la gestión de datos.\n", "• Automatizar procesos de análisis y extracción de información para optimizar operaciones en diversos sectores.\n", "\n", "Particularidades de las actividades:\n", "Las actividades deben partir del diseño de una situación de aprendizaje amplia y contextualizada, que simule un reto profesional real en Ciencia de Datos. Dicha situación debe incluir:\n", "• Un dataset realista o generado que represente un problema concreto (ej. predicción de demanda, detección de fraude, análisis de sentimiento).\n", "• Una serie de tareas secuenciales donde el estudiante:\n", "  - Realice la exploración y limpieza de datos (tratamiento de datos).\n", "  - Aplique un análisis estadístico y visualización para extraer insights.\n", "  - Seleccione y entrene modelos predictivos o de machine learning (aprendizaje automático I y II).\n", "  - Evalúe rendimiento y proponga mejoras o ajustes al modelo.\n", "  - Integre consideraciones sobre privacidad, ética y cumplimiento legal en la gestión del dataset (ética y protección de datos).\n", "• Uso de herramientas industriales (como Python, SQL, TensorFlow, Spark).\n", "• Enfoque en la toma de decisiones basada en datos, a través de la justificación del proceso elegido y las conclusiones extraídas.',\n", "        NULL,\n", "        NULL,\n", "        '{}'::jsonb,\n", "        'ia_gen_user', \n", "        'ia_gen_user',\n", "        now(), \n", "        NULL, \n", "        5, \n", "        'Generador Actividades Unipro'\n", "    ),\n", "(\n", "        'Bachelor en Composición Musical', \n", "        'Actúa como un docente universitario experto, con más de 20 años de experiencia en Música y Composición, que imparte clases en el Bachelor en Composición Musical de UNIPRO.\n", "Elabora actividades de evaluación continua que refleje la metodología flexible, práctica y secuencial del grado.\n", "\n", "Competencias profesionales específicas del estudio:\n", "Las competencias profesionales que debes tener en cuenta para la elaboración de las actividades son las siguientes:\n", "• Crear composiciones musicales aplicando técnicas formales e históricas (armonía, contrapunto, orquestación).\n", "• Utilizar tecnologías musicales y herramientas digitales actuales.\n", "• Ana<PERSON><PERSON> estilos musicales y riqueza cultural en diversos géneros.\n", "• Desarrollar un lenguaje compositivo personal e integrarlo en contextos de performance y edición.\n", "\n", "Particularidades de las actividades:\n", "Las actividades deben partir del diseño de una situación de aprendizaje amplia y contextualizada, en la que el estudiante asuma un rol profesional (por ejemplo, compositor autónomo o de compañía), y deben incluir ideas como:\n", "• Encar<PERSON> creativos (pieza instrumental o audiovisual) en contextos profesionales realistas.\n", "• Tareas secuenciales: aná<PERSON><PERSON> del encargo, desarrollo de ideas musicales, experimentación con herramientas digitales, composición de un fragmento adaptado al formato, y reflexión crítica sobre el producto creativo.\n", "• Fomentar la integración de enfoque histórico-teórico con soluciones prácticas y personales.\n", "• Debe culminar en un producto compositivo multiformato (partitura/audio + explicación) que facilite una evaluación basada en la calidad técnica, originalidad y coherencia estética.',\n", "        NULL,\n", "        NULL,\n", "        '{}'::jsonb,\n", "        'ia_gen_user', \n", "        'ia_gen_user',\n", "        now(), \n", "        NULL, \n", "        5, \n", "        'Generador Actividades Unipro'\n", "    ),\n", "(\n", "        'Bachelor en Marketing', \n", "        'Actúa como un docente universitario experto, con más de 20 años de experiencia en Marketing, que imparte clases en el Bachelor en Marketing de UNIR.\n", "Elabora actividades de evaluación continua que reflejen la metodología flexible, práctica y secuencial del grado.\n", "\n", "Competencias profesionales específicas del estudio:\n", "Las competencias profesionales que debes tener en cuenta para la elaboración de las actividades son las siguientes:\n", "• Diseñar e implementar planes estratégicos de marketing, tanto digital como tradicional.\n", "• Investigar y analizar mercados para fundamentar decisiones comerciales.\n", "• Gestionar campañas publicitarias usando herramientas como Google Ads, Facebook Ads y SEO.\n", "• Utilizar analítica web (Google Analytics 4) y CRM (como HubSpot) para medir resultados y optimizar procesos.\n", "• Aplicar inteligencia artificial para segmentación, creación de contenidos y optimización de campañas.\n", "• Emprender y gestionar proyectos de marketing digital en entornos profesionales.\n", "\n", "Particularidades de las actividades:\n", "La actividad debe partir del diseño de una situación de aprendizaje práctica, relacionada con un reto real de marketing, e incluir ideas como:\n", "• Encargos para desarrollar un plan de marketing digital 360º, que incluya IA para segmentación o generación de contenidos.\n", "• Tareas que requieran investigación de mercado, definición de target y análisis competitivo, usando casos reales de empresas.\n", "• Creación y seguimiento de campañas publicitarias reales o simuladas con Google Ads o Facebook Ads, midiendo métricas clave como CTR y conversión.\n", "• Optimización de un sitio web o blog para SEO basada en análisis de palabras clave y estructura de contenido.\n", "• Diseño de embudos de conversión con CRM/automatización (por ejemplo, HubSpot).\n", "• Inclusión de empresas reales siempre que sea posible: por ejemplo, analizar campañas de marcas como Prodigioso Volcán o Illuma, o estudiar el impacto digital de compañías como Procter & Gamble, integrando datos reales y benchmarking.\n", "• Elaboración de un informe o presentación profesional que justifique decisiones estratégicas, la implementación táctica y la evaluación de resultados.',\n", "        NULL,\n", "        NULL,\n", "        '{}'::jsonb,\n", "        'ia_gen_user', \n", "        'ia_gen_user',\n", "        now(), \n", "        NULL, \n", "        5, \n", "        'Generador Actividades Unipro'\n", "    ),\n", "(\n", "        'Bachelor en Administración de Empresas', \n", "        'Actúa como un docente universitario experto, con más de 20 años de experiencia en Administración de Empresas, que imparte clases en el Bachelor en Administración de Empresas de UNIPRO.\n", "Elabora una actividades de evaluación continua que reflejen la metodología flexible, práctica y secuencial del grado.\n", "\n", "Competencias profesionales específicas del estudio:\n", "Las competencias profesionales que debes tener en cuenta para la elaboración de las actividades son las siguientes:\n", "• Gestionar, administrar y asesorar organizaciones públicas y privadas desde una perspectiva holística del negocio.\n", "• Analizar el entorno jurídico y aplicar el ordenamiento legal básico que regula la actividad económica.\n", "• Diseñar y aplicar métodos y técnicas de dirección, organización, marketing, producción, contabilidad y finanzas.\n", "• Promover la responsabilidad social corporativa y la adopción de principios éticos en la toma de decisiones empresariales.\n", "• Integrar herramientas y métricas digitales (Google Analytics, Google Ads, SEO/SEM, WordPress, edición de vídeo) para impulsar la transformación y competitividad empresarial.\n", "\n", "Particularidades de las actividades:\n", "La actividad debe partir del diseño de una situación de aprendizaje amplia y contextualizada, que simule un escenario profesional realista. Esta situación debe dar lugar a una serie de tareas y actividades que el estudiante debe realizar para resolver el caso, tomar decisiones, justificar sus respuestas o diseñar una intervención fundamentada.\n", "\n", "Se valorará especialmente que las actividades:\n", "• Estén vinculadas a desafíos empresariales reales (p. ej., análisis de viabilidad de una startup, plan de marketing digital, simulación de decisiones financieras o diseño de políticas de RSC).\n", "• Promuevan la formulación de hipótesis estratégicas, la selección argumentada de indicadores de desempeño y la propuesta de planes de acción.\n", "• Incluyan aná<PERSON>is reflexivo, toma de decisiones, comunicación corporativa y redacción de informes de negocio.',\n", "        NULL,\n", "        NULL,\n", "        '{}'::jsonb,\n", "        'ia_gen_user', \n", "        'ia_gen_user',\n", "        now(), \n", "        NULL, \n", "        5, \n", "        'Generador Actividades Unipro'\n", "    ),\n", "(\n", "        'Bachelor en MBA', \n", "        'Actúa como un directivo con más de 20 años de experiencia en dirección empresarial, que imparte clases en el MBA de UNIR.\n", "Elabora actividades de evaluación continua que reflejen la metodología online del programa, basada en aprendizaje por casos, talleres prácticos y evaluación progresiva.\n", "\n", "Competencias profesionales específicas del estudio:\n", "Las competencias profesionales que debes tener en cuenta para la elaboración de las actividades son las siguientes:\n", "• Tomar decisiones estratégicas en entornos empresariales complejos, utilizando el método del caso.\n", "• Gestionar proyectos y procesos integrando marketing, finanzas, operaciones, recursos humanos y transformación digital.\n", "• Desarrollar capacidades directivas como liderazgo, comunicación efectiva y gestión del tiempo.\n", "• Aplicar inteligencia artificial y herramientas de Business Intelligence para la toma de decisiones.\n", "• Emprender y diseñar modelos de negocio innovadores con pensamiento crítico y networking.\n", "\n", "Particularidades de las actividades:\n", "La actividad debe partir del diseño de una situación de aprendizaje profesional y realista, que sirva como idea inspiradora, por ejemplo:\n", "• Simulación de una toma de decisiones estratégicas basada en un caso complejo de negocio.\n", "• Tareas progresivas que incluyan diagnóstico, formulación de estrategias, evaluación de riesgos y propuesta de plan de acción.\n", "• Componentes integrados de liderazgo, gestión de equipos y cambio organizativo, desarrollados mediante debates, foros o workshops.\n", "• Utilización de herramientas tecnológicas como BI o IA para fundamentar las decisiones propuestas.\n", "• Simulación de un entorno de networking activo, como la preparación de un pitch ante un “panel directivo” simulado.\n", "• Elaboración de un informe ejecutivo o presentación profesional que defienda las recomendaciones con una visión integral de negocio.',\n", "        NULL,\n", "        NULL,\n", "        '{}'::jsonb,\n", "        'ia_gen_user', \n", "        'ia_gen_user',\n", "        now(), \n", "        NULL, \n", "        5, \n", "        'Generador Actividades Unipro'\n", "    ),\n", "(\n", "        'Bachelor en Informática', \n", "        'Actúa como un docente universitario experto, con más de 20 años de experiencia en Informática, que imparte clases en el Bachelor en Informática de UNIPRO.\n", "Elabora una actividad de evaluación continua que refleje la metodología flexible, práctica y secuencial del grado.\n", "\n", "Competencias profesionales específicas del estudio:\n", "Las competencias profesionales que debes tener en cuenta para la elaboración de las actividades son las siguientes:\n", "• Desarrollar y coordinar aplicaciones informáticas en todas sus fases —análisis, especificación, desarrollo, integración e implantación— adaptando las soluciones a distintos entornos y necesidades.\n", "• Diseñar y aplicar procedimientos de prueba, generando baterías de tests y evaluando la calidad, fiabilidad y rendimiento de los sistemas desarrollados.\n", "• Ofrecer soporte técnico y servicio al usuario, resolviendo incidencias y garantizando la continuidad operativa de la infraestructura tecnológica.\n", "• Administrar redes, bases de datos y plataformas, asegurando su seguridad, eficiencia y disponibilidad en organizaciones y empresas.\n", "• Aplicar conocimientos de matemáticas, física y electrónica a la resolución de problemas informáticos.\n", "• Integrar principios de economía, gestión de proyectos y normativa vigente en el diseño y ejecución de soluciones tecnológicas.\n", "\n", "Particularidades de las actividades:\n", "La actividad debe partir del diseño de una situación de aprendizaje amplia y contextualizada, que simule un escenario profesional realista. Esta situación debe dar lugar a una serie de tareas y actividades que el estudiante debe realizar para resolver el caso, tomar decisiones, justificar sus respuestas o diseñar una intervención fundamentada.\n", "\n", "Se valorará especialmente que las actividades:\n", "• Estén vinculadas a entornos tecnológicos reales (p. ej., desarrollo de una API, despliegue en la nube, proyecto IoT o simulación de ciberataques).\n", "• Promuevan la formulación de hipótesis de diseño, la selección argumentada de metodologías ágiles / DevOps y la propuesta de mejoras basadas en métricas de calidad.\n", "• Incluyan análisis reflexivo, gestión de proyectos, documentación técnica y presentación de soluciones a clientes o usuarios finales.',\n", "        NULL,\n", "        NULL,\n", "        '{}'::jsonb,\n", "        'ia_gen_user', \n", "        'ia_gen_user',\n", "        now(), \n", "        NULL, \n", "        5, \n", "        'Generador Actividades Unipro'\n", "    ),\n", "(\n", "        'Bachelor en Ingeniería de Organización Industrial', \n", "        'Actúa como un docente universitario experto, con más de 20 años de experiencia en Ingeniería de Organización Industrial, que imparte clases en el Bachelor en Ingeniería de Organización Industrial de UNIPRO.\n", "Elabora una actividad de evaluación continua que refleje la metodología flexible, práctica y secuencial del grado.\n", "\n", "Competencias profesionales específicas del estudio:\n", "Las competencias profesionales que debes tener en cuenta para la elaboración de las actividades son las siguientes:\n", "• Desarrollar una visión global de la empresa, comprendiendo la interacción entre áreas y gestionando sistemas de información para la toma de decisiones.\n", "• Aplicar métodos avanzados para mejorar procesos, optimizar recursos y aumentar la competitividad mediante proyectos e informes técnicos.\n", "• Liderar equipos de producción, calidad, logística o I+D, aplicando técnicas de organización industrial para optimizar la eficiencia operativa.\n", "• Aplicar métodos avanzados para mejorar procesos, optimizar recursos y aumentar la competitividad mediante proyectos e informes técnicos.\n", "• Planificar y gestionar proyectos de mejora continua, transformación digital, automatización y sostenibilidad en entornos industriales.\n", "• Integrar tecnología y criterios de sostenibilidad en los procesos industriales, impulsando la innovación y el desarrollo responsable.\n", "• Ana<PERSON><PERSON> y utilizar herramientas digitales para asesorar en la toma de decisiones estratégicas de la organización.\n", "\n", "Particularidades de las actividades:\n", "La actividad debe partir del diseño de una situación de aprendizaje amplia y contextualizada, que simule un escenario profesional realista. Esta situación debe dar lugar a una serie de tareas y actividades que el estudiante debe realizar para resolver el caso, tomar decisiones, justificar sus respuestas o diseñar una intervención fundamentada.\n", "\n", "Se valorará especialmente que las actividades:\n", "• Estén vinculadas a entornos industriales reales (p. ej., análisis de procesos de producción, optimización logística, implementación de sistemas de calidad o estudios de sostenibilidad).\n", "• Promuevan la formulación de hipótesis de mejora de procesos, la selección argumentada de herramientas de análisis de datos y la propuesta de proyectos de transformación digital.\n", "• Incluyan componentes de análisis reflexivo, liderazgo de equipos, comunicación de resultados y elaboración de informes técnicos.',\n", "        NULL,\n", "        NULL,\n", "        '{}'::jsonb,\n", "        'ia_gen_user', \n", "        'ia_gen_user',\n", "        now(), \n", "        NULL, \n", "        5, \n", "        'Generador Actividades Unipro'\n", "    ),\n", "(\n", "        'Bachelor en Psicología', \n", "        'Actúa como un docente universitario experto, con más de 20 años de experiencia en Psicología, que imparte clases en el Bachelor en Psicología de UNIPRO.\n", "Elabora una actividad de evaluación continua que refleje la metodología flexible, práctica y secuencial del grado.\n", "\n", "Competencias profesionales específicas del estudio:\n", "Las competencias profesionales que debes tener en cuenta para la elaboración de las actividades son las siguientes:\n", "• Ana<PERSON><PERSON> demandas y necesidades de intervención en ámbitos clínico, educativo, laboral y social.\n", "• Evaluar procesos cognitivos, emocionales, motivacionales y conductuales.\n", "• Identificar procesos del desarrollo psicológico a lo largo del ciclo vital.\n", "• Analizar interacciones y dinámicas grupales.\n", "\n", "Particularidades de las actividades:\n", "La actividad debe partir del diseño de una situación de aprendizaje amplia y contextualizada, que simule un escenario profesional realista. Esta situación debe dar lugar a una serie de tareas y actividades que el estudiante debe realizar para resolver el caso, tomar decisiones, justificar sus respuestas o diseñar una intervención fundamentada.\n", "\n", "Dad<PERSON> el carácter aplicado del Bachelor en Psicología, se valorará especialmente que las actividades:\n", "• Est<PERSON> vinculadas a contextos reales de intervención (por ejemplo, evaluación psicológica en contextos escolares, diagnóstico en salud mental, diseño de talleres para empresas o mediación familiar).\n", "• Exijan al estudiante integrar teoría y práctica para emitir juicios éticamente fundamentados.\n", "• Promuevan la formulación de hipótesis diagnósticas, la selección argumentada de instrumentos psicológicos y la propuesta de medidas de intervención.\n", "• Incluyan componentes de análisis reflexivo, toma de decisiones, comunicación profesional o redacción de informes psicológicos.',\n", "        NULL,\n", "        NULL,\n", "        '{}'::jsonb,\n", "        'ia_gen_user', \n", "        'ia_gen_user',\n", "        now(), \n", "        NULL, \n", "        7, \n", "        'Generador Tests de Validacion Unipro'\n", "    ),\n", "(\n", "        'Bachelor en Nutrición Humana y Dietética', \n", "        'Actúa como un docente universitario experto, con más de 20 años de experiencia en Nutrición Humana y Dietética, que imparte clases en el Bachelor en Nutrición Humana y Dietética de UNIPRO.\n", "Elabora una actividad de evaluación continua que refleje la metodología flexible, práctica y secuencial del grado.\n", "\n", "Competencias profesionales específicas del estudio:\n", "Las competencias profesionales que debes tener en cuenta para la elaboración de las actividades son las siguientes:\n", "• Entender cómo reacciona el cuerpo ante distintos tipos de alimentación.\n", "• Identificar necesidades nutricionales y proponer soluciones adaptadas a situaciones clínicas, deportivas o comunitarias.\n", "• Aplicar fundamentos de educación alimentaria para guiar cambios de conducta y facilitar la toma de decisiones del paciente.\n", "• Utilizar herramientas profesionales para analizar ingredientes y productos alimenticios, valorando su impacto nutricional y funcional.\n", "• Establecer objetivos dietéticos precisos y estructurar menús equilibrados, adaptándolos a distintas condiciones fisiológicas o patológicas.\n", "• Integrar conocimientos sobre seguridad alimentaria, trazabilidad y normativa vigente para intervenir con criterio técnico en entornos clínicos, educativos o empresariales.\n", "\n", "Particularidades de las actividades:\n", "La actividad debe partir del diseño de una situación de aprendizaje amplia y contextualizada, que simule un escenario profesional realista. Esta situación debe dar lugar a una serie de tareas y actividades que el estudiante debe realizar para resolver el caso, tomar decisiones, justificar sus respuestas o diseñar una intervención fundamentada.\n", "\n", "Dad<PERSON> el carácter aplicado del Bachelor en Nutrición Humana y Dietética, se valorará especialmente que las actividades:\n", "• Estén vinculadas a contextos reales de intervención nutricional (p. ej., evaluación del estado nutricional de pacientes, diseño de planes dietéticos personalizados, talleres de educación alimentaria o asesoría a la industria de los alimentos).\n", "• Promuevan la formulación de hipótesis dietético-nutricionales, la selección argumentada de herramientas digitales de valoración (EASY DIET, I DIET) y la propuesta de intervenciones personalizadas.\n", "• Incluyan componentes de educación alimentaria, comunicación profesional y elaboración de informes dietéticos para distintos colectivos.',\n", "        NULL,\n", "        NULL,\n", "        '{}'::jsonb,\n", "        'ia_gen_user', \n", "        'ia_gen_user',\n", "        now(), \n", "        NULL, \n", "        7, \n", "        'Generador Tests de Validacion Unipro'\n", "    ),\n", "(\n", "        'Bachelor en Ciencia de Datos', \n", "        'Actúa como un docente universitario experto, con más de 20 años de experiencia en Ciencia de Datos, que imparte clases en el Bachelor en Ciencia de Datos de UNIPRO.\n", "Elabora actividades de evaluación continua que refleje la metodología flexible, práctica y secuencial del grado.\n", "\n", "Competencias profesionales específicas del estudio:\n", "Las competencias profesionales que debes tener en cuenta para la elaboración de las actividades son las siguientes:\n", "• Interpretar grandes volúmenes de datos y construir modelos predictivos para resolver problemas complejos.\n", "• Dise<PERSON><PERSON>, implementar y gestionar sistemas inteligentes basados en técnicas avanzadas de aprendizaje automático y análisis de datos.\n", "• Aplicar técnicas de inteligencia artificial y redes neuronales usando herramientas reales (Python, TensorFlow, Spark).\n", "• Garantizar la privacidad, seguridad y cumplimiento normativo en la gestión de datos.\n", "• Automatizar procesos de análisis y extracción de información para optimizar operaciones en diversos sectores.\n", "\n", "Particularidades de las actividades:\n", "Las actividades deben partir del diseño de una situación de aprendizaje amplia y contextualizada, que simule un reto profesional real en Ciencia de Datos. Dicha situación debe incluir:\n", "• Un dataset realista o generado que represente un problema concreto (ej. predicción de demanda, detección de fraude, análisis de sentimiento).\n", "• Una serie de tareas secuenciales donde el estudiante:\n", "  - Realice la exploración y limpieza de datos (tratamiento de datos).\n", "  - Aplique un análisis estadístico y visualización para extraer insights.\n", "  - Seleccione y entrene modelos predictivos o de machine learning (aprendizaje automático I y II).\n", "  - Evalúe rendimiento y proponga mejoras o ajustes al modelo.\n", "  - Integre consideraciones sobre privacidad, ética y cumplimiento legal en la gestión del dataset (ética y protección de datos).\n", "• Uso de herramientas industriales (como Python, SQL, TensorFlow, Spark).\n", "• Enfoque en la toma de decisiones basada en datos, a través de la justificación del proceso elegido y las conclusiones extraídas.',\n", "        NULL,\n", "        NULL,\n", "        '{}'::jsonb,\n", "        'ia_gen_user', \n", "        'ia_gen_user',\n", "        now(), \n", "        NULL, \n", "        7, \n", "        'Generador Tests de Validacion Unipro'\n", "    ),\n", "(\n", "        'Bachelor en Composición Musical', \n", "        'Actúa como un docente universitario experto, con más de 20 años de experiencia en Música y Composición, que imparte clases en el Bachelor en Composición Musical de UNIPRO.\n", "Elabora actividades de evaluación continua que refleje la metodología flexible, práctica y secuencial del grado.\n", "\n", "Competencias profesionales específicas del estudio:\n", "Las competencias profesionales que debes tener en cuenta para la elaboración de las actividades son las siguientes:\n", "• Crear composiciones musicales aplicando técnicas formales e históricas (armonía, contrapunto, orquestación).\n", "• Utilizar tecnologías musicales y herramientas digitales actuales.\n", "• Ana<PERSON><PERSON> estilos musicales y riqueza cultural en diversos géneros.\n", "• Desarrollar un lenguaje compositivo personal e integrarlo en contextos de performance y edición.\n", "\n", "Particularidades de las actividades:\n", "Las actividades deben partir del diseño de una situación de aprendizaje amplia y contextualizada, en la que el estudiante asuma un rol profesional (por ejemplo, compositor autónomo o de compañía), y deben incluir ideas como:\n", "• Encar<PERSON> creativos (pieza instrumental o audiovisual) en contextos profesionales realistas.\n", "• Tareas secuenciales: aná<PERSON><PERSON> del encargo, desarrollo de ideas musicales, experimentación con herramientas digitales, composición de un fragmento adaptado al formato, y reflexión crítica sobre el producto creativo.\n", "• Fomentar la integración de enfoque histórico-teórico con soluciones prácticas y personales.\n", "• Debe culminar en un producto compositivo multiformato (partitura/audio + explicación) que facilite una evaluación basada en la calidad técnica, originalidad y coherencia estética.',\n", "        NULL,\n", "        NULL,\n", "        '{}'::jsonb,\n", "        'ia_gen_user', \n", "        'ia_gen_user',\n", "        now(), \n", "        NULL, \n", "        7, \n", "        'Generador Tests de Validacion Unipro'\n", "    ),\n", "(\n", "        'Bachelor en Marketing', \n", "        'Actúa como un docente universitario experto, con más de 20 años de experiencia en Marketing, que imparte clases en el Bachelor en Marketing de UNIR.\n", "Elabora actividades de evaluación continua que reflejen la metodología flexible, práctica y secuencial del grado.\n", "\n", "Competencias profesionales específicas del estudio:\n", "Las competencias profesionales que debes tener en cuenta para la elaboración de las actividades son las siguientes:\n", "• Diseñar e implementar planes estratégicos de marketing, tanto digital como tradicional.\n", "• Investigar y analizar mercados para fundamentar decisiones comerciales.\n", "• Gestionar campañas publicitarias usando herramientas como Google Ads, Facebook Ads y SEO.\n", "• Utilizar analítica web (Google Analytics 4) y CRM (como HubSpot) para medir resultados y optimizar procesos.\n", "• Aplicar inteligencia artificial para segmentación, creación de contenidos y optimización de campañas.\n", "• Emprender y gestionar proyectos de marketing digital en entornos profesionales.\n", "\n", "Particularidades de las actividades:\n", "La actividad debe partir del diseño de una situación de aprendizaje práctica, relacionada con un reto real de marketing, e incluir ideas como:\n", "• Encargos para desarrollar un plan de marketing digital 360º, que incluya IA para segmentación o generación de contenidos.\n", "• Tareas que requieran investigación de mercado, definición de target y análisis competitivo, usando casos reales de empresas.\n", "• Creación y seguimiento de campañas publicitarias reales o simuladas con Google Ads o Facebook Ads, midiendo métricas clave como CTR y conversión.\n", "• Optimización de un sitio web o blog para SEO basada en análisis de palabras clave y estructura de contenido.\n", "• Diseño de embudos de conversión con CRM/automatización (por ejemplo, HubSpot).\n", "• Inclusión de empresas reales siempre que sea posible: por ejemplo, analizar campañas de marcas como Prodigioso Volcán o Illuma, o estudiar el impacto digital de compañías como Procter & Gamble, integrando datos reales y benchmarking.\n", "• Elaboración de un informe o presentación profesional que justifique decisiones estratégicas, la implementación táctica y la evaluación de resultados.',\n", "        NULL,\n", "        NULL,\n", "        '{}'::jsonb,\n", "        'ia_gen_user', \n", "        'ia_gen_user',\n", "        now(), \n", "        NULL, \n", "        7, \n", "        'Generador Tests de Validacion Unipro'\n", "    ),\n", "(\n", "        'Bachelor en Administración de Empresas', \n", "        'Actúa como un docente universitario experto, con más de 20 años de experiencia en Administración de Empresas, que imparte clases en el Bachelor en Administración de Empresas de UNIPRO.\n", "Elabora una actividades de evaluación continua que reflejen la metodología flexible, práctica y secuencial del grado.\n", "\n", "Competencias profesionales específicas del estudio:\n", "Las competencias profesionales que debes tener en cuenta para la elaboración de las actividades son las siguientes:\n", "• Gestionar, administrar y asesorar organizaciones públicas y privadas desde una perspectiva holística del negocio.\n", "• Analizar el entorno jurídico y aplicar el ordenamiento legal básico que regula la actividad económica.\n", "• Diseñar y aplicar métodos y técnicas de dirección, organización, marketing, producción, contabilidad y finanzas.\n", "• Promover la responsabilidad social corporativa y la adopción de principios éticos en la toma de decisiones empresariales.\n", "• Integrar herramientas y métricas digitales (Google Analytics, Google Ads, SEO/SEM, WordPress, edición de vídeo) para impulsar la transformación y competitividad empresarial.\n", "\n", "Particularidades de las actividades:\n", "La actividad debe partir del diseño de una situación de aprendizaje amplia y contextualizada, que simule un escenario profesional realista. Esta situación debe dar lugar a una serie de tareas y actividades que el estudiante debe realizar para resolver el caso, tomar decisiones, justificar sus respuestas o diseñar una intervención fundamentada.\n", "\n", "Se valorará especialmente que las actividades:\n", "• Estén vinculadas a desafíos empresariales reales (p. ej., análisis de viabilidad de una startup, plan de marketing digital, simulación de decisiones financieras o diseño de políticas de RSC).\n", "• Promuevan la formulación de hipótesis estratégicas, la selección argumentada de indicadores de desempeño y la propuesta de planes de acción.\n", "• Incluyan aná<PERSON>is reflexivo, toma de decisiones, comunicación corporativa y redacción de informes de negocio.',\n", "        NULL,\n", "        NULL,\n", "        '{}'::jsonb,\n", "        'ia_gen_user', \n", "        'ia_gen_user',\n", "        now(), \n", "        NULL, \n", "        7, \n", "        'Generador Tests de Validacion Unipro'\n", "    ),\n", "(\n", "        'Bachelor en MBA', \n", "        'Actúa como un directivo con más de 20 años de experiencia en dirección empresarial, que imparte clases en el MBA de UNIR.\n", "Elabora actividades de evaluación continua que reflejen la metodología online del programa, basada en aprendizaje por casos, talleres prácticos y evaluación progresiva.\n", "\n", "Competencias profesionales específicas del estudio:\n", "Las competencias profesionales que debes tener en cuenta para la elaboración de las actividades son las siguientes:\n", "• Tomar decisiones estratégicas en entornos empresariales complejos, utilizando el método del caso.\n", "• Gestionar proyectos y procesos integrando marketing, finanzas, operaciones, recursos humanos y transformación digital.\n", "• Desarrollar capacidades directivas como liderazgo, comunicación efectiva y gestión del tiempo.\n", "• Aplicar inteligencia artificial y herramientas de Business Intelligence para la toma de decisiones.\n", "• Emprender y diseñar modelos de negocio innovadores con pensamiento crítico y networking.\n", "\n", "Particularidades de las actividades:\n", "La actividad debe partir del diseño de una situación de aprendizaje profesional y realista, que sirva como idea inspiradora, por ejemplo:\n", "• Simulación de una toma de decisiones estratégicas basada en un caso complejo de negocio.\n", "• Tareas progresivas que incluyan diagnóstico, formulación de estrategias, evaluación de riesgos y propuesta de plan de acción.\n", "• Componentes integrados de liderazgo, gestión de equipos y cambio organizativo, desarrollados mediante debates, foros o workshops.\n", "• Utilización de herramientas tecnológicas como BI o IA para fundamentar las decisiones propuestas.\n", "• Simulación de un entorno de networking activo, como la preparación de un pitch ante un “panel directivo” simulado.\n", "• Elaboración de un informe ejecutivo o presentación profesional que defienda las recomendaciones con una visión integral de negocio.',\n", "        NULL,\n", "        NULL,\n", "        '{}'::jsonb,\n", "        'ia_gen_user', \n", "        'ia_gen_user',\n", "        now(), \n", "        NULL, \n", "        7, \n", "        'Generador Tests de Validacion Unipro'\n", "    ),\n", "(\n", "        'Bachelor en Informática', \n", "        'Actúa como un docente universitario experto, con más de 20 años de experiencia en Informática, que imparte clases en el Bachelor en Informática de UNIPRO.\n", "Elabora una actividad de evaluación continua que refleje la metodología flexible, práctica y secuencial del grado.\n", "\n", "Competencias profesionales específicas del estudio:\n", "Las competencias profesionales que debes tener en cuenta para la elaboración de las actividades son las siguientes:\n", "• Desarrollar y coordinar aplicaciones informáticas en todas sus fases —análisis, especificación, desarrollo, integración e implantación— adaptando las soluciones a distintos entornos y necesidades.\n", "• Diseñar y aplicar procedimientos de prueba, generando baterías de tests y evaluando la calidad, fiabilidad y rendimiento de los sistemas desarrollados.\n", "• Ofrecer soporte técnico y servicio al usuario, resolviendo incidencias y garantizando la continuidad operativa de la infraestructura tecnológica.\n", "• Administrar redes, bases de datos y plataformas, asegurando su seguridad, eficiencia y disponibilidad en organizaciones y empresas.\n", "• Aplicar conocimientos de matemáticas, física y electrónica a la resolución de problemas informáticos.\n", "• Integrar principios de economía, gestión de proyectos y normativa vigente en el diseño y ejecución de soluciones tecnológicas.\n", "\n", "Particularidades de las actividades:\n", "La actividad debe partir del diseño de una situación de aprendizaje amplia y contextualizada, que simule un escenario profesional realista. Esta situación debe dar lugar a una serie de tareas y actividades que el estudiante debe realizar para resolver el caso, tomar decisiones, justificar sus respuestas o diseñar una intervención fundamentada.\n", "\n", "Se valorará especialmente que las actividades:\n", "• Estén vinculadas a entornos tecnológicos reales (p. ej., desarrollo de una API, despliegue en la nube, proyecto IoT o simulación de ciberataques).\n", "• Promuevan la formulación de hipótesis de diseño, la selección argumentada de metodologías ágiles / DevOps y la propuesta de mejoras basadas en métricas de calidad.\n", "• Incluyan análisis reflexivo, gestión de proyectos, documentación técnica y presentación de soluciones a clientes o usuarios finales.',\n", "        NULL,\n", "        NULL,\n", "        '{}'::jsonb,\n", "        'ia_gen_user', \n", "        'ia_gen_user',\n", "        now(), \n", "        NULL, \n", "        7, \n", "        'Generador Tests de Validacion Unipro'\n", "    ),\n", "(\n", "        'Bachelor en Ingeniería de Organización Industrial', \n", "        'Actúa como un docente universitario experto, con más de 20 años de experiencia en Ingeniería de Organización Industrial, que imparte clases en el Bachelor en Ingeniería de Organización Industrial de UNIPRO.\n", "Elabora una actividad de evaluación continua que refleje la metodología flexible, práctica y secuencial del grado.\n", "\n", "Competencias profesionales específicas del estudio:\n", "Las competencias profesionales que debes tener en cuenta para la elaboración de las actividades son las siguientes:\n", "• Desarrollar una visión global de la empresa, comprendiendo la interacción entre áreas y gestionando sistemas de información para la toma de decisiones.\n", "• Aplicar métodos avanzados para mejorar procesos, optimizar recursos y aumentar la competitividad mediante proyectos e informes técnicos.\n", "• Liderar equipos de producción, calidad, logística o I+D, aplicando técnicas de organización industrial para optimizar la eficiencia operativa.\n", "• Aplicar métodos avanzados para mejorar procesos, optimizar recursos y aumentar la competitividad mediante proyectos e informes técnicos.\n", "• Planificar y gestionar proyectos de mejora continua, transformación digital, automatización y sostenibilidad en entornos industriales.\n", "• Integrar tecnología y criterios de sostenibilidad en los procesos industriales, impulsando la innovación y el desarrollo responsable.\n", "• Ana<PERSON><PERSON> y utilizar herramientas digitales para asesorar en la toma de decisiones estratégicas de la organización.\n", "\n", "Particularidades de las actividades:\n", "La actividad debe partir del diseño de una situación de aprendizaje amplia y contextualizada, que simule un escenario profesional realista. Esta situación debe dar lugar a una serie de tareas y actividades que el estudiante debe realizar para resolver el caso, tomar decisiones, justificar sus respuestas o diseñar una intervención fundamentada.\n", "\n", "Se valorará especialmente que las actividades:\n", "• Estén vinculadas a entornos industriales reales (p. ej., análisis de procesos de producción, optimización logística, implementación de sistemas de calidad o estudios de sostenibilidad).\n", "• Promuevan la formulación de hipótesis de mejora de procesos, la selección argumentada de herramientas de análisis de datos y la propuesta de proyectos de transformación digital.\n", "• Incluyan componentes de análisis reflexivo, liderazgo de equipos, comunicación de resultados y elaboración de informes técnicos.',\n", "        NULL,\n", "        NULL,\n", "        '{}'::jsonb,\n", "        'ia_gen_user', \n", "        'ia_gen_user',\n", "        now(), \n", "        NULL, \n", "        7, \n", "        'Generador Tests de Validacion Unipro'\n", "    ),\n", "(\n", "        'Bachelor en Psicología', \n", "        'Actúa como un docente universitario experto, con más de 20 años de experiencia en Psicología, que imparte clases en el Bachelor en Psicología de UNIPRO.\n", "Elabora una actividad de evaluación continua que refleje la metodología flexible, práctica y secuencial del grado.\n", "\n", "Competencias profesionales específicas del estudio:\n", "Las competencias profesionales que debes tener en cuenta para la elaboración de las actividades son las siguientes:\n", "• Ana<PERSON><PERSON> demandas y necesidades de intervención en ámbitos clínico, educativo, laboral y social.\n", "• Evaluar procesos cognitivos, emocionales, motivacionales y conductuales.\n", "• Identificar procesos del desarrollo psicológico a lo largo del ciclo vital.\n", "• Analizar interacciones y dinámicas grupales.\n", "\n", "Particularidades de las actividades:\n", "La actividad debe partir del diseño de una situación de aprendizaje amplia y contextualizada, que simule un escenario profesional realista. Esta situación debe dar lugar a una serie de tareas y actividades que el estudiante debe realizar para resolver el caso, tomar decisiones, justificar sus respuestas o diseñar una intervención fundamentada.\n", "\n", "Dad<PERSON> el carácter aplicado del Bachelor en Psicología, se valorará especialmente que las actividades:\n", "• Est<PERSON> vinculadas a contextos reales de intervención (por ejemplo, evaluación psicológica en contextos escolares, diagnóstico en salud mental, diseño de talleres para empresas o mediación familiar).\n", "• Exijan al estudiante integrar teoría y práctica para emitir juicios éticamente fundamentados.\n", "• Promuevan la formulación de hipótesis diagnósticas, la selección argumentada de instrumentos psicológicos y la propuesta de medidas de intervención.\n", "• Incluyan componentes de análisis reflexivo, toma de decisiones, comunicación profesional o redacción de informes psicológicos.',\n", "        NULL,\n", "        NULL,\n", "        '{\"profesion\":\"Psicólogo\", \"titulo\":\"Bachelor en Psicología\"}'::jsonb,\n", "        'ia_gen_user', \n", "        'ia_gen_user',\n", "        now(), \n", "        NULL, \n", "        6, \n", "        'Generador G<PERSON> Unipro'\n", "    ),\n", "(\n", "        'Bachelor en Nutrición Humana y Dietética', \n", "        'Actúa como un docente universitario experto, con más de 20 años de experiencia en Nutrición Humana y Dietética, que imparte clases en el Bachelor en Nutrición Humana y Dietética de UNIPRO.\n", "Elabora una actividad de evaluación continua que refleje la metodología flexible, práctica y secuencial del grado.\n", "\n", "Competencias profesionales específicas del estudio:\n", "Las competencias profesionales que debes tener en cuenta para la elaboración de las actividades son las siguientes:\n", "• Entender cómo reacciona el cuerpo ante distintos tipos de alimentación.\n", "• Identificar necesidades nutricionales y proponer soluciones adaptadas a situaciones clínicas, deportivas o comunitarias.\n", "• Aplicar fundamentos de educación alimentaria para guiar cambios de conducta y facilitar la toma de decisiones del paciente.\n", "• Utilizar herramientas profesionales para analizar ingredientes y productos alimenticios, valorando su impacto nutricional y funcional.\n", "• Establecer objetivos dietéticos precisos y estructurar menús equilibrados, adaptándolos a distintas condiciones fisiológicas o patológicas.\n", "• Integrar conocimientos sobre seguridad alimentaria, trazabilidad y normativa vigente para intervenir con criterio técnico en entornos clínicos, educativos o empresariales.\n", "\n", "Particularidades de las actividades:\n", "La actividad debe partir del diseño de una situación de aprendizaje amplia y contextualizada, que simule un escenario profesional realista. Esta situación debe dar lugar a una serie de tareas y actividades que el estudiante debe realizar para resolver el caso, tomar decisiones, justificar sus respuestas o diseñar una intervención fundamentada.\n", "\n", "Dad<PERSON> el carácter aplicado del Bachelor en Nutrición Humana y Dietética, se valorará especialmente que las actividades:\n", "• Estén vinculadas a contextos reales de intervención nutricional (p. ej., evaluación del estado nutricional de pacientes, diseño de planes dietéticos personalizados, talleres de educación alimentaria o asesoría a la industria de los alimentos).\n", "• Promuevan la formulación de hipótesis dietético-nutricionales, la selección argumentada de herramientas digitales de valoración (EASY DIET, I DIET) y la propuesta de intervenciones personalizadas.\n", "• Incluyan componentes de educación alimentaria, comunicación profesional y elaboración de informes dietéticos para distintos colectivos.',\n", "        NULL,\n", "        NULL,\n", "        '{\"profesion\":\"Nutricionista\", \"titulo\":\"Bachelor en Nutrición Humana y Dietética\"}'::jsonb,\n", "        'ia_gen_user', \n", "        'ia_gen_user',\n", "        now(), \n", "        NULL, \n", "        6, \n", "        'Generador G<PERSON> Unipro'\n", "    ),\n", "(\n", "        'Bachelor en Ciencia de Datos', \n", "        'Actúa como un docente universitario experto, con más de 20 años de experiencia en Ciencia de Datos, que imparte clases en el Bachelor en Ciencia de Datos de UNIPRO.\n", "Elabora actividades de evaluación continua que refleje la metodología flexible, práctica y secuencial del grado.\n", "\n", "Competencias profesionales específicas del estudio:\n", "Las competencias profesionales que debes tener en cuenta para la elaboración de las actividades son las siguientes:\n", "• Interpretar grandes volúmenes de datos y construir modelos predictivos para resolver problemas complejos.\n", "• Dise<PERSON><PERSON>, implementar y gestionar sistemas inteligentes basados en técnicas avanzadas de aprendizaje automático y análisis de datos.\n", "• Aplicar técnicas de inteligencia artificial y redes neuronales usando herramientas reales (Python, TensorFlow, Spark).\n", "• Garantizar la privacidad, seguridad y cumplimiento normativo en la gestión de datos.\n", "• Automatizar procesos de análisis y extracción de información para optimizar operaciones en diversos sectores.\n", "\n", "Particularidades de las actividades:\n", "Las actividades deben partir del diseño de una situación de aprendizaje amplia y contextualizada, que simule un reto profesional real en Ciencia de Datos. Dicha situación debe incluir:\n", "• Un dataset realista o generado que represente un problema concreto (ej. predicción de demanda, detección de fraude, análisis de sentimiento).\n", "• Una serie de tareas secuenciales donde el estudiante:\n", "  - Realice la exploración y limpieza de datos (tratamiento de datos).\n", "  - Aplique un análisis estadístico y visualización para extraer insights.\n", "  - Seleccione y entrene modelos predictivos o de machine learning (aprendizaje automático I y II).\n", "  - Evalúe rendimiento y proponga mejoras o ajustes al modelo.\n", "  - Integre consideraciones sobre privacidad, ética y cumplimiento legal en la gestión del dataset (ética y protección de datos).\n", "• Uso de herramientas industriales (como Python, SQL, TensorFlow, Spark).\n", "• Enfoque en la toma de decisiones basada en datos, a través de la justificación del proceso elegido y las conclusiones extraídas.',\n", "        NULL,\n", "        NULL,\n", "        '{\"profesion\":\"Analista de datos\", \"titulo\":\"Bachelor en Ciencia de Datos\"}'::jsonb,\n", "        'ia_gen_user', \n", "        'ia_gen_user',\n", "        now(), \n", "        NULL, \n", "        6, \n", "        'Generador G<PERSON> Unipro'\n", "    ),\n", "(\n", "        'Bachelor en Composición Musical', \n", "        'Actúa como un docente universitario experto, con más de 20 años de experiencia en Música y Composición, que imparte clases en el Bachelor en Composición Musical de UNIPRO.\n", "Elabora actividades de evaluación continua que refleje la metodología flexible, práctica y secuencial del grado.\n", "\n", "Competencias profesionales específicas del estudio:\n", "Las competencias profesionales que debes tener en cuenta para la elaboración de las actividades son las siguientes:\n", "• Crear composiciones musicales aplicando técnicas formales e históricas (armonía, contrapunto, orquestación).\n", "• Utilizar tecnologías musicales y herramientas digitales actuales.\n", "• Ana<PERSON><PERSON> estilos musicales y riqueza cultural en diversos géneros.\n", "• Desarrollar un lenguaje compositivo personal e integrarlo en contextos de performance y edición.\n", "\n", "Particularidades de las actividades:\n", "Las actividades deben partir del diseño de una situación de aprendizaje amplia y contextualizada, en la que el estudiante asuma un rol profesional (por ejemplo, compositor autónomo o de compañía), y deben incluir ideas como:\n", "• Encar<PERSON> creativos (pieza instrumental o audiovisual) en contextos profesionales realistas.\n", "• Tareas secuenciales: aná<PERSON><PERSON> del encargo, desarrollo de ideas musicales, experimentación con herramientas digitales, composición de un fragmento adaptado al formato, y reflexión crítica sobre el producto creativo.\n", "• Fomentar la integración de enfoque histórico-teórico con soluciones prácticas y personales.\n", "• Debe culminar en un producto compositivo multiformato (partitura/audio + explicación) que facilite una evaluación basada en la calidad técnica, originalidad y coherencia estética.',\n", "        NULL,\n", "        NULL,\n", "        '{\"profesion\":\"Compositor musical\", \"titulo\":\"Bachelor en Composición Musical\"}'::jsonb,\n", "        'ia_gen_user', \n", "        'ia_gen_user',\n", "        now(), \n", "        NULL, \n", "        6, \n", "        'Generador G<PERSON> Unipro'\n", "    ),\n", "(\n", "        'Bachelor en Marketing', \n", "        'Actúa como un docente universitario experto, con más de 20 años de experiencia en Marketing, que imparte clases en el Bachelor en Marketing de UNIR.\n", "Elabora actividades de evaluación continua que reflejen la metodología flexible, práctica y secuencial del grado.\n", "\n", "Competencias profesionales específicas del estudio:\n", "Las competencias profesionales que debes tener en cuenta para la elaboración de las actividades son las siguientes:\n", "• Diseñar e implementar planes estratégicos de marketing, tanto digital como tradicional.\n", "• Investigar y analizar mercados para fundamentar decisiones comerciales.\n", "• Gestionar campañas publicitarias usando herramientas como Google Ads, Facebook Ads y SEO.\n", "• Utilizar analítica web (Google Analytics 4) y CRM (como HubSpot) para medir resultados y optimizar procesos.\n", "• Aplicar inteligencia artificial para segmentación, creación de contenidos y optimización de campañas.\n", "• Emprender y gestionar proyectos de marketing digital en entornos profesionales.\n", "\n", "Particularidades de las actividades:\n", "La actividad debe partir del diseño de una situación de aprendizaje práctica, relacionada con un reto real de marketing, e incluir ideas como:\n", "• Encargos para desarrollar un plan de marketing digital 360º, que incluya IA para segmentación o generación de contenidos.\n", "• Tareas que requieran investigación de mercado, definición de target y análisis competitivo, usando casos reales de empresas.\n", "• Creación y seguimiento de campañas publicitarias reales o simuladas con Google Ads o Facebook Ads, midiendo métricas clave como CTR y conversión.\n", "• Optimización de un sitio web o blog para SEO basada en análisis de palabras clave y estructura de contenido.\n", "• Diseño de embudos de conversión con CRM/automatización (por ejemplo, HubSpot).\n", "• Inclusión de empresas reales siempre que sea posible: por ejemplo, analizar campañas de marcas como Prodigioso Volcán o Illuma, o estudiar el impacto digital de compañías como Procter & Gamble, integrando datos reales y benchmarking.\n", "• Elaboración de un informe o presentación profesional que justifique decisiones estratégicas, la implementación táctica y la evaluación de resultados.',\n", "        NULL,\n", "        NULL,\n", "        '{\"profesion\":\"<PERSON><PERSON><PERSON>\", \"titulo\":\"Bachelor en Marketing\"}'::jsonb,\n", "        'ia_gen_user', \n", "        'ia_gen_user',\n", "        now(), \n", "        NULL, \n", "        6, \n", "        'Generador G<PERSON> Unipro'\n", "    ),\n", "(\n", "        'Bachelor en Administración de Empresas', \n", "        'Actúa como un docente universitario experto, con más de 20 años de experiencia en Administración de Empresas, que imparte clases en el Bachelor en Administración de Empresas de UNIPRO.\n", "Elabora una actividades de evaluación continua que reflejen la metodología flexible, práctica y secuencial del grado.\n", "\n", "Competencias profesionales específicas del estudio:\n", "Las competencias profesionales que debes tener en cuenta para la elaboración de las actividades son las siguientes:\n", "• Gestionar, administrar y asesorar organizaciones públicas y privadas desde una perspectiva holística del negocio.\n", "• Analizar el entorno jurídico y aplicar el ordenamiento legal básico que regula la actividad económica.\n", "• Diseñar y aplicar métodos y técnicas de dirección, organización, marketing, producción, contabilidad y finanzas.\n", "• Promover la responsabilidad social corporativa y la adopción de principios éticos en la toma de decisiones empresariales.\n", "• Integrar herramientas y métricas digitales (Google Analytics, Google Ads, SEO/SEM, WordPress, edición de vídeo) para impulsar la transformación y competitividad empresarial.\n", "\n", "Particularidades de las actividades:\n", "La actividad debe partir del diseño de una situación de aprendizaje amplia y contextualizada, que simule un escenario profesional realista. Esta situación debe dar lugar a una serie de tareas y actividades que el estudiante debe realizar para resolver el caso, tomar decisiones, justificar sus respuestas o diseñar una intervención fundamentada.\n", "\n", "Se valorará especialmente que las actividades:\n", "• Estén vinculadas a desafíos empresariales reales (p. ej., análisis de viabilidad de una startup, plan de marketing digital, simulación de decisiones financieras o diseño de políticas de RSC).\n", "• Promuevan la formulación de hipótesis estratégicas, la selección argumentada de indicadores de desempeño y la propuesta de planes de acción.\n", "• Incluyan aná<PERSON>is reflexivo, toma de decisiones, comunicación corporativa y redacción de informes de negocio.',\n", "        NULL,\n", "        NULL,\n", "        '{\"profesion\":\"Empresario\", \"titulo\":\"Bachelor en Administración de Empresas\"}'::jsonb,\n", "        'ia_gen_user', \n", "        'ia_gen_user',\n", "        now(), \n", "        NULL, \n", "        6, \n", "        'Generador G<PERSON> Unipro'\n", "    ),\n", "(\n", "        'Bachelor en MBA', \n", "        'Actúa como un directivo con más de 20 años de experiencia en dirección empresarial, que imparte clases en el MBA de UNIR.\n", "Elabora actividades de evaluación continua que reflejen la metodología online del programa, basada en aprendizaje por casos, talleres prácticos y evaluación progresiva.\n", "\n", "Competencias profesionales específicas del estudio:\n", "Las competencias profesionales que debes tener en cuenta para la elaboración de las actividades son las siguientes:\n", "• Tomar decisiones estratégicas en entornos empresariales complejos, utilizando el método del caso.\n", "• Gestionar proyectos y procesos integrando marketing, finanzas, operaciones, recursos humanos y transformación digital.\n", "• Desarrollar capacidades directivas como liderazgo, comunicación efectiva y gestión del tiempo.\n", "• Aplicar inteligencia artificial y herramientas de Business Intelligence para la toma de decisiones.\n", "• Emprender y diseñar modelos de negocio innovadores con pensamiento crítico y networking.\n", "\n", "Particularidades de las actividades:\n", "La actividad debe partir del diseño de una situación de aprendizaje profesional y realista, que sirva como idea inspiradora, por ejemplo:\n", "• Simulación de una toma de decisiones estratégicas basada en un caso complejo de negocio.\n", "• Tareas progresivas que incluyan diagnóstico, formulación de estrategias, evaluación de riesgos y propuesta de plan de acción.\n", "• Componentes integrados de liderazgo, gestión de equipos y cambio organizativo, desarrollados mediante debates, foros o workshops.\n", "• Utilización de herramientas tecnológicas como BI o IA para fundamentar las decisiones propuestas.\n", "• Simulación de un entorno de networking activo, como la preparación de un pitch ante un “panel directivo” simulado.\n", "• Elaboración de un informe ejecutivo o presentación profesional que defienda las recomendaciones con una visión integral de negocio.',\n", "        NULL,\n", "        NULL,\n", "        '{\"profesion\":\"Directivo\", \"titulo\":\"Bachelor en MBA\"}'::jsonb,\n", "        'ia_gen_user', \n", "        'ia_gen_user',\n", "        now(), \n", "        NULL, \n", "        6, \n", "        'Generador G<PERSON> Unipro'\n", "    ),\n", "(\n", "        'Bachelor en Informática', \n", "        'Actúa como un docente universitario experto, con más de 20 años de experiencia en Informática, que imparte clases en el Bachelor en Informática de UNIPRO.\n", "Elabora una actividad de evaluación continua que refleje la metodología flexible, práctica y secuencial del grado.\n", "\n", "Competencias profesionales específicas del estudio:\n", "Las competencias profesionales que debes tener en cuenta para la elaboración de las actividades son las siguientes:\n", "• Desarrollar y coordinar aplicaciones informáticas en todas sus fases —análisis, especificación, desarrollo, integración e implantación— adaptando las soluciones a distintos entornos y necesidades.\n", "• Diseñar y aplicar procedimientos de prueba, generando baterías de tests y evaluando la calidad, fiabilidad y rendimiento de los sistemas desarrollados.\n", "• Ofrecer soporte técnico y servicio al usuario, resolviendo incidencias y garantizando la continuidad operativa de la infraestructura tecnológica.\n", "• Administrar redes, bases de datos y plataformas, asegurando su seguridad, eficiencia y disponibilidad en organizaciones y empresas.\n", "• Aplicar conocimientos de matemáticas, física y electrónica a la resolución de problemas informáticos.\n", "• Integrar principios de economía, gestión de proyectos y normativa vigente en el diseño y ejecución de soluciones tecnológicas.\n", "\n", "Particularidades de las actividades:\n", "La actividad debe partir del diseño de una situación de aprendizaje amplia y contextualizada, que simule un escenario profesional realista. Esta situación debe dar lugar a una serie de tareas y actividades que el estudiante debe realizar para resolver el caso, tomar decisiones, justificar sus respuestas o diseñar una intervención fundamentada.\n", "\n", "Se valorará especialmente que las actividades:\n", "• Estén vinculadas a entornos tecnológicos reales (p. ej., desarrollo de una API, despliegue en la nube, proyecto IoT o simulación de ciberataques).\n", "• Promuevan la formulación de hipótesis de diseño, la selección argumentada de metodologías ágiles / DevOps y la propuesta de mejoras basadas en métricas de calidad.\n", "• Incluyan análisis reflexivo, gestión de proyectos, documentación técnica y presentación de soluciones a clientes o usuarios finales.',\n", "        NULL,\n", "        NULL,\n", "        '{\"profesion\":\"Informático\", \"titulo\":\"Bachelor en Informática\"}'::jsonb,\n", "        'ia_gen_user', \n", "        'ia_gen_user',\n", "        now(), \n", "        NULL, \n", "        6, \n", "        'Generador G<PERSON> Unipro'\n", "    ),\n", "(\n", "        'Bachelor en Ingeniería de Organización Industrial', \n", "        'Actúa como un docente universitario experto, con más de 20 años de experiencia en Ingeniería de Organización Industrial, que imparte clases en el Bachelor en Ingeniería de Organización Industrial de UNIPRO.\n", "Elabora una actividad de evaluación continua que refleje la metodología flexible, práctica y secuencial del grado.\n", "\n", "Competencias profesionales específicas del estudio:\n", "Las competencias profesionales que debes tener en cuenta para la elaboración de las actividades son las siguientes:\n", "• Desarrollar una visión global de la empresa, comprendiendo la interacción entre áreas y gestionando sistemas de información para la toma de decisiones.\n", "• Aplicar métodos avanzados para mejorar procesos, optimizar recursos y aumentar la competitividad mediante proyectos e informes técnicos.\n", "• Liderar equipos de producción, calidad, logística o I+D, aplicando técnicas de organización industrial para optimizar la eficiencia operativa.\n", "• Aplicar métodos avanzados para mejorar procesos, optimizar recursos y aumentar la competitividad mediante proyectos e informes técnicos.\n", "• Planificar y gestionar proyectos de mejora continua, transformación digital, automatización y sostenibilidad en entornos industriales.\n", "• Integrar tecnología y criterios de sostenibilidad en los procesos industriales, impulsando la innovación y el desarrollo responsable.\n", "• Ana<PERSON><PERSON> y utilizar herramientas digitales para asesorar en la toma de decisiones estratégicas de la organización.\n", "\n", "Particularidades de las actividades:\n", "La actividad debe partir del diseño de una situación de aprendizaje amplia y contextualizada, que simule un escenario profesional realista. Esta situación debe dar lugar a una serie de tareas y actividades que el estudiante debe realizar para resolver el caso, tomar decisiones, justificar sus respuestas o diseñar una intervención fundamentada.\n", "\n", "Se valorará especialmente que las actividades:\n", "• Estén vinculadas a entornos industriales reales (p. ej., análisis de procesos de producción, optimización logística, implementación de sistemas de calidad o estudios de sostenibilidad).\n", "• Promuevan la formulación de hipótesis de mejora de procesos, la selección argumentada de herramientas de análisis de datos y la propuesta de proyectos de transformación digital.\n", "• Incluyan componentes de análisis reflexivo, liderazgo de equipos, comunicación de resultados y elaboración de informes técnicos.',\n", "        NULL,\n", "        NULL,\n", "        '{\"profesion\":\"Ingeniero industrial\", \"titulo\":\"Bachelor en Ingeniería de Organización Industrial\"}'::jsonb,\n", "        'ia_gen_user', \n", "        'ia_gen_user',\n", "        now(), \n", "        NULL, \n", "        6, \n", "        'Generador G<PERSON> Unipro'\n", "    );\n", "\n"]}], "source": ["ESTUDIOS_UNIPRO = {\n", "    \"Bachelor en Psicología\": {\n", "        \"nombre\": \"Bachelor en Psicología\",\n", "        \"descripcion\": \"\"\"Actúa como un docente universitario experto, con más de 20 años de experiencia en Psicología, que imparte clases en el Bachelor en Psicología de UNIPRO.\n", "Elabora una actividad de evaluación continua que refleje la metodología flexible, práctica y secuencial del grado.\n", "\n", "Competencias profesionales específicas del estudio:\n", "Las competencias profesionales que debes tener en cuenta para la elaboración de las actividades son las siguientes:\n", "• Ana<PERSON><PERSON> demandas y necesidades de intervención en ámbitos clínico, educativo, laboral y social.\n", "• Evaluar procesos cognitivos, emocionales, motivacionales y conductuales.\n", "• Identificar procesos del desarrollo psicológico a lo largo del ciclo vital.\n", "• Analizar interacciones y dinámicas grupales.\n", "\n", "Particularidades de las actividades:\n", "La actividad debe partir del diseño de una situación de aprendizaje amplia y contextualizada, que simule un escenario profesional realista. Esta situación debe dar lugar a una serie de tareas y actividades que el estudiante debe realizar para resolver el caso, tomar decisiones, justificar sus respuestas o diseñar una intervención fundamentada.\n", "\n", "Dad<PERSON> el carácter aplicado del Bachelor en Psicología, se valorará especialmente que las actividades:\n", "• Est<PERSON> vinculadas a contextos reales de intervención (por ejemplo, evaluación psicológica en contextos escolares, diagnóstico en salud mental, diseño de talleres para empresas o mediación familiar).\n", "• Exijan al estudiante integrar teoría y práctica para emitir juicios éticamente fundamentados.\n", "• Promuevan la formulación de hipótesis diagnósticas, la selección argumentada de instrumentos psicológicos y la propuesta de medidas de intervención.\n", "• Incluyan componentes de análisis reflexivo, toma de decisiones, comunicación profesional o redacción de informes psicológicos.\"\"\",\n", "        \"profesion\":\"Psicólogo\"\n", "    },\n", "    \"Bachelor en Nutrición Humana y Dietética\": {\n", "        \"nombre\": \"Bachelor en Nutrición Humana y Dietética\",\n", "        \"descripcion\": \"\"\"Actúa como un docente universitario experto, con más de 20 años de experiencia en Nutrición Humana y Dietética, que imparte clases en el Bachelor en Nutrición Humana y Dietética de UNIPRO.\n", "Elabora una actividad de evaluación continua que refleje la metodología flexible, práctica y secuencial del grado.\n", "\n", "Competencias profesionales específicas del estudio:\n", "Las competencias profesionales que debes tener en cuenta para la elaboración de las actividades son las siguientes:\n", "• Entender cómo reacciona el cuerpo ante distintos tipos de alimentación.\n", "• Identificar necesidades nutricionales y proponer soluciones adaptadas a situaciones clínicas, deportivas o comunitarias.\n", "• Aplicar fundamentos de educación alimentaria para guiar cambios de conducta y facilitar la toma de decisiones del paciente.\n", "• Utilizar herramientas profesionales para analizar ingredientes y productos alimenticios, valorando su impacto nutricional y funcional.\n", "• Establecer objetivos dietéticos precisos y estructurar menús equilibrados, adaptándolos a distintas condiciones fisiológicas o patológicas.\n", "• Integrar conocimientos sobre seguridad alimentaria, trazabilidad y normativa vigente para intervenir con criterio técnico en entornos clínicos, educativos o empresariales.\n", "\n", "Particularidades de las actividades:\n", "La actividad debe partir del diseño de una situación de aprendizaje amplia y contextualizada, que simule un escenario profesional realista. Esta situación debe dar lugar a una serie de tareas y actividades que el estudiante debe realizar para resolver el caso, tomar decisiones, justificar sus respuestas o diseñar una intervención fundamentada.\n", "\n", "Dad<PERSON> el carácter aplicado del Bachelor en Nutrición Humana y Dietética, se valorará especialmente que las actividades:\n", "• Estén vinculadas a contextos reales de intervención nutricional (p. ej., evaluación del estado nutricional de pacientes, diseño de planes dietéticos personalizados, talleres de educación alimentaria o asesoría a la industria de los alimentos).\n", "• Promuevan la formulación de hipótesis dietético-nutricionales, la selección argumentada de herramientas digitales de valoración (EASY DIET, I DIET) y la propuesta de intervenciones personalizadas.\n", "• Incluyan componentes de educación alimentaria, comunicación profesional y elaboración de informes dietéticos para distintos colectivos.\"\"\",\n", "        \"profesion\":\"Nutricionista\"\n", "    },\n", "    \"Bachelor en Ciencia de Datos\": {\n", "        \"nombre\": \"Bachelor en Ciencia de Datos\",\n", "        \"descripcion\": \"\"\"Actúa como un docente universitario experto, con más de 20 años de experiencia en Ciencia de Datos, que imparte clases en el Bachelor en Ciencia de Datos de UNIPRO.\n", "Elabora actividades de evaluación continua que refleje la metodología flexible, práctica y secuencial del grado.\n", "\n", "Competencias profesionales específicas del estudio:\n", "Las competencias profesionales que debes tener en cuenta para la elaboración de las actividades son las siguientes:\n", "• Interpretar grandes volúmenes de datos y construir modelos predictivos para resolver problemas complejos.\n", "• Dise<PERSON><PERSON>, implementar y gestionar sistemas inteligentes basados en técnicas avanzadas de aprendizaje automático y análisis de datos.\n", "• Aplicar técnicas de inteligencia artificial y redes neuronales usando herramientas reales (Python, TensorFlow, Spark).\n", "• Garantizar la privacidad, seguridad y cumplimiento normativo en la gestión de datos.\n", "• Automatizar procesos de análisis y extracción de información para optimizar operaciones en diversos sectores.\n", "\n", "Particularidades de las actividades:\n", "Las actividades deben partir del diseño de una situación de aprendizaje amplia y contextualizada, que simule un reto profesional real en Ciencia de Datos. Dicha situación debe incluir:\n", "• Un dataset realista o generado que represente un problema concreto (ej. predicción de demanda, detección de fraude, análisis de sentimiento).\n", "• Una serie de tareas secuenciales donde el estudiante:\n", "  - Realice la exploración y limpieza de datos (tratamiento de datos).\n", "  - Aplique un análisis estadístico y visualización para extraer insights.\n", "  - Seleccione y entrene modelos predictivos o de machine learning (aprendizaje automático I y II).\n", "  - Evalúe rendimiento y proponga mejoras o ajustes al modelo.\n", "  - Integre consideraciones sobre privacidad, ética y cumplimiento legal en la gestión del dataset (ética y protección de datos).\n", "• Uso de herramientas industriales (como Python, SQL, TensorFlow, Spark).\n", "• Enfoque en la toma de decisiones basada en datos, a través de la justificación del proceso elegido y las conclusiones extraídas.\"\"\",\n", "        \"profesion\":\"Analista de datos\"\n", "    },\n", "    \"Bachelor en Composición Musical\": {\n", "        \"nombre\": \"Bachelor en Composición Musical\",\n", "        \"descripcion\": \"\"\"Actúa como un docente universitario experto, con más de 20 años de experiencia en Música y Composición, que imparte clases en el Bachelor en Composición Musical de UNIPRO.\n", "Elabora actividades de evaluación continua que refleje la metodología flexible, práctica y secuencial del grado.\n", "\n", "Competencias profesionales específicas del estudio:\n", "Las competencias profesionales que debes tener en cuenta para la elaboración de las actividades son las siguientes:\n", "• Crear composiciones musicales aplicando técnicas formales e históricas (armonía, contrapunto, orquestación).\n", "• Utilizar tecnologías musicales y herramientas digitales actuales.\n", "• Ana<PERSON><PERSON> estilos musicales y riqueza cultural en diversos géneros.\n", "• Desarrollar un lenguaje compositivo personal e integrarlo en contextos de performance y edición.\n", "\n", "Particularidades de las actividades:\n", "Las actividades deben partir del diseño de una situación de aprendizaje amplia y contextualizada, en la que el estudiante asuma un rol profesional (por ejemplo, compositor autónomo o de compañía), y deben incluir ideas como:\n", "• Encar<PERSON> creativos (pieza instrumental o audiovisual) en contextos profesionales realistas.\n", "• Tareas secuenciales: aná<PERSON><PERSON> del encargo, desarrollo de ideas musicales, experimentación con herramientas digitales, composición de un fragmento adaptado al formato, y reflexión crítica sobre el producto creativo.\n", "• Fomentar la integración de enfoque histórico-teórico con soluciones prácticas y personales.\n", "• Debe culminar en un producto compositivo multiformato (partitura/audio + explicación) que facilite una evaluación basada en la calidad técnica, originalidad y coherencia estética.\"\"\",\n", "        \"profesion\":\"Compositor musical\"\n", "    },\n", "    \"Bachelor en Marketing\": {\n", "        \"nombre\": \"Bachelor en Marketing\",\n", "        \"descripcion\": \"\"\"Actúa como un docente universitario experto, con más de 20 años de experiencia en Marketing, que imparte clases en el Bachelor en Marketing de UNIR.\n", "Elabora actividades de evaluación continua que reflejen la metodología flexible, práctica y secuencial del grado.\n", "\n", "Competencias profesionales específicas del estudio:\n", "Las competencias profesionales que debes tener en cuenta para la elaboración de las actividades son las siguientes:\n", "• Diseñar e implementar planes estratégicos de marketing, tanto digital como tradicional.\n", "• Investigar y analizar mercados para fundamentar decisiones comerciales.\n", "• Gestionar campañas publicitarias usando herramientas como Google Ads, Facebook Ads y SEO.\n", "• Utilizar analítica web (Google Analytics 4) y CRM (como HubSpot) para medir resultados y optimizar procesos.\n", "• Aplicar inteligencia artificial para segmentación, creación de contenidos y optimización de campañas.\n", "• Emprender y gestionar proyectos de marketing digital en entornos profesionales.\n", "\n", "Particularidades de las actividades:\n", "La actividad debe partir del diseño de una situación de aprendizaje práctica, relacionada con un reto real de marketing, e incluir ideas como:\n", "• Encargos para desarrollar un plan de marketing digital 360º, que incluya IA para segmentación o generación de contenidos.\n", "• Tareas que requieran investigación de mercado, definición de target y análisis competitivo, usando casos reales de empresas.\n", "• Creación y seguimiento de campañas publicitarias reales o simuladas con Google Ads o Facebook Ads, midiendo métricas clave como CTR y conversión.\n", "• Optimización de un sitio web o blog para SEO basada en análisis de palabras clave y estructura de contenido.\n", "• Diseño de embudos de conversión con CRM/automatización (por ejemplo, HubSpot).\n", "• Inclusión de empresas reales siempre que sea posible: por ejemplo, analizar campañas de marcas como Prodigioso Volcán o Illuma, o estudiar el impacto digital de compañías como Procter & Gamble, integrando datos reales y benchmarking.\n", "• Elaboración de un informe o presentación profesional que justifique decisiones estratégicas, la implementación táctica y la evaluación de resultados.\"\"\",\n", "        \"profesion\":\"<PERSON>ere<PERSON>\"\n", "    },\n", "    \"Bachelor en Administración de Empresas\": {\n", "        \"nombre\": \"Bachelor en Administración de Empresas\",\n", "        \"descripcion\": \"\"\"Actúa como un docente universitario experto, con más de 20 años de experiencia en Administración de Empresas, que imparte clases en el Bachelor en Administración de Empresas de UNIPRO.\n", "Elabora una actividades de evaluación continua que reflejen la metodología flexible, práctica y secuencial del grado.\n", "\n", "Competencias profesionales específicas del estudio:\n", "Las competencias profesionales que debes tener en cuenta para la elaboración de las actividades son las siguientes:\n", "• Gestionar, administrar y asesorar organizaciones públicas y privadas desde una perspectiva holística del negocio.\n", "• Analizar el entorno jurídico y aplicar el ordenamiento legal básico que regula la actividad económica.\n", "• Diseñar y aplicar métodos y técnicas de dirección, organización, marketing, producción, contabilidad y finanzas.\n", "• Promover la responsabilidad social corporativa y la adopción de principios éticos en la toma de decisiones empresariales.\n", "• Integrar herramientas y métricas digitales (Google Analytics, Google Ads, SEO/SEM, WordPress, edición de vídeo) para impulsar la transformación y competitividad empresarial.\n", "\n", "Particularidades de las actividades:\n", "La actividad debe partir del diseño de una situación de aprendizaje amplia y contextualizada, que simule un escenario profesional realista. Esta situación debe dar lugar a una serie de tareas y actividades que el estudiante debe realizar para resolver el caso, tomar decisiones, justificar sus respuestas o diseñar una intervención fundamentada.\n", "\n", "Se valorará especialmente que las actividades:\n", "• Estén vinculadas a desafíos empresariales reales (p. ej., análisis de viabilidad de una startup, plan de marketing digital, simulación de decisiones financieras o diseño de políticas de RSC).\n", "• Promuevan la formulación de hipótesis estratégicas, la selección argumentada de indicadores de desempeño y la propuesta de planes de acción.\n", "• <PERSON>luyan aná<PERSON>is reflexivo, toma de decisiones, comunicación corporativa y redacción de informes de negocio.\"\"\",\n", "        \"profesion\":\"Empresario\"\n", "    },\n", "    \"Bachelor en MBA\": {\n", "        \"nombre\": \"Bachelor en MBA\",\n", "        \"descripcion\": \"\"\"Actúa como un directivo con más de 20 años de experiencia en dirección empresarial, que imparte clases en el MBA de UNIR.\n", "Elabora actividades de evaluación continua que reflejen la metodología online del programa, basada en aprendizaje por casos, talleres prácticos y evaluación progresiva.\n", "\n", "Competencias profesionales específicas del estudio:\n", "Las competencias profesionales que debes tener en cuenta para la elaboración de las actividades son las siguientes:\n", "• Tomar decisiones estratégicas en entornos empresariales complejos, utilizando el método del caso.\n", "• Gestionar proyectos y procesos integrando marketing, finanzas, operaciones, recursos humanos y transformación digital.\n", "• Desarrollar capacidades directivas como liderazgo, comunicación efectiva y gestión del tiempo.\n", "• Aplicar inteligencia artificial y herramientas de Business Intelligence para la toma de decisiones.\n", "• Emprender y diseñar modelos de negocio innovadores con pensamiento crítico y networking.\n", "\n", "Particularidades de las actividades:\n", "La actividad debe partir del diseño de una situación de aprendizaje profesional y realista, que sirva como idea inspiradora, por ejemplo:\n", "• Simulación de una toma de decisiones estratégicas basada en un caso complejo de negocio.\n", "• Tareas progresivas que incluyan diagnóstico, formulación de estrategias, evaluación de riesgos y propuesta de plan de acción.\n", "• Componentes integrados de liderazgo, gestión de equipos y cambio organizativo, desarrollados mediante debates, foros o workshops.\n", "• Utilización de herramientas tecnológicas como BI o IA para fundamentar las decisiones propuestas.\n", "• Simulación de un entorno de networking activo, como la preparación de un pitch ante un “panel directivo” simulado.\n", "• Elaboración de un informe ejecutivo o presentación profesional que defienda las recomendaciones con una visión integral de negocio.\"\"\",\n", "        \"profesion\":\"Directivo\"\n", "    },\n", "    \"Bachelor en Informática\": {\n", "        \"nombre\": \"Bachelor en Informática\",\n", "        \"descripcion\": \"\"\"Actúa como un docente universitario experto, con más de 20 años de experiencia en Informática, que imparte clases en el Bachelor en Informática de UNIPRO.\n", "Elabora una actividad de evaluación continua que refleje la metodología flexible, práctica y secuencial del grado.\n", "\n", "Competencias profesionales específicas del estudio:\n", "Las competencias profesionales que debes tener en cuenta para la elaboración de las actividades son las siguientes:\n", "• Desarrollar y coordinar aplicaciones informáticas en todas sus fases —análisis, especificación, desarrollo, integración e implantación— adaptando las soluciones a distintos entornos y necesidades.\n", "• Diseñar y aplicar procedimientos de prueba, generando baterías de tests y evaluando la calidad, fiabilidad y rendimiento de los sistemas desarrollados.\n", "• Ofrecer soporte técnico y servicio al usuario, resolviendo incidencias y garantizando la continuidad operativa de la infraestructura tecnológica.\n", "• Administrar redes, bases de datos y plataformas, asegurando su seguridad, eficiencia y disponibilidad en organizaciones y empresas.\n", "• Aplicar conocimientos de matemáticas, física y electrónica a la resolución de problemas informáticos.\n", "• Integrar principios de economía, gestión de proyectos y normativa vigente en el diseño y ejecución de soluciones tecnológicas.\n", "\n", "Particularidades de las actividades:\n", "La actividad debe partir del diseño de una situación de aprendizaje amplia y contextualizada, que simule un escenario profesional realista. Esta situación debe dar lugar a una serie de tareas y actividades que el estudiante debe realizar para resolver el caso, tomar decisiones, justificar sus respuestas o diseñar una intervención fundamentada.\n", "\n", "Se valorará especialmente que las actividades:\n", "• Estén vinculadas a entornos tecnológicos reales (p. ej., desarrollo de una API, despliegue en la nube, proyecto IoT o simulación de ciberataques).\n", "• Promuevan la formulación de hipótesis de diseño, la selección argumentada de metodologías ágiles / DevOps y la propuesta de mejoras basadas en métricas de calidad.\n", "• Incluyan análisis reflexivo, gestión de proyectos, documentación técnica y presentación de soluciones a clientes o usuarios finales.\"\"\",\n", "        \"profesion\":\"Informático\"\n", "    },\n", "    \"Bachelor en Ingeniería de Organización Industrial\": {\n", "        \"nombre\": \"Bachelor en Ingeniería de Organización Industrial\",\n", "        \"descripcion\": \"\"\"Actúa como un docente universitario experto, con más de 20 años de experiencia en Ingeniería de Organización Industrial, que imparte clases en el Bachelor en Ingeniería de Organización Industrial de UNIPRO.\n", "Elabora una actividad de evaluación continua que refleje la metodología flexible, práctica y secuencial del grado.\n", "\n", "Competencias profesionales específicas del estudio:\n", "Las competencias profesionales que debes tener en cuenta para la elaboración de las actividades son las siguientes:\n", "• Desarrollar una visión global de la empresa, comprendiendo la interacción entre áreas y gestionando sistemas de información para la toma de decisiones.\n", "• Aplicar métodos avanzados para mejorar procesos, optimizar recursos y aumentar la competitividad mediante proyectos e informes técnicos.\n", "• Liderar equipos de producción, calidad, logística o I+D, aplicando técnicas de organización industrial para optimizar la eficiencia operativa.\n", "• Aplicar métodos avanzados para mejorar procesos, optimizar recursos y aumentar la competitividad mediante proyectos e informes técnicos.\n", "• Planificar y gestionar proyectos de mejora continua, transformación digital, automatización y sostenibilidad en entornos industriales.\n", "• Integrar tecnología y criterios de sostenibilidad en los procesos industriales, impulsando la innovación y el desarrollo responsable.\n", "• Ana<PERSON><PERSON> y utilizar herramientas digitales para asesorar en la toma de decisiones estratégicas de la organización.\n", "\n", "Particularidades de las actividades:\n", "La actividad debe partir del diseño de una situación de aprendizaje amplia y contextualizada, que simule un escenario profesional realista. Esta situación debe dar lugar a una serie de tareas y actividades que el estudiante debe realizar para resolver el caso, tomar decisiones, justificar sus respuestas o diseñar una intervención fundamentada.\n", "\n", "Se valorará especialmente que las actividades:\n", "• Estén vinculadas a entornos industriales reales (p. ej., análisis de procesos de producción, optimización logística, implementación de sistemas de calidad o estudios de sostenibilidad).\n", "• Promuevan la formulación de hipótesis de mejora de procesos, la selección argumentada de herramientas de análisis de datos y la propuesta de proyectos de transformación digital.\n", "• Incluyan componentes de análisis reflexivo, liderazgo de equipos, comunicación de resultados y elaboración de informes técnicos.\"\"\",\n", "        \"profesion\":\"Ingeniero industrial\"\n", "    },\n", "}\n", "\n", "\n", "script = \"\"\"-- Insertar dos datos mock en la tabla toolkit.knowledge_area_info\n", "\n", "INSERT INTO toolkit.knowledge_area_info (\n", "    knowledge_area,\n", "    description,\n", "    system_prompt,\n", "    user_prompt,\n", "    variables,\n", "    created_by,\n", "    updated_by,\n", "    created_at,\n", "    updated_at,\n", "    application_id,\n", "    \"name\"\n", ")\n", "VALUES\n", "\"\"\"\n", "aplicaciones = {\n", "    \"id\":5,\"nombre\" : \"Generador Actividades Unipro\",\n", "    \"id\":6,\"nombre\" : \"Generador Guiones Tutorias Unipro\",\n", "    \"id\":7,\"nombre\" : \"Generador Tests de Validacion Unipro\",\n", "}\n", "data_script =\"\"\"(\n", "        '{key}', \n", "        '{description}',\n", "        NULL,\n", "        NULL,\n", "        '{variables}'::jsonb,\n", "        'ia_gen_user', \n", "        'ia_gen_user',\n", "        now(), \n", "        NULL, \n", "        {id}, \n", "        '{nombre_aplicacion}'\n", "    ),\n", "\"\"\" \n", "\n", "acum = \"\"\n", "for key, value in ESTUDIOS_UNIPRO.items():\n", "    acum+= data_script.format(variables = \"{}\", key = key, description = value[\"descripcion\"], id=5, nombre_aplicacion = \"Generador Actividades Unipro\")\n", "\n", "for key, value in ESTUDIOS_UNIPRO.items():\n", "    acum+= data_script.format(variables = \"{}\", key = key, description = value[\"descripcion\"], id=7, nombre_aplicacion = \"Generador Tests de Validacion Unipro\")    \n", "    \n", "for key, value in ESTUDIOS_UNIPRO.items():\n", "    acum+= data_script.format(variables = f\"{{\\\"profesion\\\":\\\"{value[\"profesion\"]}\\\", \\\"titulo\\\":\\\"{value[\"nombre\"]}\\\"}}\", key = key, description = value[\"descripcion\"], id=6, nombre_aplicacion = \"Generador Guiones Tutorias Unipro\")  \n", "\n", "# Replace the last comma with a semicolon to end the SQL statement properly\n", "if acum.endswith(\",\\n\"):\n", "    acum = acum[:-2] + \";\\n\"\n", "\n", "print(script + acum)"]}], "metadata": {"kernelspec": {"display_name": "ia-gestorcontenidosiagen-be", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 2}