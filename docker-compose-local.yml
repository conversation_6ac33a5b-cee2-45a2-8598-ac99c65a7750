services:
  db: #In prod this would be a managed db.
    container_name: db_contenidos_postgres
    image: pgvector/pgvector:0.7.4-pg16
    ports:
      - "5436:5432"
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: contenidos_postgres
    volumes:
      - postgres:/var/lib/postgresql/data/
    restart: always
  phoenix:
    image: arizephoenix/phoenix:latest #this in prod 4.31
    ports:
      - 6006:6006
      - 4317:4317
    environment:
      - PHOENIX_WORKING_DIR=/mnt/data
      #- PHOENIX_ENABLE_AUTH=True
      #- PHOENIX_SECRET=sLjvTNOnuGw82Qiilh68vX5bwjIYNUtus
    volumes:
      - phoenix_data:/mnt/data
    restart: always
    pull_policy: always
  app:
    container_name: app_contenidos_local
    build:
      context: .
      dockerfile: Dockerfile.app
      target: without-cert
      args:
        UV_INDEX_UNIR_PASSWORD: ${UV_INDEX_UNIR_PASSWORD}
    command: [".venv/bin/poe", "start-api-local"]
    volumes:
      - ./src:/app/src
      - queue_broker_data:/app/tmp
    ports:
      - "8000:8000"
    environment:
      CommonEnvironment: ${CommonEnvironment}
      LoggingLevel: ${LoggingLevel}
      Workers: ${Workers}
      OpenaiApiKey: ${OpenaiApiKey}
      AnthropicApiKey: ${AnthropicApiKey}
      AzureOpenaiEndpoint: ${AzureOpenaiEndpoint}
      AzureOpenaiApiKey: ${AzureOpenaiApiKey}
      AzureApiVersion: ${AzureApiVersion}
      GenAiApiUrl: ${GenAiApiUrl}
      FastapiApiKey: ${FastapiApiKey}
      DbPort: ${DbPort}
      DbUser: ${DbUser}
      DbName: ${DbName}
      DbPassword: ${DbPassword}
      DbHost: ${DbHost}
      QueueMode: ${QueueMode}
      ServiceBusConnectionString: ${ServiceBusConnectionString}
      ContentGenerationQueueName: ${ContentGenerationQueueName}
      ProcessDocumentQueueName: ${ProcessDocumentQueueName}
      GenerateContentTaskNConcurrent: ${GenerateContentTaskNConcurrent}
      AzureStorageConnectionString: ${AzureStorageConnectionString}
      CooldownAzureStorageConnectionString: ${CooldownAzureStorageConnectionString}
      ScanContainerName: ${ScanContainerName}
      RerankAzureApiKey: ${RerankAzureApiKey}
      RerankAzureName: ${RerankAzureName}
      RerankAzureUrl: ${RerankAzureUrl}
      SearchEngine: ${SearchEngine}
      BraveSearchApiKey: ${BraveSearchApiKey}
      TavilySearchApiKey: ${TavilySearchApiKey}
      PhoenixProjectName: ${PhoenixProjectName}
      PhoenixCollectorEndpoint: ${PhoenixCollectorEndpoint}
      PhoenixPort: ${PhoenixPort}
      OtelExporterOtlpHeaders: ${OtelExporterOtlpHeaders}
      ApplicationinsightsConnectionString: ${ApplicationinsightsConnectionString}
      AzureKeyVaultUrl: ${AzureKeyVaultUrl}
      MermaidBaseUrl: ${MermaidBaseUrl}
      SendEmailApiKey: ${SendEmailApiKey}
      MailSendEndpoint: ${MailSendEndpoint}
      GenerateDocumentTaskNConcurrent: ${GenerateDocumentTaskNConcurrent}
      BaseUrl: ${BaseUrl}
      RouterApiKey: ${RouterApiKey}
    depends_on:
      - "db"
      - "phoenix"
    restart: always

  background:
    build:
      context: .
      target: without-cert
      dockerfile: Dockerfile.background
      args:
        UV_INDEX_UNIR_PASSWORD: ${UV_INDEX_UNIR_PASSWORD}
    ports:
      - "80:80"
    volumes:
      - ./src:/app/src
      - queue_broker_data:/app/tmp
    environment:
      CommonEnvironment: ${CommonEnvironment}
      LoggingLevel: ${LoggingLevel}
      Workers: ${Workers}
      OpenaiApiKey: ${OpenaiApiKey}
      AnthropicApiKey: ${AnthropicApiKey}
      AzureOpenaiEndpoint: ${AzureOpenaiEndpoint}
      AzureOpenaiApiKey: ${AzureOpenaiApiKey}
      AzureApiVersion: ${AzureApiVersion}
      GenAiApiUrl: ${GenAiApiUrl}
      FastapiApiKey: ${FastapiApiKey}
      DbPort: ${DbPort}
      DbUser: ${DbUser}
      DbName: ${DbName}
      DbPassword: ${DbPassword}
      DbHost: ${DbHost}
      QueueMode: ${QueueMode}
      ServiceBusConnectionString: ${ServiceBusConnectionString}
      ContentGenerationQueueName: ${ContentGenerationQueueName}
      ProcessDocumentQueueName: ${ProcessDocumentQueueName}
      GenerateContentTaskNConcurrent: ${GenerateContentTaskNConcurrent}
      AzureStorageConnectionString: ${AzureStorageConnectionString}
      CooldownAzureStorageConnectionString: ${CooldownAzureStorageConnectionString}
      ScanContainerName: ${ScanContainerName}
      RerankAzureApiKey: ${RerankAzureApiKey}
      RerankAzureName: ${RerankAzureName}
      RerankAzureUrl: ${RerankAzureUrl}
      SearchEngine: ${SearchEngine}
      BraveSearchApiKey: ${BraveSearchApiKey}
      TavilySearchApiKey: ${TavilySearchApiKey}
      PhoenixProjectName: ${PhoenixProjectName}
      PhoenixCollectorEndpoint: ${PhoenixCollectorEndpoint}
      PhoenixPort: ${PhoenixPort}
      OtelExporterOtlpHeaders: ${OtelExporterOtlpHeaders}
      ApplicationinsightsConnectionString: ${ApplicationinsightsConnectionString}
      AzureKeyVaultUrl: ${AzureKeyVaultUrl}
      SendEmailApiKey: ${SendEmailApiKey}
      MailSendEndpoint: ${MailSendEndpoint}
      MermaidBaseUrl: ${MermaidBaseUrl}
      GenerateDocumentTaskNConcurrent: ${GenerateDocumentTaskNConcurrent}
      BaseUrl: ${BaseUrl}
      RouterApiKey: ${RouterApiKey}
    restart: "always"

volumes:
  postgres:
  phoenix_data:
    driver: local
  queue_broker_data:

networks:
  default:
    name: cont_gen_poc_network
#TODO: Add option for sqlite

secrets:
  UV_INDEX_UNIR_PASSWORD:
    file: ./uv-index-unir-password.txt
