#!/usr/bin/env bash
# Push image to the UNIR private registry
source "$DO_SCRIPTS_PATH/functions.sh"

if [ -z "$TEAMCITY_VERSION" ]; then
  # ejecuciones fuera de TeamCity
  print "<red>ERROR: </red><yellow>--docker-push</yellow> <red>is not allowed outside Teamcity</red>"
  exit 1
fi

REPO=760557048868.dkr.ecr.eu-west-1.amazonaws.com

$DO_SCRIPTS_PATH/aws-ecr-login.sh teamcity && {
    print "2. tag <yellow>$DO_IMAGE_NAME:$DO_IMAGE_VERSION</yellow>" && \
    docker tag "$DO_IMAGE_NAME:$DO_IMAGE_VERSION" "$REPO/$DO_IMAGE_NAME:$DO_IMAGE_VERSION" && \

    print "3. push: <yellow>$REPO/$DO_IMAGE_NAME:$DO_IMAGE_VERSION</yellow>" && \
    execute_script "docker push $REPO/$DO_IMAGE_NAME:$DO_IMAGE_VERSION"
}
