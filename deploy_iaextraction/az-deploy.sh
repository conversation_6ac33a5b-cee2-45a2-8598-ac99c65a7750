#!/usr/bin/env bash
# deploy to Azure <version>
source "$DO_SCRIPTS_PATH/functions.sh"

AZ_RESOURCE_TYPE="Microsoft.Web/sites"
AWS_ECR_URL="https://760557048868.dkr.ecr.eu-west-1.amazonaws.com"
AWS_ECR_USER="AWS"
AWS_IMAGEN_NAME=760557048868.dkr.ecr.eu-west-1.amazonaws.com/${DO_DPTO}-${DO_APPNAME}:${DO_IMAGE_VERSION}

SUBSCRIPTION=""
if [[ -n "$DO_AZ_SUBSCRIPTION" ]]; then
    print "Adding Azure subscription"
    SUBSCRIPTION="--subscription $DO_AZ_SUBSCRIPTION"
fi

print "2. Logging into Azure"
az login --service-principal \
    -u "$DO_AZ_SERVICE_PRINCIPAL_CLIENTID" \
    -p "$DO_AZ_SERVICE_PRINCIPAL_CLIENT_SECRET" \
    --tenant "$DO_AZ_TENANT_UNIR"

print "3. Enabling access to App Service"
az resource update --resource-group "$DO_AZ_RESOURCE_GROUP" \
    --name "$DO_AZ_RESOURCE" \
    --resource-type "$AZ_RESOURCE_TYPE" \
    --set properties.publicNetworkAccess=Enabled \
    $SUBSCRIPTION

print "4. Configuring Docker image in App Service"
az webapp config container set --resource-group "$DO_AZ_RESOURCE_GROUP" \
    --name "$DO_AZ_RESOURCE" \
    --container-registry-url "$AWS_ECR_URL" \
    --container-registry-user "$AWS_ECR_USER" \
    --container-image-name "$AWS_IMAGEN_NAME" \
    $SUBSCRIPTION  

print "5. Restarting App Service"
az webapp restart --resource-group "$DO_AZ_RESOURCE_GROUP" \
    --name "$DO_AZ_RESOURCE" \
    $SUBSCRIPTION 

print "6. Disabling access to App Service"
az resource update --resource-group "$DO_AZ_RESOURCE_GROUP" \
    --name "$DO_AZ_RESOURCE" \
    --resource-type "$AZ_RESOURCE_TYPE" \
    --set properties.publicNetworkAccess=Disabled \
    $SUBSCRIPTION