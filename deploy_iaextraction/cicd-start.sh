#!/bin/bash
# SCRIPT de inicio para la build, publicacion y despliegue de aplicaciones
# Para mas detalles ejecutar:
#   cicd-start.sh --help

# Variables comunes ---------------------------------------------------------------------------------------------------------
export DO_PROJECT=""
export DO_APP_TYPE=""
export DO_OPTION=""
export DO_SCRIPTS_PATH="./deploy_iaextraction" 
export DO_DPTO=ia
export DO_ENTORNO=des
export DO_HOST_PORT=5001 #solo para levantar la imagen en local y hacer pruebas de funcionamiento
export DO_VERBOSE="true"
export DO_AWS_PROFILE="teamcity"
export DO_SCRIPT_PARAMS=("$@")
export DO_CUSTOM_SCRIPT="no-custom-script-defined"
# ruta del archivo que contiene la version de la aplicacion: Formatos soportados: cs, ts, json
export DO_FILE_VERSION=""

source "$DO_SCRIPTS_PATH/functions.sh"
print "{head}"

# permisos
chmodx_scripts

# parametros
get_script_parameters "$@"

# validaciones
validate_PROJECT

# Comprueba el tipo de aplicacion e inicializa ciertas variables dependiendo de su valor
check_app_type

# version
check_version

# logs
print_log

# SCRIPT A EJECUTAR ------------------------------------------------------------------------------------------------------------------
# Ejecutamos el script segun al parametro $DO_OPTION
case $DO_OPTION in
   --docker-build)
    SCRIPT_FILE="$DO_SCRIPTS_PATH/docker-build.sh" ;;
  --docker-publish)
    SCRIPT_FILE="$DO_SCRIPTS_PATH/docker-push.sh" ;;
  --az-deploy)
    SCRIPT_FILE="$DO_SCRIPTS_PATH/az-deploy.sh" ;;
  --run-image)
    SCRIPT_FILE="$DO_SCRIPTS_PATH/docker-run.sh" ;;
  --create-tag)
     SCRIPT_FILE="$DO_SCRIPTS_PATH/create-az-tc-tags.sh" ;;
  --run-script)
    SCRIPT_FILE="$DO_CUSTOM_SCRIPT" ;;
esac

# Ejecutamos el script
execute_script "$SCRIPT_FILE"

print "END ${DO_LOG_SEPARATOR}"