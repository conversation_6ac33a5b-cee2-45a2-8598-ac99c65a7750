#!/usr/bin/env bash


# Valida los parametros principales y asigna sus valores a las variables correspondientes
set_main_parameters() {
  for i in "${MAIN_PARAMETERS[@]}"; do
    # Extraer clave y valor
    local key=$(echo "$i" | cut -d':' -f1)
    local value=$(echo "$i" | cut -d':' -f2)

    case $key in
      --project)
        DO_PROJECT="$value" ;;

      --app-type)
        DO_APP_TYPE="$value" ;;

      --version)
        CUSTOM_VERSION="$value" ;;

      --host-port)
        DO_HOST_PORT="$value" ;;

      --aws-profile)
        DO_AWS_PROFILE="$value" ;;

      --version-path)
        DO_FILE_VERSION="$value" ;;

      --dpto)
        DO_DPTO="$value" ;;

      --entorno)
        DO_ENTORNO="$value" ;;

      --script)
        DO_CUSTOM_SCRIPT="$value" ;;

    esac
  done
}

# Valida los parametros principales (los que se definen al inicio de cicd-start.sh) y asigna sus valores a las variables correspondientes
validate_allowed_main_parameters() {
  for i in "${MAIN_PARAMETERS[@]}"; do
    # Extraer clave y valor
    key=$(echo "$i" | cut -d':' -f1)
    value=$(echo "$i" | cut -d':' -f2)

    case $key in
        --project | --app-type | --dpto | --entorno)
            validate_allowed_option_parameter "$DO_OPTION" "$key" all_allowed_options[@] ;;

        --version)
            validate_allowed_option_parameter "$DO_OPTION" "$key" version_allowed_options[@] ;;

        --host-port)
            local runimage_allowed_options=("--run-image")
            validate_allowed_option_parameter "$DO_OPTION" "$key" runimage_allowed_options[@] ;;

        --aws-profile)
            validate_allowed_option_parameter "$DO_OPTION" "$key" dockerbuild_allowed_options[@] ;;

    esac
  done
}

# Función para verificar si un valor existe en una lista
validate_allowed_option_parameter() {
  local option="$1"
  local argument="$2"
  local list=("${!3}") # Recibe el nombre de la variable de array como referencia

  if ! exists_element_in_list "$option" list[@]; then
    print "<red>Argument</red> <yellow>$argument<yellow> <red>is not valid for option</red> <yellow>$option<yellow>"
    print "<red>Allowed options: </red>"
    for argument in "${list[@]}"; do
        print_variable "  " "$argument"
    done

    show_usage
    exit 1
  fi
}

# Valida si los parámetros estan permitidos para el tipo de aplicación actual (DO_APP_TYPE).
validate_allowed_app_type_parameters() {
  for i in "${MAIN_PARAMETERS[@]}"; do
    local key=$(echo "$i" | cut -d':' -f1)

    case $DO_APP_TYPE in
        $APP_TYPE_SPA)
            validate_allowed_app_type_parameter "$key" "${spa_allowed_parameters[*]}" ;;
        $APP_TYPE_BE)
            validate_allowed_app_type_parameter "$key" "${be_allowed_parameters[*]}" ;;
        $APP_TYPE_MFE)
            validate_allowed_app_type_parameter "$key" "${mfe_allowed_parameters[*]}" ;;
    esac
  done
}

# Valida si el parámetro esta permitido para el tipo de aplicación actual (DO_APP_TYPE).
# Si el parámetro no está permitido para ese tipo de aplicación, se muestra un mensaje de error
validate_allowed_app_type_parameter() {
  local parameter="$1"
  local allowed_parameters_string="$2"
  local allowed_parameters=($allowed_parameters_string)  # Convierte la cadena en un array

  if ! exists_element_in_list "$parameter" allowed_parameters[@]; then
    print "<red>Error: Argument </red><yellow>$parameter</yellow> <red>is not valid for</red> <yellow>--app-type: $DO_APP_TYPE</yellow>"
    print "<red>Allowed arguments: </red>"
    for argument in "${allowed_parameters[@]}"; do
        print_variable "  " "$argument"
    done
    exit 1
  fi
}


validate_mfe_parameters() {
  if [[ "$DO_APP_TYPE" == $APP_TYPE_MFE ]]; then
    for i in "${MAIN_PARAMETERS[@]}"; do
        # Extraer clave y valor
        local key=$(echo "$i" | cut -d':' -f1)
        local value=$(echo "$i" | cut -d':' -f2)

        case $key in
            --app-name | --app-list | --enable-sourcemaps)
                validate_allowed_option_parameter "$DO_OPTION" "$key" all_allowed_options[@] ;;

            --change-detection)
                if validate_allowed_option_parameter "$DO_OPTION" "$key" all_allowed_options[@]; then
                  local change_detection_allowed_values=("affected" "all")
                  validate_argument_allowed_values "$key" "$value" change_detection_allowed_values[@]
                fi
                ;;

            --use-nginx-config)
                validate_allowed_option_parameter "$DO_OPTION" "$key" dockerbuild_allowed_options[@] ;;
        esac
    done
  fi
}

# Valida que el parametro sea un comando permitido para aplicaciones FE
validate_spa_parameters() {
  if [[ "$DO_APP_TYPE" == $APP_TYPE_SPA ]]; then
    for i in "${MAIN_PARAMETERS[@]}"; do
        # Extraer clave y valor
        local key=$(echo "$i" | cut -d':' -f1)
        local value=$(echo "$i" | cut -d':' -f2)

        case $key in
            --enable-sourcemaps)
                validate_allowed_option_parameter "$DO_OPTION" "$key" all_allowed_options[@] ;;

            --use-nginx-config)
                validate_allowed_option_parameter "$DO_OPTION" "$key" dockerbuild_allowed_options[@] ;;
        esac
    done
  fi
}

# Valida que el parametro sea un comando permitido para aplicaciones BE
validate_be_parameters() {
  local app_type=$1

  if [[ "$DO_APP_TYPE" == $app_type ]]; then
    validate_required_parameters $app_type "--docker-build" be_required_parameters[@]

    for i in "${MAIN_PARAMETERS[@]}"; do
        # Extraer clave y valor
        local key=$(echo "$i" | cut -d':' -f1)
        local value=$(echo "$i" | cut -d':' -f2)

        case $key in
            --pat-key | --pat-token)
                validate_allowed_option_parameter "$DO_OPTION" "$key" dockerbuild_allowed_options[@] ;;
        esac
    done
  fi
}

# Valida el tipo de aplicacion
is_valid_app_type() {
  APP_TYPES=($APP_TYPE_BE $APP_TYPE_BFF $APP_TYPE_SPA $APP_TYPE_MFE)

  if exists_element_in_list "$DO_APP_TYPE" APP_TYPES[@]; then
    echo "true"
  else
    echo "false"
  fi
}

# Valida los valores permitidos para argumentos cuyos valores deban estar dentro de un rango predefinido
validate_argument_allowed_values() {
  local argument_name=$1
  local argument_value=$2
  local allowed_values=("${!3}")

  if ! exists_element_in_list "$argument_value" allowed_values[@]; then
    print "<red>Error: Argument</red> <yellow>$argument_name</yellow> <red>has an invalid value</red>: <yellow>$argument_value</yellow>"
    exit 1 #ERROR
  fi
}

# Funcion para validar la variable DO_PROJECT
validate_PROJECT() {
  # Comprueba si DO_PROJECT esta vacio
  if [[ -z "$DO_PROJECT" ]]; then
    print "<red>Error: The project name (--project) is required and cannot be empty.</red>"
    show_usage
    exit 1  #ERROR
  fi

  local appType=$(echo "$DO_APP_TYPE" | tr '[:upper:]' '[:lower:]')

  if [[ "$appType" == "$APP_TYPE_BE" || "$appType" == "$APP_TYPE_BFF" ]]; then
    if [[ "$DO_PROJECT" == *-* ]]; then
      print "<red>Error: The project name (--project) cannot contain hyphens (-).</red>"
      exit 1  #ERROR
    fi
  else
    # Comprueba si DO_PROJECT contiene un guion o letras mayusculas
    if [[ "$DO_PROJECT" == *[-A-Z]* ]]; then
      print "<red>Error: The project name (--project) cannot contain hyphens (-) or capital letters.</red>"
      exit 1  #ERROR
    fi
  fi
}

# Función para verificar si una ruta de archivo es válida
is_valid_file_path() {
  local file_path="$1"

  # Comprueba si la ruta existe y es un archivo
  if [[ -f "$file_path" ]]; then
    echo "true"  # La ruta es válida y es un archivo
  else
    echo "false"  # La ruta no es válida o no es un archivo
  fi
}

# Función para verificar si una ruta de directorio es válida
is_valid_directory_path() {
  local directory_path="$1"

  # Comprueba si la ruta existe y es un directorio
  if [[ -d "$directory_path" ]]; then
    echo "true"  # La ruta es válida y es un directorio
  else
    echo "false"  # La ruta no es válida o no es un directorio
  fi
}

# Valida el tipo de aplicacion
validate_app_type() {
  if [[ $(is_valid_app_type) == "false" ]]; then
    print "<red>Error: DO_APP_TYPE (--app-type parameter) value ('$DO_APP_TYPE') is not valid.</red>"
    show_usage
    exit 1 #ERROR
  fi
}

# Valida parametros requeridos para el tipo de aplicación actual (DO_APP_TYPE).
validate_required_parameters() {
  local app_type="$1"
  local option="$2"

  if [[ "$DO_APP_TYPE" == $app_type && "$DO_OPTION" == "$option" ]]; then
    local required_parameters=("${!3}")
    local found

    for required_param in "${required_parameters[@]}"; do
        found=false
        for i in "${MAIN_PARAMETERS[@]}"; do
            local key=$(echo "$i" | cut -d':' -f1)
            if [[ "$key" == "$required_param" ]]; then
                found=true
                break
            fi
        done

        if [[ "$found" == false ]]; then
            print "<red>Error: Required argument </red><yellow>$required_param</yellow> <red>is missing for</red> <yellow>--app-type: $app_type</yellow> <red>and option</red> <yellow>$option</yellow>"
            exit 1
        fi
    done
  fi
}