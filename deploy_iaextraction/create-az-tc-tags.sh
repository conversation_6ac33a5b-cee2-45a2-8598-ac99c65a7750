#!/usr/bin/env bash

print "<red>Init TAGGING</red>" 

source "$DO_SCRIPTS_PATH/functions.sh"

chmod +x $DO_APPTYPE_PATH/get-version.sh

print "Setting tags based on file-version:<yellow>$DO_FILE_VERSION</yellow>"

# Return the complete version number as x.y.z
VERSION=`$DO_APPTYPE_PATH/get-version.sh`

TAG="iaext-${BRANCH}-${VERSION}"

print "Setting tag branch-version:<yellow>$TAG</yellow>"

#tag in TC:
echo "##teamcity[addBuildTag '$TAG']"

#tag in Azure-Devops:
git tag -a "$TAG" -m "$TAG"
git push origin --tags
