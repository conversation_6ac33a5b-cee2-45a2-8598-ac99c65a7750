#!/usr/bin/env bash
export DO_LOG_PREFIX="##>"
export DO_LOG_SEPARATOR="----------------------------------------------------------------------------------------------"
export DISABLE_SCRIPT_PATH=""
export MAIN_PARAMETERS=()

export APP_TYPE_MFE="mfe"
export APP_TYPE_SPA="spa"
export APP_TYPE_BE="be"
export APP_TYPE_BFF="bff"

source "$DO_SCRIPTS_PATH/validations.sh"

# Función para mostrar el uso del script
show_usage() {
  print "" "true" #desactiva la impresion de la ruta del script
  print "Usage: cicd-start.sh [OPCION] [ARGUMENTOS...]" "true"
  print ""
  print "Available options:"
  print "  <yellow>--build</yellow>                  Compile to generate artifacts (only for frontend applications)"
  print "  <yellow>--docker-build</yellow>           Generate Docker image"
  print "  <yellow>--docker-publish</yellow>         Publish Docker image to AWS"
  print "  <yellow>--az-deploy</yellow>              Deploy Docker image to Azure"
  print "  <yellow>--run-image</yellow>              Start the container of the previously generated image with the --build + --docker-build option"
  print "  <yellow>--crete-tag</yellow>              Create Teamcity and Azure DevOps tag"
  print "  <yellow>--help</yellow>                   Show this help message"
  print ""
  print "<yellow>Common arguments:</yellow>"
  print "    --project                              Project name"
  print "    --app-type                             Application type (be|bff|spa|mfe|lib|job)"
  print "    --entorno                              Entorno (local|des|pre|pro)"
  print "    --dpto                                 Department/Area (arc|aca|fin|com)"
  print "    --aws-profile                          (Optional) AWS Services profile"
  print "    --version                              (Optional) Specific version of the application"
  print "    --version-path                         (Optional) Source File that contains the application version (Major.Minor.TeamcityBuildNumber) that will be versioned with the docker image"
  print "    --az-service-principal-clientid        (Optional) Azure service principal clientid"
  print "    --az-service-principal-client-secret   (Optional) Azure service principal client secret"
  print "    --az-tenant-unir                       (Optional) Azure tenant unir"
  print "    --az-resource-group                    (Optional) Azure resource group"
  print "    --az-resource                          (Optional) Azure resource (service name)"
  print "    --subscription                         (Optional) Azure Subscription"
  print "    --unir-token                           (Optional) Azure DevOps Personal Access Token"
  print ""
  print "<yellow>MFE</yellow> arguments:"
  print "    --app-name             (Optional) Monorepo/Microfrontend application name"
  print "    --app-list             (Optional) Monorepo/Microfrontend application list, separated by '|'"
  print "    --change-detection     (Optional) MFE application modifications detection on source repository. Values: affected | all"
  print ""
  print "<yellow>SPA/MFE</yellow> arguments:"
  print "    --enable-sourcemaps    (Optional) Enable compilation to deploy artifacts with source maps for debugging like in a "
  print "                           local environment. (only if --app-type=mfe|spa). Values: true|false"
  print "    --use-nginx-config     (Optional) Copy and overwrites nginx.conf on docker build phase. Values: Only the presence of the parameter indicates true"
  print ""
  print "<yellow>BE</yellow> arguments:"
  print "    --pat-token            PAT (Personal Access Token) value"
  print "    --pat-key              PAT (Personal Access Token) Key nane"
  print ""
  print "<yellow>--run-image</yellow> arguments:"
  print "    --host-port            (Optional) Port exposed by 'docker run' command"
  print "" "false" #activa la impresion de la ruta del script
}

# procesa todos los parámetros del script y se lanzan las validaciones para comprobar si los parámetros son válidos, si hay algún
# parámetro que no pertenezca a la opción o al tipo de aplicación seleccionados.
get_script_parameters() {
  define_rules

  # mientras haya argumentos que procesar
  while (( "$#" )); do
      case "$1" in
          # OPCIONES ------------------------------------------------------------------------------------------------------
          --build | --docker-build | --docker-publish | --az-deploy | --run-image | --create-tag | --run-script)
              option="$1"
              ;;
          --help)
              show_usage
              exit 0
              ;;

          # ARGUMENTOS de opciones ------------------------------------------------------------------------------------------------------
          --project | --version | --app-name | --change-detection | --app-type | --dpto | --entorno | --host-port | --aws-profile | \
          --app-list | --pat-key | --pat-token | --version-path | --enable-sourcemaps | --use-nginx-config | \
          --script | --az-service-principal-clientid | --az-service-principal-client-secret | \
          --az-tenant-unir | --az-resource-group | --az-resource | --subscription | --unir-token)
              MAIN_PARAMETERS+=("$1:$2")
              shift
              ;;

          --) # Fin de todas las opciones
              shift
              break
              ;;
          * | -* | --*)
              print "<red>Invalid option</red> <yellow>$1<yellow>"
              show_usage
              exit 1
              ;;
      esac
      shift
  done

  DO_OPTION="$option"

  validate_allowed_main_parameters
  set_main_parameters

  validate_allowed_app_type_parameters

  validate_mfe_parameters
  validate_spa_parameters
}

# Define las reglas sobre que parametros pueden ser utilizados dependiendo del tipo de aplicacion o de la opcion escogida
define_rules() {
  # parametros permitidos segun el tipo de opcion (para todos los app types)
  all_allowed_options=("--build" "--docker-build" "--docker-publish" "--az-deploy" "--run-image" "--create-tag" "--run-script")
  version_allowed_options=("--build" "--docker-build" "--docker-publish" "--az-deploy" "--run-image")
  dockerbuild_allowed_options=("--docker-build" "--run-script")

  # parametros obligatorios segun el tipo de aplicacion
  be_required_parameters=("--pat-token" "--pat-key")

  # parametros permitidos segun el tipo aplicacion
  common_allowed_parameters=("--project" "--app-type" "--version" "--aws-profile" "--host-port" "--version-path" "--dpto" "--entorno" "--script" "--az-service-principal-clientid" "--az-service-principal-client-secret" "--az-tenant-unir" "--az-resource-group" "--az-resource" "--subscription" "--unir-token")
  spa_allowed_parameters=("${common_allowed_parameters[@]}" "--enable-sourcemaps" "--use-nginx-config" "--app-name")
  mfe_allowed_parameters=("${spa_allowed_parameters[@]}" "--app-list" "--change-detection")
  be_allowed_parameters=("${common_allowed_parameters[@]}" "${be_required_parameters[@]}")

  # parametros sensibles que no deben ser mostrados en los logs
  credential_args=("--pat-token" "--unir-token")
}

# print: Funcion para imprimir mensajes con formato de log y colores
# $1 (string):      Texto a imprimir
# $2 (true/false):  Habilita la impresion de la ruta del script
print() {
  local scriptPath="$0"
  local text="$1"
  local displayScriptPath="$2"
  local message
  shift  # Elimina el primer argumento (cadena de formato)

  if [[ -n "$displayScriptPath" ]]; then
    DISABLE_SCRIPT_PATH=$displayScriptPath
    echo ""
    return 0
  fi

  if [[ "$(is_valid_file_path "$scriptPath")" == "true" && "$DISABLE_SCRIPT_PATH" != "true" ]]; then
    # Construir el mensaje. Si el segundo argumento es "{head}", formatea el mensaje
    # como encabezado, de lo contrario, como un mensaje normal
    if [[ "$text" == "{head}" ]]; then
      message="${DO_LOG_PREFIX} <blue>$scriptPath</blue> ${DO_LOG_SEPARATOR}"
    else
      message="${DO_LOG_PREFIX} <green>$scriptPath:</green> $text"
    fi
  else
    message="$text"
  fi

  # realiza transformaciones de argumentos adicionales (si los hubiese) con mensajes que contienen especificadores de formato como %s, %d, etc.
  message=$(printf "$message" "$@")

  # Llamar a echoColor para imprimir el mensaje con los colores apropiados
  if [ "$(uname -s)" = "Darwin" ]; then
    # macOS
    echoColorMacOs "$message"
  else
    # Windows / linux
    # echoColorLinux "$message"
    echoColorTeamcity "$message"
  fi
}

# print: Funcion para imprimir variables con formato de log y colores
print_variable() {
  if [[ "$DO_VERBOSE" == "true" ]]; then
      print "<purple>$1</purple> <yellow>$2</yellow>"
  fi
}

# echoColorLinux: Funcion para imprimir texto con colores usando secuencias de escape ANSI.
# Colores soportados:
# - <red>: Rojo
# - <green>: Verde
# - <yellow>: Amarillo
# - <blue>: Azul
# - <purple>: Morado
# - <cyan>: Cian
# - <white>: Blanco
echoColorLinux() {
  local text="$1"

  # Definir colores usando tput
  local red=$(tput setaf 1)
  local green=$(tput setaf 2)
  local yellow=$(tput setaf 3)
  local blue=$(tput setaf 4)
  local purple=$(tput setaf 5)
  local cyan=$(tput setaf 6)
  local white=$(tput setaf 7)
  local reset=$(tput sgr0)

  # Reemplazar etiquetas de colores en el texto por sus correspondientes codigos de color
  text="${text//<red>/$red}"; text="${text//<\/red>/$reset}"
  text="${text//<green>/$green}"; text="${text//<\/green>/$reset}"
  text="${text//<yellow>/$yellow}"; text="${text//<\/yellow>/$reset}"
  text="${text//<blue>/$blue}"; text="${text//<\/blue>/$reset}"
  text="${text//<purple>/$purple}"; text="${text//<\/purple>/$reset}"
  text="${text//<cyan>/$cyan}"; text="${text//<\/cyan>/$reset}"
  text="${text//<white>/$white}"; text="${text//<\/white>/$reset}"

  # Imprimir el texto final con los colores aplicados
  print_to_console "$text"
}

# echoColorLinux: Funcion para imprimir texto con colores usando secuencias de escape ANSI.
# Colores soportados:
# - <red>: Rojo
# - <green>: Verde
# - <yellow>: Amarillo
# - <blue>: Azul
# - <purple>: Morado
# - <cyan>: Cian
# - <white>: Blanco
echoColorMacOs() {
  local text="$1"
  local reset="\e[0m"

  # Reemplazar etiquetas de colores en el texto por sus correspondientes
  # secuencias de escape ANSI, y resetear el color despues de cada etiqueta
  text="${text//<red>/$reset\e[31m}"; text="${text//<\/red>/$reset}"
  text="${text//<green>/$reset\e[32m}"; text="${text//<\/green>/$reset}"
  # Repetir para cada color soportado
  text="${text//<yellow>/$reset\e[33m}"; text="${text//<\/yellow>/$reset}"
  text="${text//<blue>/$reset\e[34m}"; text="${text//<\/blue>/$reset}"
  text="${text//<purple>/$reset\e[35m}"; text="${text//<\/purple>/$reset}"
  text="${text//<cyan>/$reset\e[36m}"; text="${text//<\/cyan>/$reset}"
  text="${text//<white>/$reset\e[37m}"; text="${text//<\/white>/$reset}"

  # Imprimir el texto final con las secuencias de escape interpretadas
  print_to_console "$text"
}

# echoColor: Funcion para imprimir texto con colores y estilos en TeamCity.
# Esta funcion interpreta etiquetas personalizadas en el texto y las convierte en
# codigos de escape ANSI compatibles con TeamCity. Soporta los siguientes colores y estilos:
# - Colores: <red>, <green>, <yellow>, <blue>, <purple>, <cyan>, <white>
#     usage:
#     echoColorTeamcity "<cyan>Running: Update process has begun...</cyan>"
#     echoColorTeamcity "<blue>Info: Starting the backup process.</blue>"
#     echoColorTeamcity "<yellow>Warning: Disk space is running low.</yellow>"
#     echoColorTeamcity "<green>Success: Operation completed successfully.</green>"
#     echoColorTeamcity "<red>Error: File not found.</red>"
echoColorTeamcity() {
  local text="$1"
  local reset="\033[0m"

  # Definir colores y estilos usando códigos soportados por TeamCity
  local red="\033[31m"
  local green="\033[32m"
  local yellow="\033[33m"
  local blue="\033[34m"
  local purple="\033[35m"
  local cyan="\033[36m"
  local white="\033[37m"

  # Reemplazar etiquetas de colores y estilos en el texto
  text="${text//<red>/$red}"; text="${text//<\/red>/$reset}"
  text="${text//<green>/$green}"; text="${text//<\/green>/$reset}"
  text="${text//<yellow>/$yellow}"; text="${text//<\/yellow>/$reset}"
  text="${text//<blue>/$blue}"; text="${text//<\/blue>/$reset}"
  text="${text//<purple>/$purple}"; text="${text//<\/purple>/$reset}"
  text="${text//<cyan>/$cyan}"; text="${text//<\/cyan>/$reset}"
  text="${text//<white>/$white}"; text="${text//<\/white>/$reset}"

  print_to_console "$text"
}

print_to_console() {
  local text="$1"

  # Convertir el texto a minúsculas para verificar si contiene "error"
  local lowercase_text=$(echo "$text" | tr '[:upper:]' '[:lower:]')

  # Verificar si contiene "error" y las etiquetas correspondientes
  if [[ "$lowercase_text" == *"<red>"*"error"*"</red>"* ]]; then
    # Imprimir el mensaje en stderr
    printf "%b\n" "$text$reset" >&2
  else
    # Imprimir el mensaje en stdout
    printf "%b\n" "$text$reset"
  fi
}

# carga los parametros por defecto
load_app_type_default_parameters() {
  # ruta de scripts especificos para un tipo de aplicacion concreta
  export DO_APPTYPE_PATH="$DO_SCRIPTS_PATH/$DO_APP_TYPE"
  print "Loading specific variables from <yellow>$DO_APPTYPE_PATH/setup.sh</yellow> and overwriting default values with provided parameters ..."
  source "$DO_APPTYPE_PATH/setup.sh"
}

# Establece variables de aplicaciones fe/be y el resto de variables de sistema
set_app_properties() {
  export DO_APPNAME="${DO_PROJECT}-${DO_APP_TYPE}"
  # ruta de la app
  export DO_APP_PATH="$DO_SCRIPTS_PATH/$DO_APP_TYPE"

  set_app_common_properties
}

# Establece la ruta de las plantillas de la aplicacion y el resto de variables de sistema
set_app_common_properties() {
  # VARIABLES AUTO-GENERADAS
  export DO_IMAGE_NAME="${DO_DPTO}-${DO_APPNAME}" 


  # # PLANTILLAS
  export DO_DOCKER_FILE_TEMPLATE="$DO_APPTYPE_PATH/Dockerfile.template"
  export DO_DOCKER_FILE="$DO_APP_PATH/Dockerfile"
}

# Método para verificar si una app es válida en la lista de apps (app_type=mfe)
exists_element_in_list() {
  local target_element="$1"
  local list=("${!2}")  # Recibe el nombre de la variable de array como referencia
  local list_str=" ${list[*]} "  # Crea una cadena con todos los elementos del array

  if [[ "$list_str" =~ " $target_element " ]]; then
      return 0  # Elemento encontrado en la lista
  else
      return 1  # Elemento no encontrado en la lista
  fi
}

# Función para comprobar si hay una aplicacion monorepo definida y ejecuta la opcion para todas las aplicaciones mfe que corresponda.
# Si no hay aplicacion monorepo, se ejecuta el flujo normal para aplicaciones (--app-type=spa|be).
check_app_type() {
  load_app_type_default_parameters
  validate_app_type

  if is_mfe_app_type; then
    validate_mfe_app
  else
    # No es una app MFE, sigue el flujo normal para otros tipos de apps
    set_app_properties
  fi
}

is_mfe_app_type() {
  if [[ "$DO_APP_TYPE" == "mfe" ]]; then
    return 0 # Es una app MFE
  else
    return 1 # No es una app MFE
  fi
}

chmodx_scripts() {
  # if [ "$DO_VERBOSE" == "true" ]; then
  #     print "Granting exec permission (chmod +x) to following scripts:"
  #     find $DO_SCRIPTS_PATH -type f -name "*.sh" -exec chmod +x {} \; -print
  # else
      find $DO_SCRIPTS_PATH -type f -name "*.sh" -exec chmod +x {} \;
  # fi
}

check_version() {
  export DO_IMAGE_VERSION=""
  if is_valid_file_version; then
    if [ "$CUSTOM_VERSION" == "" ]; then
        local version=`$DO_APPTYPE_PATH/get-version.sh`
        DO_IMAGE_VERSION="${version:-1.0.0}"
        print_variable "version:" "$DO_IMAGE_VERSION"
    else
        DO_IMAGE_VERSION=$CUSTOM_VERSION
        print_variable "version:" "$DO_IMAGE_VERSION (custom)"
    fi
  fi
}

print_log() {
  print_variable "project :" "$DO_PROJECT"
  print_variable "app type:" "$DO_APP_TYPE"
  print_variable "image name:" "$DO_APPNAME"
  print_variable "app path:" "$DO_APP_PATH"

  print_variable "script arguments:" ""
  local prev_arg=""
  for arg in "${DO_SCRIPT_PARAMS[@]}"; do
    if exists_element_in_list "$prev_arg" credential_args[@]; then
      print_variable "  " "***** (hidden for security)"
    else
      print_variable "  " "${arg}"
    fi
    prev_arg=$arg
  done

  print_variable "Node.js version:" "$(node -v)"
  print_variable "working dir:" "$(pwd)"
  print_log_app_type # este metodo debe estar definido en el setup.sh del app type especifico
}

print_file_content() {
  local filePath="$1"
  local title="${2:-"Template result:"}"
  local file_content

  # Leer el contenido del archivo y reemplazar '%' por '%%'
  file_content=$(cat "$filePath" | sed 's/%/%%/g')

  print "$title"
  print "" "true"  # Desactiva la impresión de la ruta del script
  print "<yellow>$file_content</yellow>"
  print "" "false" # Activa la impresión de la ruta del script
}

# Funcion para ejecutar un script y gestionar si diera algun error
execute_script() {
    local command="$1"

    print "Executing: <yellow>$command</yellow>"
    eval "$command"
    local exit_code=$?

    if [ "$exit_code" -ne 0 ]; then
        print "<red>Error: </red><yellow>$command</yellow> <red>failed with exit code $exit_code</red>"
        exit "$exit_code"
    fi
}

run_custom_script() {
  # Ejecuta cualquier script con ruta valida que se pase como parametro
  if [[ "$(is_valid_file_path "$DO_CUSTOM_SCRIPT_PATH")" == "true" ]]; then
    echo "$DO_CUSTOM_SCRIPT_PATH" #TODO: parametrizar
  else
    print "<red>ERROR: The specified script</red> <yellow>'$DO_CUSTOM_SCRIPT_PATH'</yellow> <red>has an invalid path.</red>"
    exit 1
  fi
}

is_valid_file_version() {
  # Comprueba si hay archivo de version definido
  if [[ -n "$DO_FILE_VERSION" ]]; then
    # Comprueba si existe el archivo
    if [[ "$(is_valid_file_path "$DO_FILE_VERSION")" == "true" ]]; then
      return 0
    else
      # Da error solo si se especifica la ruta pero no existe
      print "<red>Error: File version</red> <yellow>$DO_FILE_VERSION</yellow> <red>not found</red>"
      exit 1 #ERROR
    fi
  else
    print "<yellow>WARNING: no file version found to write to</yellow>"
    return 1
  fi
}

docfx() {
  # install
  # dotnet tool update -g docfx --version 2.75.1

  # build
  # docfx metadata "docfx_project/docfx.json"
  # docfx build "docfx_project/docfx.json"
  # docfx pdf "docfx_project/docfx.json" genera pdf (ver documentacion)  

  # deploy
  # #Formateo de la carpeta donde vuelca la documentación 
  # #requisito: 
  # # - input: "xxx-folder-zzz" 
  # # - output: "folder"
  # TARGET_FOLDER_NAME=$(echo "%system.teamcity.projectName%" | sed 's/^.*-\(.*\)-.*$/\1/')
  # echo "TARGET_FOLDER_NAME = $TARGET_FOLDER_NAME"

  # #Define la carpeta local "SOURCE" donde se genera la documentación en la pipeline.
  # SOURCE=docfx_project/_site/

  # #Define la carpeta remota "TARGET" donde vuelca la documentación.
  # TARGET_HOST=oldes-primarysource01
  # TARGET=${TARGET_HOST}:/mnt/portaldesarrollador/backends/$TARGET_FOLDER_NAME 

  # #Sincronización remota:
  # #rsync --recursive /dev/null $TARGET
  # rsync -crv $SOURCE $TARGET && \
  # rsync -crv --exclude=keepall --delete $SOURCE $TARGET
  return 1
}