#!/usr/bin/env bash
source "$DO_SCRIPTS_PATH/functions.sh"

if [[ "$DO_VERBOSE" == "true" ]]; then
  print "git log ..."
  git log -n 5 --oneline
fi

# (Opcional)
# execute_script "$DO_SCRIPTS_PATH/env-replacement.sh"

# (Opcional) setea la version antes de compilar
if is_valid_file_version; then
	execute_script "$DO_APPTYPE_PATH/set-version.sh $DO_IMAGE_VERSION"
fi

print "building <yellow>$DO_SPA_APP_NAME</yellow> ..." && \

# Escribir aqui los comandos necesarios para compilar la aplicacion
# >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>

# command="npx nx reset && npx nx build $DO_SPA_APP_NAME --prod"
command="npm run gestor:build"

execute_script "$command"

# <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<

# (opcional) restaura el archivo cambiado
# execute_script "$DO_SCRIPTS_PATH/env-replacement.sh restore"

