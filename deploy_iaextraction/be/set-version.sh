#!/usr/bin/env bash
#source "$DO_SCRIPTS_PATH/functions.sh"

NEW_VERSION=$1

EDIT_COMMIT_HASH=${2:-"0"}

GIT_COMMIT_HASH=$(git rev-parse --short HEAD)

if [ "$EDIT_COMMIT_HASH" == "0" ]; then
   sed -i -E "s/commithash/$GIT_COMMIT_HASH/" "$DO_FILE_VERSION"
fi

if [ "$(uname -s)" != "Darwin" ]; then
  # Windows / linux
  sed -i -E "/__version__ = /s/[0-9]+\.[0-9]+\.[0-9]+/${NEW_VERSION}/" "$DO_FILE_VERSION"
else
  # MacOs
  sed -i '' -E "/__version__ = /s/[0-9]+\.[0-9]+\.[0-9]+/${NEW_VERSION}/" "$DO_FILE_VERSION"
fi

   #sed -i -E "s/timestamp/$(date -u +"%Y-%m-%dT%H:%M:%S.%N%:z")/" "$DO_FILE_VERSION"
   sed -i -E "s|timestamp|$(date -u +"%Y-%m-%dT%H:%M:%S.%N%:z")|" "$DO_FILE_VERSION"

#if [ "$DO_VERBOSE" == "true" ]; then
#  print_file_content "$DO_FILE_VERSION" "$DO_FILE_VERSION file content: "
#fi
