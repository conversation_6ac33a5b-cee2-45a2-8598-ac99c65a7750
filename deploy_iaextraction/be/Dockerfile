FROM ghcr.io/astral-sh/uv:0.5.25-python3.12-bookworm-slim AS builder

ENV UV_KEYRING_PROVIDER=disabled
ENV UV_INDEX_UNIR_USERNAME=VssSessionToken
ENV UV_COMPILE_BYTECODE=1
ENV UV_NO_CACHE=1

ARG UV_INDEX_UNIR_PASSWORD
ENV UV_INDEX_UNIR_PASSWORD=${UV_INDEX_UNIR_PASSWORD}
#Esto igual to tengo que cambiar al de unir password para que uv lo reconozca.
RUN groupadd -r appgrp && useradd -r -g appgrp -u 10001 appuser

WORKDIR /app
RUN apt-get update && apt-get --yes install gcc python3-pip

COPY pyproject.toml uv.lock ./
RUN --mount=type=cache,target=/root/.cache/uv \
    uv sync --group doc-ingestion --no-dev --frozen --no-install-project

COPY src src

RUN --mount=type=cache,target=/root/.cache/uv \
    uv sync --group doc-ingestion --no-dev --frozen

RUN .venv/bin/python -m ensurepip --upgrade \
&& .venv/bin/python -m pip install --upgrade pip \
&& .venv/bin/python -m spacy download en_core_web_sm \
&& .venv/bin/python -m spacy download es_core_news_sm

FROM python:3.12-slim-bookworm

RUN groupadd -r appgrp && useradd -r -g appgrp -u 10001 appuser

WORKDIR /app
COPY --from=builder /app .
RUN mkdir -p tmp && chown appuser:appgrp tmp

EXPOSE 80

USER appuser

CMD [".venv/bin/poe", "start-background"]