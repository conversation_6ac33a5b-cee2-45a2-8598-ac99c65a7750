#!/usr/bin/env bash

# Nombre de la aplicación frontend que se va a compilar/desplegar
export DO_SPA_APP_NAME=$DO_PROJECT
# Ruta de la carpeta de resultado de la transpilacion/compilacion y construccion de la aplicación Angular
export DO_DIST_PATH=""

# [OPCIONAL]: archivo de entorno. Si se especifica, se hace un reemplazo por environment.ts justo antes de comenzar la build.
# Ejemplo: ./path/to/environment.{env}.ts
export DO_FILE_ENV="" # TODO: Parametrizar?? el file replacement ya lo realiza el propio Angular

# [OPCIONAL]: Ruta del archivo de configuracion de nginx (ya viene en la imagen, solo sobreescribir en caso de necesitar una
# configuración distinta) El cambio se aplica cuando se especifica la opcion --use-nginx-config (true).
# La ruta por defecto es:
# para --app-type=spa > ./deploy/fe/nginx.template.conf
export DO_NGIX_PATH="$DO_APP_PATH/nginx.template.conf"
export DO_NGIX_DOCKER_PATH="/etc/nginx/templates/default.conf.template"
# usar custom nginx config por defecto para desplegar las aplicaciones MFE
export DO_USE_NGIX_CONFIG=""

# [OPCIONAL para --app-type=spa|mfe]: Habilita la compilacion para desplegar los artefactos con source maps y poder debuguear igual que en el entorno local.
# Para ello debe estar habilitada la opcion "sourceMap": true en la configuracion "development" del archivo de configuracion de
# Angular (Project.json o Angular.json) y ejecutar en el script project-build.sh los comandos de compilacion con el parametro --configuration=development
export DO_ENABLE_SOURCEMAPS=""

# Parámetros necesarios para desplegar a Azure
export DO_AZ_SERVICE_PRINCIPAL_CLIENTID=""
export DO_AZ_SERVICE_PRINCIPAL_CLIENT_SECRET=""
export DO_AZ_TENANT_UNIR=""
export DO_AZ_RESOURCE_GROUP=""
export DO_AZ_RESOURCE=""
export DO_AZ_SUBSCRIPTION=""

# Azure DevOps Personal Access Token 
export DO_UNIR_TOKEN=""

# Sobreescribe valores predeterminados por los que vengan en los parametros del script
set_spa_parameters() {
  for i in "${MAIN_PARAMETERS[@]}"; do
      # Extraer clave y valor
      key=$(echo "$i" | cut -d':' -f1)
      value=$(echo "$i" | cut -d':' -f2)

          case $key in
      --pat-key)
        DO_PAT_KEY="$value" ;;

      --pat-token)
        DO_PAT_TOKEN="$value" ;;

      --az-service-principal-clientid)
        DO_AZ_SERVICE_PRINCIPAL_CLIENTID="$value" ;;

      --az-service-principal-client-secret)
        DO_AZ_SERVICE_PRINCIPAL_CLIENT_SECRET="$value" ;;

      --az-tenant-unir)
        DO_AZ_TENANT_UNIR="$value" ;;

      --az-resource-group)
        DO_AZ_RESOURCE_GROUP="$value" ;;

      --az-resource)
        DO_AZ_RESOURCE="$value" ;;     
           
      --subscription)
        DO_AZ_SUBSCRIPTION="$value" ;;

      --unir-token)
        DO_UNIR_TOKEN="$value" ;; 
    esac
  done

  # [OPCIONAL]: ruta del archivo que contiene la version de la aplicacion: Formatos soportados: cs, ts, json
  # NOTA: En el caso de aplicaciones Frontend SPA esta ruta no se calcula como se hace en backend (be/bff) o MFE, ya que es fija y si cambia debe especificarse Manualmente aqui
  # con el parametro --version-path (pero comentando la siguiente linea para que no sobreescriba su valor, ya que el parámetro es principal y se carga antes. ver: set_main_parameters)
  DO_FILE_VERSION="./src/version_iaext.py"

  DO_DIST_PATH="./src"
}

set_spa_parameters

# Imprime variables especificas del tipo de palicacion
print_log_app_type() {
  print_variable "DO_FILE_VERSION" "$DO_FILE_VERSION"
  print_variable "DO_FILE_ENV" "$DO_FILE_ENV"
  print_variable "DO_ENABLE_SOURCEMAPS" "$DO_ENABLE_SOURCEMAPS"
  print_variable "DO_USE_NGIX_CONFIG" "$DO_USE_NGIX_CONFIG"
  print_variable "DO_SPA_APP_NAME" "$DO_SPA_APP_NAME"
}

