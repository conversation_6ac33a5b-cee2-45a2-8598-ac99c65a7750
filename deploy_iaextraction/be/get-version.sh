#!/usr/bin/env bash

# Proporcionado por Teamcity o 0, si no está definido como valor predeterminado
PATCH=${BUILD_NUMBER:-"0"}

# Lee el contenido de package.json y extrae la versión
VERSION=$(grep '__version__ = ' ${DO_FILE_VERSION} | awk -F'"' '{print $2}')

# Separa la versión en mayor, menor y parche
BUILD_MAYOR=$(echo "$VERSION" | cut -d. -f1)
BUILD_MINOR=$(echo "$VERSION" | cut -d. -f2)

# Retorna el número de versión completo como x.y.z
echo "${BUILD_MAYOR:-1}.${BUILD_MINOR:-0}.${PATCH}"